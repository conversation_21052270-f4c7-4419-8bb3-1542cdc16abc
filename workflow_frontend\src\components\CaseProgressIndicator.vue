<template>
  <q-circular-progress :value="props.progressValue" :size="props.size"
                       :color="getColor(props.progressValue)" track-color="grey-3" :thickness="0.8"/>
</template>

<script setup>

const props = defineProps({
  progressValue: Number,
  size: String,
})

function getColor(val){
  if(val === 100){
    return 'light-green-9';
  } else if (val >= 80){
    return 'light-green';
  } else if (val >= 60){
    return 'lime';
  } else if (val >= 40){
    return 'amber-5';
  } else if (val >= 20){
    return 'orange-7';
  } else if (val >= 5){
    return 'deep-orange-7';
  } else {
    return 'red-10';
  }
}

</script>