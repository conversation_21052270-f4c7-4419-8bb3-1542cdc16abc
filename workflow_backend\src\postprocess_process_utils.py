import json
import logging
from json import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r
from pathlib import Path
from .Define import ProcessStatus, Step
from workflow_backend.src.database.database_helpers import add_new_process_to_database, run_query
from .queue_utils import queue_next_step_if_requirements_fulfilled
from workflow_backend.src.models.processes_models import AddProcessPayload, UpdateProcessPayload


def parse_data_from_image_folder_path(image_folder_path):
    """
    Parse image folder path and extract information from it.
    Note: that does not work for window paths when running in a docker container
    :param image_folder_path: path to the image
    :return: client, reference number, subject's last name, prefix of subject's first name, evidence item name
    """
    logging.info("Parsing image folder path {image_folder_path} ...")
    reference_number = None
    subject_last_name, subject_first_name_prefix = None, None
    evidence_item_name = None

    # note: if you are running this in a docker container, paths are always treated as posix.
    # This causes parsing of windows path always failing when the workflow backend is running in a docker container on windows and thus on the linux subsystem.
    image_folder_path_obj = Path(image_folder_path.removesuffix('\n'))
    # /Pfad/LKA Test/AZ 666-666_<PERSON>_Fried/Müller_Ass_1
    # note: do not use path.stem() because evidence item names can be Müller_Ass_1.1
    evidence_item_folder_name = image_folder_path_obj.name  # Müller_Ass_1
    case_folder_name = image_folder_path_obj.parent.name    # AZ 666-666_Müller_Fried
    client = image_folder_path_obj.parent.parent.name       # LKA Test

    # Parse reference number, short case name and evidence item name from folder path.
    # The folder path generated from qrprintwebservice follows the structure "{reference_number}_{last_name}_{first_name[:3]}".
    # Note: in reference number, "/" are replace with "_"
    # example: "AZ 666-666_23_Müller_Fri" is parsed into "AZ 666-666/23", "Müller" and "Fri"
    case_folder_name_segments = case_folder_name.rsplit("_", maxsplit=2)
    if len(case_folder_name_segments) >= 3:
        reference_number = case_folder_name_segments[0]
        subject_last_name = case_folder_name_segments[1]
        subject_first_name_prefix = case_folder_name_segments[2]
        # revert replacement of qrprintwebservice to enable matching reference number in workflow database
        reference_number = reference_number.replace("_", "/")
    else:
        msg = f"Parsing case name and reference number failed. '{case_folder_name}' does not contain at least 2 underscores."
        logging.log(logging.WARNING, msg)
    # split "Müller_Ass_1" into "Müller" "Fried" and "Ass_1"
    evidence_item_folder_name_segments = evidence_item_folder_name.split("_", maxsplit=1)
    # check that string has been split into 3 parts (last name, first name, evidence item name)
    if len(evidence_item_folder_name_segments) == 2:
        evidence_item_name = evidence_item_folder_name_segments[1]
    else:
        msg = f"Parsing evidence item name failed. '{evidence_item_folder_name}' does not contain at least one underscores."
        logging.log(logging.WARNING, msg)

    logging.info(f"Parsed data from image folder path: client={client}, reference_number={reference_number}, last_name={subject_last_name}, first_name_prefix={subject_first_name_prefix}, evidence_item_name={evidence_item_name}")
    return client, reference_number, subject_last_name, subject_first_name_prefix, evidence_item_name


def process_image_station_result_and_update_process(process_id, result_data: str | None):
    """
    Parse 'image_folder_path' from result_data to get case and evidence item information.
    Check if a case with matching reference number, client and case name exists. If not, create it.
    Check if an evidence item belonging to this case exists with a matching evidence item name. If not, create it.
    (note: creating an evidence item will also add a new finished process for LagerDB/intake)
    Then assign the process to the determined evidence item by updating the "evidence_item_id" in the processes table.

    If parsing fails, then return None.
    """

    # if result data is not empty
    if result_data is not None and result_data != "":
        # load json to dict
        try:
            result_data_dict = json.loads(result_data)
        except JSONDecodeError as e:
            logging.error(f"Cannot load json string: '{result_data}' - {e}")
            logging.warning(f"The ImageStation process with id {process_id} has no valid json stored in result_data: {result_data}")
            return None

        # if image station result contains "image_folder_path",
        # parse the information needed for assigning the process to an evidence item and case (which are created if not already existing)
        if "image_folder_path" in result_data_dict:
            client, reference_number, subject_last_name, subject_first_name_prefix, evidence_item_name = (
                parse_data_from_image_folder_path(result_data_dict["image_folder_path"]))
            # if any of the values is None
            if not all([client, reference_number, subject_last_name, subject_first_name_prefix, evidence_item_name]):
                logging.error(f"Could not parse expected information from image folder path: '{result_data_dict["image_folder_path"]}'")
                return None

            case_result = get_case_and_create_if_not_exists(client, reference_number,
                                                            subject_first_name_prefix,
                                                            subject_last_name)
            evidence_item_result = get_evidence_item_and_create_if_not_exists(case_result[0]["id"], evidence_item_name)

            # assign process to evidence item
            update_process_query = """UPDATE processes 
                                        SET evidence_item_id = :evidence_item_id, updated_at = NOW() 
                                        WHERE id = :process_id 
                                        RETURNING *;"""
            updated_process = run_query(update_process_query, {"process_id": process_id,
                                             "evidence_item_id": evidence_item_result[0]["id"]})
            logging.info(f"Assigned process {process_id} to evidence item {evidence_item_result[0]['id']}.")
            return updated_process

        else:
            logging.warning(f"The ImageStation process with id {process_id} has no 'image_folder_path' in its result_data.")
    else:
        logging.warning(f"The ImageStation process with id {process_id} has status FINISHED but no result_data.")

    return None


def process_image_station_result_and_update_evidence_item(evidence_item_id, result_data: str | None):
    """
    Parse 'device_size' from result_data  and update the "device_size" in the evidence_items table.

    If updating evidence item fails, then return None.
    """

    # if result data is not empty
    if result_data is not None and result_data != "":
        # load json to dict
        try:
            result_data_dict = json.loads(result_data)
        except JSONDecodeError as e:
            logging.error(f"Cannot load json string: '{result_data}' - {e}")
            logging.warning(f"Could not get device size for evidence item {evidence_item_id}. The ImageStation process has no valid json stored in result_data: {result_data}")
            return None

        # if image station result contains "device_size"
        if "device_size" in result_data_dict:
            device_size = result_data_dict["device_size"]

            # update the evidence item
            update_evidence_item_query = """UPDATE evidence_items SET device_size = :device_size 
                                            WHERE id = :evidence_item_id RETURNING *;"""
            updated_evidence_item = run_query(update_evidence_item_query, {"evidence_item_id": evidence_item_id,
                                             "device_size": device_size})
            logging.info(f"Set device size to {updated_evidence_item[0]['device_size']} for evidence item {updated_evidence_item[0]['id']}.")
            return updated_evidence_item

        else:
            logging.warning(f"The ImageStation process has no 'device_size' in its result_data.")
    else:
        logging.warning(f"The given result_data is empty. Cannot get the device size for evidence item {evidence_item_id}.")

    return None


def get_evidence_item_and_create_if_not_exists(case_id, evidence_item_name):
    """
    Get evidence item and create it if it doesn't exist.
    (note: creating an evidence item will also add a new finished process for LagerDB/intake)
    :param case_id:
    :param evidence_item_name:
    :return:
    """
    # create evidence item if not exist
    logging.info(f"Creating evidence item {evidence_item_name} if not exists ...")
    create_evidence_item_query = """INSERT INTO evidence_items (evidence_item_name, case_id)
                                    SELECT :evidence_item_name,
                                           :case_id WHERE NOT EXISTS(
                                            SELECT 1 FROM evidence_items 
                                            WHERE evidence_item_name = :evidence_item_name and case_id = :case_id) 
                                    RETURNING *;"""
    create_evidence_item_result = run_query(create_evidence_item_query, {
        "evidence_item_name": evidence_item_name,
        "case_id": case_id
    })
    if create_evidence_item_result is not None and len(create_evidence_item_result) > 0:
        logging.info(f"Created evidence item: {create_evidence_item_result[0]}")
        # Add finished process for step 0 (LagerDB /case intake)
        logging.info(f"Adding finished intake process for evidence item {create_evidence_item_result[0]["id"]} ...")
        add_new_process_to_database(AddProcessPayload(
            evidence_item_id=create_evidence_item_result[0]["id"],
            step_id=Step.LAGER_DB,
            status=ProcessStatus.FINISHED
        ))
    # select evidence item
    select_evidence_item_query = """SELECT *
                                    FROM evidence_items
                                    WHERE evidence_item_name = :evidence_item_name
                                      and case_id = :case_id;"""
    evidence_item_result = run_query(select_evidence_item_query, {
        "evidence_item_name": evidence_item_name,
        "case_id": case_id
    })
    return evidence_item_result


def get_case_and_create_if_not_exists(client, reference_number, subject_first_name_prefix, subject_last_name):
    """
    Get case with the given client, reference number and a case name like "[lastname]%[firstnameprefix]%" or "[firstnameprefix]% [lastname]"
    :param client:
    :param reference_number:
    :param subject_first_name_prefix:
    :param subject_last_name:
    :return:
    """
    # create case if not exist
    case_name = subject_last_name + ", " + subject_first_name_prefix
    logging.info(f"Creating case {case_name} if not exists ...")
    create_case_query = """INSERT INTO cases (case_name, reference_number, client, intake_at)
                           SELECT :case_name,
                                  :reference_number,
                                  :client,
                                  NOW() WHERE NOT EXISTS(
                                    SELECT 1 FROM cases 
                                    WHERE client = :client and reference_number = :reference_number and 
                                    (case_name LIKE :case_name_pattern1 or case_name LIKE :case_name_pattern2))
                            RETURNING *;"""
    case_name_pattern1 = subject_last_name + "%" + subject_first_name_prefix + "%"
    case_name_pattern2 = subject_first_name_prefix + "%" + subject_last_name + "%"
    case_create_result = run_query(create_case_query,
                                   {"case_name": case_name,
                                    "case_name_pattern1": case_name_pattern1,
                                    "case_name_pattern2": case_name_pattern2,
                                    "reference_number": reference_number,
                                    "client": client})
    if not case_create_result and len(case_create_result) > 0:
        logging.info(f"Created case: {case_create_result[0]}")
    # select case
    select_case_query = """SELECT *
                           FROM cases
                           WHERE client = :client
                             and reference_number = :reference_number
                             and (case_name LIKE :case_name_pattern1 or case_name LIKE :case_name_pattern2);"""
    case_result = run_query(select_case_query, {"reference_number": reference_number,
                                                "client": client,
                                                "case_name_pattern1": case_name_pattern1,
                                                "case_name_pattern2": case_name_pattern2})
    return case_result


def postprocessing_on_process_insert_or_update(payload: AddProcessPayload | UpdateProcessPayload, process_id, upserted_process):
    """
    If an ImageStation process has not evidence item id, yet, and contains result data,
    parse the result data to assign the process to an evidence item and a case (both are created if not exist).

    If the status of the process is FINISHED, queue the next step if applicable.
    (The next step can only be queued if the process is assigned to an evidence item.)
    :param payload: The payload given for adding or updating a process.
    :param process_id: The id of the process.
    :param upserted_process: The result of the database update or insert query.
    :return: The updated version of the updated/added process.
    """

    evidence_item_id = upserted_process[0]["evidence_item_id"]
    step_id = upserted_process[0]["step_id"]

    # if the ImageStation process contains result data but is still not assigned to an evidence item,
    # then process the result data to create (if necessary) and assign evidence item and case
    if (step_id == Step.IMAGE_STATION and (payload.result_data != "" or payload.result_data is not None)
            and evidence_item_id is None):
        logging.info("ImageStation process is still not assigned to an evidence item. Processing result data now ...")
        processing_result = process_image_station_result_and_update_process(process_id, payload.result_data)
        if processing_result is not None:
            upserted_process = processing_result
            evidence_item_id = upserted_process[0]["evidence_item_id"]

    # if the ImageStation process contains result data and is assigned to an evidence item,
    # then process the result data to set the device size of the evidence item
    if step_id == Step.IMAGE_STATION and (payload.result_data != "" or payload.result_data is not None):
        process_image_station_result_and_update_evidence_item(evidence_item_id, payload.result_data)

    # Automatically queue the next step if the current step is finished
    # important note: this need to be AFTER processing image station result because a process without an evidence item id will always fail to meet the requirements
    if (upserted_process is not None
            and upserted_process[0]["status"] == ProcessStatus.FINISHED
            and upserted_process[0]["evidence_item_id"] is not None):
        logging.info(f"Process with id {process_id} has status FINISHED. Checking if next step can be queued automatically...")
        queue_next_step_if_requirements_fulfilled(evidence_item_id, step_id)
    else:
        logging.info(
            f"Conditions for trying to automatically queue the next step weren't met for the updated/added process: {upserted_process}")
    return upserted_process
