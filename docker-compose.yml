services:
  workflow_database:
    image: postgres:17.2
    container_name: workflow_database
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - ./workflow_database/db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - workflow_database_network
    env_file:
      - .env

  workflow_migrations:
    image: python:3.11
    container_name: workflow_migrations
    depends_on:
      - workflow_database
    volumes:
      - ./workflow_database/migrations:/migrations
    working_dir: /migrations
    command: >
      sh -c "
      pip install yoyo-migrations psycopg2 &&
      sleep 10 &&
      yoyo apply --database postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:5432/${DB_NAME} /migrations
      "
    networks:
      - workflow_database_network
    env_file:
      - .env

  workflow_backend:
    container_name: workflow_backend
    restart: unless-stopped
    build:
      context: ./workflow_backend
      dockerfile: Dockerfile
    volumes:
      - ./workflow_backend:/app
    working_dir: /app
    ports:
      - "8000:8000"
    networks:
      - workflow_database_network
    env_file:
      - .env

  workflow_frontend:
    container_name: workflow_frontend
    restart: unless-stopped
    build:
      context: ./workflow_frontend
      additional_contexts:
        main_workflow_dir: .
      dockerfile: Dockerfile
      target: production-stage
    ports:
      - "8001:80"
    networks:
      - workflow_database_network

  workflow_grafana:
    image: grafana/grafana:latest
    container_name: workflow_grafana
    restart: unless-stopped
    ports:
      - "3001:3000"  # Maps external port 3001 to internal container port 3000
    environment:
      - GF_SECURITY_ADMIN_USER=${GF_SECURITY_ADMIN_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GF_SECURITY_ADMIN_PASSWORD}
      - GF_DATABASE_HOST=${DB_HOST}
      - GF_DATABASE_NAME=${DB_NAME}
      - GF_DATABASE_USER=${DB_USER}
      - GF_DATABASE_PASSWORD=${DB_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./workflow_grafana/config/grafana.ini:/etc/grafana/grafana.ini
      - ./workflow_grafana/provisioning:/etc/grafana/provisioning
    env_file:
      - .env
    user: "0:0"
    networks:
      - workflow_database_network

networks:
  workflow_database_network:
    name: workflow_database_network
    driver: bridge

volumes:
  grafana_data: