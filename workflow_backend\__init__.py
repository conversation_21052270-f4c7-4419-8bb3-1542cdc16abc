import logging
from logging.handlers import RotatingFileHandler
from pathlib import Path
import sys



logfile_path = Path("logs/forensic_workflow.log")
logfile_path.parent.mkdir(parents=True, exist_ok=True)

# if production environment (we are running in a bundle (exe file))
loglevel = 'INFO' if getattr(sys, 'frozen', False) else 'DEBUG'

# Create rotating file handler
file_handler = RotatingFileHandler(
    logfile_path,
    maxBytes=5 * 1024 * 1024,  # 5MB
    backupCount=10             # 10 backup files
)
file_handler.setFormatter(logging.Formatter(
    '[%(asctime)s] {%(filename)s:%(lineno)d} %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S (%Z)'
))

# Set up root logger
logger = logging.getLogger()
logger.setLevel(loglevel)

# Prevent adding multiple handlers on reload
if not any(isinstance(h, RotatingFileHandler) for h in logger.handlers):
    logger.addHandler(file_handler)