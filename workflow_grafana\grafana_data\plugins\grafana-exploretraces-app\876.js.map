{"version": 3, "file": "876.js", "mappings": "kKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,+CACxB,iBAAkB,gCAClB,yBAA0B,+CAE3B,yBAA0B,CACzB,sBAAuB,uCACvB,mBAAoB,qDAErB,gBAAiB,CAChBC,QAAS,uGACTC,SAAU,0CACVC,MAAO,kBAER,wBAAyB,CACxB,wBAAyB,mBACzB,sBAAuB,mBACvB,sBAAuB,mBAExB,iBAAkB,CACjB,iBAAkB,gBAClB,cAAe,cACf,uBAAwB,mBAEzB,iBAAkB,CACjB,eAAgB,mBAChB,aAAc,oBAEf,oCAAqC,CACpC,eAAgB,YAChB,iBAAkB,kCAEnBC,SAAU,CACT,gCAAiC,0CAElC,YAAa,CACZD,MAAO,CACNA,MAAO,UAGT,2BAA4B,CAC3BE,QAAS,aAEV,qBAAsB,CACrB,uBAAwB,8BACxB,sCAAuC,+CAExC,yBAA0B,CACzB,2DAA4D,iIAC5D,kBAAmB,mDAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,aAEnB,oBAAqB,CACpB,uBAAwB,sBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,6BAEpC,wBAAyB,CACxB,wBAAyB,oBACzB,mBAAoB,qBAErB,yBAA0B,CACzB,2BAA4B,mBAC5B,aAAc,CACb,2BAA4B,mBAE7B,qBAAsB,kBACtB,sBAAuB,mBACvB,eAAgB,CACf,2BAA4B,mBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,aAGb,8CAA+C,CAC9C,mBAAoB,UACpBC,QAAS,wHACT,gDAAiD,4EAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,uBACzB,uBAAwB,2BACxB,gCAAiC,2CACjC,oDAAqD,sFACrD,0BAA2B,2BAC3B,uBAAwB,sBACxB,mBAAoB,0BACpB,mDAAoD,qDACpD,uBAAwB,2BACxB,kDAAmD,gEACnD,iCAAkC,kCAClC,oCAAqC,+CAIxC,6BAA8B,CAC7B,+BAAgC,2BAChC,6BAA8B,yBAE/B,oBAAqB,CACpB,2BAA4B,kBAE7B,8BAA+B,CAC9B,kBAAmB,6BAEpB,2BAA4B,CAC3BC,MAAO,YAER,yBAA0B,CACzB,mBAAoB,2BAErB,4BAA6B,CAC5B,6CAA8C,kFAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,UAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,SAGR,uBAAwB,CACvB,0BAA2B,iBAE5B,wBAAyB,CACxB,2BAA4B,oB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/de-DE/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Filter mit Schlüssel {{keyLabel}} bearbeiten\",\n\t\t\t\"managed-filter\": \"{{origin}} verwalteter Filter\",\n\t\t\t\"remove-filter-with-key\": \"Filter mit Schlüssel {{keyLabel}} entfernen\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Filterwert entfernen – {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Benutzerdefinierten Wert verwenden: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Wenn Sie über einen Link hierher gekommen sind, enthält diese Anwendung möglicherweise einen Fehler.\",\n\t\t\tsubTitle: \"Die URL stimmt mit keiner Seite überein\",\n\t\t\ttitle: \"Nicht gefunden\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"Szene ausblenden\",\n\t\t\t\"expand-button-label\": \"Szene einblenden\",\n\t\t\t\"remove-button-label\": \"Szene entfernen\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Objektdetails\",\n\t\t\t\"scene-graph\": \"<PERSON>zen<PERSON>raph\",\n\t\t\t\"title-scene-debugger\": \"Szenen-Debugger\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Zeile ausblenden\",\n\t\t\t\"expand-row\": \"Zeile einblenden\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Vergleich\",\n\t\t\t\"button-tooltip\": \"Zeitrahmenvergleich aktivieren\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Widget zur Größenänderung des Bereichs\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Titel\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Entdecken\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Plugin-Panel wird geladen …\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"Das Panel-Plugin hat keine Panel-Komponente\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Das Rendern von zu vielen Reihen in einem einzigen Panel kann die Leistung beeinträchtigen und das Lesen der Daten erschweren.\",\n\t\t\t\"warning-message\": \"Es werden nur {{seriesLimit}} Reihen angezeigt\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Entfernen\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Abfrage abbrechen\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Filteroperator bearbeiten\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Filter hinzufügen\",\n\t\t\t\"title-add-filter\": \"Filter hinzufügen\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Filter entfernen\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Label auswählen\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Label auswählen\",\n\t\t\t\"title-remove-filter\": \"Filter entfernen\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Wert auswählen\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"Standard\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"löschen\",\n\t\t\ttooltip: \"Wird in diesem Dashboard standardmäßig angewendet. Wenn es bearbeitet wird, wird es auf andere Dashboards übertragen.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Die Einstellung „Gruppieren nach“ von diesem Dashboard wiederherstellen.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Kommagetrennte Werte\",\n\t\t\t\t\t\"double-quoted-values\": \"Doppelt angegebene Werte\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Datum auf verschiedene Arten formatieren\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Formatieren Sie mehrwertige Variablen mit der glob-Syntax, Beispiel {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"HTML-Escaping von Werten\",\n\t\t\t\t\t\"json-stringify-value\": \"JSON-Stringify-Wert\",\n\t\t\t\t\t\"keep-value-as-is\": \"Wert unverändert lassen\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Mehrere Werte werden als variable=value formatiert\",\n\t\t\t\t\t\"single-quoted-values\": \"Einfach angegebene Werte\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Nützlich für URL-Escaping-Werte, die URI-Syntaxzeichen nutzen\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Nützlich für URL-Escaping-Werte\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Werte werden durch das Zeichen | getrennt\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Nach Selektor gruppieren\",\n\t\t\t\"placeholder-group-by-label\": \"Nach Label gruppieren\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Wert auswählen\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Optionen werden geladen …\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Anwenden\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Keine Optionen gefunden\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Beim Abrufen der Label ist ein Fehler aufgetreten. Klicken Sie zum Wiederholen\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Hallo\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Text\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Wert eingeben\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Wert auswählen\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}