"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[328],{7709:(e,t,n)=>{n.d(t,{F:()=>l});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(3571);const l=e=>{const{buttonFill:t,hideExclude:n,isExcluded:r,isIncluded:i,onClear:l,onExclude:u,onInclude:d,titles:p}=e,g=(0,s.useStyles2)(c,i,r,n);return a().createElement("div",{className:g.container},a().createElement(s.<PERSON><PERSON>,{variant:i?"primary":"secondary",fill:t,size:"sm","aria-selected":i,className:g.includeButton,onClick:i?l:d,"data-testid":o.b.exploreServiceDetails.buttonFilterInclude,title:null==p?void 0:p.include},"Include"),!n&&a().createElement(s.<PERSON><PERSON>,{variant:r?"primary":"secondary",fill:t,size:"sm","aria-selected":r,className:g.excludeButton,onClick:r?l:u,title:null==p?void 0:p.exclude,"data-testid":o.b.exploreServiceDetails.buttonFilterExclude},"Exclude"))},c=(e,t,n,r)=>({container:(0,i.css)({display:"flex",justifyContent:"center"}),excludeButton:(0,i.css)({borderLeft:n?void 0:"none",borderRadius:`0 ${e.shape.radius.default} ${e.shape.radius.default} 0`}),includeButton:(0,i.css)({borderRadius:0,borderRight:t||r?void 0:"none"})})},7191:(e,t,n)=>{n.d(t,{R:()=>o});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007);const o=({children:e})=>{const t=(0,s.useStyles2)(l);return a().createElement("div",{className:t.wrap},a().createElement(s.EmptyState,{variant:"not-found",message:e?"":"An error occurred"},e&&e))},l=e=>({wrap:(0,i.css)({margin:"0 auto"})})},8714:(e,t,n)=>{n.d(t,{P:()=>ot,y:()=>st});var r=n(5959),a=n.n(r),i=n(219),s=n(7781),o=n(8531),l=n(6865),c=n(2245),u=n(2007),d=n(6709),p=n(4509),g=n(1532),h=n(4702),f=n(7839),v=n(6838),b=n(8502),m=n(6854),y=n(5700);function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let w={8:"backspace",9:"tab",13:"enter",16:"shift",17:"ctrl",18:"alt",20:"capslock",27:"esc",32:"space",33:"pageup",34:"pagedown",35:"end",36:"home",37:"left",38:"up",39:"right",40:"down",45:"ins",46:"del",91:"meta",93:"meta",224:"meta"},O={106:"*",107:"+",109:"-",110:".",111:"/",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},E={"!":"1",'"':"'","#":"3",$:"4","%":"5","&":"7","(":"9",")":"0","*":"8","+":"=",":":";","<":",",">":".","?":"/","@":"2","^":"6",_:"-","|":"\\","~":"`"},x={command:"meta",escape:"esc",mod:/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"meta":"ctrl",option:"alt",plus:"+",return:"enter"},P=null;for(let e=1;e<20;++e)w[111+e]="f"+e;for(let e=0;e<=9;++e)w[e+96]=e.toString();function j(e){if("keypress"===e.type){let t=String.fromCharCode(e.which);return e.shiftKey||(t=t.toLowerCase()),t}return w[e.which]?w[e.which]:O[e.which]?O[e.which]:String.fromCharCode(e.which).toLowerCase()}function C(e,t){return e.sort().join(",")===t.sort().join(",")}function L(e){return"shift"===e||"ctrl"===e||"alt"===e||"meta"===e}function _(e,t,n){return n||(n=function(){if(!P){P={};for(let e in w){const t=parseInt(e,10);t>95&&t<112||w.hasOwnProperty(e)&&(P[w[e]]=e)}}return P}()[e]?"keydown":"keypress"),"keypress"===n&&t.length&&(n="keydown"),n}function F(e,t){let n,r,a,i=[];for(n=function(e){return"+"===e?["+"]:(e=e.replace(/\+{2}/g,"+plus")).split("+")}(e),a=0;a<n.length;++a)r=n[a],x[r]&&(r=x[r]),t&&"keypress"!==t&&E[r]&&(r=E[r],i.push("shift")),L(r)&&i.push(r);if(!r)throw new Error("Unable to get key");return{action:t=_(r,i,t),key:r,modifiers:i}}function k(e,t){return null!==e&&e!==document&&(e===t||k(e.parentNode,t))}const T=new class{constructor(e){S(this,"target",void 0),S(this,"_callbacks",{}),S(this,"_directMap",{}),S(this,"_sequenceLevels",{}),S(this,"_resetTimer",void 0),S(this,"_ignoreNextKeyup",!1),S(this,"_ignoreNextKeypress",!1),S(this,"_nextExpectedAction",!1),S(this,"_globalCallbacks",{}),S(this,"_resetSequences",e=>{e=e||{};let t,n=!1;for(t in this._sequenceLevels)e[t]?n=!0:this._sequenceLevels[t]=0;n||(this._nextExpectedAction=!1)}),S(this,"_getMatches",(e,t,n,r,a,i)=>{let s,o,l=[],c=n.type;if(!this._callbacks[e])return[];for("keyup"===c&&L(e)&&(t=[e]),s=0;s<this._callbacks[e].length;++s)if(o=this._callbacks[e][s],(r||!o.seq||this._sequenceLevels[o.seq]===o.level)&&c===o.action&&("keypress"===c&&!n.metaKey&&!n.ctrlKey||C(t,o.modifiers))){let t=!r&&o.combo===a,n=r&&o.seq===r&&o.level===i;(t||n)&&this._callbacks[e].splice(s,1),l.push(o)}return l}),S(this,"_fireCallback",(e,t,n,r)=>{const a=t.target||t.srcElement;var i;a&&a instanceof HTMLElement&&this.stopCallback(t,a,n,r)||!1===e(t,n)&&((i=t).preventDefault?i.preventDefault():i.returnValue=!1,function(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}(t))}),S(this,"_handleKey",(e,t,n)=>{let r,a=this._getMatches(e,t,n),i={},s=0,o=!1;for(r=0;r<a.length;++r){var l;if(a[r].seq)s=Math.max(s,null!==(l=a[r].level)&&void 0!==l?l:0)}for(r=0;r<a.length;++r){const t=a[r].seq;if(t){if(a[r].level!==s)continue;o=!0,i[t]=1,this._fireCallback(a[r].callback,n,a[r].combo,t);const l=t.lastIndexOf(e),c=t.slice(0,l);for(const[e,t]of Object.entries(this._sequenceLevels))t>0&&e.startsWith(c)&&(i[e]=1);continue}o||this._fireCallback(a[r].callback,n,a[r].combo)}var c;for(const t of null!==(c=this._callbacks[e])&&void 0!==c?c:[])t.action===n.type&&t.seq&&0===t.level&&(i[t.seq]=1);let u="keypress"===n.type&&this._ignoreNextKeypress;n.type!==this._nextExpectedAction||L(e)||u||this._resetSequences(i),this._ignoreNextKeypress=o&&"keydown"===n.type}),S(this,"_handleKeyEvent",e=>{if(!(e instanceof KeyboardEvent))throw new Error("Didn't get a KeyboardEvent");const t=e;if(t.repeat)return;"number"!=typeof t.which&&(t.which=t.keyCode);let n=j(t);n&&("keyup"!==t.type||this._ignoreNextKeyup!==n?this.handleKey(n,function(e){let t=[];return e.shiftKey&&t.push("shift"),e.altKey&&t.push("alt"),e.ctrlKey&&t.push("ctrl"),e.metaKey&&t.push("meta"),t}(t),t):this._ignoreNextKeyup=!1)}),S(this,"_resetSequenceTimer",()=>{clearTimeout(this._resetTimer),this._resetTimer=setTimeout(this._resetSequences,1e3)}),S(this,"_bindSequence",(e,t,n,r)=>{this._sequenceLevels[e]=0;const a=t=>()=>{this._nextExpectedAction=t,++this._sequenceLevels[e],this._resetSequenceTimer()},i=t=>{this._fireCallback(n,t,e),"keyup"!==r&&(this._ignoreNextKeyup=j(t)),this._resetSequenceTimer()};for(let n=0;n<t.length;++n){let s=n+1===t.length?i:a(r||F(t[n+1]).action);this._bindSingle(t[n],s,r,e,n)}}),S(this,"_bindSingle",(e,t,n,r,a)=>{this._directMap[e+":"+n]=t;let i,s=(e=e.replace(/\s+/g," ")).split(" ");if(s.length>1)return void this._bindSequence(e,s,t,n);i=F(e,n),this._callbacks[i.key]=this._callbacks[i.key]||[];const o={ctrlKey:!1,metaKey:!1,type:i.action};this._getMatches(i.key,i.modifiers,o,r,e,a);const l={action:i.action,callback:t,combo:e,level:a,modifiers:i.modifiers,seq:r};this._callbacks[i.key][r?"unshift":"push"](l)}),S(this,"_bindMultiple",(e,t,n)=>{for(let r=0;r<e.length;++r)this._bindSingle(e[r],t,n)}),S(this,"bind",(e,t,n)=>(e=e instanceof Array?e:[e],this._bindMultiple(e,t,n),self)),S(this,"unbind",(e,t)=>this.bind(e,function(){},t)),S(this,"bindGlobal",(e,t,n)=>{if(this.bind(e,t,n),e instanceof Array)for(let t=0;t<e.length;t++)this._globalCallbacks[e[t]]=!0;else this._globalCallbacks[e]=!0}),S(this,"unbindGlobal",(e,t)=>{if(this.unbind(e,t),e instanceof Array)for(let t=0;t<e.length;t++)this._globalCallbacks[e[t]]=!1;else this._globalCallbacks[e]=!1}),S(this,"trigger",(e,t)=>{let n=this;return n._directMap[e+":"+t]&&n._directMap[e+":"+t]({},e),n}),S(this,"reset",()=>(this._callbacks={},this._directMap={},this)),S(this,"stopCallback",(e,t,n,r)=>{if(this._globalCallbacks[n]||r&&this._globalCallbacks[r])return!1;if((" "+t.className+" ").indexOf(" mousetrap ")>-1)return!1;if(k(t,this.target))return!1;if("composedPath"in e&&"function"==typeof e.composedPath){let n=e.composedPath()[0];n!==e.target&&n instanceof HTMLElement&&(t=n)}return Boolean("INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||"isContentEditable"in t&&t.isContentEditable)}),S(this,"handleKey",(...e)=>this._handleKey(...e)),S(this,"addKeycodes",e=>{for(let t in e)e.hasOwnProperty(t)&&(w[t]=e[t]);P=null}),this.target=e,this.target.addEventListener("keypress",e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)}),this.target.addEventListener("keydown",e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)}),this.target.addEventListener("keyup",e=>{e instanceof KeyboardEvent&&this._handleKeyEvent(e)})}}(document);class D{addBinding(e){T.bind(e.key,t=>{t.preventDefault(),t.stopPropagation(),t.returnValue=!1,e.onTrigger()},"keydown"),this._binds.push(e)}removeAll(){this._binds.forEach(e=>{T.unbind(e.key,e.type)}),this._binds=[]}constructor(){var e,t,n;n=[],(t="_binds")in(e=this)?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}}var N=n(8428),$=n(5719);function A(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function B(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){A(i,r,a,s,o,"next",e)}function o(e){A(i,r,a,s,o,"throw",e)}s(void 0)})}}function M(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const I=(0,o.getAppEvents)();function R(e){const t=new D;let n=null;const r=I.subscribe(s.SetPanelAttentionEvent,e=>{"string"==typeof e.payload.panelId&&(n=e.payload.panelId)});function a(e,t){return()=>{const r=l.jh.findObject(e,e=>e.state.key===n&&e.isActive);r&&r instanceof l.Eb&&t(r)}}return t.addBinding({key:"p l",onTrigger:a(e,G)}),t.addBinding({key:"a l",onTrigger:function(e,t){return()=>{l.jh.findAllObjects(e,e=>e instanceof l.Eb&&e.isActive).forEach(e=>{e&&e instanceof l.Eb&&t(e)})}}(e,G)}),t.addBinding({key:"p x",onTrigger:a(e,e=>B(function*(){const t=(0,y.iD)(e);t&&o.locationService.push(t)})())}),t.addBinding({key:"t c",onTrigger:()=>{!function(e){const t=window.__grafanaSceneContext;window.__grafanaSceneContext=e}(l.jh.getTimeRange(e)),I.publish(new U)}}),t.addBinding({key:"t v",onTrigger:()=>{const t=new W({updateUrl:!1});e.publishEvent(t),I.publish(t)}}),t.addBinding({key:"d r",onTrigger:()=>l.jh.getTimeRange(e).onRefresh()}),t.addBinding({key:"t z",onTrigger:()=>{V(e)}}),t.addBinding({key:"ctrl+z",onTrigger:()=>{V(e)}}),t.addBinding({key:"t a",onTrigger:()=>{const t=(0,$.m0)(e);null==t||t.toAbsolute()}}),t.addBinding({key:"t left",onTrigger:()=>{z(e,"left")}}),t.addBinding({key:"t right",onTrigger:()=>{z(e,"right")}}),()=>{t.removeAll(),r.unsubscribe()}}function V(e){const t=(0,$.m0)(e);null==t||t.onZoom()}function z(e,t){const n=(0,$.m0)(e);n&&("left"===t&&n.onMoveBackward(),"right"===t&&n.onMoveForward())}function G(e){const t=e.state.options;var n;null!=(n=t)&&"object"==typeof n&&"legend"in n&&"boolean"==typeof t.legend.showLegend&&e.onOptionsChange({legend:{showLegend:!t.legend.showLegend}})}class U extends s.BusEventBase{}M(U,"type","copy-time");class W extends s.BusEventWithPayload{}M(W,"type","paste-time");var K=n(5953),Q=n(2152),H=n(708),q=n(4532),J=n(8729),Y=n(8848),X=n(9683),Z=n(6464),ee=n(20);function te(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function ne(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){te(i,r,a,s,o,"next",e)}function o(e){te(i,r,a,s,o,"throw",e)}s(void 0)})}}function re(e){return ne(function*(){const t=yield(0,o.getDataSourceSrv)().get((0,$.U4)(e));if(!(t instanceof o.DataSourceWithBackend))throw K.v.error(new Error("getTagKeysProvider: Invalid datasource!")),new Error("Invalid datasource!");const n=t;if(n&&n.getTagKeys){const t={filters:new Z.K(e.state.filters).getJoinedLabelsFilters()},r=yield n.getTagKeys(t);return{replace:!0,values:(Array.isArray(r)?r:[]).filter(e=>!b.rm.includes(e.text))}}return K.v.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{replace:!0,values:[]}})()}function ae(e){return ne(function*({expr:e,limit:t,sceneRef:n,scopedVars:r,timeRange:a,variableType:i}){const l=yield(0,o.getDataSourceSrv)().get((0,$.U4)(n));if(!(l instanceof o.DataSourceWithBackend))throw K.v.error(new Error("getTagKeysProvider: Invalid datasource!")),new Error("Invalid datasource!");const c=l,u=c.languageProvider,d={expr:e,limit:t,sceneRef:n,scopedVars:r,timeRange:a,variableType:i},p=c&&"function"==typeof u.fetchDetectedFields&&u.fetchDetectedFields.bind(u)||function(e){return function(e,t,n){return ne(function*(){if(!("interpolateString"in e)||"function"!=typeof(null==e?void 0:e.interpolateString))throw new Error("Datasource missing interpolateString method");const r=t.expr&&t.expr!==ie?e.interpolateString(t.expr,t.scopedVars):void 0;if(!r)throw new Error("fetchDetectedFields requires query expression");const a="detected_fields";var i;const o=null!==(i=null==t?void 0:t.timeRange)&&void 0!==i?i:(0,s.getDefaultTimeRange)(),l=e.getTimeRangeParams(o),{end:c,start:u}=l;var d;const p={end:c,limit:null!==(d=null==t?void 0:t.limit)&&void 0!==d?d:1e3,start:u};return p.query=r,new Promise((t,r)=>ne(function*(){try{const r=yield e.getResource(a,p,n);t(r.fields)}catch(e){console.error("error",e),r(e)}})())})()}(c,e)};if(p&&"function"==typeof p){const e=yield p(d);if(e instanceof Error)throw K.v.error(e,{msg:"Failed to fetch detected fields"}),e;const t=e.filter(e=>i===ee._Y?e.label===ee.e4:i===ee.sL&&e.label!==ee.e4||null!==e.parsers).map(e=>{if(i===ee.sL){var t;let n=1===(null===(t=e.parsers)||void 0===t?void 0:t.length)?e.parsers[0]:"mixed";null===e.parsers&&(n="structuredMetadata");return{group:n,meta:{parser:n,type:e.type},text:e.label,value:e.label}}return{text:e.label,value:e.label}});return t.sort((e,t)=>"structuredMetadata"===e.group&&"structuredMetadata"!==t.group?-1:"structuredMetadata"!==e.group&&"structuredMetadata"===t.group?1:0),{replace:!0,values:t}}return K.v.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{replace:!0,values:[]}}).apply(this,arguments)}const ie="{}";var se=n(3241),oe=n(4351),le=n(5553);function ce(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function ue(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){ce(i,r,a,s,o,"next",e)}function o(e){ce(i,r,a,s,o,"throw",e)}s(void 0)})}}const de=(e,t,n,r,a,i)=>ue(function*(){const s=yield(0,o.getDataSourceSrv)().get((0,$.U4)(r));if(!(s instanceof o.DataSourceWithBackend))throw K.v.error(new Error("getTagValuesProvider: Invalid datasource!")),new Error("Invalid datasource!");const l=s.languageProvider;let c=[];if(l&&l.fetchDetectedLabelValues){const r={expr:n,limit:1e3,throwError:!0,timeRange:a},s={showErrorAlert:!1};try{let n=yield l.fetchDetectedLabelValues(e.key,r,s);if(n&&(0,se.isArray)(n)){var u;if(i===ee._Y)return{replace:!0,values:n.map(e=>({text:e}))};const r=t.state.filters;let a=[];r.forEach(e=>{var t,n;const r=null!==(n=null===(t=e.valueLabels)||void 0===t?void 0:t[0])&&void 0!==n?n:e.value;(0,H.SM)(e.operator)?r.split("|").forEach(e=>a.push(e)):a.push(r)});const s=n.filter(e=>!a.includes(e));if("structuredMetadata"!==(null===(u=e.meta)||void 0===u?void 0:u.parser)){if(e.value){const t=(0,le.bu)(e,i);return{replace:!0,values:s.map(e=>({text:e,value:JSON.stringify({parser:t.parser,value:e})}))}}return{replace:!0,values:s.map(t=>{var n,r;return{text:t,value:JSON.stringify({parser:null!==(r=null===(n=e.meta)||void 0===n?void 0:n.parser)&&void 0!==r?r:"mixed",value:t})}})}}c=s.map(e=>({text:e}))}else c=[],K.v.error(n,{msg:"fetchDetectedLabelValues error!"})}catch(e){K.v.error(e,{msg:"getDetectedFieldValuesTagValuesProvider: loki missing detected_field/.../values endpoint. Upgrade to Loki 3.3.0 or higher."}),c=[]}}else K.v.warn("getDetectedFieldValuesTagValuesProvider: fetchDetectedLabelValues is not defined in Loki datasource. Upgrade to Grafana 11.4 or higher."),c=[];return{replace:!0,values:c}})();function pe(e,t){return ue(function*(){const n=yield(0,o.getDataSourceSrv)().get((0,$.U4)(e));if(!(n instanceof o.DataSourceWithBackend))throw K.v.error(new Error("getTagValuesProvider: Invalid datasource!")),new Error("Invalid datasource!");const r=n;if(r&&r.getTagValues){const n=function(e,t){let n=e.filter(e=>!((0,H.BG)(t.operator)&&e.key===t.key));return n.some(e=>(0,H.BG)(e.operator))||(n=[]),n}(new Z.K(e.state.filters).getJoinedLabelsFilters(),t),i={filters:n,key:t.key};let s=yield r.getTagValues(i);if((0,se.isArray)(s)){var a;s=s.filter(n=>!e.state.filters.filter(e=>e.key===t.key).some(e=>{if((0,H.SM)(e.operator)){return e.value.split("|").some(e=>e===n.text)}return e.operator===m.w7.Equal&&e.value===n.text}));const n=(0,oe.eT)(null===(a=(0,le.S9)(e).getValue())||void 0===a?void 0:a.toString(),t.key),r=new Set(n);n.length&&s.sort((e,t)=>(r.has(t.text)?1:-1)-(r.has(e.text)?1:-1))}return{replace:!0,values:s}}return K.v.error(new Error("getTagValuesProvider: missing or invalid datasource!")),{replace:!0,values:[]}})()}var ge=n(5548),he=n(6089),fe=n(1792);const ve=()=>{const e=(0,u.useStyles2)(be),t=(0,u.useTheme2)();return a().createElement("div",{className:e.wrap},a().createElement("div",{className:e.graphicContainer},a().createElement(fe.A,{src:(t.isDark,"/public/plugins/grafana-lokiexplore-app/img/grot_loki.svg")})),a().createElement("div",{className:e.text},a().createElement("h3",{className:e.title},"Welcome to Grafana Logs Drilldown"),a().createElement("p",null,"We noticed there is no Loki datasource configured.",a().createElement("br",null),"Add a"," ",a().createElement("a",{className:"external-link",href:s.locationUtil.assureBaseUrl("/connections/datasources/new")},"Loki datasource")," ","to view logs."),a().createElement("br",null),a().createElement("p",null,"Click"," ",a().createElement("a",{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/logs/",target:"_blank",className:"external-link",rel:"noreferrer"},"here")," ","to learn more...")))},be=e=>({graphicContainer:(0,he.css)({[e.breakpoints.up("md")]:{alignSelf:"flex-end",height:"auto",padding:e.spacing(1),width:"300px"},[e.breakpoints.up("lg")]:{alignSelf:"flex-end",height:"auto",padding:e.spacing(1),width:"400px"},display:"flex",height:"250px",justifyContent:"center",margin:"0 auto",padding:e.spacing(1),width:"200px"}),text:(0,he.css)({alignItems:"center",display:"flex",flexDirection:"column",justifyContent:"center"}),title:(0,he.css)({marginBottom:"1.5rem"}),wrap:(0,he.css)({[e.breakpoints.up("md")]:{flexDirection:"row",margin:"4rem auto auto auto"},alignItems:"center",display:"flex",flexDirection:"column",margin:"0 auto auto auto",padding:"2rem",textAlign:"center"})});var me,ye,Se,we=n(6991),Oe=n(9721),Ee=n(173);class xe extends l.Bs{}Se=function({model:e}){var t,n;const r=l.jh.getVariables(e).useState();let i=r.variables;return(null===(t=e.state.include)||void 0===t?void 0:t.length)&&(i=r.variables.filter(t=>{var n,r;return null===(n=e.state.include)||void 0===n?void 0:n.includes(null!==(r=t.state.name)&&void 0!==r?r:"")})),(null===(n=e.state.exclude)||void 0===n?void 0:n.length)&&(i=r.variables.filter(t=>{var n,r;return!(null===(n=e.state.exclude)||void 0===n?void 0:n.includes(null!==(r=t.state.name)&&void 0!==r?r:""))})),a().createElement(a().Fragment,null,i.map(t=>a().createElement(l.Lp,{key:t.state.key,variable:t,layout:e.state.layout})))},(ye="Component")in(me=xe)?Object.defineProperty(me,ye,{value:Se,enumerable:!0,configurable:!0,writable:!0}):me[ye]=Se;var Pe=n(9598);function je(e){const t=(0,u.useStyles2)(Ce);return a().createElement(a().Fragment,null,a().createElement(u.Alert,{className:t.alert,severity:"info",title:"Welcome to Grafana Logs Drilldown!",onRemove:e.onRemove},a().createElement("div",null,"Check out our"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/logs/",rel:"noreferrer"},"Get started doc"),", or see"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://github.com/grafana/explore-logs/releases",rel:"noreferrer"},"recent changes"),".",a().createElement("br",null),"Help us shape the future of the app."," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://forms.gle/1sYWCTPvD72T1dPH9",rel:"noreferrer"},"Send us feedback")," ","or engage with us on"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://github.com/grafana/explore-logs/?tab=readme-ov-file#explore-logs",rel:"noreferrer"},"GitHub"),".")))}function Ce(e){return{alert:(0,he.css)({flex:"none"})}}var Le=n(2085),_e=n(1459);class Fe extends l.Bs{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Fe,"Component",({model:e})=>{const t=(0,le.cR)(e),n=l.jh.getTimeRange(e);t.useState(),(0,le.ir)(e).useState(),(0,le.iw)(e).useState(),(0,le.oY)(e).useState(),(0,le.Gk)(e).useState(),(0,le.Ku)(e).useState(),n.useState();const r=l.jh.getAncestor(e,ot),i=r.getContentScene();if(!(i instanceof Oe.Mn&&i.state.embedded))return K.v.error(new Error("Service scene does not exist, or is not embedded!")),null;const o=l.Go.getUrlState(r),{labelName:c,labelValue:d}=(0,ge.xb)(i,t);return a().createElement(u.LinkButton,{onClick:()=>{(0,p.EE)(p.NO.service_details,p.ir.service_details.embedded_go_to_explore_clicked)},href:s.urlUtil.renderUrl(X.bw.logs(d,c),o),variant:"secondary",icon:"arrow-right"},"Logs Drilldown")});const ke=()=>{const e=(0,u.useStyles2)(Te);return a().createElement("div",{className:e.wrapper},a().createElement("a",{href:"https://forms.gle/1sYWCTPvD72T1dPH9",className:e.feedback,title:"Share your thoughts about Logs in Grafana.",target:"_blank",rel:"noreferrer noopener"},a().createElement(u.Icon,{name:"comment-alt-message"})," Give feedback"))},Te=e=>({feedback:(0,he.css)({"&:hover":{color:e.colors.text.link},alignSelf:"center",color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize}),wrapper:(0,he.css)({display:"flex",gap:e.spacing(1),marginLeft:"auto",position:"relative",top:e.spacing(-1)})});var De=n(7478),Ne=n(3571);const $e=({onRemove:e,pattern:t,size:n="lg"})=>{const i=(0,u.useStyles2)(Me),[s,o]=(0,r.useState)(!1);return a().createElement("div",{className:i.pattern,onClick:()=>o(!s),onMouseLeave:()=>o(!1),onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||o(!s)},role:"button",tabIndex:0},a().createElement(u.Tag,{title:t,key:t,name:s?t:Be(t,n),className:i.tag}),a().createElement(u.Button,{"aria-label":"Remove pattern","data-testid":Ne.b.exploreServiceDetails.buttonRemovePattern,variant:"secondary",size:"sm",className:i.removeButton,onClick:e},a().createElement(u.Icon,{name:"times"})))},Ae={lg:Math.round(window.innerWidth/8),sm:50};function Be(e,t){const n=e.length;if(n<Ae[t])return e;const r=Math.round(.4*Ae[t]);return`${e.substring(0,r)} … ${e.substring(n-r)}`}const Me=e=>({pattern:(0,he.css)({cursor:"pointer",display:"flex",fontFamily:"monospace",gap:e.spacing(.25),overflow:"hidden"}),removeButton:(0,he.css)({paddingLeft:2.5,paddingRight:2.5}),tag:(0,he.css)({backgroundColor:e.colors.secondary.main,border:`solid 1px ${e.colors.secondary.border}`,borderBottomRightRadius:0,borderTopRightRadius:0,boxSizing:"border-box",color:e.colors.secondary.text,overflow:"hidden",padding:e.spacing(.25,.75),textOverflow:"ellipsis"})}),Ie=({onRemove:e,patterns:t})=>{const n=(0,u.useStyles2)(Re);if(!t||0===t.length)return null;const r=t.filter(e=>"include"===e.type),i=t.filter(e=>"include"!==e.type),s=n=>{(0,De.bN)(),e(t.filter(e=>e!==n)),(0,p.EE)(p.NO.service_details,p.ir.service_details.pattern_removed,{excludePatternsLength:i.length-("include"!==(null==n?void 0:n.type)?1:0),includePatternsLength:r.length-("include"===(null==n?void 0:n.type)?1:0),type:n.type})};return a().createElement("div",null,r.length>0&&a().createElement("div",{className:n.patternsContainer},a().createElement(u.Text,{variant:"bodySmall",weight:"bold","data-testid":Ne.b.patterns.buttonIncludedPattern},"Included pattern",t.length>1?"s":""),a().createElement("div",{className:n.patterns},r.map(e=>a().createElement($e,{key:e.pattern,pattern:e.pattern,size:"lg",onRemove:()=>s(e)})))),i.length>0&&a().createElement("div",{className:n.patternsContainer},a().createElement(u.Text,{variant:"bodySmall",weight:"bold","data-testid":Ne.b.patterns.buttonExcludedPattern},"Excluded pattern",i.length>1?"s":"",":"),a().createElement("div",{className:n.patterns},i.map(e=>a().createElement($e,{key:e.pattern,pattern:e.pattern,size:i.length>1?"sm":"lg",onRemove:()=>s(e)})))))};function Re(e){return{patterns:(0,he.css)({alignItems:"center",display:"flex",flexWrap:"wrap",gap:e.spacing(1)}),patternsContainer:(0,he.css)({overflow:"hidden"})}}class Ve extends l.Bs{onActivate(){l.jh.getAncestor(this,ot).state.embedded&&this.setState({embeddedLink:new Fe({})})}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}function ze(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ge(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Ve,"Component",({model:e})=>{const t=l.jh.getAncestor(e,ot),{controls:n,patterns:r}=t.useState(),i=l.jh.getAncestor(e,He),{levelsRenderer:s,lineFilterRenderer:c}=i.useState(),d=(0,o.useChromeHeaderHeight)(),p=(0,u.useStyles2)(e=>function(e,t){return{controlsContainer:(0,he.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),label:"controlsContainer",padding:e.spacing(2)}),controlsFirstRowContainer:(0,he.css)({[e.breakpoints.down("md")]:{flexDirection:"column-reverse"},alignItems:"flex-start",display:"flex",gap:e.spacing(2),justifyContent:"space-between",label:"controls-first-row"}),controlsRowContainer:(0,he.css)({[e.breakpoints.down("lg")]:{flexDirection:"column"},"&:empty":{display:"none"},alignItems:"flex-start",display:"flex",gap:e.spacing(2),label:"controls-row"}),controlsWrapper:(0,he.css)({display:"flex",flexDirection:"column",label:"controlsWrapper",marginTop:e.spacing(.375)}),filters:(0,he.css)({display:"flex",label:"filters"}),filtersWrap:(0,he.css)({alignItems:"flex-end",display:"flex",flexWrap:"wrap",gap:e.spacing(2),label:"filtersWrap",width:"calc(100% - 450)"}),firstRowWrapper:(0,he.css)({"& > div > div":{[e.breakpoints.down("lg")]:{flexDirection:"column"},gap:"16px",label:"first-row-wrapper"}}),stickyControlsContainer:(0,he.css)({background:e.colors.background.canvas,boxShadow:e.shadows.z1,gap:e.spacing(0),left:0,position:"sticky",top:t,zIndex:e.zIndex.navbarFixed}),timeRange:(0,he.css)({display:"flex",flexDirection:"row",gap:e.spacing(1),label:"timeRange"}),timeRangeDatasource:(0,he.css)({display:"flex",flexWrap:"wrap",gap:e.spacing(1),justifyContent:"flex-end",label:"timeRangeDatasource"})}}(e,null!=d?d:40));return a().createElement("div",{className:(0,he.cx)(p.controlsContainer,"sticky"===e.state.position?p.stickyControlsContainer:void 0)},a().createElement(a().Fragment,null,n&&a().createElement("div",{className:p.controlsFirstRowContainer},a().createElement("div",{className:p.filtersWrap},a().createElement("div",{className:(0,he.cx)(p.filters,p.firstRowWrapper)},n.map(e=>e instanceof l.G1?a().createElement(e.Component,{key:e.state.key,model:e}):null))),a().createElement("div",{className:p.controlsWrapper},!t.state.embedded&&a().createElement(ke,null),a().createElement("div",{className:p.timeRangeDatasource},e.state.embeddedLink&&a().createElement(e.state.embeddedLink.Component,{model:e.state.embeddedLink}),n.map(e=>e.state.key===Qe?a().createElement(e.Component,{key:e.state.key,model:e}):null),a().createElement("div",{className:p.timeRange},n.map(e=>e instanceof xe||e instanceof l.G1?null:a().createElement(e.Component,{key:e.state.key,model:e})))))),a().createElement("div",{className:p.controlsRowContainer},s&&a().createElement(s.Component,{model:s}),n&&a().createElement("div",{className:p.filtersWrap},a().createElement("div",{className:p.filters},n.map(e=>e instanceof xe&&e.state.key===We?a().createElement(e.Component,{key:e.state.key,model:e}):null)))),(0,oe.Qi)()&&a().createElement("div",{className:p.controlsRowContainer},n&&a().createElement("div",{className:p.filtersWrap},a().createElement("div",{className:p.filters},n.map(e=>e instanceof xe&&e.state.key===Ke?a().createElement(e.Component,{key:e.state.key,model:e}):null)))),a().createElement("div",{className:p.controlsRowContainer},a().createElement(Ie,{patterns:r,onRemove:e=>t.setState({patterns:e})})),a().createElement("div",{className:p.controlsRowContainer},c&&a().createElement(c.Component,{model:c}))))});const Ue=`${Pe.s_}.interceptBannerStorageKey`,We="vars-fields-metadata",Ke="vars-json-fields",Qe="vars-ds";class He extends l.Bs{onActivate(){const e=(0,X.FT)();this.setState({levelsRenderer:new Le.qV({}),lineFilterRenderer:new _e.Y({}),variableLayout:new Ve({position:e===f.G3.explore?"sticky":"relative"})})}dismiss(){this.setState({interceptDismissed:!0}),localStorage.setItem(Ue,"true")}constructor(e){super(Ge(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ze(e,t,n[t])})}return e}({},e),{interceptDismissed:!!localStorage.getItem(Ue)})),this.addActivationHandler(this.onActivate.bind(this))}}function qe(e){return{body:(0,he.css)({display:"flex",flexDirection:"column",flexGrow:1,gap:e.spacing(1),label:"body-wrapper",padding:`0 ${e.spacing(2)} ${e.spacing(2)}`}),bodyContainer:(0,he.css)({display:"flex",flexDirection:"column",flexGrow:1,minHeight:"100%"}),container:(0,he.css)({display:"flex",flexDirection:"column",flexGrow:1,maxWidth:"100vw",minHeight:"100%"}),controlsContainer:(0,he.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),label:"controlsContainer"})}}ze(He,"Component",({model:e})=>{const t=l.jh.getAncestor(e,ot),{contentScene:n}=t.useState(),{interceptDismissed:r,variableLayout:i}=e.useState();if(!n)return K.v.warn("content scene not defined"),null;const s=(0,u.useStyles2)(qe);return a().createElement("div",{className:s.bodyContainer},a().createElement("div",{className:s.container},!r&&a().createElement(je,{onRemove:()=>{e.dismiss()}}),i&&a().createElement(i.Component,{model:i}),a().createElement("div",{className:s.body},n&&a().createElement(n.Component,{model:n}))))});var Je=n(7796),Ye=n(9731);function Xe(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}const Ze=(e,t)=>{return(n=function*(){const n=[],r=yield(0,$.hJ)(e);if(!r)return;n.push((0,i.createContext)(i.ItemDataType.Datasource,{datasourceName:r.name,datasourceUid:r.uid,datasourceType:r.type}));const a=(0,le.cR)(e);a.state.filters.length>0&&n.push(...a.state.filters.map(e=>(0,i.createContext)(i.ItemDataType.LabelValue,{datasourceUid:r.uid,datasourceType:r.type,labelName:e.key,labelValue:e.value}))),t(n)},function(){var e=this,t=arguments;return new Promise(function(r,a){var i=n.apply(e,t);function s(e){Xe(i,r,a,s,o,"next",e)}function o(e){Xe(i,r,a,s,o,"throw",e)}s(void 0)})})();var n};var et=n(7985);function tt(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function nt(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){tt(i,r,a,s,o,"next",e)}function o(e){tt(i,r,a,s,o,"throw",e)}s(void 0)})}}function rt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function at(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){rt(e,t,n[t])})}return e}function it(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const st="showLogsButtonScene";class ot extends l.Bs{onActivate(){var e;const t={};this.setVariableProviders();l.jh.findByKeyAndType(this,st,Je.H).setState({hidden:!1}),this.state.contentScene||(t.contentScene=this.getContentScene()),this.setTagProviders(),this.setState(t),this.updatePatterns(this.state,(0,le.Ku)(this)),this.state.embedded||(this.resetVariablesIfNotInUrl((0,le.ir)(this),(0,le.n5)(ee.mB)),this.resetVariablesIfNotInUrl((0,le.iw)(this),(0,le.n5)(ee._Y))),this._subs.add(this.subscribeToState(e=>{this.updatePatterns(e,(0,le.Ku)(this))}));const n=l.jh.getTimeRange(this);this._subs.add(n.subscribeToState(this.limitMaxInterval(n))),this._subs.add(this.subscribeToEvent(W,this.subscribeToPasteTimeEvent));const r=(0,le.ir)(this).state.filters,a=(0,le.oY)(this).state.filters,s=(0,le.YS)(this);s.updateFilters([...a,...r]),this._subs.add(s.subscribeToState(this.subscribeToCombinedFieldsVariable));const o=R(this);return void 0!==this.state.embedded&&this.state.embedded!==(null===(e=(0,Q.JO)().getServiceSceneState())||void 0===e?void 0:e.embedded)&&(0,Q.JO)().setEmbedded(this.state.embedded),this._subs.add((0,i.isAssistantAvailable)().subscribe(e=>{e&&!this.assistantInitialized&&this.provideAssistantContext()})),()=>{o()}}getContentScene(){var e;if(this.state.embedded){const e=s.urlUtil.getUrlSearchParams(),t=(0,N.nv)(e),n=(0,N.sJ)(e);return new Oe.Mn({drillDownLabel:t||void 0,embedded:!0,pageSlug:n||f.G3.logs})}return function(e){if((0,X.FT)()===f.G3.explore)return new Ee.y({});return new Oe.Mn({drillDownLabel:e})}(null===(e=this.state.routeMatch)||void 0===e?void 0:e.params.breakdownLabel)}provideAssistantContext(){const e=(0,i.providePageContext)(`${Pe.Gy}/**`,[]);this._subs.add((0,le.S9)(this).subscribeToState(()=>nt(function*(){yield Ze(this,e)}).call(this))),this._subs.add((0,le.cR)(this).subscribeToState(()=>nt(function*(){yield Ze(this,e)}).call(this))),this.assistantInitialized=!0}setTagProviders(){this.setLabelsProviders()}setLabelsProviders(){const e=(0,le.cR)(this);e._getOperators=()=>(0,ge.Ht)(e),e.setState({getTagKeysProvider:re,getTagValuesProvider:pe})}limitMaxInterval(e){return(t,n)=>{const{jsonData:r}=d.plugin.meta;if(null==r?void 0:r.interval)try{var a;const i=s.rangeUtil.intervalToSeconds(null!==(a=null==r?void 0:r.interval)&&void 0!==a?a:"");if(!i)return;const c=t.value.to.diff(t.value.from,"seconds");if(c>i){if(c<=n.value.to.diff(n.value.from,"seconds"))e.setState({from:n.from,to:n.to,value:n.value});else{const t=new l.JZ(we.sp);e.setState({from:t.state.from,to:t.state.to,value:t.state.value})}(0,o.getAppEvents)().publish({payload:["Time range interval exceeds maximum interval configured by the administrator."],type:s.AppEvents.alertWarning.name}),(0,p.EE)("all","interval_too_long",{attempted_duration_seconds:c,configured_max_interval:i})}}catch(e){console.error(e)}}}setVariableProviders(){const e=(0,le.iw)(this),t=(0,le.YS)(this);t._getOperators=()=>(0,ge.Ht)(t),e.setState({getTagKeysProvider:this.getLevelsTagKeysProvider(),getTagValuesProvider:this.getLevelsTagValuesProvider()}),t.setState({getTagKeysProvider:this.getCombinedFieldsTagKeysProvider(),getTagValuesProvider:this.getCombinedFieldsTagValuesProvider()})}getCombinedFieldsTagKeysProvider(){return(e,t)=>{const n=(0,le.oY)(this),r=(0,le.ir)(this),a=(0,v.O)(ee.sL),i=n.state.filters.filter(e=>e.key!==t),s=r.state.filters.filter(e=>e.key!==t),o=this.renderVariableFilters(ee.mB,s),c=this.renderVariableFilters(ee._P,i),u=a.replace(ee.Gd,o).replace(ee.w0,c);return ae({expr:l.jh.interpolate(this,u),sceneRef:this,timeRange:l.jh.getTimeRange(this).state.value,variableType:ee.sL})}}getCombinedFieldsTagValuesProvider(){return(e,t)=>{const n=(0,v.O)(ee.sL),r=(0,le.oY)(this),a=(0,le.ir)(this),i=r.state.filters.filter(e=>e.key!==t.key&&(0,H.BG)(e.operator)),s=a.state.filters.filter(e=>e.key!==t.key&&(0,H.BG)(e.operator)),o=this.renderVariableFilters(ee.mB,s),c=this.renderVariableFilters(ee._P,i),u=n.replace(ee.Gd,o).replace(ee.w0,c),d=(0,et.Sh)(this,u);return de(t,e,d,this,l.jh.getTimeRange(this).state.value,ee.sL)}}getLevelsTagKeysProvider(){return(e,t)=>{const n=e.state.filters.filter(e=>e.key!==t),r=this.renderVariableFilters(ee._Y,n),a=(0,v.O)(ee._Y).replace(ee.Gd,r);return ae({expr:l.jh.interpolate(this,a),sceneRef:this,timeRange:l.jh.getTimeRange(this).state.value,variableType:ee._Y})}}getLevelsTagValuesProvider(){return(e,t)=>{const n=e.state.filters.filter(e=>e.key!==t.key&&e.operator===m.w7.Equal),r=this.renderVariableFilters(ee._Y,n),a=(0,v.O)(ee._Y).replace(ee.Gd,r),i=(0,et.Sh)(this,a);return de(t,e,i,this,l.jh.getTimeRange(this).state.value,ee._Y)}}renderVariableFilters(e,t){if(e===ee.mB)return(0,et.ZX)(t);if(e===ee._P)return(0,et.E3)(t);if(e===ee._Y)return(0,et.E3)(t);{const e=new Error("getFieldsTagValuesProvider only supports fields, metadata, and levels");throw K.v.error(e),e}}resetVariablesIfNotInUrl(e,t){const n=o.locationService.getLocation();null===new URLSearchParams(n.search).get(t)&&e.setState({filters:[]})}updatePatterns(e,t){var n;const r=(0,Y.M)(null!==(n=e.patterns)&&void 0!==n?n:[]);t.changeValueTo(r)}getUrlState(){return{patterns:JSON.stringify(this.state.patterns)}}updateFromUrl(e){const t={};e.patterns&&"string"==typeof e.patterns&&(t.patterns=JSON.parse(e.patterns)),this.setState(t)}constructor(e){var t;const{jsonData:n}=d.plugin.meta;var r,a,i;const u=null!==(i=null!==(a=null!==(r=null==n?void 0:n.dataSource)&&void 0!==r?r:(0,oe.QB)())&&void 0!==a?a:(0,oe.x0)())&&void 0!==i?i:"grafanacloud-logs",{unsub:p,variablesScene:f}=function(e,t,n,r,a){const i=new J.R({allowCustomValue:!0,datasource:ee.eL,expressionBuilder:et.VW,hide:c.zL.dontHide,key:"adhoc_service_filter",label:"Labels",layout:"combobox",name:ee.MB,onAddCustomValue:et.c0,readonlyFilters:(null!=t?t:[]).map(e=>it(at({},e),{origin:r,readOnly:!0}))});i._getOperators=function(){return q.II};const s=new l.H9({allowCustomValue:!0,applyMode:"manual",expressionBuilder:et.ZX,hide:c.zL.hideVariable,label:"Detected fields",layout:"combobox",name:ee.mB});s._getOperators=()=>q.II;const o=new l.H9({allowCustomValue:!0,applyMode:"manual",expressionBuilder:e=>(0,et.E3)(e),hide:c.zL.hideVariable,label:"Metadata",layout:"combobox",name:ee._P});o._getOperators=()=>q.II;const u=new l.H9({allowCustomValue:!0,applyMode:"manual",hide:c.zL.hideVariable,label:"Fields",layout:"combobox",name:ee.sL,onAddCustomValue:et.PP,skipUrlSync:!0}),d=new l.H9({applyMode:"manual",expressionBuilder:et._q,hide:c.zL.hideVariable,label:"Error levels",layout:"vertical",name:ee._Y,supportsMultiValueOperators:!0}),p=new l.H9({expressionBuilder:et.CY,filters:null!=a?a:[],getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),hide:c.zL.hideVariable,layout:"horizontal",name:ee.NW});p._getOperators=()=>q.eb;const g=new l.mI({hide:n?c.zL.hideVariable:c.zL.dontHide,label:"Data source",name:ee.EY,pluginId:"loki",value:e}),f=g.subscribeToState(e=>{const t=`${e.value}`;e.value&&(0,oe.ke)(t)}),v=new l.H9({allowCustomValue:!0,expressionBuilder:(0,et.Hs)(),getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),name:ee.lV}),b=new l.H9({allowCustomValue:!0,expressionBuilder:(0,et.tR)(),getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),layout:"horizontal",name:ee.pw});return{unsub:f,variablesScene:new l.Pj({variables:[b,g,i,s,d,o,v,u,new l.yP({hide:c.zL.hideVariable,name:ee.uw,value:""}),new l.H9({expressionBuilder:et.CY,hide:c.zL.hideVariable,name:ee.WM}),p,new h.m({hide:c.zL.hideVariable,name:ee.QE,options:[{label:ee.YN,value:ee.YN}],skipUrlSync:!0,value:ee.YN})]})}}(u,null==e?void 0:e.readOnlyLabelFilters,e.embedded,e.embedderName,e.defaultLineFilters),v=[new l.G1({children:[new l.vA({body:new xe({include:[ee.MB],key:"vars-labels",layout:"vertical"})}),new Je.H({disabled:!0,key:st})],direction:"row",key:"vars-row__datasource-labels-timepicker-button"}),new xe({include:[ee._P],key:"vars-metadata",layout:"vertical"}),new xe({include:[ee.mB],key:"vars-fields",layout:"vertical"}),new xe({include:[ee.EY],key:Qe,layout:"horizontal"}),new xe({include:[ee.sL],key:We,layout:"vertical"}),new xe({include:[ee.lV,ee.pw],key:Ke,layout:"vertical"}),new l.KE({key:"vars-timepicker"}),new l.WM({key:"vars-refresh"})];var m,y,S,w;"explore"===(0,X.FT)()&&o.config.featureToggles.exploreLogsAggregatedMetrics&&v.push(new Ye.s({isOpen:!1,key:"vars-toolbar"})),super(it(at({$timeRange:null!==(m=e.$timeRange)&&void 0!==m?m:new l.JZ({}),$variables:null!==(y=e.$variables)&&void 0!==y?y:f,controls:null!==(S=e.controls)&&void 0!==S?S:v,embedded:null!==(w=e.embedded)&&void 0!==w&&w,patterns:[]},e),{body:new He({})})),rt(t=this,"_urlSync",new l.So(t,{keys:["patterns"]})),rt(t,"assistantInitialized",!1),rt(t,"subscribeToCombinedFieldsVariable",(e,n)=>{if(!(0,g.B)(e.filters,null==n?void 0:n.filters)){const n=e.filters.filter(e=>(0,b.OH)(e)),r=e.filters.filter(e=>!(0,b.OH)(e));(0,le.ir)(t).updateFilters(r),(0,le.oY)(t).updateFilters(n)}}),rt(t,"subscribeToPasteTimeEvent",()=>nt(function*(){const e=yield B(function*(){const e=yield navigator.clipboard.readText();let t;try{t=JSON.parse(e);const n=(0,N.OK)(t);if(n)return{isError:!1,range:n}}catch(e){}return{isError:!0,range:e}})();if(e.isError)return;const n=l.jh.getTimeRange(t),r="string"==typeof e.range.to?e.range.to:void 0,a="string"==typeof e.range.from?e.range.from:void 0,i=s.rangeUtil.convertRawToRange(e.range);n&&i?n.setState({from:a,to:r,value:i}):K.v.error(new Error("Invalid time range from clipboard"),{from:null!=a?a:"",msg:"Invalid time range from clipboard",sceneTimeRange:typeof n,to:null!=r?r:""})})()),t._subs.add(p),t.addActivationHandler(t.onActivate.bind(t)),(0,$.hJ)(t).then(e=>{t.setState({ds:e})})}}rt(ot,"Component",({model:e})=>{const{body:t}=e.useState();return(0,le.S9)(e).state.options.length?t?a().createElement(t.Component,{model:t}):a().createElement(u.LoadingPlaceholder,{text:"Loading..."}):a().createElement(ve,null)})},2085:(e,t,n)=>{n.d(t,{dm:()=>m,kz:()=>v,qV:()=>b});var r=n(5959),a=n.n(r),i=n(6089),s=n(6865),o=n(2007),l=n(6854),c=n(7478),u=n(3571),d=n(5553),p=n(20);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){g(e,t,n[t])})}return e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const v="levels-var-custom-renderer";class b extends s.Bs{onActivate(){this.onFilterChange(),this._subs.add((0,d.iw)(this).subscribeToEvent(s.oh,()=>{this.onFilterChange()}))}onFilterChange(){const e=(0,d.iw)(this);this.setState({options:e.state.filters.map(e=>{var t,n;return{selected:!0,text:null!==(n=null===(t=e.valueLabels)||void 0===t?void 0:t[0])&&void 0!==n?n:e.value,value:e.value}})})}constructor(e){super(f(h({},e),{isLoading:!1,isOpen:!1,key:v,visible:!1})),g(this,"getTagValues",()=>{var e,t;this.setState({isLoading:!0});const n=(0,d.iw)(this);var r;const a=null==n||null===(t=n.state)||void 0===t||null===(e=t.getTagValuesProvider)||void 0===e?void 0:e.call(t,n,null!==(r=n.state.filters[0])&&void 0!==r?r:{key:p.e4});null==a||a.then(e=>{Array.isArray(e.values)&&this.setState({isLoading:!1,options:e.values.map(e=>{var t;return{selected:n.state.filters.some(t=>t.value===e.text),text:e.text,value:null!==(t=e.value)&&void 0!==t?t:e.text}})})})}),g(this,"updateFilters",(e,t)=>{var n;const r=(0,d.iw)(this),a=null===(n=this.state.options)||void 0===n?void 0:n.filter(e=>e.selected);var i;r.updateFilters(null!==(i=null==a?void 0:a.map(e=>({key:p.e4,operator:l.w7.Equal,value:e.text})))&&void 0!==i?i:[],{forcePublish:t,skipPublish:e})}),g(this,"onChangeOptions",e=>{var t;(0,c.bN)(),this.setState({options:null===(t=this.state.options)||void 0===t?void 0:t.map(t=>e.some(e=>e.value===t.value)?f(h({},t),{selected:!0}):f(h({},t),{selected:!1}))}),this.state.isOpen?this.updateFilters(!0):this.updateFilters(!1)}),g(this,"openSelect",e=>{this.setState({isOpen:e})}),g(this,"onCloseMenu",()=>{this.openSelect(!1),this.updateFilters(!1,!0)}),this.addActivationHandler(this.onActivate.bind(this))}}function m(e){const t=s.jh.findObject(e,e=>e instanceof b);t instanceof b&&t.onFilterChange()}g(b,"Component",({model:e})=>{const{isLoading:t,isOpen:n,options:r,visible:i}=e.useState(),l=(0,o.useStyles2)(y);return(0,d.iw)(e).useState(),i?a().createElement("div",{"data-testid":u.b.variables.levels.inputWrap,className:l.wrapper},a().createElement(s.Zx,{layout:"vertical",label:"Log levels"}),a().createElement(o.MultiSelect,{"aria-label":"Log level filters",prefix:a().createElement(o.Icon,{size:"lg",name:"filter"}),placeholder:"All levels",className:l.control,onChange:e.onChangeOptions,onCloseMenu:()=>e.onCloseMenu(),onOpenMenu:e.getTagValues,onFocus:()=>e.openSelect(!0),menuShouldPortal:!0,isOpen:n,isLoading:t,isClearable:!0,blurInputOnSelect:!1,closeMenuOnSelect:!1,openMenuOnFocus:!0,showAllSelectedWhenOpen:!0,hideSelectedOptions:!1,value:null==r?void 0:r.filter(e=>e.selected),options:null==r?void 0:r.map(e=>({label:e.text,value:e.value}))})):null});const y=()=>({control:(0,i.css)({flex:"1"}),wrapper:(0,i.css)({flex:"0 0 auto",whiteSpace:"nowrap"})})},1459:(e,t,n)=>{n.d(t,{Y:()=>w,F:()=>O});var r=n(5959),a=n.n(r),i=n(6089),s=n(3241),o=n(6865),l=n(2007),c=n(4509),u=n(6854),d=n(7478),p=n(5553),g=n(72);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function v({onClick:e,props:t}){const[n,i]=(0,r.useState)(!1),s=(0,l.useStyles2)(b);return a().createElement(a().Fragment,null,a().createElement("span",null,a().createElement("div",{className:s.titleWrap},a().createElement("span",null,"Line filter"),a().createElement(l.IconButton,{onClick:e,name:"times",size:"xs","aria-label":"Remove line filter"})),a().createElement("span",{className:s.collapseWrap},a().createElement(g._,f(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){h(e,t,n[t])})}return e}({},t),{focus:n,setFocus:i,type:"variable"})),n&&a().createElement(l.IconButton,{className:s.collapseBtn,tooltip:"Collapse",size:"lg","aria-label":"Collapse filter",onClick:()=>i(!1),name:"table-collapse-all"}))))}const b=e=>({collapseBtn:(0,i.css)({marginLeft:e.spacing(1)}),collapseWrap:(0,i.css)({display:"flex"}),titleWrap:(0,i.css)({display:"flex",fontSize:e.typography.bodySmall.fontSize,gap:e.spacing(1),marginBottom:e.spacing(.5)})});function m(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){m(e,t,n[t])})}return e}function S(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class w extends o.Bs{isFilterExclusive({operator:e}){return e===u.cK.negativeMatch||e===u.cK.negativeRegex}updateFilter(e,t,n=!0){n?(this.updateVariableLineFilter(e,t,!0),this.updateVariableDebounced(e,t,!1,!0)):this.updateVariableLineFilter(e,t)}constructor(...e){super(...e),m(this,"handleEnter",(e,t,n)=>{"Enter"===e.key&&((0,d.bN)(),this.updateVariableLineFilter(n,S(y({},n),{value:t})))}),m(this,"onRegexToggle",e=>{let t;switch(e.operator){case u.cK.match:t=u.cK.regex;break;case u.cK.negativeMatch:t=u.cK.negativeRegex;break;case u.cK.regex:t=u.cK.match;break;case u.cK.negativeRegex:t=u.cK.negativeMatch;break;default:throw new Error("Invalid operator!")}this.updateFilter(e,S(y({},e),{operator:t}),!1)}),m(this,"onToggleExclusive",e=>{let t;switch(e.operator){case u.cK.match:t=u.cK.negativeMatch;break;case u.cK.negativeMatch:t=u.cK.match;break;case u.cK.regex:t=u.cK.negativeRegex;break;case u.cK.negativeRegex:t=u.cK.regex;break;default:throw new Error("Invalid operator!")}this.updateFilter(e,S(y({},e),{operator:t}),!1)}),m(this,"onCaseSensitiveToggle",e=>{const t=e.key===u.ld.caseSensitive?u.ld.caseInsensitive:u.ld.caseSensitive;this.updateFilter(e,S(y({},e),{key:t}),!1)}),m(this,"onInputChange",(e,t)=>{this.updateFilter(t,S(y({},t),{value:e.target.value}),!0)}),m(this,"removeFilter",e=>{(0,d.bN)();const t=(0,p.Gk)(this),n=t.state.filters.filter(t=>void 0!==t.keyLabel&&t.keyLabel!==e.keyLabel);t.setState({filters:n})}),m(this,"updateVariableLineFilter",(e,t,n=!1,r=!1)=>{const a=(0,p.Gk)(this),i=a.state.filters.filter(t=>void 0!==t.keyLabel&&t.keyLabel!==e.keyLabel);a.updateFilters([{key:t.key,keyLabel:e.keyLabel,operator:t.operator,value:t.value},...i],{forcePublish:r,skipPublish:n}),(0,c.EE)(c.NO.service_details,c.ir.service_details.search_string_in_variables_changed,{caseSensitive:t.key,containsLevel:e.value.toLowerCase().includes("level"),operator:t.operator,searchQueryLength:e.value.length})}),m(this,"updateVariableDebounced",(0,s.debounce)((e,t,n=!1,r=!1)=>{this.updateVariableLineFilter(e,t,n,r)},1e3))}}function O(e){e.sort((e,t)=>{var n,r;return parseInt(null!==(n=e.keyLabel)&&void 0!==n?n:"0",10)-parseInt(null!==(r=t.keyLabel)&&void 0!==r?r:"0",10)})}function E(e){return{lineFiltersWrap:(0,i.css)({display:"flex",flexWrap:"wrap",gap:`${e.spacing(.25)} ${e.spacing(2)}`,label:"lineFiltersWrap"})}}m(w,"Component",({model:e})=>{const t=(0,p.Gk)(e),{filters:n}=t.useState(),r=(0,l.useStyles2)(E);return O(n),n.length?a().createElement("div",{className:r.lineFiltersWrap},n.map(t=>{const n={caseSensitive:t.key===u.ld.caseSensitive,exclusive:e.isFilterExclusive(t),handleEnter:(n,r)=>e.handleEnter(n,t.value,t),lineFilter:t.value,onCaseSensitiveToggle:()=>e.onCaseSensitiveToggle(t),onInputChange:n=>e.onInputChange(n,t),onRegexToggle:()=>e.onRegexToggle(t),regex:t.operator===u.cK.regex||t.operator===u.cK.negativeRegex,setExclusive:()=>e.onToggleExclusive(t),updateFilter:(n,r)=>e.updateFilter(t,S(y({},t),{value:n}),r)};return a().createElement(v,{key:t.keyLabel,onClick:()=>e.removeFilter(t),props:n})})):null})},7796:(e,t,n)=>{n.d(t,{H:()=>g});var r=n(5959),a=n.n(r),i=n(6089),s=n(6865),o=n(2007),l=n(7478),c=n(708),u=n(3571),d=n(5553);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends s.Bs{onActivate(){const e=(0,d.cR)(this),t=e.state.filters.some(e=>(0,c.BG)(e.operator));this.setState({disabled:!t}),e.subscribeToState(e=>{const t=e.filters.some(e=>(0,c.BG)(e.operator));this.setState({disabled:!t})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){p(e,t,n[t])})}return e}({},e)),p(this,"getLink",()=>{const e=(0,d.cR)(this).state.filters.find(e=>(0,c.BG)(e.operator));return e?(0,l.k9)(e.key,e.value):""}),this.addActivationHandler(this.onActivate.bind(this))}}function h(e){return{button:(0,i.css)({[e.breakpoints.down("lg")]:{alignSelf:"flex-end"},[e.breakpoints.down("md")]:{alignSelf:"flex-start",marginTop:e.spacing(1)},alignSelf:"flex-start",marginTop:"22px"})}}p(g,"Component",({model:e})=>{const{disabled:t,hidden:n}=e.useState(),r=(0,o.useStyles2)(h);if(!0===n)return null;const i=e.getLink();return a().createElement(o.LinkButton,{"data-testid":u.b.index.header.showLogsButton,disabled:t||!i,fill:"outline",className:r.button,href:i},"Show logs")})},9731:(e,t,n)=>{n.d(t,{s:()=>f});var r=n(5959),a=n.n(r),i=n(6089),s=n(8531),o=n(6865),l=n(2007),c=n(2533),u=n(4509),d=n(3571),p=n(173);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const h=`${c.id}.serviceSelection.aggregatedMetrics`;class f extends o.Bs{constructor(e){const t=localStorage.getItem(h),n=s.config.featureToggles.exploreLogsAggregatedMetrics&&"false"!==t;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){g(e,t,n[t])})}return e}({isOpen:!1,options:{aggregatedMetrics:{active:null!=n&&n,disabled:!1,userOverride:"true"===t}}},e)),g(this,"toggleAggregatedMetricsOverride",()=>{const e=!this.state.options.aggregatedMetrics.active;(0,u.EE)(u.NO.service_selection,u.ir.service_selection.aggregated_metrics_toggled,{enabled:e}),localStorage.setItem(h,e.toString()),this.setState({options:{aggregatedMetrics:{active:e,disabled:this.state.options.aggregatedMetrics.disabled,userOverride:e}}})}),g(this,"onToggleOpen",e=>{this.setState({isOpen:e})})}}function v(e){return{heading:(0,i.css)({fontWeight:e.typography.fontWeightMedium,paddingBottom:e.spacing(2)}),options:(0,i.css)({alignItems:"center",columnGap:e.spacing(2),display:"grid",gridTemplateColumns:"1fr 50px",rowGap:e.spacing(1)}),popover:(0,i.css)({background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3,display:"flex",flexDirection:"column",marginRight:e.spacing(2),padding:e.spacing(2)})}}g(f,"Component",({model:e})=>{const{isOpen:t,options:n}=e.useState(),r=(0,l.useStyles2)(v),i=()=>a().createElement("div",{className:r.popover,role:"dialog","aria-modal":"true","aria-label":"Query options",onClick:e=>e.stopPropagation()},a().createElement("div",{className:r.heading},"Query options"),a().createElement("div",{className:r.options},a().createElement("div",{title:"Aggregated metrics will return service queries results much more quickly, but with lower resolution"},"Aggregated metrics"),a().createElement("span",{title:n.aggregatedMetrics.disabled?`Aggregated metrics can only be enabled for queries starting after ${p.X.toLocaleString()}`:""},a().createElement(l.Switch,{label:"Toggle aggregated metrics","data-testid":d.b.index.aggregatedMetricsToggle,value:n.aggregatedMetrics.active,disabled:n.aggregatedMetrics.disabled,onChange:e.toggleAggregatedMetricsOverride}))));return n.aggregatedMetrics?a().createElement(l.Dropdown,{overlay:i,placement:"bottom",onVisibleChange:e.onToggleOpen},a().createElement(l.ToolbarButton,{icon:"cog",variant:"canvas",isOpen:t,"data-testid":d.b.index.aggregatedMetricsMenu})):a().createElement(a().Fragment,null)})},6991:(e,t,n)=>{n.d(t,{Oo:()=>w,c:()=>S,sp:()=>v,uL:()=>y});var r=n(5959),a=n.n(r),i=n(7781),s=n(8531),o=n(6865),l=n(2007),c=n(7839),u=n(5953),d=n(7478),p=n(9598),g=n(9683),h=n(5002),f=n(8714);const v={from:"now-15m",to:"now"};function b(e){return new o.P1({body:new f.P({$timeRange:new o.JZ(v),routeMatch:e})})}function m(e){const{component:t,isLoading:n}=(0,s.usePluginComponent)("grafana-lokiexplore-app/embedded-logs-exploration/v1"),i=(0,r.useMemo)(()=>t,[n]);return n?a().createElement(l.LoadingPlaceholder,{text:"Loading..."}):i?a().createElement(i,e):(console.error("No grafana-lokiexplore-app/embedded-logs-exploration/v1 component found in the Grafana registry! You might need to restart your Grafana instance?"),null)}function y(){return new o.jD({drilldowns:[{getPage:(e,t)=>O(e,t,c.G3.embed),routePath:g.HU.embed}],getScene:e=>function(){const e="now-15m",t="now",n={from:(0,i.dateTimeParse)(e),raw:{from:e,to:t},to:(0,i.dateTimeParse)(t)},r={embedded:!0,embedderName:"EmbeddedLogs",query:'{service_name="tempo-distributor"} |~ "(?i)Error"',timeRangeState:new o.JZ({from:e,to:t,value:n}).state};return new o.P1({body:new o.dM({component:m,props:r})})}(),layout:i.PageLayoutType.Custom,routePath:`${c.G3.embed}`,title:"Grafana Logs Drilldown — Embedded",url:(0,p._F)(c.G3.embed)})}function S(){return new o.jD({drilldowns:[{defaultRoute:!0,getPage:(e,t)=>O(e,t,c.G3.logs),routePath:g.HU.logs},{getPage:(e,t)=>O(e,t,c.G3.labels),routePath:g.HU.labels},{getPage:(e,t)=>O(e,t,c.G3.patterns),routePath:g.HU.patterns},{getPage:(e,t)=>O(e,t,c.G3.fields),routePath:g.HU.fields},{getPage:(e,t)=>E(e,t,c._J.label),routePath:g.KL.label},{getPage:(e,t)=>E(e,t,c._J.field),routePath:g.KL.field}],getScene:e=>b(e),layout:i.PageLayoutType.Custom,preserveUrlKeys:g.Zt,routePath:`${c.G3.explore}/*`,title:"Grafana Logs Drilldown",url:(0,p._F)(c.G3.explore)})}function w(){return new o.jD({$behaviors:[()=>{(0,d.Ns)()}],getScene:()=>new o.P1({body:new o.G1({children:[],direction:"column"})}),hideFromBreadcrumbs:!0,routePath:"*",title:"",url:i.urlUtil.renderUrl(p.Gy,void 0)})}function O(e,t,n){const{labelName:r,labelValue:a}=(0,g.XJ)(e);return new o.jD({getParentPage:()=>t,getScene:e=>b(e),layout:i.PageLayoutType.Custom,preserveUrlKeys:g.tm,routePath:g.HU[n],title:(0,h.Zr)(n),url:g.bw[n](a,r)})}function E(e,t,n){const{breakdownLabel:r,labelName:a,labelValue:s}=(0,g.XJ)(e);if(!r){const e=new Error("Breakdown value missing!");throw u.v.error(e,{breakdownLabel:null!=r?r:"",labelName:a,labelValue:s,msg:"makeBreakdownValuePage: Breakdown value missing!"}),e}return new o.jD({getParentPage:()=>t,getScene:e=>b(e),layout:i.PageLayoutType.Custom,preserveUrlKeys:g.tm,routePath:g.KL[n],title:(0,h.Zr)(r),url:g.mC[n](s,a,r)})}},5700:(e,t,n)=>{n.d(t,{Ci:()=>V,GD:()=>z,Ze:()=>R,iD:()=>U,K_:()=>H});var r=n(5959),a=n.n(r),i=n(6089),s=n(1269),o=n(219),l=n(8531),c=n(6865),u=n(4509),d=n(7389),p=n(5953),g=n(4907),h=n(7985),f=n(5719),v=n(4351),b=n(8714),m=n(2007);const y=n.p+"3d96a93cfcb32df74eef.svg";function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){S(e,t,n[t])})}return e}function O(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class E extends c.Bs{constructor(e){super(O(w({},e),{queries:[]})),S(this,"onActivate",()=>{(0,f.hJ)(this).then(e=>{this.setState({ds:e})}),this._subs.add(this.subscribeToState((e,t)=>{this.state.queries.length||this.getQueries(),!this.state.context&&this.state.queries.length&&this.getContext()}))}),S(this,"getQueries",()=>{const e=c.jh.getData(this),t=(0,f.UX)(e,e=>e instanceof c.dt,c.dt);if(t){const e=this.state.frame?x(this.state.frame):null,n=t.state.queries.map(n=>{var r;return O(w({},n),{datasource:null!==(r=n.datasource)&&void 0!==r?r:void 0,expr:(0,h.Sh)(t,n.expr),legendFormat:(null==e?void 0:e.name)?`{{ ${e.name} }}`:c.jh.interpolate(t,n.legendFormat)})});JSON.stringify(n)!==JSON.stringify(this.state.queries)&&this.setState({queries:n})}}),S(this,"getFieldConfig",()=>{var e;const t=(0,f.UX)(this,e=>e instanceof c.Eb,c.Eb),n=c.jh.getData(this),r=null==n||null===(e=n.state.data)||void 0===e?void 0:e.series;let a=null==t?void 0:t.state.fieldConfig;if(a&&(null==r?void 0:r.length))for(const e of r)for(const t of e.fields){const e=Object.keys(t.config).map(e=>({id:e,value:t.config[e]})),n=a.overrides.find(e=>{var n,r;return e.matcher.options===(null!==(r=null!==(n=t.config.displayNameFromDS)&&void 0!==n?n:t.config.displayName)&&void 0!==r?r:t.name)&&"byName"===e.matcher.id});var i,s;if(!n)a.overrides.unshift({matcher:{id:"byName",options:null!==(s=null!==(i=t.config.displayNameFromDS)&&void 0!==i?i:t.config.displayName)&&void 0!==s?s:t.name},properties:e});n&&JSON.stringify(n.properties)!==JSON.stringify(e)&&(n.properties=e)}return a}),S(this,"getContext",()=>{const e=this.getFieldConfig(),{ds:t,fieldName:n,labelName:r,queries:a,type:i}=this.state,s=c.jh.getTimeRange(this);if(!s||!a||!(null==t?void 0:t.uid))return;const o={datasource:{uid:t.uid},drillDownLabel:n,fieldConfig:e,id:`${JSON.stringify(a)}${r}${n}`,logoPath:y,origin:"Grafana Logs Drilldown",queries:a,timeRange:w({},s.state.value),title:`${r}${n?` > ${n}`:""}`,type:null!=i?i:"timeseries",url:window.location.href};JSON.stringify(o)!==JSON.stringify(this.state.context)&&this.setState({context:o})}),this.addActivationHandler(this.onActivate)}}S(E,"Component",({model:e})=>{const{context:t}=e.useState(),{links:n}=(0,l.usePluginLinks)({context:t,extensionPointId:d.R6.MetricInvestigation});return a().createElement(a().Fragment,null,n.filter(e=>"grafana-investigations-app"===e.pluginId&&e.onClick).map(e=>{var t;return a().createElement(m.IconButton,{tooltip:e.description,"aria-label":"extension-link-to-open-exploration",key:e.id,name:null!==(t=e.icon)&&void 0!==t?t:"panel-add",onClick:t=>{e.onClick&&e.onClick(t)}})}))});const x=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{};if(1!==Object.keys(r).length)return;const a=Object.keys(r)[0];return{name:a,value:r[a]}};var P=n(8351),j=n(2969),C=n(6887),L=n(5865),_=n(7781),F=n(2165),k=n(8428);n(3571);const T=(e,t,n=!1)=>{t||(t=(0,f.u9)(e)),t=t.replace(/\s+/g," ").trimEnd();const r=(0,f.U4)(e),a=c.jh.getTimeRange(e).state.value,i=(0,v.N$)(e),s=(0,v.k5)(),o=function(){const e=new URLSearchParams(window.location.search).get("urlColumns");if(e)try{const t=(0,k.aJ)(JSON.parse(e));let n={};for(const e in t)n[e]=t[e];return n}catch(e){console.error(e)}return}(),u=JSON.stringify({"loki-explore":{range:(0,_.toURLRange)(a.raw),queries:[{refId:"logs",expr:t,datasource:r}],panelsState:{logs:{displayedFields:i,visualisationType:"json"===s?"logs":s,columns:o,labelFieldName:"table"===s?F.bz:void 0}},datasource:r}});var d;const p=null!==(d=l.config.appSubUrl)&&void 0!==d?d:"",g=_.urlUtil.renderUrl(`${p}/explore`,{panes:u,schemaVersion:1});return n&&window.open(g,"_blank"),g};function D(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function N(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){D(i,r,a,s,o,"next",e)}function o(e){D(i,r,a,s,o,"throw",e)}s(void 0)})}}function $(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function A(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const B="Add to investigation",M="investigations_divider",I="Investigations";var R=function(e){return e.timeseries="timeseries",e.histogram="histogram",e}({}),V=function(e){return e.collapsed="Collapse",e.expanded="Expand",e}({});class z extends c.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){var t,n;super(A(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){$(e,t,n[t])})}return e}({},e),{addInvestigationsLink:null===(n=e.addInvestigationsLink)||void 0===n||n})),(t=this).addActivationHandler(()=>{var e,n,r,a,i,s,u;const d=[{text:"Navigation",type:"group"},{href:U(t),iconClassName:"compass",onClick:()=>W(),shortcut:"p x",text:"Explore"}];let p;try{p=c.jh.getAncestor(t,c.Eb)}catch(e){return void t.setState({body:new c.Lw({items:d})})}var h;(t.setState({investigationsButton:new E({fieldName:null===(e=t.state.investigationOptions)||void 0===e?void 0:e.fieldName,frame:null===(n=t.state.investigationOptions)||void 0===n?void 0:n.frame,labelName:(null===(r=t.state.investigationOptions)||void 0===r?void 0:r.getLabelName)?null===(a=t.state.investigationOptions)||void 0===a?void 0:a.getLabelName():null===(i=t.state.investigationOptions)||void 0===i?void 0:i.labelName,type:null===(s=t.state.investigationOptions)||void 0===s?void 0:s.type})}),t.state.addInvestigationsLink)&&(null===(h=t.state.investigationsButton)||void 0===h||h.activate());(t.state.panelType||(null==p?void 0:p.state.collapsible))&&function(e){e.push({text:"",type:"divider"}),e.push({text:"Visualization",type:"group"})}(d),(null==p?void 0:p.state.collapsible)&&function(e,t){const n=c.jh.getAncestor(t,c.Eb);e.push({iconClassName:n.state.collapsed?"table-collapse-all":"table-expand-all",onClick:()=>{const e=n.state.collapsed?"Expand":"Collapse",r=c.jh.getAncestor(t,c.G1);(0,L.Zb)(r,e),n.setState({collapsed:!n.state.collapsed}),(0,v.IW)("collapsed",e)},text:n.state.collapsed?"Expand":"Collapse"})}(d,t),t.state.panelType&&function(e,t){e.push({iconClassName:"histogram"!==t.state.panelType?"graph-bar":"chart-line",onClick:()=>{const e=c.jh.getAncestor(t,c.xK),n=c.jh.getAncestor(t,c.Eb).clone(),r=c.jh.getData(t).clone(),a=t.clone(),i=Array.isArray(n.state.headerActions)?n.state.headerActions.map(e=>e.clone()):n.state.headerActions;let s;s="histogram"!==t.state.panelType?c.d0.timeseries().setOverrides(g.jC):c.d0.histogram(),e.setState({body:s.setMenu(a).setTitle(n.state.title).setHeaderActions(i).setData(r).build()});const o="timeseries"!==t.state.panelType?"timeseries":"histogram";(0,v.IW)("panelType",o),a.setState({panelType:o});const l=(0,f.UX)(e,e=>e instanceof P.E,P.E);l&&l.rebuildAvgFields(),K(o)},text:"histogram"!==t.state.panelType?"Histogram":"Time series"})}(d,t),t.setState({body:new c.Lw({items:d})}),t._subs.add((0,o.isAssistantAvailable)().subscribe(e=>N(function*(){if(e){const e=yield(0,l.getDataSourceSrv)().get((0,f.U4)(t));t.addItem({text:"",type:"divider"}),t.addItem({text:"AI",type:"group"}),t.addItem({iconClassName:"ai-sparkle",text:"Explain in Assistant",onClick:()=>{(0,o.openAssistant)({prompt:"Help me understand this query and provide a summary of the data. Be concise and to the point.",context:[(0,o.createContext)(o.ItemDataType.Datasource,{datasourceName:e.name,datasourceUid:e.uid,datasourceType:e.type}),(0,o.createContext)(o.ItemDataType.Structured,{title:"Logs Drilldown Query",data:{query:G(t)}})]})}})}})())),t._subs.add(null===(u=t.state.investigationsButton)||void 0===u?void 0:u.subscribeToState(()=>N(function*(){var e;yield(e=t,N(function*(){const t=e.state.investigationsButton;if(t){var n;const l=yield Q(t);var r;const c=null!==(r=null===(n=e.state.body)||void 0===n?void 0:n.state.items)&&void 0!==r?r:[],u=c.find(e=>e.text===B);var a,i,s,o;l&&(u?u&&(null===(a=e.state.body)||void 0===a||a.setItems(c.filter(e=>!1===[M,I,B].includes(e.text)))):(null===(i=e.state.body)||void 0===i||i.addItem({text:M,type:"divider"}),null===(s=e.state.body)||void 0===s||s.addItem({text:I,type:"group"}),null===(o=e.state.body)||void 0===o||o.addItem({iconClassName:"plus-square",onClick:e=>l.onClick&&l.onClick(e),text:B})))}})())})()))})}}$(z,"Component",({model:e})=>{const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):a().createElement(a().Fragment,null)});const G=e=>{const t=c.jh.getData(e);let n=t instanceof c.dt?t:(0,f.oh)(t)[0];if(!n){const t=c.jh.findObject(e,e=>e instanceof j.u||e instanceof C.u);if(t){const e=c.jh.getData(t);n=e instanceof c.dt?e:(0,f.oh)(e)[0]}else p.v.error(new Error("Unable to locate query runner!"),{msg:"PanelMenu - getExploreLink: Unable to locate query runner!"})}const r=n.state.queries[0].expr;return(0,h.Sh)(e,r)},U=e=>{const t=c.jh.getAncestor(e,b.P),n=G(e);return T(t,n)},W=()=>{(0,u.EE)(u.NO.all,u.ir.all.open_in_explore_menu_clicked)},K=e=>{(0,u.EE)(u.NO.service_details,u.ir.service_details.change_viz_type,{newVizType:e})},Q=e=>N(function*(){const t=d.R6.MetricInvestigation,n=e.state.context;if(void 0!==l.getPluginLinkExtensions){return(0,l.getPluginLinkExtensions)({context:n,extensionPointId:t}).extensions[0]}if(void 0!==l.getObservablePluginLinks){return(yield(0,s.firstValueFrom)((0,l.getObservablePluginLinks)({context:n,extensionPointId:t})))[0]}})();const H=e=>({errorWrapper:(0,i.css)({}),panelWrapper:(0,i.css)({display:"flex",flexDirection:"column",height:"100%",label:"panel-wrapper",position:"absolute",width:"100%"})})},1220:(e,t,n)=>{n.d(t,{g:()=>T});var r=n(5959),a=n.n(r),i=n(6089),s=n(7781),o=n(6865),l=n(2007),c=n(4509),u=n(7839),d=n(8428),p=n(7478),g=n(7985),h=n(9683),f=n(8714),v=n(8531),b=n(5002);function m(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function y(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){m(i,r,a,s,o,"next",e)}function o(e){m(i,r,a,s,o,"throw",e)}s(void 0)})}}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class w extends o.Bs{setIsOpen(e){this.setState({isOpen:e})}onCopyLink(e,t,r){e?(x(r||n.g.location.href),(0,v.reportInteraction)("grafana_explore_shortened_link_clicked",{isAbsoluteTime:t})):((0,b.Dk)(void 0!==r?`${window.location.protocol}//${window.location.host}${v.config.appSubUrl}${r}`:n.g.location.href),this.state.onCopyLink&&this.state.onCopyLink(e,t,r))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){S(e,t,n[t])})}return e}({isOpen:!1,lastSelected:O},e))}}S(w,"MenuActions",({model:e})=>{const t=[{items:[{absTime:!1,getUrl:()=>{},icon:"link",key:"copy-shortened-link",label:"Copy shortened URL",shorten:!0},{absTime:!1,getUrl:()=>{},icon:"link",key:"copy-link",label:"Copy URL",shorten:!1}],key:"normal",label:"Normal URL links"},{items:[{absTime:!0,getUrl:()=>P(void 0!==e.state.getSceneTimeRange?e.state.getSceneTimeRange():o.jh.getTimeRange(e)),icon:"clock-nine",key:"copy-short-link-abs-time",label:"Copy absolute shortened URL",shorten:!0},{absTime:!0,getUrl:()=>P(void 0!==e.state.getSceneTimeRange?e.state.getSceneTimeRange():o.jh.getTimeRange(e)),icon:"clock-nine",key:"copy-link-abs-time",label:"Copy absolute URL",shorten:!1}],key:"timesync",label:"Time-sync URL links (share with time range intact)"}];return a().createElement(l.Menu,null,t.map(t=>a().createElement(l.MenuGroup,{key:t.key,label:t.label},t.items.map(t=>a().createElement(l.Menu.Item,{key:t.key,label:t.label,icon:t.icon,onClick:()=>{const n=t.getUrl();e.onCopyLink(t.shorten,t.absTime,n),e.setState({lastSelected:t})}})))))}),S(w,"Component",({model:e})=>{const{isOpen:t,lastSelected:n}=e.useState();return a().createElement(l.ButtonGroup,null,a().createElement(l.ToolbarButton,{tooltip:n.label,icon:n.icon,variant:"canvas",narrow:!0,onClick:()=>{const t=n.getUrl();e.onCopyLink(n.shorten,n.absTime,t)},"aria-label":"Copy shortened URL"},a().createElement("span",null,"Share")),a().createElement(l.Dropdown,{overlay:a().createElement(w.MenuActions,{model:e}),placement:"bottom-end",onVisibleChange:e.setIsOpen.bind(e)},a().createElement(l.ToolbarButton,{narrow:!0,variant:"canvas",isOpen:t,"aria-label":"Open copy link options"})))});const O={absTime:!1,getUrl:()=>{},icon:"share-alt",key:"copy-link",label:"Copy shortened URL",shorten:!0};function E(e){let t=e.replace(`${window.location.protocol}//${window.location.host}${v.config.appSubUrl}`,"");return t.startsWith("/")?t.substring(1,t.length):t}const x=e=>y(function*(){const t=(0,v.getAppEvents)(),n=yield function(e){return y(function*(){const t=(0,v.getAppEvents)();try{return(yield(0,v.getBackendSrv)().post("/api/short-urls",{path:E(e)})).url}catch(e){console.error("Error when creating shortened link: ",e),t.publish({payload:["Error generating shortened link"],type:s.AppEvents.alertError.name})}})()}(e);n?((0,b.Dk)(n),t.publish({payload:["Shortened link copied to clipboard"],type:s.AppEvents.alertSuccess.name})):t.publish({payload:["Error generating shortened link"],type:s.AppEvents.alertError.name})})(),P=e=>{const t=(0,s.toUtc)(e.state.value.from),n=(0,s.toUtc)(e.state.value.to),r=v.locationService.getLocation(),a=s.urlUtil.getUrlSearchParams();return a.from=t.toISOString(),a.to=n.toISOString(),s.urlUtil.renderUrl(r.pathname,a)};var j,C,L,_=n(4452),F=n(9721);function k(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}class T extends o.Bs{onActivate(){const e=o.jh.getAncestor(this,f.P).state.ds;void 0!==(null==e?void 0:e.maxLines)&&this.setState({maxLines:e.maxLines}),this.state.shareButtonScene||this.setState({shareButtonScene:new w({})})}getPageSlug(){const e=(0,h.FT)();if(e!==u.G3.embed)return e;const t=o.jh.getAncestor(this,F.Mn),n=(0,d.mx)(t.state.pageSlug);return n||void 0}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}function D(e){return e===u._J.field?u.G3.fields:e===u._J.label?u.G3.labels:e}L=({model:e})=>{const t=(0,l.useStyles2)($);let n,r=!1;const d=o.jh.getAncestor(e,F.Mn);if(d.state.embedded&&d.state.pageSlug)n=D(d.state.pageSlug);else if(n=e.getPageSlug(),!n||!Object.values(u.G3).includes(n)){const e=(0,h.er)();r=!0,e&&(n=D(e))}const f=d.useState(),{$data:v,loading:b,logsCount:m,totalLogsCount:y}=f,S=k(f,["$data","loading","logsCount","totalLogsCount"]),{maxLines:w}=e.useState(),O=S.loadingStates;return a().createElement(l.Box,{paddingY:0},a().createElement("div",{className:t.actions},a().createElement(l.Stack,{gap:1},e.state.shareButtonScene&&a().createElement(e.state.shareButtonScene.Component,{model:e.state.shareButtonScene}))),a().createElement(l.TabsBar,null,_._.filter(e=>!(e.value===u.G3.patterns&&void 0===d.state.$patternsData)).map((e,t)=>a().createElement(l.Tab,{"data-testid":e.testId,key:t,label:e.displayName,active:n===e.value,counter:O[e.displayName]?void 0:N(e,S),suffix:e.displayName===u.ob.logs?({className:e})=>function(e,t,n,r){const o=(0,l.useStyles2)(A),c=(0,s.getValueFormat)("short");if(void 0===t&&void 0!==n&&n<r){var u;const t=c(n,0);return a().createElement("span",{className:(0,i.cx)(e,o.logsCountStyles)},t.text,null===(u=t.suffix)||void 0===u?void 0:u.trim())}if(void 0!==t){var d;const n=c(t,0);return a().createElement("span",{className:(0,i.cx)(e,o.logsCountStyles)},n.text,null===(d=n.suffix)||void 0===d?void 0:d.trim())}return a().createElement("span",{className:(0,i.cx)(e,o.emptyCountStyles)})}(e,y,m,null!=w?w:g.by):void 0,icon:O[e.displayName]?"spinner":void 0,href:(0,p.rs)(e.value,d),onChangeTab:()=>{(e.value&&e.value!==n||r)&&(0,c.EE)(c.NO.service_details,c.ir.service_details.action_view_changed,{newActionView:e.value,previousActionView:n})}}))))},(C="Component")in(j=T)?Object.defineProperty(j,C,{value:L,enumerable:!0,configurable:!0,writable:!0}):j[C]=L;const N=(e,t)=>{switch(e.value){case"fields":return t.fieldsCount;case"patterns":return t.patternsCount;case"labels":return t.labelsCount;default:return}};function $(e){return{actions:(0,i.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,zIndex:2},display:"flex",justifyContent:"flex-end"})}}function A(e){return{emptyCountStyles:(0,i.css)({display:"inline-block",fontSize:e.typography.bodySmall.fontSize,marginLeft:e.spacing(1),minWidth:"1em",padding:e.spacing(.25,1)}),logsCountStyles:(0,i.css)({backgroundColor:e.colors.action.hover,borderRadius:e.spacing(3),color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,fontWeight:e.typography.fontWeightMedium,label:"counter",marginLeft:e.spacing(1),padding:e.spacing(.25,1)})}}},4452:(e,t,n)=>{n.d(t,{_:()=>Oe,n:()=>Ee});var r=n(6865),a=n(6145),i=n(7839),s=n(3571),o=n(6830),l=n(8072),c=n(5959),u=n.n(c),d=n(6089),p=n(7781),g=n(2007),h=n(1532),f=n(8714),v=n(9721),b=n(8531),m=n(5953),y=n(708),S=n(5553),w=n(7478),O=n(4509);function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e){var t,n;const{indexScene:a,pattern:i,type:s}=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){E(e,t,n[t])})}return e}({},e),o=r.jh.getAncestor(a,f.P);if(!o)return void m.v.warn("logs exploration scene not found");(0,w.bN)();const{patterns:l=[]}=o.state,c=l.filter(e=>e.pattern!==i);var u;const d=null!==(u=null===(t=c.filter(e=>"include"===e.type))||void 0===t?void 0:t.length)&&void 0!==u?u:0;var p;const g=null!==(p=null===(n=c.filter(e=>"exclude"===e.type))||void 0===n?void 0:n.length)&&void 0!==p?p:0;(0,O.EE)(O.NO.service_details,O.ir.service_details.pattern_selected,{excludePatternsLength:g+("exclude"===s?1:0),includePatternsLength:d+("include"===s?1:0),type:s}),"undo"===s?o.setState({patterns:c}):o.setState({patterns:[...c,{pattern:i,type:s}]})}var P=n(7985),j=n(20),C=n(7709),L=n(9405);const _=e=>({logsStatsRow:(0,d.css)({margin:`${e.spacing(1.15)}px 0`}),logsStatsRowActive:(0,d.css)({color:e.colors.primary.text,position:"relative"}),logsStatsRowBar:(0,d.css)({background:e.colors.text.disabled,height:e.spacing(.5),overflow:"hidden"}),logsStatsRowCount:(0,d.css)({marginLeft:e.spacing(.75),textAlign:"right"}),logsStatsRowInnerBar:(0,d.css)({background:e.colors.primary.main,height:e.spacing(.5),overflow:"hidden"}),logsStatsRowLabel:(0,d.css)({display:"flex",marginBottom:"1px"}),logsStatsRowPercent:(0,d.css)({marginLeft:e.spacing(.75),textAlign:"right",width:e.spacing(4.5)}),logsStatsRowValue:(0,d.css)({flex:1,overflow:"hidden",textOverflow:"ellipsis"})}),F=({active:e,count:t,proportion:n,value:r})=>{const a=(0,g.useStyles2)(_),i=`${Math.round(100*n)}%`,s={width:i};return u().createElement("div",{className:e?`${a.logsStatsRow} ${a.logsStatsRowActive}`:a.logsStatsRow},u().createElement("div",{className:a.logsStatsRowLabel},u().createElement("div",{className:a.logsStatsRowValue,title:r},r),u().createElement("div",{className:a.logsStatsRowCount},t),u().createElement("div",{className:a.logsStatsRowPercent},i)),u().createElement("div",{className:a.logsStatsRowBar},u().createElement("div",{className:a.logsStatsRowInnerBar,style:s})))};function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const D=e=>({logsStats:(0,d.css)({background:"inherit",color:e.colors.text.primary,marginTop:e.spacing(1),maxHeight:"40vh",overflowY:"auto",width:"fit-content",wordBreak:"break-all"}),logsStatsBody:(0,d.css)({padding:"5px 0px"}),logsStatsClose:(0,d.css)({cursor:"pointer"}),logsStatsHeader:(0,d.css)({borderBottom:`1px solid ${e.colors.border.medium}`,display:"flex"}),logsStatsTitle:(0,d.css)({display:"inline-block",flexGrow:1,fontWeight:e.typography.fontWeightMedium,paddingRight:e.spacing(2),textOverflow:"ellipsis",whiteSpace:"nowrap"})}),N=e=>{const t=(0,g.useStyles2)(D),{stats:n,value:r}=e,a=n.slice(0,10);let i=a.find(e=>e.value===r),s=n.slice(10);!i&&(i=s.find(e=>e.value===r),s=s.filter(e=>e.value!==r));const o=s.reduce((e,t)=>e+t.count,0),l=a.reduce((e,t)=>e+t.count,0)+o;let c=[...a];return o>0&&c.push({count:o,proportion:o/l,value:"Other"}),c.sort((e,t)=>t.count-e.count),u().createElement("div",{className:t.logsStats},u().createElement("div",{className:t.logsStatsHeader},u().createElement("div",{className:t.logsStatsTitle},"From a sample of ",l," rows found")),u().createElement("div",{className:t.logsStatsBody},c.map(e=>u().createElement(F,T(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){k(e,t,n[t])})}return e}({key:e.value},e),{active:e.value===r})))))};var $=n(5719);function A(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}const B=({exploration:e,maxLines:t,pattern:n})=>{const a=function(e){const t=[];let n=e.indexOf("<_>");for(;-1!==n;)t.push(n),n=e.indexOf("<_>",n+1);return t}(n),[i,s]=(0,c.useState)(void 0),[o,l]=(0,c.useState)(!1),d=(0,g.useStyles2)(M),h=(0,c.useRef)(null),f=(0,c.useRef)(null),v=()=>{return(o=function*(){(0,O.EE)(O.NO.service_details,O.ir.service_details.pattern_field_clicked);const o=function(e,t,n){let r=1;const a=e.replace(/<_>/g,()=>`<field_${r++}>`),i=n.state.filterExpression,s=t.map((e,t)=>`field_${t+1}`).join(" ,");return`{${i}} |> \`${e}\` | pattern \`${a}\` | keep ${s} | line_format ""`}(n,a,(0,S.cR)(e)),c=yield(0,$.hJ)(e),u=r.jh.getTimeRange(e).state.value;i&&o===h.current&&u===f.current||(h.current=o,f.current=u,null==c||c.query({app:"",interval:"",intervalMs:0,range:u,requestId:"1",scopedVars:{},startTime:0,targets:[(0,P.l)(o,{maxLines:t})],timezone:""}).forEach(e=>{var n,r;e.state!==p.LoadingState.Done||(null===(n=e.errors)||void 0===n?void 0:n.length)?(e.state===p.LoadingState.Error||(null===(r=e.errors)||void 0===r?void 0:r.length))&&(s(void 0),l(!0)):(s(function(e,t,n){const r=new Map;e.data[0].fields[0].values.toArray().forEach(e=>{Object.keys(e).forEach(t=>{var n,a;r.has(t)||r.set(t,new Map),null===(a=r.get(t))||void 0===a||a.set(e[t],((null===(n=r.get(t))||void 0===n?void 0:n.get(e[t]))||0)+1)})});const a=[];for(let e=0;e<=t;e++){var i;const t=[];null===(i=r.get(`field_${e+1}`))||void 0===i||i.forEach((e,r)=>{t.push({count:e,proportion:e/n,value:r})}),t.sort((e,t)=>t.count-e.count),a.push(t)}return a}(e,a.length,t)),l(!1))}))},function(){var e=this,t=arguments;return new Promise(function(n,r){var a=o.apply(e,t);function i(e){A(a,n,r,i,s,"next",e)}function s(e){A(a,n,r,i,s,"throw",e)}i(void 0)})})();var o},b=(0,c.useMemo)(()=>n.split("<_>"),[n]);return u().createElement("div",null,b.map((e,t)=>u().createElement("span",{key:t},e,t!==a.length&&u().createElement(g.Toggletip,{onOpen:v,content:u().createElement(u().Fragment,null,i&&i[t].length>0&&u().createElement(N,{stats:i[t],value:""}),i&&0===i[t].length&&u().createElement("div",null,"No available stats for this field in the current timestamp."),!i&&o&&u().createElement("div",null,"Could not load stats for this pattern."),!i&&!o&&u().createElement("div",{style:{padding:"10px"}},u().createElement(g.Spinner,{size:"xl"})))},u().createElement("span",{className:d.pattern},"<_>")))))};function M(e){return{pattern:(0,d.css)({"&:hover":{backgroundColor:e.colors.emphasize(e.colors.background.primary,.2)},backgroundColor:e.colors.emphasize(e.colors.background.primary,.1),cursor:"pointer",margin:"0 2px"})}}var I=n(4907),R=n(8848),V=n(4351);function z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class G extends r.Bs{onActivate(){if(this.state.body)return;const e=(0,P.l)(j.SA);this.replacePatternsInQuery(e);const t=(0,I.rS)([e]);t.getResultsStream().subscribe(e=>{this.onQueryWithFiltersResult(e)}),this.setState({body:new r.G1({children:[new r.vA({body:void 0,height:0,width:"100%"}),new r.vA({body:r.d0.logs().setHoverHeader(!0).setOption("showLogContextToggle",!0).setOption("showTime",!0).setOption("noInteractions",!0).setOption("controlsStorageKey",V.Bz).setData(t).build(),height:300,width:"100%"})],direction:"column"})})}replacePatternsInQuery(e){const t={pattern:this.state.pattern,type:"include"},n=(0,R.M)([t]);e.expr=e.expr.replace(j.sC,n)}removePatternFromFilterExclusion(){const e=r.jh.getAncestor(this,K);var t;const n=null!==(t=e.state.patternsNotMatchingFilters)&&void 0!==t?t:[],a=n.findIndex(e=>e===this.state.pattern);-1!==a&&(n.splice(a,1),e.setState({patternsNotMatchingFilters:n}))}setWarningMessage(e){const t=this.getNoticeFlexItem(),n=this.getVizFlexItem();return t instanceof r.vA&&t.setState({body:new r.dM({reactNode:e}),height:"auto",isHidden:!1}),n}getNoticeFlexItem(){const e=this.getFlexItemChildren();return null==e?void 0:e[0]}getVizFlexItem(){const e=this.getFlexItemChildren();return null==e?void 0:e[1]}getFlexItemChildren(){var e;return null===(e=this.state.body)||void 0===e?void 0:e.state.children}excludeThisPatternFromFiltering(){const e=r.jh.getAncestor(this,K);var t;const n=null!==(t=e.state.patternsNotMatchingFilters)&&void 0!==t?t:[];e.setState({patternsNotMatchingFilters:[...n,this.state.pattern]})}static Component({model:e}){const{body:t}=e.useState();return t?u().createElement(t.Component,{model:t}):null}constructor(e){super(e),z(this,"clearFilters",()=>{const e=(0,S.ir)(this),t=(0,S.Gk)(this),n=(0,S.iw)(this);if(e.setState({filters:[]}),n.setState({filters:[]}),t.state.filters.length){t.setState({filters:[]});const e=this.getNoticeFlexItem();null==e||e.setState({isHidden:!0}),this.removePatternFromFilterExclusion()}}),z(this,"onQueryError",e=>{if(e.data.state===p.LoadingState.Done&&(0===e.data.series.length||e.data.series.every(e=>0===e.length))||e.data.state===p.LoadingState.Error){let t;try{t={msg:"onQueryError",pattern:this.state.pattern,request:JSON.stringify(e.data.request),traceIds:JSON.stringify(e.data.traceIds)}}catch(e){t={msg:"Failed to encode context",pattern:this.state.pattern}}m.v.error(new Error("Pattern sample query returns no results"),t),this.setWarningMessage(u().createElement(g.Alert,{severity:"error",title:""},"This pattern returns no logs."));const n=this.getVizFlexItem();n instanceof r.vA&&n.setState({isHidden:!0})}}),z(this,"onQueryWithFiltersResult",e=>{const t=(0,P.l)(j.pT);this.replacePatternsInQuery(t);const n=(0,I.rS)([t]);if(n.getResultsStream().subscribe(this.onQueryError),e.data.state===p.LoadingState.Done&&(0===e.data.series.length||e.data.series.every(e=>0===e.length))){const e=this.getNoticeFlexItem(),t=this.getVizFlexItem();if(e instanceof r.vA&&e.setState({body:new r.dM({reactNode:u().createElement(g.Alert,{severity:"warning",title:""},"The logs returned by this pattern do not match the current query filters.",u().createElement(g.Button,{className:o.ZI.button,onClick:()=>this.clearFilters()},"Clear filters"))}),height:"auto",isHidden:!1}),t instanceof r.vA){const e=t.state.body;e instanceof r.Eb&&(null==e||e.setState({$data:n}))}this.excludeThisPatternFromFiltering()}e.data.state===p.LoadingState.Error&&this.onQueryError(e)}),this.addActivationHandler(this.onActivate.bind(this))}}function U({row:e,tableViz:t}){const{expandedRows:n}=t.useState(),r=null==n?void 0:n.find(t=>t.state.key===e.pattern);return(0,c.useEffect)(()=>{if(!r){const r=(a=e.pattern,new G({key:a,pattern:a}));var n;t.setState({expandedRows:[...null!==(n=t.state.expandedRows)&&void 0!==n?n:[],r]})}var a},[e,t,r]),r?u().createElement(r.Component,{model:r}):null}const W=[""," K"," Mil"," Bil"," Tri"," Quadr"," Quint"," Sext"," Sept"];class K extends r.Bs{onActivate(){var e;const t=null===(e=r.jh.getAncestor(this,f.P).state.ds)||void 0===e?void 0:e.maxLines;this.setState({maxLines:t})}buildColumns(e,t,n,a,i,s,o){const l=Z(n),c=r.jh.getTimeRange(this).state.value,h=[{cell:e=>{const t={series:[e.cell.row.original.dataFrame],state:p.LoadingState.Done,timeRange:c},n=new r.Zv({data:t}),a=r.d0.timeseries().setData(n).setHoverHeader(!0).setOption("tooltip",{mode:g.TooltipDisplayMode.None}).setCustomFieldConfig("hideFrom",{legend:!0,tooltip:!0}).setCustomFieldConfig("axisPlacement",g.AxisPlacement.Hidden).setDisplayMode("transparent").build();return u().createElement("div",{className:l.tableTimeSeriesWrap},u().createElement("div",{className:l.tableTimeSeries},u().createElement(a.Component,{model:a})))},header:"",id:"volume-samples"},{cell:e=>{const t=(0,p.scaledUnits)(1e3,W)(e.cell.row.original.sum);var n,r;return u().createElement("div",{className:l.countTextWrap},u().createElement("div",null,null!==(n=t.prefix)&&void 0!==n?n:"",t.text,null!==(r=t.suffix)&&void 0!==r?r:""))},header:"Count",id:"count",sortType:"number"},{cell:t=>u().createElement("div",{className:l.countTextWrap},u().createElement("div",null,(100*t.cell.row.original.sum/e).toFixed(0),"%")),header:"%",id:"percent",sortType:"number"},{cell:e=>u().createElement("div",{className:(0,d.cx)(Y(),l.tablePatternTextDefault)},u().createElement(B,{exploration:(0,$.Ti)(this),pattern:e.cell.row.original.pattern,maxLines:a})),header:"Pattern",id:"pattern"},{cell:e=>{if(null==s?void 0:s.includes(e.cell.row.original.pattern))return;const n=null==t?void 0:t.find(t=>t.pattern===e.cell.row.original.pattern),r="include"===(null==n?void 0:n.type),a="exclude"===(null==n?void 0:n.type);return u().createElement(C.F,{isExcluded:a,isIncluded:r,onInclude:()=>e.cell.row.original.includeLink(),onExclude:()=>e.cell.row.original.excludeLink(),onClear:()=>e.cell.row.original.undoLink(),buttonFill:"outline"})},disableGrow:!0,header:void 0,id:"include"}];return i.some(e=>e.levels.length>0)&&h.splice(1,0,{header:"Levels",id:"levels",cell:e=>(e.cell.row.original.levels.sort(),e.cell.row.original.levels.map(t=>u().createElement(g.Button,{key:t,size:"sm",variant:o.some(e=>(0,y.BG)(e.operator)&&e.value===t)?"primary":"secondary",fill:"outline",className:l.levelWrap,onClick:()=>{e.cell.row.original.togglePatternLevel(t)}},t)))}),h}buildTableData(e,t){const n=r.jh.getAncestor(this,f.P);return e.filter(e=>!t.size||t.has(e.pattern)).map(e=>({dataFrame:e.dataFrame,excludeLink:()=>x({indexScene:n,pattern:e.pattern,type:"exclude"}),includeLink:()=>x({indexScene:n,pattern:e.pattern,type:"include"}),togglePatternLevel:e=>{(0,L.Qt)(j.e4,e,"toggle",n,j._Y)},pattern:e.pattern,sum:e.sum,levels:e.levels,undoLink:()=>x({indexScene:n,pattern:e.pattern,type:"undo"})}))}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}var Q,H,q;q=function({model:e}){const t=r.jh.getAncestor(e,f.P),{patterns:n}=t.useState(),a=(0,g.useTheme2)(),i=X(a),o=r.jh.getAncestor(e,ie),{legendSyncPatterns:l}=o.useState(),{patternFrames:c,patternsNotMatchingFilters:d}=e.useState();let p=null!=c?c:[];const h=(0,S.iw)(e),{filters:v}=h.useState(),b=r.jh.getAncestor(e,me);var m;const y=(null!==(m=b.state.patternFrames)&&void 0!==m?m:[]).reduce((e,t)=>e+t.sum,0),w=e.buildTableData(p,l);var O;const E=e.buildColumns(y,n,a,null!==(O=e.state.maxLines)&&void 0!==O?O:P.by,p,d,v);return u().createElement("div",{"data-testid":s.b.patterns.tableWrapper,className:i.tableWrap},u().createElement(g.InteractiveTable,{columns:E,data:w,getRowId:e=>e.pattern,renderExpandedRow:t=>u().createElement(U,{tableViz:e,row:t})}))},(H="Component")in(Q=K)?Object.defineProperty(Q,H,{value:q,enumerable:!0,configurable:!0,writable:!0}):Q[H]=q;const J=b.config.theme2,Y=()=>(0,d.css)({fontFamily:J.typography.fontFamilyMonospace,minWidth:"200px",overflow:"hidden",overflowWrap:"break-word"}),X=e=>({link:(0,d.css)({textDecoration:"underline"}),tableWrap:(0,d.css)({"> div":{height:"calc(100vh - 450px)",minHeight:"470px"},th:{backgroundColor:e.colors.background.canvas,position:"sticky",top:0,zIndex:e.zIndex.navbarFixed}})}),Z=e=>({levelWrap:(0,d.css)({fontSize:e.typography.bodySmall.fontSize,fontFamily:e.typography.fontFamilyMonospace,"&:not(:last-child)":{marginRight:e.spacing(.5)}}),countTextWrap:(0,d.css)({fontSize:e.typography.bodySmall.fontSize}),tablePatternTextDefault:(0,d.css)({fontFamily:e.typography.fontFamilyMonospace,fontSize:e.typography.bodySmall.fontSize,maxWidth:"100%",minWidth:"200px",overflow:"hidden",overflowWrap:"break-word",wordBreak:"break-word"}),tableTimeSeries:(0,d.css)({height:"30px",overflow:"hidden"}),tableTimeSeriesWrap:(0,d.css)({pointerEvents:"none",width:"230px"})});function ee(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function te(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){ee(i,r,a,s,o,"next",e)}function o(e){ee(i,r,a,s,o,"throw",e)}s(void 0)})}}function ne(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function re(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const ae=b.config.theme2.visualization.palette;class ie extends r.Bs{onActivate(){this.updateBody();const e=r.jh.getAncestor(this,me);this.updatePatterns(e.state.patternFrames),this._subs.add(r.jh.getAncestor(this,v.Mn).subscribeToState((e,t)=>{var n,a,i,s,o,l;const c=null==e||null===(i=e.$patternsData)||void 0===i||null===(a=i.state)||void 0===a||null===(n=a.data)||void 0===n?void 0:n.series,u=null==t||null===(l=t.$patternsData)||void 0===l||null===(o=l.state)||void 0===o||null===(s=o.data)||void 0===s?void 0:s.series;if(!(0,h.B)(c,u)){const e=r.jh.getAncestor(this,me);this.updatePatterns(e.state.patternFrames),e.setState({filteredPatterns:void 0})}})),this._subs.add(r.jh.getAncestor(this,me).subscribeToState((e,t)=>{const n=r.jh.getAncestor(this,me);e.filteredPatterns&&!(0,h.B)(e.filteredPatterns,t.filteredPatterns)?this.updatePatterns(n.state.filteredPatterns):n.state.patternFilter||this.updatePatterns(n.state.patternFrames)})),this._subs.add((0,S.iw)(this).subscribeToState((e,t)=>{if(!(0,h.B)(e.filters,t.filters)){const e=r.jh.getAncestor(this,me);this.updatePatterns(e.state.patternFrames)}}))}updatePatterns(){return te(function*(e=[]){var t;e=this.filterPatternFramesByLevel(e),null===(t=this.state.body)||void 0===t||t.forEachChild(t=>{t instanceof r.Eb&&t.setState({$data:this.getTimeseriesDataNode(e)}),t instanceof K&&t.setState({patternFrames:e})})}).apply(this,arguments)}updateBody(){return te(function*(){var e,t;const n=r.jh.getAncestor(this,me).state.patternFrames;(null===(t=r.jh.getAncestor(this,v.Mn).state.$patternsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.series)&&n?this.setState({body:this.getSingleViewLayout(),legendSyncPatterns:new Set,loading:!1}):m.v.warn("Failed to update PatternsFrameScene body")}).call(this)}extendTimeSeriesLegendBus(e,t){const n=t.onToggleSeriesVisibility;t.onToggleSeriesVisibility=(t,r)=>{var a;null==n||n(t,r);const i=null===(a=e.state.fieldConfig.overrides)||void 0===a?void 0:a[0],s=null==i?void 0:i.matcher.options.names,o=new Set;s&&s.forEach(o.add,o),this.setState({legendSyncPatterns:o})}}getSingleViewLayout(){const e=r.jh.getAncestor(this,me).state.patternFrames;if(!e)return void m.v.warn("Failed to set getSingleViewLayout");const t=this.getTimeSeries(e);return new r.gF({autoRows:"200px",children:[t,new K({patternFrames:e})],isLazy:!0,templateColumns:"100%"})}getTimeSeries(e){const t=r.jh.getAncestor(this,f.P),n=r.d0.timeseries().setData(this.getTimeseriesDataNode(e)).setOption("legend",{asTable:!0,displayMode:g.LegendDisplayMode.Table,placement:"right",showLegend:!0,width:200}).setHoverHeader(!0).setUnit("short").setLinks([{onClick:e=>{x({indexScene:t,pattern:e.origin.labels.name,type:"include"})},targetBlank:!1,title:"Include",url:"#"},{onClick:e=>{x({indexScene:t,pattern:e.origin.labels.name,type:"exclude"})},targetBlank:!1,title:"Exclude",url:"#"}]).build();return n.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(e,t)}),n}getTimeseriesDataNode(e){const t=r.jh.getTimeRange(this).state.value;return new r.Zv({data:{series:e.map((e,t)=>{const n=e.dataFrame;return n.fields[1].config.color=function(e){return{fixedColor:ae[e],mode:"fixed"}}(t),n.fields[1].name="",n}),state:p.LoadingState.Done,timeRange:t}})}constructor(e){super(re(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ne(e,t,n[t])})}return e}({loading:!0},e),{legendSyncPatterns:new Set})),ne(this,"filterPatternFramesByLevel",e=>{const t=(0,S.iw)(this).state.filters;if(t.length&&e.some(e=>e.levels.length>0)){const n=new Set;t.forEach(e=>{(0,y.BG)(e.operator)&&n.add(e.value)}),e=e.filter(e=>e.levels.some(e=>n.has(e)))}return e}),this.addActivationHandler(this.onActivate.bind(this))}}ne(ie,"Component",({model:e})=>{var t;const{body:n,loading:a}=e.useState(),i=r.jh.getAncestor(e,v.Mn),{$patternsData:s}=i.useState(),o=null==s||null===(t=s.state.data)||void 0===t?void 0:t.series;return u().createElement("div",{className:se.container},!a&&o&&o.length>0&&u().createElement(u().Fragment,null,n&&u().createElement(n.Component,{model:n})))});const se={container:(0,d.css)({".show-on-hover":{display:"none"},width:"100%"})};var oe=n(7191);function le(){return u().createElement(oe.R,null,u().createElement("div",null,u().createElement("p",null,u().createElement("strong",null,"Sorry, we could not detect any patterns.")),u().createElement("p",null,"Check back later or reach out to the team in the"," ",u().createElement(g.TextLink,{href:"https://slack.grafana.com/",external:!0},"Grafana Labs community Slack channel")),u().createElement("p",null,"Patterns let you detect similar log lines to include or exclude from your search.")))}function ce(){return u().createElement(oe.R,null,u().createElement("div",null,u().createElement("p",null,u().createElement("strong",null,"Patterns are only available for the most recent ",be," hours of data.")),u().createElement("p",null,"See the"," ",u().createElement(g.TextLink,{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/logs/patterns/",external:!0},"patterns docs")," ","for more info.")))}var ue=n(9193),de=n(9284);function pe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ge extends r.Bs{onActivate(){const e=r.jh.getAncestor(this,me);this._subs.add(e.subscribeToState((e,t)=>{if(e.patternFilter!==t.patternFilter){const e=r.jh.getAncestor(this,me);e.state.patternFrames&&(0,ue.E)(e.state.patternFrames.map(e=>e.pattern),e.state.patternFilter,this.onSearchResult)}})),this._subs.add(e.subscribeToState((e,t)=>{e.patternFilter&&!e.filteredPatterns&&e.patternFrames&&!(0,h.B)(e.filteredPatterns,t.filteredPatterns)&&(0,ue.X)(e.patternFrames.map(e=>e.pattern),e.patternFilter,this.onSearchResult)}))}setFilteredPatterns(e,t){const n=r.jh.getAncestor(this,me),a=null!=t?t:n.state.patternFrames;if(a){const t=a.filter(t=>!(!n.state.patternFilter||!(null==a?void 0:a.length))&&e.find(e=>e===t.pattern));n.setState({filteredPatterns:t})}}setEmptySearch(){r.jh.getAncestor(this,me).setState({filteredPatterns:void 0})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){pe(e,t,n[t])})}return e}({},e)),pe(this,"clearSearch",()=>{r.jh.getAncestor(this,me).setState({patternFilter:""})}),pe(this,"handleSearchChange",e=>{r.jh.getAncestor(this,me).setState({patternFilter:e.target.value})}),pe(this,"onSearchResult",e=>{const t=r.jh.getAncestor(this,me);t.state.patternFilter?this.setFilteredPatterns(e[0]):t.state.filteredPatterns&&!t.state.patternFilter&&this.setEmptySearch()}),this.addActivationHandler(this.onActivate.bind(this))}}pe(ge,"Component",function({model:e}){const t=r.jh.getAncestor(e,me),{patternFilter:n}=t.useState();return u().createElement(g.Field,{className:he.field},u().createElement(de.D,{onChange:e.handleSearchChange,onClear:e.clearSearch,value:n,placeholder:"Search patterns"}))});const he={field:(0,d.css)({label:"field",marginBottom:0}),icon:(0,d.css)({cursor:"pointer"})};var fe=n(5659);function ve(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const be=3;class me extends r.Bs{onActivate(){var e,t;const n=r.jh.getAncestor(this,v.Mn);var a;(this.setBody(),null===(e=n.state.$patternsData)||void 0===e?void 0:e.state)&&this.onDataChange(null===(a=n.state.$patternsData)||void 0===a?void 0:a.state);this._subs.add(null===(t=n.state.$patternsData)||void 0===t?void 0:t.subscribeToState(this.onDataChange))}setBody(){this.setState({body:new r.G1({children:[new r.vA({body:new ge,ySizing:"content"}),new r.vA({body:new ie})],direction:"column"})})}updatePatternFrames(e){if(!e)return;const t=this.dataFrameToPatternFrame(e);this.setState({patternFrames:t})}dataFrameToPatternFrame(e){const t=r.jh.getAncestor(this,v.Mn),n=r.jh.getAncestor(t,f.P).state.patterns;return e.map(e=>{var t,r,a,i;const s=null==n?void 0:n.find(t=>t.pattern===e.name),o=null===(r=e.meta)||void 0===r||null===(t=r.custom)||void 0===t?void 0:t.sum,l=null===(i=e.meta)||void 0===i||null===(a=i.custom)||void 0===a?void 0:a.level;var c;return{dataFrame:e,pattern:null!==(c=e.name)&&void 0!==c?c:"",status:null==s?void 0:s.type,sum:o,levels:l}})}constructor(e){var t;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ve(e,t,n[t])})}return e}({$variables:null!==(t=e.$variables)&&void 0!==t?t:new r.Pj({variables:[new r.yP({defaultToAll:!0,includeAll:!0,name:j.Jg})]}),loading:!0,patternFilter:""},e)),ve(this,"onDataChange",(e,t)=>{var n,r,a,i,s;const o=null===(n=e.data)||void 0===n?void 0:n.series,l=null==t||null===(r=t.data)||void 0===r?void 0:r.series;(null===(a=e.data)||void 0===a?void 0:a.state)===p.LoadingState.Done?(this.setState({error:!1,loading:!1}),(0,h.B)(o,l)||this.updatePatternFrames(o)):(null===(i=e.data)||void 0===i?void 0:i.state)===p.LoadingState.Loading?this.setState({error:!1,loading:!0}):(null===(s=e.data)||void 0===s?void 0:s.state)===p.LoadingState.Error&&this.setState({error:!0,loading:!1})}),this.addActivationHandler(this.onActivate.bind(this))}}function ye(e){return{container:(0,d.css)({display:"flex",flexDirection:"column",flexGrow:1,minHeight:"100%"}),content:(0,d.css)({display:"flex",flexGrow:1,paddingTop:e.spacing(0)}),controls:(0,d.css)({alignItems:"top",display:"flex",flexGrow:0,gap:e.spacing(2)}),controlsLeft:(0,d.css)({display:"flex",flexDirection:"column",justifyContent:"flex-left",justifyItems:"left",width:"100%"}),controlsRight:(0,d.css)({display:"flex",flexGrow:0,justifyContent:"flex-end"}),patternMissingText:(0,d.css)({padding:e.spacing(2)})}}ve(me,"Component",({model:e})=>{const{blockingMessage:t,body:n,error:a,loading:i,patternFrames:s}=e.useState(),{value:o}=r.jh.getTimeRange(e).useState(),l=(0,g.useStyles2)(ye),c=(0,p.dateTime)().diff(o.to,"hours")>=be;return u().createElement("div",{className:l.container},u().createElement(fe.O,{blockingMessage:t,isLoading:i},!i&&a&&u().createElement("div",{className:l.patternMissingText},u().createElement(g.Text,{textAlignment:"center",color:"primary"},u().createElement("p",null,"There are no pattern matches."),u().createElement("p",null,"Pattern matching has not been configured."),u().createElement("p",null,"Patterns let you detect similar log lines and add or exclude them from your search."),u().createElement("p",null,"To see them in action, add the following to your Loki configuration"),u().createElement("p",null,u().createElement("code",null,"--pattern-ingester.enabled=true")))),!a&&!i&&0===(null==s?void 0:s.length)&&c&&u().createElement(ce,null),!a&&!i&&!s&&!c&&u().createElement(le,null),!a&&!i&&s&&s.length>0&&u().createElement("div",{className:l.content},n&&u().createElement(n.Component,{model:n}))))});var Se=n(5607),we=n(71);const Oe=[{displayName:i.ob.logs,getScene:()=>new r.G1({children:[new r.vA({body:new we._({})}),new r.vA({body:new Se.i({}),height:"calc(100vh - 500px)",minHeight:"470px"})],direction:"column"}),testId:s.b.exploreServiceDetails.tabLogs,value:i.G3.logs},{displayName:i.ob.labels,getScene:()=>new r.G1({$behaviors:[new r.Gg.K2({key:"sync",sync:a.yV.Crosshair})],children:[new r.vA({body:new l.O({})})]}),testId:s.b.exploreServiceDetails.tabLabels,value:i.G3.labels},{displayName:i.ob.fields,getScene:e=>{return t=e,new r.G1({$behaviors:[new r.Gg.K2({key:"sync",sync:a.yV.Crosshair})],children:[new r.vA({body:new o.J6({changeFieldCount:t})})]});var t},testId:s.b.exploreServiceDetails.tabFields,value:i.G3.fields},{displayName:i.ob.patterns,getScene:()=>new r.G1({children:[new r.vA({body:new me({})})]}),testId:s.b.exploreServiceDetails.tabPatterns,value:i.G3.patterns}],Ee=[{displayName:"Label",getScene:e=>function(e){return new r.G1({$behaviors:[new r.Gg.K2({key:"sync",sync:a.yV.Crosshair})],children:[new r.vA({body:new l.O({value:e})})]})}(e),testId:s.b.exploreServiceDetails.tabLabels,value:i._J.label},{displayName:"Field",getScene:e=>function(e){return new r.G1({$behaviors:[new r.Gg.K2({key:"sync",sync:a.yV.Crosshair})],children:[new r.vA({body:new o.J6({value:e})})]})}(e),testId:s.b.exploreServiceDetails.tabFields,value:i._J.field}]},9405:(e,t,n)=>{n.d(t,{Of:()=>S,Qt:()=>C,XI:()=>O,hi:()=>E,oR:()=>F,ts:()=>L,u7:()=>P,vn:()=>j});var r=n(5959),a=n.n(r),i=n(7781),s=n(6865),o=n(1532),l=n(696),c=n(376),u=n(8502),d=n(6854),p=n(5953),g=n(7478),h=n(5553),f=n(9721),v=n(7709),b=n(4509),m=n(20);function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class S extends i.BusEventBase{constructor(e,t,n,r){super(),y(this,"source",void 0),y(this,"operator",void 0),y(this,"key",void 0),y(this,"value",void 0),this.source=e,this.operator=t,this.key=n,this.value=r}}y(S,"type","add-filter");class w extends i.BusEventBase{constructor(e,t,n){super(),y(this,"key",void 0),y(this,"value",void 0),y(this,"operator",void 0),this.key=e,this.value=t,this.operator=n}}function O(e,t,n){const r="="===e.operator?"toggle":"exclude";C(e.key,e.value,r,t,n)}function E(e,t,n,r,a){const i=T(n,e,t);let s=i.state.filters.filter(t=>{const i=(0,h.z2)(n,t);return r&&a?!(t.key===e&&i.value===r&&t.operator===a):r?!(t.key===e&&i.value===r):a?!(t.key===e&&t.operator===a):!(t.key===e)});t.publishEvent(new w(e,r,a),!0),i.setState({filters:s})}y(w,"type","add-filter");const x=e=>e===d.w7.gt||e===d.w7.gte?"greater":e===d.w7.lt||e===d.w7.lte?"lesser":void 0;function P(e,t,n,r){r||(r=_(e,t));const a=T(r,e,t),i=n?x(n):void 0;let s=a.state.filters.filter(t=>!(t.key===e&&(x(t.operator)===i||t.operator===d.w7.NotEqual)));a.setState({filters:s})}function j(e,t,n,r,a){const i=x(n);a||(a=_(e,r));const s=T(a,e,r);let o;a===m.mB&&(o=JSON.stringify({parser:(0,c.Ri)(e,r),value:t}));let l=s.state.filters.filter(t=>!(t.key===e&&(x(t.operator)===i||t.operator===d.w7.NotEqual)));l=[...l,{key:e,operator:n,value:o||t,valueLabels:[t]}],s.setState({filters:l}),r.publishEvent(new S("filterButton",n,e,t),!0)}function C(e,t,n,r,a,i=!0,s=!1){i&&(0,g.bN)(),a===m.MB&&(0,l._J)(e,t,r);const o=T(a,e,r);let u,p=t;a===m.mB?u=JSON.stringify({parser:s?"json":(0,c.Ri)(e,r),value:t}):a===m._Y&&"exclude"===n&&(p=`!${t}`);let f=o.state.filters.filter(r=>{const i=(0,h.z2)(a,r);return"include"===n?!(r.key===e&&r.operator===d.w7.NotEqual):"exclude"===n?!(r.key===e&&r.operator===d.w7.Equal):!(r.key===e&&i.value===t)});const v=f.length!==o.state.filters.length;("include"===n||"exclude"===n||!v&&"toggle"===n)&&(f=[...f,{key:e,operator:"exclude"===n?d.w7.NotEqual:d.w7.Equal,value:u||t,valueLabels:[p]}]),o.setState({filters:f}),r.publishEvent(new S("filterButton",n,e,t),!0)}function L(e,t){return e===m.e4?m._Y:t}function _(e,t){var n,r;return(null===(r=(0,f.TG)(t))||void 0===r||null===(n=r.fields)||void 0===n?void 0:n.find(t=>t.name===e))?m.MB:m.mB}class F extends s.Bs{onActivate(){const e=k(this.state.frame);if(e){const t=T(this.state.variableName,e.name,this);this.setFilterState(t),this._subs.add(t.subscribeToState((e,n)=>{(0,o.B)(e.filters,n.filters)||this.setFilterState(t)}))}}setFilterState(e){const t=k(this.state.frame);if(!t)return void this.setState({isExcluded:!1,isIncluded:!1});const n=e.state.filters.find(e=>{const n=(0,u.OH)(t),r=(0,h.z2)(n?m._P:m.mB,e);return e.key===t.name&&r.value===t.value});n?this.setState({isExcluded:n.operator===d.w7.NotEqual,isIncluded:n.operator===d.w7.Equal}):this.setState({isExcluded:!1,isIncluded:!1})}constructor(e){super(e),y(this,"onClick",e=>{const t=k(this.state.frame);if(!t)return;C(t.name,t.value,e,this,this.state.variableName);const n=T(this.state.variableName,t.name,this);(0,b.EE)(b.NO.service_details,b.ir.service_details.add_to_filters_in_breakdown_clicked,{action:e,filtersLength:(null==n?void 0:n.state.filters.length)||0,filterType:this.state.variableName,key:t.name})}),this.addActivationHandler(this.onActivate.bind(this))}}y(F,"Component",({model:e})=>{const{hideExclude:t,isExcluded:n,isIncluded:r}=e.useState();return a().createElement(v.F,{buttonFill:"outline",isIncluded:null!=r&&r,isExcluded:null!=n&&n,onInclude:()=>e.onClick("include"),onClear:()=>e.onClick("clear"),onExclude:()=>e.onClick("exclude"),hideExclude:t})});const k=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{};if(1!==Object.keys(r).length)return void p.v.warn("getFilter: unexpected empty labels");const a=Object.keys(r)[0];return{name:a,value:r[a]}},T=(e,t,n)=>e===m.mB||e===m._P?(0,h.YS)(n):(0,h.bY)(L(t,e),n)},8313:(e,t,n)=>{n.d(t,{G:()=>f,x:()=>g});var r=n(5959),a=n.n(r),i=n(7781),s=n(6865),o=n(5953),l=n(158),c=n(6830),u=n(8072),d=n(9284);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends i.BusEventBase{}p(g,"type","breakdown-search-reset");const h={};class f extends s.Bs{filterValues(e){const t=s.jh.findObject(this,e=>e instanceof u.O||e instanceof c.J6);if(t instanceof u.O||t instanceof c.J6){h[this.cacheKey]=e;const n=s.jh.findDescendents(t,l.h);null==n||n.forEach(t=>{t.state.body.isActive&&t.filterByString(e)})}else o.v.warn("unable to find Breakdown scene",{filter:e,typeofBody:typeof t})}constructor(e){var t;super({filter:null!==(t=h[e])&&void 0!==t?t:""}),p(this,"cacheKey",void 0),p(this,"onValueFilterChange",e=>{this.setState({filter:e.target.value}),this.filterValues(e.target.value)}),p(this,"clearValueFilter",()=>{this.setState({filter:""}),this.filterValues("")}),p(this,"reset",()=>{this.setState({filter:""}),h[this.cacheKey]=""}),this.cacheKey=e}}p(f,"Component",({model:e})=>{const{filter:t}=e.useState();return a().createElement(d.D,{value:t,onChange:e.onValueFilterChange,onClear:e.clearValueFilter,placeholder:"Search for value"})})},158:(e,t,n)=>{n.d(t,{h:()=>y});var r=n(5959),a=n.n(r),i=n(6089),s=n(1269),o=n(7781),l=n(6865),c=n(2007),u=n(5953),d=n(9193),p=n(8313),g=n(6081),h=n(5865),f=n(1049),v=n(2601);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}class y extends l.Bs{performRepeat(e){const t=[],n=(0,v.sortSeries)(e.series,this.sortBy,this.direction);for(let e=0;e<n.length;e++){const r=this.state.getLayoutChild(n[e],e);t.push(r)}this.sortedSeries=n,this.unfilteredChildren=t,this.getFilter()?(this.state.body.setState({children:[]}),this.filterByString(this.getFilter())):this.state.body.setState({children:t})}filterSummaryChart(e){const t=l.jh.getAncestor(this,g.U);if(t){const n=l.jh.findAllObjects(t,e=>e.isActive&&e.state.key===h.s$);if(n[0]instanceof l.G1){const t=l.jh.findDescendents(n[0],l.Eb)[0];t instanceof l.Eb?t.setState({$data:new l.Es({transformations:[()=>{return t=e[0],e=>e.pipe((0,s.map)(e=>{if(!t||!t.length)return e;let n=[];return e.forEach(e=>{const r=(0,f.ee)(e);t.includes(r)&&n.push(e)}),n}));var t}]})}):u.v.warn("filterSummaryChart: VizPanel not found",{typeofPanel:typeof t})}else u.v.warn("filterSummaryChart: SceneFlexItem not found",{typeofGraphParent:typeof n})}}constructor(e){var{direction:t,getFilter:n,sortBy:r}=e;super(m(e,["direction","getFilter","sortBy"])),b(this,"unfilteredChildren",[]),b(this,"sortBy",void 0),b(this,"direction",void 0),b(this,"sortedSeries",[]),b(this,"getFilter",void 0),b(this,"sort",(e,t)=>{const n=l.jh.getData(this);this.sortBy=e,this.direction=t,n.state.data&&this.performRepeat(n.state.data)}),b(this,"iterateFrames",e=>{if(l.jh.getData(this).state.data)for(let t=0;t<this.sortedSeries.length;t++)e(this.sortedSeries,t)}),b(this,"filterByString",e=>{let t=[];this.iterateFrames((e,n)=>{const r=(0,f.ee)(e[n]);t.push(r)}),(0,d.X)(t,e,e=>{e&&e[0]?this.filterFrames(t=>{const n=(0,f.ee)(t);return e[0].includes(n)}):this.filterFrames(()=>!0),this.filterSummaryChart(e)})}),b(this,"filterFrames",e=>{const t=[];if(this.iterateFrames((n,r)=>{e(n[r])&&t.push(this.unfilteredChildren[r])}),0===t.length){const e=this.getFilter();this.state.body.setState({children:[S(e,this.clearFilter)]})}else this.state.body.setState({children:t})}),b(this,"clearFilter",()=>{this.publishEvent(new p.x,!0)}),this.sortBy=r,this.direction=t,this.getFilter=n,this.addActivationHandler(()=>{const e=l.jh.getData(this);this._subs.add(e.subscribeToState((e,t)=>{var n,r,a,i;((null===(n=e.data)||void 0===n?void 0:n.state)===o.LoadingState.Done||(null===(r=e.data)||void 0===r?void 0:r.state)===o.LoadingState.Streaming&&e.data.series.length>(null!==(i=null===(a=t.data)||void 0===a?void 0:a.series.length)&&void 0!==i?i:0))&&this.performRepeat(e.data)})),e.state.data&&this.performRepeat(e.state.data)})}}function S(e,t){return new l.G1({children:[new l.vA({body:new l.dM({reactNode:a().createElement("div",{className:w.alertContainer},a().createElement(c.Alert,{title:"",severity:"info",className:w.noResultsAlert},"No values found matching “",e,"”",a().createElement(c.Button,{className:w.clearButton,onClick:t},"Clear filter")))})})],direction:"row"})}b(y,"Component",({model:e})=>{const{body:t}=e.useState();return a().createElement(t.Component,{model:t})});const w={alertContainer:(0,i.css)({alignItems:"center",display:"flex",flexGrow:1,justifyContent:"center"}),clearButton:(0,i.css)({marginLeft:"1.5rem"}),noResultsAlert:(0,i.css)({flexGrow:0,minWidth:"30vw"})}},713:(e,t,n)=>{n.d(t,{a:()=>c});var r=n(5959),a=n.n(r),i=n(6865),s=n(2007),o=n(7191),l=n(6830);class c extends i.Bs{static Component({model:e}){const{type:t}=e.useState();return a().createElement(o.R,null,a().createElement(s.Alert,{title:"",severity:"warning"},"We did not find any ",t," for the given timerange. Please"," ",a().createElement("a",{className:l.ZI.link,href:"https://forms.gle/1sYWCTPvD72T1dPH9",target:"_blank",rel:"noopener noreferrer"},"let us know")," ","if you think this is a mistake."))}}},7243:(e,t,n)=>{n.d(t,{f:()=>p,u:()=>d});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(7985),l=n(3571);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function d({label:e,onChange:t,options:n,value:i}){const o=(0,s.useStyles2)(g),[l,c]=(0,r.useState)(!1),u=n.map(e=>({label:e.label,value:e.value}));return a().createElement(s.InlineField,{className:o.selectWrapper,label:e},a().createElement(s.Select,{options:u,value:i,onOpenMenu:()=>c(!0),onCloseMenu:()=>c(!1),onChange:e=>t(e.value),className:o.select,prefix:l?void 0:a().createElement(s.Icon,{name:"search"})}))}function p({initialFilter:e,isLoading:t,label:n,onChange:i,options:d,selectOption:p,value:h}){var f;const v=(0,s.useStyles2)(g),[b,m]=(0,r.useState)(!1),[y,S]=(0,r.useState)(e),w=d.map(e=>({label:e.label,value:e.value})),O=y&&h&&(null===(f=y.value)||void 0===f?void 0:f.includes(h))?[y,...w]:w,E=null==O?void 0:O.find(e=>e.value===h);return a().createElement(s.InlineField,{className:v.serviceSceneSelectWrapper,label:n},a().createElement(s.Select,{isLoading:t,"data-testid":l.b.exploreServiceSearch.search,placeholder:"Search values",options:O,isClearable:!0,value:h,onOpenMenu:()=>m(!0),onCloseMenu:()=>m(!1),allowCustomValue:!0,prefix:b||(null==E?void 0:E.__isNew__)?void 0:a().createElement(s.Icon,{name:"search"}),onChange:(e,t)=>(null==e?void 0:e.__isNew__)||(null==e?void 0:e.icon)?(S(u(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){c(e,t,n[t])})}return e}({},e),{icon:"filter"})),i(e.value)):"clear"===t.action?i(""):void("select-option"===t.action&&e.value&&!e.__isNew__&&p(e.value)),onInputChange:(e,t)=>{const n=t;return"input-change"===n.action?i(e):"menu-close"===n.action&&n.prevInputValue?(S({__isNew__:!0,icon:"filter",label:n.prevInputValue,value:(0,o.vC)(n.prevInputValue)}),i(n.prevInputValue)):void 0}}))}function g(e){return{input:(0,i.css)({marginBottom:0}),select:(0,i.css)({maxWidth:e.spacing(64),minWidth:e.spacing(20)}),selectWrapper:(0,i.css)({label:"field-selector-select-wrapper",marginBottom:0,maxWidth:e.spacing(62.5),minWidth:e.spacing(20)}),serviceSceneSelectWrapper:(0,i.css)({label:"service-select-wrapper",marginBottom:0,marginRight:e.spacing.x1,maxWidth:e.spacing(62.5),minWidth:e.spacing(20)})}}},2969:(e,t,n)=>{n.d(t,{u:()=>F});var r,a,i,s=n(5959),o=n.n(s),l=n(7781),c=n(6865),u=n(2007),d=n(1532),p=n(376),g=n(5953),h=n(4907),f=n(7985),v=n(2601),b=n(4351),m=n(5553),y=n(20),S=n(5700),w=n(9721),O=n(158),E=n(6830),x=n(6081),P=n(5865),j=n(2524),C=n(1049);function L(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function _(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){L(i,r,a,s,o,"next",e)}function o(e){L(i,r,a,s,o,"throw",e)}s(void 0)})}}class F extends c.Bs{static Selector({model:e}){const{body:t}=e.useState();return t instanceof x.U?o().createElement(o().Fragment,null,t&&o().createElement(x.U.Selector,{model:t})):o().createElement(o().Fragment,null)}getTagKey(){const e=(0,m.Hj)(this);return String(e.state.value)}onActivate(){var e;const t=this.buildQuery();this.setState({$data:this.buildQueryRunner(),body:this.buildBody(t)}),this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState(e=>{this.onValuesDataQueryChange(e,t)})),this.runQuery(),this.setSubscriptions()}buildQueryRunner(){const e=this.buildQuery();return(0,h.rS)([e],{runQueriesMode:"manual"})}buildQuery(){const e=this.getTagKey(),t=(0,m.ir)(this),n=(0,w.rD)(this),r=(0,m.Gc)(this),a=(0,p.Jl)(e,t,n,r),{filterExpression:i,variableName:s}=this.removeFieldLabelFromVariableInterpolation(),o=c.jh.interpolate(this,a.replace(`\${${s}}`,i));return(0,f.l)(o,{legendFormat:`{{${e}}}`,refId:e})}setSubscriptions(){this._subs.add(c.jh.getTimeRange(this).subscribeToState(()=>{this.runQuery()})),this._subs.add((0,m.Gk)(this).subscribeToState((e,t)=>{(0,d.B)(e.filters,t.filters)||this.runQuery()})),this._subs.add((0,m.Ku)(this).subscribeToState((e,t)=>{e.value!==t.value&&this.runQuery()})),this._subs.add((0,m.cR)(this).subscribeToState((e,t)=>{(0,d.B)(e.filters,t.filters)||this.runQuery()})),this._subs.add((0,m.iw)(this).subscribeToState((e,t)=>{(0,d.B)(e.filters,t.filters)||this.runQuery()}));const{parser:e}=this.getParserForThisField();"structuredMetadata"!==e?this.setFieldParserSubscriptions():this.setMetadataParserSubscriptions()}setMetadataParserSubscriptions(){const e=this.getTagKey();this._subs.add((0,m.ir)(this).subscribeToState((e,t)=>_(function*(){(0,d.B)(e.filters,t.filters)||this.runQuery()}).call(this))),this._subs.add((0,m.oY)(this).subscribeToState((t,n)=>_(function*(){(0,d.B)(t.filters.filter(t=>t.key!==e),n.filters.filter(t=>t.key!==e))||this.runQuery()}).call(this)))}setFieldParserSubscriptions(){const e=this.getTagKey();this._subs.add((0,m.oY)(this).subscribeToState((e,t)=>_(function*(){(0,d.B)(e.filters,t.filters)||this.runQuery()}).call(this))),this._subs.add((0,m.ir)(this).subscribeToState((t,n)=>_(function*(){(0,d.B)(t.filters.filter(t=>t.key!==e),n.filters.filter(t=>t.key!==e))||this.runQuery()}).call(this)))}rebuildQuery(){var e;const t=this.buildQuery();null===(e=this.getSceneQueryRunner())||void 0===e||e.setState({queries:[t]})}runQuery(){this.rebuildQuery();const e=this.getSceneQueryRunner();null==e||e.runQueries()}getSceneQueryRunner(){if(this.state.$data){const e=c.jh.findDescendents(this.state.$data,c.dt);if(1!==e.length){const e=new Error("Unable to find query runner in value breakdown!");throw g.v.error(e,{msg:"FieldValuesBreakdownScene: Unable to find query runner in value breakdown!"}),e}return e[0]}g.v.warn("FieldValuesBreakdownScene: Query is attempting to execute, but query runner is undefined!")}removeFieldLabelFromVariableInterpolation(){const e=this.getTagKey();let t,n;if("structuredMetadata"===this.getQueryParser()){const r=(0,m.oY)(this);n=y._P,t=(0,f.E3)(r.state.filters,[e])}else{n=y.mB;const r=(0,m.ir)(this);t=(0,f.ZX)(r.state.filters,[e])}return{filterExpression:t,variableName:n}}onValuesDataQueryChange(e,t){var n,r;(null===(n=e.data)||void 0===n?void 0:n.state)===l.LoadingState.Done&&this.state.body instanceof c.dM&&this.setState({body:this.buildBody(t)}),(null===(r=e.data)||void 0===r?void 0:r.state)===l.LoadingState.Error&&0===e.data.series.length&&this.setErrorState(e.data.errors,e.data.series.length>0)}buildErrorState(e,t){const n=c.jh.getAncestor(this,w.Mn);return new c.G1({children:[new c.vA({body:new c.dM({reactNode:o().createElement(j.xX,{errors:e,tagKey:this.getTagKey(),isPartial:t,serviceScene:n})})})],direction:"column"})}setErrorState(e,t){this.setState({errorBody:this.buildErrorState(e,t)})}buildBody(e){const{optionValue:t,parser:n}=this.getParserForThisField(),{direction:r,sortBy:a}=(0,b.vs)("fields",v.DEFAULT_SORT_BY,"desc"),i=c.jh.getAncestor(this,E.J6),s=()=>{var e;return null!==(e=i.state.search.state.filter)&&void 0!==e?e:""};return new x.U({active:"grid",layouts:[new c.G1({children:[new c.N0,new P.s7({tagKey:this.getTagKey(),title:t,type:"field"}),new c.dM({reactNode:o().createElement(E.J6.ValuesMenu,{model:i})}),new O.h({body:new c.gF({autoRows:"200px",children:[new c.vA({body:new c.dM({reactNode:null})})],isLazy:!0,templateColumns:E.OK}),direction:r,getFilter:s,getLayoutChild:(0,p.Zp)(C.ee,(null==e?void 0:e.expr.includes("count_over_time"))?u.DrawStyle.Bars:u.DrawStyle.Line,"structuredMetadata"===n?y._P:y.mB,c.jh.getAncestor(this,E.J6).state.sort,t),sortBy:a})],direction:"column"}),new c.G1({children:[new c.N0,new P.s7({tagKey:this.getTagKey(),title:t,type:"field"}),new c.dM({reactNode:o().createElement(E.J6.ValuesMenu,{model:i})}),new O.h({body:new c.gF({autoRows:"200px",children:[new c.vA({body:new c.dM({reactNode:null})})],isLazy:!0,templateColumns:"1fr"}),direction:r,getFilter:s,getLayoutChild:(0,p.Zp)(C.ee,(null==e?void 0:e.expr.includes("count_over_time"))?u.DrawStyle.Bars:u.DrawStyle.Line,"structuredMetadata"===n?y._P:y.mB,c.jh.getAncestor(this,E.J6).state.sort,t),sortBy:a})],direction:"column"})],options:[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]})}getParserForThisField(){const e=(0,m.Hj)(this),t=String(e.state.value);return{optionValue:t,parser:(0,p.Ri)(t,this)}}getParserForFields(){return(0,p.k$)((0,m.ir)(this))}getQueryParser(){const{parser:e}=this.getParserForThisField(),t=this.getParserForFields();return e===t?t:void 0===e?"mixed":"structuredMetadata"===e?t:"structuredMetadata"===t?e:"mixed"}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}i=({model:e})=>{var t;const{body:n,errorBody:r}=e.useState(),a=(0,u.useStyles2)(S.K_),i=c.jh.getAncestor(e,E.J6),s=c.jh.getData(e),{data:l}=s.useState();var d;const p=(null!==(d=null==l?void 0:l.series.length)&&void 0!==d?d:0)>0;var g;const h=(null!==(g=null==l||null===(t=l.errors)||void 0===t?void 0:t.length)&&void 0!==g?g:0)>0;return n?o().createElement("span",{className:a.panelWrapper},o().createElement(E.J6.LabelsMenu,{model:i}),h&&r&&o().createElement("div",{className:a.errorWrapper},o().createElement(r.Component,{model:r})),(p||!r)&&o().createElement("div",null,n instanceof x.U&&o().createElement(n.Component,{model:n}),!(n instanceof x.U)&&n&&o().createElement(n.Component,{model:n}))):o().createElement(u.LoadingPlaceholder,{text:"Loading..."})},(a="Component")in(r=F)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},8351:(e,t,n)=>{n.d(t,{E:()=>C});var r=n(5959),a=n.n(r),i=n(7781),s=n(6865),o=n(2007),l=n(4509),c=n(7839),u=n(376),d=n(5953),p=n(4907),g=n(7985),h=n(4351),f=n(5553),v=n(20),b=n(5700),m=n(9721),y=n(6830),S=n(6081),w=n(2606),O=n(6089);const E=e=>({toggleIcon:(0,O.css)({color:e.colors.error.main,marginRight:e.spacing(1)}),toggleLabel:(0,O.css)({display:"flex",marginRight:e.spacing(2)}),toggleLabelText:(0,O.css)({marginRight:e.spacing(1)})});const x=e=>({radioGroup:(0,O.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{flexDirection:"row"},"> div > label":{height:"100%"},flexDirection:"column"})});var P=n(4059);function j(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class C extends s.Bs{updateChildren(e,t=void 0){var n;const r=(0,m.UO)(e),a=(0,m.nU)(e),i=(0,m.dB)(e),o=this.calculateCardinalityMap(e);null===(n=this.state.body)||void 0===n||n.state.layouts.forEach(e=>{if(e instanceof s.gF){const n=new Set(null==a?void 0:a.values),l=e.state.children;for(let o=0;o<l.length;o++){const c=e.state.children[o];if(c instanceof s.xK){const e=c.state.body;if(e instanceof s.Eb){if(t){const n=null==a?void 0:a.values.indexOf(e.state.title),s=n&&-1!==n?null==i?void 0:i.values[n]:void 0;if("timeseries"===this.state.fieldsPanelsType&&s!==t){const t=(0,u.ph)(e.state.title,r),n=this.getTimeSeriesQueryRunnerForPanel(e.state.title,r,t);e.setState({$data:n})}}n.has(e.state.title)?n.delete(e.state.title):(l.splice(o,1),o--)}else d.v.warn("panel is not VizPanel!")}else d.v.warn("gridItem is not SceneCSSGridItem")}const c=Array.from(n).map(e=>e);l.push(...this.buildChildren(c)),l.sort(this.sortChildren(o)),l.map(e=>{this.subscribeToPanel(e)}),e.setState({children:l})}else d.v.warn("Layout is not SceneCSSGridLayout")})}sortChildren(e){return(t,n)=>{const r=t.state.body,a=n.state.body;var i;const s=null!==(i=e.get(r.state.title))&&void 0!==i?i:0;var o;return(null!==(o=e.get(a.state.title))&&void 0!==o?o:0)-s}}calculateCardinalityMap(e){const t=(0,m.UO)(e),n=new Map;if(null==t?void 0:t.length)for(let e=0;e<(null==t?void 0:t.length);e++){const r=t.fields[0].values[e],a=t.fields[1].values[e];n.set(r,a)}return n}onActivate(){var e;this.setState({body:this.build()});const t=s.jh.getAncestor(this,m.Mn);void 0===t.state.fieldsCount&&this.updateFieldCount(),this._subs.add(null===(e=t.state.$detectedFieldsData)||void 0===e?void 0:e.subscribeToState(this.onDetectedFieldsChange)),this._subs.add(this.subscribeToFieldsVar()),this._subs.add(this.subscribeToState((e,t)=>{e.fieldsPanelsType!==t.fieldsPanelsType&&this.setState({body:this.build()})}))}subscribeToFieldsVar(){return(0,f.ir)(this).subscribeToState((e,t)=>{const n=s.jh.getAncestor(this,m.Mn),r=e.filters.map(e=>(0,f.bu)(e).parser),a=t.filters.map(e=>(0,f.bu)(e).parser),i=(0,u.Qg)(r);if(i!==(0,u.Qg)(a)){var o;const e=null===(o=n.state.$detectedFieldsData)||void 0===o?void 0:o.state;e&&this.updateChildren(e,i)}})}build(){var e;const t=(0,f.Hj)(this).state.options.map(e=>String(e.value));s.jh.getAncestor(this,y.J6).state.search.reset();const n=this.buildChildren(t),r=s.jh.getAncestor(this,m.Mn),a=this.calculateCardinalityMap(null===(e=r.state.$detectedFieldsData)||void 0===e?void 0:e.state);n.sort(this.sortChildren(a));const i=n.map(e=>e.clone());return[...n,...i].map(e=>{this.subscribeToPanel(e)}),new S.U({active:"grid",layouts:[new s.gF({autoRows:"timeseries"===this.state.fieldsPanelsType?"200px":"35px",children:n,isLazy:!0,templateColumns:y.OK}),new s.gF({autoRows:"timeseries"===this.state.fieldsPanelsType?"200px":"35px",children:i,isLazy:!0,templateColumns:"1fr"})],options:[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]})}subscribeToPanel(e){const t=e.state.body;var n;t&&t instanceof s.Eb&&this._subs.add(null==t||null===(n=t.state.$data)||void 0===n?void 0:n.getResultsStream().subscribe(n=>{n.data.errors&&n.data.errors.length>0?(this.state.showErrorPanels?e.setState({isHidden:!1}):e.setState({isHidden:!0}),this.state.showErrorPanelToggle||this.setState({showErrorPanelToggle:!0}),this.updateFieldCount()):(0,p.qi)(n,t)}))}rebuildAvgFields(){const e=(0,m.rD)(this),t=this.getActiveGridLayouts(),n=[];var r;const a=null!==(r=(0,h.ex)("panelType",[b.Ze.histogram,b.Ze.timeseries]))&&void 0!==r?r:b.Ze.timeseries;null==t||t.state.children.forEach(t=>{if(t instanceof s.xK&&this.state.showErrorPanels||t instanceof s.xK&&!t.state.isHidden){const r=s.jh.findDescendents(t,s.Eb);if(r.length){const i=r[0].state.title,s=(0,u.ph)(i,e);if((0,u.JI)(s)){const t=this.buildChild(i,e,a);t&&n.push(t)}else n.push(t)}}}),n.length&&(null==t||t.setState({children:n}))}buildChildren(e){const t=[],n=(0,m.rD)(this);var r;const a=null!==(r=(0,h.ex)("panelType",[b.Ze.timeseries,b.Ze.histogram]))&&void 0!==r?r:b.Ze.timeseries;for(const r of e){if(r===v.To||!r)continue;const e=this.buildChild(r,n,a);e&&t.push(e)}return t}buildChild(e,t,n){if(e===v.To||!e)return;const r=(0,u.ph)(e,t);let a;if("text"===this.state.fieldsPanelsType){const t=this.getEstimatedCardinalityQueryRunnerForPanel(e);a=this.buildText(e,r,t)}else{const i=this.getTimeSeriesQueryRunnerForPanel(e,t,r);a=this.buildTimeSeries(r,e,i,n)}a.setShowMenuAlways(!0);const i=a.build();return new s.xK({body:i})}getTimeSeriesQueryRunnerForPanel(e,t,n){const r=(0,f.ir)(this),a=(0,f.Gc)(this),i=(0,u.Jl)(e,r,t,a),s=(0,g.l)(i,{legendFormat:(0,u.JI)(n)?e:`{{${e}}}`,refId:e});return(0,p.rS)([s])}getEstimatedCardinalityQueryRunnerForPanel(e){return new s.Es({transformations:[]})}getActiveGridLayouts(){var e,t,n;return null!==(n=null===(e=this.state.body)||void 0===e?void 0:e.state.layouts.find(e=>e.isActive))&&void 0!==n?n:null===(t=this.state.body)||void 0===t?void 0:t.state.layouts[0]}updateFieldCount(){var e,t;const n=this.getActiveGridLayouts(),r=null==n?void 0:n.state.children,a=null==r?void 0:r.filter(e=>this.state.showErrorPanels||!e.state.isHidden);var i;null===(e=(t=s.jh.getAncestor(this,y.J6).state).changeFieldCount)||void 0===e||e.call(t,null!==(i=null==a?void 0:a.length)&&void 0!==i?i:0)}toggleErrorPanels(e){const t=e.target.checked;this.setState({showErrorPanels:t}),(0,h.rg)(t);const n=s.jh.getAncestor(this,m.Mn);var r,a;((0,l.EE)(l.NO.service_details,l.ir.service_details.toggle_error_panels,{checked:t}),t)?this.setState({body:this.build()}):(null===(r=n.state.$detectedFieldsData)||void 0===r?void 0:r.state)?this.updateChildren(null===(a=n.state.$detectedFieldsData)||void 0===a?void 0:a.state):this.setState({body:this.build()})}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&a().createElement(S.U.Selector,{model:t}))}constructor(e){var t;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){j(e,t,n[t])})}return e}({fieldsPanelsType:null!==(t=(0,h.JA)())&&void 0!==t?t:"timeseries",showErrorPanels:(0,h.Vt)(),showErrorPanelToggle:!1},e)),j(this,"onDetectedFieldsChange",e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done&&this.updateChildren(e)}),j(this,"buildText",(e,t,n)=>{const r=s.d0.text().setTitle(e).setData(n).setHeaderActions(new w.X({fieldType:c._J.field,hasNumericFilters:"int"===t,labelName:String(e)}));return r.setOption("content",""),r}),j(this,"buildTimeSeries",(e,t,n,r)=>{let a,i=[];return(0,u.JI)(e)?(a="histogram"===r?s.d0.histogram():s.d0.timeseries(),a.setTitle(t).setData(n).setMenu(new b.GD({investigationOptions:{labelName:t},panelType:r})),i.push(new w.X({fieldType:c._J.field,hideValueDrilldown:!0,labelName:String(t)}))):(a=s.d0.timeseries().setTitle(t).setData(n).setMenu(new b.GD({investigationOptions:{labelName:t}})).setCustomFieldConfig("stacking",{mode:o.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",o.DrawStyle.Bars).setOverrides(p.jC),i.push(new w.X({fieldType:c._J.field,hasNumericFilters:"int"===e,labelName:String(t)}))),a.setSeriesLimit(P.l),a.setHeaderActions(i),a}),this.addActivationHandler(this.onActivate.bind(this))}}j(C,"ShowErrorPanelToggle",function({model:e}){const{showErrorPanels:t,showErrorPanelToggle:n}=e.useState(),r=(0,o.useStyles2)(E);return n?a().createElement(o.Label,{className:r.toggleLabel},a().createElement(o.IconButton,{className:r.toggleIcon,tooltip:"One or more requests could not be processed",name:"exclamation-triangle",variant:"secondary"}),a().createElement("span",{className:r.toggleLabelText},"Show panels with errors"),a().createElement(o.InlineSwitch,{onChange:t=>e.toggleErrorPanels(t),value:t})):null}),j(C,"ShowFieldDisplayToggle",function({model:e}){const{fieldsPanelsType:t}=e.useState(),n=(0,o.useStyles2)(x);return a().createElement(o.RadioButtonGroup,{className:n.radioGroup,options:[{label:"Volume",value:"timeseries"},{label:"Names",value:"text"}],value:t,onChange:t=>{e.setState({fieldsPanelsType:t}),(0,h.NM)(t),(0,l.EE)(l.NO.service_details,l.ir.service_details.fields_panel_type_toggle,{fieldsPanelType:t})}})}),j(C,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,o.useStyles2)(b.K_);return t?a().createElement("span",{className:n.panelWrapper},t&&a().createElement(t.Component,{model:t})):a().createElement(o.LoadingPlaceholder,{text:"Loading..."})})},6830:(e,t,n)=>{n.d(t,{J6:()=>$,OK:()=>N,ZI:()=>A});var r=n(5959),a=n.n(r),i=n(6089),s=n(7781),o=n(6865),l=n(2007),c=n(1532),u=n(4702),d=n(7839),p=n(7478),g=n(9683),h=n(2601),f=n(5553),v=n(5548),b=n(8714),m=n(9721),y=n(8313),S=n(158),w=n(713),O=n(8351),E=n(7243),x=n(2969),P=n(6081),j=n(6779),C=n(1049),L=n(5659),_=n(4509),F=n(8502),k=n(4351),T=n(20);function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const N="repeat(auto-fit, minmax(400px, 1fr))";class $ extends o.Bs{onActivate(){var e,t,n;const r=(0,f.Hj)(this),a=o.jh.getAncestor(this,m.Mn);this.setState({loading:(null===(t=a.state.$detectedLabelsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Loading}),this._subs.add(this.subscribeToEvent(y.x,()=>{this.state.search.clearValueFilter()})),this._subs.add(this.subscribeToEvent(C.gf,this.handleSortByChange)),this._subs.add(r.subscribeToState(this.variableChanged)),this._subs.add((0,f.cR)(this).subscribeToState((e,t)=>{const n=(0,f.Hj)(this);let{labelName:r}=this.getPrimaryLabel();const a=e.filters.find(e=>e.key===r),i=t.filters.find(e=>e.key===r);n.state.value===T.To&&a!==i&&this.setState({body:void 0,loading:!0})})),this._subs.add(null===(n=a.state.$detectedFieldsData)||void 0===n?void 0:n.subscribeToState((e,t)=>{var n,r,a,i;(null===(n=e.data)||void 0===n?void 0:n.state)!==s.LoadingState.Done&&(null===(r=e.data)||void 0===r?void 0:r.state)!==s.LoadingState.Error||(null===(a=e.data.series)||void 0===a?void 0:a[0])&&this.updateOptions(null===(i=e.data.series)||void 0===i?void 0:i[0])}));const i=(0,m.rD)(this);i&&this.updateOptions(i)}getPrimaryLabel(){let{breakdownLabel:e,labelName:t,labelValue:n}=(0,g.MC)(this);if(!t||!n){const e=(0,f.cR)(this);return t=e.state.filters[0].key,n=e.state.filters[0].value,{labelName:(0,g.UU)(t),labelValue:n}}return{breakdownLabel:e,labelName:t,labelValue:n}}updateOptions(e){if(!e||!e.length){const e=o.jh.getAncestor(this,b.P);let r;var t,n;if((0,v.mE)(e).length>1)null===(t=(n=this.state).changeFieldCount)||void 0===t||t.call(n,0),r=new j.W({clearCallback:()=>(0,v.rA)(this)});else r=new w.a({type:"fields"});return void this.setState({body:r,loading:!1})}const r=o.jh.getAncestor(this,m.Mn);var a;(0,f.Hj)(this).setState({loading:!1,options:(0,F.rd)(e.fields[0].values.map(e=>String(e))),value:null!==(a=r.state.drillDownLabel)&&void 0!==a?a:T.To}),this.setState({loading:!1})}updateBody(e){const t=(0,f.Hj)(this);if(!t.state.options||!t.state.options.length)return;const n={};if(t.state.options&&t.state.options.length<=1){const e=o.jh.getAncestor(this,b.P);var r,a;if((0,v.mE)(e).length>1)null===(r=(a=this.state).changeFieldCount)||void 0===r||r.call(a,0),n.body=new j.W({clearCallback:()=>(0,v.rA)(this)});else n.body=new w.a({type:"fields"})}else e.value===T.To&&this.state.body instanceof x.u?n.body=new O.E({}):e.value!==T.To&&this.state.body instanceof O.E?n.body=new x.u({}):(void 0===this.state.body||this.state.body instanceof w.a||this.state.body instanceof j.W)&&(n.body=e.value===T.To?new O.E({}):new x.u({}));this.setState(n)}constructor(e){var t,n,r,a;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){D(e,t,n[t])})}return e}({$variables:null!==(r=e.$variables)&&void 0!==r?r:new o.Pj({variables:[new u.m({defaultToAll:!1,includeAll:!0,name:T.LI,options:null!==(t=e.options)&&void 0!==t?t:[],value:null!==(n=e.value)&&void 0!==n?n:T.To})]}),loading:!0,search:new y.G("fields"),sort:new C.wd({target:"fields"}),value:null!==(a=e.value)&&void 0!==a?a:T.To},e)),D(this,"_variableDependency",new o.Sh(this,{variableNames:[T.MB]})),D(this,"variableChanged",(e,t)=>{(e.value!==t.value||!(0,c.B)(e.options,t.options)||void 0===this.state.body||this.state.body instanceof w.a||this.state.body instanceof j.W)&&this.updateBody(e)}),D(this,"handleSortByChange",e=>{if("fields"!==e.target)return;const t=this.state.body;var n;t instanceof x.u&&t.state.body instanceof P.U&&(null===(n=t.state.body)||void 0===n||n.state.layouts.forEach(n=>{o.jh.findDescendents(t,S.h).forEach(t=>t.sort(e.sortBy,e.direction))}));(0,_.EE)(_.NO.service_details,_.ir.service_details.value_breakdown_sort_change,{criteria:e.sortBy,direction:e.direction,target:"fields"})}),D(this,"onFieldSelectorChange",e=>{if(!e)return;const t=(0,f.Hj)(this),{direction:n,sortBy:r}=(0,k.vs)("fields",h.DEFAULT_SORT_BY,"desc");(0,_.EE)(_.NO.service_details,_.ir.service_details.select_field_in_breakdown_clicked,{field:e,previousField:t.getValueText(),sortBy:r,sortByDirection:n,view:"fields"});const a=o.jh.getAncestor(this,m.Mn);(0,p.fg)(d._J.field,e,a)}),this.addActivationHandler(this.onActivate.bind(this))}}D($,"LabelsMenu",({hideSearch:e,model:t})=>{const{body:n,loading:r,search:s}=t.useState(),o=(0,l.useStyles2)(B),c=(0,f.Hj)(t),{options:u,value:d}=c.useState();return null==n||n.useState(),a().createElement("div",{className:(0,i.cx)(o.labelsMenuWrapper,e?o.labelsMenuWrapperNoSearch:void 0)},n instanceof O.E&&a().createElement(a().Fragment,null,a().createElement("span",{className:o.toggleWrapper},"text"!==n.state.fieldsPanelsType&&a().createElement(O.E.ShowErrorPanelToggle,{model:n}),a().createElement(O.E.Selector,{model:n})),a().createElement(O.E.ShowFieldDisplayToggle,{model:n})),n instanceof x.u&&a().createElement(x.u.Selector,{model:n}),!0!==e&&n instanceof x.u&&a().createElement(s.Component,{model:s}),!r&&u.length>1&&a().createElement(E.u,{label:"Field",options:u,value:String(d),onChange:t.onFieldSelectorChange}))}),D($,"ValuesMenu",({model:e})=>{const{loading:t,sort:n}=e.useState(),r=(0,l.useStyles2)(B),i=(0,f.Hj)(e),{value:s}=i.useState();return a().createElement("div",{className:r.valuesMenuWrapper},!t&&s!==T.To&&a().createElement(a().Fragment,null,a().createElement(n.Component,{model:n})))}),D($,"Component",({model:e})=>{const{blockingMessage:t,body:n,loading:r}=e.useState(),i=(0,l.useStyles2)(B);return a().createElement("div",{className:i.container},a().createElement(L.O,{blockingMessage:t,isLoading:r},n instanceof O.E&&e&&a().createElement($.LabelsMenu,{model:e}),a().createElement("div",{className:i.content},n&&a().createElement(n.Component,{model:n}))))});const A={button:(0,i.css)({marginLeft:"1.5rem"}),link:(0,i.css)({textDecoration:"underline"})};function B(e){return{container:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1,gap:e.spacing(1),minHeight:"100%"}),content:(0,i.css)({display:"flex",flexGrow:1,paddingTop:e.spacing(0)}),labelsMenuWrapper:(0,i.css)({alignItems:"top",display:"flex",flexDirection:"row-reverse",flexGrow:0,gap:e.spacing(2),justifyContent:"space-between"}),labelsMenuWrapperNoSearch:(0,i.css)({flexDirection:"row"}),toggleWrapper:(0,i.css)({display:"flex",flexDirection:"row"}),valuesMenuWrapper:(0,i.css)({alignItems:"top",display:"flex",flexDirection:"row",flexGrow:0,gap:e.spacing(2)})}}},8072:(e,t,n)=>{n.d(t,{O:()=>I});var r=n(5959),a=n.n(r),i=n(6089),s=n(7781),o=n(6865),l=n(2007),c=n(1532),u=n(4702),d=n(7839),p=n(7478),g=n(2601),h=n(5553),f=n(9721),v=n(8313),b=n(158),m=n(713),y=n(7243),S=n(42),w=n(4907),O=n(20),E=n(5700),x=n(6081),P=n(2606),j=n(4059);function C(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class L extends o.Bs{onActivate(){var e;const t=(0,h.ir)(this),n=o.jh.getAncestor(this,f.Mn).state.$detectedLabelsData;this.state.body?(null==n||null===(e=n.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Done&&this.update(null==n?void 0:n.state.data.series[0]):this.setState({body:this.build()}),this._subs.add(null==n?void 0:n.subscribeToState((e,t)=>{var n;(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&this.update(e.data.series[0])})),this._subs.add(t.subscribeToState(()=>{this.updateQueriesOnFieldsVariableChange()}))}getPanelByIndex(e,t){const n=e.state.children[t].state.body;return{panel:n,title:n.state.title}}update(e){var t;const n=(0,h.P4)(this).state.options.filter(e=>e.value!==O.To).map(e=>e.label);null===(t=this.state.body)||void 0===t||t.state.layouts.forEach(t=>{let r=[];const a=t,i=new Set(n),s=a.state.children;for(let e=0;e<s.length;e++){const{title:t}=this.getPanelByIndex(a,e);i.has(t)?i.delete(t):(s.splice(e,1),e--),r.push(t)}const o=Array.from(i).map(e=>({label:e,value:e}));s.push(...this.buildChildren(o));const l=this.calculateCardinalityMap(e);s.sort(this.sortChildren(l)),a.setState({children:s})})}calculateCardinalityMap(e){const t=new Map;if(null==e?void 0:e.length)for(let n=0;n<(null==e?void 0:e.fields.length);n++){const r=e.fields[n].name,a=e.fields[n].values[0];t.set(r,a)}return t}build(){var e;const t=(0,h.P4)(this);o.jh.getAncestor(this,I).state.search.reset();const n=this.buildChildren(t.state.options),r=o.jh.getAncestor(this,f.Mn).state.$detectedLabelsData;if((null==r||null===(e=r.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Done){const e=this.calculateCardinalityMap(null==r?void 0:r.state.data.series[0]);n.sort(this.sortChildren(e))}const a=n.map(e=>e.clone());return new x.U({active:"grid",layouts:[new o.gF({autoRows:"200px",children:n,isLazy:!0,templateColumns:S.di}),new o.gF({autoRows:"200px",children:a,isLazy:!0,templateColumns:"1fr"})],options:[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]})}buildChildren(e){const t=[];for(const n of e){const{value:e}=n,r=String(e);if(e===O.To||!e)continue;const a=(0,S.oj)(this,String(n.value),String(n.value)),i=(0,w.rS)([a]);t.push(new o.xK({body:o.d0.timeseries().setTitle(r).setData(i).setHeaderActions([new P.X({fieldType:d._J.label,labelName:r})]).setCustomFieldConfig("stacking",{mode:l.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",l.DrawStyle.Bars).setHoverHeader(!1).setShowMenuAlways(!0).setOverrides(w.jC).setMenu(new E.GD({investigationOptions:{labelName:r}})).setSeriesLimit(j.l).build()}))}return t}sortChildren(e){return(t,n)=>{const r=t.state.body,a=n.state.body;if(r.state.title===O.e4)return-1;if(a.state.title===O.e4)return 1;var i;const s=null!==(i=e.get(r.state.title))&&void 0!==i?i:0;var o;return(null!==(o=e.get(a.state.title))&&void 0!==o?o:0)-s}}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&a().createElement(x.U.Selector,{model:t}))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){C(e,t,n[t])})}return e}({},e)),C(this,"updateQueriesOnFieldsVariableChange",()=>{var e;null===(e=this.state.body)||void 0===e||e.state.layouts.forEach(e=>{const t=e;for(let e=0;e<t.state.children.length;e++){const{panel:a,title:i}=this.getPanelByIndex(t,e),s=a.state.$data,l=(0,S.oj)(this,i,i);var n,r;if(s instanceof o.dt)if(l.expr===(null==s||null===(r=s.state.queries)||void 0===r||null===(n=r[0])||void 0===n?void 0:n.expr))break;a.setState({$data:(0,w.rS)([l])})}})}),this.addActivationHandler(this.onActivate.bind(this))}}C(L,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,l.useStyles2)(E.K_);return t?a().createElement("span",{className:n.panelWrapper},t&&a().createElement(t.Component,{model:t})):a().createElement(l.LoadingPlaceholder,{text:"Loading..."})});var _=n(6887),F=n(1049),k=n(5659),T=n(4509),D=n(8502),N=n(9683),$=n(4351);function A(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){A(e,t,n[t])})}return e}function M(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class I extends o.Bs{onActivate(){var e,t,n,r,a;const i=o.jh.getAncestor(this,f.Mn),l=(0,h.P4)(this);this.setState({error:(null===(t=i.state.$detectedLabelsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)===s.LoadingState.Error,loading:(null===(r=i.state.$detectedLabelsData)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)!==s.LoadingState.Done}),this._subs.add(this.subscribeToEvent(v.x,()=>{this.state.search.clearValueFilter()})),this._subs.add(this.subscribeToEvent(F.gf,this.handleSortByChange)),this._subs.add(null===(a=i.state.$detectedLabelsData)||void 0===a?void 0:a.subscribeToState(this.onDetectedLabelsDataChange)),this._subs.add((0,h.cR)(this).subscribeToState((e,t)=>{this.onLabelsVariableChange(e,t)})),this._subs.add(l.subscribeToState((e,t)=>{this.onGroupByVariableChange(e,t)}));const c=(0,f.TG)(this);c&&this.updateOptions(c)}onGroupByVariableChange(e,t){(e.value!==t.value||!(0,c.B)(e.options,t.options)||void 0===this.state.body||this.state.body instanceof m.a)&&this.updateBody()}onLabelsVariableChange(e,t){let{labelName:n}=(0,N.MC)(this);n===O.ky&&(n=O.OX);const r=(0,h.P4)(this),a=e.filters.find(e=>e.key===n),i=t.filters.find(e=>e.key===n);r.state.value===O.To&&a!==i&&this.setState({body:void 0,error:void 0,loading:!0})}updateOptions(e){if(!e||!e.length)return void this.setState({body:new m.a({type:"labels"}),loading:!1});const t=(0,h.P4)(this),n=(0,D.dD)(e.fields.map(e=>e.name));var r;t.setState({loading:!1,options:n,value:null!==(r=this.state.value)&&void 0!==r?r:O.To})}updateBody(){const e=(0,h.P4)(this);if(!e.state.options||!e.state.options.length)return;const t={blockingMessage:void 0,error:!1,loading:!1};e.hasAllValue()&&this.state.body instanceof _.u?t.body=new L({}):!e.hasAllValue()&&this.state.body instanceof L?t.body=new _.u({}):void 0===this.state.body?e.state.options.length>0?t.body=e.hasAllValue()?new L({}):new _.u({}):t.body=new m.a({type:"labels"}):this.state.body instanceof m.a&&e.state.options.length>0&&(t.body=e.hasAllValue()?new L({}):new _.u({})),this.setState(B({},t))}constructor(e){var t,n,r;super(M(B({},e),{$variables:null!==(r=e.$variables)&&void 0!==r?r:new o.Pj({variables:[new u.m({defaultToAll:!1,includeAll:!0,name:O.Jg,options:null!==(t=e.options)&&void 0!==t?t:[],value:null!==(n=e.value)&&void 0!==n?n:O.To})]}),loading:!0,search:new v.G("labels"),sort:new F.wd({target:"labels"}),value:e.value})),A(this,"_variableDependency",new o.Sh(this,{variableNames:[O.MB]})),A(this,"onDetectedLabelsDataChange",(e,t)=>{var n,r,a,i,o,l,u,d,p;if((null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&(null===(r=e.data.series)||void 0===r?void 0:r[0])&&!(0,c.B)(null===(i=e.data.series)||void 0===i||null===(a=i[0])||void 0===a?void 0:a.fields,null===(u=t.data)||void 0===u||null===(l=u.series)||void 0===l||null===(o=l[0])||void 0===o?void 0:o.fields))this.updateOptions(null===(p=e.data.series)||void 0===p?void 0:p[0]);else if((null===(d=e.data)||void 0===d?void 0:d.state)===s.LoadingState.Done){(0,h.P4)(this).setState({loading:!1})}}),A(this,"handleSortByChange",e=>{if("labels"!==e.target)return;const t=this.state.body;if(t instanceof _.u){o.jh.findDescendents(t,b.h).forEach(t=>{t.sort(e.sortBy,e.direction)})}(0,T.EE)(T.NO.service_details,T.ir.service_details.value_breakdown_sort_change,{criteria:e.sortBy,direction:e.direction,target:"labels"})}),A(this,"onChange",e=>{if(!e)return;const t=(0,h.P4)(this);t.changeValueTo(e);const{direction:n,sortBy:r}=(0,$.vs)("labels",g.DEFAULT_SORT_BY,"desc");(0,T.EE)(T.NO.service_details,T.ir.service_details.select_field_in_breakdown_clicked,{label:e,previousLabel:t.getValueText(),sortBy:r,sortByDirection:n,view:"labels"});const a=o.jh.getAncestor(this,f.Mn);(0,p.fg)(d._J.label,e,a)}),this.addActivationHandler(this.onActivate.bind(this))}}function R(e){return{container:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1,gap:e.spacing(1),minHeight:"100%"}),content:(0,i.css)({display:"flex",flexGrow:1,paddingTop:e.spacing(0)}),labelsMenuWrapper:(0,i.css)({alignItems:"top",display:"flex",flexDirection:"row-reverse",flexGrow:0,gap:e.spacing(2),justifyContent:"space-between"}),valuesMenuWrapper:(0,i.css)({alignItems:"top",display:"flex",flexDirection:"row",flexGrow:0,gap:e.spacing(2)})}}A(I,"LabelsMenu",({model:e})=>{const{body:t,loading:n,search:r}=e.useState(),i=(0,h.P4)(e),{options:s,value:o}=i.useState(),c=(0,l.useStyles2)(R);return a().createElement("div",{className:c.labelsMenuWrapper},t instanceof _.u&&a().createElement(_.u.Selector,{model:t}),t instanceof L&&a().createElement(L.Selector,{model:t}),t instanceof _.u&&a().createElement(r.Component,{model:r}),!n&&s.length>0&&a().createElement(y.u,{label:"Label",options:s,value:String(o),onChange:e.onChange}))}),A(I,"ValuesMenu",({model:e})=>{const{loading:t,sort:n}=e.useState(),r=(0,h.P4)(e),{value:i}=r.useState(),s=(0,l.useStyles2)(R);return a().createElement("div",{className:s.valuesMenuWrapper},!t&&i!==O.To&&a().createElement(a().Fragment,null,a().createElement(n.Component,{model:n})))}),A(I,"Component",({model:e})=>{const{blockingMessage:t,body:n,error:r,loading:i}=e.useState(),s=(0,l.useStyles2)(R);return a().createElement("div",{className:s.container},a().createElement(k.O,{blockingMessage:t,isLoading:i},r&&a().createElement(l.Alert,{title:"",severity:"warning"},"The labels are not available at this moment. Try using a different time range or check again later."),n instanceof L&&e&&a().createElement(I.LabelsMenu,{model:e}),a().createElement("div",{className:s.content},n&&a().createElement(n.Component,{model:n}))))})},6887:(e,t,n)=>{n.d(t,{u:()=>N});var r=n(5959),a=n.n(r),i=n(7781),s=n(8531),o=n(6865),l=n(2007),c=n(1532),u=n(376),d=n(42),p=n(5953),g=n(4907),h=n(7985),f=n(2601),v=n(4351),b=n(5553),m=n(5548),y=n(20),S=n(8714),w=n(5700),O=n(158),E=n(713),x=n(8072),P=n(6081),j=n(6779),C=n(5865),L=n(1049);function _(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function F(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){_(i,r,a,s,o,"next",e)}function o(e){_(i,r,a,s,o,"throw",e)}s(void 0)})}}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){k(e,t,n[t])})}return e}function D(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class N extends o.Bs{onActivate(){this.setState({$data:this.buildQueryRunner(),body:this.build()}),this.runQuery(),this.setSubscriptions()}buildQueryRunner(){const e=this.buildQuery();return(0,g.rS)([e],{runQueriesMode:"manual"})}buildQuery(){const e=(0,d.oj)(this,y.zp,String((0,b.P4)(this).state.value)),{filterExpression:t,variableName:n}=this.removeValueLabelFromVariableInterpolation();return e.expr=e.expr.replace(`\${${n}}`,t),e}setSubscriptions(){var e;this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState((e,t)=>{this.onValuesDataQueryChange(e)})),this._subs.add((0,b.P4)(this).subscribeToState(e=>{e.value===y.To&&this.setState({$data:void 0,body:void 0})})),this._subs.add(o.jh.getTimeRange(this).subscribeToState(()=>{this.runQuery()})),this._subs.add((0,b.ir)(this).subscribeToState((e,t)=>{(0,c.B)(e.filters,t.filters)||this.runQuery()})),this._subs.add((0,b.oY)(this).subscribeToState((e,t)=>{(0,c.B)(e.filters,t.filters)||this.runQuery()})),this._subs.add((0,b.Gk)(this).subscribeToState((e,t)=>{(0,c.B)(e.filters,t.filters)||this.runQuery()})),this._subs.add((0,b.Ku)(this).subscribeToState((e,t)=>{e.value!==t.value&&this.runQuery()}));const t=this.getTagKey();this._subs.add((0,b.cR)(this).subscribeToState((e,n)=>F(function*(){(0,c.B)(e.filters.filter(e=>t===y.e4&&e.key!==t),n.filters.filter(e=>t===y.e4&&e.key!==t))||this.runQuery()}).call(this))),this._subs.add((0,b.iw)(this).subscribeToState((e,n)=>F(function*(){(0,c.B)(e.filters.filter(e=>t!==y.e4&&e.key!==t),n.filters.filter(e=>t!==y.e4&&e.key!==t))||this.runQuery()}).call(this)))}rebuildQuery(){var e;null===(e=this.getSceneQueryRunner())||void 0===e||e.setState({queries:[this.buildQuery()]})}runQuery(){this.rebuildQuery();const e=this.getSceneQueryRunner();null==e||e.runQueries()}getSceneQueryRunner(){if(this.state.$data){const e=o.jh.findDescendents(this.state.$data,o.dt);if(1!==e.length){const e=new Error("Unable to find query runner in value breakdown!");throw p.v.error(e,{msg:"LabelValuesBreakdownScene: Unable to find query runner in value breakdown!"}),e}return e[0]}p.v.warn("LabelValuesBreakdownScene: Query is attempting to execute, but query runner is undefined!")}removeValueLabelFromVariableInterpolation(){const e=this.getTagKey();let t,n;if(e===y.e4){const r=(0,b.iw)(this);n=y._Y,t=(0,h._q)(r.state.filters,[e])}else{const r=(0,b.cR)(this);n=y.MB,t=(0,h.VW)(r.state.filters,[e])}return{filterExpression:t,variableName:n}}getTagKey(){const e=(0,b.P4)(this);return String(e.state.value)}onValuesDataQueryChange(e){this.setEmptyStates(e),this.setErrorStates(e)}setErrorStates(e){var t,n;if((null==e||null===(t=e.data)||void 0===t?void 0:t.errors)&&(null===(n=e.data)||void 0===n?void 0:n.state)!==i.LoadingState.Done){var r;const t=this.state.errors;null==e||null===(r=e.data)||void 0===r||r.errors.forEach(e=>{const n=`${e.status}_${e.traceId}_${e.message}`;void 0===t[n]&&(t[n]=D(T({},e),{displayed:!1}))}),this.setState({errors:t}),this.showErrorToast(this.state.errors)}}setEmptyStates(e){var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done)if(e.data.series.length>0&&!(this.state.body instanceof P.U))this.setState({body:this.build()});else if(0===e.data.series.length){const e=o.jh.getAncestor(this,S.P);(0,m.mE)(e).length>1?this.setState({body:new j.W({clearCallback:()=>(0,m.rA)(this)})}):this.setState({body:new E.a({type:"fields"})})}}getActiveLayout(){const e=this.state.body;if(e instanceof P.U){const t=null==e?void 0:e.state.layouts.find(e=>e.isActive);if(t instanceof o.G1)return t}}activeLayoutContainsNoPanels(){const e=this.getActiveLayout();if(e){return o.jh.findDescendents(e,O.h).some(e=>{const t=e.state.body.state.children[0];return t instanceof o.vA||t instanceof o.dM})}return!1}build(){const e=(0,b.P4)(this).state,t=String(null==e?void 0:e.value),n=o.jh.getAncestor(this,x.O);let r=o.d0.timeseries();r=r.setCustomFieldConfig("stacking",{mode:l.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",l.DrawStyle.Bars).setShowMenuAlways(!0).setOverrides(g.jC).setMenu(new w.GD({})).setTitle(t);const{direction:i,sortBy:s}=(0,v.vs)("labels",f.DEFAULT_SORT_BY,"desc"),c=()=>{var e;return null!==(e=n.state.search.state.filter)&&void 0!==e?e:""};return new P.U({active:"grid",layouts:[new o.G1({children:[new o.dM({reactNode:a().createElement(x.O.LabelsMenu,{model:n})}),new C.s7({levelColor:!0,tagKey:this.getTagKey(),title:t,type:"label"}),new o.dM({reactNode:a().createElement(x.O.ValuesMenu,{model:n})}),new O.h({body:new o.gF({autoRows:"200px",children:[new o.vA({body:new o.dM({reactNode:a().createElement(l.LoadingPlaceholder,{text:"Loading..."})})})],isLazy:!0,templateColumns:d.di}),direction:i,getFilter:c,getLayoutChild:(0,u.Zp)(L.ee,l.DrawStyle.Bars,y.MB,o.jh.getAncestor(this,x.O).state.sort,t),sortBy:s})],direction:"column"}),new o.G1({children:[new o.dM({reactNode:a().createElement(x.O.LabelsMenu,{model:n})}),new C.s7({levelColor:!0,tagKey:this.getTagKey(),title:t,type:"label"}),new o.dM({reactNode:a().createElement(x.O.ValuesMenu,{model:n})}),new O.h({body:new o.gF({autoRows:"200px",children:[new o.vA({body:new o.dM({reactNode:a().createElement(l.LoadingPlaceholder,{text:"Loading..."})})})],templateColumns:"1fr"}),direction:i,getFilter:c,getLayoutChild:(0,u.Zp)(L.ee,l.DrawStyle.Bars,y.MB,o.jh.getAncestor(this,x.O).state.sort,t),sortBy:s})],direction:"column"})],options:[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]})}showErrorToast(e){const t=(0,s.getAppEvents)();let n=[];for(const t in e){const r=e[t];r.displayed||(n.push(r),r.displayed=!0)}n.length&&(this.activeLayoutContainsNoPanels()||t.publish({payload:null==n?void 0:n.map((e,t)=>this.renderError(t,e)),type:i.AppEvents.alertError.name}),this.setState({errors:e}))}renderError(e,t){return a().createElement("div",{key:e},t.status&&a().createElement(a().Fragment,null,a().createElement("strong",null,"Status"),": ",t.status," ",a().createElement("br",null)),t.message&&a().createElement(a().Fragment,null,a().createElement("strong",null,"Message"),": ",t.message," ",a().createElement("br",null)),t.traceId&&a().createElement(a().Fragment,null,a().createElement("strong",null,"TraceId"),": ",t.traceId))}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&t instanceof P.U&&a().createElement(P.U.Selector,{model:t}))}constructor(e){super(D(T({},e),{errors:{}})),this.addActivationHandler(this.onActivate.bind(this))}}k(N,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,l.useStyles2)(w.K_);return t?a().createElement("span",{className:n.panelWrapper},t&&a().createElement(t.Component,{model:t})):a().createElement(l.LoadingPlaceholder,{text:"Loading..."})})},6081:(e,t,n)=>{n.d(t,{U:()=>g});var r=n(5959),a=n.n(r),i=n(6089),s=n(6865),o=n(2007),l=n(9683),c=n(4509),u=n(4351);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class g extends s.Bs{constructor(e){var t;super(p(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){d(e,t,n[t])})}return e}({},e),{active:null!==(t=e.active)&&void 0!==t?t:"grid"})),d(this,"updateLayout",()=>{const e=(0,u.Dy)();e&&this.setState({active:"grid"===e||"rows"===e?e:"grid"})}),d(this,"onLayoutChange",e=>{(0,c.EE)(c.NO.service_details,c.ir.service_details.layout_type_changed,{layout:e,view:(0,l.FT)()}),(0,u.zu)(e),this.setState({active:e})}),d(this,"onActivate",()=>{this.updateLayout()}),this.addActivationHandler(this.onActivate.bind(this))}}d(g,"Selector",function({model:e}){const{active:t,options:n}=e.useState(),r=(0,o.useStyles2)(h);return a().createElement(o.Field,{className:r.field},a().createElement(o.RadioButtonGroup,{options:n,value:t,onChange:e.onLayoutChange}))}),d(g,"Component",({model:e})=>{const{active:t,layouts:n,options:r}=e.useState(),i=r.findIndex(e=>e.value===t);if(-1===i)return null;const s=n[i];return a().createElement(s.Component,{model:s})});const h=e=>({field:(0,i.css)({marginBottom:0})})},6779:(e,t,n)=>{n.d(t,{W:()=>p});var r,a,i,s=n(5959),o=n.n(s),l=n(6865),c=n(2007),u=n(7191),d=n(6830);class p extends l.Bs{}i=({model:e})=>{const{clearCallback:t}=e.useState();return o().createElement(u.R,null,o().createElement(c.Alert,{title:"",severity:"info"},"No labels match these filters."," ",o().createElement(c.Button,{className:d.ZI.button,onClick:()=>t()},"Clear filters")," "))},(a="Component")in(r=p)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},5865:(e,t,n)=>{n.d(t,{Zb:()=>y,s$:()=>w,s7:()=>m});var r=n(5959),a=n.n(r),i=n(7781),s=n(6865),o=n(2007),l=n(4509),c=n(42),u=n(5570),d=n(5953),p=n(4907),g=n(4351),h=n(5553),f=n(20),v=n(5700);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class m extends s.Bs{onActivate(){var e;const t=null!==(e=(0,g.ex)("collapsed",[v.Ci.collapsed,v.Ci.expanded]))&&void 0!==e?e:v.Ci.expanded,n=function(e,t){var n;const r=null!==(n=(0,g.ex)("collapsed",[v.Ci.collapsed,v.Ci.expanded]))&&void 0!==n?n:v.Ci.expanded,a=s.d0.timeseries().setTitle(e).setMenu(new v.GD({})).setCollapsible(!0).setCollapsed(r===v.Ci.collapsed).setCustomFieldConfig("stacking",{mode:o.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",o.DrawStyle.Bars).setShowMenuAlways(!0).setSeriesLimit(100);(null==t?void 0:t.levelColor)&&a.setOverrides(p.jC);return a.build()}(this.state.title,{levelColor:this.state.levelColor}),r=S(t);n.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(t)}),n.addActivationHandler(()=>{const e=s.jh.getData(this);this._subs.add(e.getResultsStream().subscribe(e=>{e.data.errors&&e.data.errors.length||(0,p.qi)(e,n)}))}),this.setState({body:new s.G1({children:[new s.vA({body:n})],height:r,key:w,maxHeight:r,minHeight:r,wrap:"nowrap"})}),this._subs.add(n.subscribeToState((e,t)=>{if(e.collapsed!==t.collapsed){y(s.jh.getAncestor(n,s.G1),e.collapsed?v.Ci.collapsed:v.Ci.expanded),(0,g.IW)("collapsed",e.collapsed?v.Ci.collapsed:v.Ci.expanded)}}))}initLegendOptions(e,t,n){e&&("label"===this.state.type?t===f.e4?(0,p.C6)(n,e,this):(0,p.dO)(t,n,e,this):(0,p.Nr)(t,n,e,this))}getQuerySubscription(e,t,n){return t.subscribeToState((t,r)=>{var a;(null===(a=t.data)||void 0===a?void 0:a.state)===i.LoadingState.Done&&("label"===this.state.type?e===f.e4?(0,p.C6)(n,t.data.series,this):(0,p.dO)(e,n,t.data.series,this):(0,p.Nr)(e,n,t.data.series,this))})}getFieldsVariableLegendSyncSubscription(e,t){return null==t?void 0:t.subscribeToState(()=>{var t,n;const r=null===(t=this.state.body)||void 0===t?void 0:t.state.children[0];if(!(r instanceof s.vA))throw new Error("Cannot find sceneFlexItem");const a=r.state.body;if(!(a instanceof s.Eb))throw new Error("ValueSummary - getFieldsVariableLegendSyncSubscription: Cannot find VizPanel");const i=null===(n=s.jh.getData(this).state.data)||void 0===n?void 0:n.series;i?(0,p.Nr)(e,a,i,this):d.v.warn("ValueSummary - getFieldsVariableLegendSyncSubscription: missing dataframe!")})}getLabelsVariableLegendSyncSubscription(e){const t=e===f.e4,n=t?(0,h.iw)(this):(0,h.cR)(this);return null==n?void 0:n.subscribeToState(()=>{var n,r;const a=null===(n=this.state.body)||void 0===n?void 0:n.state.children[0];if(!(a instanceof s.vA))throw new Error("Cannot find sceneFlexItem");const i=a.state.body;if(!(i instanceof s.Eb))throw new Error("ValueSummary - getLabelsVariableLegendSyncSubscription: Cannot find VizPanel");const o=null===(r=s.jh.getData(this).state.data)||void 0===r?void 0:r.series;o?t?(0,p.C6)(i,o,this):(0,p.dO)(e,i,o,this):d.v.warn("ValueSummary - getLabelsVariableLegendSyncSubscription: missing dataframe!")})}constructor(e){super(e),b(this,"extendTimeSeriesLegendBus",e=>{var t,n;const r=s.jh.getData(this),a=null===(t=r.state.data)||void 0===t?void 0:t.series,i=this.state.tagKey,o=null===(n=this.state.body)||void 0===n?void 0:n.state.children[0];if(!(o instanceof s.vA))throw new Error("Cannot find sceneFlexItem");const d=o.state.body;if(!(d instanceof s.Eb))throw new Error("Cannot find VizPanel");this.initLegendOptions(a,i,d),"label"===this.state.type?this._subs.add(this.getLabelsVariableLegendSyncSubscription(i)):(this._subs.add(this.getFieldsVariableLegendSyncSubscription(i,(0,h.ir)(this))),this._subs.add(this.getFieldsVariableLegendSyncSubscription(i,(0,h.oY)(this)))),this._subs.add(this.getQuerySubscription(i,r,d)),e.onToggleSeriesVisibility=(e,t)=>{let n;n="label"===this.state.type?i===f.e4?(0,u.PE)(e,this):(0,c.R7)(i,e,this):(0,c.zr)(i,e,this),(0,l.EE)(l.NO.service_details,l.ir.service_details.label_in_panel_summary_clicked,{action:n,label:e})}}),this.addActivationHandler(this.onActivate.bind(this))}}function y(e,t){const n=S(t);e.setState({height:n,maxHeight:n,minHeight:n})}function S(e){return e===v.Ci.collapsed?35:300}b(m,"Component",({model:e})=>{const{body:t}=e.useState();return t?a().createElement("div",null,a().createElement(t.Component,{model:t})):null});const w="value_summary_panel"},2524:(e,t,n)=>{n.d(t,{j4:()=>u,xX:()=>d});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(7839),l=n(7478),c=n(7191);const u=/maximum of series \(\d+\) reached for a single query/;function d(e){var t;const n=(0,s.useStyles2)(h),r=new Set,i=new Set,u=null===(t=e.errors)||void 0===t?void 0:t.filter(e=>{if(e.traceId&&i.add(e.traceId),e.message){if(r.has(e.message))return!1;r.add(e.message)}return!0}),d=e.isPartial?`Showing partial results for ${e.tagKey}`:`Error fetching results for ${e.tagKey}`;return a().createElement(c.R,null,a().createElement("div",{className:n.queryError},a().createElement(s.Alert,{title:d,severity:"error"},null==u?void 0:u.map((t,n)=>a().createElement(p,{traces:i,key:n,err:t,label:e.tagKey})),a().createElement("div",{className:n.buttonWrap},a().createElement(s.LinkButton,{variant:"secondary",href:(0,l.rs)(o.G3.fields,e.serviceScene)},"Return to all fields")))))}function p(e){const t=[...e.traces];return a().createElement("div",null,t.length&&a().createElement("div",null,1===t.length&&a().createElement(a().Fragment,null,a().createElement("strong",null,"TraceId"),": ",t[0]),t.length>1&&a().createElement(a().Fragment,null,a().createElement("strong",null,"TraceIds"),": ",t.join(", "))),a().createElement(g,{err:e.err,label:e.label}))}function g(e){var t;return(null===(t=e.err.message)||void 0===t?void 0:t.match(u))?a().createElement(a().Fragment,null,e.err.message&&a().createElement(a().Fragment,null,a().createElement("p",null,a().createElement("strong",null,"Max series limit exceeded"),": ",e.err.message,"."),a().createElement("p",null,"To increase this limit, adjust the"," ",a().createElement("a",{target:"_blank",href:"https://grafana.com/docs/loki/latest/configure/#limits_config",className:"external-link",rel:"noreferrer"},"max_query_series")," ","in your Loki configuration."),a().createElement("p",null,a().createElement("strong",null,"Tip:")," Reduce the time range, or add additional filters to reduce the number of unique values in the ",e.label," field."))):a().createElement(a().Fragment,null,e.err.message&&a().createElement("div",null,a().createElement("strong",null,"Message"),": ",e.err.message))}const h=e=>({buttonWrap:(0,i.css)({display:"flex",justifyContent:"flex-end"}),queryError:(0,i.css)({textAlign:"left"})})},9284:(e,t,n)=>{n.d(t,{D:()=>c});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const c=e=>{var{onChange:t,onClear:n,placeholder:r,suffix:i,value:c}=e,d=l(e,["onChange","onClear","placeholder","suffix","value"]);const p=(0,s.useStyles2)(u);return a().createElement(s.Input,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){o(e,t,n[t])})}return e}({value:c,onChange:t,suffix:a().createElement("span",{className:p.suffixWrapper},n&&c?a().createElement(s.IconButton,{"aria-label":"Clear search",tooltip:"Clear search",onClick:n,name:"times",className:p.clearIcon}):void 0,i&&i),prefix:a().createElement(s.Icon,{name:"search"}),placeholder:r},d))},u=e=>({clearIcon:(0,i.css)({cursor:"pointer"}),suffixWrapper:(0,i.css)({display:"inline-flex",gap:e.spacing(.5)})})},2606:(e,t,n)=>{n.d(t,{X:()=>A});var r=n(5959),a=n.n(r),i=n(6089),s=n(3241),o=n(7781),l=n(6865),c=n(2007),u=n(7839),d=n(376),p=n(6854),g=n(5953),h=n(7478),f=n(9683),v=n(5719),b=n(3571),m=n(5553),y=n(20),S=n(2085),w=n(9721),O=n(9405);function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var x=function(e){return e.ns="ns",e.us="µs",e.ms="ms",e.s="s",e.m="m",e.h="h",e}(x||{});var P=function(e){return e.B="B",e.KB="KB",e.MB="MB",e.GB="GB",e.TB="TB",e}(P||{});class j extends l.Bs{onActivate(){const e=(0,m.bY)((0,O.ts)(this.state.labelName,this.state.variableType),this).state.filters.filter(e=>e.key===this.state.labelName),t=e.find(e=>e.operator===p.w7.gte||e.operator===p.w7.gt),n=e.find(e=>e.operator===p.w7.lte||e.operator===p.w7.lt);let r={};if("duration"===this.state.fieldType||"bytes"===this.state.fieldType){if(t){const e=C((0,m.bu)(t).value,this.state.fieldType);e&&(r.gt=e.value,r.gtu=e.unit,r.gte=t.operator===p.w7.gte)}if(n){const e=C((0,m.bu)(n).value,this.state.fieldType);e&&(r.lt=e.value,r.ltu=e.unit,r.lte=n.operator===p.w7.lte)}}else{if(t){const e=(0,m.bu)(t).value;r.gt=Number(e),r.gtu="",r.gte=t.operator===p.w7.gte}if(n){const e=(0,m.bu)(n).value;r.lt=Number(e),r.ltu="",r.lte=n.operator===p.w7.lte}}0!==Object.keys(r).length&&(r.hasExistingFilter=!0),this.setState(r)}onSubmit(){this.state.gt?(0,O.vn)(this.state.labelName,this.state.gt.toString()+this.state.gtu,this.state.gte?p.w7.gte:p.w7.gt,this,this.state.variableType):(0,O.u7)(this.state.labelName,this,this.state.gte?p.w7.gte:p.w7.gt,this.state.variableType),this.state.lt?(0,O.vn)(this.state.labelName,this.state.lt.toString()+this.state.ltu,this.state.lte?p.w7.lte:p.w7.lt,this,this.state.variableType):(0,O.u7)(this.state.labelName,this,this.state.lte?p.w7.lte:p.w7.lt,this.state.variableType);l.jh.getAncestor(this,A).togglePopover()}constructor(e){let t;const n=e.fieldType;if("bytes"===n)t={gtu:"B",ltu:"B"};else if("duration"===n)t={gtu:"s",ltu:"s"};else{if("float"!==n&&"int"!==n)throw new Error(`field type incorrectly defined: ${n}`);t={gtu:"",ltu:""}}super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){E(e,t,n[t])})}return e}({},e,t)),E(this,"onInputKeydown",e=>{const t=void 0===this.state.gt&&void 0===this.state.lt;"Enter"!==e.key||t||this.onSubmit()}),this.addActivationHandler(this.onActivate.bind(this))}}function C(e,t){if("duration"===t){const t=Object.values(x).find(t=>{const n=t.length;return e.slice(-1*n)===t});if(t){const n=Number(e.replace(t,""));if(!isNaN(n))return{unit:t,value:n}}}if("bytes"===t){const t=Object.values(P).sort((e,t)=>t.length-e.length).find(t=>{const n=t.length;return e.slice(-1*n)===t});if(t){const n=Number(e.replace(t,""));if(!isNaN(n))return{unit:t,value:n}}}}function L(e){if("duration"===e){return Object.keys(x).map(e=>({label:e,text:e,value:x[e]}))}if("bytes"===e){return Object.keys(P).map(e=>({label:e,text:e,value:P[e]}))}const t=new Error(`invalid field type: ${e}`);throw g.v.error(t,{msg:"getUnitOptions, invalid field type"}),t}E(j,"Component",({model:e})=>{const t=(0,c.useStyles2)(_),{fieldType:n,gt:r,gte:s,gtu:o,hasExistingFilter:u,labelName:d,lt:p,lte:g,ltu:h}=e.useState(),f="float"!==n&&"int"!==n&&n!==d?`(${n})`:void 0,v=l.jh.getAncestor(e,A),m=void 0===r&&void 0===p;return a().createElement(c.ClickOutsideWrapper,{useCapture:!0,onClick:()=>v.togglePopover()},a().createElement(c.Stack,{direction:"column",gap:0,role:"tooltip"},a().createElement("div",{className:t.card.body},a().createElement("div",{className:t.card.title},d," ",f),a().createElement("div",{className:t.card.fieldWrap},a().createElement(c.FieldSet,{className:t.card.fieldset},a().createElement(c.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputGreaterThanInclusive,horizontal:!0,className:(0,i.cx)(t.card.field,t.card.inclusiveField)},a().createElement(c.Select,{className:t.card.inclusiveInput,menuShouldPortal:!1,value:void 0!==s?s.toString():"false",options:[{label:"Greater than",value:"false"},{label:"Greater than or equal",value:"true"}],onChange:t=>e.setState({gte:"true"===t.value})})),a().createElement(c.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputGreaterThan,horizontal:!0,className:t.card.field},a().createElement(c.Input,{onKeyDownCapture:e.onInputKeydown,autoFocus:!0,onChange:t=>{e.setState({gt:""!==t.currentTarget.value?Number(t.currentTarget.value):void 0})},className:t.card.numberInput,value:r,type:"number"})),"float"!==n&&"int"!==n&&a().createElement(c.Label,null,a().createElement(c.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputGreaterThanUnit,horizontal:!0,className:t.card.field,label:a().createElement("span",{className:t.card.unitFieldLabel},"Unit")},a().createElement(c.Select,{onChange:t=>{e.setState({gtu:t.value})},menuShouldPortal:!1,options:L(n),className:t.card.selectInput,value:o})))),a().createElement(c.FieldSet,{className:t.card.fieldset},a().createElement(c.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputLessThanInclusive,horizontal:!0,className:(0,i.cx)(t.card.field,t.card.inclusiveField)},a().createElement(c.Select,{className:t.card.inclusiveInput,menuShouldPortal:!1,value:void 0!==g?g.toString():"false",options:[{label:"Less than",value:"false"},{label:"Less than or equal",value:"true"}],onChange:t=>e.setState({lte:"true"===t.value})})),a().createElement(c.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputLessThan,horizontal:!0,className:t.card.field},a().createElement(c.Input,{onKeyDownCapture:e.onInputKeydown,onChange:t=>e.setState({lt:""!==t.currentTarget.value?Number(t.currentTarget.value):void 0}),className:t.card.numberInput,value:p,type:"number"})),"float"!==n&&"int"!==n&&a().createElement(c.Label,null,a().createElement(c.Field,{"data-testid":b.b.breakdowns.common.filterNumericPopover.inputLessThanUnit,horizontal:!0,className:t.card.field,label:a().createElement("span",{className:t.card.unitFieldLabel},"Unit")},a().createElement(c.Select,{onChange:t=>{e.setState({ltu:t.value})},menuShouldPortal:!1,options:L(n),className:t.card.selectInput,value:h}))))),a().createElement("div",{className:t.card.buttons},u&&a().createElement(c.Button,{"data-testid":b.b.breakdowns.common.filterNumericPopover.removeButton,disabled:!u,onClick:()=>{e.setState({gt:void 0,lt:void 0}),e.onSubmit()},size:"sm",variant:"destructive",fill:"outline"},"Remove"),a().createElement(c.Button,{"data-testid":b.b.breakdowns.common.filterNumericPopover.submitButton,disabled:m,onClick:()=>e.onSubmit(),size:"sm",variant:"primary",fill:"outline",type:"submit"},"Add"),a().createElement(c.Button,{"data-testid":b.b.breakdowns.common.filterNumericPopover.cancelButton,onClick:()=>v.togglePopover(),size:"sm",variant:"secondary",fill:"outline"},"Cancel")))))});const _=e=>({card:{body:(0,i.css)({padding:e.spacing(2)}),buttons:(0,i.css)({display:"flex",flexWrap:"wrap",gap:e.spacing(1.5),justifyContent:"flex-end",marginTop:e.spacing(1)}),field:(0,i.css)({alignItems:"center",display:"flex",marginBottom:e.spacing(1)}),fieldset:(0,i.css)({alignItems:"center",display:"flex",justifyContent:"space-between",marginBottom:0,width:"100%"}),fieldWrap:(0,i.css)({display:"flex",flexDirection:"column",paddingBottom:0,paddingTop:e.spacing(2)}),inclusiveField:(0,i.css)({marginRight:e.spacing(1)}),inclusiveInput:(0,i.css)({minWidth:"185px"}),numberFieldLabel:(0,i.css)({width:"100px"}),numberInput:(0,i.css)({width:"75px"}),p:(0,i.css)({maxWidth:300}),selectInput:(0,i.css)({minWidth:"65px"}),switchFieldLabel:(0,i.css)({marginLeft:e.spacing(2),marginRight:e.spacing(1)}),title:(0,i.css)({}),unitFieldLabel:(0,i.css)({marginLeft:e.spacing(2),marginRight:e.spacing(1.5)})}});function F(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){F(e,t,n[t])})}return e}function T(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const D="Include",N="Exclude",$="Add to filter";class A extends l.Bs{onChange(e){const t=this.getVariable(),n=t.state.name,r=this.getExistingFilter(t),a=(0,m.z2)(n,r);(null==r?void 0:r.operator)===p.w7.NotEqual&&a.value===y.ZO&&e.value===D?this.clearFilter(n):e.value===D?this.onClickExcludeEmpty(n):e.value===N?this.onClickIncludeEmpty(n):e.value===$&&this.onClickNumericFilter(n),this.setState({selectedValue:e})}getExistingFilter(e){let{labelName:t}=(0,f.MC)(this);if(this.state.labelName!==t)return null==e?void 0:e.state.filters.find(e=>e.key===this.state.labelName)}onActivate(){var e,t,n,r;const a=l.jh.getAncestor(this,w.Mn);(null===(t=a.state.$data)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&(null===(r=a.state.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)!==o.LoadingState.Error||this.calculateSparsity(),this._subs.add(l.jh.getData(this).subscribeToState(e=>{var t,n,r,i,s,l;(null===(t=e.data)||void 0===t?void 0:t.state)===o.LoadingState.Done&&((null===(r=a.state.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)!==o.LoadingState.Done&&(null===(s=a.state.$data)||void 0===s||null===(i=s.state.data)||void 0===i?void 0:i.state)!==o.LoadingState.Error||this.calculateSparsity(),this._subs.add(null===(l=a.state.$data)||void 0===l?void 0:l.subscribeToState(e=>{var t,n,r,i;(null===(n=a.state.$data)||void 0===n||null===(t=n.state.data)||void 0===t?void 0:t.state)!==o.LoadingState.Done&&(null===(i=a.state.$data)||void 0===i||null===(r=i.state.data)||void 0===r?void 0:r.state)!==o.LoadingState.Error||this.calculateSparsity()})))}))}togglePopover(){this.setState({showPopover:!this.state.showPopover})}calculateSparsity(){var e;const t=l.jh.getAncestor(this,w.Mn),n=(0,w.tn)(null===(e=t.state.$data)||void 0===e?void 0:e.state.data),r=null==n?void 0:n.fields.find(e=>"labels"===e.name),a=l.jh.getData(this),i=(0,v.UX)(a,e=>e instanceof l.dt,l.dt);if(i){const e=i.state.queries[0];(null==e?void 0:e.expr.includes("avg_over_time"))&&this.setState({hasNumericFilters:!0})}if(!r||!n)return void this.setState({hasSparseFilters:!1});const s=this.getVariable(),o=r.values.reduce((e,t)=>((null==t?void 0:t[this.state.labelName])&&e++,e),0),c=l.jh.getAncestor(this,l.Eb);if(void 0!==o&&n.length>0){let e=this.state.description;void 0===this.state.description&&(e=(0,d.kz)(this,this.state.labelName).description),c.setState({description:e})}else c.setState({description:void 0});const u=this.getExistingFilter(s),p=u&&s.state.name===y.mB?(0,m.bu)(u):void 0;o<n.length||(null==p?void 0:p.value)===y.ZO?this.setState({hasSparseFilters:!0}):this.setState({hasSparseFilters:!1})}getVariable(){return this.state.fieldType===u._J.field?(0,m.ir)(this):this.state.labelName===y.e4?(0,m.iw)(this):(0,m.cR)(this)}constructor(e){super(T(k({},e),{showPopover:!1})),F(this,"onClickNumericFilter",e=>{const t=(0,w.rD)(this),n=(0,d.ph)(this.state.labelName,t);if(!n||"string"===n||"boolean"===n){const e=new Error(`Incorrect field type: ${n}`);throw g.v.error(e,{msg:`onClickNumericFilter invalid field type ${n}`}),e}this.setState({popover:new j({fieldType:n,labelName:this.state.labelName,variableType:e})}),this.togglePopover()}),F(this,"getViewValuesLink",()=>{const e=l.jh.getAncestor(this,w.Mn);return(0,h.FB)(this.state.fieldType,this.state.labelName,e)}),F(this,"onClickExcludeEmpty",e=>{(0,O.Qt)(this.state.labelName,y.ZO,"exclude",this,e)}),F(this,"onClickIncludeEmpty",e=>{(0,O.Qt)(this.state.labelName,y.ZO,"include",this,e)}),F(this,"clearFilter",e=>{(0,O.Qt)(this.state.labelName,y.ZO,"clear",this,e)}),F(this,"clearFilters",e=>{(0,O.hi)(this.state.labelName,this,e),this.state.labelName===y.e4&&(0,S.dm)(this)}),this.addActivationHandler(this.onActivate.bind(this))}}function B(e){const t=(0,c.useStyles2)(M);return a().createElement("span",{className:t.description},e.selected&&a().createElement("span",{className:t.selected}),e.text)}F(A,"Component",({model:e})=>{var t;const{fieldType:n,hasNumericFilters:i,hasSparseFilters:o,hideValueDrilldown:d,labelName:g,popover:h,selectedValue:f,showPopover:v}=e.useState(),S=e.getVariable(),w=S.useState().name,O=e.getExistingFilter(S),E=(0,m.z2)(w,O),x=(0,c.useStyles2)(I),P=(0,r.useRef)(null),j=n===u._J.label&&S.state.name===y.mB&&0===S.state.filters.filter(e=>e.key!==g&&e.operator===p.w7.Equal).length,C=(null==O?void 0:O.operator)===p.w7.NotEqual&&E.value===y.ZO,L=!!O;var _;const F=null!==(_=null==f?void 0:f.value)&&void 0!==_?_:C?D:i?$:D,A=!!(null==O?void 0:O.operator)&&[p.w7.gte,p.w7.gt,p.w7.lte,p.w7.lt].includes(O.operator),M=F===$||A,R=F===D&&!M,V={component:()=>a().createElement(B,{selected:R,text:`Include all log lines with ${g}`}),value:D},z={component:()=>a().createElement(B,{selected:!1,text:`Exclude all log lines with ${g}`}),value:N},G={component:()=>a().createElement(B,{selected:M,text:`Add an expression, i.e. ${g} > 30`}),value:$},U=[];i&&U.push(G),o&&(A||U.push(V),U.push(z));const W=C?V:i?G:V,K=l.jh.getAncestor(e,l.Eb),Q=l.jh.getData(K),{data:H}=Q.useState();var q;const J=(null!==(q=null==H?void 0:H.series.length)&&void 0!==q?q:0)>0;var Y;const X=(null!==(Y=null==H||null===(t=H.errors)||void 0===t?void 0:t.length)&&void 0!==Y?Y:0)>0,Z=!J&&X;var ee;return a().createElement(a().Fragment,null,L&&a().createElement(c.IconButton,{disabled:j,name:"filter",tooltip:`Clear ${g} filters`,onClick:()=>e.clearFilters(w)}),(i||o)&&a().createElement(a().Fragment,null,a().createElement(c.ButtonGroup,{"data-testid":b.b.breakdowns.common.filterButtonGroup},a().createElement(c.Button,{"data-testid":b.b.breakdowns.common.filterButton,ref:P,className:x.button,onClick:()=>e.onChange(null!=f?f:W),size:"sm",fill:"outline",variant:"secondary"},null!==(ee=null==f?void 0:f.value)&&void 0!==ee?ee:W.value),a().createElement(c.ButtonSelect,{"data-testid":b.b.breakdowns.common.filterSelect,className:x.buttonSelect,variant:"default",options:U,onChange:t=>{e.onChange(t)}}))),!0!==d&&a().createElement(c.LinkButton,{disabled:Z,title:`View breakdown of values for ${g}`,variant:"primary",fill:"outline",size:"sm","aria-label":`Select ${g}`,href:e.getViewValuesLink()},"Select"),h&&a().createElement(c.PopoverController,{content:a().createElement(h.Component,{model:h})},(e,t,n)=>{const r={onBlur:t,onFocus:e};return a().createElement(a().Fragment,null,P.current&&a().createElement(a().Fragment,null,a().createElement(c.Popover,k(T(k({},n,s.rest),{show:v,wrapperClassName:x.popover,referenceElement:P.current,renderArrow:!0}),r))))}))});const M=e=>({description:(0,i.css)({fontSize:e.typography.pxToRem(12),textAlign:"left"}),selected:(0,i.css)({"&:before":{backgroundColor:e.colors.warning.main,content:'""',height:"calc(100% - 8px)",left:0,position:"absolute",top:"4px",width:"2px"},label:"selectable-value-selected"})}),I=e=>({button:(0,i.css)({borderRight:"1px solid red"}),buttonSelect:(0,i.css)({border:`1px solid ${e.colors.border.strong}`,borderBottomLeftRadius:0,borderLeft:"none",borderTopLeftRadius:0,height:"24px",padding:1}),description:(0,i.css)({fontSize:e.typography.pxToRem(12),textAlign:"left"}),popover:(0,i.css)({background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3})})},1049:(e,t,n)=>{n.d(t,{ee:()=>b,gf:()=>g,wd:()=>h});var r=n(5959),a=n.n(r),i=n(7781),s=n(6865),o=n(2007),l=n(2601),c=n(3571),u=n(5570),d=n(4351);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends i.BusEventBase{constructor(e,t,n){super(),p(this,"target",void 0),p(this,"sortBy",void 0),p(this,"direction",void 0),this.target=e,this.sortBy=t,this.direction=n}}p(g,"type","sort-criteria-changed");class h extends s.Bs{constructor(e){const{direction:t,sortBy:n}=(0,d.vs)(e.target,l.DEFAULT_SORT_BY,"desc");super({direction:t,sortBy:n,target:e.target}),p(this,"sortingOptions",[{label:"",options:[{description:"Smart ordering of graphs based on the most significant spikes in the data",label:"Most relevant",value:"changepoint"},{description:"Order by the amount of outlying values in the data",label:"Outlying values",value:"outliers"},{description:"Sort graphs by deviation from the average value",label:"Widest spread",value:i.ReducerID.stdDev},{description:"Alphabetical order",label:"Name",value:"alphabetical"},{description:"Sort graphs by total number of logs",label:"Count",value:i.ReducerID.sum},{description:"Sort graphs by the highest values (max)",label:"Highest spike",value:i.ReducerID.max},{description:"Sort graphs by the smallest values (min)",label:"Lowest dip",value:i.ReducerID.min}]},{label:"Percentiles",options:[...i.fieldReducers.selectOptions([],v).options]}]),p(this,"onCriteriaChange",e=>{e.value&&(this.setState({sortBy:e.value}),(0,d.fq)(this.state.target,e.value,this.state.direction),this.publishEvent(new g(this.state.target,e.value,this.state.direction),!0))}),p(this,"onDirectionChange",e=>{e.value&&(this.setState({direction:e.value}),(0,d.fq)(this.state.target,this.state.sortBy,e.value),this.publishEvent(new g(this.state.target,this.state.sortBy,e.value),!0))})}}p(h,"Component",({model:e})=>{const{direction:t,sortBy:n}=e.useState(),r=e.sortingOptions.find(e=>e.options.find(e=>e.value===n)),i=null==r?void 0:r.options.find(e=>e.value===n);return a().createElement(a().Fragment,null,a().createElement(o.InlineField,{label:"Sort by",htmlFor:"sort-by-criteria",tooltip:"Calculate a derived quantity from the values in your time series and sort by this criteria. Defaults to standard deviation."},a().createElement(o.Select,{"data-testid":c.b.breakdowns.common.sortByFunction,value:i,width:20,isSearchable:!0,options:e.sortingOptions,placeholder:"Choose criteria",onChange:e.onCriteriaChange,inputId:"sort-by-criteria"})),a().createElement(o.InlineField,null,a().createElement(o.Select,{"data-testid":c.b.breakdowns.common.sortByDirection,onChange:e.onDirectionChange,"aria-label":"Sort direction",placeholder:"",value:t,options:[{label:"Asc",value:"asc"},{label:"Desc",value:"desc"}]})))});const f=["p10","p25","p75","p90","p99"];function v(e){return e.id>="p1"&&e.id<="p99"&&f.includes(e.id)}function b(e){var t;return null!==(t=(0,u.H7)(e))&&void 0!==t?t:"No labels"}},5659:(e,t,n)=>{n.d(t,{O:()=>o});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007);function o({blockingMessage:e,children:t,isLoading:n}){const r=(0,s.useStyles2)(l);return n&&!e&&(e="Loading..."),n?a().createElement(s.LoadingPlaceholder,{className:r.statusMessage,text:e}):e?a().createElement("div",{className:r.statusMessage},e):a().createElement(a().Fragment,null,t)}function l(e){return{statusMessage:(0,i.css)({fontStyle:"italic",marginTop:e.spacing(7),textAlign:"center"})}}},4059:(e,t,n)=>{n.d(t,{l:()=>r});const r=20},1111:(e,t,n)=>{n.d(t,{iU:()=>$,Z:()=>_,mF:()=>D,jf:()=>T,EK:()=>L,fN:()=>k,vP:()=>N,sP:()=>B,eJ:()=>F,FN:()=>A,y:()=>M});var r=n(5959),a=n(7781),i=n(8531),s=n(6865),o=n(4509),l=n(376),c=n(9721);var u=n(4247),d=n(8502),p=n(4586),g=n(5553);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){h(e,t,n[t])})}return e}function v(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function b(e,t){const n=(0,c.tn)(e),r=n?(0,a.sortDataFrame)(n,1,t.state.sortOrder===a.LogsSortOrder.Descending):void 0,s=null==r?void 0:r.fields.find(e=>e.type===a.FieldType.time),o=null==r?void 0:r.fields.find(e=>e.type===a.FieldType.other&&(0,l.gE)(e.name)),h=null==r?void 0:r.fields.find(e=>e.type===a.FieldType.other&&(0,l.at)(e.name)),b=(0,i.getTemplateSrv)(),m=b.replace.bind(b),y=(0,a.getTimeZone)();if(!r)return{data:void 0,rawFrame:void 0};const S=(0,g.U2)(t).state.filters.length>0;var w;const O=null!==(w=null==r?void 0:r.fields.filter(e=>e.config.links).map(e=>v(f({},e),{getLinks:(0,a.getLinksSupplier)(r,e,{},m)})))&&void 0!==w?w:[],E=v(f({},e),{series:[r].map(e=>v(f({},e),{fields:e.fields.map((e,n)=>(0,l.Z6)(e.name)?v(f({},e),{values:e.values.map((e,n)=>{var r,a,i;let l;try{l=JSON.parse(e)}catch(t){l=e}const c=null==o||null===(r=o.values)||void 0===r?void 0:r[n],g=null==h||null===(a=h.values)||void 0===a?void 0:a[n];let f={},v={};if(!S&&c&&g){Object.keys(c).forEach(e=>{d.rm.includes(e)||(g[e]===u.H.StructuredMetadata?f[e]=c[e]:g[e]===u.H.Indexed&&(v[e]=c[e]))})}const b={[_]:l,[L]:(0,p.yC)(null==s||null===(i=s.values)||void 0===i?void 0:i[n],y)};if(t.state.hasLabels&&Object.keys(v).length>0&&(b[$]=v),t.state.hasMetadata&&Object.keys(f).length>0&&(b[T]=f),void 0!==O){let e=function(e,t){let n={};return e.forEach(e=>{var r;const a=null==e||null===(r=e.getLinks)||void 0===r?void 0:r.call(e,{valueRowIndex:t});null==a||a.forEach(e=>{if(e.href){let r=e.title,a=e.origin.name,i=1;if(n[r]&&(r=r+" "+(i++).toString()),e.origin.values[t]){const t={href:e.href,name:a};n[r]=JSON.stringify(t)}}})}),n}(O,n);Object.keys(e).length&&(b[D]=e)}return b}).filter(e=>e)}):e)}))});return{data:E,rawFrame:r}}var m=n(8428),y=n(5719),S=n(5548),w=n(5700),O=n(6779),E=n(5953),x=n(4351);function P(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const C=(0,r.lazy)(()=>n.e(905).then(n.bind(n,7905))),L="Time",_="Line",F="Metadata",k="Labels",T="__Metadata",D="__Links",N="Links",$="__Labels",A="root";class B extends s.Bs{getUrlState(){return{sortOrder:JSON.stringify(this.state.sortOrder),wrapLogMessage:JSON.stringify(this.state.wrapLogMessage)}}updateFromUrl(e){try{let t={};if("string"==typeof e.sortOrder&&e.sortOrder){const n=(0,m.FH)(JSON.parse(e.sortOrder));n&&(t.sortOrder=n)}if("string"==typeof e.wrapLogMessage&&e.wrapLogMessage){const n=!!JSON.parse(e.wrapLogMessage);n&&(t.wrapLogMessage=n)}Object.keys(t).length&&this.setState(t)}catch(e){E.v.error(e,{msg:"JSONLogsScene: updateFromUrl unexpected error"})}}onActivate(){var e,t,n,r,i,u,d,p;this.setStateFromUrl();const g=s.jh.getAncestor(this,c.Mn);this.setState({emptyScene:new O.W({clearCallback:()=>(0,S.rA)(this)}),menu:new w.GD({investigationOptions:{getLabelName:()=>`Logs: ${(0,y.Mq)(g)}`,type:"logs"}})});const h=s.jh.getData(this);(null===(e=h.state.data)||void 0===e?void 0:e.state)===a.LoadingState.Done&&this.updateJSONDataFrame(h.state.data),this._subs.add(h.subscribeToState(e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===a.LoadingState.Done&&this.updateJSONDataFrame(e.data)})),(0,l.Ak)(this);const f=(0,c.UO)(null===(n=g.state)||void 0===n||null===(t=n.$detectedFieldsData)||void 0===t?void 0:t.state);var v,b;if(f&&f.length)void 0===!g.state.fieldsCount||g.state.fieldsCount!==(null==f?void 0:f.length)?null===(b=g.state)||void 0===b||null===(v=b.$detectedFieldsData)||void 0===v||v.runQueries():this.setVizFlags(f);else if(void 0===(null===(u=g.state)||void 0===u||null===(i=u.$detectedFieldsData)||void 0===i||null===(r=i.state.data)||void 0===r?void 0:r.state)){var m,E;null===(E=g.state)||void 0===E||null===(m=E.$detectedFieldsData)||void 0===m||m.runQueries()}this._subs.add(null===(p=g.state)||void 0===p||null===(d=p.$detectedFieldsData)||void 0===d?void 0:d.subscribeToState(e=>{var t,n;(null===(t=e.data)||void 0===t?void 0:t.state)===a.LoadingState.Done&&(null===(n=e.data)||void 0===n?void 0:n.series.length)&&this.setVizFlags(e.data.series[0])})),this._subs.add(this.subscribeToState((e,t)=>{!h.state.data||e.hasMetadata===t.hasMetadata&&e.hasLabels===t.hasLabels||this.updateJSONDataFrame(h.state.data)})),(0,o.EE)(o.NO.service_details,o.ir.service_details.visualization_init,{viz:"json"},!0)}updateJSONDataFrame(e){this.setState(b(e,this))}setStateFromUrl(){const e=new URLSearchParams(i.locationService.getLocation().search);this.updateFromUrl({sortOrder:e.get("sortOrder"),wrapLogMessage:e.get("wrapLogMessage")})}setVizFlags(e){var t,n,r;(null===(t=(0,l.$1)(e))||void 0===t?void 0:t.values.some(e=>"json"===e||"mixed"===e))?this.setState({hasJSONFields:!0,JSONFiltersSupported:null!==(r=null===(n=(0,l.VN)(e))||void 0===n?void 0:n.values.some(e=>void 0!==e))&&void 0!==r?r:null}):this.setState({hasJSONFields:!1})}constructor(e){super(j(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){P(e,t,n[t])})}return e}({},e),{hasHighlight:(0,x.MZ)(),hasLabels:(0,x.vC)(),hasMetadata:(0,x.Hn)(),sortOrder:(0,x.YM)("sortOrder",a.LogsSortOrder.Descending),wrapLogMessage:(0,x.IL)("wrapLogMessage",!0),JSONFiltersSupported:null})),P(this,"_urlSync",new s.So(this,{keys:["sortOrder","wrapLogMessage"]})),P(this,"handleSortChange",e=>{if(e===this.state.sortOrder)return;(0,x.YK)("sortOrder",e);const t=s.jh.getData(this),n=t instanceof s.dt?t:s.jh.findDescendents(t,s.dt)[0];n&&n.runQueries(),this.setState({sortOrder:e})}),this.addActivationHandler(this.onActivate.bind(this))}}function M(e,t=":"){return e[0]!==L?e[0]+t:e[0]}P(B,"Component",C)},72:(e,t,n)=>{n.d(t,{_:()=>k});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(3571),l=n(8428);function c(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}let p;const g=e=>{var{onChange:t,onClear:n,placeholder:i,regex:o,suffix:c,value:g,width:v}=e,b=d(e,["onChange","onClear","placeholder","regex","suffix","value","width"]);const m=(0,s.useStyles2)(f),[y,S]=(0,r.useState)(!1),[w,O]=(0,r.useState)(""),E=(0,r.useCallback)(e=>{if(!e||!o)return O(""),void S(!1);if(void 0!==p)try{null==p||p.compile(e),S(!1),O("")}catch(e){const t=(0,l.DU)(e);S(!0),t&&O(t)}else h().then(()=>E(e))},[o]);return(0,r.useEffect)(()=>{E(g)},[E,g]),a().createElement(s.Tooltip,{placement:"auto-start",show:!!w&&y,content:w},a().createElement(s.Input,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){u(e,t,n[t])})}return e}({invalid:y,"aria-invalid":y,rows:2,width:v,onFocusCapture:e=>{b.onFocus&&b.onFocus(e)},value:g,onChange:t,suffix:a().createElement("span",{className:m.suffixWrapper},n&&g?a().createElement(s.IconButton,{"aria-label":"Clear line filter",tooltip:"Clear line filter",onClick:n,name:"times",className:m.clearIcon}):void 0,c&&c),prefix:a().createElement(s.Icon,{name:"search"}),placeholder:i},b)))},h=()=>{return(e=function*(){p=null,p=(yield n.e(470).then(n.bind(n,4470))).RE2JS},function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){c(i,r,a,s,o,"next",e)}function o(e){c(i,r,a,s,o,"throw",e)}s(void 0)})})();var e},f=e=>({clearIcon:(0,i.css)({cursor:"pointer"}),suffixWrapper:(0,i.css)({display:"inline-flex",gap:e.spacing(.5)})});var v=n(7781),b=n(6854);function m(e){return{boxShadow:`0 0 0 2px ${e.colors.background.canvas}, 0 0 0px 4px ${e.colors.primary.main}`,outline:"2px dotted transparent",outlineOffset:"2px",transitionDuration:"0.2s",transitionProperty:"outline, outline-offset, box-shadow",transitionTimingFunction:"cubic-bezier(0.19, 1, 0.22, 1)"}}function y(e,t){return{[t.transitions.handleMotion("no-preference","reduce")]:{transitionDuration:"0.2s",transitionProperty:"opacity",transitionTimingFunction:"cubic-bezier(0.4, 0, 0.2, 1)"},borderRadius:t.shape.radius.default,content:'""',height:`${e}px`,opacity:"0",position:"absolute",width:`${e}px`,zIndex:"-1"}}function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){S(e,t,n[t])})}return e}function O(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const E=e=>{const t=(0,s.useTheme2)(),n=e.caseSensitive?t.colors.text.maxContrast:t.colors.text.disabled,r=x(t),o=(e.caseSensitive?"Disable":"Enable")+" case match";return a().createElement(s.Tooltip,{content:o},a().createElement("button",{onClick:()=>e.onCaseSensitiveToggle(e.caseSensitive?b.ld.caseInsensitive:b.ld.caseSensitive),className:(0,i.cx)(r.button,e.caseSensitive?r.active:null),"aria-label":o},a().createElement("svg",{fill:n,width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},a().createElement("text",{fontSize:"13",width:"16",height:"16",x:"50%",y:"50%",dominantBaseline:"central",textAnchor:"middle"},"Aa"))))},x=(e,t="secondary")=>{const n=16+e.spacing.gridSize;return{active:(0,i.css)({"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:v.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1},"&:hover":{"&:before":{backgroundColor:"none",opacity:0}}}),button:(0,i.css)({"&:before":O(w({},y(n,e)),{position:"absolute"}),"&:focus, &:focus-visible":m(e),"&:focus:not(:focus-visible)":{boxShadow:"none",outline:"none"},"&:hover":{"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:v.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1}},alignItems:"center",background:"transparent",border:"none",boxShadow:"none",color:e.colors.text.primary,display:"inline-flex",justifyContent:"center",margin:`0 ${e.spacing.x0_5} 0 ${e.spacing.x0_5}`,padding:0,position:"relative",zIndex:0})}};function P(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function j(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){P(e,t,n[t])})}return e}function C(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const L=e=>{const t=(0,s.useTheme2)(),n=e.regex?t.colors.text.maxContrast:t.colors.text.disabled,r=_(t),o=(e.regex?"Disable":"Enable")+" regex";return a().createElement(s.Tooltip,{content:o},a().createElement("button",{onClick:()=>e.onRegexToggle(e.regex?"match":"regex"),className:(0,i.cx)(r.button,e.regex?r.active:null),"aria-label":o},a().createElement("svg",{fill:n,width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},a().createElement("text",{fontSize:"13",width:"16",height:"16",x:"50%",y:"50%",dominantBaseline:"central",textAnchor:"middle"},".*"))))},_=(e,t="secondary")=>{const n=16+e.spacing.gridSize;return{active:(0,i.css)({"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:v.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1},"&:hover":{"&:before":{backgroundColor:"none",opacity:0}}}),button:(0,i.css)({"&:before":C(j({},y(n,e)),{position:"absolute"}),"&:focus, &:focus-visible":m(e),"&:focus:not(:focus-visible)":{boxShadow:"none",outline:"none"},"&:hover":{"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:v.colorManipulator.alpha(e.colors.text.primary,.12),opacity:1}},alignItems:"center",background:"transparent",border:"none",boxShadow:"none",color:e.colors.text.primary,display:"inline-flex",justifyContent:"center",margin:`0 ${e.spacing.x0_5} 0 ${e.spacing.x0_5}`,padding:0,position:"relative",zIndex:0})}},F=30;function k({caseSensitive:e,exclusive:t,focus:n,handleEnter:l,lineFilter:c,onCaseSensitiveToggle:u,onClearLineFilter:d,onInputChange:p,onRegexToggle:h,onSubmitLineFilter:f,regex:v,setExclusive:b,setFocus:m,type:y}){const S=(0,s.useStyles2)(e=>T(e,y)),[w,O]=(0,r.useState)(F);function x(e){var t;const n=Math.max(null!==(t=null==e?void 0:e.length)&&void 0!==t?t:0,F);O(n+9)}return(0,r.useEffect)(()=>{x(c)},[c,n]),a().createElement("div",{className:S.wrapper},!f&&a().createElement(s.Select,{prefix:null,className:S.select,value:t?"exclusive":"inclusive",options:[{label:"Exclude",value:"exclusive"},{label:"Include",value:"inclusive"}],onChange:()=>b(!t)}),a().createElement(s.Field,{className:S.field},a().createElement(g,{regex:v,width:n?w:void 0,onFocus:()=>m(!0),"data-testid":o.b.exploreServiceDetails.searchLogs,value:null!=c?c:"",className:(0,i.cx)(f?S.inputNoBorderRight:void 0,S.input),onChange:p,suffix:a().createElement("span",{className:`${S.suffix} input-suffix`},a().createElement(E,{caseSensitive:e,onCaseSensitiveToggle:u}),a().createElement(L,{regex:v,onRegexToggle:h})),prefix:null,placeholder:"Search in log lines",onClear:d,onKeyUp:e=>{l(e,c),x(c)}})),f&&a().createElement("span",{className:S.buttonWrap},a().createElement(s.Button,{onClick:()=>{b(!1),f()},className:S.includeButton,variant:"secondary",fill:"outline",disabled:!c},"Include"),a().createElement(s.Button,{onClick:()=>{b(!0),f()},className:S.excludeButton,variant:"secondary",fill:"outline",disabled:!c},"Exclude")))}const T=(e,t)=>({buttonWrap:(0,i.css)({display:"flex",justifyContent:"center"}),excludeButton:(0,i.css)({"&[disabled]":{borderLeft:"none"},borderLeft:"none",borderRadius:`0 ${e.shape.radius.default} ${e.shape.radius.default} 0`}),exclusiveBtn:(0,i.css)({marginRight:"1rem"}),field:(0,i.css)({flex:"0 1 auto",label:"field",marginBottom:0}),includeButton:(0,i.css)({"&[disabled]":{borderRight:"none"},borderLeft:"none",borderRadius:0,borderRight:"none"}),input:(0,i.css)({input:{borderBottomLeftRadius:0,borderTopLeftRadius:0,fontFamily:"monospace",fontSize:e.typography.bodySmall.fontSize,width:"100%"},label:"line-filter-input-wrapper",maxWidth:"editor"===t?"calc(100vw - 198px)":"calc(100vw - 288px)",minWidth:"200px"}),inputNoBorderRight:(0,i.css)({input:{borderBottomRightRadius:0,borderTopRightRadius:0}}),removeBtn:(0,i.css)({borderBottomLeftRadius:0,borderTopLeftRadius:0}),select:(0,i.css)({borderBottomRightRadius:"0",borderRight:"none",borderTopRightRadius:"0",height:"auto",label:"line-filter-exclusion",marginLeft:0,maxWidth:"95px",minHeight:"30px",minWidth:"95px",outline:"none",paddingLeft:0}),submit:(0,i.css)({borderBottomLeftRadius:0,borderTopLeftRadius:0}),suffix:(0,i.css)({display:"inline-flex",gap:e.spacing(.5)}),wrapper:(0,i.css)({display:"flex",width:"100%"})})},6189:(e,t,n)=>{n.d(t,{V:()=>u});var r=n(5959),a=n.n(r),i=n(6089),s=n(7781),o=n(9814),l=n(2007),c=n(8544);const u=({disabledLineState:e,lineState:t,onLineStateClick:n,onScrollToBottomClick:i,onScrollToTopClick:u,onSortOrderChange:p,onToggleHighlightClick:g,onToggleLabelsClick:h,onToggleStructuredMetadataClick:f,onWrapLogMessageClick:v,showHighlight:b,showLabels:m,showMetadata:y,sortOrder:S,wrapLogMessage:w})=>{const O=(0,l.useStyles2)(d),E=(0,r.useCallback)(()=>{p(S===s.LogsSortOrder.Ascending?s.LogsSortOrder.Descending:s.LogsSortOrder.Ascending)},[p,S]);return a().createElement("div",{className:O.navContainer},i&&a().createElement(l.IconButton,{name:"arrow-down",className:O.controlButton,variant:"secondary",onClick:i,tooltip:"Scroll to bottom",size:"lg"}),a().createElement(l.IconButton,{name:S===s.LogsSortOrder.Descending?"sort-amount-up":"sort-amount-down",className:O.controlButton,onClick:E,tooltip:S===s.LogsSortOrder.Descending?"Newest logs first":"Oldest logs first",size:"lg"}),void 0!==w&&v&&a().createElement(l.IconButton,{name:"wrap-text",className:w?O.controlButtonActive:O.controlButton,"aria-pressed":w,onClick:()=>v(!w),tooltip:w?(0,o.t)("logs.logs-controls.unwrap-lines","Unwrap lines"):(0,o.t)("logs.logs-controls.wrap-lines","Wrap lines"),size:"lg"}),void 0!==y&&f&&a().createElement(l.IconButton,{name:"document-info","aria-pressed":y,className:y?O.controlButtonActive:O.controlButton,onClick:()=>f(!y),tooltip:y?"Hide structured metadata":"Show structured metadata",size:"lg"}),void 0!==m&&h&&a().createElement(l.IconButton,{name:"tag-alt","aria-pressed":m,className:m?O.controlButtonActive:O.controlButton,onClick:()=>h(!m),tooltip:m?"Hide Labels":"Show labels",size:"lg"}),void 0!==b&&g&&a().createElement(l.IconButton,{name:"brackets-curly","aria-pressed":b,className:b?O.controlButtonActive:O.controlButton,onClick:()=>g(!b),tooltip:b?"Disable highlighting":"Enable highlighting",size:"lg"}),n&&t&&a().createElement(l.IconButton,{disabled:e,name:t===c.Wg.text?"tag-alt":"text-fields",className:O.controlButton,onClick:n,tooltip:t===c.Wg.text?"Show labels":"Show log text",size:"lg"}),u&&a().createElement(l.IconButton,{name:"arrow-up","data-testid":"scrollToTop",className:O.scrollToTopButton,variant:"secondary",onClick:u,tooltip:"Scroll to top",size:"lg"}))},d=e=>({controlButton:(0,i.css)({color:e.colors.text.secondary,height:e.spacing(2),margin:0}),controlButtonActive:(0,i.css)({"&:after":{backgroundImage:e.colors.gradients.brandHorizontal,borderRadius:e.shape.radius.default,bottom:e.spacing(-1),content:'" "',display:"block",height:2,opacity:1,position:"absolute",width:"95%"},color:e.colors.text.secondary,height:e.spacing(2),margin:0}),divider:(0,i.css)({borderTop:`solid 1px ${e.colors.border.medium}`,height:1,marginBottom:e.spacing(-1.75),marginTop:e.spacing(-.25)}),navContainer:(0,i.css)({borderLeft:`solid 1px ${e.colors.border.medium}`,display:"flex",flexDirection:"column",gap:e.spacing(3),justifyContent:"flex-start",maxHeight:"100%",overflow:"hidden",paddingLeft:e.spacing(1),paddingTop:e.spacing(.75),width:e.spacing(4)}),scrollToTopButton:(0,i.css)({color:e.colors.text.secondary,height:e.spacing(2),margin:0,marginTop:"auto"})})},2649:(e,t,n)=>{n.d(t,{P:()=>S,Z:()=>y});var r=n(5959),a=n.n(r),i=n(6089),s=n(7781),o=n(8531),l=n(6865),c=n(2007),u=n(5953),d=n(8428),p=n(9641),g=n(5607),h=n(8996),f=n(4509),v=n(4907),b=n(4351);function m(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class y extends l.Bs{constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){m(e,t,n[t])})}return e}({},e)),m(this,"handleWrapLinesChange",e=>{this.getLogsPanelScene().setState({prettifyLogMessage:e,wrapLogMessage:e}),(0,b.YK)("wrapLogMessage",e),(0,b.YK)("prettifyLogMessage",e),this.getLogsListScene().setLogsVizOption({prettifyLogMessage:e,wrapLogMessage:e})}),m(this,"onChangeLogsSortOrder",e=>{this.getLogsPanelScene().setState({sortOrder:e}),(0,b.YK)("sortOrder",e),this.getLogsListScene().setLogsVizOption({sortOrder:e})}),m(this,"getLogsListScene",()=>l.jh.getAncestor(this,g.i)),m(this,"getLogsPanelScene",()=>l.jh.getAncestor(this,h.o)),m(this,"clearDisplayedFields",()=>{this.getLogsListScene().clearDisplayedFields(),(0,f.EE)(f.NO.service_details,f.ir.service_details.logs_clear_displayed_fields)})}}function S(){const e=o.locationService.getLocation(),t=new URLSearchParams(e.search).get("sortOrder");try{if("string"==typeof t){const e=(0,d.FH)(JSON.parse(t));if(e)return e}}catch(e){u.v.error(e,{msg:"LogOptionsScene(getLogsPanelSortOrderFromURL): unable to parse sortOrder"})}return!1}m(y,"Component",function({model:e}){const{onChangeVisualizationType:t,visualizationType:n}=e.useState(),{sortOrder:r,wrapLogMessage:i}=e.getLogsPanelScene().useState(),{displayedFields:o}=e.getLogsListScene().useState(),l=(0,c.useStyles2)(w),u=null!=i&&i;return a().createElement("div",{className:l.container},o.length>0&&a().createElement(c.Tooltip,{content:`Clear displayed fields: ${o.join(", ")}`},a().createElement(c.Button,{size:"sm",variant:"secondary",fill:"outline",onClick:e.clearDisplayedFields},"Show original log line")),!v.CT&&a().createElement(a().Fragment,null,a().createElement(c.InlineField,{className:l.buttonGroupWrapper,transparent:!0},a().createElement(c.RadioButtonGroup,{size:"sm",options:[{description:"Show results newest to oldest",label:"Newest first",value:s.LogsSortOrder.Descending},{description:"Show results oldest to newest",label:"Oldest first",value:s.LogsSortOrder.Ascending}],value:r,onChange:e.onChangeLogsSortOrder})),a().createElement(c.InlineField,{className:l.buttonGroupWrapper,transparent:!0},a().createElement(c.RadioButtonGroup,{size:"sm",value:u,onChange:e.handleWrapLinesChange,options:[{description:"Enable wrapping of long log lines",label:"Wrap",value:!0},{description:"Disable wrapping of long log lines",label:"No wrap",value:!1}]}))),a().createElement(p.C,{vizType:n,onChange:t}))});const w=e=>({buttonGroupWrapper:(0,i.css)({alignItems:"center",margin:0}),container:(0,i.css)({alignItems:"center",display:"flex",gap:e.spacing(1),marginTop:e.spacing(.5)})})},5607:(e,t,n)=>{n.d(t,{i:()=>K});var r=n(5959),a=n.n(r),i=n(6089),s=n(8531),o=n(6865),l=n(4509),c=n(5953),u=n(8428),d=n(8544),p=n(1220),g=n(1111),h=n(6177),f=n.n(h),v=n(6854),b=n(7478),m=n(4351),y=n(5553),S=n(72);function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class O extends o.Bs{clearVariable(){(0,y.Rr)(this).updateFilters([],{skipPublish:!0}),this.setState({lineFilter:""})}getOperator(){if(this.state.regex&&this.state.exclusive)return v.cK.negativeRegex;if(this.state.regex&&!this.state.exclusive)return v.cK.regex;if(!this.state.regex&&this.state.exclusive)return v.cK.negativeMatch;if(!this.state.regex&&!this.state.exclusive)return v.cK.match;throw new Error("getOperator: failed to determine operation")}getFilterKey(){return this.state.caseSensitive?v.ld.caseSensitive:v.ld.caseInsensitive}getFilter(){return(0,y.Rr)(this).state.filters[0]}updateFilter(e,t=!0){this.updateInputState(e),t?this.updateVariableDebounced(e):this.updateVariable(e)}updateInputState(e){this.setState({lineFilter:e})}constructor(e){var t,n,r,a;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){w(e,t,n[t])})}return e}({caseSensitive:null!==(t=null==e?void 0:e.caseSensitive)&&void 0!==t?t:(0,m.hp)(!1),exclusive:null!==(n=null==e?void 0:e.exclusive)&&void 0!==n?n:(0,m.Zs)(!1),lineFilter:null!==(r=null==e?void 0:e.lineFilter)&&void 0!==r?r:"",regex:null!==(a=null==e?void 0:e.regex)&&void 0!==a?a:(0,m.og)(!1)},e)),w(this,"onActivate",()=>{const e=this.getFilter();if(e)return this.setState({caseSensitive:e.key===v.ld.caseSensitive,exclusive:e.operator===v.cK.negativeMatch||e.operator===v.cK.negativeRegex,lineFilter:e.value,regex:e.operator===v.cK.regex||e.operator===v.cK.negativeRegex}),()=>{this.clearFilter()}}),w(this,"clearFilter",()=>{this.updateVariableDebounced.cancel(),this.updateFilter("",!1)}),w(this,"onToggleExclusive",e=>{(0,m.Bq)(e),this.setState({exclusive:e}),this.updateFilter(this.state.lineFilter,!1)}),w(this,"onSubmitLineFilter",()=>{(0,b.bN)(),this.updateFilter(this.state.lineFilter,!1),this.updateVariableDebounced.flush();const e=(0,y.Gk)(this),t=e.state.filters,n=this.getFilter();e.updateFilters([...t,n]),this.clearVariable()}),w(this,"handleChange",e=>{this.updateInputState(e.target.value)}),w(this,"handleEnter",e=>{"Enter"===e.key&&this.state.lineFilter&&this.onSubmitLineFilter()}),w(this,"onCaseSensitiveToggle",e=>{const t=e===v.ld.caseSensitive;this.setState({caseSensitive:t}),(0,m.Xo)(t),this.updateFilter(this.state.lineFilter,!1)}),w(this,"onRegexToggle",e=>{const t="regex"===e;this.setState({regex:t}),(0,m.GL)(t),this.updateFilter(this.state.lineFilter,!1)}),w(this,"updateVariableDebounced",f()(e=>{this.updateVariable(e)},1e3)),w(this,"updateVariable",e=>{this.updateVariableDebounced.flush();const t=(0,y.Rr)(this),n=(0,y.Gk)(this),r={key:this.getFilterKey(),keyLabel:n.state.filters.length.toString(),operator:this.getOperator(),value:null!=e?e:""};t.updateFilters([r]),(0,l.EE)(l.NO.service_details,l.ir.service_details.search_string_in_logs_changed,{caseSensitive:r.key,containsLevel:null==e?void 0:e.toLowerCase().includes("level"),operator:r.operator,searchQueryLength:null==e?void 0:e.length})}),this.addActivationHandler(this.onActivate)}}w(O,"Component",function({model:e}){const{caseSensitive:t,exclusive:n,lineFilter:a,regex:i}=e.useState(),[s,o]=(0,r.useState)(!1);return(0,S._)({caseSensitive:t,exclusive:n,focus:s,handleEnter:e.handleEnter,lineFilter:a,onCaseSensitiveToggle:e.onCaseSensitiveToggle,onClearLineFilter:e.clearFilter,onInputChange:e.handleChange,onRegexToggle:e.onRegexToggle,onSubmitLineFilter:e.onSubmitLineFilter,regex:i,setExclusive:e.onToggleExclusive,setFocus:o,type:"editor",updateFilter:e.updateFilter})});var E=n(8996),x=n(7781),P=n(2007),j=n(1532),C=n(376),L=n(5548),_=n(5700),F=n(5091),k=n(9641),T=n(9405),D=n(6779),N=n(6189),$=n(9721),A=n(2165),B=n(4907);function M(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function I(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const R=(0,r.lazy)(()=>Promise.all([n.e(864),n.e(675)]).then(n.bind(n,8675)));let V=F.O;class z extends o.Bs{setStateFromUrl(){const e=new URLSearchParams(s.locationService.getLocation().search);this.updateFromUrl({sortOrder:e.get("sortOrder")})}getUrlState(){return{sortOrder:JSON.stringify(this.state.sortOrder)}}updateFromUrl(e){try{if("string"==typeof e.sortOrder&&e.sortOrder){const t=(0,u.FH)(JSON.parse(e.sortOrder));t&&this.setState({sortOrder:t})}}catch(e){c.v.error(e,{msg:"LogsTableScene: updateFromUrl unexpected error"})}}onActivate(){this.setState({emptyScene:new D.W({clearCallback:()=>(0,L.rA)(this)}),menu:new _.GD({addInvestigationsLink:!1})}),this.onActivateSyncDisplayedFieldsWithUrlColumns(),this.setStateFromUrl(),this._subs.add(s.locationService.getHistory().listen(()=>{this.subscribeFromUrl()})),(0,l.EE)(l.NO.service_details,l.ir.service_details.visualization_init,{viz:"table"},!0)}getParentScene(){return o.jh.getAncestor(this,K)}constructor(e){super(I(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){M(e,t,n[t])})}return e}({},e),{sortOrder:(0,m.YM)("sortOrder",x.LogsSortOrder.Descending),isDisabledLineState:!1})),M(this,"_urlSync",new o.So(this,{keys:["sortOrder","urlColumns"]})),M(this,"subscribeFromUrl",()=>{const e=new URLSearchParams(s.locationService.getLocation().search);let t=[];try{var n;t=(0,u.aJ)(JSON.parse(decodeURIComponent(null!==(n=e.get("urlColumns"))&&void 0!==n?n:""))),t.includes(A.eB)||t.includes(A.wu)?this.setState({isDisabledLineState:!0}):this.setState({isDisabledLineState:!1})}catch(e){console.error("Error parsing urlColumns:",e)}}),M(this,"onActivateSyncDisplayedFieldsWithUrlColumns",()=>{const e=new URLSearchParams(s.locationService.getLocation().search);let t=[];try{var n;t=(0,u.aJ)(JSON.parse(decodeURIComponent(null!==(n=e.get("urlColumns"))&&void 0!==n?n:""))),(t.includes(A.eB)||t.includes(A.wu))&&this.setState({isDisabledLineState:!0})}catch(e){console.error(e)}const r=this.getParentScene();V=t&&this.urlHasDefaultUrlColumns(t)?this.updateDefaultUrlColumns(t):V,V.length,r.setState({urlColumns:Array.from(new Set([...V,...r.state.displayedFields]))})}),M(this,"updateDisplayedFields",e=>{const t=this.getParentScene();V=this.updateDefaultUrlColumns(e),V.includes(A.eB)||V.includes(A.wu)?this.setState({isDisabledLineState:!0}):this.setState({isDisabledLineState:!1});const n=Array.from(new Set([...e||[]])).filter(e=>!V.includes(e));t.setState({displayedFields:n}),(0,m.ZF)(this,t.state.displayedFields)}),M(this,"urlHasDefaultUrlColumns",e=>V.some(t=>e.includes(t))),M(this,"updateDefaultUrlColumns",e=>(V=V.reduce((t,n)=>{if(e.includes(n)){t[e.indexOf(n)]=n}return t},[]),V)),M(this,"handleSortChange",e=>{if(e===this.state.sortOrder)return;(0,m.YK)("sortOrder",e);const t=o.jh.getData(this),n=t instanceof o.dt?t:o.jh.findDescendents(t,o.dt)[0];n&&n.runQueries(),this.setState({sortOrder:e})}),M(this,"onLineStateClick",()=>{const e=o.jh.getAncestor(this,K),{tableLogLineState:t}=e.state;e.setState({tableLogLineState:t===d.Wg.text?d.Wg.labels:d.Wg.text})}),this.addActivationHandler(this.onActivate.bind(this))}}M(z,"Component",({model:e})=>{const t=(0,P.useStyles2)(G),n=o.jh.getAncestor(e,K),{data:i}=o.jh.getData(e).useState(),{selectedLine:s,tableLogLineState:l,urlColumns:c,visualizationType:u}=n.useState(),{emptyScene:p,menu:g,sortOrder:h}=e.useState(),f=o.jh.getTimeRange(e),{value:v}=f.useState(),b=(0,$.tn)(i),m=(0,r.useRef)(null);return a().createElement("div",{className:t.panelWrapper,ref:m},a().createElement(P.PanelChrome,{loadingState:null==i?void 0:i.state,title:"Logs",menu:g?a().createElement(g.Component,{model:g}):void 0,showMenuAlways:!0,actions:a().createElement(k.C,{vizType:u,onChange:n.setVisualizationType})},a().createElement("div",{className:t.container},B.CT&&b&&b.length>0&&a().createElement(N.V,{sortOrder:h,onSortOrderChange:e.handleSortChange,onLineStateClick:e.onLineStateClick,lineState:null!=l?l:d.Wg.labels,disabledLineState:!e.state.isDisabledLineState}),b&&a().createElement(R,{panelWrap:m,addFilter:t=>{const r=(0,C.OE)(b,t.key,e);(0,T.XI)(t,n,r)},timeRange:v,selectedLine:s,urlColumns:null!=c?c:[],setUrlColumns:t=>{(0,j.n)(t,n.state.urlColumns)||(n.setState({urlColumns:t}),e.updateDisplayedFields(t))},dataFrame:b,clearSelectedLine:()=>{n.state.selectedLine&&n.clearSelectedLine()},setUrlTableBodyState:e=>{n.setState({tableLogLineState:e})},urlTableBodyState:l,logsSortOrder:h}),p&&b&&0===b.length&&a().createElement(D.W.Component,{model:p}))))});const G=e=>({container:(0,i.css)({display:"flex",flexDirection:"row-reverse",justifyContent:"space-between"}),panelWrapper:(0,i.css)({height:"100%",label:"panel-wrapper-table",width:"100%"})});function U(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function W(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class K extends o.Bs{getUrlState(){var e;const t=null!==(e=this.state.urlColumns)&&void 0!==e?e:[],n=this.state.selectedLine,r=this.state.visualizationType;var a,i;const s=null!==(i=null!==(a=this.state.displayedFields)&&void 0!==a?a:(0,m.N$)(this))&&void 0!==i?i:[];return{displayedFields:JSON.stringify(s),selectedLine:JSON.stringify(n),tableLogLineState:JSON.stringify(this.state.tableLogLineState),urlColumns:JSON.stringify(t),visualizationType:JSON.stringify(r)}}updateFromUrl(e){const t={};try{if("string"==typeof e.urlColumns){const n=(0,u.aJ)(JSON.parse(e.urlColumns));n!==this.state.urlColumns&&(t.urlColumns=n)}if("string"==typeof e.selectedLine){const n=(0,u.lb)(JSON.parse(e.selectedLine));if(n){const e=n;e!==this.state.selectedLine&&(t.selectedLine=e)}}if("string"==typeof e.visualizationType){const n=(0,u.v_)(JSON.parse(e.visualizationType));n&&n!==this.state.visualizationType&&(t.visualizationType=n)}if("string"==typeof e.displayedFields){const n=(0,u.aJ)(JSON.parse(e.displayedFields));n&&n.length&&(t.displayedFields=n)}if("string"==typeof e.tableLogLineState){const n=JSON.parse(e.tableLogLineState);n!==d.Wg.labels&&n!==d.Wg.text||(t.tableLogLineState=n)}}catch(e){c.v.error(e,{msg:"LogsListScene: updateFromUrl unexpected error"})}Object.keys(t).length&&this.setState(t)}clearSelectedLine(){this.setState({selectedLine:void 0})}onActivate(){const e=new URLSearchParams(s.locationService.getLocation().search);this.setStateFromUrl(e),this.state.panel||this.updateLogsPanel(),this._subs.add(this.subscribeToState((e,t)=>{if(e.visualizationType!==t.visualizationType){this.updateLogsPanel();const e=o.jh.findObject(this,e=>e instanceof p.g);null==e||e.forceRender()}}))}setStateFromUrl(e){const t=e.get("selectedLine"),n=e.get("urlColumns"),r=e.get("visualizationType");var a;const i=null!==(a=e.get("displayedFields"))&&void 0!==a?a:JSON.stringify((0,m.N$)(this)),s=e.get("tableLogLineState");this.updateFromUrl({displayedFields:i,selectedLine:t,tableLogLineState:s,urlColumns:n,visualizationType:r})}getVizPanel(){this.logsPanelScene=new E.o({});const e="logs"===this.state.visualizationType?[new o.G1({children:[new o.vA({body:new O({lineFilter:this.state.lineFilter}),xSizing:"fill"})]}),new o.vA({body:this.logsPanelScene,height:"calc(100vh - 220px)"})]:"json"===this.state.visualizationType?[new o.vA({body:new O({lineFilter:this.state.lineFilter}),xSizing:"fill"}),new o.vA({body:new g.sP({}),height:"calc(100vh - 220px)"})]:[new o.vA({body:new O({lineFilter:this.state.lineFilter}),xSizing:"fill"}),new o.vA({body:new z({}),height:"calc(100vh - 220px)"})];return new o.G1({children:e,direction:"column"})}constructor(e){super(W(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){U(e,t,n[t])})}return e}({},e),{displayedFields:[],visualizationType:(0,m.k5)()})),U(this,"_urlSync",new o.So(this,{keys:["urlColumns","selectedLine","visualizationType","displayedFields","tableLogLineState"]})),U(this,"logsPanelScene",void 0),U(this,"clearDisplayedFields",()=>{this.setState({displayedFields:[]}),this.logsPanelScene&&this.logsPanelScene.clearDisplayedFields()}),U(this,"setLogsVizOption",(e={})=>{this.logsPanelScene&&this.logsPanelScene.setLogsVizOption(e)}),U(this,"updateLogsPanel",()=>{if(this.setState({panel:this.getVizPanel()}),this.state.panel){const e=o.jh.findDescendents(this.state.panel,O);if(e.length){const t=e[0];this._subs.add(t.subscribeToState((e,t)=>{e.lineFilter!==t.lineFilter&&this.setState({lineFilter:e.lineFilter})}))}}}),U(this,"setVisualizationType",e=>{this.setState({visualizationType:e}),(0,l.EE)(l.NO.service_details,l.ir.service_details.logs_visualization_toggle,{visualisationType:e}),(0,m.o5)(e)}),this.addActivationHandler(this.onActivate.bind(this))}}U(K,"Component",({model:e})=>{const{panel:t}=e.useState();if(t)return a().createElement("div",{className:Q.panelWrapper},a().createElement(t.Component,{model:t}))});const Q={panelWrapper:(0,i.css)({'section > div[class$="panel-content"]':(0,i.css)({contain:"none",overflow:"auto"})})}},8996:(e,t,n)=>{n.d(t,{o:()=>A});var r=n(5959),a=n.n(r),i=n(7781),s=n(8531),o=n(6865),l=n(1625),c=n(2007),u=n(4509),d=n(376),p=n(6854),g=n(5953),h=n(8428),f=n(4351),v=n(5553),b=n(20),m=n(8714),y=n(5700),S=n(9405);const w=({onClick:e})=>{const[t,n]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e;return t&&(e=setTimeout(()=>{n(!1)},2e3)),()=>{clearTimeout(e)}},[t]);const i=(0,r.useCallback)((t,r)=>{e(t,r),n(!0)},[e]);return a().createElement(c.IconButton,{"aria-label":t?"Copied":"Copy link to log line",tooltip:t?"Copied":"Copy link to log line",tooltipPlacement:"top",variant:t?"primary":"secondary",size:"md",name:t?"check":"share-alt",onClick:i})};var O=n(2649),E=n(5607),x=n(7191);const P=({clearFilters:e,error:t})=>a().createElement(x.R,null,a().createElement("div",null,a().createElement("p",null,t),e&&a().createElement(c.Button,{variant:"secondary",onClick:e},"Clear filters")));var j=n(71),C=n(9721),L=n(8839),_=n(2165),F=n(4907),k=n(5719),T=n(5002),D=n(5548);function N(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){N(e,t,n[t])})}return e}class A extends o.Bs{setStateFromUrl(){const e=new URLSearchParams(s.locationService.getLocation().search);this.updateFromUrl({prettifyLogMessage:e.get("prettifyLogMessage"),sortOrder:e.get("sortOrder"),wrapLogMessage:e.get("wrapLogMessage")})}getUrlState(){return{prettifyLogMessage:JSON.stringify(this.state.prettifyLogMessage),sortOrder:JSON.stringify(this.state.sortOrder),wrapLogMessage:JSON.stringify(this.state.wrapLogMessage)}}updateFromUrl(e){const t={};try{if("string"==typeof e.sortOrder&&e.sortOrder){const n=(0,h.FH)(JSON.parse(e.sortOrder));n&&(t.sortOrder=n)}if("string"==typeof e.prettifyLogMessage&&e.prettifyLogMessage){const n=JSON.parse(e.prettifyLogMessage);"boolean"==typeof n&&(t.prettifyLogMessage=n)}if("string"==typeof e.wrapLogMessage&&e.wrapLogMessage){const n=JSON.parse(e.wrapLogMessage);"boolean"==typeof n&&(t.wrapLogMessage=n,F.CT||(t.prettifyLogMessage=n))}}catch(e){g.v.error(e,{msg:"LogsPanelScene: updateFromUrl unexpected error"})}Object.keys(t).length&&(this.setState($({},t)),this.setLogsVizOption($({},t)))}onActivate(){this.setStateFromUrl(),(0,f.sB)(this)&&this.setState({dedupStrategy:(0,f.sB)(this)}),this.state.body||this.setState({body:this.getLogsPanel()});const e=o.jh.getAncestor(this,C.Mn);this._subs.add(e.subscribeToState((e,t)=>{var n,r,a,s,o,l;(null===(r=e.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)===i.LoadingState.Error?this.handleLogsError(null===(l=e.$data)||void 0===l?void 0:l.state.data):(null===(s=e.$data)||void 0===s||null===(a=s.state.data)||void 0===a?void 0:a.state)===i.LoadingState.Done&&(0,_.y2)(null===(o=e.$data)||void 0===o?void 0:o.state.data.series)?this.handleNoData():this.state.error&&this.clearLogsError();e.logsCount!==t.logsCount&&(this.state.body?this.state.body.setState({title:this.getTitle(e.logsCount)}):this.setState({body:this.getLogsPanel()}))})),(0,u.EE)(u.NO.service_details,u.ir.service_details.visualization_init,{viz:"logs"},!0)}handleLogsError(e){var t;const n=(null===(t=e.errors)||void 0===t?void 0:t.length)?e.errors[0]:e.error,r=null==n?void 0:n.message;var a,i;r&&g.v.error(new Error("Logs Panel error"),{msg:r,status:null!==(a=n.statusText)&&void 0!==a?a:"N/A",type:null!==(i=n.type)&&void 0!==i?i:"N/A"});let s="Unexpected error response. Please review your filters or try a different time range.";(null==r?void 0:r.includes("parse error"))?s="Logs could not be retrieved due to invalid filter parameters. Please review your filters and try again.":(null==r?void 0:r.includes("response larger than the max message size"))&&(s="The response is too large to process. Try narrowing your search or using filters to reduce the data size."),this.showLogsError(s)}handleNoData(){this.state.canClearFilters?this.showLogsError("No logs match your search. Please review your filters or try a different time range."):this.showLogsError("No logs match your search. Please try a different time range.")}showLogsError(e){var t;const n=null!==(t=this.state.logsVolumeCollapsedByError)&&void 0!==t?t:!(0,f.Rf)("collapsed"),r=o.jh.getAncestor(this,m.P),a=(0,D.mE)(r);if(this.setState({canClearFilters:a.length>0,error:e,logsVolumeCollapsedByError:n}),n){var i;null===(i=o.jh.findByKeyAndType(this,j.b,j._).state.panel)||void 0===i||i.setState({collapsed:!0})}}clearLogsError(){if(this.state.logsVolumeCollapsedByError){var e;null===(e=o.jh.findByKeyAndType(this,j.b,j._).state.panel)||void 0===e||e.setState({collapsed:!1})}this.setState({error:void 0,logsVolumeCollapsedByError:void 0})}setLogsVizOption(e={}){if(this.state.body){if("sortOrder"in e&&e.sortOrder!==this.state.body.state.options.sortOrder){const e=o.jh.getData(this),t=e instanceof o.dt?e:o.jh.findDescendents(e,o.dt)[0];t&&t.runQueries()}this.state.body.onOptionsChange(e)}}getParentScene(){return o.jh.getAncestor(this,E.i)}getTitle(e){var t;const n=(0,i.getValueFormat)("short"),r=void 0!==e?n(e,0):void 0;return void 0!==r?`Logs (${r.text}${null===(t=r.suffix)||void 0===t?void 0:t.trim()})`:"Logs"}handleLabelFilter(e,t,n,r){const a=(0,d.OE)(n,e,this);(0,S.Qt)(e,t,r,this,a),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_detail_filter_applied,{action:r,filterType:a,key:e})}constructor(e){super($({dedupStrategy:l.fY.none,error:void 0,prettifyLogMessage:(0,f.IL)("prettifyLogMessage",!1),sortOrder:(0,f.YM)("sortOrder",l.uH.Descending),wrapLogMessage:(0,f.IL)("wrapLogMessage",!1),series:[]},e)),N(this,"_urlSync",new o.So(this,{keys:["sortOrder","wrapLogMessage","prettifyLogMessage"]})),N(this,"setDisplayedFields",e=>{this.setLogsVizOption({displayedFields:e}),(0,f.ZF)(this,e);this.getParentScene().setState({displayedFields:e})}),N(this,"onClickShowField",e=>{const t=this.getParentScene();if(-1===t.state.displayedFields.indexOf(e)&&this.state.body){const n=[...t.state.displayedFields,e];this.setLogsVizOption({displayedFields:n}),t.setState({displayedFields:n}),(0,f.ZF)(this,n),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_toggle_displayed_field)}}),N(this,"onClickHideField",e=>{const t=this.getParentScene();if(t.state.displayedFields.indexOf(e)>=0&&this.state.body){const n=t.state.displayedFields.filter(t=>e!==t);this.setLogsVizOption({displayedFields:n}),t.setState({displayedFields:n}),(0,f.ZF)(this,n),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_toggle_displayed_field)}}),N(this,"clearDisplayedFields",()=>{this.state.body&&(this.setLogsVizOption({displayedFields:[]}),(0,f.ZF)(this,[]))}),N(this,"getLogsPanel",()=>{const e=this.getParentScene(),t=e.state.visualizationType,n=o.jh.getAncestor(this,C.Mn),r=o.d0.logs().setTitle(this.getTitle(n.state.logsCount)).setOption("onClickFilterLabel",this.handleLabelFilterClick).setOption("onClickFilterOutLabel",this.handleLabelFilterOutClick).setOption("isFilterLabelActive",this.handleIsFilterLabelActive).setOption("onClickFilterString",this.handleFilterStringClick).setOption("onClickFilterOutString",this.handleFilterOutStringClick).setOption("onClickShowField",this.onClickShowField).setOption("onClickHideField",this.onClickHideField).setOption("displayedFields",e.state.displayedFields).setMenu(new y.GD({investigationOptions:{getLabelName:()=>`Logs: ${(0,k.Mq)(n)}`,type:"logs"}})).setOption("showLogContextToggle",!0).setShowMenuAlways(!0).setOption("enableInfiniteScrolling",!0).setOption("onNewLogsReceived",this.updateVisibleRange).setOption("logRowMenuIconsAfter",[a().createElement(w,{onClick:this.handleShareLogLineClick,key:0})]).setHeaderActions(new O.Z({onChangeVisualizationType:e.setVisualizationType,visualizationType:t})).setOption("sortOrder",this.state.sortOrder).setOption("wrapLogMessage",this.state.wrapLogMessage).setOption("prettifyLogMessage",this.state.prettifyLogMessage).setOption("dedupStrategy",this.state.dedupStrategy);return F.CT?r.setOption("showTime",(0,f.IL)("showTime",!0)).setOption("showControls",!0).setOption("controlsStorageKey",f.vR).setOption("onLogOptionsChange",this.handleLogOptionsChange).setOption("setDisplayedFields",this.setDisplayedFields).setOption("logLineMenuCustomItems",[{label:"Copy link to log line",onClick:this.handleShareLogLine}]):r.setOption("showTime",!0),r.build()}),N(this,"handleLogOptionsChange",(e,t)=>{"sortOrder"===e&&(0,L.Q)(t)?(this.setState({sortOrder:t}),this.setLogsVizOption({sortOrder:t})):"wrapLogMessage"===e&&"boolean"==typeof t?(this.setState({wrapLogMessage:t}),this.setLogsVizOption({wrapLogMessage:t})):"prettifyLogMessage"===e&&"boolean"==typeof t?(this.setState({prettifyLogMessage:t}),this.setLogsVizOption({prettifyLogMessage:t})):"dedupStrategy"===e&&(0,L.K)(t)&&((0,f.WO)(this,t),this.setState({dedupStrategy:t}),this.setLogsVizOption({dedupStrategy:t}))}),N(this,"updateVisibleRange",e=>{o.jh.getAncestor(this,C.Mn).setState({logsCount:e[0].length}),this.setState({series:e});o.jh.findByKeyAndType(this,j.b,j._).updateVisibleRange(e)}),N(this,"handleShareLogLineClick",(e,t)=>{t&&this.handleShareLogLine(t)}),N(this,"handleShareLogLine",e=>{if(!this.state.body)return;const t=this.getParentScene(),n=(0,T.Ki)(e);(0,T.Dk)((0,T.gW)("panelState",{logs:{displayedFields:t.state.displayedFields,id:e.uid}},n))}),N(this,"handleLabelFilterClick",(e,t,n)=>{this.handleLabelFilter(e,t,n,"toggle")}),N(this,"handleLabelFilterOutClick",(e,t,n)=>{this.handleLabelFilter(e,t,n,"exclude")}),N(this,"handleIsFilterLabelActive",(e,t)=>{const n=(0,v.bY)(b.MB,this),r=(0,v.bY)(b.mB,this),a=(0,v.bY)(b._Y,this),i=(0,v.bY)(b._P,this),s=n=>n&&n.state.filters.findIndex(n=>"="===n.operator&&n.key===e&&n.value===t)>=0;return s(n)||(n=>{if(n){const r=n.state.filters.find(t=>"="===t.operator&&t.key===e);if(r){return(0,v.bu)(r,e).value===t}}return!1})(r)||s(a)||s(i)}),N(this,"handleFilterOutStringClick",e=>{const t=(0,v.Gk)(this);t&&(t.setState({filters:[...t.state.filters,{key:p.ld.caseSensitive,keyLabel:t.state.filters.length.toString(),operator:p.cK.negativeMatch,value:e}]}),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_popover_line_filter,{selectionLength:e.length}))}),N(this,"handleFilterStringClick",e=>{const t=(0,v.Gk)(this);t&&(t.setState({filters:[...t.state.filters,{key:p.ld.caseSensitive,keyLabel:t.state.filters.length.toString(),operator:p.cK.match,value:e}]}),(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_popover_line_filter,{selectionLength:e.length}))}),this.addActivationHandler(this.onActivate.bind(this))}}N(A,"Component",({model:e})=>{const{body:t,canClearFilters:n,error:r}=e.useState(),i=(0,c.useStyles2)(y.K_);return t?a().createElement("span",{className:i.panelWrapper},!r&&a().createElement(t.Component,{model:t}),r&&a().createElement(P,{error:r,clearFilters:n?()=>(0,D.rA)(t):void 0})):a().createElement(c.LoadingPlaceholder,{text:"Loading..."})})},71:(e,t,n)=>{n.d(t,{_:()=>D,b:()=>T});var r,a,i,s=n(5959),o=n.n(s),l=n(7781),c=n(6865),u=n(2007),d=n(1532),p=n(6838),g=n(5553),h=n(8714),f=n(2085),v=n(5700),b=n(9405),m=n(8531),y=n(5719),S=n(20);class w extends c.Bs{}i=function({model:e}){const{component:t,isLoading:n}=(0,m.usePluginComponent)("grafana-adaptivelogs-app/temporary-exemptions/v1"),r=(0,g.bY)(S.MB,e),{filters:a}=r.useState(),i=a.map(({key:e,operator:t,value:n})=>({key:e,operator:t,value:n})),s=(0,y.U4)(e);return n||!t?null:o().createElement(t,{dataSourceUid:s,streamSelector:i,contextHints:["explorelogs","logvolumepanel","headeraction"]})},(a="Component")in(r=w)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i;var O=n(9721),E=n(4509),x=n(5570),P=n(2165),j=n(4907),C=n(7985),L=n(4351);function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function F(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){_(e,t,n[t])})}return e}function k(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const T="logs-volume-panel";class D extends c.Bs{onActivate(){if(!this.state.panel){const e=this.getVizPanel();this.setState({panel:e}),this.updateContainerHeight(e)}const e=(0,g.cR)(this),t=(0,g.ir)(this);this._subs.add(e.subscribeToState((e,t)=>{(0,d.B)(e.filters,t.filters)||this.setState({panel:this.getVizPanel()})})),this._subs.add(t.subscribeToState((e,t)=>{(0,d.B)(e.filters,t.filters)||this.setState({panel:this.getVizPanel()})})),this._subs.add(this.subscribeToEvent(b.Of,e=>{if(e.key===S.e4){const e=c.jh.findObject(this,e=>e instanceof f.qV);if(e instanceof f.qV){const e=(0,g.iw)(this);e.setState({filters:e.state.filters})}}}))}getTitle(e,t){var n,r;var a;const i=null!==(a=null===(n=c.jh.getAncestor(this,h.P).state.ds)||void 0===n?void 0:n.maxLines)&&void 0!==a?a:C.by,s=(0,l.getValueFormat)("short"),o=void 0!==e?s(e,0):void 0;if(void 0===e&&void 0!==t&&t<i){var u;const e=s(t,0);return void 0!==e?`Log volume (${e.text}${null===(u=e.suffix)||void 0===u?void 0:u.trim()})`:"Log volume"}return void 0!==o?`Log volume (${o.text}${null===(r=o.suffix)||void 0===r?void 0:r.trim()})`:"Log volume"}setCollapsed(e,t){e?t.setState({$data:void 0}):(t.setState({$data:(0,j.rS)([(0,C.l)((0,p.m)(this,S.e4,!1),{legendFormat:`{{${S.e4}}}`})])}),this.subscribeToVisibleRange(t)),this.updateContainerHeight(t),(0,L.RN)("collapsed",e?"true":void 0)}getVizPanel(){var e;const t=c.jh.getAncestor(this,O.Mn),n=(0,L.Rf)("collapsed"),r=c.d0.timeseries().setTitle(this.getTitle(t.state.totalLogsCount,t.state.logsCount)).setOption("legend",{calcs:["sum"],displayMode:u.LegendDisplayMode.List,showLegend:!0}).setUnit("short").setMenu(new v.GD({investigationOptions:{labelName:"level"}})).setCollapsible(!0).setCollapsed(n).setHeaderActions(new w({})).setShowMenuAlways(!0).setData(n?void 0:(0,j.rS)([(0,C.l)((0,p.m)(this,S.e4,!1),{legendFormat:`{{${S.e4}}}`})]));(0,j.ZC)(r);const a=r.build();return a.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(t)}),this._subs.add(a.subscribeToState((e,t)=>{e.collapsed!==t.collapsed&&this.setCollapsed(e.collapsed,a)})),this.subscribeToVisibleRange(a),this._subs.add(null===(e=t.state.$data)||void 0===e?void 0:e.subscribeToState(e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===l.LoadingState.Done&&this.updateVisibleRange(e.data.series)})),this._subs.add(t.subscribeToState((e,t)=>{e.totalLogsCount===t.totalLogsCount&&void 0===e.logsCount||(this.state.panel?this.state.panel.setState({title:this.getTitle(e.totalLogsCount,e.logsCount)}):this.setState({panel:this.getVizPanel()}))})),a}subscribeToVisibleRange(e){var t;const n=c.jh.getAncestor(this,O.Mn);this._subs.add(null===(t=e.state.$data)||void 0===t?void 0:t.subscribeToState(t=>{var r,a,i,s;if((null===(r=t.data)||void 0===r?void 0:r.state)===l.LoadingState.Done){var o,c;if((null===(i=n.state.$data)||void 0===i||null===(a=i.state.data)||void 0===a?void 0:a.state)!==l.LoadingState.Done||(null===(s=t.data.annotations)||void 0===s?void 0:s.length))this.displayVisibleRange();else this.updateVisibleRange(null===(c=n.state.$data)||void 0===c||null===(o=c.state.data)||void 0===o?void 0:o.series);(0,j.C6)(e,t.data.series,this)}}))}updateContainerHeight(e){const t=c.jh.getAncestor(e,c.G1),n=e.state.collapsed?35:Math.max(Math.round(.2*window.innerHeight),100);t.setState({height:n,maxHeight:n,minHeight:n})}updateVisibleRange(e=[]){this.updatedLogSeries=e,this.displayVisibleRange()}displayVisibleRange(){var e,t;const n=this.state.panel;if(!n||!(null===(e=n.state.$data)||void 0===e?void 0:e.state.data)||(null===(t=n.state.$data)||void 0===t?void 0:t.state.data.state)!==l.LoadingState.Done||!this.updatedLogSeries)return;const r=(0,P.z5)(this.updatedLogSeries);this.updatedLogSeries=null,n.state.$data.setState({data:k(F({},n.state.$data.state.data),{annotations:[(0,P.hy)(r.start,r.end)]})})}constructor(e){super(k(F({},e),{key:T})),_(this,"updatedLogSeries",null),_(this,"extendTimeSeriesLegendBus",e=>{const t=(0,g.iw)(this);this._subs.add(null==t?void 0:t.subscribeToState(()=>{var e,t,n,r;const a=this.state.panel;(null==a||null===(t=a.state.$data)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.series)&&(0,j.C6)(a,null==a||null===(r=a.state.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.series,this)})),e.onToggleSeriesVisibility=(e,t)=>{const n=(0,x.PE)(e,this);this.publishEvent(new b.Of("legend","include",S.e4,e),!0),(0,E.EE)(E.NO.service_details,E.ir.service_details.level_in_logs_volume_clicked,{action:n,level:e})}}),this.addActivationHandler(this.onActivate.bind(this))}}_(D,"Component",({model:e})=>{const{panel:t}=e.useState();if(!t)return;const n=(0,u.useStyles2)(v.K_);return o().createElement("span",{className:n.panelWrapper},o().createElement(t.Component,{model:t}))})},9721:(e,t,n)=>{n.d(t,{AA:()=>V,DS:()=>R,Mn:()=>H,rD:()=>U,UO:()=>W,nU:()=>K,dB:()=>Q,TG:()=>G,tn:()=>z});var r=n(5959),a=n.n(r),i=n(7781),s=n(6865),o=n(2245),l=n(2007),c=n(6709),u=n(4509),d=n(1532),p=n(7839),g=n(7389),h=n(376),f=n(8502),v=n(5953),b=n(2152),m=n(8531),y=n(8714),S=n(6854),w=n(5553);function O(e,t){const n=["^","$",".","*","+","?","(",")","[","]","{","}","|"];return t||n.push("\\"),e.split("").filter((e,t,r)=>{const a=r[t+1],i=n.includes(a);return!("\\"===e&&i)}).join("")}var E=n(8428),x=n(7478),P=n(708),j=n(9683),C=n(5719),L=n(2085),_=n(7796),F=n(1220),k=n(4452),T=n(5607),D=n(8469),N=n(4907),$=n(7985),A=n(20);function B(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){B(e,t,n[t])})}return e}function I(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const R="logsPanelQuery",V="logsCountQuery";function z(e){return null==e?void 0:e.series.find(e=>e.refId===R)}function G(e){var t,n,r;return null===(r=s.jh.getAncestor(e,H).state.$detectedLabelsData)||void 0===r||null===(n=r.state.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0]}function U(e){var t;const n=s.jh.getAncestor(e,H);return W(null===(t=n.state.$detectedFieldsData)||void 0===t?void 0:t.state)}const W=e=>{var t,n;return null==e||null===(n=e.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0]},K=e=>{var t,n,r,a;return null===(a=e.data)||void 0===a||null===(r=a.series)||void 0===r||null===(n=r[0])||void 0===n||null===(t=n.fields)||void 0===t?void 0:t[0]},Q=e=>{var t,n,r,a;return null===(a=e.data)||void 0===a||null===(r=a.series)||void 0===r||null===(n=r[0])||void 0===n||null===(t=n.fields)||void 0===t?void 0:t[2]};class H extends s.Bs{setSubscribeToLabelsVariable(){const e=(0,w.cR)(this);0!==e.state.filters.length?this._subs.add(e.subscribeToState((e,t)=>{0===e.filters.length&&this.redirectToStart();let{breakdownLabel:n,labelName:r,labelValue:a}=(0,j.MC)(this);if(r===A.ky&&(r=A.OX),e.filters.some(e=>e.key===r&&(0,P.BG)(e.operator)&&(0,g.uu)(e.value)===(0,g.uu)(a))){if(!(0,d.B)(e.filters,t.filters)){var i,s,o,l;null===(i=this.state.$patternsData)||void 0===i||i.runQueries(),null===(s=this.state.$detectedLabelsData)||void 0===s||s.runQueries(),null===(o=this.state.$detectedFieldsData)||void 0===o||o.runQueries(),null===(l=this.state.$logsCount)||void 0===l||l.runQueries()}}else{const t=e.filters.find(e=>(0,P.BG)(e.operator)&&e.value!==A.ZO);t?this.handlePrimaryLabelChange(t,n):this.redirectToStart()}})):this.redirectToStart()}handlePrimaryLabelChange(e,t){const n=s.jh.getAncestor(this,y.P),r=n.state.routeMatch,a=(0,A.zE)(e.value)?(0,g.uu)((0,A.Dx)(e.value)):(0,g.uu)(e.value);var i,o,l;if(n.setState({routeMatch:I(M({},r),{isExact:null===(i=null==r?void 0:r.isExact)||void 0===i||i,params:I(M({},null==r?void 0:r.params),{labelName:e.key===A.OX?A.ky:e.key,labelValue:a.split("|")[0]}),path:null!==(o=null==r?void 0:r.path)&&void 0!==o?o:"",url:null!==(l=null==r?void 0:r.url)&&void 0!==l?l:""})}),this.resetTabCount(),t){const e=this.getDrilldownValueSlug();if(!e)throw new Error(`Invalid value slug ${e}`);(0,x.fg)(e,t,this)}else{const e=this.getPageSlug();if(!e)throw new Error(`Invalid page slug ${e}`);(0,x.Vt)(e,this)}}getPageSlug(){const e=(0,E.mx)((0,j.FT)());if(e&&e!==p.G3.embed)return e;const t=(0,E.mx)(this.state.pageSlug);return t||void 0}getDrilldownPageSlug(){const e=(0,E.EP)((0,j.er)());return e||this.state.pageSlug}getDrilldownValueSlug(){const e=(0,E.EP)((0,j.er)());if(e)return e}redirectToStart(){this.state.embedded?console.error("Cannot redirect to start when embedded"):(this.setState({$data:void 0,$detectedFieldsData:void 0,$detectedLabelsData:void 0,$logsCount:void 0,$patternsData:void 0,body:void 0,fieldsCount:void 0,labelsCount:void 0,logsCount:void 0,patternsCount:void 0,totalLogsCount:void 0}),(0,b.JO)().setServiceSceneState(this.state),this._subs.unsubscribe(),(0,x.Ns)())}showVariables(){s.jh.findByKeyAndType(this,L.kz,L.qV).setState({visible:!0}),(0,w.YS)(this).setState({hide:o.zL.dontHide})}getMetadata(){const e=(0,b.JO)().getServiceSceneState();e&&this.setState(M({},e))}getUrlState(){return{drillDownLabel:this.state.drillDownLabel,pageSlug:this.state.pageSlug}}updateFromUrl(e){const t={};if(this.state.embedded){if(e&&"string"==typeof e.pageSlug&&e.pageSlug!==this.state.pageSlug){const n=(0,E.Wf)(e.pageSlug);n&&(t.pageSlug=n)}var n;if(e&&"string"==typeof e.drillDownLabel||null===e.drillDownLabel&&e.drillDownLabel!==this.state.drillDownLabel)t.drillDownLabel=null!==(n=e.drillDownLabel)&&void 0!==n?n:void 0;Object.keys(t).length&&(this.setState(t),this.updateContentScene())}}updateContentScene(){const e=s.jh.getAncestor(this,y.P);e.setState({contentScene:e.getContentScene()})}onActivate(){this.state.body||this.setState({body:this.buildGraphScene()});s.jh.findByKeyAndType(this,y.y,_.H).setState({hidden:!0}),this.showVariables(),this.getMetadata(),this.resetBodyAndData(),this.setBreakdownView(),this.runQueries(),this._subs.add(this.subscribeToPatternsQuery()),this._subs.add(this.subscribeToDetectedLabelsQuery()),this._subs.add(this.subscribeToDetectedFieldsQuery(this.getPageSlug()!==p.G3.fields)),this._subs.add(this.subscribeToLogsQuery()),this._subs.add(this.subscribeToLogsCountQuery()),this.setSubscribeToLabelsVariable(),this._subs.add(this.subscribeToFieldsVariable()),this._subs.add(this.subscribeToMetadataVariable()),this._subs.add(this.subscribeToLevelsVariableChangedEvent()),this._subs.add(this.subscribeToLevelsVariableFiltersState()),this._subs.add(this.subscribeToDataSourceVariable()),this._subs.add(this.subscribeToPatternsVariable()),this._subs.add(this.subscribeToLineFiltersVariable()),this._subs.add(this.subscribeToTimeRange()),function(e){const t=i.urlUtil.getUrlSearchParams(),n=t["var-lineFilter"];if(!Array.isArray(n)||!n.length)return;const r=n[0];if("string"!=typeof r||!r)return;const a=s.jh.getAncestor(e,y.P),o=(0,w.Gk)(e),l=null==r?void 0:r.match(/\|=.`(.+?)`/);var c,u;l&&2===l.length&&(null===(u=a.state.body)||void 0===u||null===(c=u.state.lineFilterRenderer)||void 0===c||c.addActivationHandler(()=>{o.setState({filters:[{key:S.ld.caseSensitive,keyLabel:"0",operator:S.cK.match,value:O(l[1],!0)}]})}));const d=null==r?void 0:r.match(/`\(\?i\)(.+)`/);var p,g;d&&2===d.length&&(null===(g=a.state.body)||void 0===g||null===(p=g.state.lineFilterRenderer)||void 0===p||p.addActivationHandler(()=>{o.updateFilters([{key:S.ld.caseInsensitive,keyLabel:"0",operator:S.cK.match,value:O(d[1],!1)}])}));const h=m.locationService.getLocation();delete t["var-lineFilter"],m.locationService.replace(i.urlUtil.renderUrl(h.pathname,t))}(this)}subscribeToPatternsVariable(){return(0,w.Ku)(this).subscribeToState((e,t)=>{var n,r;e.value!==t.value&&(null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())})}subscribeToLineFiltersVariable(){return(0,w.Gk)(this).subscribeToEvent(s.oh,()=>{var e,t;null===(e=this.state.$logsCount)||void 0===e||e.runQueries(),null===(t=this.state.$detectedFieldsData)||void 0===t||t.runQueries()})}subscribeToDataSourceVariable(){return(0,w.S9)(this).subscribeToState(()=>{this.redirectToStart()})}resetTabCount(){this.setState({fieldsCount:void 0,labelsCount:void 0,patternsCount:void 0}),(0,b.JO)().setServiceSceneState(this.state)}subscribeToFieldsVariable(){return(0,w.ir)(this).subscribeToState((e,t)=>{var n,r;(0,d.B)(e.filters,t.filters)||(this.removeInactiveJsonParserProps(e,t),null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())})}removeInactiveJsonParserProps(e,t){const n=(0,w.U2)(this);if(e.filters.length||n.state.filters.length){if(e.filters.length<t.filters.length){t.filters.filter(t=>!e.filters.find(e=>e.key===t.key)).length&&(0,f.AY)(this)}}else(0,h.Ak)(this)}subscribeToMetadataVariable(){return(0,w.oY)(this).subscribeToState((e,t)=>{var n,r;(0,d.B)(e.filters,t.filters)||(null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries())})}subscribeToLevelsVariableChangedEvent(){return(0,w.iw)(this).subscribeToEvent(s.oh,()=>{var e;null===(e=this.state.$detectedFieldsData)||void 0===e||e.runQueries()})}subscribeToLevelsVariableFiltersState(){return(0,w.iw)(this).subscribeToState((e,t)=>{var n;(0,d.B)(e.filters,t.filters)||(null===(n=this.state.$logsCount)||void 0===n||n.runQueries())})}runQueries(){const e=this.getPageSlug(),t=this.getDrilldownPageSlug();var n,r,a,i;e!==p.G3.patterns&&void 0!==this.state.patternsCount||(null===(n=this.state.$patternsData)||void 0===n||n.runQueries());e!==p.G3.labels&&t!==p._J.label&&void 0!==this.state.labelsCount||(null===(r=this.state.$detectedLabelsData)||void 0===r||r.runQueries());e!==p.G3.fields&&t!==p._J.field&&void 0!==this.state.fieldsCount||(null===(a=this.state.$detectedFieldsData)||void 0===a||a.runQueries());void 0===this.state.logsCount&&(null===(i=this.state.$logsCount)||void 0===i||i.runQueries())}subscribeToPatternsQuery(){var e;return null===(e=this.state.$patternsData)||void 0===e?void 0:e.subscribeToState(e=>{var t;if(this.updateLoadingState(e,p.ob.patterns),(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){const t=e.data.series;void 0!==(null==t?void 0:t.length)&&(this.setState({patternsCount:t.length}),(0,b.JO)().setPatternsCount(t.length))}})}subscribeToDetectedLabelsQuery(){var e;return null===(e=this.state.$detectedLabelsData)||void 0===e?void 0:e.subscribeToState(e=>{var t;if(this.updateLoadingState(e,p.ob.labels),(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){const t=e.data,n=t.series[0].fields;if(void 0!==t.series.length&&void 0!==n.length){const e=t.series[0].fields.filter(e=>A.e4!==e.name);this.setState({labelsCount:e.length+1}),(0,b.JO)().setLabelsCount(n.length)}}})}updateLoadingState(e,t){var n;const r=this.state.loadingStates;r[t]=(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Loading;const a=Object.values(r).some(e=>e);this.setState({loading:a,loadingStates:r})}subscribeToLogsQuery(){var e;return null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState((e,t)=>{var n,r;if(this.updateLoadingState(e,p.ob.logs),(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done||(null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Streaming){var a,s;const t=null!==(s=null===(a=e.data.series[0])||void 0===a?void 0:a.length)&&void 0!==s?s:0;t!==this.state.logsCount&&this.setState({logsCount:t})}!function(e,t){var n,r,a;if((null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done)(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_on_query_complete,{vizType:null===(a=(0,C.UX)(t,e=>e instanceof T.i,T.i))||void 0===a?void 0:a.state.visualizationType,tab:t.getPageSlug()});else if((null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Error){var s,o,l,c,d,p,g;(0,u.EE)(u.NO.service_details,u.ir.service_details.logs_on_query_error,{vizType:null===(s=(0,C.UX)(t,e=>e instanceof T.i,T.i))||void 0===s?void 0:s.state.visualizationType,errorCount:null===(l=e.data)||void 0===l||null===(o=l.errors)||void 0===o?void 0:o.length,error:null===(d=e.data)||void 0===d||null===(c=d.errors)||void 0===c?void 0:c.map(e=>e.message).join(", "),status:null===(g=e.data)||void 0===g||null===(p=g.errors)||void 0===p?void 0:p.map(e=>e.status).join(", "),tab:t.getPageSlug()})}}(e,this)})}subscribeToLogsCountQuery(){var e;return null===(e=this.state.$logsCount)||void 0===e?void 0:e.subscribeToState(e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){var n,r,a,s;const t=null===(s=e.data.series[0])||void 0===s||null===(a=s.fields)||void 0===a||null===(r=a[1])||void 0===r||null===(n=r.values)||void 0===n?void 0:n[0];this.setState({totalLogsCount:t})}})}subscribeToDetectedFieldsQuery(e){var t;return null===(t=this.state.$detectedFieldsData)||void 0===t?void 0:t.subscribeToState(t=>{var n;this.updateLoadingState(t,p.ob.fields);const r=t.data,a=null==r?void 0:r.series[0];e&&(null===(n=t.data)||void 0===n?void 0:n.state)===i.LoadingState.Done&&void 0!==a&&a.length!==this.state.fieldsCount&&(this.setState({fieldsCount:a.length}),(0,b.JO)().setFieldsCount(a.length))})}subscribeToTimeRange(){return s.jh.getTimeRange(this).subscribeToState(()=>{var e,t,n,r;null===(e=this.state.$patternsData)||void 0===e||e.runQueries(),null===(t=this.state.$detectedLabelsData)||void 0===t||t.runQueries(),null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries(),null===(r=this.state.$logsCount)||void 0===r||r.runQueries()})}resetBodyAndData(){let e={};this.state.$data||(e.$data=X()),this.state.$patternsData||(e.$patternsData=q()),this.state.$detectedLabelsData||(e.$detectedLabelsData=J()),this.state.$detectedFieldsData||(e.$detectedFieldsData=Y()),this.state.$logsCount||(e.$logsCount=Z()),this.state.body||(e.body=this.buildGraphScene()),Object.keys(e).length&&this.setState(e)}buildGraphScene(){return new s.G1({children:[new s.vA({body:new F.g({}),ySizing:"content"})],direction:"column"})}setBreakdownView(){const{body:e}=this.state,t=this.getPageSlug(),n=k._.find(e=>e.value===t);if(!e){const e=new Error("body is not defined in setBreakdownView!");throw v.v.error(e,{msg:"ServiceScene setBreakdownView error"}),e}if(n)e.setState({children:[...e.state.children.slice(0,1),n.getScene(e=>{"fields"===n.value&&this.setState({fieldsCount:e})})]});else{const t=this.getDrilldownPageSlug(),n=k.n.find(e=>e.value===t);if(n&&this.state.drillDownLabel)e.setState({children:[...e.state.children.slice(0,1),n.getScene(this.state.drillDownLabel)]});else if(this.state.embedded){const t=k._[0];e.setState({children:[...e.state.children.slice(0,1),t.getScene(e=>{})]})}else v.v.error(new Error("not setting breakdown view"),{msg:"setBreakdownView error"})}}constructor(e){var t;super(M({$data:X(),$detectedFieldsData:Y(),$detectedLabelsData:J(),$logsCount:Z(),$patternsData:q(),body:null!==(t=e.body)&&void 0!==t?t:new s.G1({children:[new s.vA({body:new F.g({}),ySizing:"content"})],direction:"column"}),loading:!0,loadingStates:{[p.ob.patterns]:!1,[p.ob.labels]:!1,[p.ob.fields]:!1,[p.ob.logs]:!1}},e)),B(this,"_variableDependency",new s.Sh(this,{variableNames:[A.EY,A.MB,A.mB,A.uw,A._Y]})),B(this,"_urlSync",new s.So(this,{keys:[D.Z,D.o]})),this.addActivationHandler(this.onActivate.bind(this))}}function q(){const{jsonData:e}=c.plugin.meta;if(!(null==e?void 0:e.patternsDisabled))return(0,N.FH)([(0,$.BM)(`{${A.S1}}`,"patterns",{refId:"patterns"})],{runQueriesMode:"manual"})}function J(){return(0,N.FH)([(0,$.BM)(`{${A.S1}}`,"detected_labels",{refId:"detectedLabels"})],{runQueriesMode:"manual"})}function Y(){return(0,N.FH)([(0,$.BM)(A.Do,"detected_fields",{refId:"detectedFields"})],{runQueriesMode:"manual"})}function X(){return(0,N.rS)([(0,$.l)(A.SA,{refId:R})])}function Z(){const e=(0,N.rS)([(0,$.l)(`sum(count_over_time(${A.SA}[$__auto]))`,{queryType:"instant",refId:V})],{runQueriesMode:"manual"});if(e instanceof s.dt)return e;const t=new Error("log count query provider is not query runner!");throw v.v.error(t,{msg:"getLogCountQueryRunner: invalid return type"}),t}B(H,"Component",({model:e})=>{const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):a().createElement(l.LoadingPlaceholder,{text:"Loading..."})})},577:(e,t,n)=>{n.d(t,{p:()=>g});var r,a,i,s=n(5959),o=n.n(s),l=n(6089),c=n(6865),u=n(2007),d=n(696),p=n(4351);class g extends c.Bs{setHover(e){this.setState({hover:e})}onClick(e){e?(0,d.wy)(this.state.labelName,this.state.labelValue,this):(0,d._J)(this.state.labelName,this.state.labelValue,this)}}i=({model:e})=>{const{ds:t,hover:n,labelName:r,labelValue:a}=e.useState(),i=(0,p.eT)(t,r).includes(a),s=(0,u.useStyles2)(e=>({wrapper:(0,l.css)({alignSelf:"center",display:"flex",flexDirection:"column",justifyContent:"center"})})),c=i?`Remove  ${a} from favorites`:`Add ${a} to favorites`;return o().createElement("span",{className:s.wrapper},o().createElement(u.ToolbarButton,{onMouseOver:()=>{e.setHover(!0)},onMouseOut:()=>{e.setHover(!1)},icon:o().createElement(u.Icon,{name:i?"favorite":"star",size:"lg",type:i?"mono":"default"}),color:i?"rgb(235, 123, 24)":"#ccc",onClick:()=>e.onClick(i),name:"star","aria-label":c,tooltip:c}))},(a="Component")in(r=g)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},173:(e,t,n)=>{n.d(t,{X:()=>ue,y:()=>ge});var r=n(5959),a=n.n(r),i=n(6089),s=n(3241),o=n(7781),l=n(8531),c=n(6865),u=n(2245),d=n(2007),p=n(1532),g=n(4702),h=n(7478),f=n(5719),v=n(5553),b=n(8714),m=n(7796),y=n(9731),S=n(7243),w=n(4509),O=n(6854),E=n(3571),x=n(20),P=n(9405);function j(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function C(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){j(e,t,n[t])})}return e}function L(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class _ extends c.Bs{onActivate(){this.setState(C({},this.isSelected())),this._subs.add((0,v.cR)(this).subscribeToState(()=>{const e=this.isSelected();this.state.included!==e.included&&this.setState(C({},e))}))}getFilter(){return{name:this.state.name,value:this.state.value}}constructor(e){super(L(C({},e),{included:null})),j(this,"isSelected",()=>{const e=(0,v.cR)(this).state.filters.find(e=>{const t=(0,v.z2)(x.MB,e);return e.key===this.state.name&&t.value===this.state.value});return e?{included:e.operator===O.w7.Equal}:{included:!1}}),j(this,"onClick",e=>{const t=this.getFilter();(0,P.Qt)(t.name,t.value,e,this,x.MB);const n=(0,v.cR)(this);(0,w.EE)(w.NO.service_selection,w.ir.service_selection.add_to_filters,{action:e,filtersLength:(null==n?void 0:n.state.filters.length)||0,filterType:"index-filters",key:t.name}),this.setState(C({},this.isSelected()))}),this.addActivationHandler(this.onActivate.bind(this))}}j(_,"Component",({model:e})=>{const{included:t,value:n}=e.useState(),r=(0,d.useStyles2)(F);return a().createElement("span",{className:r.wrapper},a().createElement(d.Button,{tooltip:!0===t?`Remove ${n} from filters`:`Add ${n} to filters`,variant:"secondary",fill:"outline",size:"sm","aria-selected":!0===t,className:r.includeButton,onClick:()=>!0===t?e.onClick("clear"):e.onClick("include"),"data-testid":E.b.exploreServiceDetails.buttonFilterInclude},t?"Remove":"Include"))});const F=()=>({container:(0,i.css)({display:"flex",justifyContent:"center"}),includeButton:(0,i.css)({borderRadius:0}),wrapper:(0,i.css)({alignSelf:"center",display:"flex",flexDirection:"column",justifyContent:"center"})});var k=n(7191);const T=()=>a().createElement(k.R,null,a().createElement("p",null,"Log volume has not been configured."),a().createElement("p",null,a().createElement(d.TextLink,{href:"https://grafana.com/docs/loki/latest/reference/api/#query-log-volume",external:!0},"Instructions to enable volume in the Loki config:")),a().createElement(d.Text,{textAlignment:"left"},a().createElement("pre",null,a().createElement("code",null,"limits_config:",a().createElement("br",null),"  volume_enabled: true"))));var D=n(577);const N=()=>a().createElement(k.R,null,a().createElement("p",null,"No service matched your search.")),$=e=>a().createElement(k.R,null,a().createElement("p",null,"No logs found in ",a().createElement("strong",null,e.labelName),".",a().createElement("br",null),"Please adjust time range or select another label."));var A=n(696);function B(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class M extends c.Bs{onActivate(){const e=(0,v.cR)(this);this.setState({hidden:e.state.filters.length>0}),e.subscribeToState(e=>{this.setState({hidden:e.filters.length>0})})}constructor(e){super(e),B(this,"getLink",()=>{if(this.state.labelValue)return R(this.state.labelName,this.state.labelValue,this)}),B(this,"onClick",()=>{I(this.state.labelName,this.state.labelValue,this)}),this.addActivationHandler(this.onActivate.bind(this))}}function I(e,t,n){(0,w.EE)(w.NO.service_selection,w.ir.service_selection.service_selected,{label:e,value:t}),(0,A._J)(e,t,n)}function R(e,t,n){var r;const a=(0,v.cR)(n),i=[...a.state.filters.filter(n=>!(n.key===e&&n.value===t)),{key:e,operator:O.w7.Equal,value:t}];e===x.OX&&(e=x.ky);const s=a.clone({filters:i});return(0,h.k9)(e,t,null===(r=s.urlSync)||void 0===r?void 0:r.getUrlState())}function V(e){return{button:(0,i.css)({alignSelf:"center"})}}B(M,"Component",({model:e})=>{const t=(0,d.useStyles2)(V);(0,v.cR)(e).useState();const{hidden:n}=e.useState();if(n)return null;const r=e.getLink();return a().createElement(d.LinkButton,{"data-testid":E.b.index.selectServiceButton,tooltip:`View logs for ${e.state.labelValue}`,className:t.button,variant:"primary",fill:"outline",size:"sm",disabled:!r,href:e.getLink(),onClick:e.onClick},"Show logs")});var z=n(4351);function G(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class U extends c.Bs{}function W(e){return{icon:(0,i.css)({color:e.colors.text.disabled,marginLeft:e.spacing.x1}),searchFieldPlaceholderText:(0,i.css)({alignItems:"center",color:e.colors.text.disabled,display:"flex",flex:"1 0 auto",fontSize:e.typography.bodySmall.fontSize,textWrapMode:"nowrap"}),searchPageCountWrap:(0,i.css)({alignItems:"center",display:"flex"}),select:(0,i.css)({marginLeft:e.spacing(1),marginRight:e.spacing(1),maxWidth:"65px"})}}G(U,"PageCount",({model:e,totalCount:t})=>{const n=(0,d.useStyles2)(W),i=c.jh.getAncestor(e,ge),{countPerPage:s}=i.useState(),o=function(e){const t=20,n=60,r=Math.ceil(e/t)*t,a=[];for(let i=t;i<=n&&i<=r;i+=t){let n=i.toString();i<t?n=i.toString():i>e&&(n=e.toString()),a.push({label:n,value:i.toString()})}return a}(t);return(0,r.useEffect)(()=>{var e,t;const n=null!==(t=null===(e=o[o.length-1])||void 0===e?void 0:e.value)&&void 0!==t?t:s.toString();s.toString()>n&&i.setState({countPerPage:parseInt(n,10)})},[s,o,i]),a().createElement("span",{className:n.searchPageCountWrap},a().createElement("span",{className:n.searchFieldPlaceholderText},"Showing"," ",a().createElement(d.Select,{className:n.select,onChange:e=>{if(e.value){const t=parseInt(e.value,10);i.setState({countPerPage:t,currentPage:1}),i.updateBody(),(0,z.uF)(t)}},options:o,value:s.toString()})," ","of ",t," ",a().createElement(d.IconButton,{className:n.icon,"aria-label":"Count info",name:"info-circle",tooltip:`${t} labels have values for the selected time range. Total label count may differ`})))}),G(U,"Component",({model:e,totalCount:t})=>{const n=c.jh.getAncestor(e,ge),{countPerPage:r,currentPage:s}=n.useState(),o=(0,d.useStyles2)(e=>({pagination:(0,i.css)({float:"none"}),paginationWrap:(0,i.css)({[e.breakpoints.up("lg")]:{display:"none"},[e.breakpoints.down("lg")]:{display:"flex",flex:"1 0 auto",justifyContent:"flex-end"}}),paginationWrapMd:(0,i.css)({[e.breakpoints.down("lg")]:{display:"none"},[e.breakpoints.up("lg")]:{display:"flex",flex:"1 0 auto",justifyContent:"flex-end"}})}));return t>r?a().createElement(a().Fragment,null,a().createElement("span",{className:o.paginationWrapMd},a().createElement(d.Pagination,{className:o.pagination,currentPage:s,numberOfPages:Math.ceil(t/r),onNavigate:e=>{n.setState({currentPage:e}),n.updateBody()}})),a().createElement("span",{className:o.paginationWrap},a().createElement(d.Pagination,{showSmallVersion:!0,className:o.pagination,currentPage:s,numberOfPages:Math.ceil(t/r),onNavigate:e=>{n.setState({currentPage:e}),n.updateBody()}}))):null});var K=n(4907),Q=n(7985),H=n(5002);function q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class Y extends c.Bs{}q(Y,"Component",({model:e})=>{const t=c.jh.getAncestor(e,ge),n=c.jh.getAncestor(e,ne),{showPopover:r,tabOptions:i}=n.useState(),s=(0,d.useStyles2)(X),o=i.map(e=>J(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){q(e,t,n[t])})}return e}({},e),{icon:e.saved?"save":void 0,label:`${e.label}`}));return a().createElement(d.Stack,{direction:"column",gap:0,role:"tooltip"},a().createElement("div",{className:s.card.body},a().createElement(d.Select,{menuShouldPortal:!1,width:50,onBlur:()=>{n.toggleShowPopover()},autoFocus:!0,isOpen:r,placeholder:"Search labels",options:o,isSearchable:!0,openMenuOnFocus:!0,onChange:e=>{e.value&&(n.toggleShowPopover(),t.setSelectedTab(e.value))}})))});const X=e=>({card:{body:(0,i.css)({padding:e.spacing(1)}),p:(0,i.css)({maxWidth:300})}});function Z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ee(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Z(e,t,n[t])})}return e}function te(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class ne extends c.Bs{getLabelsFromQueryRunnerState(e=(()=>{var e;return null===(e=this.state.$labelsData)||void 0===e?void 0:e.state})()){var t,n,r;return null===(r=e.data)||void 0===r||null===(n=r.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.fields.map(e=>({cardinality:e.values[0],label:e.name}))}populatePrimaryLabelsVariableOptions(e){const t=c.jh.getAncestor(this,ge).getSelectedTab(),n=(0,z.sj)((0,v.S9)(this).getValue().toString()),r=e.map(e=>{const r=n.indexOf(e.label);return{active:t===e.label,label:e.label===x.OX?x.ky:e.label,saved:-1!==r,savedIndex:r,value:e.label}}).sort((e,t)=>e.value===x.OX||t.value===x.OX?e.value===x.OX?-1:1:e.label<t.label?-1:e.label>t.label?1:0);this.setState({tabOptions:r})}runDetectedLabels(){this.state.$labelsData.runQueries()}runDetectedLabelsSubs(){this._subs.add(c.jh.getTimeRange(this).subscribeToState(()=>{this.runDetectedLabels()})),this._subs.add((0,v.S9)(this).subscribeToState(()=>{this.runDetectedLabels()}))}onActivate(){this.runDetectedLabels(),this.setState({popover:new Y({})}),this.runDetectedLabelsSubs(),this._subs.add((0,v.S9)(this).subscribeToState(()=>{this.state.$labelsData.runQueries()})),this._subs.add((0,v.El)(this).subscribeToState(()=>{var e;const t=this.getLabelsFromQueryRunnerState(null===(e=this.state.$labelsData)||void 0===e?void 0:e.state);t&&this.populatePrimaryLabelsVariableOptions(t)})),this._subs.add(this.state.$labelsData.subscribeToState(e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===o.LoadingState.Done){const t=this.getLabelsFromQueryRunnerState(e),n=c.jh.getAncestor(this,ge);t&&this.populatePrimaryLabelsVariableOptions(t);const r=n.getSelectedTab();(null==t?void 0:t.some(e=>e.label===r))||n.selectDefaultLabelTab()}}))}constructor(e){super(ee({$labelsData:(0,K.HF)({queries:[(0,Q.BM)("","detected_labels")],runQueriesMode:"manual"}),showPopover:!1,tabOptions:[{label:x.ky,saved:!0,value:x.OX}]},e)),Z(this,"removeSavedTab",e=>{(0,z.Gg)((0,v.S9)(this).getValue().toString(),e);const t=this.getLabelsFromQueryRunnerState();t&&this.populatePrimaryLabelsVariableOptions(t);const n=c.jh.getAncestor(this,ge);n.getSelectedTab()===e&&n.selectDefaultLabelTab()}),Z(this,"toggleShowPopover",()=>{this.setState({showPopover:!this.state.showPopover})}),this.addActivationHandler(this.onActivate.bind(this))}}Z(ne,"Component",({model:e})=>{const{$labelsData:t,popover:n,showPopover:l,tabOptions:u}=e.useState(),{data:p}=t.useState(),g=c.jh.getAncestor(e,ge);(0,v.El)(e).useState();const h=(0,d.useStyles2)(re),f=(0,r.useRef)(null);return a().createElement(d.TabsBar,{className:h.tabs},u.filter(e=>e.saved||e.active||e.value===x.OX).sort((e,t)=>{return e.value===x.OX||t.value===x.OX?e.value===x.OX?-1:1:(null!==(n=e.savedIndex)&&void 0!==n?n:0)-(null!==(r=t.savedIndex)&&void 0!==r?r:0);var n,r}).map(t=>{const n=a().createElement(d.Tab,{key:t.value,onChangeTab:()=>{g.setSelectedTab(t.value)},label:(0,H.EJ)(t.label,15,!0),active:t.active,suffix:t.value!==x.OX?n=>a().createElement(a().Fragment,null,a().createElement(d.Tooltip,{content:"Remove tab"},a().createElement(d.Icon,{onKeyDownCapture:n=>{"Enter"===n.key&&e.removeSavedTab(t.value)},onClick:n=>{n.stopPropagation(),e.removeSavedTab(t.value)},name:"times",className:(0,i.cx)(n.className)}))):void 0});return t.label.length>15?a().createElement(d.Tooltip,{key:t.value,content:t.label},n):n}),(null==p?void 0:p.state)===o.LoadingState.Loading&&a().createElement(d.Tab,{label:"Loading tabs",icon:"spinner"}),(null==p?void 0:p.state)===o.LoadingState.Done&&a().createElement("span",{className:h.addTab},a().createElement(d.Tab,{onChangeTab:e.toggleShowPopover,label:"Add label",ref:f,icon:"plus-circle"})),n&&a().createElement(d.PopoverController,{content:a().createElement(n.Component,{model:n})},(e,t,n)=>{const r={onBlur:t,onFocus:e};return a().createElement(a().Fragment,null,f.current&&a().createElement(a().Fragment,null,a().createElement(d.Popover,ee(te(ee({},n,s.rest),{show:l,wrapperClassName:h.popover,referenceElement:f.current,renderArrow:!0}),r))))}))});const re=e=>({addTab:(0,i.css)({"& button":{color:e.colors.primary.text},color:e.colors.primary.text,label:"add-label-tab"}),popover:(0,i.css)({background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3}),tabs:(0,i.css)({overflowY:"hidden"})});var ae=n(5570);function ie(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function se(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ie(e,t,n[t])})}return e}function oe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const le=l.config.featureToggles.exploreLogsAggregatedMetrics,ce="__aggregated_metric__",ue=(0,o.dateTime)("2024-08-30","YYYY-MM-DD");const de="var-primary_label",pe="var-ds";class ge extends c.Bs{getUrlState(){const{key:e}=he(),t=(0,v.El)(this).state.filters[0];return t.key&&t.key!==e&&(0,v.El)(this).setState({filters:[oe(se({},t),{key:null!=e?e:t.key})]}),{}}updateFromUrl(e){}addDatasourceChangeToBrowserHistory(e){const t=l.locationService.getLocation(),n=new URLSearchParams(t.search),r=n.get(pe);if(r&&e!==r){const r=t.pathname+t.search;n.set(pe,e);const a=t.pathname+"?"+n.toString();r!==a&&(0,h.ad)(a)}}addLabelChangeToBrowserHistory(e,t=!1){const{key:n,location:r,search:a}=he();if(n){const i=null==n?void 0:n.split("|");if((null==i?void 0:i[0])!==e){i[0]=e,a.set(de,i.join("|"));const n=r.pathname+r.search,s=r.pathname+"?"+a.toString();n!==s&&(t?l.locationService.replace(s):(0,h.ad)(s))}}}getSelectedTab(){var e;return null===(e=(0,v.El)(this).state.filters[0])||void 0===e?void 0:e.key}selectDefaultLabelTab(){this.addLabelChangeToBrowserHistory(x.OX,!0),this.setSelectedTab(x.OX)}setSelectedTab(e){(0,z.cO)((0,v.S9)(this).getValue().toString(),e),(0,v.h)(this),(0,v.BL)(e,this)}buildServiceLayout(e,t,n,r,a){var i;const s=[];var l;this.isAggregatedMetricsActive()||s.push(new _({name:e,value:t})),s.push(new M({labelName:e,labelValue:t}));const u=c.d0.timeseries().setTitle(t).setData((0,K.rS)([(0,Q.l)(this.getMetricExpression(t,n,r),{legendFormat:`{{${x.e4}}}`,refId:`ts-${t}`,step:n.state.value===ce?"10s":void 0})],{runQueriesMode:"manual"})).setCustomFieldConfig("stacking",{mode:d.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",d.DrawStyle.Bars).setUnit("short").setOverrides(K.jC).setOption("legend",{calcs:["sum"],displayMode:d.LegendDisplayMode.Table,placement:"right",showLegend:!0}).setHeaderActions([new D.p({ds:null!==(l=null===(i=a.getValue())||void 0===i?void 0:i.toString())&&void 0!==l?l:"",labelName:e,labelValue:t}),...s]).build();u.setState({extendPanelContext:(n,r)=>this.extendTimeSeriesLegendBus(e,t,r,u)});const p=new c.xK({$behaviors:[new c.Gg.K2({key:"serviceCrosshairSync",sync:o.DashboardCursorSync.Crosshair})],body:u});return p.addActivationHandler(()=>{var e;(null===(e=(0,f.oh)(p)[0].state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&this.runPanelQuery(p)}),p}isAggregatedMetricsActive(){const e=this.getQueryOptionsToolbar();return!(null==e?void 0:e.state.options.aggregatedMetrics.disabled)&&(null==e?void 0:e.state.options.aggregatedMetrics.active)}formatPrimaryLabelForUI(){const e=this.getSelectedTab();return e===x.OX?x.ky:e}setVolumeQueryRunner(){this.setState({$data:(0,K.HF)({queries:[(0,Q.$k)(`{${x.kl}, ${x.ll}}`,"volume",this.getSelectedTab())],runQueriesMode:"manual"})}),this.subscribeToVolume()}doVariablesNeedSync(){const e=(0,v.cR)(this),t=(0,v.aW)(this),n=this.getSelectedTab(),r=e.state.filters.filter(e=>e.key!==n);return{filters:r,needsSync:!(0,p.B)(r,t.state.filters)}}syncVariables(){const e=(0,v.aW)(this),{filters:t,needsSync:n}=this.doVariablesNeedSync();n&&e.setState({filters:t})}onActivate(){var e;this.fixRequiredUrlParams(),this.syncVariables(),this.setVolumeQueryRunner(),this.subscribeToPrimaryLabelsVariable(),this.subscribeToLabelFilterChanges(),this.subscribeToActiveTabVariable((0,v.El)(this)),(null===(e=this.state.$data.state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&this.runVolumeOnActivate(),this.subscribeToTimeRange(),this.subscribeToDatasource(),this.subscribeToAggregatedMetricToggle(),this.subscribeToAggregatedMetricVariable()}runVolumeOnActivate(){var e,t;this.isTimeRangeTooEarlyForAggMetrics()?(this.onUnsupportedAggregatedMetricTimeRange(),(null===(e=this.state.$data.state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&this.runVolumeQuery()):(this.onSupportedAggregatedMetricTimeRange(),(null===(t=this.state.$data.state.data)||void 0===t?void 0:t.state)!==o.LoadingState.Done&&this.runVolumeQuery())}subscribeToAggregatedMetricToggle(){var e;this._subs.add(null===(e=this.getQueryOptionsToolbar())||void 0===e?void 0:e.subscribeToState((e,t)=>{e.options.aggregatedMetrics.userOverride!==t.options.aggregatedMetrics.userOverride&&this.runVolumeQuery(!0)}))}subscribeToDatasource(){this._subs.add((0,v.S9)(this).subscribeToState(e=>{this.setState({body:new c.gF({children:[]})}),this.addDatasourceChangeToBrowserHistory(e.value.toString()),this.runVolumeQuery()}))}subscribeToActiveTabVariable(e){this._subs.add(e.subscribeToState((e,t)=>{if(e.filterExpression!==t.filterExpression){const t=e.filters[0].key;this.addLabelChangeToBrowserHistory(t);const{needsSync:n}=this.doVariablesNeedSync();n?this.syncVariables():this.runVolumeQuery(!0)}}))}subscribeToAggregatedMetricVariable(){this._subs.add((0,v.vm)(this).subscribeToState((e,t)=>{e.value!==t.value&&(this.setState({body:new c.gF({children:[]})}),this.updateBody(!0))}))}subscribeToPrimaryLabelsVariable(){const e=(0,v.cR)(this);this._subs.add(e.subscribeToState((e,t)=>{(0,p.B)(e.filters,t.filters)||this.syncVariables()}))}subscribeToLabelFilterChanges(){const e=(0,v.aW)(this);this._subs.add(e.subscribeToState((e,t)=>{(0,p.B)(e.filters,t.filters)||this.runVolumeQuery(!0)}))}subscribeToVolume(){this._subs.add(this.state.$data.subscribeToState((e,t)=>{var n,r,a;(null===(n=e.data)||void 0===n?void 0:n.state)!==o.LoadingState.Done||(0,p.B)(null==t||null===(r=t.data)||void 0===r?void 0:r.series,null==e||null===(a=e.data)||void 0===a?void 0:a.series)||this.updateBody(!0)}))}subscribeToTimeRange(){this._subs.add(c.jh.getTimeRange(this).subscribeToState(()=>{this.isTimeRangeTooEarlyForAggMetrics()?this.onUnsupportedAggregatedMetricTimeRange():this.onSupportedAggregatedMetricTimeRange(),this.runVolumeQuery()}))}fixRequiredUrlParams(){const{key:e}=he();e||this.selectDefaultLabelTab()}isTimeRangeTooEarlyForAggMetrics(){return c.jh.getTimeRange(this).state.value.from.isBefore((0,o.dateTime)(ue))}onUnsupportedAggregatedMetricTimeRange(){const e=this.getQueryOptionsToolbar();null==e||e.setState({options:{aggregatedMetrics:oe(se({},null==e?void 0:e.state.options.aggregatedMetrics),{disabled:!0})}})}getQueryOptionsToolbar(){var e;return null===(e=c.jh.getAncestor(this,b.P).state.controls)||void 0===e?void 0:e.find(e=>e instanceof y.s)}onSupportedAggregatedMetricTimeRange(){const e=this.getQueryOptionsToolbar();null==e||e.setState({options:{aggregatedMetrics:oe(se({},null==e?void 0:e.state.options.aggregatedMetrics),{disabled:!1})}})}runVolumeQuery(e=!1){e&&this.setVolumeQueryRunner(),this.updateAggregatedMetricVariable(),this.state.$data.runQueries()}updateAggregatedMetricVariable(){const e=(0,v.vm)(this),t=(0,v.cR)(this);if(this.isTimeRangeTooEarlyForAggMetrics()&&le||!this.isAggregatedMetricsActive()){e.changeValueTo(x.OX),t.setState({hide:u.zL.dontHide}),e.changeValueTo(x.OX);c.jh.findByKeyAndType(this,b.y,m.H).setState({hidden:!1})}else{e.changeValueTo(ce),t.setState({filters:[],hide:u.zL.hideVariable});c.jh.findByKeyAndType(this,b.y,m.H).setState({hidden:!0})}}updateTabs(){if(!this.state.tabs){const e=new ne({});this.setState({tabs:e})}}getGridItems(){return this.state.body.state.children}getVizPanel(e){return e.state.body instanceof c.Eb?e.state.body:void 0}runPanelQuery(e){if(e.isActive){const n=(0,f.oh)(e);if(1===n.length){var t;const e=n[0],r=e.state.queries[0],a=null===(t=e.state.data)||void 0===t?void 0:t.timeRange,i=c.jh.getTimeRange(this),s=a?Math.abs(i.state.value.from.diff(null==a?void 0:a.from,"s")):1/0,o=a?Math.abs(i.state.value.to.diff(null==a?void 0:a.to,"s")):1/0,l=c.jh.interpolate(this,r.expr);(e.state.key!==l||s>0||o>0)&&(e.setState({key:l}),e.runQueries())}}}updateBody(e=!1){var t;const{labelsToQuery:n}=this.getLabels(null===(t=this.state.$data.state.data)||void 0===t?void 0:t.series),r=this.getSelectedTab();if(this.updateTabs(),this.state.paginationScene||this.setState({paginationScene:new U({})}),n&&0!==n.length){const t=[],a=this.getGridItems(),i=(0,v.vm)(this),s=(0,v.El)(this),o=(0,v.S9)(this),l=(this.state.currentPage-1)*this.state.countPerPage,c=l+this.state.countPerPage;for(const u of n.slice(l,c)){const n=a.filter(e=>{const t=this.getVizPanel(e);return(null==t?void 0:t.state.title)===u});if(2===n.length)t.push(n[0],n[1]),n[0].isActive&&e&&this.runPanelQuery(n[0]),n[1].isActive&&e&&this.runPanelQuery(n[1]);else{const e=this.buildServiceLayout(r,u,i,s,o),n=this.buildServiceLogsLayout(r,u);t.push(e,n)}}this.state.body.setState({autoRows:"200px",children:t,isLazy:!0,md:{columnGap:1,rowGap:1,templateColumns:"1fr"},templateColumns:"repeat(auto-fit, minmax(350px, 1fr) minmax(300px, calc(70vw - 100px)))"})}else this.state.body.setState({children:[]})}updateServiceLogs(e,t){var n;if(!this.state.body)return void this.updateBody();const{labelsToQuery:r}=this.getLabels(null===(n=this.state.$data.state.data)||void 0===n?void 0:n.series),a=null==r?void 0:r.indexOf(t);if(void 0===a||a<0)return;let i=[...this.getGridItems()];i.splice(2*a+1,1,this.buildServiceLogsLayout(e,t)),this.state.body.setState({children:i})}getLogExpression(e,t,n){return`{${e}=\`${t}\` , ${x.ll} }${n}`}getMetricExpression(e,t,n){const r=n.state.filters[0];return t.state.value===ce?r.key===x.OX?`sum by (${x.e4}) (sum_over_time({${ce}=\`${e}\` } | logfmt | unwrap count [$__auto]))`:`sum by (${x.e4}) (sum_over_time({${ce}=~\`.+\` } | logfmt | ${r.key}=\`${e}\` | unwrap count [$__auto]))`:`sum by (${x.e4}) (count_over_time({ ${r.key}=\`${e}\`, ${x.ll} } [$__auto]))`}getLabels(e){var t,n,r;const a=null!==(r=null==e||null===(t=e[0])||void 0===t?void 0:t.fields[0].values)&&void 0!==r?r:[],i=null===(n=(0,v.S9)(this).getValue())||void 0===n?void 0:n.toString(),s=(0,v.eY)(this).getValue(),o=this.getSelectedTab(),l=function(e,t,n,r){if(!(null==e?void 0:e.length))return[];".+"===n&&(n="");const a=(0,z.eT)(t,r).filter(t=>t.toLowerCase().includes(n.toLowerCase())&&e.includes(t));return Array.from(new Set([...a,...e]))}(a,i,String(s),o);return{labelsByVolume:a,labelsToQuery:l}}constructor(e){var t,n;super(se({$data:(0,K.HF)({queries:[],runQueriesMode:"manual"}),$variables:new c.Pj({variables:[new g.m({hide:u.zL.hideVariable,label:"Service",name:x.Du,skipUrlSync:!0,value:".+"}),new g.m({hide:u.zL.hideLabel,label:"",name:x.Wi,options:[{label:x.OX,value:x.OX},{label:ce,value:ce}],skipUrlSync:!0,value:x.OX}),new c.H9({expressionBuilder:e=>function(e){if(e.length){const t=e[0];return`${t.key}${t.operator}\`${t.value}\``}return""}(e),filters:[{key:null!==(t=he().key)&&void 0!==t?t:x.OX,operator:"=~",value:".+"}],hide:u.zL.hideLabel,name:x.Gb}),new c.H9({datasource:x.eL,expressionBuilder:Q.VW,filters:[],hide:u.zL.hideVariable,key:"adhoc_service_filter_replica",layout:"vertical",name:x.fi,skipUrlSync:!0})]}),body:new c.gF({children:[]}),countPerPage:null!==(n=(0,z.KH)())&&void 0!==n?n:20,currentPage:1,serviceLevel:new Map,showPopover:!1,tabOptions:[{label:x.ky,value:x.OX}]},e)),ie(this,"_urlSync",new c.So(this,{keys:[de]})),ie(this,"onSearchServicesChange",(0,s.debounce)(e=>{const t=(0,v.eY)(this);(e?(0,Q.vC)(e):".+")!==t.state.value&&t.setState({label:null!=e?e:"",value:e?(0,Q.vC)(e):".+"});const n=(0,v.El)(this),r=n.state.filters[0];(0,Q.vC)(t.state.value.toString())!==r.value&&n.setState({filters:[oe(se({},r),{value:(0,Q.vC)(t.state.value.toString())})]}),this.setState({currentPage:1}),(0,w.EE)(w.NO.service_selection,w.ir.service_selection.search_services_changed,{searchQuery:e})},500)),ie(this,"getLevelFilterForService",e=>{let t=this.state.serviceLevel.get(e)||[];if(0===t.length)return"";return` | ${t.map(e=>("logs"===e&&(e=""),`${x.e4}=\`${e}\``)).join(" or ")} `}),ie(this,"buildServiceLogsLayout",(e,t)=>{const n=this.getLevelFilterForService(t),r=new c.xK({$behaviors:[new c.Gg.K2({sync:o.DashboardCursorSync.Off})],body:c.d0.logs().setHoverHeader(!0).setData((0,K.rS)([(0,Q.l)(this.getLogExpression(e,t,n),{maxLines:100,refId:`logs-${t}`})],{runQueriesMode:"manual"})).setTitle(t).setOption("showTime",!0).setOption("enableLogDetails",!1).setOption("fontSize","small").setOption("displayedFields",(0,z.LJ)(this,e,t)).setOption("noInteractions",!0).build()});return r.addActivationHandler(()=>{var e;(null===(e=(0,f.oh)(r)[0].state.data)||void 0===e?void 0:e.state)!==o.LoadingState.Done&&this.runPanelQuery(r)}),r}),ie(this,"extendTimeSeriesLegendBus",(e,t,n,r)=>{const a=n.onToggleSeriesVisibility;n.onToggleSeriesVisibility=(n,i)=>{var s,o,l;null==a||a(n,i);const c=(0,ae.vX)(null!==(l=null===(o=r.state.$data)||void 0===o||null===(s=o.state.data)||void 0===s?void 0:s.series)&&void 0!==l?l:[]),u=(0,ae.pC)(n,this.state.serviceLevel.get(t),i,c);this.state.serviceLevel.set(t,u),this.updateServiceLogs(e,t)}}),this.addActivationHandler(this.onActivate.bind(this))}}function he(){const e=l.locationService.getLocation(),t=new URLSearchParams(e.search),n=t.get(de),r=null==n?void 0:n.split("|");return{key:null==r?void 0:r[0],location:e,search:t}}function fe(e){return{body:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1}),bodyWrapper:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1}),container:(0,i.css)({display:"flex",flexDirection:"column",flexGrow:1,position:"relative"}),header:(0,i.css)({position:"absolute",right:0,top:"4px",zIndex:2}),headingWrapper:(0,i.css)({marginTop:e.spacing(1)}),loadingText:(0,i.css)({margin:0}),searchField:(0,i.css)({marginTop:e.spacing(1),position:"relative"}),searchPaginationWrap:(0,i.css)({[e.breakpoints.down("md")]:{marginTop:e.spacing(1),width:"100%"},alignItems:"center",display:"flex",flex:"1 0 auto",flexWrap:"wrap",label:"search-pagination-wrap"}),searchWrapper:(0,i.css)({[e.breakpoints.down("md")]:{alignItems:"flex-start",flexDirection:"column"},alignItems:"center",display:"flex",flexWrap:"wrap",label:"search-wrapper"})}}ie(ge,"Component",({model:e})=>{var t;const n=(0,d.useStyles2)(fe),{$data:r,body:i,paginationScene:s,tabs:l}=e.useState(),{data:c}=r.useState(),u=e.getSelectedTab(),p=(0,v.eY)(e),{label:g,value:f}=p.useState(),b=f&&".+"!==f,{labelsByVolume:m,labelsToQuery:y}=e.getLabels(null==c?void 0:c.series),w=(null==c?void 0:c.state)===o.LoadingState.Loading||(null==c?void 0:c.state)===o.LoadingState.Streaming||void 0===c,O=(null===(t=r.state.data)||void 0===t?void 0:t.state)===o.LoadingState.Error,E=e.formatPrimaryLabelForUI();let x=p.getValue().toString();".+"===x&&(x="");const P=(0,Q.sT)(x);var j;return a().createElement("div",{className:n.container},a().createElement("div",{className:n.bodyWrapper},l&&a().createElement(l.Component,{model:l}),a().createElement(d.Field,{className:n.searchField},a().createElement("div",{className:n.searchWrapper},a().createElement(S.f,{initialFilter:{icon:"filter",label:P,value:x},isLoading:w,value:x||g,onChange:t=>(t=>{e.onSearchServicesChange(t)})(t),selectOption:t=>{!function(e,t,n){const r=R(e,t,n);I(e,t,n),(0,h.ad)(r)}(u,t,e)},label:E,options:null!==(j=null==y?void 0:y.map(e=>({label:e,value:e})))&&void 0!==j?j:[]}),!w&&a().createElement("span",{className:n.searchPaginationWrap},s&&a().createElement(U.PageCount,{model:s,totalCount:y.length}),s&&a().createElement(U.Component,{model:s,totalCount:y.length})))),!w&&O&&a().createElement(T,null),!w&&!O&&b&&!(null==m?void 0:m.length)&&a().createElement(N,null),!w&&!O&&!b&&!(null==m?void 0:m.length)&&a().createElement($,{labelName:u}),!(!w&&O)&&a().createElement("div",{className:n.body},a().createElement(i.Component,{model:i}),a().createElement("div",{className:n.headingWrapper},s&&a().createElement(U.Component,{totalCount:y.length,model:s})))))})},8544:(e,t,n)=>{n.d(t,{Wg:()=>p,lI:()=>v,nz:()=>h});var r=n(5959),a=n.n(r),i=n(5953),s=n(2165),o=n(8428);function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){l(e,t,n[t])})}return e}function u(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}const d=`${n(9598).s_}.tableColumnWidths`;var p=function(e){return e.text="text",e.labels="labels",e.auto="auto",e}({});const g=(0,r.createContext)({bodyState:"auto",clearSelectedLine:()=>{},columns:{},columnWidthMap:{},filteredColumns:{},setBodyState:()=>{},setColumns:()=>{},setColumnWidthMap:()=>{},setFilteredColumns:()=>{}});const h=({children:e,clearSelectedLine:t,initialColumns:n,logsFrame:l,setUrlColumns:u,setUrlTableBodyState:p,urlTableBodyState:h})=>{const[v,b]=(0,r.useState)(f(n)),[m,y]=(0,r.useState)(null!=h?h:"auto"),[S,w]=(0,r.useState)(void 0),O=function(){let e={};const t=localStorage.getItem(d);if(t)try{return e=(0,o.Zt)(JSON.parse(t)),!1===e&&i.v.error(new o.QX("getColumnWidthsFromLocalStorage: unable to validate values in local storage"),{msg:"NarrowingError: error parsing table column widths from local storage"}),e}catch(e){i.v.error(e,{msg:"error parsing table column widths from local storage"})}return e}(),[E,x]=(0,r.useState)(O),P=(0,r.useCallback)(e=>{if(e){const t=f(e);b(t),u((e=>{let t=[];return Object.keys(e).forEach(n=>{e[n].active&&void 0!==e[n].index&&t.push(n)}),t.sort((t,n)=>{const r=e[t],a=e[n];return r.index-a.index}),t})(t))}},[u]),j=(0,r.useCallback)(e=>{y(e),p(e)},[p]);return(0,r.useEffect)(()=>{n&&P(n)},[n,P]),(0,r.useEffect)(()=>{h&&y(h)},[h]),(0,r.useEffect)(()=>{const e=function(e,t){if(!t)return void i.v.warn("missing dataframe, cannot set url state");const n=Object.keys(e).filter(t=>{var n;return null===(n=e[t])||void 0===n?void 0:n.active}).sort((t,n)=>{const r=e[t],a=e[n];return void 0!==r.index&&void 0!==a.index?r.index-a.index:0}),r=t.timeField,a=t.bodyField;if(r&&a||n.length){const e=[];return(null==r?void 0:r.name)&&e.push(r.name),(null==a?void 0:a.name)&&e.push(a.name),n.length?n:e}return[]}(v,l);if(null==e?void 0:e.length){0===Object.keys(v).filter(e=>v[e].active).length&&function(e,t,n){const r=c({},e);r[(0,s.fF)(n)]={active:!0,cardinality:1/0,index:0,percentOfLinesWithLabel:100,type:"TIME_FIELD"},r[(0,s.Il)(n)]={active:!0,cardinality:1/0,index:1,percentOfLinesWithLabel:100,type:"BODY_FIELD"},t(r)}(v,P,l),w(void 0)}},[v,l,w,P]),a().createElement(g.Provider,{value:{bodyState:m,clearSelectedLine:()=>{t()},columns:v,columnWidthMap:E,filteredColumns:S,setBodyState:j,setColumns:P,setColumnWidthMap:e=>{localStorage.setItem(d,JSON.stringify(e)),x(e)},setFilteredColumns:w}},e)},f=e=>{if("labelTypes"in e){const t=c({},e),{labelTypes:n}=t;return u(t,["labelTypes"])}return e};const v=()=>(0,r.useContext)(g)},9641:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(5959),a=n.n(r),i=n(6089),s=n(2007),o=n(4907);function l(e){return a().createElement("div",{className:o.CT?c.container:void 0},a().createElement(s.RadioButtonGroup,{options:[{description:"Show results in logs visualisation",label:"Logs",value:"logs"},{description:"Show results in table visualisation",label:"Table",value:"table"},{description:"Show results in json visualisation",label:"JSON",value:"json"}],size:"sm",value:e.vizType,onChange:e.onChange}))}const c={container:(0,i.css)({paddingRight:6})}},5091:(e,t,n)=>{n.d(t,{O:()=>a,R:()=>r});const r="detected_level",a=["timestamp","body","Time","Line"]},4702:(e,t,n)=>{n.d(t,{m:()=>s});var r=n(1269),a=n(6865);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class s extends a.n8{getValueOptions(e){return(0,r.of)(this.state.options)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){i(e,t,n[t])})}return e}({name:"",options:[],text:"",type:"custom",value:""},e))}}i(s,"Component",({model:e})=>(0,a.WY)({model:e}))},6464:(e,t,n)=>{n.d(t,{K:()=>g});var r=n(3241),a=n(6865),i=n(6854),s=n(8428),o=n(708),l=n(4351),c=n(5553),u=n(20);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){d(e,t,n[t])})}return e}class g{getJoinedLabelsFilters(){let{equalsFilters:e,notEqualsFilters:t,regexEqualFilters:n,regexNotEqualFilters:r}=this.getCombinedLabelFilters();const a=[];return[e,t,n,r].filter(e=>e).forEach(e=>{const t=this.joinCombinedFiltersValues(e,"|");for(const n in e){const r=e[n].operator;a.push({key:n,operator:r,value:t[n]})}}),a}getExpr(){let{equalsFilters:e,gteFilters:t,gtFilters:n,lteFilters:r,ltFilters:a,notEqualsFilters:i,regexEqualFilters:s,regexNotEqualFilters:o}=this.getCombinedLabelFilters();this.options.debug;const l=this.buildLabelsLogQLFromFilters({equalsFilters:e,gteFilters:t,gtFilters:n,lteFilters:r,ltFilters:a,notEqualsFilters:i,regexEqualFilters:s,regexNotEqualFilters:o});var c;return l?(null!==(c=this.options.prefix)&&void 0!==c?c:"")+l:""}getLabelsExpr(e){return this.options=p({},{decodeFilters:!1,filterType:"indexed",joinMatchFilters:!0},e),this.getExpr()}getMetadataExpr(e){return this.options=p({},{decodeFilters:!1,filterSeparator:" |",filterType:"field",joinMatchFilters:!1,prefix:"| "},e),this.getExpr()}getLevelsExpr(e){return this.options=p({},{decodeFilters:!1,filterSeparator:" |",filterType:"field",joinMatchFilters:!1,prefix:"| "},e),this.getExpr()}getFieldsExpr(e){return this.options=p({},{decodeFilters:!0,filterSeparator:" |",filterType:"field",joinMatchFilters:!1,prefix:"| "},e),this.getExpr()}buildLabelsLogQLFromFilters({equalsFilters:e,gteFilters:t,gtFilters:n,lteFilters:a,ltFilters:s,notEqualsFilters:o,regexEqualFilters:l,regexNotEqualFilters:c}){let u,d,p,g,h,f,v,b;const m=[];var y;this.options.joinMatchFilters?(u=this.joinCombinedFiltersValues(e,"|"),d=this.joinCombinedFiltersValues(o,"|"),p=this.joinCombinedFiltersValues(l,"|"),g=this.joinCombinedFiltersValues(c,"|"),m.push(...this.buildJoinedFilters(u,i.KQ.Equal)),m.push(...this.buildJoinedFilters(d,i.KQ.NotEqual)),m.push(...this.buildJoinedFilters(p,i.KQ.RegexEqual)),m.push(...this.buildJoinedFilters(g,i.KQ.RegexNotEqual))):(u=this.getFilterValues(e),d=this.getFilterValues(o),p=this.getFilterValues(l),g=this.getFilterValues(c),m.push(...this.buildFilter(u,i.KQ.Equal)),m.push(...this.buildFilter(d,i.KQ.NotEqual)),m.push(...this.buildFilter(p,i.KQ.RegexEqual)),m.push(...this.buildFilter(g,i.KQ.RegexNotEqual))),h=this.getFilterValues(s),f=this.getFilterValues(a),v=this.getFilterValues(n),b=this.getFilterValues(t),m.push(...this.buildFilter(h,i.Rk.lt)),m.push(...this.buildFilter(f,i.Rk.lte)),m.push(...this.buildFilter(v,i.Rk.gt)),m.push(...this.buildFilter(b,i.Rk.gte)),this.options.debug;const S=(0,r.trim)(this.combineValues(m,`${null!==(y=this.options.filterSeparator)&&void 0!==y?y:","} `));return this.options.debug,S}getCombinedLabelFilters(){const{[i.KQ.Equal]:e,[i.KQ.NotEqual]:t,[i.KQ.RegexEqual]:n,[i.KQ.RegexNotEqual]:r,[i.Rk.lt]:a,[i.Rk.lte]:s,[i.Rk.gt]:o,[i.Rk.gte]:l}=this.groupFiltersByKey(this.filters);let c,u,d,p,g,h,f,v;return this.options.joinMatchFilters?(c=this.combineFiltersValues(e,i.KQ.RegexEqual),u=this.combineFiltersValues(t,i.KQ.RegexNotEqual),d=this.combineFiltersValues(n),p=this.combineFiltersValues(r)):(c=this.combineFiltersValues(e),u=this.combineFiltersValues(t),d=this.combineFiltersValues(n),p=this.combineFiltersValues(r)),g=this.combineFiltersValues(a),h=this.combineFiltersValues(s),f=this.combineFiltersValues(o),v=this.combineFiltersValues(l),this.options.debug,this.options.joinMatchFilters&&(c&&(d=this.mergeFilters(i.KQ.RegexEqual,c,d),c=this.removeStaleOperators(c,i.KQ.Equal)),u&&(p=this.mergeFilters(i.KQ.RegexNotEqual,u,p),u=this.removeStaleOperators(u,i.KQ.NotEqual))),{equalsFilters:c,gteFilters:v,gtFilters:f,lteFilters:h,ltFilters:g,notEqualsFilters:u,regexEqualFilters:d,regexNotEqualFilters:p}}buildFilter(e,t){const n=[];for(const r in e){const a=[],i=e[r];(0,o.iu)(t)?i.forEach(e=>a.push(this.buildFilterString(r,t,e,""))):i.forEach(e=>a.push(this.buildFilterString(r,t,e))),(0,o.BG)(t)?n.push(a.join(` ${this.positiveFilterValueSeparator} `)):n.push(a.join(` ${this.negativeFilterValueSeparator} `))}return n}buildJoinedFilters(e,t){const n=[];for(const r in e)n.push(this.buildFilterString(r,t,e[r]));return n}removeStaleOperators(e,t){const n={};return Object.keys(e).forEach(r=>{e[r].operator===t&&(n[r]=e[r])}),n}mergeFilters(e,t,n){return Object.keys(t).filter(n=>t[n].operator===e).map(e=>({key:e,values:t[e].values})).forEach(r=>{void 0===n&&(n={[r.key]:{operator:e,values:[]}}),void 0===n[r.key]&&(n[r.key]={operator:e,values:[]}),n[r.key].values.push(...this.mergeCombinedFiltersValues(t[r.key],e))}),n}mergeCombinedFiltersValues(e,t){var n;const r=[];return e.operator===t&&(null===(n=e.values)||void 0===n?void 0:n.length)&&r.push(...e.values),r}joinCombinedFiltersValues(e,t){const n={};for(const r in e)e[r].values.length&&(n[r]=this.combineValues(e[r].values,t));return n}getFilterValues(e){const t={};for(const n in e)e[n].values.length&&(t[n]=e[n].values);return t}combineValues(e,t){return e.join(`${t}`)}combineFiltersValues(e,t){let n={};for(const i in e){if(!e[i].length)continue;const o=(0,s.kR)(e[i][0].operator),l=null!=t?t:o,c=e[i][0];if(n[i]={operator:l,values:[]},1===e[i].length){var r;const e=this.escapeFieldValue(c.operator,c.value,null!==(r=c.valueLabels)&&void 0!==r?r:[]);n[i]={operator:o,values:[e]},this.options.debug}else{const t=this.escapeFieldValues(i,e,l);var a;if(void 0===n[i].operator)n[i]={operator:l,values:t};else null===(a=n[i].values)||void 0===a||a.push(...t)}}return n}escapeFieldValues(e,t,n){return t[e].map(e=>{var t;return this.escapeFieldValue(n,e.value,null!==(t=e.valueLabels)&&void 0!==t?t:[])})}escapeFieldValue(e,t,n){const r=(0,u.zE)(t);if(this.options.decodeFilters){t=(0,c.bu)({value:t,valueLabels:n}).value}return t===u.ZO?(this.options.debug,t):r?(this.options.debug,a.Go.escapeLabelValueInExactSelector((0,u.Dx)(t))):(0,o.SM)(e)?(this.options.debug,a.Go.escapeLabelValueInRegexSelector(t)):(this.options.debug,a.Go.escapeLabelValueInExactSelector(t))}buildFilterString(e,t,n,r='"'){if(n===u.ZO)return`${e}${t}${n}`;const a=`${e}${t}${r}${n}${r}`;return this.options.debug,a}groupFiltersByKey(e){let t=e.filter(e=>{var t;return!(null===(t=this.options.ignoreKeys)||void 0===t?void 0:t.includes(e.key))||(0,o.SM)(e.operator)});"indexed"===this.options.filterType&&t.length<1&&(t=e);const n=t.filter(e=>(0,o.BG)(e.operator)&&!(0,o.SM)(e.operator)),a=t.filter(e=>(0,o.BG)(e.operator)&&(0,o.SM)(e.operator)),s=t.filter(e=>(0,o.Lw)(e.operator)&&!(0,o.SM)(e.operator)),l=t.filter(e=>(0,o.Lw)(e.operator)&&(0,o.SM)(e.operator)),c=t.filter(e=>e.operator===i.w7.gt),u=t.filter(e=>e.operator===i.w7.gte),d=t.filter(e=>e.operator===i.w7.lt),p=t.filter(e=>e.operator===i.w7.lte),g=(0,r.groupBy)(n,e=>e.key),h=(0,r.groupBy)(a,e=>e.key),f=(0,r.groupBy)(s,e=>e.key),v=(0,r.groupBy)(l,e=>e.key),b=(0,r.groupBy)(c,e=>e.key),m=(0,r.groupBy)(u,e=>e.key),y=(0,r.groupBy)(d,e=>e.key),S=(0,r.groupBy)(p,e=>e.key);return{[i.w7.Equal]:g,[i.w7.RegexEqual]:h,[i.w7.NotEqual]:f,[i.w7.RegexNotEqual]:v,[i.w7.gt]:b,[i.w7.gte]:m,[i.w7.lt]:y,[i.w7.lte]:S}}constructor(e,t={decodeFilters:!1,filterType:"field",joinMatchFilters:!0}){d(this,"filters",void 0),d(this,"options",void 0),d(this,"positiveFilterValueSeparator","or"),d(this,"negativeFilterValueSeparator","|"),this.filters=e,this.options=t,this.options.debug||(this.options.debug=(0,l.Rb)())}}},4586:(e,t,n)=>{n.d(t,{IR:()=>i,K4:()=>d,N0:()=>u,Rl:()=>p,VM:()=>l,nu:()=>o,tZ:()=>c,yC:()=>s});var r=n(6089),a=n(7781);function i(e,t){if(1===t.length)return e[t[0]];const n=t.shift();return void 0!==n?i(e[n],t):void 0}const s=(e,t)=>(0,a.dateTimeFormat)(e,{defaultWithMS:!0,timeZone:t}),o=e=>({labelButtonsWrap:(0,r.css)({color:"var(--json-tree-label-color)",display:"inline-flex",marginLeft:e.spacing(.5)}),JSONNestedLabelWrapStyles:(0,r.css)({alignItems:"center",color:"var(--json-tree-label-color)",display:"inline-flex",marginLeft:e.spacing(.5)}),JSONLabelWrapStyles:(0,r.css)({alignItems:"center",color:"var(--json-tree-label-color)",display:"inline-flex",marginLeft:e.spacing(1.25)})}),l=(0,r.css)({alignItems:"center",display:"inline-flex"}),c=(0,r.css)({alignItems:"center",display:"flex",overflowX:"auto"}),u=(0,r.css)({marginLeft:"0.5em",marginRight:"0.5em"}),d=(0,r.css)({marginLeft:"0.5em"}),p=(0,r.css)({display:"flex",flexWrap:"nowrap",fontSize:"12px",textWrap:"nowrap"})},8729:(e,t,n)=>{n.d(t,{R:()=>p});var r=n(7781),a=n(8531),i=n(6865),s=n(1532),o=n(5548);function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){l(e,t,n[t])})}return e}function u(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function d(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}class p extends i.H9{getReadonlyFilters(){return this.readonlyFilters}constructor(e){const{readonlyFilters:t}=e;super(c({filters:t},d(e,["readonlyFilters"]))),l(this,"readonlyFilters",void 0),l(this,"setReadOnlyFilters",e=>{let t=!1;const n=e.filters.map(e=>{var n;const r=null===(n=this.readonlyFilters)||void 0===n?void 0:n.find(t=>t.key===e.key&&t.operator===e.operator&&t.value===e.value&&t.readOnly!==e.readOnly&&t.origin!==e.origin);return r?(t=!0,u(c({},e),{origin:r.origin,readOnly:r.readOnly})):e});t&&this.setState({filters:n})}),l(this,"addReadonlyFilters",e=>{var t;if(null===(t=this.readonlyFilters)||void 0===t?void 0:t.length){const t=new Set;if((0,o.EW)(e.filters,t),!this.readonlyFilters.every(e=>t.has((0,o.Qi)(e)))){var n,i;const t=new Set,s=[...this.readonlyFilters,...e.filters].filter(e=>{const n=(0,o.Qi)(e);return!t.has(n)&&(t.add(n),!0)});this.setState({filters:s});(0,a.getAppEvents)().publish({payload:[`Cannot remove ${null===(i=this.readonlyFilters)||void 0===i||null===(n=i[0])||void 0===n?void 0:n.origin} managed filters!`],type:r.AppEvents.alertError.name})}}}),this.readonlyFilters=t,this._subs.add(this.subscribeToState((e,t)=>{var n;(null===(n=this.readonlyFilters)||void 0===n?void 0:n.length)&&!(0,s.B)(e.filters,t.filters)&&(this.addReadonlyFilters(e),this.setReadOnlyFilters(e))}))}}},4509:(e,t,n)=>{n.d(t,{EE:()=>i,NO:()=>s,ir:()=>o});var r=n(8531),a=n(2533);const i=(e,t,n,i=!1)=>{const s=((e,t)=>`${a.id.replace(/-/g,"_")}_${e}_${t}`)(e,t);if(i){if(sessionStorage.getItem(s))return;sessionStorage.setItem(s,"1")}(0,r.reportInteraction)(s,n)},s={all:"all",service_details:"service_details",service_selection:"service_selection"},o={[s.service_selection]:{add_to_filters:"add_to_filters",aggregated_metrics_toggled:"aggregated_metrics_toggled",search_services_changed:"search_services_changed",service_selected:"service_selected"},[s.service_details]:{action_view_changed:"action_view_changed",add_to_filters_in_breakdown_clicked:"add_to_filters_in_breakdown_clicked",add_to_filters_in_json_panel:"add_to_filters_in_json_panel",change_line_format_in_json_panel:"change_line_format_in_json_panel",change_viz_type:"change_viz_type",label_in_panel_summary_clicked:"label_in_panel_summary_clicked",layout_type_changed:"layout_type_changed",level_in_logs_volume_clicked:"level_in_logs_volume_clicked",logs_clear_displayed_fields:"logs_clear_displayed_fields",logs_on_query_complete:"logs_on_query_complete",logs_on_query_error:"logs_on_query_error",logs_detail_filter_applied:"logs_detail_filter_applied",logs_popover_line_filter:"logs_popover_line_filter",logs_toggle_displayed_field:"logs_toggle_displayed_field",logs_visualization_toggle:"logs_visualization_toggle",open_in_explore_clicked:"open_in_explore_clicked",pattern_field_clicked:"pattern_field_clicked",pattern_removed:"pattern_removed",pattern_selected:"pattern_selected",search_string_in_logs_changed:"search_string_in_logs_changed",search_string_in_variables_changed:"search_string_in_variables_changed",select_field_in_breakdown_clicked:"select_field_in_breakdown_clicked",toggle_error_panels:"toggle_error_panels",value_breakdown_sort_change:"value_breakdown_sort_change",wasm_not_supported:"wasm_not_supported",embedded_go_to_explore_clicked:"embedded_go_to_explore_clicked",visualization_init:"visualization_init",fields_panel_type_toggle:"fields_panel_type_toggle"},[s.all]:{interval_too_long:"interval_too_long",open_in_explore_menu_clicked:"open_in_explore_menu_clicked"}}},1532:(e,t,n)=>{n.d(t,{B:()=>i,n:()=>s});var r=n(3241),a=n.n(r);const i=(e,t)=>{if(typeof e!=typeof t)return!1;const n=new Set(e),r=new Set(t);return n.size===r.size&&a().isEqual(n,r)},s=(e,t)=>typeof e==typeof t&&a().isEqual(e,t)},1296:(e,t,n)=>{n.r(t),n.d(t,{DETECTED_FIELDS_CARDINALITY_NAME:()=>O,DETECTED_FIELDS_NAME_FIELD:()=>w,DETECTED_FIELDS_PARSER_NAME:()=>E,DETECTED_FIELDS_PATH_NAME:()=>P,DETECTED_FIELDS_TYPE_NAME:()=>x,MAX_PATTERNS_LIMIT:()=>j,WRAPPED_LOKI_DS_UID:()=>S,WrappedLokiDatasource:()=>C,default:()=>F,mergeLokiSamples:()=>L});var r=n(1269),a=n(7781),i=n(8531),s=n(6865),o=n(8502),l=n(5953),c=n(9330),u=n(9598),d=n(7985),p=n(5719),g=n(9594),h=n(20);function f(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function v(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){f(i,r,a,s,o,"next",e)}function o(e){f(i,r,a,s,o,"throw",e)}s(void 0)})}}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){b(e,t,n[t])})}return e}function y(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const S="wrapped-loki-ds-uid",w="name",O="cardinality",E="parser",x="type",P="jsonPath",j=500;class C extends s.UU{query(e){return new r.Observable(t=>{var n;if(!(null===(n=e.scopedVars)||void 0===n?void 0:n.__sceneObject))throw new Error("Scene object not found in request");(0,i.getDataSourceSrv)().get((0,p.U4)(e.scopedVars.__sceneObject.valueOf())).then(n=>v(function*(){var r;if(!(n instanceof i.DataSourceWithBackend&&"interpolateString"in n&&"getTimeRangeParams"in n))throw new Error("Invalid datasource!");const a=n;e.targets=null===(r=e.targets)||void 0===r?void 0:r.map(e=>(e.datasource=a,e));const s=new Set;if(e.targets.forEach(e=>{var t;s.add(null!==(t=e.resource)&&void 0!==t?t:"")}),1!==s.size)throw new Error("A request cannot contain queries to multiple endpoints");switch(e.targets[0].resource){case"volume":yield this.getVolume(e,a,t);break;case"patterns":yield this.getPatterns(e,a,t);break;case"detected_labels":yield this.getDetectedLabels(e,a,t);break;case"detected_fields":yield this.getDetectedFields(e,a,t);break;case"labels":yield this.getLabels(e,a,t);break;default:this.getData(e,a,t)}}).call(this))})}getData(e,t,n){const r=i.config.featureToggles.exploreLogsShardSplitting,a=y(m({},e),{targets:t.interpolateVariablesInQueries(e.targets,e.scopedVars).map(e=>y(m({},e),{expr:(0,d.VT)(e.expr),resource:void 0}))});return(!1!==(0,c.E2)(a)&&r?(0,g.C)(t,a):t.query(a)).subscribe(n),n}getPatterns(e,t,n){return v(function*(){const r=e.targets.filter(e=>"patterns"===e.resource);if(1!==r.length)throw new Error("Patterns query can only have a single target!");const{expression:i,interpolatedTarget:s}=this.interpolate(t,r,e);n.next({data:[],state:a.LoadingState.Loading});try{var o;const r=t.getResource("patterns",{end:e.range.to.utc().toISOString(),query:i,start:e.range.from.utc().toISOString(),step:e.interval},{headers:{"X-Query-Tags":`Source=${u.s_}`},requestId:null!==(o=e.requestId)&&void 0!==o?o:"patterns"}),l=yield r,c=null==l?void 0:l.data.slice(0,j);let d=-1/0,p=0,g={};c.forEach(e=>{g[e.pattern]?(e.level&&g[e.pattern].levels.push(e.level),g[e.pattern].samples=L(e.samples,g[e.pattern].samples)):g[e.pattern]={samples:e.samples,levels:e.level?[e.level]:[]}});const h=Object.keys(g).map(e=>{const t=[],n=[];let r=0;const i=g[e];return i.samples.forEach(([e,a])=>{t.push(1e3*e),n.push(a),a>d&&(d=a),a<p&&(p=a),a>d&&(d=a),a<p&&(p=a),r+=a}),(0,a.createDataFrame)({fields:[{config:{},name:"time",type:a.FieldType.time,values:t},{config:{},name:e,type:a.FieldType.number,values:n}],meta:{custom:{sum:r,level:i.levels},preferredVisualisationType:"graph"},name:e,refId:s.refId})});h.sort((e,t)=>{var n,r,a,i;return(null===(r=t.meta)||void 0===r||null===(n=r.custom)||void 0===n?void 0:n.sum)-(null===(i=e.meta)||void 0===i||null===(a=i.custom)||void 0===a?void 0:a.sum)}),n.next({data:h,state:a.LoadingState.Done})}catch(e){n.next({data:[],state:a.LoadingState.Error})}return n}).call(this)}interpolate(e,t,n){const r=e.interpolateVariablesInQueries(t,n.scopedVars);if(!r.length)throw new Error("Datasource failed to interpolate query!");const a=r[0];return{expression:(0,d.VT)(a.expr),interpolatedTarget:a}}getDetectedLabels(e,t,n){return v(function*(){const r=e.targets.filter(e=>"detected_labels"===e.resource);if(1!==r.length)throw new Error("Detected labels query can only have a single target!");let{expression:i,interpolatedTarget:s}=this.interpolate(t,r,e);"{}"===i&&(i=""),n.next({data:[],state:a.LoadingState.Loading});try{var l,c,d;const r=null===(c=(yield t.getResource("detected_labels",{end:e.range.to.utc().toISOString(),query:i,start:e.range.from.utc().toISOString()},{headers:{"X-Query-Tags":`Source=${u.s_}`},requestId:null!==(d=e.requestId)&&void 0!==d?d:"detected_labels"})).detectedLabels)||void 0===c||null===(l=c.filter(e=>!o.rm.includes(e.label)))||void 0===l?void 0:l.sort((e,t)=>(0,o.p_)(e,t)),p=null==r?void 0:r.map(e=>({name:e.label,values:[e.cardinality]})),g=(0,a.createDataFrame)({fields:null!=p?p:[],refId:s.refId});n.next({data:[g],state:a.LoadingState.Done})}catch(e){n.next({data:[],state:a.LoadingState.Error})}return n}).call(this)}getDetectedFields(e,t,n){return v(function*(){const r=e.targets.filter(e=>"detected_fields"===e.resource);if(1!==r.length)throw new Error("Detected fields query can only have a single target!");n.next({data:[],state:a.LoadingState.Loading});const{expression:i,interpolatedTarget:s}=this.interpolate(t,r,e);try{var c,d;const r=yield t.getResource("detected_fields",{end:e.range.to.utc().toISOString(),query:i,start:e.range.from.utc().toISOString()},{headers:{"X-Query-Tags":`Source=${u.s_}`},requestId:null!==(d=e.requestId)&&void 0!==d?d:"detected_fields"}),l={config:{},name:w,type:a.FieldType.string,values:[]},p={config:{},name:O,type:a.FieldType.number,values:[]},g={config:{},name:E,type:a.FieldType.string,values:[]},h={config:{},name:x,type:a.FieldType.string,values:[]},f={config:{},name:P,type:a.FieldType.string,values:[]};null===(c=r.fields)||void 0===c||c.forEach(e=>{var t;o.$R.includes(e.label)||(l.values.push(e.label),p.values.push(e.cardinality),g.values.push((null===(t=e.parsers)||void 0===t?void 0:t.length)?e.parsers.join(", "):"structuredMetadata"),h.values.push(e.type),f.values.push(e.jsonPath))});const v=(0,a.createDataFrame)({fields:[l,p,g,h,f],refId:s.refId});n.next({data:[v],state:a.LoadingState.Done})}catch(e){l.v.error(e,{msg:"Detected fields error"}),n.next({data:[],state:a.LoadingState.Error})}return n}).call(this)}getVolume(e,t,n){return v(function*(){if(1!==e.targets.length)throw new Error("Volume query can only have a single target!");const r=e.targets[0],i=r.primaryLabel;if(!i)throw new Error("Primary label is required for volume queries!");const s=t.interpolateVariablesInQueries([r],e.scopedVars),o=(0,d.VT)(s[0].expr.replace(".*.*",".+"));n.next({data:[],state:a.LoadingState.Loading});try{var c,p,g;const r=yield t.getResource("index/volume",{end:e.range.to.utc().toISOString(),limit:5e3,query:o,start:e.range.from.utc().toISOString()},{headers:{"X-Query-Tags":`Source=${u.s_}`},requestId:null!==(g=e.requestId)&&void 0!==g?g:"volume"});null==r||r.data.result.sort((e,t)=>{const n=e.value[1],r=t.value[1];return Number(r)-Number(n)});const s=(0,a.createDataFrame)({fields:[{name:h.OX,values:null==r||null===(c=r.data.result)||void 0===c?void 0:c.map(e=>e.metric[i])},{name:"volume",values:null==r||null===(p=r.data.result)||void 0===p?void 0:p.map(e=>Number(e.value[1]))}]});n.next({data:[s]})}catch(e){l.v.error(e),n.next({data:[],state:a.LoadingState.Error})}return n.complete(),n})()}getLabels(e,t,n){return v(function*(){if(1!==e.targets.length)throw new Error("Volume query can only have a single target!");try{var r;const i=yield t.getResource("labels",{end:e.range.to.utc().toISOString(),start:e.range.from.utc().toISOString()},{headers:{"X-Query-Tags":`Source=${u.s_}`},requestId:null!==(r=e.requestId)&&void 0!==r?r:"labels"}),s=(0,a.createDataFrame)({fields:[{name:"labels",values:null==i?void 0:i.data}]});n.next({data:[s],state:a.LoadingState.Done})}catch(e){n.next({data:[],state:a.LoadingState.Error})}return n.complete(),n})()}testDatasource(){return Promise.resolve({message:"Data source is working",status:"success",title:"Success"})}constructor(e,t){super(e,t)}}function L(e,t){const n=new Map;e.forEach(e=>{n.set(e[0],e[1])}),t.forEach(e=>{if(n.has(e[0])){const t=n.get(e[0]);n.set(e[0],e[1]+(null!=t?t:0))}else n.set(e[0],e[1])});const r=Array.from(n);return r.sort((e,t)=>e[0]-t[0]),r}let _=!1;const F=function(){_||(_=!0,s.Go.registerRuntimeDataSource({dataSource:new C("wrapped-loki-ds",S)}))}},6838:(e,t,n)=>{n.d(t,{O:()=>l,m:()=>o});var r=n(376),a=n(5953),i=n(5553),s=n(20);function o(e,t,n=!0){const a=(0,i.ir)(e);let o="";n&&t===s.e4&&(o=`| ${s.e4} != ""`);const l=a.state.filters,c=(0,r.k$)(a);if(l.length){if("mixed"===c)return`sum(count_over_time({${s.S1}} ${o} ${s.S6} ${s.sC} ${s.rl} ${s.YN} ${s.Oc} ${s.jf} [$__auto])) by (${t})`;if("json"===c)return`sum(count_over_time({${s.S1}} ${o} ${s.S6} ${s.sC} ${s.rl} ${s.VL} ${s.Oc} ${s.jf} [$__auto])) by (${t})`;if("logfmt"===c)return`sum(count_over_time({${s.S1}} ${o} ${s.S6} ${s.sC} ${s.rl} ${s.mF} ${s.Oc} ${s.jf} [$__auto])) by (${t})`}return`sum(count_over_time({${s.S1}} ${o} ${s.S6} ${s.sC} ${s.rl} ${s.Oc} ${s.jf} [$__auto])) by (${t})`}function l(e){switch(e){case s._Y:return s.Sy;case s.sL:return s.fJ;default:const t=new Error(`Unknown variable type: ${e}`);throw a.v.error(t,{msg:`getFieldsTagValuesExpression: Unknown variable type: ${e}`,variableType:e}),t}}},696:(e,t,n)=>{n.d(t,{_J:()=>u,wy:()=>d});var r=n(6865),a=n(8714),i=n(577),s=n(173),o=n(4351),l=n(5553);function c(e){const t=r.jh.getAncestor(e,a.P);r.jh.findAllObjects(t,e=>e instanceof i.p).forEach(e=>e.forceRender());r.jh.findDescendents(t,s.y).forEach(e=>e.forceRender())}function u(e,t,n){const r=(0,l.S9)(n).getValue();(0,o.OB)(r,e,t),c(n)}function d(e,t,n){const r=(0,l.S9)(n).getValue();(0,o.cC)(r,e,t),c(n)}},376:(e,t,n)=>{n.d(t,{$1:()=>O,Ak:()=>B,JI:()=>F,Jl:()=>T,Mz:()=>M,OE:()=>L,Qg:()=>S,Ri:()=>j,VN:()=>x,Z6:()=>D,Zp:()=>C,at:()=>$,gE:()=>N,k$:()=>_,kz:()=>P,ph:()=>k,vF:()=>A});var r=n(1269),a=n(7781),i=n(6865),s=n(2007),o=n(5700),l=n(9721),c=n(4247),u=n(5953),d=n(2165),p=n(3142),g=n(4907),h=n(5553),f=n(20),v=n(9405);const b=e=>{if(e){if(Object.values(a.ReducerID).includes(e))return e}};function m(e){switch(e){case"json":return"json";case"logfmt":return"logfmt";case"":case"structuredMetadata":return"structuredMetadata";default:return"mixed"}}function y(e){switch(e){case"int":case"float":case"duration":case"boolean":case"bytes":return e;default:return"string"}}function S(e){var t;const n=new Set(null!==(t=null==e?void 0:e.map(e=>e.toString()))&&void 0!==t?t:[]);n.delete("structuredMetadata");const r=Array.from(n);return 1===r.length?m(r[0]):0===n.size?"structuredMetadata":"mixed"}function w(e){return null==e?void 0:e.fields[0]}function O(e){return null==e?void 0:e.fields[2]}function E(e){return null==e?void 0:e.fields[3]}function x(e){return null==e?void 0:e.fields[4]}function P(e,t){var n;const r=i.jh.getAncestor(e,l.Mn),a=(0,l.tn)(null===(n=r.state.$data)||void 0===n?void 0:n.state.data),s=null==a?void 0:a.fields.find(e=>"labels"===e.name),o=null==s?void 0:s.values.reduce((e,n)=>((null==n?void 0:n[t])&&e++,e),0);if(void 0!==o&&a&&a.length>0){const n=(o/a.length*100).toLocaleString();let r=`${t} exists on ${n}% of ${null==a?void 0:a.length} sampled log lines`;const i=(0,l.rD)(e),s=function(e,t){const n=w(t),r=function(e){return null==e?void 0:e.fields[1]}(t),a=null==n?void 0:n.values.findIndex(t=>t===e);var i;if(void 0!==a&&-1!==a)return null==r||null===(i=r.values)||void 0===i?void 0:i[a]}(t,i);return void 0!==s&&(r+=`, and contains ${s} unique values`),{cardinality:s?parseInt(s,10):void 0,description:r,sparsity:parseInt(n,10)}}return{cardinality:void 0,description:void 0,sparsity:void 0}}function j(e,t){var n;const r=(0,l.rD)(t),a=O(r),i=w(r),s=null==i?void 0:i.values.indexOf(e);var o;const c=void 0!==s&&-1!==s?m(null!==(o=null==a||null===(n=a.values)||void 0===n?void 0:n[s])&&void 0!==o?o:""):void 0;return void 0===c?(u.v.warn("missing parser, using mixed format for",{fieldName:e}),"mixed"):c}function C(e,t,n,a,l){return(c,u)=>{const d=b(a.state.sortBy),p=i.d0.timeseries().setOption("legend",{showLegend:!1}).setCustomFieldConfig("fillOpacity",9).setTitle(e(c)).setShowMenuAlways(!0).setData(new i.Es({transformations:[()=>function(e){return t=>t.pipe((0,r.map)(()=>[e]))}(c)]})).setOverrides(g.jC).setMenu(new o.GD({investigationOptions:{fieldName:e(c),frame:c,labelName:l}})).setHeaderActions([new v.oR({frame:c,hideExclude:l===f.e4,variableName:n})]);return t===s.DrawStyle.Bars&&p.setCustomFieldConfig("stacking",{mode:s.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setOverrides(g.jC).setCustomFieldConfig("drawStyle",s.DrawStyle.Bars),d&&(p.setOption("legend",{calcs:[d],showLegend:!0}),p.setDisplayName(" ")),new i.xK({body:p.build()})}}function L(e,t,n){const r=e?(0,p.E)(t,e):c.H.Parsed;if(r)return function(e,t){switch(e){case c.H.Indexed:return f.MB;case c.H.Parsed:return f.mB;case c.H.StructuredMetadata:return t===f.e4?f._Y:f._P;default:{const n=new Error(`Invalid label type for ${t}`);throw u.v.error(n,{msg:`Invalid label type for ${t}`,type:e}),n}}}(r,t);const a=j(t,n);return"structuredMetadata"===a?f._P:(u.v.warn("unable to determine label variable, falling back to parsed field",{key:t,parserForThisField:null!=a?a:""}),f.mB)}function _(e){return S(e.state.filters.map(e=>(0,h.bu)(e).parser))}function F(e){return"duration"===e||"bytes"===e||"float"===e}function k(e,t){var n;const r=w(t),a=E(t),i=null==r?void 0:r.values.indexOf(e);return void 0!==i&&-1!==i?y(null==a||null===(n=a.values)||void 0===n?void 0:n[i]):void 0}function T(e,t,n,r){var a,i,s;const o=w(n),l=E(n),c=O(n),d=x(n),p=null==o?void 0:o.values.indexOf(e),g=void 0!==p&&-1!==p?m(null==c||null===(a=c.values)||void 0===a?void 0:a[p]):"mixed",f=void 0!==p&&-1!==p?y(null==l||null===(i=l.values)||void 0===i?void 0:i[p]):void 0,v=void 0!==p&&-1!==p?null==d||null===(s=d.values)||void 0===s?void 0:s[p]:void 0,b=t.state.filters.map(e=>{var t;const n=null==o?void 0:o.values.indexOf(e.key),r=(0,h.bu)(e);if(r.parser)return r.parser;var a;const i=void 0!==n&&-1!==n?m(null!==(a=null==c||null===(t=c.values)||void 0===t?void 0:t[n])&&void 0!==a?a:"mixed"):void 0;return null!=i?i:"mixed"}),P=S([...b,g]);let j="",C="";"structuredMetadata"===g?C=`| ${e}!=""`:j=`| ${e}!=""`;const L={fieldExpressionToAdd:j,fieldType:f,parser:P,structuredMetadataToAdd:C};if(("json"===P||"mixed"===P)&&v){const n=M(v),a=t.state.filters,i=null==r?void 0:r.state.filters;a.every(e=>null==i?void 0:i.some(t=>e.key===t.key))?L.jsonParserPropToAdd=(null==r?void 0:r.state.filters.length)?`${e}="${n}",`:`${e}="${n}"`:u.v.warn("missing json path for field filters",{fieldFilters:JSON.stringify(a),jsonFilters:JSON.stringify(i)})}return function(e,t){return t.fieldType&&["bytes","duration"].includes(t.fieldType)?`avg_over_time(${(0,h.DX)(t)} | unwrap `+t.fieldType+`(${e}) | __error__="" [$__auto]) by ()`:t.fieldType&&"float"===t.fieldType?`avg_over_time(${(0,h.DX)(t)} | unwrap `+e+' | __error__="" [$__auto]) by ()':`sum by (${e}) (count_over_time(${(0,h.DX)(t)} [$__auto]))`}(e,L)}function D(e){return e===d.wu||e===d.eB}function N(e){return e===d.bz}function $(e){return e===d.wd}function A(e){return e===d.BR}function B(e){const t=(0,h.ir)(e),n=(0,h.Gc)(e),r=(0,h.U2)(e);t.state.filters.length||r.state.filters.length||n.setState({filters:[]})}function M(e){var t;return null===(t=e.map(e=>`[\\"${e}\\"]`))||void 0===t?void 0:t.join("")}},8502:(e,t,n)=>{n.d(t,{$R:()=>u,AY:()=>v,OH:()=>f,Oc:()=>y,X:()=>b,dD:()=>p,gR:()=>m,p_:()=>d,rd:()=>h,rm:()=>g,uE:()=>c});var r=n(3241),a=n(376),i=n(6854),s=n(42),o=n(5553),l=n(20);const c=" ",u=["level_extracted",l.e4,"level"];function d(e,t){return 1===e.cardinality?1:1===t.cardinality?-1:e.cardinality-t.cardinality}function p(e){const t=[...e];e.includes(l.e4)||t.unshift(l.e4);const n=t.map(e=>({label:e,value:String(e)}));return[{label:"All",value:l.To},...n]}const g=["__aggregated_metric__","__stream_shard__"];function h(e){const t=[...e].map(e=>({label:e,value:String(e)}));return[{label:"All",value:l.To},...t]}function f(e){var t;return((0,l.zE)(e.value)?(0,l.Dx)(e.value):e.value)===(null===(t=e.valueLabels)||void 0===t?void 0:t[0])}function v(e){const t=(0,o.U2)(e).state.filters,n=(0,o.ir)(e),r=(0,o.Gc)(e),a=new Set;t.forEach(e=>{a.add(e.key)});const i=new Set;n.state.filters.forEach(e=>i.add(e.key));const s=r.state.filters.filter(e=>{let n=!1,r=[];n=t.some(t=>{r.push(t.key);return r.join("_")===e.key});return i.has(e.key)||n});r.setState({filters:s})}function b(e){const t=(0,o.U2)(e);v(e),t.setState({filters:[]})}function m(e,t){const n=(0,o.Gc)(e),l=S(t),c=function(e){return e.match(s.HO)&&(e=e.replace(s.HO,"_")),e}(y(t)),u=[...t];let d=u.shift(),p=[...n.state.filters.filter(e=>e.key!==c),{key:c,operator:i.w7.Equal,value:l}];for(;d&&!(0,a.Z6)(d.toString())&&!(0,r.isNumber)(d)&&"root"!==d;){const e=y(u),t=S(u);e&&!p.find(n=>n.key===e&&n.value===t&&n.operator===i.w7.Equal)&&(p=[...p.filter(t=>t.key!==e),{key:e,operator:i.w7.Equal,value:t}]),d=u.shift()}n.setState({filters:p})}function y(e){return w(e).join("_")}function S(e){const t=w(e);return(0,a.Mz)(t)}function w(e){let t;const n=[...e],i=[];for(;(t=n.shift())&&!(0,a.Z6)(t.toString())&&!(0,r.isNumber)(t)&&"root"!==t;)i.unshift(t);return i}},8839:(e,t,n)=>{n.d(t,{K:()=>i,Q:()=>a});var r=n(7781);function a(e){return e===r.LogsSortOrder.Ascending||e===r.LogsSortOrder.Descending}function i(e){const t=Object.values(r.LogsDedupStrategy).map(e=>e.toString());return"string"==typeof e&&t.includes(e)}},42:(e,t,n)=>{n.d(t,{CP:()=>b,HO:()=>y,R7:()=>h,_t:()=>v,de:()=>p,di:()=>u,ec:()=>f,oj:()=>d,zr:()=>g});var r=n(9405),a=n(376),i=n(5570),s=n(708),o=n(7985),l=n(5553),c=n(20);const u="repeat(auto-fit, minmax(400px, 1fr))";function d(e,t,n){let r="",i="";const s=(0,l.ir)(e),u=(0,a.k$)(s);return n&&n!==c.e4?r=` ,${n} != ""`:n&&n===c.e4&&(i=` | ${n} != ""`),(0,o.l)(`sum(count_over_time(${(0,l.DX)({labelExpressionToAdd:r,parser:u,structuredMetadataToAdd:i})} [$__auto])) by (${t})`,{legendFormat:`{{${t}}}`,refId:"LABEL_BREAKDOWN_VALUES"})}function p(e){return e.map(e=>(0,i.H7)(e)).flatMap(e=>e?[e]:[])}function g(e,t,n){const i=(0,l.YS)(n),o=0===i.state.filters.length,u="structuredMetadata"===(0,a.Ri)(e,n),d=i.state.filters.find(e=>u?(0,s.BG)(e.operator)&&e.value===t:(0,s.BG)(e.operator)&&(0,l.bu)(e).value===t);return o||!d?((0,r.Qt)(e,t,"include",n,u?c._P:c.mB),"include"):((0,r.Qt)(e,t,"toggle",n,u?c._P:c.mB),"toggle")}function h(e,t,n){const a=(0,l.cR)(n),i=0===a.state.filters.length,o=a.state.filters.find(e=>e.value===t&&(0,s.BG)(e.operator));return i||!o?((0,r.Qt)(e,t,"include",n,c.MB),"include"):((0,r.Qt)(e,t,"toggle",n,c.MB),"toggle")}function f(e,t,n){return m(e,t,(0,l.cR)(n))}function v(e,t,n){return m(e,t,(0,l.ir)(n))}function b(e,t,n){return m(e,t,(0,l.oY)(n))}function m(e,t,n){const r=n.state.filters.filter(t=>t.key===e&&(0,s.BG)(t.operator)).map(e=>n.state.name===c.mB?(0,l.bu)(e).value:e.value),a=n.state.filters.filter(t=>t.key===e&&(0,s.Lw)(t.operator)).map(e=>n.state.name===c.mB?(0,l.bu)(e).value:e.value);return t.filter(e=>!a.includes(e)&&(0===r.length||r.includes(e)))}const y=/[^a-zA-Z0-9_:]/g},5570:(e,t,n)=>{n.d(t,{Ex:()=>d,H7:()=>u,PE:()=>g,pC:()=>l,vX:()=>c});var r=n(2007),a=n(708),i=n(5553),s=n(20),o=n(9405);function l(e,t,n,a){if(n===r.SeriesVisibilityChangeMode.ToggleSelection){const n=null!=t?t:[];return 1===n.length&&n.includes(e)?[]:[e]}let i=(null==t?void 0:t.length)?t:a;return i.includes(e)?i.filter(t=>t!==e):[...i,e]}function c(e){return e.map(e=>{var t;return null!==(t=u(e))&&void 0!==t?t:"logs"})}function u(e){var t;const n=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!n)return null;const r=Object.keys(n);return 0===r.length?null:n[r[0]]}function d(e,t){const n=(0,i.iw)(t),r=n.state.filters.filter(e=>(0,a.BG)(e.operator)).map(e=>e.value.split("|").map(p)).join("|"),s=n.state.filters.filter(e=>(0,a.Lw)(e.operator)).map(e=>e.value.split("|").map(p)).join("|");return e.filter(e=>!s.includes(e)&&(0===r.length||r.includes(e)))}function p(e){return'""'===e?"logs":e}function g(e,t){const n=(0,i.iw)(t),r=0===n.state.filters.length,l=n.state.filters.find(t=>t.value===e&&(0,a.BG)(t.operator));return"logs"===e&&(e='""'),r||!l?((0,o.Qt)(s.e4,e,"include",t,s._Y),"include"):((0,o.Qt)(s.e4,e,"toggle",t,s._Y),"toggle")}},9330:(e,t,n)=>{n.d(t,{Bg:()=>p,E2:()=>c,Yb:()=>d,tO:()=>g});var r=n(2344),a=n(3257);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){i(e,t,n[t])})}return e}function o(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function l(e){return e.trim().length>2&&!function(e,t){let n=!1;return r.K3.parse(e).iterate({enter:({type:e})=>{if(e.id===t)return n=!0,!1}}),n}(e,r.Yw)}function c(e){if(function(e){return void 0!==e.targets.find(e=>l(e.expr))}(e))return!1;if(function(e){return e.targets.find(e=>"instant"===e.queryType)}(e))return!1;for(let n=0;n<e.targets.length;n++){var t;if(null===(t=e.targets[n].expr)||void 0===t?void 0:t.includes("avg_over_time"))return!1}return!0}const u="__stream_shard_number__",d=e=>e.replace("}",`, __stream_shard__=~"${u}"}`),p=(e,t)=>{if(void 0===t||0===t.length)return e.map(e=>o(s({},e),{expr:e.expr.replace(`, __stream_shard__=~"${u}"}`,"}")}));let n=t.join("|");return"-1"===n||1===t.length?(n="-1"===n?"":n,e.map(e=>o(s({},e),{expr:e.expr.replace(`, __stream_shard__=~"${u}"}`,`, __stream_shard__="${n}"}`)}))):e.map(e=>o(s({},e),{expr:e.expr.replace(new RegExp(`${u}`,"g"),n)}))},g=e=>{const t=(0,a.QH)(e,[r.MD]);return t.length>0?e.substring(t[0].from,t[0].to).replace(`, __stream_shard__=~"${u}"}`,"}"):""}},2165:(e,t,n)=>{n.d(t,{BR:()=>c,Il:()=>f,Os:()=>g,bz:()=>u,eB:()=>s,fF:()=>h,hy:()=>y,po:()=>v,wd:()=>d,wu:()=>o,y2:()=>S,z5:()=>b});var r=n(7781);function a(e,t,n){const r=e.getFieldByName(t);if(void 0!==r)return r.type===n?r:void 0}const i="timestamp",s="body",o="Line",l="severity",c="id",u="labels",d="labelTypes";function p(e){const t={};return Object.entries(e).forEach(([e,n])=>{t[e]="string"==typeof n?n:JSON.stringify(n)}),t}function g(e){var t;return(null===(t=e.meta)||void 0===t?void 0:t.type)===r.DataFrameType.LogLines?function(e){const t=new r.FieldCache(e),n=a(t,i,r.FieldType.time),o=a(t,s,r.FieldType.string);if(void 0===n||void 0===o)return null;var d;const g=null!==(d=a(t,l,r.FieldType.string))&&void 0!==d?d:null;var h;const f=null!==(h=a(t,c,r.FieldType.string))&&void 0!==h?h:null;var v;const b=null!==(v=a(t,u,r.FieldType.other))&&void 0!==v?v:null,m=null===b?null:b.values,y=t.fields.filter((e,t)=>t!==n.index&&t!==o.index&&t!==(null==g?void 0:g.index)&&t!==(null==f?void 0:f.index)&&t!==(null==b?void 0:b.index));return{bodyField:o,extraFields:y,getLabelFieldName:()=>null!==b?b.name:null,getLogFrameLabels:()=>m,getLogFrameLabelsAsLabels:()=>null!==m?m.map(p):null,idField:f,raw:e,severityField:g,timeField:n,timeNanosecondField:null}}(e):function(e){const t=new r.FieldCache(e),n=t.getFirstFieldOfType(r.FieldType.time),a=t.getFirstFieldOfType(r.FieldType.string);if(void 0===n||void 0===a)return null;var i;const s=null!==(i=t.getFieldByName("tsNs"))&&void 0!==i?i:null;var o;const l=null!==(o=t.getFieldByName("level"))&&void 0!==o?o:null;var c;const u=null!==(c=t.getFieldByName("id"))&&void 0!==c?c:null,[d,g]=function(e,t,n){const a=e.getFieldByName("labels");if(void 0!==a&&a.type===r.FieldType.other){const e=a.values.map(p);return[a,()=>e]}return[null,()=>function(e,t){const n=e.labels;if(void 0!==n){const e=new Array(t);return e.fill(n),e}return null}(t,n.length)]}(t,a,e),h=t.fields.filter((e,t)=>t!==n.index&&t!==a.index&&t!==(null==s?void 0:s.index)&&t!==(null==l?void 0:l.index)&&t!==(null==u?void 0:u.index)&&t!==(null==d?void 0:d.index));return{bodyField:a,extraFields:h,getLabelFieldName:()=>{var e;return null!==(e=null==d?void 0:d.name)&&void 0!==e?e:null},getLogFrameLabels:g,getLogFrameLabelsAsLabels:g,idField:u,raw:e,severityField:l,timeField:n,timeNanosecondField:s}}(e)}function h(e){var t;return null!==(t=null==e?void 0:e.timeField.name)&&void 0!==t?t:i}function f(e){var t;return null!==(t=null==e?void 0:e.bodyField.name)&&void 0!==t?t:s}function v(e){var t,n;return null!==(n=null==e||null===(t=e.idField)||void 0===t?void 0:t.name)&&void 0!==n?n:c}function b(e){var t;let n=0,a=0;const i=null===(t=e[0])||void 0===t?void 0:t.fields.find(e=>e.type===r.FieldType.time);if(i){const e=[...i.values].sort(),t=e[0]<e[e.length-1];n=t?e[0]:e[e.length-1],a=t?e[e.length-1]:e[0]}return{end:a,start:n}}const m="Visible range";function y(e,t){const n=(0,r.arrayToDataFrame)([{color:"rgba(58, 113, 255, 0.3)",isRegion:!0,text:"Range from oldest to newest logs in display",time:e,timeEnd:t}]);return n.name=m,n.meta={dataTopic:r.DataTopic.Annotations},n}function S(e){return 0===e.length||0===e[0].fields[0].values.length}},2152:(e,t,n)=>{let r;function a(){r||(r=new i)}n.d(t,{JO:()=>s,rX:()=>a});class i{getServiceSceneState(){return this.serviceSceneState}setPatternsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.patternsCount=e}setLabelsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.labelsCount=e}setEmbedded(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.embedded=e}setFieldsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.fieldsCount=e}setServiceSceneState(e){this.serviceSceneState={embedded:e.embedded,fieldsCount:e.fieldsCount,labelsCount:e.labelsCount,loading:e.loading,logsCount:e.logsCount,patternsCount:e.patternsCount,totalLogsCount:e.totalLogsCount}}constructor(){var e,t,n;n=void 0,(t="serviceSceneState")in(e=this)?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}}function s(){return r}},7478:(e,t,n)=>{n.d(t,{FB:()=>b,Ns:()=>x,Vt:()=>w,ad:()=>O,bN:()=>E,fg:()=>m,k9:()=>y,rs:()=>S});var r=n(7781),a=n(8531),i=n(6865),s=n(8714),o=n(8469),l=n(7839),c=n(7389),u=n(2152),d=n(9598),p=n(9683),g=n(20);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let f;function v(e,t){return r.urlUtil.renderUrl(e,function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){h(e,t,n[t])})}return e}({},Object.entries(r.urlUtil.getUrlSearchParams()).reduce((e,[t,n])=>(p.tm.includes(t)&&(e[t]=n),e),{}),e)}(t))}function b(e,t,n){var p,h;const f=i.jh.getAncestor(n,s.P),b=null===(p=f.state.routeMatch)||void 0===p?void 0:p.params.labelName,m=null===(h=f.state.routeMatch)||void 0===h?void 0:h.params.labelValue;if(!f.state.embedded&&b&&m){let r=function(e,t,n,r="service"){return e===g.To&&t===l._J.label?(0,d._F)(`${l.G3.explore}/${r}/${(0,c.uu)(n)}/${l.G3.labels}`):e===g.To&&t===l._J.field?(0,d._F)(`${l.G3.explore}/${r}/${(0,c.uu)(n)}/${l.G3.fields}`):(0,d._F)(`${l.G3.explore}/${r}/${(0,c.uu)(n)}/${t}/${(0,c.uu)(e)}`)}(t,e,m,b);const a=v(r);if(n){(0,u.JO)().setServiceSceneState(n.state)}return a}return function(e,t,n){return n[o.Z]=t,n[o.o]=e,v(a.locationService.getLocation().pathname,n)}(t,e,r.urlUtil.getUrlSearchParams())}function m(e,t,n){const r=b(e,t,n);r&&O(r)}function y(e,t,n){return v(p.bw.logs(t,e),n)}function S(e,t,n){var u,p;const g=i.jh.getAncestor(t,s.P),h=null===(u=g.state.routeMatch)||void 0===u?void 0:u.params.labelValue,f=null===(p=g.state.routeMatch)||void 0===p?void 0:p.params.labelName;if(h&&!t.state.embedded){return v((0,d._F)(`${l.G3.explore}/${f}/${(0,c.uu)(h)}/${e}`),n)}if(t.state.embedded){const t=a.locationService.getLocation();return void 0===n&&(n=r.urlUtil.getUrlSearchParams()),n[o.Z]=e,n[o.o]=void 0,v(t.pathname,n)}throw new Error("Unable to build drilldown tab link!")}function w(e,t,n){const r=S(e,t,n);if(r){if(t){(0,u.JO)().setServiceSceneState(t.state)}O(r)}else;}function O(e){f=e,a.locationService.push(e)}function E(){const e=a.locationService.getLocation();a.locationService.push(e.pathname+e.search)}function x(){const e=a.locationService.getLocation(),t=(0,p.qe)(p.bw.explore()),n=e.pathname+e.search,r=a.locationService.getSearch();var i,s,o;t===n||n.includes(t)||null!==(o=null===(s=(0,u.JO)())||void 0===s||null===(i=s.getServiceSceneState())||void 0===i?void 0:i.embedded)&&void 0!==o&&o||(r.get("var-filters")?O(t):(f&&a.locationService.replace(f),a.locationService.push(t)))}},4907:(e,t,n)=>{n.d(t,{C6:()=>N,CT:()=>G,FH:()=>I,HF:()=>V,Nr:()=>A,ZC:()=>k,dO:()=>$,jC:()=>F,qi:()=>z,rS:()=>R,wx:()=>_});var r=n(1269),a=n(7781),i=n(9814),s=n(8531),o=n(6865),l=n(1625),c=n(2007),u=n(9721),d=n(1296),p=n(376),g=n(42),h=n(5570),f=n(3142),v=n(9594),b=n(4351),m=n(2649);function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function S(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){y(e,t,n[t])})}return e}function w(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const O="logs",E=/^info$/i,x=/^debug$/i,P=/^(warn|warning)$/i,j=/^error$/i,C=/^(crit|critical|fatal)$/i,L=/^(logs|unknown)$/i,_={"log-token-info":E,"log-token-debug":x,"log-token-warning":P,"log-token-error":j,"log-token-critical":C,"log-token-unknown":L};function F(e){e.matchFieldsWithNameByRegex(E.source).overrideColor({fixedColor:"semi-dark-green",mode:"fixed"}),e.matchFieldsWithNameByRegex(x.source).overrideColor({fixedColor:"semi-dark-blue",mode:"fixed"}),e.matchFieldsWithNameByRegex(P.source).overrideColor({fixedColor:"semi-dark-orange",mode:"fixed"}),e.matchFieldsWithNameByRegex(j.source).overrideColor({fixedColor:"semi-dark-red",mode:"fixed"}),e.matchFieldsWithNameByRegex(C.source).overrideColor({fixedColor:"#705da0",mode:"fixed"}),e.matchFieldsWithNameByRegex(L.source).overrideColor({fixedColor:"darkgray",mode:"fixed"})}function k(e){return e.setCustomFieldConfig("stacking",{mode:c.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("axisSoftMin",0).setCustomFieldConfig("drawStyle",c.DrawStyle.Bars).setOverrides(F)}function T(e){return e.setCustomFieldConfig("stacking",{mode:c.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",c.DrawStyle.Bars)}function D(e,t){t.match({id:a.FieldMatcherID.byNames,options:{mode:"exclude",names:e,prefix:"All except:",readOnly:!0}}).overrideCustomFieldConfig("hideFrom",{legend:!1,tooltip:!1,viz:!0});const n=t.build();n[n.length-1].__systemRef="hideSeriesFrom"}function N(e,t,n){const r=(0,h.Ex)((0,h.vX)(t),n),a=k(o.No.timeseries()).setOverrides(D.bind(null,r));a instanceof o.OS&&e.getPlugin()&&e.onFieldConfigChange(a.build(),!0)}function $(e,t,n,r){const a=(0,g.de)(n),i=(0,g.ec)(e,a,r),s=T(o.No.timeseries());i.length&&s.setOverrides(D.bind(null,i)),s instanceof o.OS&&t.onFieldConfigChange(s.build(),!0)}function A(e,t,n,r){const a=(0,g.de)(n),i="structuredMetadata"===(0,p.Ri)(e,r)?(0,g.CP)(e,a,r):(0,g._t)(e,a,r),s=T(o.No.timeseries());i.length&&s.setOverrides(D.bind(null,i)),s instanceof o.OS&&t.onFieldConfigChange(s.build(),!0)}function B(){return e=>e.pipe((0,r.map)(e=>e.map((t,n)=>w(S({},t),{fields:t.fields.map((n,r)=>{if(n.type===a.FieldType.time)return n;const i=(0,a.getFieldDisplayName)(n,t,e);return w(S({},n),{config:w(S({},n.config),{color:{mode:a.FieldColorModeId.PaletteClassicByName},displayName:i})})})}))))}function M(){return e=>e.pipe((0,r.map)(e=>e.map(e=>(e.fields.length<2||e.fields[1].config.displayNameFromDS||(e.fields[1].config.displayNameFromDS=O),e)).sort((e,t)=>{if(e.fields.length<2||t.fields.length<2)return 0;const n=e.fields[1].config.displayNameFromDS,r=(null==n?void 0:n.match(C))?5:(null==n?void 0:n.match(j))?4:(null==n?void 0:n.match(P))?3:(null==n?void 0:n.match(x))||(null==n?void 0:n.match(E))?2:1,a=t.fields[1].config.displayNameFromDS;return r-((null==a?void 0:a.match(C))?5:(null==a?void 0:a.match(j))?4:(null==a?void 0:a.match(P))?3:(null==a?void 0:a.match(x))||(null==a?void 0:a.match(E))?2:1)})))}function I(e,t){return new o.dt(S({datasource:{uid:d.WRAPPED_LOKI_DS_UID},queries:e},t))}function R(e,t){const n=e.find(e=>{var t;return null===(t=e.legendFormat)||void 0===t?void 0:t.toLowerCase().includes("level")}),r=e.find(e=>e.refId===u.DS||e.refId===u.AA);return n?new o.Es({$data:V(S({datasource:{uid:d.WRAPPED_LOKI_DS_UID},queries:e},t)),transformations:[M]}):r?(e=e.map(e=>w(S({},e),{get direction(){return((0,m.P)()||(0,b.YM)("sortOrder",l.uH.Descending))===l.uH.Descending?f.t.Backward:f.t.Forward}})),V(S({datasource:{uid:d.WRAPPED_LOKI_DS_UID},queries:e},t))):new o.Es({$data:V(S({datasource:{uid:d.WRAPPED_LOKI_DS_UID},queries:e},t)),transformations:[B]})}function V(e){return new o.dt(S({datasource:{uid:d.WRAPPED_LOKI_DS_UID},queries:[]},e))}function z(e,t){(0,v.m)(e.data.series)?t.setState({_pluginLoadError:(0,i.t)("drilldown-logs.notices.max-series-reached","Maximum limit of results reached. Displaying partial results.")}):t.state._pluginLoadError&&t.setState({_pluginLoadError:void 0})}const G=s.config.featureToggles.logsPanelControls&&(s.config.buildInfo.version>"12.1"||s.config.buildInfo.version.includes("12.1"))},9598:(e,t,n)=>{n.d(t,{Gy:()=>a,_F:()=>i,s_:()=>r});const r=n(2533).id,a=`/a/${r}`;function i(e,t=a){return`${t}/${e}`}},7985:(e,t,n)=>{n.d(t,{$k:()=>f,BM:()=>p,CY:()=>O,E3:()=>S,Hs:()=>C,PP:()=>m,Sh:()=>j,VT:()=>P,VW:()=>v,ZX:()=>w,_q:()=>y,by:()=>_,c0:()=>b,l:()=>g,sT:()=>x,tR:()=>L,vC:()=>E});var r=n(6865),a=n(1459),i=n(6464),s=n(6854),o=n(9598),l=n(20);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){c(e,t,n[t])})}return e}function d(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const p=(e,t,n,r)=>d(u(d(u({},h),{refId:t,resource:t}),n),{datasource:{uid:l.gR},expr:e,primaryLabel:r}),g=(e,t)=>d(u({},h,t),{expr:e}),h={editorMode:"code",queryType:"range",refId:"A",supportingQueryType:o.s_},f=(e,t,n,r)=>p(e,t,u({},r),n);function v(e,t){return new i.K(e).getLabelsExpr({ignoreKeys:t})}function b(e){var t,n,r;return e.value?{value:(0,l.OQ)(e.value),valueLabels:[null!==(t=e.label)&&void 0!==t?t:e.value]}:{value:e.value,valueLabels:[null!==(r=null!==(n=e.label)&&void 0!==n?n:e.value)&&void 0!==r?r:""]}}function m(e,t){var n,r,a;const i={parser:null!==(r=null==t||null===(n=t.meta)||void 0===n?void 0:n.parser)&&void 0!==r?r:"mixed",value:null!==(a=e.value)&&void 0!==a?a:""};var s,o;return"structuredMetadata"===i.parser?{value:(0,l.OQ)(i.value),valueLabels:[null!==(s=e.label)&&void 0!==s?s:i.value]}:{value:(0,l.OQ)(JSON.stringify(i)),valueLabels:[null!==(o=e.label)&&void 0!==o?o:i.value]}}function y(e,t){return new i.K(e).getLevelsExpr({ignoreKeys:t})}function S(e,t){return new i.K(e).getMetadataExpr({ignoreKeys:t})}function w(e,t){return new i.K(e).getFieldsExpr({ignoreKeys:t})}function O(e){return(0,a.F)(e),e.map(e=>{if(!e.value)return"";const t=function(e){var t,n,a;return e.operator===s.cK.match||e.operator===s.cK.negativeMatch?e.key===s.ld.caseInsensitive?r.Go.escapeLabelValueInRegexSelector(null!==(t=e.value)&&void 0!==t?t:""):r.Go.escapeLabelValueInExactSelector(null!==(n=e.value)&&void 0!==n?n:""):r.Go.escapeLabelValueInExactSelector(null!==(a=e.value)&&void 0!==a?a:"")}(e);return function(e,t){return e.key===s.ld.caseInsensitive?e.operator===s.cK.negativeRegex||e.operator===s.cK.negativeMatch?`${s.cK.negativeRegex} "(?i)${t}"`:`${s.cK.regex} "(?i)${t}"`:`${e.operator} "${t}"`}(e,t)}).join(" ")}function E(e){return".+"===e?e:"(?i).*"!==e.substring(0,6)?`(?i).*${e}.*`:e}function x(e){return"(?i).*"===e.substring(0,6)&&".*"===e.slice(-2)?e.slice(6).slice(0,-2):e}function P(e){return e.replace(/\s*,\s*}/,"}")}function j(e,t){let n=r.jh.interpolate(e,t);return n.includes(l.fK)&&(n=r.jh.interpolate(e,n)),n}function C(){return e=>{let t=e.map(e=>`${e.key}${e.operator}"${e.value}"`).join(",");return t.length&&(t+="| json"),t}}function L(){return e=>{if(e.length){return`| line_format "{{.${e.map(e=>e.key).join("_")}}}"`}return""}}const _=1e3},9683:(e,t,n)=>{n.d(t,{FT:()=>S,HU:()=>v,KL:()=>b,MC:()=>P,UU:()=>w,XJ:()=>x,Zt:()=>m,bw:()=>h,er:()=>O,mC:()=>f,qe:()=>E,tm:()=>y});var r=n(7781),a=n(8531),i=n(6865),s=n(9721),o=n(7839),l=n(7389),c=n(8428),u=n(9598),d=n(5548),p=n(20);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const h={embed:()=>(0,u._F)(o.G3.embed),explore:()=>(0,u._F)(o.G3.explore),fields:(e,t="service")=>(0,u._F)(`${o.G3.explore}/${t}/${(0,l.uu)(e)}/${o.G3.fields}`),labels:(e,t="service")=>(0,u._F)(`${o.G3.explore}/${t}/${(0,l.uu)(e)}/${o.G3.labels}`),logs:(e,t="service")=>(0,u._F)(`${o.G3.explore}/${t}/${(0,l.uu)(e)}/${o.G3.logs}`),patterns:(e,t="service")=>(0,u._F)(`${o.G3.explore}/${t}/${(0,l.uu)(e)}/${o.G3.patterns}`)},f={field:(e,t="service",n)=>(0,u._F)(`${o.G3.explore}/${t}/${(0,l.uu)(e)}/${o._J.field}/${n}`),label:(e,t="service",n)=>(0,u._F)(`${o.G3.explore}/${t}/${(0,l.uu)(e)}/${o._J.label}/${n}`)},v={embed:`${o.G3.embed}/*`,explore:`${o.G3.explore}/*`,fields:`:labelName/:labelValue/${o.G3.fields}`,labels:`:labelName/:labelValue/${o.G3.labels}`,logs:`:labelName/:labelValue/${o.G3.logs}`,patterns:`:labelName/:labelValue/${o.G3.patterns}`},b={field:`:labelName/:labelValue/${o._J.field}/:breakdownLabel`,label:`:labelName/:labelValue/${o._J.label}/:breakdownLabel`},m=["from","to",`var-${p.EY}`,`var-${p.MB}`],y=["from","to","mode","urlColumns","visualizationType","selectedLine","displayedFields","panelState",p.uw,`var-${p.uw}`,`var-${p.MB}`,`var-${p.mB}`,`var-${p._Y}`,`var-${p.LI}`,`var-${p.Jg}`,`var-${p.EY}`,`var-${p.WM}`,`var-${p._P}`,`var-${p.NW}`,`var-${p.lV}`,`var-${p.pw}`];function S(){const e=a.locationService.getLocation();return e.pathname.slice(e.pathname.lastIndexOf("/")+1,e.pathname.length)}function w(e){return e===p.OX&&(e=p.ky),e}function O(){const e=a.locationService.getLocation().pathname.split("/");return(0,c.EP)(e[e.length-2])}function E(e,t){return r.urlUtil.renderUrl(e,function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){g(e,t,n[t])})}return e}({},Object.entries(r.urlUtil.getUrlSearchParams()).reduce((e,[t,n])=>(m.includes(t)&&(e[t]=n),e),{}),e)}(t))}function x(e){return{breakdownLabel:e.params.breakdownLabel,labelName:e.params.labelName,labelValue:e.params.labelValue}}function P(e){let t,n,r,o;try{o=i.jh.getAncestor(e,s.Mn)}catch(t){o=i.jh.findDescendents(e,s.Mn)[0]}return o&&o.state.embedded?({breakdownLabel:t,labelName:n,labelValue:r}=(0,d.xb)(o)):({breakdownLabel:t,labelName:n,labelValue:r}=function(){const e=a.locationService.getLocation(),t="/a/grafana-lokiexplore-app/explore",n=e.pathname.indexOf(t);if(-1===n)throw new Error("Cannot get primary label from URL! getPrimaryLabelFromUrl should not be called when the app is embedded");const r=e.pathname.slice(n+34+1).split("/");let i=r[0];const s=r[1];return{breakdownLabel:r[3],labelName:w(i),labelValue:s}}()),{breakdownLabel:t,labelName:n,labelValue:r}}},5719:(e,t,n)=>{n.d(t,{Mq:()=>p,Ti:()=>c,U4:()=>u,UX:()=>f,hJ:()=>g,m0:()=>v,oh:()=>h,u9:()=>d});n(7781);var r=n(8531),a=n(6865),i=n(5953),s=(n(9683),n(20)),o=n(8714);function l(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function c(e){return a.jh.getAncestor(e,o.P)}function u(e){return a.jh.interpolate(e,s.gR)}function d(e){return a.jh.interpolate(e,s.SA).replace(/\s+/g," ")}function p(e){return a.jh.interpolate(e,s.FX).replace(/\s+/g," ")}function g(e){return(t=function*(){return yield(0,r.getDataSourceSrv)().get(s.gR,{__sceneObject:{value:e}})},function(){var e=this,n=arguments;return new Promise(function(r,a){var i=t.apply(e,n);function s(e){l(i,r,a,s,o,"next",e)}function o(e){l(i,r,a,s,o,"throw",e)}s(void 0)})})();var t}function h(e){return a.jh.findDescendents(e,a.dt)}function f(e,t,n){const r=a.jh.findObject(e,t);return r instanceof n?r:(null!==r&&i.v.warn(`invalid return type: ${n.toString()}`),null)}function v(e){var t;return null===(t=e.state.controls)||void 0===t?void 0:t.find(e=>e instanceof a.KE)}},9193:(e,t,n)=>{n.d(t,{E:()=>o,X:()=>s});var r=n(7993),a=n(3241);const i=new r.A({intraDel:1,intraIns:1,intraMode:1,intraSub:1,intraTrn:1});function s(e,t,n){const[a,s,o]=i.search(e,t,0,1e5);let l=[],c=new Set;if(a&&o){const t=(e,t)=>{t&&c.add(e)};for(let n=0;n<o.length;n++){let a=o[n];r.A.highlight(e[s.idx[a]],s.ranges[a],t),l.push(e[s.idx[a]])}n([l,[...c]])}else t||n([])}const o=(0,a.debounce)(s,300)},9594:(e,t,n)=>{n.d(t,{m:()=>j,C:()=>P});var r=n(1269),a=n(5745),i=n(7781),s=n(2524),o=n(2533),l=n(5953);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){c(e,t,n[t])})}return e}function d(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function p(e){const t=e.fields.find(e=>e.type===i.FieldType.number);if(!t)throw new Error("Unable to find number field on sharded dataframe!");if(!e.name)if(t.labels){var n;e.name=(null!==(n=e.refId)&&void 0!==n?n:"")+JSON.stringify(t.labels)}else{if(!e.refId)throw new Error("Unable to find refId field on sharded dataframe!");e.name=e.refId}return e.name}function g(e,t){if(!e)return d(u({},n=t),{data:n.data.map(y)});var n;const r=new Map;var a,s;e.data.forEach(e=>{r.set(p(e),e)}),t.data.forEach(t=>{var n;let a;var s;if((null===(n=t.meta)||void 0===n?void 0:n.type)!==i.DataFrameType.TimeSeriesMulti)throw new Error(`Invalid data frame type: ${null===(s=t.meta)||void 0===s?void 0:s.type}`);{const n=p(t);r.has(n)?(a=r.get(n),function(e,t){var n,r;const a=e.fields.find(e=>e.type===i.FieldType.time),s=e.fields.find(e=>e.type===i.FieldType.string&&"id"===e.name),o=t.fields.find(e=>e.type===i.FieldType.time),c=t.fields.find(e=>e.type===i.FieldType.string&&"id"===e.name);if(!a||!o)return void l.v.error(new Error("Time fields not found in the data frames"));var p;const g=null!==(p=null==o?void 0:o.values.slice(0))&&void 0!==p?p:[],b=Math.max(e.fields.length,t.fields.length);for(let n=0;n<g.length;n++){const r=h(a,o,n),l=f(a,s,r,o,c,n);for(let a=0;a<b;a++){if(!e.fields[a])continue;const s=v(e.fields[a],t.fields,a);if(s)if(l){if(e.fields[a].type===i.FieldType.time)continue;var y;e.fields[a].type===i.FieldType.number?e.fields[a].values[r]=(null!==(y=e.fields[a].values[r])&&void 0!==y?y:0)+s.values[n]:e.fields[a].type===i.FieldType.other?"object"==typeof s.values[n]?e.fields[a].values[r]=u({},e.fields[a].values[r],s.values[n]):null!=s.values[n]&&(e.fields[a].values[r]=s.values[n]):e.fields[a].values[r]=s.values[n]}else if(void 0!==s.values[n]){var S,w;if(e.fields[a].values.splice(r,0,s.values[n]),s.nanos)e.fields[a].nanos=null!==(w=e.fields[a].nanos)&&void 0!==w?w:new Array(e.fields[a].values.length-1).fill(0),null===(S=e.fields[a].nanos)||void 0===S||S.splice(r,0,s.nanos[n])}}}var O,E;e.length=e.fields[0].values.length,e.meta=d(u({},e.meta),{stats:m(null!==(O=null===(n=e.meta)||void 0===n?void 0:n.stats)&&void 0!==O?O:[],null!==(E=null===(r=t.meta)||void 0===r?void 0:r.stats)&&void 0!==E?E:[])})}(a,t)):e.data.push(y(t))}});const o=[...null!==(a=e.errors)&&void 0!==a?a:[],...null!==(s=t.errors)&&void 0!==s?s:[]];var c;o.length>0&&(e.errors=o);const g=null!==(c=e.error)&&void 0!==c?c:t.error;var b,S;null!=g&&(e.error=g);const w=[...null!==(b=e.traceIds)&&void 0!==b?b:[],...null!==(S=t.traceIds)&&void 0!==S?S:[]];return w.length>0&&(e.traceIds=w),e}function h(e,t,n){const r=(0,i.closestIdx)(t.values[n],e.values);return r<0?0:t.values[n]===e.values[r]&&null!=t.nanos&&null!=e.nanos?t.nanos[n]>e.nanos[r]?r+1:r:t.values[n]>e.values[r]?r+1:r}function f(e,t,n,r,a,i){const s=function(e,t,n,r){if(e.nanos&&n.nanos)return void 0!==e.values[t]&&e.values[t]===n.values[r]&&void 0!==e.nanos[t]&&e.nanos[t]===n.nanos[r];return void 0!==e.values[t]&&e.values[t]===n.values[r]}(e,n,r,i);return!!s&&(null==t||null==a||void 0!==t.values[n]&&t.values[n]===a.values[i])}function v(e,t,n){const r=t.filter(t=>t.name===e.name);return 1===r.length?r[0]:t[n]}const b="Summary: total bytes processed";function m(e,t){const n=e.find(e=>e.displayName===b),r=t.find(e=>e.displayName===b);if(null!=r&&null!=n)return[{displayName:b,unit:n.unit,value:r.value+n.value}];const a=null!=r?r:n;return null!=a?[a]:[]}function y(e){return d(u({},e),{fields:e.fields.map(e=>d(u({},e),{values:e.values}))})}var S=n(9330),w=n(3257);function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){O(e,t,n[t])})}return e}function x(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function P(e,t){const n=e.interpolateVariablesInQueries(t.targets,t.scopedVars).filter(e=>e.expr).map(e=>x(E({},e),{expr:(0,S.Yb)(e.expr)}));return function(e,t,n){let o=!1,c={data:[],key:(0,a.A)(),state:i.LoadingState.Streaming},u=null,d=new Map,p=null;const h=(r,a,f,v)=>{let b=v,m=!1;null!=u&&(u.unsubscribe(),u=null);const y=()=>{c.state=o?i.LoadingState.Error:i.LoadingState.Done,r.next(c),r.complete()};if(o)return void y();const w=()=>{const e=Math.min(a+v,f.length);a<f.length&&e<=f.length?h(r,e,f,b):y()},O=e=>{try{if(e&&!function(e){var t,n,r;const a=e.errors?(null!==(n=e.errors[0].message)&&void 0!==n?n:"").toLowerCase():null!==(r=null===(t=e.error)||void 0===t?void 0:t.message)&&void 0!==r?r:"";if(a.includes("timeout"))return!0;if(a.includes("parse error")||a.match(s.j4))throw new Error(a);return!1}(e))return!1}catch(r){var t,n,i,c,u,g;return l.v.error(r,{error:null!==(c=null==e||null===(t=e.error)||void 0===t?void 0:t.message)&&void 0!==c?c:"",errors:null!==(u=null==e||null===(n=e.errors)||void 0===n?void 0:n.map(e=>e.message).join(" | "))&&void 0!==u?u:"",msg:"sharding retry error",traces:null!==(g=null==e||null===(i=e.traceIds)||void 0===i?void 0:i.join("|"))&&void 0!==g?g:""}),o=!0,!1}if(v>1)return C(`Possible time out, new group size ${v=Math.floor(Math.sqrt(v))}`),m=!0,h(r,a,f,v),!0;var b;const y=null!==(b=d.get(a))&&void 0!==b?b:0;return y>3?(o=!0,!1):(d.set(a,y+1),p=setTimeout(()=>{l.v.info(`Retrying ${a} (${y+1})`),h(r,a,f,v),p=null},1500*Math.pow(2,y)),m=!0,!0)},P=function(e,t,n){if(t===e.length)return[-1];return e.slice(t,t+n)}(f,a,v);C(`Querying ${P.join(", ")}`);const L=x(E({},t),{targets:(0,S.Bg)(n,P)});t.requestId&&(L.requestId=`${t.requestId}_shard_${a}_${v}`),u=e.runQuery(L).subscribe({complete:()=>{m||(c.data.length&&r.next(c),j(c.data)?y():w())},error:e=>{l.v.error(e,{msg:"failed to shard"}),r.next(c),O()||w()},next:e=>{var t;if(!((null!==(t=e.errors)&&void 0!==t?t:[]).length>0||null!=e.error)||!O(e)){b=function(e,t,n){const r=.7;return Math.min(t,Math.max(Math.floor((n-e)*r),1))}(a+v,function(e,t){var n,r;if(!e.data.length)return t+1;const a=null===(r=e.data[0].meta)||void 0===r||null===(n=r.stats)||void 0===n?void 0:n.find(e=>"Summary: exec time"===e.displayName);if(a){const e=Math.round(a.value);return C(`${a.value}`),e<=1?Math.floor(1.5*t):e<6?Math.ceil(1.1*t):1===t?t:e<20?Math.ceil(.9*t):Math.floor(t/2)}return t}(e,v),f.length),b!==v&&C(`New group size ${b}`);try{c=g(c,e)}catch(t){l.v.error(t,{msg:"shardQuerySplitting::combineResponses error!"}),c=c.data.length>e.data.length?c:e,y()}}}})},f=n=>{u=e.query(t).subscribe({complete:()=>{n.next(c)},error:e=>{l.v.error(e,{msg:"runNonSplitRequest subscription error"}),n.error(c)},next:e=>{c=e}})},v=new r.Observable(r=>{const a=(0,S.tO)(n[0].expr);return(0,w.T0)(a)?(e.languageProvider.fetchLabelValues("__stream_shard__",{streamSelector:a||void 0,timeRange:t.range}).then(e=>{const t=e.map(e=>parseInt(e,10));t&&t.length?(t.sort((e,t)=>t-e),C(`Querying ${t.join(", ")} shards`),h(r,0,t,function(e){return Math.floor(Math.sqrt(e.length))}(t))):(l.v.warn("Shard splitting not supported. Issuing a regular query."),f(r))}).catch(e=>{l.v.error(e,{msg:"failed to fetch label values for __stream_shard__"}),f(r)}),()=>{o=!0,p&&clearTimeout(p),null!=u&&(u.unsubscribe(),u=null)}):(C(`Skipping invalid selector: ${a}`),void r.complete())});return v}(e,t,n)}function j(e){var t,n;const r=/maximum number of series/,a=e.find(e=>{var t,n,a;return(null===(n=e.meta)||void 0===n||null===(t=n.notices)||void 0===t?void 0:t.length)&&(null===(a=e.meta)||void 0===a?void 0:a.notices.some(e=>e.text.match(r)))});return a&&Boolean(null===(n=a.meta)||void 0===n||null===(t=n.notices)||void 0===t?void 0:t.length)}Boolean(localStorage.getItem(`${o.id}.sharding_debug_enabled`));function C(e){}},2601:(e,t,n)=>{n.r(t),n.d(t,{DEFAULT_SORT_BY:()=>u,calculateDataFrameChangepoints:()=>p,calculateOutlierValue:()=>v,sortSeries:()=>d,sortSeriesByName:()=>g,wasmSupported:()=>b});var r=n(1854),a=n(6944),i=n(3241),s=n(7781),o=n(4509),l=n(5570),c=n(5953);const u="changepoint",d=(0,i.memoize)((e,t,n)=>{if("alphabetical"===t)return g(e,n);"outliers"===t&&h(e);const r=n=>{var r;try{if("changepoint"===t)return p(n);if("outliers"===t)return v(e,n)}catch(e){c.v.error(e,{msg:"failed to sort"}),t=s.ReducerID.stdDev}const a=s.fieldReducers.get(t);var i;var o;return null!==(o=(null!==(i=null===(r=a.reduce)||void 0===r?void 0:r.call(a,n.fields[1],!0,!0))&&void 0!==i?i:(0,s.doStandardCalcs)(n.fields[1],!0,!0))[t])&&void 0!==o?o:0},a=e.map(e=>({dataFrame:e,value:r(e)}));return a.sort((e,t)=>void 0!==e.value&&void 0!==t.value?t.value-e.value:0),"asc"===n&&a.reverse(),a.map(({dataFrame:e})=>e)},(e,t,n)=>{const r=e.length>0?e[0].fields[0].values[0]:0,a=e.length>0?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0,i=e.length>0?(0,l.H7)(e[0]):"",s=e.length>0?(0,l.H7)(e[e.length-1]):"",o=e.map(e=>e.length+"_"+e.fields.map(e=>e.name+"_"+e.values[0]+"_"+e.values[e.values.length-1]));return`${i}_${s}_${r}_${a}_${e.length}_${o}_${t}_${n}`}),p=e=>{if(!b())throw new Error("WASM not supported, fall back to stdDev");const t=e.fields.filter(e=>e.type===s.FieldType.number),n=t[0].values.length;let a=Math.floor(n/100)||1;a>1&&(a=Math.ceil(a/2));const i=t[0].values.filter((e,t)=>t%a===0),o=new Float64Array(i);return r.ChangepointDetector.defaultArgpcp().detectChangepoints(o).indices.length},g=(e,t)=>{const n=[...e];return n.sort((e,t)=>{const n=(0,l.H7)(e),r=(0,l.H7)(t);return n&&r&&null!==(a=null==n?void 0:n.localeCompare(r))&&void 0!==a?a:0;var a}),"desc"===t&&n.reverse(),n},h=e=>{if(!b())return;const t=(0,s.outerJoinDataFrames)({frames:e});if(!t)return;const n=t.fields.filter(e=>e.type===s.FieldType.number).flatMap(e=>new Float64Array(e.values));try{const e=a.OutlierDetector.dbscan({sensitivity:.4}).preprocess(n);f=e.detect()}catch(e){c.v.error(e,{msg:"initOutlierDetector: OutlierDetector error"})}};let f;const v=(e,t)=>{if(!b())throw new Error("WASM not supported, fall back to stdDev");if(!f)throw new Error("Initialize outlier detector first");const n=e.indexOf(t);return f.seriesResults[n].isOutlier?f.seriesResults[n].outlierIntervals.length:0},b=()=>{const e="object"==typeof WebAssembly;return e||(0,o.EE)(o.NO.service_details,o.ir.service_details.wasm_not_supported),e}},4351:(e,t,n)=>{n.d(t,{Bq:()=>pe,Bz:()=>I,Dy:()=>xe,FD:()=>ie,GL:()=>de,Gg:()=>w,Hn:()=>te,IL:()=>V,IW:()=>me,JA:()=>Ce,KH:()=>Oe,LJ:()=>D,MZ:()=>ae,N$:()=>N,NM:()=>Le,OB:()=>m,QB:()=>P,Qi:()=>Z,RN:()=>U,Rb:()=>Se,Rf:()=>W,Vt:()=>J,WO:()=>B,Xo:()=>ue,YK:()=>z,YM:()=>R,ZF:()=>$,Zs:()=>fe,_2:()=>ne,cC:()=>y,cO:()=>S,eT:()=>b,ex:()=>be,fq:()=>F,hp:()=>ge,k5:()=>Q,ke:()=>C,ki:()=>le,o5:()=>H,og:()=>he,rg:()=>Y,sB:()=>A,sj:()=>O,uF:()=>Ee,vC:()=>oe,vR:()=>M,vs:()=>_,x0:()=>j,zu:()=>Pe});var r=n(7781),a=n(8531),i=n(2533),s=n(7389),o=n(8839),l=n(5953),c=n(8428),u=n(9683),d=n(5553),p=n(20);const g=`${i.id}.services.favorite`,h=`${i.id}.primarylabels.tabs.favorite`,f=`${i.id}.datasource`,v=`${i.id}.scene.layout`;function b(e,t){if(!e||"string"!=typeof e)return[];const n=E(e,t);let r=[];try{r=(0,c.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){l.v.error(e,{msg:"Error parsing favorite services from local storage"})}return Array.isArray(r)||(r=[]),r}function m(e,t,n){if(!e||"string"!=typeof e)return;const r=E(e,t);let a=[];try{a=(0,c.aJ)(JSON.parse(localStorage.getItem(r)||"[]"))}catch(e){l.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(a)||(a=[]);const i=a.filter(e=>e!==n);i.unshift(n),localStorage.setItem(r,JSON.stringify(i))}function y(e,t,n){if(!e||!t||!n||"string"!=typeof e)return;const r=E(e,t);let a=[];try{a=(0,c.aJ)(JSON.parse(localStorage.getItem(r)||"[]"))}catch(e){l.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(a)||(a=[]);const i=a.filter(e=>e!==n);localStorage.setItem(r,JSON.stringify(i))}function S(e,t){if(!e||!t)return;const n=x(e);let r=[];try{r=(0,c.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){l.v.error(e,{msg:"Error parsing saved tabs from local storage"})}if(Array.isArray(r)||(r=[]),-1===r.indexOf(t)){const e=r.filter(e=>e!==t);e.unshift(t),localStorage.setItem(n,JSON.stringify(e))}}function w(e,t){if(!e||!t)return;const n=x(e);let r=[];try{r=(0,c.aJ)(JSON.parse(localStorage.getItem(n)||"[]"))}catch(e){l.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(r)||(r=[]);const a=r.filter(e=>e!==t);localStorage.setItem(n,JSON.stringify(a))}function O(e){if(!e||"string"!=typeof e)return[];const t=x(e);let n=[];try{n=(0,c.aJ)(JSON.parse(localStorage.getItem(t)||"[]"))}catch(e){l.v.error(e,{msg:"Error parsing favorite services from local storage"})}return Array.isArray(n)||(n=[]),n}function E(e,t){return t=t===p.OX?"":`_${t}`,`${g}_${e}${t}`}function x(e){return`${h}_${e}`}function P(){var e;return null!==(e=localStorage.getItem(f))&&void 0!==e?e:void 0}function j(){const e=(0,a.getDataSourceSrv)().getList({type:"loki"}).find(e=>e.isDefault);return null==e?void 0:e.uid}function C(e){localStorage.setItem(f,e)}const L=`${i.id}.values.sort`;function _(e,t,n){var r;const a=(null!==(r=localStorage.getItem(`${L}.${e}.by`))&&void 0!==r?r:"").split(".");if(!a[0]||!a[1])return{direction:n,sortBy:t};const i=a[0];return{direction:a[1],sortBy:i}}function F(e,t,n){t&&n&&localStorage.setItem(`${L}.${e}.by`,`${t}.${n}`)}function k(e){const{labelName:t,labelValue:n}=(0,u.MC)(e);return T(e,t,n)}function T(e,t,n){const r=(0,d.nH)(e);return t===p.OX||t===p.ky?`${r}.${(0,s.uu)(n)}`:`${r}.${t}.${(0,s.uu)(n)}`}function D(e,t,n){const r=T(e,t,n),a=localStorage.getItem(`${i.id}.${r}.logs.fields`);var s;return a&&null!==(s=(0,c.aJ)(JSON.parse(a)))&&void 0!==s?s:[]}function N(e){const t=k(e),n=localStorage.getItem(`${i.id}.${t}.logs.fields`);var r;return n&&null!==(r=(0,c.aJ)(JSON.parse(n)))&&void 0!==r?r:[]}function $(e,t){const n=k(e);localStorage.setItem(`${i.id}.${n}.logs.fields`,JSON.stringify(t))}function A(e){const t=k(e),n=localStorage.getItem(`${i.id}.${t}.logs.dedupStrategy`);return n&&(0,o.K)(n)?n:r.LogsDedupStrategy.none}function B(e,t){const n=k(e);localStorage.setItem(`${i.id}.${n}.logs.dedupStrategy`,t)}const M="grafana.explore.logs",I="grafana.explore.logs.patterns";function R(e,t){const n=localStorage.getItem(`${M}.${e}`);return n||t}function V(e,t){const n=localStorage.getItem(`${M}.${e}`);return null===n?t:!(""===n||"false"===n)}function z(e,t){let n=t.toString();localStorage.setItem(`${M}.${e}`,n)}const G="grafana.explore.logs.logsVolume";function U(e,t){const n=`${G}.${e}`;void 0!==t?localStorage.setItem(n,t):localStorage.removeItem(n)}function W(e){return Boolean(localStorage.getItem(`${G}.${e}`))}const K="grafana.explore.logs.visualisationType";function Q(){var e;const t=null!==(e=localStorage.getItem(K))&&void 0!==e?e:"";switch(t){case"table":case"logs":return t;case"json":return"json";default:return"logs"}}function H(e){localStorage.setItem(K,e)}const q=`${i.id}.panelOptions.showErrors`;function J(){return!!localStorage.getItem(q)}function Y(e){localStorage.setItem(q,e?"true":"")}const X=`${i.id}.jsonParser.visible`;function Z(){return!!localStorage.getItem(X)}const ee=`${i.id}.jsonViz.metadata`;function te(){return!!localStorage.getItem(ee)}function ne(e){localStorage.setItem(ee,e?"true":"")}const re=`${i.id}.jsonViz.highlight`;function ae(){return!!localStorage.getItem(re)}function ie(e){localStorage.setItem(re,e?"true":"")}const se=`${i.id}.jsonViz.labels`;function oe(){return!!localStorage.getItem(se)}function le(e){localStorage.setItem(se,e?"true":"")}const ce=`${i.id}.linefilter.option`;function ue(e){let t=e.toString();e||(t=""),localStorage.setItem(`${ce}.caseSensitive`,t)}function de(e){let t=e.toString();e||(t=""),localStorage.setItem(`${ce}.regex`,t)}function pe(e){let t=e.toString();e||(t=""),localStorage.setItem(`${ce}.exclusive`,t)}function ge(e){return"true"===localStorage.getItem(`${ce}.caseSensitive`)||e}function he(e){return"true"===localStorage.getItem(`${ce}.regex`)||e}function fe(e){return"true"===localStorage.getItem(`${ce}.exclusive`)||e}const ve=`${i.id}.panel.option`;function be(e,t){const n=localStorage.getItem(`${ve}.${e}`);var r;return null!==n&&null!==(r=t.find(e=>n===e))&&void 0!==r?r:null}function me(e,t){localStorage.setItem(`${ve}.${e}`,t)}const ye=`${i.id}.expressionBuilder.debug`;function Se(){return!!localStorage.getItem(ye)}const we=`${i.id}.serviceSelection.pageCount`;function Oe(){const e=localStorage.getItem(we);return e?parseInt(e,10):void 0}function Ee(e){localStorage.setItem(we,e.toString(10))}function xe(){return localStorage.getItem(v)}function Pe(e){localStorage.setItem(v,e)}const je=`${i.id}.fieldsBreakdown.fieldsPanelType`;function Ce(){const e=localStorage.getItem(je);return"text"===e||"timeseries"===e?e:null}function Le(e){localStorage.setItem(je,e)}},3571:(e,t,n)=>{n.d(t,{b:()=>r});const r={appConfig:{apiKey:"data-testid ac-api-key",apiUrl:"data-testid ac-api-url",container:"data-testid ac-container",submit:"data-testid ac-submit-form"},breakdowns:{common:{filterButton:"data-testid filter-button",filterButtonGroup:"data-testid filter-button-group",filterNumericPopover:{cancelButton:"data-testid filter-numeric-cancel",inputGreaterThan:"data-testid filter-numeric-gt",inputGreaterThanInclusive:"data-testid filter-numeric-gte",inputGreaterThanUnit:"data-testid filter-numeric-gtu",inputLessThan:"data-testid filter-numeric-lt",inputLessThanInclusive:"data-testid filter-numeric-lte",inputLessThanUnit:"data-testid filter-numeric-ltu",removeButton:"data-testid filter-numeric-remove",submitButton:"data-testid filter-numeric-submit"},filterSelect:"data-testid filter-select",sortByDirection:"data-testid SortBy direction",sortByFunction:"data-testid SortBy function"},fields:{},labels:{}},exploreServiceDetails:{buttonFilterExclude:"data-testid button-filter-exclude",buttonFilterInclude:"data-testid button-filter-include",buttonRemovePattern:"data-testid button-remove-pattern",openExplore:"data-testid open-explore",searchLogs:"data-testid search-logs",tabFields:"data-testid tab-fields",tabLabels:"data-testid tab-labels",tabLogs:"data-testid tab-logs",tabPatterns:"data-testid tab-patterns"},exploreServiceSearch:{search:"data-testid search-services"},header:{refreshPicker:"data-testid RefreshPicker run button"},index:{addNewLabelTab:"data-testid Tab Add label",aggregatedMetricsMenu:"data-testid aggregated-metrics-menu",aggregatedMetricsToggle:"data-testid aggregated-metrics-toggle",header:{showLogsButton:"data-testid Show logs header"},searchLabelValueInput:"data-testid search-services-input",selectServiceButton:"data-testid button-select-service",showLogsButton:"data-testid button-filter-include"},logsPanelHeader:{header:"data-testid Panel header Logs",radio:"data-testid radio-button"},patterns:{buttonExcludedPattern:"data-testid button-excluded-pattern",buttonIncludedPattern:"data-testid button-included-pattern",tableWrapper:"data-testid table-wrapper"},table:{inspectLine:"data-testid inspect",rawLogLine:"data-testid raw-log-line",wrapper:"data-testid table-wrapper"},variables:{combobox:{},datasource:{label:"data-testid Dashboard template variables submenu Label Data source"},levels:{inputWrap:"data-testid detected_level filter variable"},serviceName:{label:"data-testid Dashboard template variables submenu Label Labels"}}}},5002:(e,t,n)=>{n.d(t,{Dk:()=>s,EJ:()=>c,Ki:()=>u,Zr:()=>l,gW:()=>o});var r=n(7781),a=n(8531),i=n(5953);const s=e=>{if(navigator.clipboard&&window.isSecureContext)navigator.clipboard.writeText(e);else{const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t)}};const o=(e,t,n)=>{const i=a.locationService.getLocation(),s=r.urlUtil.getUrlSearchParams();return s.from=n.from.toISOString(),s.to=n.to.toISOString(),s[e]=JSON.stringify(t),o=r.urlUtil.renderUrl(i.pathname,s),`${window.location.protocol}//${window.location.host}${a.config.appSubUrl}${o}`;var o};function l(e){return e.length?(null==e?void 0:e.charAt(0).toUpperCase())+e.slice(1):(i.v.warn("invalid string argument"),e)}function c(e,t,n){return e.substring(0,t)+(n&&e.length>t?"…":"")}function u(e){const t=(0,r.dateTime)(e.timeEpochMs-1),n=(0,r.dateTime)(e.timeEpochMs+1);return{from:t,raw:{from:t,to:n},to:n}}},5553:(e,t,n)=>{n.d(t,{BL:()=>C,DX:()=>c,El:()=>j,Gc:()=>_,Gk:()=>O,Hj:()=>S,Ku:()=>u,P4:()=>m,Rr:()=>b,S9:()=>w,U2:()=>L,YS:()=>h,aW:()=>p,bY:()=>E,bu:()=>k,cR:()=>d,eY:()=>x,h:()=>P,ir:()=>f,iw:()=>v,n5:()=>F,nH:()=>D,oY:()=>g,vm:()=>y,z2:()=>T});var r=n(6865),a=n(4702),i=n(8502),s=n(5953),o=n(8428),l=n(20);function c(e){const{fieldExpressionToAdd:t="",jsonParserPropToAdd:n="",labelExpressionToAdd:r="",parser:a,structuredMetadataToAdd:i=""}=e;switch(a){case"structuredMetadata":return`{${l.S1}${r}} ${i} ${l.qZ} ${l.S6} ${l.sC} ${l.rl} ${t} ${l.Oc}`;case"json":return`{${l.S1}${r}} ${i} ${l.qZ} ${l.S6} ${l.sC} ${l.rl} | json ${n} ${l.fK} | drop __error__, __error_details__ ${t} ${l.Oc}`;case"logfmt":return`{${l.S1}${r}} ${i} ${l.qZ} ${l.S6} ${l.sC} ${l.rl} ${l.mF} ${t} ${l.Oc}`;default:return`{${l.S1}${r}} ${i} ${l.qZ} ${l.S6} ${l.sC} ${l.rl} | json ${n} ${l.fK} | logfmt | drop __error__, __error_details__  ${t} ${l.Oc}`}}function u(e){const t=r.jh.lookupVariable(l.uw,e);if(!(t instanceof r.yP))throw new Error("VAR_PATTERNS not found");return t}function d(e){return E(l.MB,e)}function p(e){return E(l.fi,e)}function g(e){return E(l._P,e)}function h(e){return E(l.sL,e)}function f(e){return E(l.mB,e)}function v(e){return E(l._Y,e)}function b(e){const t=r.jh.lookupVariable(l.WM,e);if(!(t instanceof r.H9))throw new Error("VAR_LINE_FILTER not found");return t}function m(e){const t=r.jh.lookupVariable(l.Jg,e);if(!(t instanceof a.m))throw new Error("VAR_LABEL_GROUP_BY not found");return t}function y(e){const t=r.jh.lookupVariable(l.Wi,e);if(!(t instanceof a.m))throw new Error("SERVICE_LABEL_VAR not found");return t}function S(e){const t=r.jh.lookupVariable(l.LI,e);if(!(t instanceof a.m))throw new Error("VAR_FIELD_GROUP_BY not found");return t}function w(e){const t=r.jh.lookupVariable(l.EY,e);if(!(t instanceof r.mI))throw new Error("VAR_DATASOURCE not found");return t}function O(e){const t=r.jh.lookupVariable(l.NW,e);if(!(t instanceof r.H9))throw new Error("VAR_LINE_FILTERS not found");return t}function E(e,t){const n=r.jh.lookupVariable(e,t);if(!(n instanceof r.H9))throw new Error(`Could not get AdHocFiltersVariable ${e}. Variable not found.`);return n}function x(e){const t=r.jh.lookupVariable(l.Du,e);if(!(t instanceof a.m))throw new Error("VAR_PRIMARY_LABEL_SEARCH not found");return t}function P(e){x(e).setState({label:"",value:".+"})}function j(e){const t=r.jh.lookupVariable(l.Gb,e);if(!(t instanceof r.H9))throw new Error("VAR_PRIMARY_LABEL not found");return t}function C(e,t){j(t).setState({filters:[{key:e,operator:"=~",value:".+"}]})}function L(e){const t=r.jh.lookupVariable(l.pw,e);if(!(t instanceof r.H9))throw new Error("VAR_JSON_PARSER not found!");return t}function _(e){const t=r.jh.lookupVariable(l.lV,e);if(!(t instanceof r.H9))throw new Error("VAR_JSON_FIELDS not found!");return t}function F(e){return`var-${e}`}function k(e,t=l.mB){if((0,i.OH)(e))return{parser:"structuredMetadata",value:e.value};try{const t=(0,l.zE)(e.value)?(0,l.Dx)(e.value):e.value,n=(0,o.fS)(JSON.parse(t));if(!1!==n)return n;throw new o.QX("getValueFromFieldsFilter: invalid filter value!")}catch(n){if(n instanceof o.QX?s.v.error(n,{msg:`getValueFromFieldsFilter: Failed to validate ${t}`,value:e.value}):s.v.error(n,{msg:`getValueFromFieldsFilter: Failed to parse ${t}`,value:e.value}),e.value)return{parser:"mixed",value:e.value};throw n}}function T(e,t){return e===l.mB&&t?k(t):{value:null==t?void 0:t.value}}function D(e){return w(e).getValue()}},5548:(e,t,n)=>{n.d(t,{EW:()=>b,Ht:()=>f,Qi:()=>v,mE:()=>g,rA:()=>h,xb:()=>m});var r=n(6865),a=n(8714),i=n(4702),s=n(6854),o=n(708),l=n(4532),c=n(8729),u=n(9683),d=n(5553),p=n(20);function g(e){const t=r.jh.getVariables(e);let n=[];for(const e of t.state.variables)e instanceof c.R?e.state.filters.every(t=>{var n;return null===(n=e.getReadonlyFilters())||void 0===n?void 0:n.find(e=>e.key===t.key&&e.value===t.value&&e.operator===t.operator)})||n.push(e):e instanceof r.H9&&e.state.filters.length&&n.push(e),e instanceof i.m&&e.state.value&&"logsFormat"!==e.state.name&&n.push(e);return n}function h(e){const t=r.jh.getAncestor(e,a.P);t.setState({patterns:[]});g(t).forEach(t=>{if(t instanceof c.R){let{labelName:n,labelValue:r}=(0,u.MC)(e);n===p.ky&&(n=p.OX);const a=t.getReadonlyFilters(),i=new Set;b(null!=a?a:[],i);const s=t.state.filters.filter(e=>e.key===n&&(0,o.BG)(e.operator)&&e.value===r||i.has(v(e)));t.setState({filters:s})}else t instanceof r.H9?t.setState({filters:[]}):t instanceof i.m&&t.setState({text:"",value:""})})}const f=function(e){const t=e.state._wip;if(t&&e.state.filters.some(e=>e.key===t.key&&e.operator===s.w7.Equal))return l._i;const n=e.state.name===p.MB,r=e.state.filters.filter(e=>(0,o.BG)(e.operator)).length,a=!(null==t?void 0:t.key)&&1===r,i=(null==t?void 0:t.key)&&r<1;if(n&&(a||i))return l._i;if(null==t?void 0:t.meta){const e=t.meta.type;if("float"===e||"bytes"===e||"duration"===e)return l.hI}return l.II},v=e=>e.key+"_"+e.operator+"_"+e.value,b=(e,t)=>e.forEach(e=>t.add(v(e)));function m(e,t=(0,d.cR)(e)){if(!e.state.embedded)throw new Error("getPrimaryLabelFromUrl should be used instead when embedded!");return{breakdownLabel:e.state.drillDownLabel,labelName:t.state.filters[0].key,labelValue:t.state.filters[0].value}}}}]);
//# sourceMappingURL=328.js.map?_cache=2a581908eae56bffcaae