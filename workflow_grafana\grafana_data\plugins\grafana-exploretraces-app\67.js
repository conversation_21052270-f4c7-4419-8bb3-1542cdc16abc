"use strict";(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[67],{67:(e,r,a)=>{a.r(r),a.d(r,{default:()=>s});var n=a(8531),t=a(2007),o=a(5959),p=a.n(o),u=a(2533);function s({datasourceUid:e,matchers:r,from:a,to:s,returnToPreviousSource:c,renderButton:i}){const l=(0,n.useReturnToPrevious)(),d=(0,o.useMemo)(()=>{let n=new URLSearchParams;return e&&n.append("var-ds",e),a&&n.append("from",a),s&&n.append("to",s),r.forEach(e=>{n.append("var-filters",`${e.name}|${e.operator}|${e.value}`)}),n.append("var-primarySignal","true"),`a/${u.id}/explore?${n.toString()}`},[e,a,s,r]);return d?i?i({href:d}):p().createElement(t.LinkButton,{variant:"secondary",href:d,onClick:()=>l(c||"previous")},"Open in Traces Drilldown"):null}}}]);
//# sourceMappingURL=67.js.map