<template>
 <q-page>
   <div class="row"
        style="justify-content: space-between; align-items: center; padding-bottom: 30px; padding-top: 30px;">
     <div style="font-size: 30px; font-weight: 500;">Prozesse im Schritt
       <q-btn-dropdown size=lg rounded style="margin: 0 7px 0 7px;"
                       :label="stepDisplayString">
         <q-list>
          <!--   limit this page to show only processes of Triage, DÜ and report-->
<!--           TODO:  add FOCUS step id here, when enabling Triage-Tool again -->
           <q-item v-for="step in [2,4]"
                   clickable v-close-popup
                   @click="selectStep(step)">
             <q-item-section>
               <q-item-label>{{ getForensicStepDisplayString(step) }}</q-item-label>
             </q-item-section>
           </q-item>
         </q-list>
       </q-btn-dropdown>
       mit Status
        <q-btn-dropdown
          size="lg"
          rounded
          :color="processStatus === null ? 'white-1' : getProcessStatusColor(processStatus)"
          :text-color="processStatus === null ? 'black' : 'white'"
          style="margin-left: 7px;"
          :label="processStatus === null ? 'alle' : getProcessStatusDisplayString(processStatus)"
          bordered
        >
         <q-list>
           <q-item v-for="status in [null, ProcessStatus.QUEUED, ProcessStatus.RUNNING, ProcessStatus.FINISHED, ProcessStatus.FAILED, ProcessStatus.CANCELED]"
                   clickable v-close-popup
                   @click="selectProcessStatus(status)">
             <q-item-section>
               <q-item-label>{{ status === null ? 'alle' : getProcessStatusDisplayString(status) }}</q-item-label>
             </q-item-section>
           </q-item>
         </q-list>
       </q-btn-dropdown>
       <span v-if="processStatus !== ProcessStatus.QUEUED"> der letzten {{daysLimit}} Tage</span>
     </div>
   </div>

   <q-card class="q-pa-md">
     <div class="row tableTopBtnRow no-wrap">
       <q-btn icon="refresh" label="Aktualisieren" @click="fetchProcesses"/>
     </div>

      <q-table
        :rows="filteredProcesses"
        row-key="process_id"
        :columns="processColumnsShown"
        no-data-label="Keine Prozesse mit diesen Kriterien vorhanden."
        dense flat bordered
        :rows-per-page-options="[0, 20, 50, 100]"
      >
        <!-- Custom Header Slot -->
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th v-for="col in props.cols" :key="col.name" :props="props" style="font-size: 13px">
              <i v-if="col.name === 'position'" class="material-icons" style="font-size: 16px; padding: 0 2px 2px 0;">
                info_outline
                <q-tooltip>
                  Wartelistenplätze werden nach ältestem Aktualisierungszeitstempel und Priorisierungsmarker vergeben.
                </q-tooltip>
              </i>
              {{ col.label }}
            </q-th>
          </q-tr>
          <q-tr>
            <q-th v-for="col in props.cols" :key="col.name">
              <q-input v-if="filterDict[col.name] !== undefined"
                       class="tableHeaderSearchInput" debounce="300" borderless
                       v-model="filterDict[col.name]" placeholder="Suchen"
                       @update:model-value="filterProcesses">
                <template v-slot:append>
                  <q-icon name="search"/>
                </template>
              </q-input>
            </q-th>
          </q-tr>
        </template>

        <template v-slot:body-cell-position="props">
          <q-td v-if="processStatus === ProcessStatus.QUEUED"
                style="text-align: right;" auto-width>
            {{ props.row.position }}
          </q-td>
        </template>

        <!-- Case Name Column (Highlight if missing) -->
        <template v-slot:body-cell-case_name="props">
          <q-td>
            <div v-if="props.row.case_name">
              {{ props.row.case_name }}
              <q-tooltip>Fall ID: {{ props.row.id }}</q-tooltip>
            </div>
            <q-badge v-else color="red-4">Kein Fall zugewiesen</q-badge>
          </q-td>
        </template>

        <!-- Evidence Item Name Column (Highlight if missing) -->
        <template v-slot:body-cell-evidence_item_name="props">
          <q-td>
            <div v-if="props.row.evidence_item_name">
              {{ props.row.evidence_item_name }}
              <q-tooltip>Asservat ID: {{ props.row.evidence_item_id }}</q-tooltip>
            </div>
            <q-badge v-else color="red-4">Kein Asservat zugewiesen</q-badge>
          </q-td>
        </template>

         <!-- Device Size Column  -->
        <template v-slot:body-cell-device_size="props">
          <q-td>
            <div>{{ formatDeviceSize(props.row.device_size) }}</div>
          </q-td>
        </template>

      </q-table>
    </q-card>
  </q-page>
</template>

<script setup>

import {computed, onBeforeMount, onMounted, ref} from "vue";
import {Notify} from "quasar";
import {
  formatDeviceSize,
  getProcessStatusColor,
  getProcessStatusDisplayString,
  ProcessStatus
} from "src/utils/processes_utils.js";

const workflowApiUrl = computed(() => {
  let url = new URL(window.location.toString());
  url.port = '8000';
  url.hash = ''
  url.pathname = '/'
  // remove ending '/' (appending path segments later would fail otherwise)
  return url.toString().replace(/\/$/, '');
});

const processes = ref([]);
const filteredProcesses = ref([]);

const filterProcesses = () => {
  filteredProcesses.value = processes.value.filter((item) =>
    Object.keys(filterDict.value).every((key) => {
      let fieldValue = item[key] ? item[key].toString().toLowerCase() : "";
      return fieldValue.includes(filterDict.value[key].toLowerCase());
    })
  );
};

const processColumns = [
  { name: "position", label: "Wartelistenplatz", field: "position", align: "left", sortable: true },
  { name: "case_name", label: "Fallname", field: "case_name", align: "left", sortable: true },
  { name: "evidence_item_name", label: "Asservatname", field: "evidence_item_name", align: "left", sortable: true },
  { name: "device_size", label: "Asservatgröße", field: "device_size", align: "left"},
  { name: "created_at", label: "Erstellt am", field: "created_at", align: "left", sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
  { name: "started_running_at", label: "Start am", field: "started_running_at", align: "left", sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
  { name: "ended_at", label: "Ende am", field: "ended_at", align: "left", sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
  { name: "updated_at", label: "Letzte Aktualisierung am", field: "updated_at", align: "left", sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
  { name: "process_id", label: "Prozess-ID", field: "id", align: "left", sortable: true },
];

const processColumnsShown = computed(() => {
  return processColumns.filter(col => {
    return (col.name !== 'position' || processStatus.value === ProcessStatus.QUEUED);
  })
})

const stepId = ref(2);
const stepDisplayString = ref("");
const processStatus = ref(ProcessStatus.QUEUED);
const filterDict = ref({
  evidence_item_name: "",
  case_name: "",
});

function selectStep(step){
  stepId.value = step;
  stepDisplayString.value = getForensicStepDisplayString(stepId.value)
  fetchProcesses();
}

function selectProcessStatus(status){
  processStatus.value = status;
  fetchProcesses();
}

const daysLimit = 14; //in days
const fetchProcesses = async () => {
  try {
    let url = `${workflowApiUrl.value}/processes/`
    url += `?step_id=${stepId.value}`
    
    // Only add status filter if not "alle"
    if (processStatus.value !== null) {
      url += `&status=${processStatus.value}`
    }

    // get only processes within last 14 days (for all processes except QUEUED - to not show the whole queue)
    if (processStatus.value !== ProcessStatus.QUEUED) {
      const d = new Date()
      d.setDate(d.getDate() - daysLimit)
      url += `&date_threshold=${d.toISOString()}`
    }

    console.log("GET " + url);
    const response = await fetch(url, { method: 'GET' });
    if (!response.ok) throw new Error('Fehler beim Abrufen der Prozesse');

    const data = await response.json();
    processes.value = data;

    for (let i=0; i < processes.value.length; i++) {
      const p = processes.value[i];
      p.position = i+1;

      if (p.evidence_item_id !== null) {
        const evidenceItemUrl = `${workflowApiUrl.value}/evidence-items/${p.evidence_item_id}`
        console.log("GET " + evidenceItemUrl);
        const response1 = await fetch(evidenceItemUrl, {method: 'GET'});
        if (!response1.ok) throw new Error(`Fehler beim Abrufen des Asservats ${p.evidence_item_id}`);
        const evidence_item = await response1.json();
        p.evidence_item_name = evidence_item.evidence_item_name;
        p.device_size = evidence_item.device_size;

        if (evidence_item.case_id !== null) {
          const caseUrl = `${workflowApiUrl.value}/cases/${evidence_item.case_id}`
          console.log("GET " + caseUrl);
          const response2 = await fetch(caseUrl, {method: 'GET'});
          if (!response2.ok) throw new Error(`Fehler beim Abrufen des Falls ${evidence_item.case_id}`);
          p.case_name = (await response2.json()).case_name;
        }
      }
    }

    filteredProcesses.value = [...processes.value];
  } catch (error) {
    console.error("Fehler beim Abrufen der Prozesse:", error);
    Notify.create({ message: "Fehler beim Abrufen der Prozesse", type: "negative", timeout: 5000 });
  }
};

function getForensicStepDisplayString(step){
  return forensicStepsDict[step]["display_name"];
}

const forensicStepsDict = {};
async function fetchForensicSteps() {
  try {
    // fetch evidence items
    const forensicStepsUrl = `${workflowApiUrl.value}/forensic-steps`;
    console.log("GET " + forensicStepsUrl);
    const responseForensicSteps = await fetch(forensicStepsUrl, {method: 'GET'});
    if (!responseForensicSteps.ok) throw new Error('Failed to fetch forensic steps.');


    const forensicStepsList = await responseForensicSteps.json();
    forensicStepsList.forEach(step => {
      forensicStepsDict[step['id']] = step;
    });

    stepDisplayString.value = getForensicStepDisplayString(stepId.value)

  } catch (err) {
    console.log(err)
    Notify.create({message: 'Error fetching forensic steps', type: 'negative', timeout: 10000});
  }
}

onBeforeMount(async () => {
  // fetch step names (used as column headers)
  await fetchForensicSteps();
})

onMounted(async () => {
  await fetchProcesses();
});
</script>

<style scoped>
.q-table {
  margin-top: 20px;
  width: 100%;
}

.q-btn {
  text-transform: none;
}

.tableHeaderSearchInput :deep(.q-field__control), :deep(.q-field__append) {
  height: 22px;
  padding: 0px;
  font-size: 13px;
}

.tableTopBtnRow {
  margin: 0px 0px 15px 0px;

  .q-btn {
    float: right;
    width: auto;
    height: 38px;
    background-color: #be1717;
    color: white;
    margin-right: 5px;
  }
}
</style>