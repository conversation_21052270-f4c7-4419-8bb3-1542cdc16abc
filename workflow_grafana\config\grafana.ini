[server]
protocol = http
http_port = 3000

[security]
admin_user = ${GF_SECURITY_ADMIN_USER}
admin_password = ${GF_SECURITY_ADMIN_PASSWORD}
allow_embedding = true
x_frame_options = allow-from http://localhost:8001

[auth.anonymous]
enabled = true
org_name = Main Org.
org_role = Viewer

[auth.basic]
enabled = false

[dashboard]
default_home_dashboard_path = /etc/grafana/provisioning/dashboards/client_dashboard.json.json
