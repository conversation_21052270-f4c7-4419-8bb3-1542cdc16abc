"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[452],{7452:(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var r=a(7781),n=a(7985),i=a(5959),l=a.n(i),d=a(2993),o=a(8732),c=a(1522),s=a(3347),u=a(4796),m=a(7818);function p(e){return"string"==typeof e&&r.dateMath.isMathString(e)?e:r.dateMath.toDateTime(new Date(e),{roundUp:!1}).toISOString()}const f=({query:e,initialStart:t,initialEnd:a,dataSource:r})=>{const[f]=(0,c.n)(),{metric:b,labels:w}=(0,m.$9)(e),k=p(t),_=p(a),g=(0,u.ef)({metric:b,initialDS:r.uid,initialFilters:w.map(({label:e,op:t,value:a})=>({key:e,operator:t,value:a})),$timeRange:new n.JZ({from:k,to:_}),embedded:!0}),h=(0,i.useRef)(!1);return(0,i.useEffect)(()=>{h.current||(h.current=!0,(0,s.z)("exposed_component_viewed",{component:"label_breakdown"}))},[]),l().createElement("div",{"data-testid":"metrics-drilldown-embedded-label-breakdown"},f?l().createElement(d.E,{error:f}):l().createElement(o.A,{trail:g}))}}}]);
//# sourceMappingURL=452.js.map?_cache=2b21d327e72e37b1c8bb