"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[619,836],{28:(e,t,r)=>{r.d(t,{Y:()=>s});var n,a,i,o=r(7781);class s extends o.BusEventWithPayload{}i="filters-changed",(a="type")in(n=s)?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i},384:(e,t,r)=>{function n(e){return null!==e&&"adhoc"===(null==e?void 0:e.state.type)}function a(e){return null!==e&&"custom"===(null==e?void 0:e.state.type)}function i(e){return null!==e&&"query"===(null==e?void 0:e.state.type)}r.d(t,{BE:()=>n,UG:()=>a,bA:()=>i})},416:(e,t,r)=>{r.d(t,{V:()=>n});const n={DATASOURCE:"datasource",BOOKMARKS:"bookmarks",RECENT_METRICS:"recent-metrics",BREAKDOWN_SORTBY:"breakdown.sortby"}},619:(e,t,r)=>{r.r(t),r.d(t,{sortSeries:()=>h,wasmSupported:()=>f});var n=r(6944),a=r(7781),i=r(3241),o=r(3616),s=r(4964),l=r(3347);function c(e){var t;const r=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!r)return null;const n=Object.keys(r);return 0===n.length?null:r[n[0]]}const u=(e,t="asc")=>{const r="asc"===t?(e,t)=>(0,s._)(e,t):(e,t)=>(0,s._)(t,e);return e.sort((e,t)=>{const n=c(e);if(!n)return 0;const a=c(t);return a?r(n,a):0})},d=(e,t,r="asc")=>{const n=a.fieldReducers.get(t),i=e.map(e=>{var r;const i=e.fields[1];if(!i)return{value:0,dataFrame:e};var o;var s;return{value:null!==(s=(null!==(o=null===(r=n.reduce)||void 0===r?void 0:r.call(n,i,!0,!0))&&void 0!==o?o:(0,a.doStandardCalcs)(i,!0,!0))[t])&&void 0!==s?s:0,dataFrame:e}});return i.sort("asc"===r?(e,t)=>e.value-t.value:(e,t)=>t.value-e.value),i.map(({dataFrame:e})=>e)},p=e=>{const t=(0,a.outerJoinDataFrames)({frames:e});if(!t)throw new Error("Error while joining frames into a single one");const r=t.fields.filter(e=>e.type===a.FieldType.number).map(e=>new Float64Array(e.values));return n.OutlierDetector.dbscan({sensitivity:.9}).detect(r)},m=(e,t)=>e.seriesResults[t].isOutlier?-e.seriesResults[t].outlierIntervals.length:0,h=(0,i.memoize)((e,t,r="asc")=>{if(!e.length)return[];const n=[...e];if("alphabetical"===t)return u(n,"asc");if("alphabetical-reversed"===t)return u(n,"desc");if("outliers"===t)try{return((e,t="asc")=>{if(!f())throw new Error("WASM not supported");const r=p(e),n=e.map((e,t)=>({value:m(r,t),dataFrame:e}));return n.sort("asc"===t?(e,t)=>e.value-t.value:(e,t)=>t.value-e.value),n.map(({dataFrame:e})=>e)})(n,r)}catch(e){const t=`Error while sorting by outlying series: "${e.toString()}"!`;return(0,o.HA)([t,"Falling back to standard deviation to identify the most variable series."]),d(n,a.ReducerID.stdDev,r)}return d(n,t,r)},(e,t,r="asc")=>{const n=g(e)?e[0].fields[0].values[0]:0,a=g(e)?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0;return`${e.length>0?c(e[0]):""}_${e.length>0?c(e[e.length-1]):""}_${n}_${a}_${e.length}_${t}_${r}`});function g(e){return e.length>0&&e[0].fields.length>0&&e[0].fields[0].values.length>0}const f=()=>{const e="object"==typeof WebAssembly;return e||(0,l.z)("wasm_not_supported",{}),e}},697:(e,t,r)=>{r.d(t,{x:()=>s});var n,a,i,o=r(7781);class s extends o.BusEventWithPayload{}i="metrics-variable-activated",(a="type")in(n=s)?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i},1053:(e,t,r)=>{r.d(t,{_:()=>s});var n=r(6089),a=r(2007),i=r(5959),o=r.n(i);function s({title:e,description:t}){const r=(0,a.useStyles2)(l);return o().createElement("h6",{className:r.title},o().createElement("span",null,e),o().createElement(a.Tooltip,{content:t,placement:"top"},o().createElement(a.Icon,{name:"info-circle",size:"sm",className:r.infoIcon})))}function l(e){return{title:(0,n.css)({fontSize:"15px",fontWeight:e.typography.fontWeightLight,borderBottom:`1px solid ${e.colors.border.weak}`,paddingBottom:e.spacing(.5)}),infoIcon:(0,n.css)({marginLeft:e.spacing(1),cursor:"pointer",color:e.colors.text.secondary,position:"relative",top:"-4px"})}}},1199:(e,t,r)=>{r.d(t,{B:()=>a});var n=r(3241);const a=(e,t)=>e.length===t.length&&(0,n.isEqual)(e,t)},1252:(e,t,r)=>{r.d(t,{fD:()=>$,NJ:()=>R,VN:()=>B,yH:()=>H,Rm:()=>V});var n=r(7985),a=r(5959),i=r.n(a),o=r(4964),s=r(8361),l=r(8531),c=r(2445),u=r(9851);function d(e){const t=u.K3.parse(e),r=new Set,n=t.cursor();do{if(n.type.is("VectorSelector")&&n.firstChild()){do{if(n.type.is("Identifier")){const t=e.slice(n.from,n.to);t&&r.add(t)}}while(n.nextSibling());n.parent()}}while(n.next());return Array.from(r)}function p(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}const m={showSuccessAlert:!1,showErrorAlert:!1};function h(){return(e=function*(){try{return function(e){const t={};for(const r in e)t[r]={usageType:"alerting-usage",count:e[r]};return t}(function(e){const t={},r=e.filter(e=>(null==e?void 0:e.data.length)>0);for(const e of r){const r=e.data.filter(e=>{var t;return"string"==typeof(null===(t=e.model)||void 0===t?void 0:t.expr)&&"__expr__"!==e.datasourceUid});for(const n of r)try{const e=d(n.model.expr);for(const r of e)t[r]=(t[r]||0)+1}catch(t){c.v.warn(t,{message:`Failed to parse PromQL expression in alert rule ${e.title}`})}}return t}(yield(0,l.getBackendSrv)().get("/api/v1/provisioning/alert-rules",void 0,"grafana-metricsdrilldown-app-alert-rule-metric-usage",m)))}catch(e){const t="string"==typeof e?new Error(e):e;return c.v.error(t,{message:"Failed to fetch alerting rules"}),{}}},function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){p(i,n,a,o,s,"next",e)}function s(e){p(i,n,a,o,s,"throw",e)}o(void 0)})})();var e}var g=r(7348),f=r(7476);function b(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function y(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){b(i,n,a,o,s,"next",e)}function s(e){b(i,n,a,o,s,"throw",e)}o(void 0)})}}function v(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function w(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const S={showSuccessAlert:!1,showErrorAlert:!1},O=new Map,E=(0,g.g)((e,t,r)=>y(function*(){let n=O.get(e);return n||(n=(0,l.getBackendSrv)().get(`/api/dashboards/uid/${e}`,void 0,`grafana-metricsdrilldown-app-dashboard-metric-usage-${e}`,S).then(({dashboard:e})=>w(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){v(e,t,r[t])})}return e}({},e),{url:t})).catch(t=>(r<=5&&c.v.error(t,{dashboardUid:e}),r++,Promise.resolve(null))).finally(()=>{O.delete(e)}),O.set(e,n)),n})(),{concurrency:50});function x(){return y(function*(){try{const e=yield(0,l.getBackendSrv)().get("/api/search",{type:"dash-db",limit:500},"grafana-metricsdrilldown-app-dashboard-search",S);let t=0;return yield Promise.all(e.map(({uid:e,url:r})=>E(e,r,t))).then(P)}catch(e){const t="string"==typeof e?new Error(e):e;return c.v.error(t,{message:"Failed to fetch dashboard metrics"}),{}}})()}function k(e,t,r,n,a){var i;(a[e]||(a[e]={usageType:"dashboard-usage",count:0,dashboards:{}}),a[e].count++,"dashboard-usage"===a[e].usageType)&&(a[e].dashboards[t]={count:((null===(i=a[e].dashboards[t])||void 0===i?void 0:i.count)||0)+1,uid:r||"unknown",url:n})}function P(e){const t={},r=e.filter(e=>{var t;return e&&(null==e||null===(t=e.panels)||void 0===t?void 0:t.length)});for(const e of r){const r=e.title||`Dashboard ${e.uid}`,n=e.panels.filter(e=>{var t;return(0,f.aQ)(e.datasource)&&"targets"in e&&(null===(t=e.targets)||void 0===t?void 0:t.length)});for(const a of n)for(const n of a.targets){const a=d("string"==typeof n.expr?n.expr:"");for(const n of a)k(n,r,e.uid||"unknown",e.url,t)}}return t}class j{getUsageMetrics(e){return this._usageState[e].metrics&&Object.keys(this._usageState[e].metrics).length>0?Promise.resolve(this._usageState[e].metrics):(this._usageState[e].metricsPromise||(this._usageState[e].metricsPromise=this._usageState[e].fetcher().then(t=>(this._usageState[e].metrics=t,this._usageState[e].metricsPromise=void 0,t))),this._usageState[e].metricsPromise)}getUsageForMetric(e,t){return this.getUsageMetrics(t).then(t=>{var r,n;return null!==(n=null===(r=t[e])||void 0===r?void 0:r.count)&&void 0!==n?n:0})}getUsageDetailsForMetric(e,t){return this.getUsageMetrics(t).then(r=>{var n;return null!==(n=r[e])&&void 0!==n?n:"dashboard-usage"===t?{usageType:"dashboard-usage",count:0,dashboards:{}}:{usageType:"alerting-usage",count:0}})}constructor(){var e,t,r;r={"dashboard-usage":{metrics:{},metricsPromise:void 0,fetcher:x},"alerting-usage":{metrics:{},metricsPromise:void 0,fetcher:h}},(t="_usageState")in(e=this)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}}var C=r(416),_=r(5731);function N(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){N(e,t,r[t])})}return e}function T(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const D=6,A=30;function B(e){try{const t=I(),r=Date.now(),n=t.filter(t=>t.name!==e);n.unshift({name:e,timestamp:r});const a=n.slice(0,D);_.K.setItem(C.V.RECENT_METRICS,a)}catch(t){const r=t instanceof Error?t:new Error(String(t));c.v.error(r,T(L({},r.cause||{}),{metricName:e}))}}function I(){try{const e=_.K.getItem(C.V.RECENT_METRICS)||[];if(!e.length)return[];const t=Date.now()-24*A*60*60*1e3,r=e.filter(e=>e.timestamp>t);return r.length!==e.length&&_.K.setItem(C.V.RECENT_METRICS,r),r}catch(e){return c.v.error(e,{message:"Failed to get recent metrics:"}),[]}}const M=[{label:"Default",value:"default"},{label:"Dashboard Usage",value:"dashboard-usage"},{label:"Alerting Usage",value:"alerting-usage"}],R="metrics-reducer-sort-by";class $ extends n.Bs{activationHandler(){const e=n.jh.getVariables(this).getByName(R);this.supportedSortByOptions.has(e.getValue())||e.changeValueTo("default"),this._subs.add(e.subscribeToState((e,t)=>{e.value!==t.value&&this.publishEvent(new s.S({sortBy:e.value}),!0)}))}getUsageDetailsForMetric(e,t){return this.usageFetcher.getUsageDetailsForMetric(e,t)}getUsageMetrics(e){return this.usageFetcher.getUsageMetrics(e).then(e=>{const t={};for(const r in e)t[r]=e[r].count;return t})}constructor(e){super(T(L({},e),{key:"metrics-sorter",$variables:new n.Pj({variables:[new n.yP({name:R,label:"Sort by",value:"default",query:M.map(e=>`${e.label} : ${e.value}`).join(","),description:"Sort metrics by default (alphabetically, with recently-selected metrics first), by prevalence in dashboard panel queries, or by prevalence in alerting rules"})]}),inputControls:new n.K8({layout:"horizontal"})})),N(this,"initialized",!1),N(this,"supportedSortByOptions",new Set(["default","dashboard-usage","alerting-usage"])),N(this,"usageFetcher",new j),this.addActivationHandler(()=>this.activationHandler())}}function H(e,t){return[...e].sort((e,r)=>{const n=t[e]||0,a=t[r]||0;return a!==n?a-n:(0,o._)(e,r)})}function V(e){const t=I().map(e=>e.name),r=new Set(t),[n,a]=e.reduce(([e,t],n)=>(r.has(n)?e.push(n):t.push(n),[e,t]),[[],[]]),i=function(e){return[...e].sort((e,t)=>(0,o._)(e,t))}(a);return[...t.filter(e=>n.includes(e)),...i]}N($,"Component",({model:e})=>{const{inputControls:t}=e.useState();return i().createElement("div",{"data-testid":"sort-by-select"},i().createElement(t.Component,{model:t}))})},1464:(e,t,r)=>{r.d(t,{I:()=>o});var n=r(697),a=r(6920),i=r(7397);function o(e){const t=e.state.key;if(!t)throw new TypeError(`Variable "${e.state.name}" has no key. Please provide a key in order to publish its lifecycle events.`);return e.addActivationHandler(()=>(e.publishEvent(new n.x({key:t}),!0),!e.state.loading&&e.state.options.length&&e.publishEvent(new i.x({key:t,options:e.state.options}),!0),e.subscribeToState((r,n)=>{!r.loading&&n.loading&&e.publishEvent(new i.x({key:t,options:r.options}),!0)}),()=>{e.publishEvent(new a.e({key:t}),!0)})),e}},1522:(e,t,r)=>{r.d(t,{n:()=>o});var n=r(5959),a=r(2445);function i(e,t){return e instanceof Error?e:"string"==typeof e?new Error(e):"string"==typeof e.message?new Error(e.message):new Error(t)}function o(){const[e,t]=(0,n.useState)();return(0,n.useEffect)(()=>{const e=e=>{(function(e){var t,r,n,i;if(e.filename&&new URL(e.filename).protocol.endsWith("extension:"))return a.v.error(new Error(`Browser extension error: ${e.message}`),{filename:e.filename,lineno:null===(t=e.lineno)||void 0===t?void 0:t.toString(),colno:null===(r=e.colno)||void 0===r?void 0:r.toString()}),!1;return null!==e.error||!e.message||(a.v.error(new Error(`Non-critical error: ${e.message}`),{filename:e.filename,lineno:null===(n=e.lineno)||void 0===n?void 0:n.toString(),colno:null===(i=e.colno)||void 0===i?void 0:i.toString()}),!1)})(e)&&t(i(e.error,"Uncaught exception!"))},r=e=>{"cancelled"!==e.reason.type?t(i(e.reason,"Unhandled rejection!")):t(void 0)};return window.addEventListener("error",e),window.addEventListener("unhandledrejection",r),()=>{window.removeEventListener("unhandledrejection",r),window.removeEventListener("error",e)}},[]),[e,t]}},1816:(e,t,r)=>{r.d(t,{W:()=>s});var n,a,i,o=r(7781);class s extends o.BusEventWithPayload{}i="quick-search-changed",(a="type")in(n=s)?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i},1955:(e,t,r)=>{r.d(t,{b:()=>U});var n=r(6089),a=r(7781),i=r(1932),o=r(8531),s=r(7985),l=r(2007),c=r(5959),u=r.n(c),d=r(2127),p=r(2445),m=r(7476),h=r(416),g=r(5731);class f extends s.mI{onActivate(){this.setState({skipUrlSync:!1}),this.subscribeToState((e,t)=>{e.value&&e.value!==t.value&&g.K.setItem(h.V.DATASOURCE,e.value)})}static getCurrentDataSource(){const e=Object.values(o.config.datasources).filter(e=>(0,m.aQ)(e)),t=new URL(window.location.href).searchParams.get(`var-${d.EY}`),r=g.K.getItem(h.V.DATASOURCE),n=e.find(e=>e.uid===t)||e.find(e=>e.uid===r)||e.find(e=>e.isDefault)||e[0];return n?n.uid:(p.v.warn("Cannot find any Prometheus data source!"),"no-data-source-configured")}constructor({initialDS:e}){super({key:d.EY,name:d.EY,pluginId:"prometheus",label:"Data source",description:"Only prometheus data sources are supported",skipUrlSync:!e,value:e||f.getCurrentDataSource()}),this.addActivationHandler(this.onActivate.bind(this))}}const b=(0,c.memo)(function({size:e}){const t=(0,l.useStyles2)(y);return u().createElement("img",{className:(0,n.cx)(t.logo,e),src:"public/plugins/grafana-metricsdrilldown-app/img/logo.svg"})}),y=()=>({logo:n.css`
    &.small {
      width: 24px;
      height: 24px;
      margin-right: 4px;
      position: relative;
      top: -2px;
    }

    &.large {
      width: 40px;
      height: 40px;
    }
  `});const v=r(5176).t,w=`https://github.com/grafana/metrics-drilldown/commit/${v}`,{buildInfo:S}=o.config;function O(){const e=(0,l.useStyles2)(k),{meta:{info:{version:t,updated:r}}}=(0,a.usePluginContext)()||{meta:{info:{version:"?.?.?",updated:"?"}}};return u().createElement("div",{className:e.menuHeader},u().createElement("h5",null,u().createElement(b,{size:"small"}),"Grafana Metrics Drilldown v",t),u().createElement("div",{className:e.subTitle},"Last update: ",r))}function E(){const e="dev"===v,t=e?v:v.slice(0,8);return u().createElement(l.Menu,{header:u().createElement(O,null)},u().createElement(l.Menu.Item,{label:`Commit SHA: ${t}`,icon:"github",onClick:()=>window.open(w),disabled:e}),u().createElement(l.Menu.Item,{label:"Changelog",icon:"list-ul",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/blob/main/CHANGELOG.md","_blank","noopener,noreferrer")}),u().createElement(l.Menu.Item,{label:"Contribute",icon:"external-link-alt",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/blob/main/docs/contributing.md","_blank","noopener,noreferrer")}),u().createElement(l.Menu.Item,{label:"Documentation",icon:"document-info",onClick:()=>window.open("https://grafana.com/docs/grafana/latest/explore/simplified-exploration/metrics","_blank","noopener,noreferrer")}),u().createElement(l.Menu.Item,{label:"Report an issue",icon:"bug",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/issues/new?template=bug_report.md","_blank","noopener,noreferrer")}),u().createElement(l.Menu.Divider,null),u().createElement(l.Menu.Item,{label:`Grafana ${S.edition} v${S.version} (${S.env})`,icon:"grafana",onClick:()=>window.open(`https://github.com/grafana/grafana/commit/${S.commit}`,"_blank","noopener,noreferrer")}))}function x(){return u().createElement(l.Dropdown,{overlay:()=>u().createElement(E,null),placement:"bottom-end"},u().createElement(l.Button,{icon:"info-circle",variant:"secondary",tooltip:"Plugin info",tooltipPlacement:"top",title:"Plugin info","data-testid":"plugin-info-button"}))}const k=e=>({button:n.css`
    position: relative;
    display: flex;
    align-items: center;
    width: 32px;
    height: 32px;
    line-height: 30px;
    border: 1px solid ${e.colors.border.weak};
    border-radius: 2px;
    border-left: 0;
    color: ${e.colors.text.primary};
    background: ${e.colors.background.secondary};

    &:hover {
      border-color: ${e.colors.border.medium};
      background-color: ${e.colors.background.canvas};
    }
  `,menuHeader:n.css`
    padding: ${e.spacing(.5,1)};
    white-space: nowrap;
  `,subTitle:n.css`
    color: ${e.colors.text.secondary};
    font-size: ${e.typography.bodySmall.fontSize};
  `});var P=r(3616),j=r(8499),C=r(3347),_=r(2634),N=r(4796);function L(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class T extends s.Bs{constructor(e){var t,r;super({stickyMainGraph:null!==(t=e.stickyMainGraph)&&void 0!==t&&t,isOpen:null!==(r=e.isOpen)&&void 0!==r&&r}),L(this,"onToggleStickyMainGraph",()=>{const e=!this.state.stickyMainGraph;(0,C.z)("settings_changed",{stickyMainGraph:e}),this.setState({stickyMainGraph:e})}),L(this,"onToggleOpen",e=>{this.setState({isOpen:e})})}}function D(e){return{popover:(0,n.css)({display:"flex",padding:e.spacing(2),flexDirection:"column",background:e.colors.background.primary,boxShadow:e.shadows.z3,borderRadius:e.shape.radius.default,border:`1px solid ${e.colors.border.weak}`,zIndex:1,marginRight:e.spacing(2)}),heading:(0,n.css)({fontWeight:e.typography.fontWeightMedium,paddingBottom:e.spacing(2)}),options:(0,n.css)({display:"grid",gridTemplateColumns:"1fr 50px",rowGap:e.spacing(1),columnGap:e.spacing(2)})}}L(T,"Component",({model:e})=>{const{stickyMainGraph:t,isOpen:r}=e.useState(),n=(0,l.useStyles2)(D),a=(0,N.kj)(e),{topScene:i}=a.useState();if(!(i instanceof _.R))return null;return u().createElement(l.Dropdown,{overlay:()=>u().createElement("div",{className:n.popover,onClick:e=>e.stopPropagation()},u().createElement("div",{className:n.heading},"Settings"),i instanceof _.R&&u().createElement("div",{className:n.options},u().createElement("div",null,"Always keep selected metric graph in-view"),u().createElement(l.Switch,{value:t,onChange:e.onToggleStickyMainGraph}))),placement:"bottom",onVisibleChange:e.onToggleOpen},u().createElement(l.ToolbarButton,{icon:"cog",variant:"canvas",isOpen:r,"data-testid":"settings-button"}))});var A=r(5490),B=r(2425),I=r(7265),M=r(7019),R=r(384);function $(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function H(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){$(i,n,a,o,s,"next",e)}function s(e){$(i,n,a,o,s,"throw",e)}o(void 0)})}}function V(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){V(e,t,r[t])})}return e}function z(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}class U extends s.Bs{_onActivate(){this.setState({trailActivated:!0}),this.state.topScene||this.setState({topScene:q(this.state.metric)}),this.subscribeToEvent(d.OO,this._handleMetricSelectedEvent.bind(this));const e=s.jh.lookupVariable(d.Ao,this);(0,R.BE)(e)&&this._subs.add(null==e?void 0:e.subscribeToState((e,t)=>{this._addingFilterWithoutReportingInteraction||(0,C.h)(e.filters,t.filters)}));const t=()=>{const e=s.jh.lookupVariable(d.Ao,this),t=(0,R.BE)(e)&&e.state.filters.length>0;(this.state.metric||t)&&(0,B._r)().setRecentTrail(this)};return window.addEventListener("unload",t),()=>{this.state.embedded||t(),window.removeEventListener("unload",t)}}addFilterWithoutReportingInteraction(e){const t=s.jh.lookupVariable(d.Ao,this);(0,R.BE)(t)&&(this._addingFilterWithoutReportingInteraction=!0,t.setState({filters:[...t.state.filters,e]}),this._addingFilterWithoutReportingInteraction=!1)}getMetricMetadata(e){return this.datasourceHelper.getMetadataForMetric(e)}isNativeHistogram(e){return this.datasourceHelper.isNativeHistogram(e)}initializeHistograms(){return H(function*(){if(this.state.histogramsLoadP)return this.state.histogramsLoadP;if(!this.state.histogramsLoaded){try{const e=this.datasourceHelper.initializeHistograms();this.setState({histogramsLoadP:e}),yield e}catch(e){(0,P.HA)(["Error while initializing histograms!",e.toString()])}this.setState({nativeHistograms:this.listNativeHistograms(),histogramsLoadP:null,histogramsLoaded:!0})}}).call(this)}listNativeHistograms(){var e;return null!==(e=this.datasourceHelper.listNativeHistograms())&&void 0!==e?e:[]}resetNativeHistograms(){this.setState({histogramsLoaded:!1,nativeHistograms:[]})}getCurrentMetricMetadata(){return this.getMetricMetadata(this.state.metric)}_handleMetricSelectedEvent(e){return H(function*(){var t;const r=null!==(t=e.payload)&&void 0!==t?t:"";let n=!1;this.isNativeHistogram(r)&&(n=!0),this._urlSync.performBrowserHistoryAction(()=>{this.setState(this.getSceneUpdatesForNewMetricValue(r,n))});const a=s.jh.lookupVariable(d.Ao,this);(0,R.BE)(a)&&a.setState({baseFilters:W(e.payload)})}).call(this)}getSceneUpdatesForNewMetricValue(e,t){const r={};return r.metric=e,r.nativeHistogramMetric=t?"1":"",r.topScene=q(e,t),r}getUrlState(){const{metric:e,metricSearch:t,nativeHistogramMetric:r}=this.state;return{metric:e,metricSearch:t,nativeHistogramMetric:r}}updateFromUrl(e){const t={};if("string"==typeof e.metric){if(this.state.metric!==e.metric){let r=!1;"1"===e.nativeHistogramMetric&&(r=!0),Object.assign(t,this.getSceneUpdatesForNewMetricValue(e.metric,r))}}else null!=e.metric||this.state.embedded||(t.metric=void 0,t.topScene=new j.m);"string"==typeof e.metricSearch?t.metricSearch=e.metricSearch:null==e.metric&&(t.metricSearch=void 0),this.setState(t)}getQueries(){return s.jh.findAllObjects(this,e=>(0,I.xT)(e)).reduce((e,t)=>(e.push(...t.state.queries.map(e=>z(F({},e),{expr:s.jh.interpolate(t,e.expr)}))),e),[])}constructor(e){var t,r,n,a,i,o,l,c,u,p,m;super(F({$timeRange:null!==(r=e.$timeRange)&&void 0!==r?r:new s.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:G(e.initialDS,e.metric,e.initialFilters),controls:null!==(a=e.controls)&&void 0!==a?a:[new s.K8({layout:"vertical"}),new s.N0,new s.KE({}),new s.WM({})],settings:null!==(i=e.settings)&&void 0!==i?i:new T({}),pluginInfo:new s.dM({component:x}),createdAt:null!==(o=e.createdAt)&&void 0!==o?o:(new Date).getTime(),dashboardMetrics:{},alertingMetrics:{},nativeHistograms:null!==(l=e.nativeHistograms)&&void 0!==l?l:[],histogramsLoadP:null!==(c=e.histogramsLoadP)&&void 0!==c?c:null,histogramsLoaded:null!==(u=e.histogramsLoaded)&&void 0!==u&&u,nativeHistogramMetric:null!==(p=e.nativeHistogramMetric)&&void 0!==p?p:"",trailActivated:null!==(m=e.trailActivated)&&void 0!==m&&m},e)),V(t=this,"_urlSync",new s.So(t,{keys:["metric","metricSearch","nativeHistogramMetric"]})),V(t,"_variableDependency",new s.Sh(t,{variableNames:[d.EY],onReferencedVariableValueChanged:e=>H(function*(){const{name:r}=e.state;r===d.EY&&(t.datasourceHelper.reset(),t.resetNativeHistograms())})()})),V(t,"_addingFilterWithoutReportingInteraction",!1),V(t,"datasourceHelper",new A.q(t)),t.addActivationHandler(t._onActivate.bind(t))}}function q(e,t=!1){return e?new _.R({metric:e,nativeHistogram:t}):new j.m}function G(e,t,r){let n=[new f({initialDS:e}),new s.H9({key:d.Ao,name:d.Ao,label:"Filters",addFilterButtonText:"Add label",datasource:d.GH,hide:a.VariableHide.dontHide,layout:"combobox",filters:null!=r?r:[],baseFilters:W(t),applyMode:"manual",allowCustomValue:!0,expressionBuilder:e=>e.filter(e=>"__name__"!==e.key).map(e=>`${(0,i.Nc)(e.key)}${e.operator}"${e.value.replaceAll("=","=")}"`).join(",")})];return Boolean(o.config.featureToggles.scopeFilters&&o.config.featureToggles.enableScopesInMetricsExplore&&!o.config.buildInfo.version.startsWith("11."))&&n.unshift(new s.Kg({enable:!0})),new s.Pj({variables:n})}function K(e,t,r){const a=(0,M.T)(e,r);return{container:(0,n.css)({flexGrow:1,display:"flex",gap:e.spacing(1),flexDirection:"column",padding:e.spacing(1,2),position:"relative",background:a}),body:(0,n.css)({flexGrow:1,display:"flex",flexDirection:"column",minHeight:0}),controls:(0,n.css)({display:"flex",gap:e.spacing(1),padding:e.spacing(1,0),alignItems:"flex-end",flexWrap:"wrap",position:"sticky",background:a,zIndex:e.zIndex.navbarFixed,top:t,borderBottom:`1px solid ${e.colors.border.weak}`}),settingsInfo:(0,n.css)({display:"flex",gap:e.spacing(.5)})}}function W(e){return e?[{key:"__name__",operator:"=",value:e}]:[]}function Q(){const e=document.querySelector('[data-testid="app-controls"]');if(!e)return;const{height:t}=e.getBoundingClientRect();document.documentElement.style.setProperty("--app-controls-height",`${t}px`)}V(U,"Component",({model:e})=>{const{controls:t,topScene:r,settings:n,pluginInfo:a,embedded:i}=e.useState();var p;const m=null!==(p=(0,o.useChromeHeaderHeight)())&&void 0!==p?p:0,h=i?0:m,g=(0,l.useStyles2)(K,h,e);return(0,c.useEffect)(()=>{e.initializeHistograms()},[e]),(0,c.useEffect)(()=>{const t=s.jh.lookupVariable(d.Ao,e),r=e.datasourceHelper;(0,N.FG)(e,t,r)},[e]),(0,c.useEffect)(()=>{Q();const e=document.querySelector('[data-testid="app-controls"]');if(!e)return;const t=new ResizeObserver(Q);return t.observe(e),()=>{t.disconnect(),document.documentElement.style.removeProperty("--app-controls-height")}},[i,t]),u().createElement("div",{className:g.container},t&&u().createElement("div",{className:g.controls,"data-testid":"app-controls"},t.map(e=>u().createElement(e.Component,{key:e.state.key,model:e})),u().createElement("div",{className:g.settingsInfo},u().createElement(n.Component,{model:n}),u().createElement(a.Component,{model:a}))),r&&u().createElement(s.$L,{scene:r,createBrowserHistorySteps:!0,updateUrlOnInit:!0,namespace:e.state.urlNamespace},u().createElement("div",{className:g.body},r&&u().createElement(r.Component,{model:r}))))})},2024:(e,t,r)=>{r.d(t,{d:()=>o});var n=r(2007),a=r(5959),i=r.n(a);function o({label:e,batchSizes:t,onClick:r,tooltip:a}){return i().createElement(n.Button,{variant:"secondary",fill:"outline",onClick:r,tooltip:a,tooltipPlacement:"top"},"Show ",t.increment," more ",1===t.increment?e:`${e}s`," (",t.current,"/",t.total,")")}},2062:(e,t,r)=>{r.d(t,{d:()=>s,l:()=>o});var n=r(1932),a=r(8162),i=r(2127);function o(e){return e.toString().replaceAll('="__REMOVE__"',"")}function s(e){const{metric:t,matchers:r,addIgnoreUsageFilter:o}=e,s=r.map(e=>({label:(0,n.Nc)(e.key),operator:e.operator,value:e.value}));o&&s.push({label:"__ignore_usage__",operator:a.md.equal,value:""});return!(0,n.Rq)(t)&&s.push({label:(0,n.Nc)(t),operator:a.md.equal,value:"__REMOVE__"}),s.push({label:i.ui,operator:a.md.equal,value:"__REMOVE__"}),new a.r4({metric:t,values:{},defaultOperator:a.md.equal,defaultSelectors:s})}},2127:(e,t,r)=>{r.d(t,{Ao:()=>l,Az:()=>f,EY:()=>h,GH:()=>w,H0:()=>x,Kf:()=>b,OO:()=>E,QX:()=>s,Rp:()=>d,aZ:()=>m,gR:()=>g,hc:()=>y,td:()=>v,ui:()=>c,up:()=>S,ym:()=>O,yr:()=>p});var n=r(7781),a=r(7985),i=r(2245);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const s="/explore/metrics",l="filters",c="${filters}",u="metric",d="${metric}",p="groupby",m="${groupby}",h="ds",g="${ds}",f="logsDs",b="${logsDs}",y="other_metric_filters",v="$__logs__",w={uid:g},S=250;function O(e){return[new a.x0({name:u,value:e,hide:i.zL.hideVariable})]}class E extends n.BusEventWithPayload{}o(E,"type","metric-selected-event");class x extends n.BusEventBase{}o(x,"type","refresh-metrics-event")},2425:(e,t,r)=>{r.d(t,{yn:()=>S,_r:()=>E});var n=r(7781),a=r(7985),i=r(3241),o=r(8531),s=r(2007),l=r(5959),c=r.n(l),u=r(4137),d=r(2127),p=r(4796);var m=r(1955),h=r(416),g=r(5731);function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const b="grafana.trails.recent";class y{_loadRecentTrailsFromStorage(){const e=[],t=g.K.getItem(b)||[];for(const r of t){const t=this._deserializeTrail(r);e.push(t.getRef())}return e}_loadBookmarksFromStorage(){return(g.K.getItem(h.V.BOOKMARKS)||[]).map(e=>{if(null!=(t=e)&&"object"==typeof t&&"history"in t){const t=null!=e.currentStep?e.currentStep:e.history.length-1;return{urlValues:e.history[t].urlValues,createdAt:e.createdAt||Date.now()}}var t;return e})}_deserializeTrail(e){const t=new m.b({}),r="urlValues"in e;return"history"in e?e.history.forEach(e=>{this._loadFromUrl(t,e.urlValues)}):r&&this._loadFromUrl(t,e.urlValues),t.setState(a.Go.cloneSceneObjectState(t.state,{})),t}_serializeTrail(e){return{urlValues:a.Go.getUrlState(e)}}getTrailForBookmarkIndex(e){const t=this._bookmarks[e];return t?this.getTrailForBookmark(t):(0,p.ef)()}getTrailForBookmark(e){const t=S(e);for(const e of this._recent){const r=e.resolve();if(S(r)===t)return r}const r=new m.b({});return this._loadFromUrl(r,e.urlValues),r}_loadFromUrl(e,t){const r=n.urlUtil.renderUrl("",t);a.Go.syncStateFromSearchParams(e,new URLSearchParams(r))}get recent(){return this._recent}get lastModified(){return this._lastModified}load(){this._recent=this._loadRecentTrailsFromStorage(),this._bookmarks=this._loadBookmarksFromStorage(),this._refreshBookmarkIndexMap(),this._lastModified=Date.now()}setRecentTrail(e){if(!e.state.trailActivated)return;this._recent=this._recent.filter(t=>t!==e.getRef());const t=v(e);this._recent=this._recent.filter(e=>{const r=v(e.resolve());return!(0,i.isEqual)(t,r)}),this._recent.unshift(e.getRef()),this._save()}get bookmarks(){return this._bookmarks}addBookmark(e){const t={urlValues:a.Go.getUrlState(e),createdAt:Date.now()};this._bookmarks.unshift(t),this._refreshBookmarkIndexMap(),this._save(),function(){const e=(0,o.getAppEvents)(),t=(0,p.y)(u.bw.Drilldown),r=t?c().createElement("i",null,"the Metrics Reducer sidebar"):c().createElement("i",null,"Drilldown > Metrics");e.publish({type:n.AppEvents.alertSuccess.name,payload:["Bookmark created",c().createElement(s.Stack,{gap:2,direction:"row",key:"bookmark-notification"},c().createElement("div",null,"You can view bookmarks under ",r),!t&&c().createElement(s.LinkButton,{fill:"solid",variant:"secondary",href:d.QX},"View bookmarks"))]})}()}removeBookmark(e){e<this._bookmarks.length&&(this._bookmarks.splice(e,1),this._refreshBookmarkIndexMap(),this._save())}getBookmarkIndex(e){const t=S(e);return this._bookmarkIndexMap.get(t)}_refreshBookmarkIndexMap(){this._bookmarkIndexMap.clear(),this._bookmarks.forEach((e,t)=>{const r=S(e);this._bookmarkIndexMap.set(r,t)})}constructor(){f(this,"_recent",[]),f(this,"_bookmarks",[]),f(this,"_save",void 0),f(this,"_lastModified",void 0),f(this,"_bookmarkIndexMap",new Map),this.load(),this._lastModified=Date.now();const e=()=>{const e=this._recent.slice(0,20).map(e=>this._serializeTrail(e.resolve()));g.K.setItem(b,e),g.K.setItem(h.V.BOOKMARKS,this._bookmarks),this._lastModified=Date.now()};this._save=(0,i.debounce)(e,1e3),window.addEventListener("beforeunload",()=>{this._save=e})}}function v(e){const t=a.Go.getUrlState(e);return w(t),t}function w(e){var t;(delete e.actionView,delete e.layout,delete e.metricSearch,delete e.refresh,""!==e["var-groupby"]&&void 0!==e["var-groupby"]||(e["var-groupby"]="$__all"),"string"!=typeof e["var-filters"])&&(e["var-filters"]=null===(t=e["var-filters"])||void 0===t?void 0:t.filter(e=>""!==e));return e}function S(e){return e instanceof m.b?JSON.stringify(v(e)):JSON.stringify(w(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){f(e,t,r[t])})}return e}({},e.urlValues)))}let O;function E(){return O||(O=new y),O}},2634:(e,t,r)=>{r.d(t,{R:()=>Br});var n=r(6089),a=r(8531),i=r(7985),o=r(2007),s=r(5959),l=r.n(s),c=r(3347);var u,d,p,m=r(2127),h=r(384);class g extends i.fS{onActivate(){this.subscribeToState((e,t)=>{e.value&&e.value!==t.value&&(0,c.z)("groupby_label_changed",{label:String(e.value)})});const e=i.jh.lookupVariable(m.Ao,this);(0,h.BE)(e)&&e.subscribeToState((e,t)=>{e.filterExpression!==t.filterExpression&&this.changeValueTo("$__all")})}constructor(){super({name:m.yr,label:"Group by",datasource:m.GH,includeAll:!0,defaultToAll:!0,query:`label_names(${m.Rp})`,value:"",text:""}),this.addActivationHandler(this.onActivate.bind(this))}}function f(e){return{select:n.css`
      width: ${e.spacing(16)};
      & > div {
        width: 100%;
      }
    `}}p=({model:e})=>{const t=(0,o.useStyles2)(f);return l().createElement("div",{className:t.select,"data-testid":"breakdown-label-selector"},l().createElement(i.fS.Component,{model:e}))},(d="Component")in(u=g)?Object.defineProperty(u,d,{value:p,enumerable:!0,configurable:!0,writable:!0}):u[d]=p;const b={OPEN_EXPLORE_LABEL:"Open in explore",COPY_URL_LABEL:"Copy url",BOOKMARK_LABEL:"Bookmark",SELECT_NEW_METRIC_TOOLTIP:"Remove existing metric and choose a new metric"};var y=r(7818),v=r(6467),w=r(7781),S=r(7940);function O(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class E extends i.Bs{static getOptions(e,t){return[v.Gx.HEATMAP,v.Gx.PERCENTILES].includes(t)?[{value:v.Gx.PERCENTILES,label:"percentiles"},{value:v.Gx.HEATMAP,label:"heatmap"}]:[]}constructor({metric:e,panelType:t}){super({metric:e,panelType:t,options:E.getOptions(e,t),currentPanelType:t}),O(this,"onChange",e=>{this.publishEvent(new S.H({panelType:e}),!0)})}}O(E,"Component",({model:e})=>{const{options:t,currentPanelType:r}=e.useState();return t.length?l().createElement(o.RadioButtonGroup,{size:"sm",options:t,value:r,onChange:e.onChange}):null});var x=r(5490),k=r(1269),P=r(2445);const j=r.p+"ac01ecbc64128d2f3e68.svg";var C=r(4796),_=r(2533),N=r(7265);function L(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){L(e,t,r[t])})}return e}function D(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const A=`${_.id}/investigation/v1`;class B extends i.Bs{getPanelConfigAndDataFrames(){var e;const t=(0,C.UX)(this,e=>e instanceof i.Eb,i.Eb),r=i.jh.getData(this);return{fieldConfig:null==t?void 0:t.state.fieldConfig,frames:null==r||null===(e=r.state.data)||void 0===e?void 0:e.series}}constructor(e){super(D(T({},e),{queries:[]})),L(this,"_onActivate",()=>{this._subs.add(this.subscribeToState(()=>{this.getQueries(),this.getContext()}));const e=i.jh.interpolate(this,m.gR);this.setState({dsUid:e})}),L(this,"getQueries",()=>{const e=i.jh.getData(this),t=i.jh.findObject(e,N.xT);if((0,N.xT)(t)){const e=this.state.frame?I(this.state.frame):null,r=t.state.queries.map(r=>D(T({},r),{expr:i.jh.interpolate(t,r.expr),legendFormat:(null==e?void 0:e.name)?`{{ ${e.name} }}`:i.jh.interpolate(t,r.legendFormat)}));JSON.stringify(r)!==JSON.stringify(this.state.queries)&&this.setState({queries:r})}}),L(this,"updateFieldConfigOverrides",()=>{const{fieldConfig:e,frames:t}=this.getPanelConfigAndDataFrames();if(e&&(null==t?void 0:t.length)){for(const a of t)for(const t of a.fields){const a=Object.keys(t.config).map(e=>({id:e,value:t.config[e]})),i=e.overrides.find(e=>{var r,n;return e.matcher.options===(null!==(n=null!==(r=t.config.displayNameFromDS)&&void 0!==r?r:t.config.displayName)&&void 0!==n?n:t.name)&&"byName"===e.matcher.id});var r,n;if(!i)e.overrides.unshift({matcher:{id:"byName",options:null!==(n=null!==(r=t.config.displayNameFromDS)&&void 0!==r?r:t.config.displayName)&&void 0!==n?n:t.name},properties:a});i&&JSON.stringify(i.properties)!==JSON.stringify(a)&&(i.properties=a)}return e}}),L(this,"getContext",()=>{const e=this.updateFieldConfigOverrides(),{queries:t,dsUid:r,labelName:n,fieldName:a}=this.state,o=i.jh.getTimeRange(this);if(!o||!t||!r)return;const s={origin:"Metrics Drilldown",type:"timeseries",queries:t,timeRange:T({},o.state.value),datasource:{uid:r},url:window.location.href,id:`${JSON.stringify(t)}${n}${a}`,title:n+(a?` > ${a}`:""),logoPath:j,drillDownLabel:a,fieldConfig:e};JSON.stringify(s)!==JSON.stringify(this.state.context)&&this.setState({context:s})}),this.addActivationHandler(this._onActivate.bind(this))}}L(B,"Component",({model:e})=>{const{context:t}=e.useState(),{links:r}=(0,a.usePluginLinks)({extensionPointId:A,context:t,limitPerPlugin:1}),n=r.find(e=>"grafana-investigations-app"===e.pluginId);return n?l().createElement(o.IconButton,{tooltip:n.description,"aria-label":"add panel to exploration",key:n.id,name:null!==(i=n.icon)&&void 0!==i?i:"panel-add",onClick:e=>{n.onClick&&n.onClick(e)}}):null;var i});const I=e=>{var t,r;const n=null!==(r=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==r?r:{},a=Object.keys(n);if(1!==a.length)return;const i=a[0];return{name:i,value:n[i]}};function M(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function R(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){M(i,n,a,o,s,"next",e)}function s(e){M(i,n,a,o,s,"throw",e)}o(void 0)})}}function $(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function H(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){$(e,t,r[t])})}return e}function V(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const F="Add to investigation",z="investigations_divider",U="Investigations";class q extends i.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){var t,r;super(V(H({},e),{addExplorationsLink:null===(r=e.addExplorationsLink)||void 0===r||r})),(t=this).addActivationHandler(()=>{let e;try{const n=i.jh.getAncestor(t,i.Eb),a=i.jh.getData(n).state.data;if(!a)throw new Error("Cannot get link to explore, no panel data found");const o=(0,N.un)(n);var r;(null!==(r=null==o?void 0:o.state.queries)&&void 0!==r?r:[]).forEach(e=>{delete e.legendFormat}),e=(0,i.pN)(a,t,a.timeRange,e=>"expr"in e&&"string"==typeof e.expr&&e.expr.includes("__ignore_usage__")?V(H({},e),{expr:e.expr.replace(/,?__ignore_usage__="",?/,"")}):e)}catch(e){}const n=[{text:"Navigation",type:"group"},{text:"Explore",iconClassName:"compass",onClick:()=>null==e?void 0:e.then(e=>e&&window.open(e,"_blank")),shortcut:"p x"}];t.setState({body:new i.Lw({items:n})});const a=new B({labelName:t.state.labelName,fieldName:t.state.fieldName,frame:t.state.frame});var o;(t._subs.add(null==a?void 0:a.subscribeToState(()=>R(function*(){var e;yield(e=t,R(function*(){const t=e.state.explorationsButton;if(t){var r;const l=yield G(t);var n;const c=null!==(n=null===(r=e.state.body)||void 0===r?void 0:r.state.items)&&void 0!==n?n:[],u=c.find(e=>e.text===F);var a,i,o,s;l&&(u?u&&(null===(a=e.state.body)||void 0===a||a.setItems(c.filter(e=>!1===[z,U,F].includes(e.text)))):(null===(i=e.state.body)||void 0===i||i.addItem({text:z,type:"divider"}),null===(o=e.state.body)||void 0===o||o.addItem({text:U,type:"group"}),null===(s=e.state.body)||void 0===s||s.addItem({text:F,iconClassName:"plus-square",onClick:e=>l.onClick&&l.onClick(e)})))}})())})())),t.setState({explorationsButton:a}),t.state.addExplorationsLink)&&(null===(o=t.state.explorationsButton)||void 0===o||o.activate())})}}$(q,"Component",({model:e})=>{const{body:t}=e.useState();return t?l().createElement(t.Component,{model:t}):l().createElement(l().Fragment,null)});const G=e=>R(function*(){const t=e.state.context;if(a.config.buildInfo.version.startsWith("11."))try{const e=(yield Promise.resolve().then(r.t.bind(r,8531,23))).getPluginLinkExtensions;if(void 0!==e){return e({extensionPointId:A,context:t}).extensions[0]}}catch(e){P.v.error(e,{message:"Error importing getPluginLinkExtensions"})}if("function"==typeof a.getObservablePluginLinks){return(yield(0,k.firstValueFrom)((0,a.getObservablePluginLinks)({extensionPointId:A,context:t})))[0]}})();var K=r(7019);function W(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}const Q=v.mh.getPanelHeightInPixels(v.NC.XL),Y="topview";class J extends i.Bs{onActivate(){return(e=function*(){const{metric:e,topView:t}=this.state,r=(0,C.kj)(this),n=yield r.getMetricMetadata(e),a=(0,x.R)(n);t.setState({children:[new i.vA({key:Y,minHeight:Q,maxHeight:"40%",body:new v.mh({metric:e,description:a,height:v.NC.XL,headerActions:({panelType:t})=>[new E({metric:e,panelType:t})],menu:new q({labelName:e}),queryResolution:v.IK.HIGH})}),new i.vA({ySizing:"content",body:new $t({})})]})},function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){W(i,n,a,o,s,"next",e)}function s(e){W(i,n,a,o,s,"throw",e)}o(void 0)})}).call(this);var e}constructor(e){super({metric:e.metric,topView:new i.G1({direction:"column",$behaviors:[new i.Gg.K2({key:"metricCrosshairSync",sync:w.DashboardCursorSync.Crosshair})],children:[new i.vA({minHeight:Q,maxHeight:"40%",body:new i.dM({reactNode:l().createElement("div",null)})})]}),selectedTab:void 0}),this.addActivationHandler(()=>{this.onActivate()})}}function X(e,t,r){return{container:(0,n.css)({display:"flex",flexDirection:"column",position:"relative",flexGrow:1}),topView:(0,n.css)({}),sticky:(0,n.css)({display:"flex",flexDirection:"row",background:(0,K.T)(e,r),position:"sticky",paddingTop:e.spacing(1),marginTop:`-${e.spacing(1)}`,zIndex:10,top:`calc(var(--app-controls-height, 0px) + ${t}px)`}),nonSticky:(0,n.css)({display:"flex",flexDirection:"row"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(J,"Component",({model:e})=>{const{topView:t,selectedTab:r}=e.useState(),{stickyMainGraph:i}=(0,C.KE)(e).useState(),s=(0,a.useChromeHeaderHeight)(),c=(0,C.kj)(e),u=(0,o.useStyles2)(X,c.state.embedded?0:null!=s?s:0,c);return l().createElement("div",{className:u.container},l().createElement("div",{className:i?(0,n.cx)(u.topView,u.sticky):(0,n.cx)(u.topView,u.nonSticky),"data-testid":"top-view"},l().createElement(t.Component,{model:t})),r&&l().createElement("div",{"data-testid":"tab-content"},l().createElement(r.Component,{model:r})))});var Z=r(1816),ee=r(7238),te=r(5635),re=r(697),ne=r(6920),ae=r(7397),ie=r(9585),oe=r(5568),se=r(5036),le=r(6096),ce=r(28),ue=r(8156),de=r(9966),pe=r(8628),me=r(3831);function he(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ge(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const fe={label:"All metric names",value:"all"};class be extends i.Bs{getUrlState(){return{metricPrefix:this.state.value}}updateFromUrl(e){"string"!=typeof e.metricPrefix?this.setState({value:fe.value}):this.state.value!==e.metricPrefix&&this.setState({value:e.metricPrefix})}onActivate(){this.parseMetricPrefixes()}parseMetricPrefixes(){if(this._variableDependency.hasDependencyInLoadingState())return void this.setState({error:void 0,loading:!0});const e=i.jh.lookupVariable(oe.$,this);if(e.state.error)return void this.setState({error:e.state.error,loading:!1,options:[]});const t=(0,pe.w)((0,me.a)(e)),r=[fe,...t.map(e=>({value:e.value,label:`${e.label} (${e.count})`}))],{value:n}=this.state,a=r.find(e=>e.value===n)?n:fe.value;this.setState({error:null,loading:!1,options:r}),this.selectOption({value:a,label:a})}constructor(e){super(ge(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){he(e,t,r[t])})}return e}({},e),{key:"related-prefix-filter",loading:!0,error:null,options:[fe],value:fe.value})),he(this,"_variableDependency",new i.Sh(this,{variableNames:[oe.$],onVariableUpdateCompleted:()=>this.parseMetricPrefixes()})),he(this,"_urlSync",new i.So(this,{keys:["metricPrefix"]})),he(this,"selectOption",e=>{const t=null===e?fe.value:e.value;this.setState({value:t}),this.publishEvent(new ce.Y({type:"prefixes",filters:t===fe.value?[]:[t]}),!0)}),this.addActivationHandler(this.onActivate.bind(this))}}function ye(e){return{container:n.css`
      display: flex;

      & > div {
        margin: 0;
      }
    `,label:n.css`
      margin-right: 0;
      background-color: ${e.colors.background.primary};
      border: 1px solid ${e.colors.border.medium};
      border-right: 0 none;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    `,tooltipIcon:n.css`
      margin-left: ${e.spacing(.5)};
    `}}function ve(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function we(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}he(be,"Component",({model:e})=>{const t=(0,o.useStyles2)(ye),{loading:r,options:n,value:a,error:i}=e.useState();return l().createElement("div",{className:t.container,"data-testid":"prefix-filter-selector"},l().createElement(o.InlineField,{disabled:r,error:i&&i.toString(),label:l().createElement(o.InlineLabel,{width:"auto",className:t.label},l().createElement("span",null,"View by"),l().createElement(o.Tooltip,{content:"View by the metric prefix. A metric prefix is a single word at the beginning of the metric name, relevant to the domain the metric belongs to.",placement:"top"},l().createElement(o.Icon,{className:t.tooltipIcon,name:"info-circle",size:"sm"})))},l().createElement(o.Combobox,{value:a,onChange:e.selectOption,options:n})))});class Se extends i.P1{constructor(e){super(we(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){ve(e,t,r[t])})}return e}({},e),{key:"related-list-controls",body:new i.G1({direction:"row",width:"100%",maxHeight:"32px",children:[new i.vA({width:"auto",body:new be({})}),new i.vA({body:new ee.I({urlSearchParamName:"gmd-relatedSearchText",targetName:"related metric",countsProvider:new de.s,displayCounts:!0})}),new i.vA({width:"auto",body:new ue.U({})})]})}))}}function Oe(){return{headerWrapper:(0,n.css)({display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center"}}})}}ve(Se,"Component",({model:e})=>{const t=(0,o.useStyles2)(Oe),{body:r}=e.useState();return l().createElement("div",{className:t.headerWrapper,"data-testid":"related-list-controls"},l().createElement(r.Component,{model:r}))});class Ee extends i.Bs{onActivate(){this.subscribeToEvents()}subscribeToEvents(){this.initVariablesFilteringAndSorting()}initVariablesFilteringAndSorting(){const{metric:e}=this.state,t=new Map;this._subs.add(this.subscribeToEvent(re.x,e=>{const{key:r}=e.payload,n=i.jh.findByKey(this,r);t.set(r,{filterEngine:new se.k(n),sortEngine:new le.c(n)})})),this._subs.add(this.subscribeToEvent(ne.e,e=>{t.delete(e.payload.key)}));const r=i.jh.findByKeyAndType(this,"quick-search",ee.I);this._subs.add(this.subscribeToEvent(ae.x,n=>{const{key:a,options:i}=n.payload,{filterEngine:o,sortEngine:s}=t.get(a);o.setInitOptions(i);const l={names:r.state.value?[r.state.value]:[]};o.applyFilters(l,{forceUpdate:!0,notify:!1}),s.sort("related",{metric:e})})),this._subs.add(this.subscribeToEvent(Z.W,r=>{const{searchText:n}=r.payload;for(const[,{filterEngine:r,sortEngine:a}]of t)r.applyFilters({names:n?[n]:[]}),a.sort("related",{metric:e})})),this._subs.add(this.subscribeToEvent(ce.Y,r=>{const{type:n,filters:a}=r.payload;for(const[,{filterEngine:r,sortEngine:i}]of t)r.applyFilters({[n]:a}),i.sort("related",{metric:e})}))}constructor({metric:e}){super({metric:e,$variables:new i.Pj({variables:[new oe.s,new ie.V]}),key:"RelatedMetricsScene",body:new te.Qs({variableName:ie.h}),listControls:new Se({})}),this.addActivationHandler(this.onActivate.bind(this))}}function xe(e){return{body:(0,n.css)({}),list:(0,n.css)({}),listControls:(0,n.css)({margin:e.spacing(1,0,1.5,0)}),variables:(0,n.css)({display:"none"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Ee,"Component",({model:e})=>{const t=(0,o.useStyles2)(xe),{$variables:r,body:n,listControls:a}=e.useState();return l().createElement(l().Fragment,null,l().createElement("div",{className:t.listControls},l().createElement(a.Component,{model:a})),l().createElement("div",{className:t.body},l().createElement("div",{className:t.list,"data-testid":"panels-list"},l().createElement(n.Component,{model:n}))),l().createElement("div",{className:t.variables},null==r?void 0:r.state.variables.map(e=>l().createElement(e.Component,{key:e.state.name,model:e}))))});var ke=r(4137);const Pe=b.COPY_URL_LABEL,je=({trail:e})=>{const[t,r]=(0,s.useState)(Pe);return l().createElement(o.ToolbarButton,{variant:"canvas",icon:"share-alt",tooltip:t,onClick:()=>{if(navigator.clipboard){(0,c.z)("selected_metric_action_clicked",{action:"share_url"});const t=`${a.config.appUrl.endsWith("/")?a.config.appUrl.slice(0,-1):a.config.appUrl}${ke.Gy}/${(0,C.xi)(e)}`;navigator.clipboard.writeText(t),r("Copied!"),setTimeout(()=>{r(Pe)},2e3)}}})};var Ce=r(2425);var _e=r(6503),Ne=r(3241);class Le extends w.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Le,"type","timeseries-data-received");class Te extends w.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Te,"type","force-sync-y-axis");class De extends w.BusEventWithPayload{}function Ae(){return e=>{let t=Number.NEGATIVE_INFINITY,r=Number.POSITIVE_INFINITY;const n=i.jh.getTimeRange(e).subscribeToState(()=>{t=Number.NEGATIVE_INFINITY,r=Number.POSITIVE_INFINITY}),a=e.subscribeToEvent(De,()=>{t=Number.NEGATIVE_INFINITY,r=Number.POSITIVE_INFINITY}),o=e.subscribeToEvent(Te,()=>{let[n,a]=[t,r];const i=Ie(e).filter(e=>{var t;const{fieldConfig:r,$data:i}=e.state;return(!("min"in r.defaults)||!("max"in r.defaults))&&([n,a]=Be((null==i||null===(t=i.state.data)||void 0===t?void 0:t.series)||[],n,a),!0)});n===t&&a===r?Me(e,t,r,i):([t,r]=[n,a],Me(e,n,a))}),s=e.subscribeToEvent(Le,n=>{const[a,i]=Be(n.payload.series||[],t,r);a===i||a===Number.NEGATIVE_INFINITY||i===Number.POSITIVE_INFINITY||a===t&&i===r||([t,r]=[a,i],Me(e,a,i))});return()=>{s.unsubscribe(),o.unsubscribe(),a.unsubscribe(),n.unsubscribe()}}}function Be(e,t,r){let[n,a]=[t,r];for(const t of e||[]){var i;const e=null===(i=t.fields[1])||void 0===i?void 0:i.values.filter(Boolean);e&&(n=Math.max(n,...e),a=Math.min(a,...e))}return[n,a]}function Ie(e){return i.jh.findAllObjects(e,e=>e instanceof i.Eb&&"timeseries"===e.state.pluginId)}function Me(e,t,r,n){for(const a of n||Ie(e))a.clearFieldConfigCache(),a.setState({fieldConfig:(0,Ne.merge)((0,Ne.cloneDeep)(a.state.fieldConfig),{defaults:{min:r,max:t}})})}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(De,"type","reset-sync-y-axis");var Re=r(8587),$e=r(7977);const He="none",Ve="bytes",Fe="seconds",ze="percent",Ue="count",qe={[Ve]:Ve,[Fe]:"s",[ze]:ze,[Ue]:He},Ge=Object.keys(qe),Ke={[Ve]:"Bps",[Fe]:He,[Ue]:"cps",[ze]:ze};function We(e){const t=e.toLowerCase().split("_").slice(-2);for(let e=t.length-1;e>=Math.max(0,t.length-2);e--){const r=t[e];if(Ge.includes(r))return r}return null}var Qe=r(2024),Ye=r(1625);function Je(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class Xe extends i.Bs{constructor(...e){super(...e),Je(this,"onClick",()=>{const{label:e}=this.state;(0,c.z)("breakdown_panel_selected",{label:e});const t=i.jh.lookupVariable(m.yr,this);if(!(0,h.bA)(t))throw new Error("Group by variable not found");t.changeValueTo(e)})}}function Ze(){return e=>{var t;const r=null===(t=e.state.$data)||void 0===t?void 0:t.state.data;(null==r?void 0:r.state)===w.LoadingState.Done&&e.publishEvent(new Le({series:r.series}),!0),e.state.$data.subscribeToState((t,r)=>{var n,a,i;(null===(n=t.data)||void 0===n?void 0:n.state)===w.LoadingState.Done&&(null===(a=t.data)||void 0===a?void 0:a.series)!==(null===(i=r.data)||void 0===i?void 0:i.series)&&e.publishEvent(new Le({series:t.data.series}),!0)})}}Je(Xe,"Component",({model:e})=>l().createElement(o.Button,{variant:"secondary",size:"sm",fill:"outline",onClick:e.onClick},"Select"));const et=()=>e=>e.pipe((0,k.map)(e=>null==e?void 0:e.map((e,t)=>(e.refId=`${e.refId}-${t}`,e))));var tt=r(8023);function rt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const nt="seriesCount",at=(e,t)=>()=>r=>r.pipe((0,k.map)(r=>null==r?void 0:r.slice(e,t).map(e=>{var t;return e.meta=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){rt(e,t,r[t])})}return e}({},e.meta),(t=e.meta).stats||(t.stats=[]),e.meta.stats.unshift({displayName:nt,value:r.length}),e})));const it=`${v.mh.getPanelHeightInPixels(v.NC.M)}px`;class ot extends i.Bs{static buildVizPanel({metric:e,label:t,query:r,unit:n}){const a=new i.Es({$data:new i.dt({datasource:m.GH,maxDataPoints:m.up,queries:[{refId:`${e}-${t}`,expr:r,legendFormat:`{{${t}}}`,fromExploreMetrics:!0}]}),transformations:[at(0,20),(0,tt.b)(t),et]});return i.d0.timeseries().setTitle(t).setUnit(n).setData(a).setBehaviors([Ze()]).setOption("tooltip",{mode:o.TooltipDisplayMode.Multi,sort:Ye.xB.Descending}).setHeaderActions([new Xe({label:t})]).setMenu(new q({labelName:t})).setShowMenuAlways(!0).build()}onActivate(){const{body:e,label:t}=this.state;this._subs.add(e.state.$data.subscribeToState(r=>{var n;if((null===(n=r.data)||void 0===n?void 0:n.state)!==w.LoadingState.Done)return;const{series:a}=r.data;if(!(null==a?void 0:a.length))return;const i=a.every(e=>!e.length)?null:[new Xe({label:t})],o=this.getAllValuesConfig(a);e.setState((0,Ne.merge)({},e.state,{headerActions:i},o))}))}getAllValuesConfig(e){var t,r;const{label:n}=this.state,a=null===(r=e[0].meta)||void 0===r||null===(t=r.stats)||void 0===t?void 0:t.find(e=>e.displayName===nt),i=a?a.value:e.length;return{title:`${n} (${i})`,description:e.length<i?`Showing only ${e.length} series out of ${i} to keep the data easy to read. Click on "Select" on this panel to view a breakdown of all the label's values.`:"",fieldConfig:{overrides:this.getOverrides(e)}}}getOverrides(e){const{startColorIndex:t}=this.state;return e.map((e,r)=>({matcher:{id:w.FieldMatcherID.byFrameRefID,options:e.refId},properties:[{id:"color",value:{mode:"fixed",fixedColor:(0,C.Vy)(t+r)}}]}))}constructor({metric:e,label:t,query:r,unit:n,startColorIndex:a}){super({key:`label-viz-panel-${t}`,metric:e,label:t,query:r,unit:n,startColorIndex:a,body:ot.buildVizPanel({metric:e,label:t,query:r,unit:n})}),this.addActivationHandler(this.onActivate.bind(this))}}function st(){return{container:(0,n.css)({height:it})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(ot,"Component",({model:e})=>{const{body:t}=e.useState(),r=(0,o.useStyles2)(st);return l().createElement("div",{className:r.container},l().createElement(t.Component,{model:t}))});class lt extends i.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=i.jh.findByKeyAndType(this,"layout-switcher",ue.U),t=this.state.body.state.body,r=(e,r)=>{e.layout!==(null==r?void 0:r.layout)&&t.setState({templateColumns:e.layout===ue.p.ROWS?te._O:te.MV})};i.Go.syncStateFromSearchParams(e,new URLSearchParams(window.location.search)),r(e.state),this._subs.add(e.subscribeToState(r))}Controls({model:e}){const{layoutSwitcher:t}=e.useState();return l().createElement(o.Field,{label:"View"},l().createElement(t.Component,{model:t}))}constructor({metric:e}){const t=(0,$e.X)(e)?function(e){const t=We(e);return t&&Ke[t]||"cps"}(e):function(e){const t=We(e);return t&&qe[t.toLowerCase()]||He}(e);super({key:"metric-labels-list",metric:e,layoutSwitcher:new ue.U({}),body:new me.k({variableName:m.yr,initialPageSize:60,pageSizeIncrement:9,body:new i.gF({children:[],isLazy:!0,templateColumns:te.MV,autoRows:it,$behaviors:[new i.Gg.K2({key:"metricCrosshairSync",sync:w.DashboardCursorSync.Crosshair}),Ae()]}),getLayoutLoading:()=>new i.dM({reactNode:l().createElement(o.Spinner,{inline:!0})}),getLayoutEmpty:()=>new i.dM({reactNode:l().createElement(_e._,{title:"",severity:"info"},"No labels found for the current filters and time range.")}),getLayoutError:e=>new i.dM({reactNode:l().createElement(_e._,{severity:"error",title:"Error while loading labels!",error:e})}),getLayoutChild:(r,n)=>{const{queries:a}=(0,Re.H)({metric:e,matchers:[],groupBy:r.value,queryResolution:v.IK.MEDIUM,addIgnoreUsageFilter:!0});return new i.xK({body:new ot({metric:e,label:r.value,query:a[0].expr,unit:t,startColorIndex:n})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function ct(e){return{container:(0,n.css)({width:"100%"}),footer:(0,n.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(lt,"Component",({model:e})=>{const{body:t}=e.useState(),r=(0,o.useStyles2)(ct),n=i.jh.lookupVariable(m.yr,e),{loading:a,error:s}=n.useState(),c=t.useSizes(),u=!a&&!s&&c.total>0&&c.current<c.total;return l().createElement("div",{"data-testid":"labels-list"},l().createElement("div",{className:r.container},l().createElement(t.Component,{model:t})),u&&l().createElement("div",{className:r.footer},l().createElement(Qe.d,{label:"label",batchSizes:c,onClick:()=>{t.increaseBatchSize()}})))});var ut=r(7437);function dt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class pt extends i.Bs{constructor(...e){super(...e),dt(this,"onClick",()=>{const e=i.jh.lookupVariable(m.Ao,this);if(!(0,h.BE)(e))return;const{labelName:t,labelValue:r}=this.state;(0,c.z)("label_filter_changed",{label:t,action:"added",cause:"breakdown"}),(0,C.kj)(this).addFilterWithoutReportingInteraction({key:t,operator:"=",value:r})})}}function mt(e){var t;const r=(null===(t=e.fields[1])||void 0===t?void 0:t.labels)||{},n=Object.keys(r);return 0===n.length?"<unspecified>":r[n[0]]}dt(pt,"Component",({model:e})=>l().createElement(o.Button,{variant:"secondary",size:"sm",fill:"outline",onClick:e.onClick},"Add to filters"));var ht=r(3422),gt=r(619),ft=r(416),bt=r(5731);function yt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class vt extends i.Bs{constructor(e){const t=bt.K.getItem(ft.V.BREAKDOWN_SORTBY);super({key:"breakdown-sort-by",target:e.target,options:vt.DEFAULT_OPTIONS,value:t&&vt.DEFAULT_OPTIONS.find(e=>e.value===t)||vt.DEFAULT_OPTIONS[0]}),yt(this,"onChange",e=>{this.setState({value:e}),bt.K.setItem(ft.V.BREAKDOWN_SORTBY,e.value)})}}function wt(e){return{sortByTooltip:(0,n.css)({display:"flex",gap:e.spacing(1)})}}function St(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}yt(vt,"DEFAULT_OPTIONS",[{value:"outliers",label:"Outlying series",description:"Prioritizes values that show distinct behavior from others within the same label"},{value:"alphabetical",label:"Name [A-Z]",description:"Alphabetical order"},{value:"alphabetical-reversed",label:"Name [Z-A]",description:"Reversed alphabetical order"}]),yt(vt,"Component",({model:e})=>{const t=(0,o.useStyles2)(wt),{value:r,options:n}=e.useState();return l().createElement(o.Field,{"data-testid":"sort-by-select",htmlFor:"sort-by-criteria",label:l().createElement("div",{className:t.sortByTooltip},"Sort by",l().createElement(o.IconButton,{name:"info-circle",size:"sm",variant:"secondary",tooltip:"Sorts values using standard or smart time series calculations."}))},l().createElement(o.Combobox,{id:"sort-by-criteria",placeholder:"Choose criteria",width:20,options:n,value:r,onChange:e.onChange,isClearable:!1}))});class Ot extends i.Bs{performRepeat(e){var t,r,n,a;if(e.state===w.LoadingState.Loading)return void this.setState({loadingLayout:null===(t=(r=this.state).getLayoutLoading)||void 0===t?void 0:t.call(r),errorLayout:void 0,emptyLayout:void 0,currentBatchSize:0});if(e.state===w.LoadingState.Error)return void this.setState({errorLayout:null===(n=(a=this.state).getLayoutError)||void 0===n?void 0:n.call(a,e),loadingLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const i=this.filterAndSort(e.series);var o,s;if(!i.length)return void this.setState({emptyLayout:null===(o=(s=this.state).getLayoutEmpty)||void 0===o?void 0:o.call(s),errorLayout:void 0,loadingLayout:void 0,currentBatchSize:0,counts:{current:0,total:e.series.length}});this.setState({loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,currentBatchSize:this.state.initialPageSize,counts:{current:i.length,total:e.series.length}});const l=i.slice(0,this.state.initialPageSize).map((t,r)=>this.state.getLayoutChild(e,t,r)).filter(Boolean);this.state.body.setState({children:l})}initFilterAndSort(){this.searchText=i.jh.findByKeyAndType(this,"quick-search",ee.I).state.value,this.sortBy=i.jh.findByKeyAndType(this,"breakdown-sort-by",vt).state.value.value}filterAndSort(e){let t=[];if(this.searchText){const r=this.searchText.split(",").map(e=>e.trim()).filter(Boolean).map(e=>{try{return new RegExp(e)}catch(e){return null}}).filter(Boolean);for(let n=0;n<e.length;n+=1){const a=e[n];r.some(e=>e.test(mt(a)))&&t.push(a)}}else t=e;return this.sortBy&&(t=(0,gt.sortSeries)(t,this.sortBy)),t}filter(e){this.searchText=e;const{data:t}=i.jh.getData(this).state;t&&(this.publishEvent(new De({}),!0),this.performRepeat(t))}sort(e){this.sortBy=e;const{data:t}=i.jh.getData(this).state;t&&(this.publishEvent(new De({}),!0),this.performRepeat(t))}increaseBatchSize(){const{data:e}=i.jh.getData(this).state;if(!e)return;const t=this.state.currentBatchSize+this.state.pageSizeIncrement,r=this.filterAndSort(e.series).slice(this.state.currentBatchSize,t).map((t,r)=>this.state.getLayoutChild(e,t,r)).filter(Boolean);this.state.body.setState({children:[...this.state.body.state.children,...r]}),this.setState({currentBatchSize:t}),this.publishEvent(new Te({}),!0)}useSizes(){const{currentBatchSize:e,pageSizeIncrement:t}=this.useState(),{data:r}=i.jh.getData(this).state,n=r?this.filterAndSort(r.series).length:0,a=n-e;return{increment:a<t?a:t,current:e,total:n}}getCounts(){const{data:e}=i.jh.getData(this).state;return{current:0,total:e?e.series.length:0}}constructor({$behaviors:e,body:t,getLayoutChild:r,getLayoutLoading:n,getLayoutError:a,getLayoutEmpty:o,initialPageSize:s,pageSizeIncrement:l,$data:c}){super({key:"breakdown-by-frame-repeater",$behaviors:e,body:t,getLayoutChild:r,getLayoutLoading:n,getLayoutError:a,getLayoutEmpty:o,currentBatchSize:0,initialPageSize:s||120,pageSizeIncrement:l||9,loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,counts:{current:0,total:0},$data:c}),St(this,"searchText",""),St(this,"sortBy",void 0),this.addActivationHandler(()=>{const e=i.jh.getData(this);if(!e)throw new Error("No data provider found!");this.initFilterAndSort(),this._subs.add(e.subscribeToState(e=>{e.data&&this.performRepeat(e.data)})),e.state.data&&this.performRepeat(e.state.data)})}}St(Ot,"Component",({model:e})=>{const{body:t,loadingLayout:r,errorLayout:n,emptyLayout:a}=e.useState();return r?l().createElement(r.Component,{model:r}):n?l().createElement(n.Component,{model:n}):a?l().createElement(a.Component,{model:a}):l().createElement(t.Component,{model:t})});class Et extends ht.I{constructor(){super({key:"LabelValuesCountsProvider"}),this.addActivationHandler(()=>{i.jh.findByKeyAndType(this,"breakdown-by-frame-repeater",Ot).subscribeToState((e,t)=>{e.counts!==t.counts&&this.setState({counts:e.counts})})})}}const xt=`${v.mh.getPanelHeightInPixels(v.NC.M)}px`;class kt extends i.Bs{static buildVizPanel({labelValue:e,data:t,unit:r,fixedColor:n,headerActions:a,menu:o}){return i.d0.timeseries().setTitle(e).setBehaviors([Ze()]).setData(t).setUnit(r).setColor({mode:"fixed",fixedColor:n}).setCustomFieldConfig("fillOpacity",9).setHeaderActions(a).setOption("legend",{showLegend:!1}).setShowMenuAlways(!0).setMenu(o).build()}constructor({labelValue:e,data:t,unit:r,fixedColor:n,headerActions:a,menu:i}){super({key:`label-value-viz-panel-${e}`,labelValue:e,unit:r,fixedColor:n,body:kt.buildVizPanel({labelValue:e,data:t,unit:r,fixedColor:n,headerActions:a,menu:i})})}}function Pt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){Pt(e,t,r[t])})}return e}function Ct(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(kt,"Component",({model:e})=>{const{body:t}=e.useState();return l().createElement(t.Component,{model:t})});class _t extends i.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToQuickSearchChange(){i.Go.syncStateFromSearchParams(this.state.quickSearch,new URLSearchParams(window.location.search)),this._subs.add(this.subscribeToEvent(Z.W,e=>{const t=i.jh.findDescendents(this,Ot)[0];t&&t.filter(e.payload.searchText)}))}subscribeToSortByChange(){const{sortBySelector:e}=this.state;this._subs.add(e.subscribeToState((e,t)=>{if(e.value.value!==(null==t?void 0:t.value.value)){const t=i.jh.findDescendents(this,Ot)[0];t&&t.sort(e.value.value)}}))}subscribeToLayoutChange(){const{layoutSwitcher:e}=this.state;i.Go.syncStateFromSearchParams(e,new URLSearchParams(window.location.search));const t=(e,t)=>{e.layout!==(null==t?void 0:t.layout)&&this.updateBody(e.layout)};t(e.state),this._subs.add(e.subscribeToState(t))}updateBody(e){if(e===ue.p.SINGLE)return void this.setState({body:this.buildSinglePanel()});const t=i.jh.findDescendents(this,Ot)[0],r=t||this.buildByFrameRepeater();r.state.body.setState({templateColumns:e===ue.p.ROWS?te._O:te.MV}),this.setState({body:r}),t||(this.subscribeToQuickSearchChange(),this.subscribeToSortByChange())}buildSinglePanel(){const{metric:e,label:t}=this.state;return new v.mh({metric:e,panelType:v.Gx.TIMESERIES,height:v.NC.XL,headerActions:()=>[],groupBy:t})}buildByFrameRepeater(){const{metric:e,label:t}=this.state,r=(0,Re.H)({metric:e,matchers:[],groupBy:t,queryResolution:v.IK.MEDIUM,addIgnoreUsageFilter:!0}),n=r.isRateQuery?(0,ut.MM)(e):(0,ut.l_)(e);return new Ot({$data:new i.Es({$data:new i.dt({datasource:m.GH,maxDataPoints:r.maxDataPoints,queries:r.queries}),transformations:[(0,tt.b)(t)]}),$behaviors:[Ae(),new i.Gg.K2({key:"metricCrosshairSync",sync:w.DashboardCursorSync.Crosshair})],body:new i.gF({children:[],isLazy:!0,templateColumns:te.MV,autoRows:xt}),getLayoutLoading:()=>new i.dM({reactNode:l().createElement(o.Spinner,{inline:!0})}),getLayoutEmpty:()=>new i.dM({reactNode:l().createElement(_e._,{title:"",severity:"info"},"No label values found for the current filters and time range.")}),getLayoutError:e=>new i.dM({reactNode:l().createElement(_e._,{severity:"error",title:"Error while loading metrics!",error:e.errors[0]})}),getLayoutChild:(e,r,a)=>{if(r.length<2)return null;const o=mt(r),s=!o.startsWith("<unspecified")?[new pt({labelName:t,labelValue:o})]:[],l=new kt({labelValue:o,data:new i.Zv({data:Ct(jt({},e),{series:[r]})}),unit:n,fixedColor:(0,C.Vy)(a),headerActions:s,menu:new q({labelName:o})});return new i.xK({body:l})}})}Controls({model:e}){const t=(0,o.useStyles2)(Nt),{body:r,quickSearch:n,layoutSwitcher:a,sortBySelector:i}=e.useState();return l().createElement(l().Fragment,null,r instanceof Ot&&l().createElement(l().Fragment,null,l().createElement(o.Field,{className:t.quickSearchField,label:"Search"},l().createElement(n.Component,{model:n})),l().createElement(i.Component,{model:i})),l().createElement(o.Field,{label:"View"},l().createElement(a.Component,{model:a})))}constructor({metric:e,label:t}){super({key:"metric-label-values-list",metric:e,label:t,layoutSwitcher:new ue.U({urlSearchParamName:"breakdownLayout",options:[{label:"Single",value:ue.p.SINGLE},{label:"Grid",value:ue.p.GRID},{label:"Rows",value:ue.p.ROWS}]}),quickSearch:new ee.I({urlSearchParamName:"breakdownSearchText",targetName:"label value",countsProvider:new Et,displayCounts:!0}),sortBySelector:new vt({target:"labels"}),body:void 0}),this.addActivationHandler(this.onActivate.bind(this))}}function Nt(e){return{singlePanelContainer:(0,n.css)({width:"100%",height:"300px"}),listContainer:(0,n.css)({width:"100%"}),listFooter:(0,n.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}}),quickSearchField:(0,n.css)({flexGrow:1})}}Pt(_t,"Component",({model:e})=>{const{body:t}=e.useState();return l().createElement(l().Fragment,null,t instanceof v.mh&&l().createElement(_t.SingleMetricPanelComponent,{model:e}),t instanceof Ot&&l().createElement(_t.ByFrameRepeaterComponent,{model:e}))}),Pt(_t,"SingleMetricPanelComponent",({model:e})=>{const t=(0,o.useStyles2)(Nt),{body:r}=e.useState();return l().createElement("div",{"data-testid":"single-metric-panel"},l().createElement("div",{className:t.singlePanelContainer},r instanceof v.mh&&l().createElement(r.Component,{model:r})))}),Pt(_t,"ByFrameRepeaterComponent",({model:e})=>{const t=(0,o.useStyles2)(Nt),{body:r}=e.useState(),n=i.jh.getData(e),{state:a,errors:s}=n.useState().data||{},c=r,u=c.useSizes(),d=a!==w.LoadingState.Loading&&!(null==s?void 0:s.length)&&u.total>0&&u.current<u.total;return l().createElement("div",{"data-testid":"label-values-list"},l().createElement("div",{className:t.listContainer},r instanceof Ot&&l().createElement(r.Component,{model:r})),d&&l().createElement("div",{className:t.listFooter},l().createElement(Qe.d,{label:"label value",batchSizes:u,onClick:()=>{c.increaseBatchSize()}})))});class Lt extends i.Bs{onActivate(){const e=this.getVariable();e.subscribeToState((t,r)=>{t.value!==r.value&&this.updateBody(e)}),a.config.featureToggles.enableScopesInMetricsExplore&&this._subs.add(this.subscribeToEvent(m.H0,()=>{this.updateBody(e)})),this.updateBody(e)}getVariable(){const e=i.jh.lookupVariable(m.yr,this);if(!(0,h.bA)(e))throw new Error("Group by variable not found");return e}updateBody(e){const{metric:t}=this.state;this.setState({body:e.hasAllValue()?new lt({metric:t}):new _t({metric:t,label:e.state.value})})}constructor({metric:e}){super({metric:e,body:void 0}),this.addActivationHandler(this.onActivate.bind(this))}}function Tt(e){return{container:(0,n.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column",paddingTop:e.spacing(1)}),controls:(0,n.css)({flexGrow:0,display:"flex",gap:e.spacing(2),height:"70px",justifyContent:"space-between",alignItems:"end"}),searchField:(0,n.css)({flexGrow:1})}}function Dt(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function At(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){Dt(i,n,a,o,s,"next",e)}function s(e){Dt(i,n,a,o,s,"throw",e)}o(void 0)})}}function Bt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Lt,"Component",({model:e})=>{const t=(0,o.useStyles2)(Tt),{body:r}=e.useState(),n=e.getVariable();return l().createElement("div",{className:t.container},l().createElement("div",{className:t.controls},l().createElement(o.Field,{label:"By label"},l().createElement(n.Component,{model:n})),r instanceof lt&&l().createElement(r.Controls,{model:r}),r instanceof _t&&l().createElement(r.Controls,{model:r})),l().createElement("div",{"data-testid":"panels-list"},r instanceof lt&&l().createElement(r.Component,{model:r}),r instanceof _t&&l().createElement(r.Component,{model:r})))});const It="breakdown",Mt="logs",Rt=[{displayName:"Breakdown",value:It,getScene:e=>new Lt({metric:e.state.metric})},{displayName:"Related metrics",value:"related",getScene:e=>new Ee({metric:e.state.metric}),description:"Relevant metrics based on current label filters"},{displayName:"Related logs",value:Mt,getScene:e=>e.createRelatedLogsScene(),description:"Relevant logs based on current label filters and time range"}];class $t extends i.Bs{constructor(...e){var t;super(...e),Bt(t=this,"getLinkToExplore",()=>At(function*(){const e=i.jh.findByKey(t,Y),r=i.jh.findDescendents(e,v.mh)[0],n=i.jh.findDescendents(r,i.dt)[0].state.data;if(!n)throw new Error("Cannot get link to explore, no panel data found");const a=i.jh.getAncestor(t,Br);return(0,i.pN)(n,a,n.timeRange)})()),Bt(t,"openExploreLink",()=>At(function*(){(0,c.z)("selected_metric_action_clicked",{action:"open_in_explore"}),t.getLinkToExplore().then(e=>{window.open(e,"_blank")})})())}}function Ht(e){return{actions:(0,n.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,top:16,zIndex:2}}),customTabsBar:(0,n.css)({paddingBottom:e.spacing(1)})}}Bt($t,"Component",({model:e})=>{const t=i.jh.getAncestor(e,Br),r=(0,o.useStyles2)(Ht),n=(0,C.kj)(e),[a,u]=function(e){const t=()=>(0,Ce._r)().getBookmarkIndex(e),r=t(),[n,a]=(0,s.useState)(r);(0,s.useEffect)(()=>{const t=e.subscribeToEvent(i.bZ,()=>{a((0,Ce._r)().getBookmarkIndex(e))});return()=>t.unsubscribe()},[e]),r!==n&&a(r);const o=null!=n;return[o,()=>{if((0,c.z)("bookmark_changed",{action:o?"toggled_off":"toggled_on"}),o){let e=t();for(;null!=e;)(0,Ce._r)().removeBookmark(e),e=t()}else(0,Ce._r)().addBookmark(e);a(t())}]}(n),{actionView:d}=t.useState();return l().createElement(o.Box,{paddingY:1,"data-testid":"action-bar"},l().createElement("div",{className:r.actions},l().createElement(o.Stack,{gap:1},n.state.embedded?l().createElement(o.LinkButton,{href:(0,y.Rk)((0,C.xi)(n)),variant:"secondary",icon:"arrow-right",tooltip:"Open in Metrics Drilldown",onClick:()=>(0,c.z)("selected_metric_action_clicked",{action:"open_from_embedded"})},"Metrics Drilldown"):l().createElement(o.ToolbarButton,{variant:"canvas",tooltip:b.SELECT_NEW_METRIC_TOOLTIP,onClick:()=>{(0,c.z)("selected_metric_action_clicked",{action:"unselect"}),n.publishEvent(new m.OO(void 0))}},"Select new metric"),l().createElement(o.ToolbarButton,{variant:"canvas",icon:"compass",tooltip:b.OPEN_EXPLORE_LABEL,onClick:e.openExploreLink}),l().createElement(je,{trail:n}),l().createElement(o.ToolbarButton,{variant:"canvas",icon:a?l().createElement(o.Icon,{name:"favorite",type:"mono",size:"lg"}):l().createElement(o.Icon,{name:"star",type:"default",size:"lg"}),tooltip:b.BOOKMARK_LABEL,onClick:u}))),l().createElement(o.TabsBar,{className:r.customTabsBar},Rt.map((e,r)=>{const n=e.displayName,a=e.value===Mt?t.state.relatedLogsCount:void 0,i=d===e.value,s=l().createElement(o.Tab,{key:r,label:n,counter:a,active:i,onChangeTab:()=>{i||((0,c.z)("metric_action_view_changed",{view:e.value,related_logs_count:t.relatedLogsOrchestrator.checkConditionsMetForRelatedLogs()?a:void 0}),t.setActionView(e.value))}});return e.description?l().createElement(o.Tooltip,{key:r,content:e.description,placement:"top",theme:"info"},s):s})))});var Vt=r(1932);const Ft=`${m.Rp}{${m.ui}}`,zt=`rate(${Ft}[$__rate_interval])`,Ut=`{"${m.Rp}", ${m.ui}}`,qt=`rate(${Ut}[$__rate_interval])`;function Gt({isRateQuery:e=!1,groupings:t=[],isUtf8Metric:r=!1}){let n;return n=r?e?qt:Ut:e?zt:Ft,t.length>0?`sum by(${t.join(", ")}) (${n})`:`${n}`}var Kt=r(5938);function Wt({title:e,unit:t}){return i.d0.timeseries().setTitle(e).setUnit(t).setOption("legend",{showLegend:!1}).setOption("tooltip",{mode:Ye.$N.Multi,sort:Ye.xB.Descending}).setCustomFieldConfig("fillOpacity",9)}function Qt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){Qt(e,t,r[t])})}return e}function Jt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function Xt({description:e,mainQueryExpr:t,breakdownQueryExpr:r,unit:n}){const a={title:m.Rp,unit:n},i={refId:"A",expr:t,legendFormat:e,fromExploreMetrics:!0},o=Jt(Yt({},a),{title:e,queries:[i],variant:"main",vizBuilder:()=>Wt(Yt({},o))}),s=Jt(Yt({},a),{queries:[Jt(Yt({},i),{legendFormat:e})],vizBuilder:()=>Wt(s),variant:"preview"}),l=Jt(Yt({},a),{queries:[{refId:"A",expr:r,legendFormat:`{{${m.aZ}}}`,fromExploreMetrics:!0}],vizBuilder:()=>Wt(l),variant:"breakdown"});return{preview:s,main:o,breakdown:l,variants:[]}}const Zt=new Set(["count","total"]),er={count:"sum",total:"sum"},tr={avg:"average",sum:"overall"};function rr(e){const{metricParts:t,suffix:r,isUtf8Metric:n}=e,a="total"===r?t.at(-2):r,i=Zt.has(r),o=er[r]||"avg",s=i?(0,ut.MM)(a):(0,ut.l_)(a),l=Gt({isRateQuery:i,isUtf8Metric:n}),c=`${u=o,tr[u]||u}${i?" per-second rate":""}`;var u;return Xt({description:`${m.Rp} (${c})`,mainQueryExpr:`${o}(${l})`,breakdownQueryExpr:`${o}(${l})by(${m.aZ})`,unit:s})}function nr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ar(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){nr(e,t,r[t])})}return e}function ir(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function or(e){const{unit:t,nativeHistogram:r}=e,n={title:m.Rp,unit:t},a=ir(ar({},n),{variant:"p50",queries:[sr(e,50)],vizBuilder:()=>Wt(a)}),o=ir(ar({},n),{variant:"p50",queries:[sr(e,50,[m.aZ])],vizBuilder:()=>Wt(o)}),s=ir(ar({},n),{variant:"percentiles",queries:[99,90,50].map(t=>sr(e,t)),vizBuilder:()=>function({title:e,unit:t}){return i.d0.timeseries().setTitle(e).setUnit(t).setCustomFieldConfig("fillOpacity",9).setOption("tooltip",{mode:Ye.$N.Multi,sort:Ye.xB.Descending}).setOption("legend",{showLegend:!1})}(s)}),l=ir(ar({},n),{variant:"heatmap",queries:[{refId:"Heatmap",expr:Gt({isRateQuery:!0,isUtf8Metric:e.isUtf8Metric,groupings:r?[]:["le"]}),fromExploreMetrics:!0,format:"heatmap"}],vizBuilder:()=>function({title:e,unit:t}){return i.d0.heatmap().setTitle(e).setUnit(t).setOption("calculate",!1).setOption("color",{mode:Kt.P7.Scheme,exponent:.5,scheme:"Spectral",steps:32,reverse:!1})}(l)});return{preview:l,main:l,variants:[s,l],breakdown:o}}function sr(e,t,r=[]){const n=t/100;let a=`${t}th Percentile`;r[0]&&(a=`{{${r[0]}}}`);return{refId:`Percentile${t}`,expr:`histogram_quantile(${n}, ${Gt({isRateQuery:!0,isUtf8Metric:e.isUtf8Metric,groupings:e.nativeHistogram?[...r]:["le",...r]})})`,legendFormat:a,fromExploreMetrics:!0}}function lr(e,t){return`${e.replace(m.Rp,`${t}_sum`)}/${e.replace(m.Rp,`${t}_count`)}`}function cr(e,t){const r=!(0,Vt.Rq)(e),n=e.split("_"),a=n.at(-1);if(null==a)throw new Error(`This function does not support a metric suffix of "${a}"`);const i=n.at(-2),o={metricParts:n,isUtf8Metric:r,suffix:a,unitSuffix:i,unit:(0,ut.l_)(i),nativeHistogram:t};return"sum"===a?function(e){const{metricParts:t,isUtf8Metric:r,unit:n}=e,a=t.slice(0,-1).join("_"),i=`${a} (average)`,o=Gt({isRateQuery:!0,isUtf8Metric:r});return Xt({description:i,mainQueryExpr:lr(`sum(${o})`,a),breakdownQueryExpr:lr(`sum(${o})by(${m.aZ})`,a),unit:n})}(o):"bucket"===a||t?or(o):rr(o)}var ur=r(7476);function dr(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function pr(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){dr(i,n,a,o,s,"next",e)}function s(e){dr(i,n,a,o,s,"throw",e)}o(void 0)})}}const mr={job:"service_name",instance:"service_instance_id"};function hr(e){return e in mr?mr[e]:e}const gr=e=>{let t=!1;return{name:"labelsCrossReference",checkConditionsMetForRelatedLogs:()=>t,getDataSources:()=>pr(function*(){var r;const n=(0,C.kj)(e),o=i.jh.lookupVariable(m.Ao,n);if(!(0,h.BE)(o)||!o.state.filters.length)return t=!1,[];t=!0;const s=o.state.filters.map(({key:e,operator:t,value:r})=>({key:e,operator:t,value:r})),l=null===(r=e.state.$timeRange)||void 0===r?void 0:r.state.value,c=yield(0,ur.tS)().getHealthyDataSources("loki"),u=yield Promise.all(c.map(({uid:e,name:t})=>pr(function*(){const r=yield function(e,t,r){return pr(function*(){var n;const i=yield(0,a.getDataSourceSrv)().get(e),o=yield null===(n=i.getTagKeys)||void 0===n?void 0:n.call(i,{timeRange:r,filters:t.map(({key:e,operator:t,value:r})=>({key:hr(e),operator:t,value:r}))});if(!Array.isArray(o))return!1;const s=new Set(o.map(e=>e.text));return!!t.map(e=>hr(e.key)).every(e=>s.has(e))&&(yield Promise.all(t.map(e=>pr(function*(){var n;const a=hr(e.key),o=yield null===(n=i.getTagValues)||void 0===n?void 0:n.call(i,{key:a,timeRange:r,filters:t});return!!Array.isArray(o)&&o.some(t=>t.text===e.value)})()))).every(Boolean)})()}(e,s,l);return r?{uid:e,name:t}:null})()));return u.filter(e=>null!==e)})(),getLokiQueryExpr(){const t=(0,C.kj)(e),r=i.jh.lookupVariable(m.Ao,t);if(!(0,h.BE)(r)||!r.state.filters.length)return"";return`{${r.state.filters.map(e=>`${hr(e.key)}${e.operator}"${e.value}"`).join(",")}}`}}};var fr=r(6365);function br(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function yr(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){br(i,n,a,o,s,"next",e)}function s(e){br(i,n,a,o,s,"throw",e)}o(void 0)})}}function vr(e,t,r){if(!t||!r[t])return"";const n=r[t].find(t=>t.name===e);if(!n)return"";return function(e){if(function(e){if(e.trim().length<=2)return!1;let t=!1;const r=fr.K3.parse(e);return r.iterate({enter:({type:e})=>{if(e.id===fr.Yw)return t=!0,!1}}),!t}(e))return e;const t=Or(e,fr.MD);if(!t)return"";const r=e.substring(t.from,t.to),n=Or(e,fr.AL),a=n?e.substring(n.from,n.to):"";return`${r} ${a}`.trim()}(n.query)}function wr(){return yr(function*(){const e=yield(0,ur.tS)().getHealthyDataSources("loki"),t={};return yield Promise.all(e.map(e=>yr(function*(){try{const n=function(e,t){if(0===e.length)return[];const r=new Map;return e.forEach(e=>{e.rules.filter(e=>"recording"===e.type).forEach(({type:e,name:n,query:a})=>{if(r.has(n)){const e=r.get(n);e&&(e.hasMultipleOccurrences=!0,r.set(n,e))}else r.set(n,{type:e,name:n,query:a,datasource:{name:t.name,uid:t.uid},hasMultipleOccurrences:!1})})}),Array.from(r.values())}(yield(r=e,yr(function*(){const e={url:`api/prometheus/${r.uid}/api/v1/rules`,showErrorAlert:!1,showSuccessAlert:!1},t=yield(0,k.lastValueFrom)((0,a.getBackendSrv)().fetch(e));return t.ok?t.data.data.groups:(P.v.warn(`Failed to fetch recording rules from Loki data source: ${r.name}`),[])})()),e);t[e.uid]=n}catch(e){P.v.warn(e)}var r})())),t})()}const Sr=()=>{let e={},t=!1;return{name:"lokiRecordingRules",checkConditionsMetForRelatedLogs:()=>t,getDataSources:r=>yr(function*(){e=yield wr();const n=function(e,t){const r=[];return Object.values(t).forEach(t=>{t.filter(t=>t.name===e).forEach(e=>{r.push(e.datasource)})}),r}(r,e);return t=Boolean(n.length),n})(),getLokiQueryExpr:(t,r)=>vr(t,r,e)}};function Or(e,t){let r;return fr.K3.parse(e).iterate({enter:e=>{if(e.type.id===t)return r=e.node,!1}}),r}function Er(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function xr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class kr{get lokiDataSources(){return this._internalState.lokiDataSources}set lokiDataSources(e){const t=this._internalState.lokiDataSources.map(e=>e.uid).join(","),r=e.map(e=>e.uid).join(",");t&&t===r||(this._internalState.lokiDataSources=e,this._changeHandlers.lokiDataSources.forEach(e=>e(this._internalState.lokiDataSources)))}set relatedLogsCount(e){this._internalState.relatedLogsCount=e,this._changeHandlers.relatedLogsCount.forEach(e=>e(this._internalState.relatedLogsCount))}addLokiDataSourcesChangeHandler(e){this._changeHandlers.lokiDataSources.push(e)}addRelatedLogsCountChangeHandler(e){this._changeHandlers.relatedLogsCount.push(e)}handleFiltersChange(){this.lokiDataSources&&(this.lokiDataSources=[],this.relatedLogsCount=0,this.findAndCheckAllDatasources())}findAndCheckAllDatasources(){return(e=function*(){const e=yield this._dataSourceFetcher.getHealthyDataSources("loki");e.length>0?this.checkLogsInDataSources(e):(this.lokiDataSources=[],this.relatedLogsCount=0)},function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){Er(i,n,a,o,s,"next",e)}function s(e){Er(i,n,a,o,s,"throw",e)}o(void 0)})}).call(this);var e}getLokiQueries(e,t=100){const{metric:r}=this._metricScene.state,n=this._logsConnectors.reduce((t,n,a)=>{const i=n.getLokiQueryExpr(r,e);var o;i&&(t[null!==(o=n.name)&&void 0!==o?o:`connector-${a}`]=i);return t},{});return Object.keys(n).map(e=>({refId:`RelatedLogs-${e}`,expr:n[e],maxLines:t,supportingQueryType:_.id}))}checkLogsInDataSources(e){const t=[];let r=0,n=0;if(0===e.length)return this.lokiDataSources=[],void(this.relatedLogsCount=0);e.forEach(a=>{const o=new i.dt({datasource:{uid:a.uid},queries:[],key:`related_logs_check_${a.uid}`});o.setState({queries:this.getLokiQueries(a.uid)}),o.subscribeToState(i=>{var o;if((null===(o=i.data)||void 0===o?void 0:o.state)===w.LoadingState.Done){var s;if(n++,null===(s=i.data)||void 0===s?void 0:s.series){const e=this.countLogsLines(i);e>0&&(t.push(a),r+=e)}n===e.length&&(this.lokiDataSources=t,this.relatedLogsCount=r)}}),o.activate()})}checkConditionsMetForRelatedLogs(){return this._logsConnectors.some(e=>e.checkConditionsMetForRelatedLogs())}countLogsLines(e){var t,r;return null!==(r=null===(t=e.data)||void 0===t?void 0:t.series.reduce((e,t)=>e+t.length,0))&&void 0!==r?r:0}constructor(e){xr(this,"_logsConnectors",void 0),xr(this,"_metricScene",void 0),xr(this,"_dataSourceFetcher",(0,ur.tS)()),xr(this,"_changeHandlers",{lokiDataSources:[],relatedLogsCount:[]}),xr(this,"_internalState",{relatedLogsCount:0,lokiDataSources:[]}),this._metricScene=e,this._logsConnectors=[Sr(),gr(e)]}}function Pr(){const e=(0,o.useStyles2)(jr);return l().createElement(o.Stack,{direction:"column",gap:2},l().createElement(o.Alert,{title:"No related logs found",severity:"info"},"We couldn't find any logs related to the current metric with your selected filters."),l().createElement(o.Text,null,"To find related logs, try the following:",l().createElement("ul",{className:e.list},l().createElement("li",null,"Adjust your label filters to include labels that exist in both the current metric and your logs"),l().createElement("li",null,"Select a metric created by a"," ",l().createElement(o.TextLink,{external:!0,href:"https://grafana.com/docs/loki/latest/alert/#recording-rules"},"Loki Recording Rule")),l().createElement("li",null,"Broaden the time range to include more data"))),l().createElement(o.Text,{variant:"bodySmall",color:"secondary"},"Note: Related logs is an experimental feature."))}function jr(e){return{list:(0,n.css)({paddingLeft:e.spacing(2),marginTop:e.spacing(1)})}}function Cr({context:e}){const t=(0,s.useMemo)(()=>e,[e]),{links:r,isLoading:n}=(0,a.usePluginLinks)({extensionPointId:"grafana-metricsdrilldown-app/open-in-logs-drilldown/v1",limitPerPlugin:1,context:t}),i=(0,s.useMemo)(()=>r.find(({pluginId:e})=>"grafana-lokiexplore-app"===e),[r]);if(n)return l().createElement(o.LinkButton,{variant:"secondary",size:"sm",disabled:!0},"Loading...");const u=void 0!==i;return l().createElement(o.LinkButton,{href:u?`${a.config.appSubUrl}${i.path}`:`${a.config.appSubUrl}/a/grafana-lokiexplore-app`,target:"_blank",tooltip:u?"Use the Logs Drilldown app to explore these logs":"Navigate to the Logs Drilldown app",variant:"secondary",size:"sm",onClick:()=>(0,c.z)("related_logs_action_clicked",{action:"open_logs_drilldown"})},u?"Open in Logs Drilldown":"Open Logs Drilldown")}function _r(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function Nr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Lr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const Tr="related_logs/logs_panel_container";class Dr extends i.Bs{_onActivate(){return(e=function*(){this.state.orchestrator.addLokiDataSourcesChangeHandler(()=>this.setupLogsPanel()),this.state.orchestrator.lokiDataSources.length?this.setupLogsPanel():(this.setState({loading:!0}),yield this.state.orchestrator.findAndCheckAllDatasources(),this.setState({loading:!1}))},function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){_r(i,n,a,o,s,"next",e)}function s(e){_r(i,n,a,o,s,"throw",e)}o(void 0)})}).call(this);var e}showNoLogsFound(){i.jh.findByKeyAndType(this,Tr,i.xK).setState({body:new i.dM({component:Pr})}),this.setState({controls:void 0}),this.state.orchestrator.relatedLogsCount=0}_buildQueryRunner(){this._queryRunner=new i.dt({datasource:{uid:m.Kf},queries:[],key:"related_logs/logs_query"}),this._constructLogsDrilldownLinkContext(this._queryRunner.state),this._subs.add(this._queryRunner.subscribeToState(e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)!==w.LoadingState.Done)return;0===this.state.orchestrator.countLogsLines(e)&&this.showNoLogsFound(),this._constructLogsDrilldownLinkContext(e)}))}setupLogsPanel(){if(this._buildQueryRunner(),!this.state.orchestrator.lokiDataSources.length)return void this.showNoLogsFound();i.jh.findByKeyAndType(this,Tr,i.xK).setState({body:i.d0.logs().setTitle("Logs").setData(this._queryRunner).build()});const e=new i.yP({name:m.Az,label:"Logs data source",query:this.state.orchestrator.lokiDataSources.map(e=>`${e.name} : ${e.uid}`).join(",")});this.setState({$variables:new i.Pj({variables:[e]}),controls:[new i.K8({layout:"vertical"})]}),this._subs.add(e.subscribeToState((e,t)=>{e.value!==t.value&&(0,c.z)("related_logs_action_clicked",{action:"logs_data_source_changed"})})),this.updateLokiQuery()}_constructLogsDrilldownLinkContext(e){var t,r;const n=null!==(r=null===(t=i.jh.lookupVariable(m.Az,this))||void 0===t?void 0:t.getValue())&&void 0!==r?r:"",a=e.queries,o=[];n&&a.length&&a.forEach(e=>{o.push(Lr(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){Nr(e,t,r[t])})}return e}({},e),{datasource:{uid:n,type:"loki"}}))}),this.setState({logsDrilldownLinkContext:{targets:o,timeRange:i.jh.getTimeRange(this).state}})}updateLokiQuery(){if(!this._queryRunner)return;const e=i.jh.lookupVariable(m.Az,this);let t;if((0,h.UG)(e)&&(t=e.getValue()),!t)return;const r=this.state.orchestrator.getLokiQueries(t);0!==r.length?this._queryRunner.setState({queries:r}):this.showNoLogsFound()}constructor(e){super({loading:!1,controls:[],body:new i.gF({templateColumns:"1fr",autoRows:"minmax(300px, 1fr)",children:[new i.xK({key:Tr,body:void 0})]}),orchestrator:e.orchestrator,logsDrilldownLinkContext:{targets:[]}}),Nr(this,"_queryRunner",void 0),Nr(this,"_variableDependency",new i.Sh(this,{variableNames:[m.Az,m.Ao],onReferencedVariableValueChanged:e=>{e.state.name===m.Ao?this.state.orchestrator.handleFiltersChange():e.state.name===m.Az&&this.updateLokiQuery()}})),this.addActivationHandler(()=>{this._onActivate()})}}function Ar(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Nr(Dr,"Component",({model:e})=>{const{controls:t,body:r,logsDrilldownLinkContext:n,loading:a}=e.useState();return a?l().createElement(o.Spinner,null):l().createElement(o.Stack,{gap:1,direction:"column",grow:1},l().createElement(o.Stack,{gap:1,direction:"row",justifyContent:"space-between",alignItems:"start"},l().createElement(o.Stack,{gap:1},null==t?void 0:t.map(e=>l().createElement(e.Component,{key:e.state.key,model:e}))),l().createElement(Cr,{context:n})),l().createElement(r.Component,{model:r}))});class Br extends i.Bs{_onActivate(){void 0===this.state.actionView&&this.setActionView(It),this.relatedLogsOrchestrator.findAndCheckAllDatasources(),this.relatedLogsOrchestrator.addRelatedLogsCountChangeHandler(e=>{this.setState({relatedLogsCount:e})}),a.config.featureToggles.enableScopesInMetricsExplore&&this._subs.add(this.subscribeToEvent(m.H0,e=>{var t;null===(t=this.state.body.state.selectedTab)||void 0===t||t.publishEvent(e)}))}getUrlState(){return{actionView:this.state.actionView}}updateFromUrl(e){if("string"==typeof e.actionView){if(this.state.actionView!==e.actionView){const t=Rt.find(t=>t.value===e.actionView);t&&this.setActionView(t.value)}}else null===e.actionView&&this.setActionView(null)}setActionView(e){const{body:t}=this.state,r=e?Rt.find(t=>t.value===e):null;r&&r.value!==this.state.actionView?(t.setState({selectedTab:r.getScene(this)}),this.setState({actionView:r.value})):(t.setState({selectedTab:void 0}),this.setState({actionView:void 0}))}createRelatedLogsScene(){return new Dr({orchestrator:this.relatedLogsOrchestrator})}constructor(e){var t;const r=null!==(t=e.autoQuery)&&void 0!==t?t:cr(e.metric,e.nativeHistogram);var n,a,o,s;super(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){Ar(e,t,r[t])})}return e}({$variables:null!==(n=e.$variables)&&void 0!==n?n:(s=e.metric,new i.Pj({variables:[...(0,m.ym)(s),new g]})),body:null!==(a=e.body)&&void 0!==a?a:new J({metric:e.metric}),autoQuery:r,queryDef:null!==(o=e.queryDef)&&void 0!==o?o:r.main},e)),Ar(this,"relatedLogsOrchestrator",new kr(this)),Ar(this,"_urlSync",new i.So(this,{keys:["actionView"]})),Ar(this,"_variableDependency",new i.Sh(this,{variableNames:[m.Ao],onReferencedVariableValueChanged:()=>{this.relatedLogsOrchestrator.handleFiltersChange()}})),this.addActivationHandler(this._onActivate.bind(this))}}Ar(Br,"Component",({model:e})=>{const{body:t}=e.useState(),r=(0,o.useStyles2)(Ir);return l().createElement("div",{className:r.container,"data-testid":"metric-scene"},l().createElement(t.Component,{model:t}))});const Ir=()=>({container:(0,n.css)({position:"relative",height:"100%",width:"100%",display:"flex",flexDirection:"column"})})},2745:(e,t,r)=>{r.d(t,{J:()=>s,w:()=>l});var n=r(8531),a=r(5959),i=r(4796),o=r(5521);const s=(0,a.createContext)({trail:(0,i.ef)(),goToUrlForTrail:()=>{}});function l(){const[e,t]=(0,a.useState)((0,i.ef)()),r=e=>{n.locationService.push((0,i.xi)(e)),t(e)};return(0,a.useEffect)(()=>{const e=o.C.subscribe(e=>{r(e)});return()=>e()},[]),{trail:e,goToUrlForTrail:r}}},2993:(e,t,r)=>{r.d(t,{E:()=>c});var n=r(6089),a=r(2007),i=r(5959),o=r.n(i),s=r(1159),l=r(6503);function c({error:e}){const t=(0,a.useStyles2)(u),r=(0,s.useNavigate)(),{pathname:n,search:c}=(0,s.useLocation)(),d=(0,i.useCallback)(()=>{const e=new URLSearchParams(c),t=new URLSearchParams;["from","to","timezone"].filter(t=>e.has(t)).forEach(r=>t.set(r,e.get(r))),r({pathname:n,search:t.toString()}),window.location.reload()},[r,n,c]);return o().createElement("div",{className:t.container},o().createElement(l._,{severity:"error",title:"Fatal error!",message:o().createElement(o().Fragment,null,"Please"," ",o().createElement(a.TextLink,{href:"#",onClick:d},"try reloading the page")," ","or, if the problem persists, contact your organization admin. Sorry for the inconvenience."),error:e,errorContext:{handheldBy:"React error boundary"}}))}function u(e){return{container:(0,n.css)({margin:e.spacing(2)})}}},3081:(e,t,r)=>{r.d(t,{tw:()=>M,Rg:()=>R,iK:()=>I,yG:()=>H});var n=r(6089),a=r(7781),i=r(7985),o=r(2007),s=r(5959),l=r.n(s),c=r(1932),u=r(8162);function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const p=new Set(["avg","min","max"]);function m(e,t="avg"){return e?"sum":t}function h({metric:e,filters:t,isRateQuery:r,groupings:n,ignoreUsage:a=!1,nonRateQueryFunction:i="avg",filterExtremeValues:o=!1}){const s=!(0,c.Rq)(e);let l=new u.r4({metric:s?"":e,values:{},defaultOperator:u.md.equal,defaultSelectors:[...s?[{label:(0,c.Nc)(e),operator:u.md.equal,value:"__REMOVE__"}]:[],...a?[{label:"__ignore_usage__",operator:u.md.equal,value:""}]:[],...t.filter(e=>"__name__"!==e.key).map(({key:e,value:t,operator:r})=>({label:(0,c.Nc)(e),operator:r,value:t}))]}).toString();if(s&&(l=l.replace('="__REMOVE__"',"")),o){l=u.GH.and({left:l,right:`${l} > -Inf`})}return r&&(l=u.GH.rate({expr:l,interval:"$__rate_interval"})),u.GH[m(r,i)](function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){d(e,t,r[t])})}return e}({expr:l},(null==n?void 0:n.length)?{by:n}:{}))}var g=r(7437),f=r(2127),b=r(8534),y=r(5938);var v=r(6145),w=r(1625);const S=[{type:v.dM.ValueToText,options:{0:{color:"red",text:"down"},1:{color:"green",text:"up"}}}];var O=r(9851),E=r(7818),x=r(3347),k=r(2445);function P(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){P(e,t,r[t])})}return e}function C(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function _(e){const[t]=i.jh.findDescendents(e,i.dt),r=null==t?void 0:t.subscribeToState(n=>{var o,s;if((null===(o=n.data)||void 0===o?void 0:o.state)===a.LoadingState.Done&&(null===(s=n.data)||void 0===s?void 0:s.series)){const{series:a}=n.data;if(0===a.length||!function(e){return e.every(e=>function(e){const t=e.fields.find(e=>"Value"===e.name);if(!t||(r=t,!("entities"in r)||!Array.isArray(null===(n=r.entities)||void 0===n?void 0:n.NaN)))return!1;var r,n;return t.entities.NaN.length===e.length}(e))}(a))return;(0,x.z)("extreme_value_filter_behavior_triggered",{expression:i.jh.interpolate(t,t.state.queries[0].expr)});const o=function(e,t,r){const n=e.state.queries;if(!n||0===n.length)return{success:!1,issue:"No queries found in query runner"};const a=function(e,t){let r=e;(e.includes(f.Rp)||e.includes(f.ui))&&(r=i.jh.interpolate(t,e));try{const e=O.K3.parse(r);let t="";const n=[];let a,i,o=!1;return e.iterate({enter:e=>{var s,l;if("FunctionCall"===e.name){let t="";for(let n=e.node.firstChild;n;n=n.nextSibling)if("Identifier"===n.type.name){t=r.slice(n.from,n.to);break}"rate"===t?o=!0:(l=t,p.has(l)&&(i=t,o=!1))}t||"Identifier"!==e.name||"VectorSelector"!==(null===(s=e.node.parent)||void 0===s?void 0:s.type.name)||(t=r.slice(e.from,e.to));const c=(0,E.Wf)(e,r);c&&n.push({key:c.label,operator:c.op,value:c.value}),"GroupingLabels"===e.name&&i&&(a=function(e,t){const r=[];for(let n=e.node.firstChild;n;n=n.nextSibling)if("LabelName"===n.type.name){const e=t.slice(n.from,n.to);e&&r.push(e)}return r}(e,r))}}),t?{success:!0,queryParts:{metric:t,filters:n,isRateQuery:o,groupings:a,nonRateQueryFunction:i}}:{success:!1,issue:`Could not parse the metric name from the query: ${r}`}}catch(e){return{success:!1,issue:`Unexpected error during query parsing to handle extreme values: ${e instanceof Error?e.message:String(e)}`}}}(n[0].expr,e);if(!a.success)return{success:!1,issue:a.issue};const o=h(C(j({},a.queryParts),{filterExtremeValues:!0}));r&&r.unsubscribe();const s=e.clone({queries:[C(j({},n[0]),{expr:o})]});return t.setState({$data:s,titleItems:l().createElement(N,{level:"info",message:"Panel data was re-fetched with a more complex query to handle extremely small values in the series"})}),s.runQueries(),{success:!0}}(t,e,r);o.success||(e.setState({titleItems:l().createElement(N,{level:"warning",message:"Extreme values detected, but could not re-run the query with extreme value filtering"})}),k.v.warn("ExtremeValueFilterBehavior: Failed to remove extreme values:",o.issue))}});return()=>{r&&r.unsubscribe()}}function N({message:e,level:t}){const r=(0,o.useStyles2)(L,t);return l().createElement("div",{className:r.extremeValuedisclaimer},l().createElement(o.Tooltip,{content:e},l().createElement("span",{className:r.warningMessage},l().createElement(o.Icon,{name:"warning"===t?"exclamation-triangle":"info-circle","aria-hidden":"true"}))))}const L=(e,t)=>({extremeValuedisclaimer:(0,n.css)({label:"extreme-value-disclaimer",display:"flex",alignItems:"center",gap:e.spacing(1)}),warningMessage:(0,n.css)({display:"flex",alignItems:"center",gap:e.spacing(.5),color:"warning"===t?e.colors.warning.main:e.colors.info.main,fontSize:e.typography.bodySmall.fontSize})});var T=r(519);function D(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){D(e,t,r[t])})}return e}function B(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const I="260px",M="220px",R="160px",$=new Set(["count","total","sum","bucket"]);class H extends i.Bs{onActivate(){const{body:e,prometheusFunction:t}=this.state;this._subs.add(e.state.$data.subscribeToState(r=>{var n;if((null===(n=r.data)||void 0===n?void 0:n.state)!==a.LoadingState.Done)return;const{series:i}=r.data;(null==i?void 0:i.length)&&e.setState({fieldConfig:{defaults:e.state.fieldConfig.defaults,overrides:[{matcher:{id:a.FieldMatcherID.byFrameRefID,options:i[0].refId},properties:[{id:"displayName",value:t}]}]}})}))}static buildVizPanel({metricName:e,title:t,highlight:r,color:n,hideLegend:a,prometheusFunction:o,matchers:s,headerActions:l,isNativeHistogram:c=!1}){const u=r?`${t} (current)`:t,d=(0,g.l_)(e),p=e.endsWith("_bucket")||c;return"up"===e||e.endsWith("_up")?function({panelTitle:e,headerActions:t,queryRunner:r}){return r.setState({maxDataPoints:100}),i.d0.statushistory().setTitle(e).setHeaderActions(t.map(e=>e.clone())).setData(r).setColor({mode:"palette-classic"}).setMappings(S).setOption("legend",{showLegend:!0}).setOption("perPage",0).setOption("showValue",w.yL.Never).setDisplayName("status")}({panelTitle:u,headerActions:l,queryRunner:H.buildQueryRunner({metricName:e,matchers:s,prometheusFunction:"min",isHistogram:p})}).setUnit(d).build():p?function({panelTitle:e,queryRunner:t,headerActions:r,hideLegend:n}){return i.d0.heatmap().setTitle(e).setData(t).setOption("calculate",!1).setOption("color",{mode:y.P7.Scheme,exponent:.5,scheme:"Spectral",steps:32,reverse:!1}).setHeaderActions(r.map(e=>e.clone())).setOption("legend",{show:!n})}({panelTitle:u,headerActions:l,hideLegend:a,queryRunner:H.buildQueryRunner({metricName:e,matchers:s,prometheusFunction:"rate",isHistogram:p,queryOptions:{format:"heatmap"}})}).setUnit(d).build():function({panelTitle:e,queryRunner:t,color:r,headerActions:n,hideLegend:a}){return i.d0.timeseries().setTitle(e).setData(t).setColor({mode:"fixed",fixedColor:r}).setCustomFieldConfig("fillOpacity",9).setCustomFieldConfig("pointSize",1).setHeaderActions(n.map(e=>e.clone())).setOption("legend",{showLegend:!a}).setBehaviors([_])}({panelTitle:u,headerActions:l,color:n,hideLegend:a,queryRunner:H.buildQueryRunner({metricName:e,matchers:s,prometheusFunction:o,isHistogram:p})}).setUnit(d).build()}static buildQueryRunner({metricName:e,matchers:t,isHistogram:r,prometheusFunction:n,queryOptions:a={}}){const o=t.map(T.q),{isRateQuery:s,groupings:l}=H.determineQueryProperties(e,r),c=h(A({metric:e,filters:o,isRateQuery:s,ignoreUsage:!0,groupings:l},n?{nonRateQueryFunction:n}:{}));return new i.dt({datasource:f.GH,maxDataPoints:H.MAX_DATA_POINTS,queries:[B(A({},a),{refId:e,expr:c,fromExploreMetrics:!0})]})}static determineQueryProperties(e,t){const r=e.split("_").at(-1);let n;return t&&(n=["le"]),{isRateQuery:$.has(r||""),groupings:n}}constructor(e){const{isRateQuery:t}=H.determineQueryProperties(e.metricName,e.isNativeHistogram);var r;const n=B(A({},e),{prometheusFunction:null!==(r=e.prometheusFunction)&&void 0!==r?r:m(t),isNativeHistogram:e.isNativeHistogram,matchers:e.matchers||[],title:e.title||e.metricName,height:e.height||M,hideLegend:Boolean(e.hideLegend),highlight:Boolean(e.highlight),headerActions:[...e.headerActions||[new b.B({metricName:e.metricName})]]});super(B(A({key:`metric-viz-panel-${n.metricName}`},n),{body:H.buildVizPanel(A({},n))})),this.addActivationHandler(this.onActivate.bind(this))}}D(H,"MAX_DATA_POINTS",250),D(H,"Component",({model:e})=>{const{body:t,height:r,highlight:a,isNativeHistogram:i}=e.useState(),s=(0,o.useStyles2)(F,r);return l().createElement("div",{className:(0,n.cx)(s.container,a&&s.highlight,i&&s.nativeHistogram)},t&&l().createElement(t.Component,{model:t}))});const V=e=>(0,n.css)({'[class$="-panel-header"]':{position:"relative",paddingLeft:"120px"},'[class$="-panel-title"]::before':{content:'"Native Histogram"',fontSize:"12px",color:"rgb(158, 193, 247)",position:"absolute",left:"8px",top:"7px",display:"inline-flex",alignItems:"center",justifyContent:"center",width:"116px",height:"22px",padding:0,border:`1px solid ${e.colors.info.text}`,borderRadius:e.shape.radius.pill,background:e.colors.info.transparent,cursor:"auto"}});function F(e,t){return{container:(0,n.css)({height:t}),highlight:(0,n.css)({border:`2px solid ${e.colors.primary.main}`}),nativeHistogram:V(e)}}},3347:(e,t,r)=>{r.d(t,{h:()=>d,z:()=>c});var n=r(8531),a=r(4137),i=r(5176);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const l="grafana_explore_metrics_";function c(e,t){(0,n.reportInteraction)(`${l}${e}`,s(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){o(e,t,r[t])})}return e}({},t),{meta:{appRelease:n.config.apps[a.s_].version,appVersion:i.t}}))}function u(e,t){c("label_filter_changed",{label:e,action:t,cause:"adhoc_filter"})}function d(e,t){e.length===t.length?function(e,t){for(const r of t)for(const t of e)r.key===t.key&&r.value!==t.value&&u(r.key,"changed")}(e,t):e.length<t.length?function(e,t){for(const r of t)e.some(e=>e.key===r.key)||u(r.key,"removed")}(e,t):function(e,t){for(const r of e)!t.some(e=>e.key===r.key)&&u(r.key,"added")}(e,t)}},3422:(e,t,r)=>{r.d(t,{I:()=>o});var n=r(7985);function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}class o extends n.Bs{useCounts(){return this.useState().counts}constructor(e){super(i(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){a(e,t,r[t])})}return e}({},e),{counts:{current:0,total:0}}))}}},3616:(e,t,r)=>{r.d(t,{HA:()=>c,jx:()=>l});var n=r(7781),a=r(8531),i=r(2445);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function l(e,t){const r=t.reduce((e,t,r)=>s(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){o(e,t,r[t])})}return e}({},e),{[`info${r+1}`]:t}),{handheldBy:"displayError"});i.v.error(e,r),(0,a.getAppEvents)().publish({type:n.AppEvents.alertError.name,payload:t})}function c(e){i.v.warn(e),(0,a.getAppEvents)().publish({type:n.AppEvents.alertWarning.name,payload:e})}},3831:(e,t,r)=>{r.d(t,{a:()=>c,k:()=>l});var n=r(7985),a=r(5959),i=r.n(a),o=r(2445);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class l extends n.Bs{performRepeat(){var e,t;if(this._variableDependency.hasDependencyInLoadingState())return void this.setState({loadingLayout:null===(e=(t=this.state).getLayoutLoading)||void 0===e?void 0:e.call(t),errorLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const r=n.jh.lookupVariable(this.state.variableName,this);if(!(r instanceof n.n8)){const e=new Error("SceneByVariableRepeater: variable is not a MultiValueVariable!");return void o.v.error(e)}var a,i;if(r.state.error)return void this.setState({errorLayout:null===(a=(i=this.state).getLayoutError)||void 0===a?void 0:a.call(i,r.state.error),loadingLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const s=c(r);var l,u;if(!s.length)return void this.setState({emptyLayout:null===(l=(u=this.state).getLayoutEmpty)||void 0===l?void 0:l.call(u),errorLayout:void 0,loadingLayout:void 0,currentBatchSize:0});this.setState({loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,currentBatchSize:this.state.initialPageSize});const d=s.slice(0,this.state.initialPageSize).map((e,t)=>this.state.getLayoutChild(e,t,s)).filter(Boolean);this.state.body.setState({children:d})}increaseBatchSize(){const e=c(n.jh.lookupVariable(this.state.variableName,this)),t=this.state.currentBatchSize+this.state.pageSizeIncrement,r=e.slice(this.state.currentBatchSize,t).map((t,r)=>this.state.getLayoutChild(t,this.state.currentBatchSize+r,e)).filter(Boolean);this.state.body.setState({children:[...this.state.body.state.children,...r]}),this.setState({currentBatchSize:t})}useSizes(){const{currentBatchSize:e,pageSizeIncrement:t}=this.useState(),r=n.jh.lookupVariable(this.state.variableName,this).state.options.length,a=r-e;return{increment:a<t?a:t,current:e,total:r}}constructor({variableName:e,body:t,getLayoutChild:r,getLayoutLoading:a,getLayoutError:i,getLayoutEmpty:o,initialPageSize:l,pageSizeIncrement:c}){super({variableName:e,body:t,getLayoutChild:r,getLayoutLoading:a,getLayoutError:i,getLayoutEmpty:o,currentBatchSize:0,initialPageSize:l||6,pageSizeIncrement:c||9,loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0}),s(this,"_variableDependency",new n.Sh(this,{variableNames:[this.state.variableName],onVariableUpdateCompleted:()=>this.performRepeat()})),this.addActivationHandler(()=>this.performRepeat())}}function c(e){const{value:t,text:r,options:n}=e.state;return e.hasAllValue()?n:Array.isArray(t)&&Array.isArray(r)?t.map((e,t)=>({value:e,label:r[t]})):[{value:t,label:r}]}s(l,"Component",({model:e})=>{const{body:t,loadingLayout:r,errorLayout:n,emptyLayout:a}=e.useState();return r?i().createElement(r.Component,{model:r}):n?i().createElement(n.Component,{model:n}):a?i().createElement(a.Component,{model:a}):i().createElement(t.Component,{model:t})})},4003:(e,t,r)=>{r.d(t,{c:()=>v});var n=r(7985),a=r(5959),i=r.n(a),o=r(2445),s=r(384),l=r(1252),c=r(8499),u=r(9585),d=r(3081),p=r(6089),m=r(2007);function h({usageType:e,usageCount:t,singularUsageType:r,pluralUsageType:n,icon:a,dashboardItems:o}){const s=(0,m.useStyles2)(g);return i().createElement("div",{className:s.usageContainer,"data-testid":"usage-data-panel"},"dashboard-usage"===e?i().createElement(i().Fragment,null,i().createElement(m.Dropdown,{placement:"right-start",overlay:i().createElement(m.Menu,{style:{maxWidth:"240px",maxHeight:"245px",overflowY:"auto"}},o.map(e=>i().createElement(m.Menu.Item,{key:e.id,label:"",url:e.url,target:"_blank",className:s.menuItem,component:()=>i().createElement(m.Tooltip,{content:`Used ${e.count} ${1===e.count?"time":"times"} in ${e.label}`,placement:"right"},i().createElement("div",{className:s.menuItemContent},i().createElement(m.Icon,{name:"external-link-alt"})," ",e.label," (",e.count,")"))})))},i().createElement(m.Button,{variant:"secondary",size:"sm",tooltip:`Metric used ${t} ${1===t?"time":"times"} in dashboard queries. Click to view the dashboards.`,className:(0,p.cx)(s.usageItem,s.clickableUsageItem)},i().createElement("span",{"data-testid":e},i().createElement(m.Icon,{name:a,style:{marginRight:"4px"}})," ",t)))):i().createElement(m.Tooltip,{content:`Metric is used in ${t} ${1===t?r:n}`,placement:"top"},i().createElement("span",{className:s.usageItem,"data-testid":e},i().createElement(m.Icon,{name:a})," ",t)))}function g(e){return{usageContainer:(0,p.css)({display:"flex",flexDirection:"row",justifyContent:"flex-start",gap:"17px",padding:"8px 12px",border:`1px solid ${e.colors.border.weak}`,borderTopWidth:0,backgroundColor:e.colors.background.primary,alignItems:"center"}),usageItem:(0,p.css)({display:"flex",alignItems:"center",gap:"4px",color:e.colors.text.secondary,opacity:"65%"}),clickableUsageItem:(0,p.css)({backgroundColor:"transparent",border:"none"}),menuItem:(0,p.css)({color:e.colors.text.primary,textDecoration:"none","&:hover":{color:e.colors.text.link}}),menuItemContent:(0,p.css)({overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:e.colors.text.primary,"&:hover":{color:e.colors.text.link}})}}function f(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function b(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}class v extends n.Bs{_onActivate(){let e;try{e=n.jh.getAncestor(this,c.m)}catch(e){return}if(!e.state.enginesMap.get(u.h))return;const t=n.jh.findByKeyAndType(this,"metrics-sorter",l.fD),r=n.jh.getVariables(t).getByName(l.NJ);(0,s.UG)(r)&&(this.updateSortBy(t,r.getValue()),this._subs.add(r.subscribeToState(({value:e})=>{this.updateSortBy(t,e)})))}updateSortBy(e,t){return(r=function*(){if(this.setState({sortBy:t}),this.updateLayout(t),"default"===t)return;const r=yield e.getUsageDetailsForMetric(this.state.metric,t);switch(t){case"dashboard-usage":if("dashboard-usage"!==r.usageType)return;const{dashboards:e}=r;this.setState({usageCount:r.count,singularUsageType:"dashboard panel query",pluralUsageType:"dashboard panel queries",icon:"apps",dashboardItems:Object.entries(e).map(([e,t])=>({id:t.uid,label:e,count:t.count,url:t.url})).sort((e,t)=>t.count-e.count)});break;case"alerting-usage":this.setState({usageCount:r.count,singularUsageType:"alert rule",pluralUsageType:"alert rules",icon:"bell"})}},function(){var e=this,t=arguments;return new Promise(function(n,a){var i=r.apply(e,t);function o(e){f(i,n,a,o,s,"next",e)}function s(e){f(i,n,a,o,s,"throw",e)}o(void 0)})}).call(this);var r}updateLayout(e){const t=n.jh.getAncestor(this,n.gF),r=null==t?void 0:t.state.autoRows,a="default"===e?d.tw:d.iK;r!==a&&t.setState({autoRows:a})}constructor(e){super(y(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){b(e,t,r[t])})}return e}({},e),{sortBy:"default",usageCount:0,singularUsageType:"",pluralUsageType:"",icon:"",dashboardItems:[]})),this.addActivationHandler(this._onActivate.bind(this))}}b(v,"Component",({model:e})=>{const{vizPanelInGridItem:t,sortBy:r,usageCount:n,singularUsageType:a,pluralUsageType:s,icon:l,dashboardItems:c}=e.useState();if(t)return i().createElement("div",{"data-testid":"with-usage-data-preview-panel"},i().createElement(t.Component,{model:t}),"default"!==r&&i().createElement(h,{usageType:r,usageCount:n,singularUsageType:a,pluralUsageType:s,icon:l,dashboardItems:c}));o.v.log("no viz panel")})},4796:(e,t,r)=>{r.d(t,{y:()=>S,UX:()=>P,Vy:()=>E,aO:()=>O,kj:()=>b,KE:()=>y,xi:()=>w,FG:()=>k,ef:()=>v});var n=r(7781),a=r(8531),i=r(7985),o=(r(1269),r(2445)),s=r(4137),l=r(1955),c=(r(2634),r(2127));r(2425);function u(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(){return new p}class p extends i.Bs{getSelectedScopes(){return this.selectedScopes}getSelectedScopesNames(){return this.selectedScopes.map(({scope:e})=>e.metadata.name)}setSelectedScopes(e){this.selectedScopes=e,this.notifySubscribers()}onScopesChange(e){return this.onScopesChangeCallbacks.push(e),()=>{this.onScopesChangeCallbacks=this.onScopesChangeCallbacks.filter(t=>t!==e)}}notifySubscribers(){for(const e of this.onScopesChangeCallbacks)e(this.selectedScopes)}get value(){return[]}constructor(){super({}),u(this,"selectedScopes",[]),u(this,"onScopesChangeCallbacks",[])}}var m=r(384);function h(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function g(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){h(i,n,a,o,s,"next",e)}function s(e){h(i,n,a,o,s,"throw",e)}o(void 0)})}}function f(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function b(e){return i.jh.getAncestor(e,l.b)}function y(e){return i.jh.getAncestor(e,l.b).state.settings}function v(e){var t,r;return new l.b(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){f(e,t,r[t])})}return e}({initialDS:null==e?void 0:e.initialDS,$timeRange:null!==(t=null==e?void 0:e.$timeRange)&&void 0!==t?t:new i.JZ({from:"now-1h",to:"now"}),embedded:null!==(r=null==e?void 0:e.embedded)&&void 0!==r&&r},e))}function w(e){const t=i.Go.getUrlState(e);return n.urlUtil.renderUrl(s.bw.Drilldown,t)}function S(e){return window.location.pathname.includes(e)}function O(e){return e?e===c.td?"Logs":e:"All metrics"}function E(e){const t=a.config.theme2.visualization;return t.getColorByName(t.palette[e%8])}const x=1e4;function k(e,t,r){(0,m.BE)(t)&&t.setState({getTagKeysProvider:()=>g(function*(){var n;const a={filters:t.state.filters,scopes:null===(n=d())||void 0===n?void 0:n.value,queries:e.getQueries()};return a.queries.length>20&&(a.queries=[]),{replace:!0,values:(yield r.getTagKeys(a)).slice(0,x)}})(),getTagValuesProvider:(n,a)=>g(function*(){var n;const i=t.state.filters.filter(e=>e.key!==a.key),o={key:a.key,filters:i,scopes:null===(n=d())||void 0===n?void 0:n.value,queries:e.getQueries()};o.queries.length>20&&(o.queries=[]);return{replace:!0,values:(yield r.getTagValues(o)).slice(0,x)}})()})}function P(e,t,r){const n=i.jh.findObject(e,t);return n instanceof r?n:(null!==n&&o.v.warn(`invalid return type: ${r.toString()}`),null)}},4964:(e,t,r)=>{r.d(t,{_:()=>n});const n=new Intl.Collator("en",{sensitivity:"base"}).compare},5036:(e,t,r)=>{r.d(t,{k:()=>o});var n=r(7985),a=r(3241);function i(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class o{setInitOptions(e){this.initOptions=(0,a.cloneDeep)(e)}getFilters(){return this.filters}static getFilteredOptions(e,t){let r=e;return t.categories.length>0&&(r=o.applyCategoryFilters(r,t.categories)),t.prefixes.length>0&&(r=o.applyPrefixFilters(r,t.prefixes)),t.suffixes.length>0&&(r=o.applySuffixFilters(r,t.suffixes)),t.names.length>0&&(r=o.applyNameFilters(r,t.names)),r}applyFilters(e=this.filters,t={forceUpdate:!1,notify:!0}){const r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){i(e,t,r[t])})}return e}({},this.filters,e);if(!t.forceUpdate&&(0,a.isEqual)(this.filters,r))return;if(!(r.categories.length||r.prefixes.length||r.suffixes.length||r.names.length))return this.filters=r,this.variable.setState({options:this.initOptions}),void(t.notify&&this.notifyUpdate());this.filters=r;const n=o.getFilteredOptions(this.initOptions,this.filters);this.variable.setState({options:n}),t.notify&&this.notifyUpdate()}static applyCategoryFilters(e,t){let r=[];for(const n of t){const t=o.buildRegex(n,"i");r=r.concat(e.filter(e=>t.test(e.value)))}return r}static applyPrefixFilters(e,t){const r=t.map(e=>e.includes("|")?`${e.split("|").map(e=>`^${e}([^a-z0-9]|$)`).join("|")}`:`^${e}([^a-z0-9]|$)`).join("|"),n=o.buildRegex(`(${r})`);return e.filter(e=>n.test(e.value))}static applySuffixFilters(e,t){const r=t.map(e=>e.includes("|")?`${e.split("|").map(e=>`(^|[^a-z0-9])${e}$`).join("|")}`:`(^|[^a-z0-9])${e}$`).join("|"),n=o.buildRegex(`(${r})`);return e.filter(e=>n.test(e.value))}static applyNameFilters(e,t){const[r]=t,n=r.split(",").map(e=>e.trim()).filter(Boolean).map(e=>{try{return new RegExp(e)}catch(e){return null}}).filter(Boolean);return e.filter(e=>n.some(t=>t.test(e.value)))}static buildRegex(e,t){try{return new RegExp(e,t)}catch(e){return new RegExp(".*")}}notifyUpdate(){this.variable.publishEvent(new n.oh(this.variable),!0)}constructor(e){i(this,"variable",void 0),i(this,"initOptions",[]),i(this,"filters",{categories:[],prefixes:[],suffixes:[],names:[]}),this.variable=e}}},5490:(e,t,r)=>{r.d(t,{q:()=>p,R:()=>m});var n=r(8531),a=r(7985),i=r(3616),o=r(2127);const s={"11.6.x":function(e){const t=e.languageProvider;return"function"==typeof t.fetchLabelValues&&1===t.fetchLabelValues.length},"12.0.0":function(e){const t=e.languageProvider;return"function"==typeof t.fetchLabelValues&&t.fetchLabelValues.length>1},"12.1.0-plus":function(e){return"function"==typeof e.languageProvider.queryLabelKeys}};var l=r(7476);function c(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function u(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){c(i,n,a,o,s,"next",e)}function s(e){c(i,n,a,o,s,"throw",e)}o(void 0)})}}function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class p{reset(){this._datasource=void 0,this._metricsMetadata=void 0,this._classicHistograms={},this._nativeHistograms=[]}getDatasource(){return u(function*(){if(this._datasource)return this._datasource;const e=yield(0,n.getDataSourceSrv)().get(o.gR,{__sceneObject:{value:this._trail}});return(0,l.aQ)(e)&&(this._datasource=e),this._datasource}).call(this)}_ensureMetricsMetadata(){return u(function*(){this._metricsMetadata||(yield this._getMetricsMetadata())}).call(this)}_getMetricsMetadata(){return u(function*(){const e=yield this.getDatasource();if(!e)return;const t=function(e){if(s["12.1.0-plus"](e))return e.languageProvider.queryMetricsMetadata;if(s["12.0.0"](e)||s["11.6.x"](e))return()=>Promise.resolve(e.languageProvider.metricsMetadata);throw new Error("Unsupported language provider version")}(e),r=function(e,t){if(s["12.1.0-plus"](e))return e.languageProvider.retrieveMetricsMetadata;if(s["12.0.0"](e)||s["11.6.x"](e))return()=>{var r,n,a;return(null!==(a=null===(r=(n=e.languageProvider).loadMetricsMetadata)||void 0===r?void 0:r.call(n))&&void 0!==a?a:Promise.resolve()).then(()=>t())};throw new Error("Unsupported language provider version")}(e,t);let n=yield t();n||(n=yield r()),this._metricsMetadata=n}).call(this)}getMetadataForMetric(e){return u(function*(){var t;if(e)return yield this._ensureMetricsMetadata(),null===(t=this._metricsMetadata)||void 0===t?void 0:t[e]}).call(this)}listNativeHistograms(){return this._nativeHistograms}initializeHistograms(){return u(function*(){const e=yield this.getDatasource();if(e&&0===Object.keys(this._classicHistograms).length){const t=e.metricFindQuery("metrics(.*_bucket)"),r=e.metricFindQuery("metrics(.+)"),[n,a]=yield Promise.all([t,r]);n.forEach(e=>{this._classicHistograms[e.text]=1}),yield this._ensureMetricsMetadata(),a.forEach(e=>{this.isNativeHistogram(e.text)&&this.addNativeHistogram(e.text)})}}).call(this)}isNativeHistogram(e){var t;if(!e)return!1;const r=this._metricsMetadata;var n;const a="bucket"!==(null!==(n=e.split("_").pop())&&void 0!==n?n:"");return!("histogram"!==(null==r||null===(t=r[e])||void 0===t?void 0:t.type)||!a)||this._classicHistograms[`${e}_bucket`]>0}addNativeHistogram(e){this._nativeHistograms.includes(e)||this._nativeHistograms.push(e)}getTagKeys(e){return u(function*(){const t=yield this.getDatasource();if(!t)return[];return yield t.getTagKeys(e)}).call(this)}getTagValues(e){return u(function*(){const t=yield this.getDatasource();if(!t)return[];e.key=function(e){if(""===e||!function(e){return/^".*"$/.test(e)}(e))return e;return e.slice(1,-1)}(e.key);return yield t.getTagValues(e)}).call(this)}static fetchLabels(e){const{timeRange:t,matcher:r}=e,n=e.ds;if(s["12.1.0-plus"](n))return n.languageProvider.queryLabelKeys(t,r);if(s["12.0.0"](n))return n.languageProvider.fetchLabelsWithMatch(t,r).then(e=>Object.keys(e));if(s["11.6.x"](n))return n.languageProvider.fetchLabelsWithMatch(r).then(e=>Object.keys(e));throw new Error("Unsupported language provider version")}static fetchLabelValues(e){const{labelName:t,timeRange:r,matcher:n=""}=e,a=e.ds;if(s["12.1.0-plus"](a))return a.languageProvider.queryLabelValues(r,t,n);if(s["12.0.0"](a)){return(n?a.languageProvider.fetchSeriesValuesWithMatch:a.languageProvider.fetchLabelValues)(r,t,n)}if(s["11.6.x"](a)){return(n?a.languageProvider.fetchSeriesValuesWithMatch:a.languageProvider.fetchLabelValues)(t,n)}throw new Error("Unsupported language provider version")}static getPrometheusDataSourceForScene(e){return u(function*(){try{const r=a.jh.findByKey(e,o.EY);var t;const i=null!==(t=null==r?void 0:r.state.value)&&void 0!==t?t:"";return yield(0,n.getDataSourceSrv)().get({uid:i})}catch(e){return void(0,i.jx)(e,["Error while getting the Prometheus data source!"])}})()}constructor(e){d(this,"_trail",void 0),d(this,"_datasource",void 0),d(this,"_metricsMetadata",void 0),d(this,"_classicHistograms",{}),d(this,"_nativeHistograms",[]),this._trail=e}}function m(e){if(!e)return;const{type:t,help:r,unit:n}=e;return[r,t&&`**Type:** *${t}*`,n&&`**Unit:** ${n}`].join("\n\n")}},5521:(e,t,r)=>{r.d(t,{O:()=>v,C:()=>y});var n=r(6089),a=r(7985),i=r(2007),o=r(5959),s=r.n(o),l=r(1053),c=r(7781),u=r(2127),d=r(2425),p=r(4796),m=r(384);const h=(e,t,r)=>e.length+2+t.length>r?t.substring(0,r-e.length-5)+"...":t;function g(e){const{onSelect:t,onDelete:r,bookmark:n}=e,l=(0,i.useStyles2)(f),g=(0,o.useMemo)(()=>{let t=e.trail||n&&(0,d._r)().getTrailForBookmark(n);if(!t)return null;const r=a.jh.lookupVariable(u.Ao,t);if(!(0,m.BE)(r))return null;const i=(null==n?void 0:n.createdAt)||t.state.createdAt;return{filters:r.state.filters,metric:t.state.metric,createdAt:i}},[e.trail,n]);if(!g)return null;const{filters:b,metric:y,createdAt:v}=g,w=h("",(0,p.aO)(y),27),S=`${e.compactHeight&&b.length>0?l.cardTall:""}`,O=`${l.card} ${e.wide?l.cardWide:""} ${S}`;return s().createElement("article",{"data-testid":`data-trail-card ${w}`},s().createElement(i.Card,{onClick:t,className:O},s().createElement(i.Card.Heading,null,s().createElement("div",{className:l.metricValue},w)),s().createElement(i.Card.Meta,{className:l.meta},b.map(e=>s().createElement("span",{key:e.key},s().createElement("div",{className:l.secondaryFont},e.key,": "),s().createElement("div",{className:l.primaryFont},h(e.key,e.value,44))))),s().createElement("div",{className:l.deleteButton},r&&s().createElement(i.Card.SecondaryActions,null,s().createElement(i.IconButton,{key:"delete",name:"trash-alt",className:l.secondary,tooltip:"Remove bookmark",onClick:r,"data-testid":"deleteButton"})))),s().createElement("div",{className:l.date},s().createElement("div",{className:l.secondaryFont},"Date created: "),s().createElement("div",{className:l.primaryFont},v>0&&(0,c.dateTimeFormat)(v,{format:"YYYY-MM-DD"}))))}function f(e){return{metricValue:(0,n.css)({display:"inline",color:e.colors.text.primary,fontWeight:500,wordBreak:"break-all"}),card:(0,n.css)({position:"relative",width:"318px",padding:`12px ${e.spacing(2)} ${e.spacing(1)} ${e.spacing(2)}`,alignItems:"start",marginBottom:0,borderTop:`1px solid ${e.colors.border.weak}`,borderRight:`1px solid ${e.colors.border.weak}`,borderLeft:`1px solid ${e.colors.border.weak}`,borderBottom:"none",borderRadius:"2px 2px 0 0"}),cardWide:(0,n.css)({width:"100%"}),cardTall:(0,n.css)({height:"110px"}),secondary:(0,n.css)({color:e.colors.text.secondary,fontSize:"12px"}),date:(0,n.css)({border:`1px solid ${e.colors.border.weak}`,borderRadius:"0 0 2px 2px",padding:`${e.spacing(1)} ${e.spacing(2)}`,backgroundColor:e.colors.background.primary}),meta:(0,n.css)({flexWrap:"wrap",overflow:"hidden",textOverflow:"ellipsis",maxHeight:"36px",margin:0,gridArea:"Meta",color:e.colors.text.secondary,whiteSpace:"nowrap"}),primaryFont:(0,n.css)({display:"inline",color:e.colors.text.primary,fontSize:"12px",fontWeight:"500",letterSpacing:"0.018px"}),secondaryFont:(0,n.css)({display:"inline",color:e.colors.text.secondary,fontSize:"12px",fontWeight:"400",lineHeight:"18px",letterSpacing:"0.018px"}),deleteButton:(0,n.css)({position:"absolute",bottom:e.spacing(1),right:e.spacing(1)})}}var b=r(3347);const y={listeners:new Set,emit:function(e){this.listeners.forEach(t=>t(e))},subscribe:function(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}};class v extends a.Bs{onActivate(){}constructor({key:e,title:t,description:r,icon:n,disabled:a}){super({key:e,title:t,description:r,icon:n,disabled:null!=a&&a,active:!1}),this.addActivationHandler(this.onActivate.bind(this))}}var w,S,O;function E(e){return{container:(0,n.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%"}),bookmarksList:(0,n.css)({display:"flex",flexDirection:"column",gap:e.spacing(1.5),overflowY:"auto",paddingRight:e.spacing(1)}),emptyState:(0,n.css)({display:"flex",justifyContent:"center",alignItems:"center",height:"100px",color:e.colors.text.secondary,fontStyle:"italic"})}}O=({model:e})=>{const t=(0,i.useStyles2)(E),{title:r,description:n}=e.useState(),{bookmarks:a}=(0,d._r)(),[,c]=(0,o.useState)(Date.now()),u=e=>{(0,b.z)("exploration_started",{cause:"bookmark_clicked"});const t=(0,d._r)().getTrailForBookmarkIndex(e);(0,d._r)().setRecentTrail(t),function(e){y.emit(e)}(t)};return s().createElement("div",{className:t.container},s().createElement(l._,{title:r,description:n,"data-testid":"bookmarks-list-sidebar"}),a.length>0?s().createElement("div",{className:t.bookmarksList},a.map((e,t)=>s().createElement(g,{key:(0,d.yn)(e),bookmark:e,onSelect:()=>u(t),onDelete:()=>(e=>{(0,b.z)("bookmark_changed",{action:"deleted"}),(0,d._r)().removeBookmark(e),c(Date.now())})(t),wide:!0,compactHeight:!0}))):s().createElement("div",{className:t.emptyState},"No bookmarks yet"))},(S="Component")in(w=v)?Object.defineProperty(w,S,{value:O,enumerable:!0,configurable:!0,writable:!0}):w[S]=O},5568:(e,t,r)=>{r.d(t,{$:()=>l,s:()=>c});var n=r(7781),a=r(7985),i=r(2127);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const l="metrics-wingman";class c extends a.fS{constructor(e){super(s(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){o(e,t,r[t])})}return e}({key:l,name:l,label:"Metrics"},e),{datasource:i.GH,query:`label_values({$${i.Ao}}, __name__)`,includeAll:!0,value:"$__all",skipUrlSync:!0,refresh:n.VariableRefresh.onTimeRangeChanged,sort:n.VariableSort.alphabeticalAsc,hide:n.VariableHide.hideVariable}))}}},5635:(e,t,r)=>{r.d(t,{MV:()=>b,Qs:()=>v,_O:()=>y});var n=r(6089),a=r(7985),i=r(6145),o=r(2007),s=r(5959),l=r.n(s),c=r(6503),u=r(6467),d=r(4796),p=r(8156),m=r(3081),h=r(4003),g=r(3831),f=r(2024);const b="repeat(auto-fit, minmax(400px, 1fr))",y="1fr";class v extends a.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=a.jh.findByKeyAndType(this,"layout-switcher",p.U),t=this.state.body.state.body,r=(e,r)=>{e.layout!==(null==r?void 0:r.layout)&&t.setState({templateColumns:e.layout===p.p.ROWS?y:b})};r(e.state),this._subs.add(e.subscribeToState(r))}constructor({variableName:e}){super({key:"metrics-list",variableName:e,body:new g.k({variableName:e,initialPageSize:120,pageSizeIncrement:9,body:new a.gF({children:[],isLazy:!0,templateColumns:b,autoRows:m.tw,$behaviors:[new a.Gg.K2({key:"metricCrosshairSync",sync:i.yV.Crosshair})]}),getLayoutLoading:()=>new a.dM({reactNode:l().createElement(o.Spinner,{inline:!0})}),getLayoutEmpty:()=>new a.dM({reactNode:l().createElement(c._,{title:"",severity:"info"},"No metrics found for the current filters and time range.")}),getLayoutError:e=>new a.dM({reactNode:l().createElement(c._,{severity:"error",title:"Error while loading metrics!",error:e})}),getLayoutChild:(e,t)=>new a.xK({body:new h.c({vizPanelInGridItem:new u.mh({metric:e.value,fixedColor:(0,d.Vy)(t)}),metric:e.value})})})}),this.addActivationHandler(this.onActivate.bind(this))}}var w,S,O;function E(e){return{container:(0,n.css)({}),footer:(0,n.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}})}}O=({model:e})=>{const{variableName:t,body:r}=e.useState(),n=(0,o.useStyles2)(E),i=a.jh.lookupVariable(t,e),{loading:s,error:c}=i.useState(),u=r.useSizes(),d=!s&&!c&&u.total>0&&u.current<u.total;return l().createElement("div",{"data-testid":"metrics-list"},l().createElement("div",{className:n.container},l().createElement(r.Component,{model:r})),d&&l().createElement("div",{className:n.footer},l().createElement(f.d,{label:"metric",batchSizes:u,onClick:()=>{r.increaseBatchSize()}})))},(S="Component")in(w=v)?Object.defineProperty(w,S,{value:O,enumerable:!0,configurable:!0,writable:!0}):w[S]=O},5731:(e,t,r)=>{r.d(t,{K:()=>o});var n=r(3347),a=r(2533),i=r(416);const o=new class{migrate(){let e=!1;const t=[{legacyKey:"metricsDrilldownDataSource",newKey:i.V.DATASOURCE},{legacyKey:"metrics-drilldown-recent-metrics/v1",newKey:i.V.RECENT_METRICS},{legacyKey:"grafana.trails.bookmarks",newKey:i.V.BOOKMARKS},{legacyKey:"grafana.trails.breakdown.sort.labels.by",newKey:i.V.BREAKDOWN_SORTBY}];for(const{legacyKey:r,newKey:n}of t){let t=localStorage.getItem(r);if(null!==t){try{t=JSON.parse(t)}catch(e){}this.setItem(n,t),localStorage.removeItem(r),e=!0}}e&&(0,n.z)("user_preferences_migrated",{})}buildStorageKey(e){return`${this.service}.${e}`}getItem(e){const t=this.buildStorageKey(e),r=localStorage.getItem(t);return null===r?null:JSON.parse(r)}setItem(e,t){const r=this.buildStorageKey(e);localStorage.setItem(r,JSON.stringify(t))}removeItem(e){const t=this.buildStorageKey(e);localStorage.removeItem(t)}clear(){localStorage.clear()}constructor(e){var t,r,n;n=void 0,(r="service")in(t=this)?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,this.service=e}}(a.id)},6096:(e,t,r)=>{r.d(t,{c:()=>h});var n=r(7985),a=r(3823),i=r.n(a);const o=new Map;function s(e,t){let r=o.get(e);r||(r=new Map,o.set(e,r));let n=r.get(t);if(!n){const a=e.split("_"),o=a.slice(0,a.length/2).join("_");n={halfLeven:i()(o,t)||0,wholeLeven:i()(e,t)||0},r.set(t,n)}return n}var l=r(2445),c=r(1252),u=r(1199);function d(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function p(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){d(i,n,a,o,s,"next",e)}function s(e){d(i,n,a,o,s,"throw",e)}o(void 0)})}}function m(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class h{sort(){return p(function*(e=this.sortBy,t={}){const r=this.variable.state.options.map(e=>e.value);if(e===this.sortBy&&(0,u.B)(r,this.lastMetrics))return;let n;switch(e){case"dashboard-usage":case"alerting-usage":n=yield this.sortByUsage(r,e);break;case"related":a=r,i=t.metric,n=a.sort((e,t)=>{const r=s(e,i),n=s(t,i);return r.halfLeven+r.wholeLeven-(n.halfLeven+n.wholeLeven)});break;default:n=(0,c.Rm)(r)}var a,i;this.sortBy=e,this.lastMetrics=n,this.variable.setState({options:n.map(e=>({label:e,value:e}))}),this.notifyUpdate()}).apply(this,arguments)}sortByUsage(e,t){return p(function*(){try{const r=n.jh.findByKeyAndType(this.variable,"metrics-sorter",c.fD);if(!r)return l.v.warn("Metrics sorter not found. Returning unsorted metrics.",{usageType:t}),e;const a=yield r.getUsageMetrics(t);return(0,c.yH)(e,a)}catch(r){const n="string"==typeof r?new Error(r):r;return l.v.error(n,{usageType:t}),e}}).call(this)}notifyUpdate(){this.variable.publishEvent(new n.oh(this.variable),!0)}constructor(e){m(this,"variable",void 0),m(this,"lastMetrics",void 0),m(this,"sortBy",void 0),this.variable=e,this.sortBy=void 0,this.lastMetrics=[]}}},6467:(e,t,r)=>{r.d(t,{mh:()=>_,NC:()=>P,Gx:()=>k,IK:()=>j});var n=r(6089),a=r(7985),i=r(2007),o=r(5959),s=r.n(o),l=r(4796),c=r(8534),u=r(7940),d=r(5938),p=r(7437),m=r(2127),h=r(8162),g=r(2062);function f(e){const{title:t,description:r,metric:n,matchers:i,isNativeHistogram:o,headerActions:s,menu:l,queryResolution:c}=e,u=function(e){const{metric:t,matchers:r,isNativeHistogram:n,queryResolution:a,addIgnoreUsageFilter:i}=e,o=(0,g.d)({metric:t,matchers:r,addIgnoreUsageFilter:i});let s,l,c=(0,g.l)(o);return n?(s=h.GH.rate({expr:c,interval:"$__rate_interval"}),l="rate"):(c=h.GH.rate({expr:c,interval:"$__rate_interval"}),s=h.GH.sum({expr:c,by:["le"]}),l="sum by (le) (rate)"),{fnName:l,maxDataPoints:a===j.HIGH?500:250,queries:[{refId:t,expr:s,format:"heatmap",fromExploreMetrics:!0}]}}({metric:n,matchers:i,isNativeHistogram:Boolean(o),queryResolution:c,addIgnoreUsageFilter:!0}),f=(0,p.l_)(n),b=new a.dt({datasource:m.GH,maxDataPoints:u.maxDataPoints,queries:u.queries});return a.d0.heatmap().setTitle(t).setDescription(o?"Native Histogram":r).setHeaderActions(s({metric:n,panelType:k.HEATMAP})).setMenu(null==l?void 0:l.clone()).setShowMenuAlways(Boolean(l)).setData(b).setUnit(f).setOption("calculate",!1).setOption("color",{mode:d.P7.Scheme,exponent:.5,scheme:"Spectral",steps:32,reverse:!1}).build()}var b=r(1625);function y(e){const{title:t,description:r,metric:n,matchers:i,headerActions:o,menu:s,isNativeHistogram:l,queryResolution:c}=e,u=function(e){const{metric:t,matchers:r,isNativeHistogram:n,queryResolution:a,addIgnoreUsageFilter:i}=e,o=(0,g.d)({metric:t,matchers:r,addIgnoreUsageFilter:i});let s,l=(0,g.l)(o);n?(l=h.GH.rate({expr:l,interval:"$__rate_interval"}),s="histogram_quantile(rate)"):(l=h.GH.rate({expr:l,interval:"$__rate_interval"}),l=h.GH.sum({expr:l,by:["le"]}),s="histogram_quantile(sum by (le) (rate))");const c=[99,90,50].map(e=>({refId:`${t}-p${e}`,expr:`histogram_quantile(${e/100}, ${l})`,legendFormat:`${e}th Percentile`,fromExploreMetrics:!0}));return{fnName:s,isRateQuery:!0,maxDataPoints:a===j.HIGH?500:250,queries:c}}({metric:n,matchers:i,isNativeHistogram:Boolean(l),queryResolution:c,addIgnoreUsageFilter:!0}),d=u.isRateQuery?(0,p.MM)(n):(0,p.l_)(n),f=new a.dt({datasource:m.GH,maxDataPoints:u.maxDataPoints,queries:u.queries});return a.d0.timeseries().setTitle(t).setDescription(r).setHeaderActions(o({metric:n,panelType:k.PERCENTILES})).setMenu(null==s?void 0:s.clone()).setShowMenuAlways(Boolean(s)).setData(f).setUnit(d).setOption("legend",{showLegend:!0,placement:"right"}).setOption("tooltip",{mode:b.$N.Multi,sort:b.xB.Descending}).setCustomFieldConfig("fillOpacity",9).build()}const v=[{type:r(6145).dM.ValueToText,options:{0:{color:"red",text:"down"},1:{color:"green",text:"up"}}}];function w(e){const{title:t,description:r,metric:n,matchers:i,headerActions:o,menu:s,queryResolution:l}=e,c=function(e){const{metric:t,matchers:r,queryResolution:n,addIgnoreUsageFilter:a}=e,i=(0,g.d)({metric:t,matchers:r,addIgnoreUsageFilter:a}),o=h.GH.min({expr:(0,g.l)(i)});return{fnName:"min",maxDataPoints:n===j.HIGH?200:100,queries:[{refId:t,expr:o,fromExploreMetrics:!0}]}}({metric:n,matchers:i,queryResolution:l,addIgnoreUsageFilter:!0}),u=new a.dt({datasource:m.GH,maxDataPoints:c.maxDataPoints,queries:c.queries});return a.d0.statushistory().setTitle(t).setDescription(r).setHeaderActions(o({metric:n,panelType:k.STATUSHISTORY})).setMenu(null==s?void 0:s.clone()).setShowMenuAlways(Boolean(s)).setData(u).setUnit("none").setColor({mode:"palette-classic"}).setMappings(v).setDisplayName("status").setOption("showValue",b.yL.Never).setOption("legend",{showLegend:!0}).setOption("perPage",0).build()}var S=r(8023),O=r(8587);function E(e){if(e.groupBy)return function(e){const{title:t,description:r,metric:n,matchers:i,fixedColor:o,headerActions:s,menu:l,groupBy:c,queryResolution:u}=e,d=(0,O.H)({metric:n,matchers:i,groupBy:c,queryResolution:u,addIgnoreUsageFilter:!0}),h=d.isRateQuery?(0,p.MM)(n):(0,p.l_)(n),g=new a.Es({$data:new a.dt({datasource:m.GH,maxDataPoints:d.maxDataPoints,queries:d.queries}),transformations:[(0,S.b)(c)]});return a.d0.timeseries().setTitle(t).setDescription(r).setHeaderActions(s({metric:n,panelType:k.TIMESERIES})).setMenu(null==l?void 0:l.clone()).setShowMenuAlways(Boolean(l)).setData(g).setUnit(h).setColor(o?{mode:"fixed",fixedColor:o}:void 0).setOption("legend",{showLegend:!0,placement:"right"}).setOption("tooltip",{mode:b.$N.Multi,sort:b.xB.Descending}).build()}(e);const{title:t,description:r,metric:n,matchers:i,fixedColor:o,headerActions:s,groupBy:l,menu:c,queryResolution:u}=e,d=(0,O.H)({metric:n,matchers:i,groupBy:l,queryResolution:u,addIgnoreUsageFilter:!0}),h=d.isRateQuery?(0,p.MM)(n):(0,p.l_)(n),g=new a.dt({datasource:m.GH,maxDataPoints:d.maxDataPoints,queries:d.queries});return a.d0.timeseries().setTitle(t).setDescription(r).setHeaderActions(s({metric:n,panelType:k.TIMESERIES})).setMenu(null==c?void 0:c.clone()).setShowMenuAlways(Boolean(c)).setData(g).setUnit(h).setOption("legend",{showLegend:!0,placement:"bottom"}).setColor(o?{mode:"fixed",fixedColor:o}:void 0).setCustomFieldConfig("fillOpacity",9).setDisplayName(d.fnName).build()}function x(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}var k=function(e){return e.TIMESERIES="TIMESERIES",e.HEATMAP="HEATMAP",e.STATUSHISTORY="STATUSHISTORY",e.PERCENTILES="PERCENTILES",e}({}),P=function(e){return e.S="S",e.M="M",e.L="L",e.XL="XL",e}({}),j=function(e){return e.HIGH="HIGH",e.MEDIUM="MEDIUM",e}({});const C=({metric:e})=>[new c.B({metricName:e})];class _ extends a.Bs{onActivate(){return(e=function*(){const{metric:e,panelType:t}=this.state;this.subscribeToStateChanges(),this.subscribeToEvents();const r=(0,l.kj)(this),n=r.isNativeHistogram(e);if(this.setState({panelType:t||this.getDefaultPanelType(n),isNativeHistogram:n}),n)return;yield r.initializeHistograms();const a=r.isNativeHistogram(e);a&&this.setState({panelType:this.getDefaultPanelType(a),isNativeHistogram:a})},function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){x(i,n,a,o,s,"next",e)}function s(e){x(i,n,a,o,s,"throw",e)}o(void 0)})}).call(this);var e}getDefaultPanelType(e){const{metric:t}=this.state;return(e=>"up"===e||e.endsWith("_up"))(t)?"STATUSHISTORY":e||function(e){return e.endsWith("_bucket")}(t)?"HEATMAP":"TIMESERIES"}static getPanelHeightInPixels(e){switch(e){case"S":return 160;case"L":return 260;case"XL":return 280;default:return 220}}subscribeToStateChanges(){this.subscribeToState((e,t)=>{void 0!==e.isNativeHistogram&&void 0!==e.panelType&&(e.isNativeHistogram===t.isNativeHistogram&&e.panelType===t.panelType||this.updateBody())})}subscribeToEvents(){this.subscribeToEvent(u.H,e=>{this.setState({panelType:e.payload.panelType})})}updateBody(){const{panelType:e,title:t,description:r,metric:n,matchers:a,headerActions:i,menu:o,queryResolution:s,fixedColor:l,groupBy:c,isNativeHistogram:u}=this.state;switch(e){case"TIMESERIES":return void this.setState({body:E({title:t,description:r,metric:n,matchers:a,headerActions:i,menu:o,queryResolution:s,fixedColor:l,groupBy:c})});case"HEATMAP":return void this.setState({body:f({title:t,description:r,metric:n,matchers:a,headerActions:i,menu:o,queryResolution:s,isNativeHistogram:u})});case"PERCENTILES":return void this.setState({body:y({title:t,description:r,metric:n,matchers:a,headerActions:i,menu:o,queryResolution:s,isNativeHistogram:u})});case"STATUSHISTORY":return void this.setState({body:w({title:t,description:r,metric:n,matchers:a,headerActions:i,menu:o,queryResolution:s})});default:throw new TypeError(`Unsupported panel type "${e}"!`)}}changePanelType(e){this.setState({panelType:e})}constructor({metric:e,matchers:t,height:r,headerActions:n,menu:a,panelType:i,fixedColor:o,title:s,groupBy:l,description:c,queryResolution:u}){super({metric:e,matchers:t||[],heightInPixels:`${_.getPanelHeightInPixels(r||"M")}px`,headerActions:n||C,menu:a,panelType:i,fixedColor:o,title:s||e,groupBy:l,description:c,queryResolution:u||"MEDIUM",isNativeHistogram:void 0,body:void 0}),this.addActivationHandler(()=>{this.onActivate()})}}var N,L,T;function D(e,t){return{container:n.css`
      width: 100%;
      height: ${t};
    `}}T=({model:e})=>{const{body:t,heightInPixels:r}=e.useState(),n=(0,i.useStyles2)(D,r);return s().createElement("div",{className:n.container,"data-testid":"gmd-vizpanel"},t&&s().createElement(t.Component,{model:t}))},(L="Component")in(N=_)?Object.defineProperty(N,L,{value:T,enumerable:!0,configurable:!0,writable:!0}):N[L]=T},6503:(e,t,r)=>{r.d(t,{_:()=>c});var n=r(2007),a=r(5959),i=r.n(a),o=r(2445);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function c({severity:e,title:t,message:r,error:a,errorContext:c,children:u}){let d;return a&&(d="string"==typeof a?new Error(a):a,o.v.error(d,l(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){s(e,t,r[t])})}return e}({},d.cause||{},c),{bannerTitle:t}))),i().createElement(n.Alert,{title:t,severity:e},d&&i().createElement(i().Fragment,null,d.message||d.toString(),i().createElement("br",null)),r,u)}},6920:(e,t,r)=>{r.d(t,{e:()=>s});var n,a,i,o=r(7781);class s extends o.BusEventWithPayload{}i="metrics-variable-deactivated",(a="type")in(n=s)?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i},7019:(e,t,r)=>{function n(e,t){return(null==t?void 0:t.state.embedded)||e.isLight?e.colors.background.primary:e.colors.background.canvas}r.d(t,{T:()=>n})},7238:(e,t,r)=>{r.d(t,{I:()=>m});var n=r(6089),a=r(7985),i=r(2007),o=r(3241),s=r(5959),l=r.n(s),c=r(3347),u=r(2127),d=r(1816);function p(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class m extends a.Bs{getUrlState(){return{[this.state.urlSearchParamName]:this.state.value}}updateFromUrl(e){const t=e[this.state.urlSearchParamName]||"";t!==this.state.value&&this.setState({value:t})}toggleCountsDisplay(e){this.setState({displayCounts:e})}updateValue(e){""===this.state.value&&""!==e&&(0,c.z)("quick_search_used",{}),this.setState({value:e}),this.notifyValueChange(e)}useHumanFriendlyCountsMessage(){const{targetName:e,countsProvider:t,displayCounts:r}=this.state,n=t.useCounts();return r?n.current===n.total?{tagName:`${n.current}`,tooltipContent:1!==n.current?`${n.current} ${e}s in total`:`1 ${e} in total`}:{tagName:`${n.current}/${n.total}`,tooltipContent:1!==n.current?`${n.current} out of ${n.total} ${e}s in total`:`1 out of ${n.total} ${e}s in total`}:{tagName:"",tooltipContent:""}}constructor({urlSearchParamName:e,targetName:t,countsProvider:r,displayCounts:n}){super({key:"quick-search",urlSearchParamName:e,targetName:t,countsProvider:r,displayCounts:Boolean(n),value:""}),p(this,"_variableDependency",new a.Sh(this,{variableNames:[u.EY],onReferencedVariableValueChanged:()=>{this.setState({value:""})}})),p(this,"_urlSync",new a.So(this,{keys:[this.state.urlSearchParamName]})),p(this,"notifyValueChange",(0,o.debounce)(e=>{this.publishEvent(new d.W({searchText:e}),!0)},250)),p(this,"onChange",e=>{this.updateValue(e.currentTarget.value)}),p(this,"clear",()=>{this.updateValue("")}),p(this,"onKeyDown",e=>{"Escape"===e.key&&(e.preventDefault(),this.clear())})}}p(m,"Component",({model:e})=>{const t=(0,i.useStyles2)(h),{targetName:r,value:n,countsProvider:a}=e.useState(),{tagName:o,tooltipContent:s}=e.useHumanFriendlyCountsMessage();return l().createElement(i.Input,{value:n,onChange:e.onChange,onKeyDown:e.onKeyDown,placeholder:`Quick search ${r}s`,prefix:l().createElement("i",{className:"fa fa-search"}),suffix:l().createElement(l().Fragment,null,l().createElement(a.Component,{model:a}),o&&l().createElement(i.Tooltip,{content:s,placement:"top"},l().createElement(i.Tag,{className:t.counts,name:o,colorIndex:9})),l().createElement(i.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:e.clear,disabled:!n}))})});const h=e=>({counts:n.css`
    margin-right: ${e.spacing(1)};
    border-radius: 11px;
    padding: 2px ${e.spacing(1)};
    color: ${e.colors.text.primary};
    background-color: ${e.colors.background.secondary};
  `})},7265:(e,t,r)=>{function n(e){var t,r;if(!e)return;const i=null!==(r=e.state.$data)&&void 0!==r?r:null===(t=e.parent)||void 0===t?void 0:t.state.$data;return a(i)?i:null!=(o=i)&&"state"in o&&"transformations"in o.state?n(i):void 0;var o}function a(e){return null!=e&&"state"in e&&"runQueries"in e}r.d(t,{un:()=>n,xT:()=>a})},7397:(e,t,r)=>{r.d(t,{x:()=>s});var n,a,i,o=r(7781);class s extends o.BusEventWithPayload{}i="metrics-variable-loaded",(a="type")in(n=s)?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i},7437:(e,t,r)=>{r.d(t,{MM:()=>h,l_:()=>m});const n="none",a="cps",i="bytes",o="seconds",s="percent",l="count",c={[i]:i,[o]:"s",[s]:s,[l]:n},u=Object.keys(c),d={[i]:"Bps",[o]:n,[l]:a,[s]:s};function p(e){if(!e)return null;const t=e.toLowerCase().split("_").slice(-2);for(let e=t.length-1;e>=Math.max(0,t.length-2);e--){const r=t[e];if(u.includes(r))return r}return null}function m(e){if(!e)return n;const t=p(e);return t&&c[t.toLowerCase()]||n}function h(e){if(!e)return a;const t=p(e);return t&&d[t]||a}},7476:(e,t,r)=>{r.d(t,{aQ:()=>c,tS:()=>p});var n=r(8531),a=r(2445);function i(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function o(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var o=e.apply(t,r);function s(e){i(o,n,a,s,l,"next",e)}function l(e){i(o,n,a,s,l,"throw",e)}s(void 0)})}}function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const l=/^grafana-[0-9a-z]+prometheus-datasource$/;function c(e){return"object"==typeof e&&null!==e&&"type"in e&&"string"==typeof e.type&&("prometheus"===e.type||l.test(e.type))&&"uid"in e&&"string"==typeof e.uid}class u{getHealthyDataSources(e){return o(function*(){const t=this.cache.get(e);if(null==t?void 0:t.length)return t;let r=this.pendingRequests.get(e);r||(r=this.fetchHealthyDataSources(e).finally(()=>{this.pendingRequests.delete(e)}),this.pendingRequests.set(e,r));const n=yield r;return this.cache.set(e,n),n}).call(this)}fetchHealthyDataSources(e){return o(function*(){const t=(0,n.getDataSourceSrv)().getList({logs:!0,type:e,filter:e=>"grafana"!==e.uid}),r=[],i=[];return yield Promise.all(t.map(e=>o(function*(){try{const t=yield(0,n.getBackendSrv)().get(`/api/datasources/uid/${e.uid}/health`,void 0,void 0,{showSuccessAlert:!1,showErrorAlert:!1});"OK"===(null==t?void 0:t.status)?r.push(e):i.push(e)}catch(t){i.push(e)}})())),i.length>0&&a.v.warn(`Found ${i.length} unhealthy ${e} data sources: ${i.map(e=>e.name).join(", ")}`),r})()}constructor(){s(this,"pendingRequests",new Map),s(this,"cache",new Map)}}let d;function p(){return d||(d=new u),d}},7940:(e,t,r)=>{r.d(t,{H:()=>s});var n,a,i,o=r(7781);class s extends o.BusEventWithPayload{}i="panel-type-changed",(a="type")in(n=s)?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i},7977:(e,t,r)=>{r.d(t,{X:()=>a});const n=new Set(["count","total","sum","bucket"]);function a(e){const t=e.split("_").at(-1);return!!t&&n.has(t)}},8023:(e,t,r)=>{r.d(t,{b:()=>o});var n=r(1269);function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const o=e=>()=>t=>t.pipe((0,n.map)(t=>null==t?void 0:t.map(t=>{var r;return(null==t?void 0:t.fields[1])?((null===(r=t.fields[1].labels)||void 0===r?void 0:r[e])||(t.fields[1].labels=i(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){a(e,t,r[t])})}return e}({},t.fields[1].labels),{[e]:`<unspecified ${e}>`})),t):t})))},8156:(e,t,r)=>{r.d(t,{U:()=>c,p:()=>l});var n=r(7985),a=r(2007),i=r(5959),o=r.n(i);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var l=function(e){return e.GRID="grid",e.ROWS="rows",e.SINGLE="single",e}({});class c extends n.Bs{getUrlState(){return{[this.state.urlSearchParamName]:this.state.layout}}updateFromUrl(e){const t={},r=e[this.state.urlSearchParamName];r!==this.state.layout&&(t.layout=this.state.options.find(e=>e.value===r)?r:c.DEFAULT_LAYOUT),this.setState(t)}constructor({urlSearchParamName:e,options:t}){super({key:"layout-switcher",urlSearchParamName:e||"layout",options:t||c.DEFAULT_OPTIONS,layout:c.DEFAULT_LAYOUT}),s(this,"_urlSync",new n.So(this,{keys:[this.state.urlSearchParamName]})),s(this,"onChange",e=>{this.setState({layout:e})})}}s(c,"DEFAULT_OPTIONS",[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]),s(c,"DEFAULT_LAYOUT","grid"),s(c,"Component",({model:e})=>{const{options:t,layout:r}=e.useState();return o().createElement(a.RadioButtonGroup,{"aria-label":"Layout switcher",options:t,value:r,onChange:e.onChange,fullWidth:!1})})},8361:(e,t,r)=>{r.d(t,{S:()=>s});var n,a,i,o=r(7781);class s extends o.BusEventWithPayload{}i="sort-by-changed",(a="type")in(n=s)?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i},8499:(e,t,r)=>{r.d(t,{m:()=>rt});var n=r(6089),a=r(7781),i=r(8531),o=r(7985),s=r(2007),l=r(5959),c=r.n(l),u=r(3347),d=r(4796),p=r(2127),m=r(6503),h=r(5490),g=r(384),f=r(3616),b=r(4964);function y(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function v(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){y(i,n,a,o,s,"next",e)}function s(e){y(i,n,a,o,s,"throw",e)}o(void 0)})}}const w="(none)";class S extends o.UU{query(){return v(function*(){return{state:a.LoadingState.Done,data:[{name:"Labels",fields:[{name:null,type:a.FieldType.other,values:[],config:{}}],length:0}]}})()}metricFindQuery(e,t){return v(function*(){var r,n;const a=null===(n=t.scopedVars)||void 0===n||null===(r=n.__sceneObject)||void 0===r?void 0:r.valueOf(),i=yield h.q.getPrometheusDataSourceForScene(a);if(!i)return[];var o;const[,s]=null!==(o=e.match(/valuesOf\((.+)\)/))&&void 0!==o?o:[];if(s){return(yield S.fetchLabelValues(s,a)).map(e=>({value:e,text:e}))}let l=[];try{l=yield this.fetchLabels(i,a,e)}catch(e){(0,f.HA)(["Error while fetching labels! Defaulting to an empty array.",e.toString()])}return[{value:w,text:"(none)"},...l]}).call(this)}fetchLabels(e,t,r){return v(function*(){if(!S.getLabelsMatchAPISupport(e)){const r=S.getFiltersFromVariable(t),n=yield e.getTagKeys(r);return this.processLabelOptions(n.map(({text:e})=>({value:e,text:e})))}const n=yield h.q.fetchLabels({ds:e,timeRange:o.jh.getTimeRange(t).state.value,matcher:r});return this.processLabelOptions(n.map(e=>({value:e,text:e})))}).call(this)}static getLabelsMatchAPISupport(e){try{return e.hasLabelsMatchAPISupport()}catch(e){return(0,f.HA)(["Error while checking if the current data source supports the labels match API! Defaulting to false.",e.toString()]),!1}}static getFiltersFromVariable(e){const t=o.jh.lookupVariable(p.Ao,e);return(0,g.BE)(t)?{filters:t.state.filters}:{filters:[]}}processLabelOptions(e){return e.filter(({value:e})=>!e.startsWith("__")).sort((e,t)=>(0,b._)(e.value,t.value))}static fetchLabelValues(e,t){return v(function*(){const r=yield h.q.getPrometheusDataSourceForScene(t);if(!r)return[];try{return yield h.q.fetchLabelValues({ds:r,labelName:e,timeRange:o.jh.getTimeRange(t).state.value})}catch(t){return(0,f.HA)([`Error while retrieving label "${e}" values! Defaulting to an empty array.`,t.toString()]),[]}})()}testDatasource(){return v(function*(){return{status:"success",message:"OK"}})()}constructor(){super(S.uid,S.uid)}}var O,E,x;x="grafana-prometheus-labels-datasource",(E="uid")in(O=S)?Object.defineProperty(O,E,{value:x,enumerable:!0,configurable:!0,writable:!0}):O[E]=x;const k="wingmanLabelValues";class P extends o.fS{constructor({labelName:e}){super({name:k,datasource:{uid:S.uid},query:`valuesOf(${e})`,isMulti:!1,allowCustomValue:!1,refresh:a.VariableRefresh.onTimeRangeChanged,hide:a.VariableHide.hideVariable,value:"$__all",includeAll:!0})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(P,"Component",()=>c().createElement(c().Fragment,null));var j=r(3831),C=r(2024),_=r(6467);const N="labelsWingman";class L extends o.fS{onActivate(){this._subs.add(this.subscribeToState((e,t)=>{e.query!==t.query&&(t.query&&this.setState({value:w}),this.refreshOptions())})),this._subs.add(o.jh.findByKeyAndType(this,p.EY,o.mI).subscribeToState((e,t)=>{e.value!==t.value&&(this.setState({value:w}),this.refreshOptions())})),this._subs.add(o.jh.findByKeyAndType(this,p.Ao,o.H9).subscribeToState((e,t)=>{e.filterExpression!==t.filterExpression&&this.updateQuery()})),this.updateQuery()}updateQuery(){const e=o.jh.interpolate(this,p.ui,{});this.setState({query:`{__name__=~".+",${e}}`})}constructor(){super({name:N,label:"Group by label",placeholder:"Group by label...",datasource:{uid:S.uid},query:"",includeAll:!1,isMulti:!1,allowCustomValue:!1,refresh:a.VariableRefresh.onTimeRangeChanged,hide:a.VariableHide.hideVariable}),this.addActivationHandler(this.onActivate.bind(this))}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(L,"Component",({model:e})=>{const t=(0,s.useStyles2)(T),{label:r}=e.useState();return c().createElement("div",{className:t.container},c().createElement(s.Label,{className:t.label},r),c().createElement(o.fS.Component,{model:e}))});const T=e=>({container:n.css`
    display: flex;
    align-items: center;
    gap: 0;

    [class*='input-wrapper'] {
      width: 240px;
    }
  `,label:n.css`
    height: 32px;
    white-space: nowrap;
    margin: 0;
    background-color: ${e.colors.background.primary};
    padding: ${e.spacing(1)};
    border-radius: ${e.shape.radius.default};
    border: 1px solid ${e.colors.border.weak};
    border-right: none;
  `});var D=r(8156),A=r(5635),B=r(3081),I=r(4003);function M(){return c().createElement("svg",{stroke:"currentColor",width:"17",height:"16",viewBox:"0 0 17 16",fill:"none"},c().createElement("circle",{cx:"8.92688",cy:"3.63132",r:"2.375",strokeWidth:"1.5"}),c().createElement("path",{d:"M13.6469 4.37965C14.6813 4.76699 15.3235 7.03139 14.9362 8.06582",strokeWidth:"1.5"}),c().createElement("path",{d:"M4.35309 4.37965C3.31866 4.76699 2.67651 7.03139 3.06384 8.06582",strokeWidth:"1.5"}),c().createElement("path",{d:"M10.3408 14.2531C9.75237 14.8415 8.11813 14.7799 7.50903 14.1708",strokeWidth:"1.5"}),c().createElement("circle",{cx:"4.00195",cy:"12.251",r:"2.375",strokeWidth:"1.5"}),c().createElement("circle",{cx:"13.8478",cy:"12.251",r:"2.375",strokeWidth:"1.5"}))}var R=r(1464);function $(e,t,r,n,a,i,o){try{var s=e[i](o),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,a)}function H(e){return function(){var t=this,r=arguments;return new Promise(function(n,a){var i=e.apply(t,r);function o(e){$(i,n,a,o,s,"next",e)}function s(e){$(i,n,a,o,s,"throw",e)}o(void 0)})}}class V extends o.UU{query(){return H(function*(){return{state:a.LoadingState.Done,data:[{name:"Labels",fields:[{name:null,type:a.FieldType.other,values:[],config:{}}],length:0}]}})()}metricFindQuery(e,t){return H(function*(){var r,n;const a=null===(n=t.scopedVars)||void 0===n||null===(r=n.__sceneObject)||void 0===r?void 0:r.valueOf(),i=yield h.q.getPrometheusDataSourceForScene(a);if(!i)return[];const s=o.jh.getTimeRange(a).state.value;let l=[];const c=e.startsWith("removeRules"),u=c?e.replace("removeRules",""):e;return l=yield h.q.fetchLabelValues({ds:i,labelName:"__name__",matcher:u,timeRange:s}),c&&(l=l.filter(e=>!(e=>"ALERTS"===e||"ALERTS_FOR_STATE"===e||e.includes(":"))(e))),l.map(e=>({value:e,text:e}))})()}testDatasource(){return H(function*(){return{status:"success",message:"OK"}})()}constructor(){super(V.uid,V.uid)}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(V,"uid","grafana-prometheus-metrics-with-label-values-datasource");const F="metrics-with-label-value";class z extends o.fS{onActivate(e,t,r){const n=o.jh.lookupVariable(p.Ao,this);(null==n?void 0:n.state.hide)!==a.VariableHide.hideVariable&&this.setState({query:z.buildQuery(e,t,r)})}static buildQuery(e,t,r){return r?`removeRules{${e}="${t}",${p.ui}}`:`{${e}="${t}",${p.ui}}`}constructor({labelName:e,labelValue:t,removeRules:r}){return super({key:`${F}-${e}-${t}`,name:F,datasource:{uid:V.uid},query:z.buildQuery(e,t,r),isMulti:!1,allowCustomValue:!1,refresh:a.VariableRefresh.onTimeRangeChanged,hide:a.VariableHide.hideVariable,skipUrlSync:!0,value:"$__all",includeAll:!0}),this.addActivationHandler(this.onActivate.bind(this,e,t,r)),(0,R.I)(this)}}class U extends o.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=o.jh.findByKeyAndType(this,"layout-switcher",D.U),t=this.state.body.state.body,r=(e,r)=>{e.layout!==(null==r?void 0:r.layout)&&t.setState({templateColumns:e.layout===D.p.ROWS?A._O:A.MV})};r(e.state),this._subs.add(e.subscribeToState(r))}constructor({index:e,labelName:t,labelValue:r,labelCardinality:n}){super({index:e,labelName:t,labelValue:r,labelCardinality:n,key:`${t||""}-${r||""}`,$variables:new o.Pj({variables:[new z({labelName:t,labelValue:r})]}),body:new j.k({variableName:F,initialPageSize:3,body:new o.gF({children:[],isLazy:!0,templateColumns:A.MV,autoRows:B.iK,$behaviors:[new o.Gg.K2({key:"metricCrosshairSync",sync:a.DashboardCursorSync.Crosshair})]}),getLayoutLoading:()=>new o.dM({reactNode:c().createElement(s.Spinner,{inline:!0})}),getLayoutEmpty:()=>new o.dM({reactNode:c().createElement(m._,{title:"",severity:"info"},"No metrics found for the current filters and time range.")}),getLayoutError:e=>new o.dM({reactNode:c().createElement(m._,{severity:"error",title:"Error while loading metrics!",error:e})}),getLayoutChild:(e,n)=>new o.xK({body:new I.c({vizPanelInGridItem:new _.mh({metric:e.value,matchers:[{key:t,operator:"=",value:r}],fixedColor:(0,d.Vy)(n)}),metric:e.value})})})}),this.addActivationHandler(this.onActivate.bind(this))}}function q(e){return{container:(0,n.css)({background:e.colors.background.canvas,margin:e.spacing(1,0,0,0),"& div:focus-within":{boxShadow:"none !important"}}),containerHeader:(0,n.css)({display:"flex",alignItems:"center",gap:"8px",marginBottom:"-36px",paddingBottom:e.spacing(1.5),borderBottom:`1px solid ${e.colors.border.medium}`}),headerButtons:(0,n.css)({position:"relative",top:"3px",marginLeft:"auto",marginRight:"30px",zIndex:100}),selectButton:(0,n.css)({height:"28px"}),collapsableSectionBody:(0,n.css)({display:"flex",flexDirection:"column",gap:"24px",padding:e.spacing(1)}),groupName:(0,n.css)({display:"flex",alignItems:"center",fontSize:"1.3rem",lineHeight:"1.3rem"}),labelValue:(0,n.css)({fontSize:"16px",marginLeft:"8px"}),index:(0,n.css)({fontSize:"12px",color:e.colors.text.secondary,marginLeft:"8px"}),footer:(0,n.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(1),"& button":{height:"40px"}}),variable:(0,n.css)({display:"none"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(U,"Component",({model:e})=>{const[t,r]=(0,l.useState)(!1),n=(0,s.useStyles2)(q),{index:a,labelName:i,labelValue:u,labelCardinality:d,$variables:m,body:h}=e.useState(),g=m.state.variables[0],{loading:f,error:b}=g.useState(),y=h.useSizes(),v=!f&&!b&&y.total>0&&y.current<y.total;return c().createElement("div",{className:n.container,"data-testid":`${i}-${u}-metrics-group`},c().createElement("div",{className:n.containerHeader},c().createElement("div",{className:n.headerButtons},c().createElement(s.Button,{className:n.selectButton,variant:"secondary",onClick:()=>{var t;const r=o.jh.lookupVariable(p.Ao,e);r.setState({filters:[...r.state.filters,{key:i,operator:"=",value:u}]}),null===(t=o.jh.lookupVariable(N,e))||void 0===t||t.changeValueTo(w)},tooltip:`See metrics with ${i}=${u}`,tooltipPlacement:"top"},"Select"))),c().createElement(s.CollapsableSection,{isOpen:!t,onToggle:()=>r(!t),label:c().createElement("div",{className:n.groupName},c().createElement(M,null),c().createElement("div",{className:n.labelValue},u),d>1&&c().createElement("div",{className:n.index},"(",a+1,"/",d,")"))},c().createElement("div",{className:n.collapsableSectionBody},c().createElement(h.Component,{model:h})),v&&c().createElement("div",{className:n.footer},c().createElement(C.d,{label:"metric",batchSizes:y,onClick:()=>{h.increaseBatchSize()},tooltip:`Show more metrics for ${i}="${u}"`}))),c().createElement("div",{className:n.variable},c().createElement(g.Component,{key:g.state.name,model:g})))});class G extends o.Bs{constructor({labelName:e}){super({key:"metrics-group-list",labelName:e,$variables:new o.Pj({variables:[new P({labelName:e})]}),body:new j.k({variableName:k,initialPageSize:20,pageSizeIncrement:10,body:new o.gF({children:[],isLazy:!0,templateColumns:"1fr",autoRows:"auto",rowGap:1}),getLayoutLoading:()=>new o.dM({reactNode:c().createElement(s.Spinner,{inline:!0})}),getLayoutEmpty:()=>new o.dM({reactNode:c().createElement(m._,{title:"",severity:"info"},'No label values found for label "',e,'".')}),getLayoutError:t=>new o.dM({reactNode:c().createElement(m._,{severity:"error",title:`Error while loading label "${e}" values!`,error:t})}),getLayoutChild:(t,r,n)=>new o.xK({body:new U({index:r,labelName:e,labelValue:t.value,labelCardinality:n.length})})})})}}function K(e){return{footer:(0,n.css)({display:"flex",justifyContent:"center",alignItems:"center",margin:e.spacing(3,0,1,0),"& button":{height:"40px"}}),variable:(0,n.css)({display:"none"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(G,"Component",({model:e})=>{const t=(0,s.useStyles2)(K),{body:r,$variables:n,labelName:a}=e.useState(),i=n.state.variables[0],{loading:o,error:l}=i.useState(),u=r.useSizes(),d=!o&&!l&&u.total>0&&u.current<u.total;return c().createElement("div",{"data-testid":"metrics-groupby-list"},c().createElement(r.Component,{model:r}),d&&c().createElement("div",{className:t.footer},c().createElement(C.d,{label:`"${a}" value`,batchSizes:u,onClick:()=>{r.increaseBatchSize()}})),c().createElement("div",{className:t.variable},c().createElement(i.Component,{key:i.state.name,model:i})))});var W=r(1252),Q=r(9966),Y=r(7238);function J(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function X(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}class Z extends o.P1{constructor(e){super(X(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){J(e,t,r[t])})}return e}({},e),{key:"list-controls",body:new o.G1({direction:"row",width:"100%",maxHeight:"32px",children:[new o.vA({body:new Y.I({urlSearchParamName:"search_txt",targetName:"metric",countsProvider:new Q.s})}),new o.vA({width:"auto",body:new W.fD({})}),new o.vA({width:"auto",body:new D.U({})})]})}))}}function ee(){return{headerWrapper:(0,n.css)({display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center"}}})}}J(Z,"Component",({model:e})=>{const t=(0,s.useStyles2)(ee),{body:r}=e.useState();return c().createElement("div",{className:t.headerWrapper},c().createElement(r.Component,{model:r}))});var te=r(8361),re=r(1816),ne=r(697),ae=r(6920),ie=r(7397),oe=r(9585),se=r(5568),le=r(5036),ce=r(6096);class ue extends a.BusEventWithPayload{}function de(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(ue,"type","apply-function");class pe extends o.Bs{constructor({metricName:e,prometheusFunction:t,disabled:r}){super({key:`apply-action-${e}`,metricName:e,prometheusFunction:t,disabled:Boolean(r)}),de(this,"onClick",e=>{const{metricName:t,prometheusFunction:r}=this.state;e.preventDefault(),this.publishEvent(new ue({metricName:t,prometheusFunction:r}),!0)})}}de(pe,"Component",({model:e})=>{const t=(0,s.useStyles2)(me),{disabled:r}=e.useState();return c().createElement(s.Button,{variant:"primary",fill:"outline",size:"sm",className:t.selectButton,onClick:e.onClick,disabled:r},"Apply")});const me=()=>({selectButton:n.css``});class he extends a.BusEventWithPayload{}function ge(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(he,"type","configure-function");class fe extends o.Bs{constructor({metricName:e}){super({key:`configure-action-${e}`,metricName:e}),ge(this,"onClick",()=>{this.publishEvent(new he({metricName:this.state.metricName}),!0)})}}ge(fe,"PROMETHEUS_FN_OPTIONS",[{label:"Average",value:"avg"},{label:"Sum",value:"sum"},{label:"Minimum",value:"min"},{label:"Maximum",value:"max"},{label:"Rate",value:"rate"}]),ge(fe,"Component",({model:e})=>{const t=(0,s.useStyles2)(be);return c().createElement(s.Button,{className:t.selectButton,"aria-label":"Configure",variant:"secondary",size:"sm",fill:"text",onClick:e.onClick,icon:"cog",tooltip:"Configure the Prometheus function",tooltipPlacement:"top"})});const be=()=>({selectButton:n.css`
    margin: 0;
    padding: 0;
  `});function ye(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ve(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){ye(e,t,r[t])})}return e}function we(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}class Se extends o.Bs{constructor(e){super(ve({key:"drawer",isOpen:!1},e)),ye(this,"open",({title:e,subTitle:t,body:r})=>{this.setState(we(ve({},this.state),{isOpen:!0,title:e,subTitle:t,body:r}))}),ye(this,"close",()=>{this.setState({isOpen:!1})})}}ye(Se,"Component",({model:e})=>{const{isOpen:t,title:r,subTitle:n,body:a}=e.useState();return c().createElement(c().Fragment,null,a&&t&&c().createElement(s.Drawer,{size:"lg",title:r,subtitle:n,closeOnMaskClick:!0,onClose:e.close},c().createElement(a.Component,{model:a})))});var Oe=r(28),Ee=r(2445);const xe="Non-rules metrics",ke="Recording rules";class Pe extends a.BusEventWithPayload{}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Pe,"type","section-value-changed");var je=r(1053);const Ce=({label:e,count:t,checked:r,onChange:n})=>{const a=(0,s.useStyles2)(_e);return c().createElement("div",{className:a.checkboxWrapper,title:e},c().createElement(s.Checkbox,{label:e,value:r,onChange:n}),c().createElement("span",{className:a.count},"(",t,")"))};function _e(e){return{checkboxWrapper:(0,n.css)({display:"flex",alignItems:"center",width:"100%","& label *":{fontSize:"14px !important",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}}),count:(0,n.css)({color:e.colors.text.secondary,marginLeft:e.spacing(.5),display:"inline-block"})}}function Ne({groups:e,selectedGroups:t,onSelectionChange:r}){const n=(0,s.useStyles2)(Le);return c().createElement(c().Fragment,null,c().createElement("div",{className:n.checkboxListHeader},c().createElement("div",null,t.length," selected"),c().createElement(s.Button,{variant:"secondary",fill:"text",onClick:()=>r([]),disabled:!t.length},"clear")),!e.length&&c().createElement("div",{className:n.noResults},"No results."),e.length>0&&c().createElement("ul",{className:n.checkboxList,"data-testid":"checkbox-filters-list"},e.map(e=>c().createElement("li",{key:e.value,className:n.checkboxItem},c().createElement(Ce,{label:e.label,count:e.count,checked:t.some(t=>t.value===e.value),onChange:n=>{const a=n.currentTarget.checked?[...t,{label:e.label,value:e.value}]:t.filter(t=>t.value!==e.value);r(a)}})))))}function Le(e){return{checkboxListHeader:(0,n.css)({display:"flex",justifyContent:"space-between",alignItems:"center",color:e.colors.text.secondary,margin:e.spacing(0),padding:e.spacing(0,0,0,1)}),checkboxList:(0,n.css)({height:"100%",margin:0,padding:e.spacing(0,1,1,1),overflowY:"auto","& .css-1n4u71h-Label":{fontSize:"14px !important"},"&::-webkit-scrollbar":{"-webkit-appearance":"none",width:"7px"},"&::-webkit-scrollbar-thumb":{borderRadius:"4px",backgroundColor:e.colors.secondary.main,"-webkit-box-shadow":`0 0 1px ${e.colors.secondary.shade}`}}),checkboxItem:(0,n.css)({display:"flex",alignItems:"center",width:"100%",padding:e.spacing(.5,0)}),noResults:(0,n.css)({fontStyle:"italic",padding:e.spacing(0,1,1,1)})}}function Te(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function De(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){Te(e,t,r[t])})}return e}function Ae(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}class Be extends o.Bs{getUrlState(){return{[this.state.key]:this.state.selectedGroups.map(e=>e.value).join(",")}}updateFromUrl(e){const t={};"string"==typeof e[this.state.key]&&e[this.state.key]!==this.state.selectedGroups.map(e=>e.value).join(",")&&(t.selectedGroups=e[this.state.key].split(",").map(e=>({label:e,value:e}))),this.setState(t)}onActivate(){const e=o.jh.lookupVariable(se.$,this),t=o.jh.lookupVariable(oe.h,this);this.updateLists(e.state.options),this.updateCounts();const{selectedGroups:r}=this.state;this.setState({loading:t.state.loading,active:r.length>0})}updateLists(e){this.setState({groups:this.state.computeGroups(e),loading:!1})}updateCounts(){var e;const{groups:t,computeGroups:r,type:n}=this.state,a=o.jh.lookupVariable(se.$,this).state.options,i=null===(e=o.jh.getAncestor(this,rt).state.enginesMap.get(oe.h))||void 0===e?void 0:e.filterEngine;if(!i)return void Ee.v.warn("MetricsFilterSection: No filter engine found");const s=Ae(De({},i.getFilters()),{[n]:[]}),l=le.k.getFilteredOptions(a,s),c=new Map(r(l).map(e=>[e.label,e.count])),u=t.map(e=>{var t;return Ae(De({},e),{count:null!==(t=c.get(e.label))&&void 0!==t?t:0})});this.setState({groups:u,loading:!1})}constructor({key:e,type:t,title:r,description:n,icon:a,computeGroups:i,showHideEmpty:s,showSearch:l,disabled:c,active:d}){super({key:e,type:t,title:r,description:n,icon:a,groups:[],computeGroups:i,selectedGroups:[],loading:!0,showHideEmpty:null==s||s,showSearch:null==l||l,disabled:null!=c&&c,active:null!=d&&d}),Te(this,"_variableDependency",new o.Sh(this,{variableNames:[se.$,oe.h],onReferencedVariableValueChanged:e=>{const{name:t,options:r}=e.state;t!==se.$?t===oe.h&&this.updateCounts():this.updateLists(r)}})),Te(this,"_urlSync",new o.So(this,{keys:[this.state.key]})),Te(this,"onSelectionChange",e=>{this.setState({selectedGroups:e,active:e.length>0}),this.publishEvent(new Oe.Y({type:this.state.type,filters:e.map(e=>e.value)}),!0),this.publishEvent(new Pe({key:this.state.key,values:e.map(e=>e.label)}),!0),"prefixes"===this.state.type?(0,u.z)("sidebar_prefix_filter_applied",{filter_count:e.length}):"suffixes"===this.state.type&&(0,u.z)("sidebar_suffix_filter_applied",{filter_count:e.length}),"filters-rule"===this.state.key&&e.length>0&&e.forEach(e=>{let t;switch(e.label){case xe:t="non_rules_metrics";break;case ke:t="recording_rules";break;default:return}(0,u.z)("sidebar_rules_filter_selected",{filter_type:t})})}),this.addActivationHandler(this.onActivate.bind(this))}}function Ie(e){return{container:(0,n.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"}),switchContainer:(0,n.css)({display:"flex",alignItems:"center",justifyContent:"flex-end",gap:e.spacing(1)}),switchLabel:(0,n.css)({fontSize:"12px",color:e.colors.text.primary}),searchInput:(0,n.css)({flexBasis:"32px",flexShrink:0,marginBottom:e.spacing(1),padding:e.spacing(0,.5)})}}Te(Be,"Component",({model:e})=>{const t=(0,s.useStyles2)(Ie),{groups:r,selectedGroups:n,loading:a,title:i,description:o,showHideEmpty:u,showSearch:d}=e.useState(),[p,m]=(0,l.useState)(!1),[h,g]=(0,l.useState)(""),f=(0,l.useMemo)(()=>{const e=[];return p&&e.push(e=>e.count>0),e.push(e=>e.label.toLowerCase().includes(h.toLowerCase())),r.filter(t=>e.every(e=>e(t)))},[p,r,h]);return c().createElement("div",{className:t.container},c().createElement(je._,{title:i,description:o}),u&&c().createElement("div",{className:t.switchContainer},c().createElement("span",{className:t.switchLabel},"Hide empty"),c().createElement(s.Switch,{value:p,onChange:e=>m(e.currentTarget.checked)})),d&&c().createElement(s.Input,{className:t.searchInput,prefix:c().createElement(s.Icon,{name:"search"}),placeholder:"Search...",value:h,onChange:e=>g(e.currentTarget.value),onKeyDown:e=>{"Escape"===e.key&&(e.preventDefault(),g(""))},suffix:c().createElement(s.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:()=>g("")})}),a&&c().createElement(s.Spinner,{inline:!0}),!a&&c().createElement(Ne,{groups:f,selectedGroups:n,onSelectionChange:e.onSelectionChange}))});var Me=r(8628);function Re(e){const t=new Map;for(const n of e){const e=n.value.split(/[^a-z0-9]/i),a=e.length<=1?n.value:e[e.length-1];var r;const i=null!==(r=t.get(a))&&void 0!==r?r:[];i.push(n.value),t.set(a||"<none>",i)}const n=new Map;for(const[e,r]of t)n.set(e,r.length);return Array.from(n.entries()).sort((e,t)=>e[1]!==t[1]?t[1]-e[1]:(0,b._)(e[0],t[0])).map(([e,t])=>({value:e,count:t,label:e}))}function $e(e){const t=new Map([["metrics",[]],["rules",[]]]);for(const n of e){const{value:e}=n,a=/:/i.test(e)?"rules":"metrics";var r;const i=null!==(r=t.get(a))&&void 0!==r?r:[];i.push(e),t.set(a,i)}return[{value:"^(?!.*:.*)",label:xe,count:t.get("metrics").length},{value:":",label:ke,count:t.get("rules").length}]}var He=r(5521);function Ve({labels:e,selectedLabel:t,onClickLabel:r,onClickClearSelection:n}){const a=(0,s.useStyles2)(Fe);return c().createElement(c().Fragment,null,c().createElement("div",{className:a.listHeader},c().createElement("div",{className:a.selected},t===w?"No selection":`Selected: "${t}"`),c().createElement(s.Button,{variant:"secondary",fill:"text",onClick:n,disabled:t===w},"clear")),!e.length&&c().createElement("div",{className:a.noResults},"No results."),e.length>0&&c().createElement("div",{className:a.list,"data-testid":"labels-list"},c().createElement(s.RadioButtonList,{name:"labels-list",options:e,onChange:r,value:t})))}function Fe(e){return{listHeader:(0,n.css)({display:"flex",justifyContent:"space-between",alignItems:"center",color:e.colors.text.secondary,margin:e.spacing(0),padding:e.spacing(0,0,0,1)}),selected:(0,n.css)({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),list:(0,n.css)({display:"flex",flex:1,flexDirection:"column",gap:0,overflowY:"auto",'& [role="radiogroup"]':{gap:0},"& label":{cursor:"pointer",padding:e.spacing(.5,1),"&:hover":{background:e.colors.background.secondary}},"& label div":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}}),noResults:(0,n.css)({fontStyle:"italic",padding:e.spacing(0,1,1,1)})}}function ze(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class Ue extends o.Bs{onActivate(){const e=o.jh.lookupVariable(this.state.variableName,this),t=e.state.value;this.setState({active:Boolean(t&&t!==w)}),this._subs.add(e.subscribeToState(e=>{const t=Boolean(e.value&&e.value!==w);this.setState({active:t}),this.publishEvent(new Pe({key:this.state.key,values:t?[e.value]:[]}),!0)}))}selectLabel(e){o.jh.lookupVariable(this.state.variableName,this).changeValueTo(e);const t=Boolean(e&&e!==w);this.setState({active:t}),this.publishEvent(new Pe({key:this.state.key,values:t?[e]:[]}),!0)}constructor({key:e,variableName:t,title:r,description:n,icon:a,disabled:i,active:s}){super({key:e,variableName:t,title:r,description:n,icon:a,disabled:null!=i&&i,active:null!=s&&s}),ze(this,"onClickLabel",e=>{(0,u.z)("sidebar_group_by_label_filter_applied",{label:e}),this.selectLabel(e)}),ze(this,"onClickClearSelection",()=>{this.selectLabel(w)}),ze(this,"useLabelsBrowser",()=>{const{variableName:e,title:t,description:r}=this.useState(),n=o.jh.lookupVariable(e,this),{loading:a,options:i,value:s}=n.useState(),[c,u]=(0,l.useState)("");return{title:t,description:r,loading:a,selectedLabel:s,labelsList:(0,l.useMemo)(()=>{const e=[e=>e!==w,e=>e.toLowerCase().includes(c.toLowerCase())];return i.filter(t=>e.every(e=>e(t.value)))},[i,c]),searchValue:c,onInputChange:e=>{u(e.currentTarget.value)},onInputKeyDown:e=>{"Escape"===e.key&&(e.preventDefault(),u(""))},onInputClear:()=>{u("")}}}),this.addActivationHandler(this.onActivate.bind(this))}}function qe(e){return{container:(0,n.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"}),search:(0,n.css)({marginBottom:e.spacing(1),padding:e.spacing(0,.5)})}}ze(Ue,"Component",({model:e})=>{const t=(0,s.useStyles2)(qe),{title:r,description:n,loading:a,labelsList:i,selectedLabel:o,searchValue:l,onInputChange:u,onInputKeyDown:d,onInputClear:p}=e.useLabelsBrowser();return c().createElement("div",{className:t.container,"data-testid":"labels-browser"},c().createElement(je._,{title:r,description:n}),c().createElement(s.Input,{className:t.search,prefix:c().createElement(s.Icon,{name:"search"}),placeholder:"Search...",value:l,onChange:u,onKeyDown:d,suffix:c().createElement(s.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:p})}),a&&c().createElement(s.Spinner,{inline:!0}),!a&&c().createElement(Ve,{labels:i,selectedLabel:o,onClickLabel:e.onClickLabel,onClickClearSelection:e.onClickClearSelection}))});class Ge extends o.Bs{onActivate(){}constructor({key:e,title:t,description:r,icon:n,disabled:a}){super({key:e,title:t,description:r,icon:n,disabled:null!=a&&a,active:!1}),this.addActivationHandler(this.onActivate.bind(this))}}function Ke(e){return{container:(0,n.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"})}}!function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(Ge,"Component",({model:e})=>{const t=(0,s.useStyles2)(Ke),{title:r,description:n}=e.useState();return c().createElement("div",{className:t.container},c().createElement(je._,{title:r,description:n}))});const We=new Map([["rules",function(){return c().createElement("svg",{stroke:"currentColor",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},c().createElement("rect",{x:"1.25",y:"1.625",width:"5.25",height:"5.25",rx:"1",strokeWidth:"1.5"}),c().createElement("circle",{cx:"12.25",cy:"4.25",r:"2.75",strokeWidth:"1.5"}),c().createElement("circle",{cx:"3.75",cy:"11.75",r:"2.75",strokeWidth:"1.5"}),c().createElement("rect",{x:"9.5",y:"9.125",width:"5.25",height:"5.25",rx:"1",strokeWidth:"1.5"}))}],["groups",M]]);function Qe({ariaLabel:e,disabled:t,visible:r,active:i,tooltip:o,iconOrText:l,onClick:u}){const d=(0,s.useStyles2)(Ye);let p,m;return l in a.availableIconsIndex?p=l:m=We.has(l)?We.get(l):function(){return c().createElement(c().Fragment,null,l)},c().createElement(s.Button,{className:(0,n.cx)(d.button,t&&"disabled",r&&"visible",i&&"active"),size:"md",variant:"secondary",fill:"text",icon:p,"aria-label":e,tooltip:o,tooltipPlacement:"right",onClick:u,disabled:t},m&&c().createElement(m,null))}function Ye(e){return{button:(0,n.css)({margin:0,color:e.colors.text.secondary,"&:hover":{color:e.colors.text.maxContrast,background:"transparent"},"&.disabled:hover":{color:e.colors.text.secondary},"&.visible":{color:e.colors.text.maxContrast},"&.active":{color:e.colors.text.maxContrast}})}}function Je(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const Xe=["filters-rule","filters-prefix","filters-suffix"];class Ze extends o.Bs{onActivate(){const e=this.initOtherMetricsVar();return this._subs.add(this.subscribeToEvent(Pe,e=>{const{key:t,values:r}=e.payload,{sectionValues:n}=this.state,a=new Map(n).set(t,r);this.setOtherMetricFilters(a),this.setState({sectionValues:a})})),()=>{e()}}setOtherMetricFilters(e){const t=o.jh.lookupVariable(p.hc,this);if(!(0,g.BE)(t))return;const r={"filters-rule":"rule group","filters-prefix":"prefix","filters-suffix":"suffix"},n=Array.from(e.entries()).reduce((e,[t,n])=>(n.length&&Xe.includes(t)&&e.push({key:t,operator:"=",value:n.join(", "),keyLabel:r[t]}),e),[]);t.setState({filters:n,hide:n.length?a.VariableHide.hideLabel:a.VariableHide.hideVariable})}initOtherMetricsVar(){const e=(0,d.kj)(this).state.$variables;if(!e)return()=>{};const t=new o.H9({name:p.hc,readOnly:!0,skipUrlSync:!0,datasource:null,hide:a.VariableHide.hideVariable,layout:"combobox",applyMode:"manual",allowCustomValue:!0});return e.setState({variables:[...e.state.variables,t]}),this.setOtherMetricFilters(this.state.sectionValues),()=>{e.setState({variables:[...e.state.variables.filter(e=>e!==t)]})}}static getSectionValuesFromUrl(){const e=new URLSearchParams(window.location.search),t=new Map;for(const r of Xe){const n=e.get(r);t.set(r,n?n.split(",").map(e=>e.trim()):[])}const r=e.get(`var-${N}`);return Boolean(r&&r!==w)&&t.set("groupby-labels",[r]),t}setActiveSection(e){const{visibleSection:t,sections:r}=this.state;if(!e||e===(null==t?void 0:t.state.key))return(0,u.z)("metrics_sidebar_toggled",{action:"closed",section:null==t?void 0:t.state.key}),void this.setState({visibleSection:null});var n;(0,u.z)("metrics_sidebar_toggled",{action:"opened",section:e}),"filters-prefix"===e?(0,u.z)("sidebar_prefix_filter_section_clicked",{}):"filters-suffix"===e&&(0,u.z)("sidebar_suffix_filter_section_clicked",{}),this.setState({visibleSection:null!==(n=r.find(t=>t.state.key===e))&&void 0!==n?n:null})}constructor(e){var t,r,n;const a=Ze.getSectionValuesFromUrl();super(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){Je(e,t,r[t])})}return e}({key:"sidebar",visibleSection:null,sections:[new Be({key:"filters-rule",type:"categories",title:"Rules filters",description:"Filter metrics and recording rules",icon:"rules",computeGroups:$e,showHideEmpty:!1,showSearch:!1,active:Boolean(null===(t=a.get("filters-rule"))||void 0===t?void 0:t.length)}),new Be({key:"filters-prefix",type:"prefixes",title:"Prefix filters",description:"Filter metrics based on their name prefix (Prometheus namespace)",icon:"A_",computeGroups:Me.w,active:Boolean(null===(r=a.get("filters-prefix"))||void 0===r?void 0:r.length)}),new Be({key:"filters-suffix",type:"suffixes",title:"Suffix filters",description:"Filter metrics based on their name suffix",icon:"_Z",computeGroups:Re,active:Boolean(null===(n=a.get("filters-suffix"))||void 0===n?void 0:n.length)}),new Ue({key:"groupby-labels",variableName:N,title:"Group by labels",description:"Group metrics by their label values",icon:"groups",active:a.has("groupby-labels")}),new He.O({key:"bookmarks",title:"Bookmarks",description:"Access your saved metrics for quick reference",icon:"star"}),new Ge({key:"settings",title:"Settings",description:"Settings",icon:"cog",disabled:!0})],sectionValues:a},e)),a.set("filters-rule",[]),this.addActivationHandler(this.onActivate.bind(this))}}function et(e){return{container:(0,n.css)({position:"relative",display:"flex",flexDirection:"row",height:"100%",overflow:"hidden"}),buttonsBar:(0,n.css)({display:"flex",flexDirection:"column",alignItems:"center",gap:0,width:"42px",padding:0,margin:0,boxSizing:"border-box",border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,backgroundColor:e.colors.background.primary,borderTopLeftRadius:0,borderBottomLeftRadius:0,position:"relative"}),buttonContainer:(0,n.css)({marginTop:e.spacing(1),"&::before":{transition:"0.5s ease",content:'""',position:"absolute",left:0,height:"32px",borderLeft:`2px solid ${e.colors.action.selectedBorder}`,boxSizing:"border-box",opacity:0,visibility:"hidden"},"&:hover::before":{opacity:1,visibility:"visible"},"&.visible::before":{opacity:1,visibility:"visible"},"&.disabled::before":{opacity:0,visibility:"hidden"},"&.active::after":{content:'""',position:"absolute",right:0,width:"8px",height:"8px",backgroundColor:e.colors.action.selectedBorder,borderRadius:"50%",margin:"2px 4px 0 0"}}),content:(0,n.css)({width:"calc(300px - 42px)",boxSizing:"border-box",border:`1px solid ${e.colors.border.weak}`,borderLeft:"none",borderRadius:e.shape.radius.default,backgroundColor:e.colors.background.canvas,padding:e.spacing(1.5)}),closeButton:(0,n.css)({position:"absolute",top:e.spacing(1.5),right:e.spacing(1),margin:0})}}function tt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}Je(Ze,"Component",({model:e})=>{const t=(0,s.useStyles2)(et),{sections:r,visibleSection:a,sectionValues:i}=e.useState();return c().createElement("div",{className:t.container},c().createElement("div",{className:t.buttonsBar,"data-testid":"sidebar-buttons"},r.map(r=>{var o,s;const{key:l,title:u,icon:d,disabled:p,active:m}=r.state,h=(null==a?void 0:a.state.key)===l,g=(null===(o=i.get(l))||void 0===o?void 0:o.length)?`${u}: ${null===(s=i.get(l))||void 0===s?void 0:s.join(", ")}`:u;return c().createElement("div",{key:l,className:(0,n.cx)(t.buttonContainer,h&&"visible",m&&"active",p&&"disabled")},c().createElement(Qe,{ariaLabel:u,disabled:p,visible:h,active:m,tooltip:g,onClick:()=>e.setActiveSection(l),iconOrText:d}))})),a&&c().createElement("div",{className:t.content,"data-testid":"sidebar-content"},c().createElement(s.IconButton,{className:t.closeButton,name:"times","aria-label":"Close",tooltip:"Close",tooltipPlacement:"top",onClick:()=>e.setActiveSection("")}),a instanceof Be&&c().createElement(a.Component,{model:a}),a instanceof Ue&&c().createElement(a.Component,{model:a}),a instanceof He.O&&c().createElement(a.Component,{model:a}),a instanceof Ge&&c().createElement(a.Component,{model:a})))});class rt extends o.Bs{onActivate(){const e=o.jh.lookupVariable(N,this).state.value;this.updateBasedOnGroupBy(e),this.subscribeToEvents()}updateBasedOnGroupBy(e){const t=Boolean(e&&e!==w);o.jh.findByKeyAndType(this,"quick-search",Y.I).toggleCountsDisplay(!t),this.setState({body:t?new G({labelName:e}):new A.Qs({variableName:oe.h})})}subscribeToEvents(){this.initVariablesFilteringAndSorting(),this._subs.add(this.subscribeToEvent(he,e=>{this.openDrawer(e.payload.metricName)})),this._subs.add(this.subscribeToEvent(ue,()=>{this.state.drawer.close()})),this._subs.add(this.subscribeToEvent(p.OO,e=>{void 0!==e.payload&&(0,W.VN)(e.payload)}))}initVariablesFilteringAndSorting(){this._subs.add(this.subscribeToEvent(ne.x,e=>{const{key:t}=e.payload,r=o.jh.findByKey(this,t);this.state.enginesMap.set(t,{filterEngine:new le.k(r),sortEngine:new ce.c(r)})})),this._subs.add(this.subscribeToEvent(ae.e,e=>{this.state.enginesMap.delete(e.payload.key)}));const e=o.jh.findByKeyAndType(this,"quick-search",Y.I),t=o.jh.findAllObjects(this,e=>e instanceof Be),r=o.jh.findByKeyAndType(this,"metrics-sorter",W.fD).state.$variables.getByName(W.NJ);this._subs.add(this.subscribeToEvent(ie.x,n=>{const{key:a,options:i}=n.payload,{filterEngine:o,sortEngine:s}=this.state.enginesMap.get(a);o.setInitOptions(i);const l={names:e.state.value?[e.state.value]:[]};for(const e of t)l[e.state.type]=e.state.selectedGroups.map(e=>e.value);o.applyFilters(l,{forceUpdate:!0,notify:!1}),s.sort(r.state.value)})),this._subs.add(this.subscribeToEvent(re.W,e=>{const{searchText:t}=e.payload;for(const[,{filterEngine:e,sortEngine:n}]of this.state.enginesMap)e.applyFilters({names:t?[t]:[]}),n.sort(r.state.value)})),this._subs.add(this.subscribeToEvent(Oe.Y,e=>{const{type:t,filters:n}=e.payload;for(const[,{filterEngine:e,sortEngine:a}]of this.state.enginesMap)e.applyFilters({[t]:n}),a.sort(r.state.value)})),this._subs.add(this.subscribeToEvent(te.S,e=>{const{sortBy:t}=e.payload;(0,u.z)("sorting_changed",{from:"metrics-reducer",sortBy:t});for(const[,{sortEngine:e}]of this.state.enginesMap)e.sort(t)}))}openDrawer(e){const t=(0,d.kj)(this);this.state.drawer.open({title:"Choose a new Prometheus function",subTitle:e,body:new o.gF({templateColumns:A.MV,autoRows:B.Rg,isLazy:!0,$behaviors:[new o.Gg.K2({key:"metricCrosshairSync",sync:a.DashboardCursorSync.Crosshair})],children:fe.PROMETHEUS_FN_OPTIONS.map((r,n)=>new o.xK({body:new B.yG({title:r.label,metricName:e,color:(0,d.Vy)(n),prometheusFunction:r.value,height:B.Rg,hideLegend:!0,highlight:1===n,isNativeHistogram:t.isNativeHistogram(e),headerActions:[new pe({metricName:e,prometheusFunction:r.value,disabled:1===n})]})}))})})}constructor(){super({$variables:new o.Pj({variables:[new se.s,new oe.V,new L]}),listControls:new Z({}),sidebar:new Ze({}),body:new A.Qs({variableName:oe.h}),drawer:new Se({}),enginesMap:new Map}),tt(this,"_variableDependency",new o.Sh(this,{variableNames:[N],onReferencedVariableValueChanged:e=>{this.updateBasedOnGroupBy(e.state.value)}})),function(e){try{for(const t of e)(0,o.pY)({dataSource:t})}catch(e){const{message:t}=e;/A runtime data source with uid (.+) has already been registered/.test(t)||(0,f.jx)(e,["Fail to register all the runtime data sources!","The application cannot work as expected, please try reloading the page or if the problem persists, contact your organization admin."])}}([new S,new V]),this.addActivationHandler(this.onActivate.bind(this))}}tt(rt,"Component",({model:e})=>{var t;const r=null!==(t=(0,i.useChromeHeaderHeight)())&&void 0!==t?t:0,n=(0,s.useStyles2)(at,r),{$variables:a,body:o,listControls:l,drawer:u,sidebar:d}=e.useState();return c().createElement(c().Fragment,null,c().createElement("div",{className:n.listControls,"data-testid":"list-controls"},c().createElement(l.Component,{model:l})),c().createElement("div",{className:n.body},c().createElement("div",{className:n.sidebar,"data-testid":"sidebar"},c().createElement(d.Component,{model:d})),c().createElement("div",{className:n.list},c().createElement(o.Component,{model:o}))),c().createElement("div",{className:n.variables},null==a?void 0:a.state.variables.map(e=>c().createElement(e.Component,{key:e.state.name,model:e}))),c().createElement(u.Component,{model:u}))});const nt=144;function at(e,t){return{listControls:(0,n.css)({marginBottom:e.spacing(1.5)}),body:(0,n.css)({display:"flex",flexDirection:"row",gap:e.spacing(1),height:`calc(100vh - ${t+nt}px)`}),list:(0,n.css)({width:"100%",overflowY:"auto"}),sidebar:(0,n.css)({flex:"0 0 auto",overflowY:"auto"}),variables:(0,n.css)({display:"none"})}}},8534:(e,t,r)=>{r.d(t,{B:()=>c});var n=r(7985),a=r(2007),i=r(5959),o=r.n(i),s=r(2127);function l(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class c extends n.Bs{constructor({metricName:e,variant:t,fill:r}){super({key:`select-action-${e}`,metricName:e,variant:t||"primary",fill:r||"outline"}),l(this,"onClick",()=>{this.publishEvent(new s.OO(this.state.metricName),!0)})}}l(c,"Component",({model:e})=>{const{variant:t,fill:r}=e.useState();return o().createElement(a.Button,{variant:t,fill:r,size:"sm",onClick:e.onClick,"data-testid":`select-action-${e.state.metricName}`},"Select")})},8587:(e,t,r)=>{r.d(t,{H:()=>s});var n=r(8162),a=r(6467),i=r(2062),o=r(7977);function s(e){const{metric:t,matchers:r,groupBy:s,queryResolution:l,addIgnoreUsageFilter:c}=e,u=(0,i.d)({metric:t,matchers:r,addIgnoreUsageFilter:c});let d,p,m=(0,i.l)(u);const h=(0,o.X)(t);return h?(m=n.GH.rate({expr:m,interval:"$__rate_interval"}),d=n.GH.sum({expr:m,by:s?[s]:void 0}),p="sum(rate)"):(d=n.GH.avg({expr:m,by:s?[s]:void 0}),p="avg"),{fnName:p,isRateQuery:h,maxDataPoints:l===a.IK.HIGH?500:250,queries:[{refId:s?`${t}-by-${s}`:t,expr:d,legendFormat:s?`{{${s}}}`:void 0,fromExploreMetrics:!0}]}}},8628:(e,t,r)=>{r.d(t,{w:()=>i});var n=r(4964);const a="<none>";function i(e){const t=new Map;for(const n of e){const e=n.value.split(/[^a-z0-9]/i),i=e.length<=1?n.value:e[0];var r;const o=null!==(r=t.get(i))&&void 0!==r?r:[];o.push(n.value),t.set(i||a,o)}const i=new Map;for(const[e,r]of t)i.set(e,r.length);return Array.from(i.entries()).sort((e,t)=>e[1]!==t[1]?t[1]-e[1]:(0,n._)(e[0],t[0])).map(([e,t])=>({value:e,count:t,label:e}))}},8732:(e,t,r)=>{r.d(t,{A:()=>l,S:()=>u});var n=r(5959),a=r.n(n),i=r(1159),o=r(4137),s=r(2745);const l=(0,n.lazy)(()=>r.e(78).then(r.bind(r,9078))),c=()=>{const e=(0,i.useLocation)();return a().createElement(i.Navigate,{to:`${o.bw.Drilldown}${e.search}`,replace:!0})},u=()=>{const{trail:e}=(0,n.useContext)(s.J);return a().createElement(i.Routes,null,a().createElement(i.Route,{path:o.bw.Drilldown,element:a().createElement(l,{trail:e})}),a().createElement(i.Route,{path:o.bw.Trail,element:a().createElement(c,null)}),a().createElement(i.Route,{path:"*",element:a().createElement(i.Navigate,{to:o.bw.Drilldown,replace:!0})}))}},9585:(e,t,r)=>{r.d(t,{V:()=>s,h:()=>o});var n=r(7985),a=r(5568),i=r(1464);const o="filtered-metrics-wingman";class s extends n.yP{onActivate(){const e=n.jh.findByKeyAndType(this,a.$,a.s),{loading:t,error:r,options:i}=e.state;this.setState({loading:t,error:r,options:i}),this._subs.add(e.subscribeToState(e=>{this.setState({loading:e.loading,error:e.error,options:e.options})}))}constructor(){return super({key:o,name:o,label:"Filtered Metrics",loading:!1,error:null,options:[],includeAll:!0,value:"$__all",skipUrlSync:!0}),this.addActivationHandler(this.onActivate.bind(this)),(0,i.I)(this)}}},9966:(e,t,r)=>{r.d(t,{s:()=>l});var n=r(7985),a=r(9585),i=r(1199),o=r(5568),s=r(3422);class l extends s.I{onActivate(){const e=n.jh.lookupVariable(o.$,this),t=n.jh.lookupVariable(a.h,this);this.setInitCounts(e,t),this._subs.add(e.subscribeToState((e,r)=>{(0,i.B)(e.options,r.options)||this.setState({counts:{current:t.state.options.length,total:e.options.length}})})),this._subs.add(t.subscribeToState((t,r)=>{t.loading||r.loading||(0,i.B)(t.options,r.options)||this.setState({counts:{current:t.options.length,total:e.state.options.length}})}))}setInitCounts(e,t){const r={current:0,total:0};!e.state.loading&&e.state.options.length&&(r.total=e.state.options.length),!t.state.loading&&t.state.options.length&&(r.current=t.state.options.length),this.setState({counts:r})}constructor(){super({key:"MetricVariableCountsProvider"}),this.addActivationHandler(this.onActivate.bind(this))}}}}]);
//# sourceMappingURL=836.js.map?_cache=6db109af7f73f8074619