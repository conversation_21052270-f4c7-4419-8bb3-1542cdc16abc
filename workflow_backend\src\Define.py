from enum import IntEnum


class ProcessStatus(IntEnum):
    QUEUED = 0
    RUNNING = 1
    FINISHED = 2
    FAILED = 3
    CANCELED = 4
    ASSIGNED = 5

class Step(IntEnum):
    LAGER_DB = 0
    IMAGE_STATION = 1
    FOCUS_AI = 3
    XWAYS_DU = 2
    MANUAL_ANALYSIS = 4

NEXT_STEP_AFTER = {
        # Step.IMAGE_STATION: Step.FOCUS_AI,  # After ImageStation, queue  FOCUS.AI
        # Step.FOCUS_AI: Step.XWAYS_DU  # After FOCUS.AI, queue XwaysDÜ
        # temporary:
        Step.IMAGE_STATION: Step.XWAYS_DU,  # After ImageStation, queue XwaysDÜ
}

class CaseProgressScore(IntEnum):
    QUEUED = 1
    ASSIGNED = 2
    RUNNING = 3
    FINISHED = 4
    FAILED = 0
    CANCELLED = 0

def get_case_progress_score(status: ProcessStatus) -> CaseProgressScore | None:
    if status == ProcessStatus.QUEUED:
        return CaseProgressScore.QUEUED
    elif status == ProcessStatus.RUNNING:
        return CaseProgressScore.RUNNING
    elif status == ProcessStatus.FINISHED:
        return CaseProgressScore.FINISHED
    elif status == ProcessStatus.FAILED:
        return CaseProgressScore.FAILED
    elif status == ProcessStatus.CANCELED:
        return CaseProgressScore.CANCELLED
    elif status == ProcessStatus.ASSIGNED:
        return CaseProgressScore.ASSIGNED
    else:
        return None