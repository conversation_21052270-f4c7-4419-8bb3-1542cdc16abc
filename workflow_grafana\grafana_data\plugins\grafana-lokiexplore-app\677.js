"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[677],{677:(e,t,r)=>{r.r(t),r.d(t,{VARIABLE_NAMESPACE:()=>O,buildLogsExplorationFromState:()=>y,default:()=>g});var n=r(5959),o=r.n(n),a=r(6865),l=r(8469),u=r(8714),c=r(1296),i=r(3257),s=r(2152);function p(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function b(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}function y(e){var{onTimeRangeChange:t,query:r,timeRangeState:n}=e,o=b(e,["onTimeRangeChange","query","timeRangeState"]);const l=new a.JZ(n);if(l.subscribeToState(e=>{t&&t(e.value)}),!r)return console.error("No query parameter found! Please pass in a valid logQL query string when embedding Logs Drilldown."),null;(0,c.default)();const{labelFilters:s,lineFilters:y}=(0,i.BW)(r),O=s.map(e=>({key:e.key,operator:e.operator,value:e.value}));return new u.P(f(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){p(e,t,r[t])})}return e}({},o),{$timeRange:l,defaultLineFilters:y,embedded:!0,readOnlyLabelFilters:O}))}const O="ld";function g(e){const[t,r]=(0,n.useState)(null);return(0,n.useEffect)(()=>{t||((0,s.rX)(),r(y(e)))},[t,e]),t?o().createElement(a.$L,{scene:t,updateUrlOnInit:!1,createBrowserHistorySteps:!0,namespace:O,excludeFromNamespace:["from","to","timezone",l.o,l.Z]},o().createElement(t.Component,{model:t})):null}}}]);
//# sourceMappingURL=677.js.map?_cache=4abf8fc642aec3399b73