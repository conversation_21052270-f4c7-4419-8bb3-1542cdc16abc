"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[675],{8675:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Be});var l=n(5959),r=n.n(l),o=n(7781),a=n(2165);const i={addFilter:e=>{},logsFrame:null,selectedLine:void 0,timeRange:void 0},c=(0,l.createContext)(i),s=({addFilter:e,children:t,logsFrame:n,selectedLine:l,timeRange:o})=>r().createElement(c.Provider,{value:{addFilter:e,logsFrame:n,selectedLine:l,timeRange:o}},t),d=()=>(0,l.useContext)(c);var u=n(6089),m=n(5540),p=n(8544),g=n(3241),f=n(3929),b=n(3367),v=n(1269),h=n(8531),y=n(1625),x=n(2007),w=n(3571),C=n(5953),E=n(9193);function k({collapseButtonClassName:e,isTableSidebarCollapsed:t,onToggleTableSidebarCollapse:n,searchValue:l,setSearchValue:o}){const{columns:a,setFilteredColumns:i}=(0,p.lI)(),c=e=>{const t=e[0];let n={},l=0;var r;t.forEach(e=>{e in a&&(n[e]=a[e],l++)}),i(n),r=l,(0,h.reportInteraction)("grafana_logs_app_table_text_search_result_count",{resultCount:r})},s=function(e){return{collapseTableSidebarButton:(0,u.css)({position:"absolute",right:e.spacing(.2),top:e.spacing(1)})}}((0,x.useTheme2)());return r().createElement(r().Fragment,null,r().createElement(x.IconButton,{className:e||s.collapseTableSidebarButton,onClick:n,name:t?"angle-right":"angle-left",tooltip:t?"Expand sidebar":"Collapse sidebar",size:"sm"}),!t&&r().createElement(x.Field,null,r().createElement(x.Input,{value:l,type:"text",placeholder:"Search fields by name",onChange:e=>{var t;const n=null===(t=e.currentTarget)||void 0===t?void 0:t.value;var l;o(n),n?(l=n,(0,E.E)(Object.keys(a),l,c)):i(void 0)}})))}var O=n(5755);function I(){const e=function(e){return{empty:(0,u.css)({fontSize:e.typography.fontSize,marginBottom:e.spacing(2),marginLeft:e.spacing(1.75)})}}((0,x.useTheme2)());return r().createElement("div",{className:e.empty},"No fields")}function S(e){const t=function(e){return{checkboxLabel:(0,u.css)({"> span":{display:"block",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}}),contentWrap:(0,u.css)({alignItems:"center",display:"flex",justifyContent:"space-between",width:"100%"}),customWidthWrap:(0,u.css)({cursor:"pointer",fontSize:e.typography.bodySmall.fontSize}),dragIcon:(0,u.css)({cursor:"drag",marginLeft:e.spacing(1),opacity:.4}),labelCount:(0,u.css)({alignItems:"self-end",appearance:"none",background:"none",border:"none",display:"flex",flexDirection:"column",fontSize:e.typography.pxToRem(11),marginLeft:e.spacing(.5),marginRight:e.spacing(.5),opacity:.6}),valueCount:(0,u.css)({textWrap:"nowrap"})}}((0,x.useTheme2)());var n,l,o,a,i;return e.labels[e.label]?r().createElement(r().Fragment,null,r().createElement("div",{className:t.contentWrap},r().createElement(x.Checkbox,{className:t.checkboxLabel,label:e.label,onChange:e.onChange,checked:null!==(i=null===(n=e.labels[e.label])||void 0===n?void 0:n.active)&&void 0!==i&&i}),e.showCount&&r().createElement("div",{className:t.labelCount},r().createElement("div",null,null===(l=e.labels[e.label])||void 0===l?void 0:l.percentOfLinesWithLabel,"%"),r().createElement("div",{className:t.valueCount},null===(o=e.labels[e.label])||void 0===o?void 0:o.cardinality," ",1===(null===(a=e.labels[e.label])||void 0===a?void 0:a.cardinality)?"value":"values"))),e.draggable&&r().createElement(x.Icon,{"aria-label":"Drag and drop icon",title:"Drag and drop to reorder",name:"draggabledots",size:"lg",className:t.dragIcon})):null}function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function F(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},l=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),l.forEach(function(t){L(e,t,n[t])})}return e}function j(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,l)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const N=e=>{const{columnWidthMap:t,setColumnWidthMap:n}=(0,p.lI)(),{labels:l,reorderColumn:o,toggleColumn:a,valueFilter:i}=e,c=(0,x.useTheme2)(),{columns:s}=(0,p.lI)(),d=function(e){return{columnWrapper:(0,u.css)({marginBottom:e.spacing(1.5),paddingLeft:e.spacing(.5)}),dragging:(0,u.css)({background:e.colors.background.secondary}),wrap:(0,u.css)({background:e.colors.background.primary,display:"flex",marginBottom:e.spacing(1),marginTop:e.spacing(1)})}}(c),m=Object.keys(l).filter(e=>i(e)),g=e=>{e.destination&&o(s,e.source.index,e.destination.index)},f=e=>{const t=l[e];if(t)return`${e} appears in ${null==t?void 0:t.percentOfLinesWithLabel}% of log lines`};return m.length?r().createElement(O.JY,{onDragEnd:g},r().createElement(O.gL,{droppableId:"order-fields",direction:"vertical"},e=>r().createElement("div",j(F({className:d.columnWrapper},e.droppableProps),{ref:e.innerRef}),m.sort(function(e){return(t,n)=>{const l=e[t],r=e[n];return null!=l.index&&null!=r.index?l.index-r.index:0}}(l)).map((e,o)=>r().createElement(O.sx,{draggableId:e,key:e,index:o},(o,i)=>r().createElement("div",j(F({className:(0,u.cx)(d.wrap,i.isDragging?d.dragging:void 0),ref:o.innerRef},o.draggableProps,o.dragHandleProps),{title:f(e)}),r().createElement(S,{setColumnWidthMap:n,columnWidthMap:t,label:e,onChange:()=>a(e),labels:l,draggable:!0})))),e.placeholder))):r().createElement(I,null)},T=new Intl.Collator(void 0,{sensitivity:"base"});const W=e=>{const{labels:t,toggleColumn:n,valueFilter:l}=e,o=function(e){return{columnWrapper:(0,u.css)({marginBottom:e.spacing(1.5),paddingLeft:e.spacing(.5)}),dragging:(0,u.css)({background:e.colors.background.secondary}),wrap:(0,u.css)({background:e.colors.background.primary,borderBottom:`1px solid ${e.colors.background.canvas}`,display:"flex",marginBottom:e.spacing(.25),marginTop:e.spacing(.25)})}}((0,x.useTheme2)()),a=Object.keys(t).filter(e=>l(e));return a.length?r().createElement("div",{className:o.columnWrapper},a.sort(function(e){return(t,n)=>{const l=e[t],r=e[n];return null!=l&&null!=r?Number("TIME_FIELD"===r.type)-Number("TIME_FIELD"===l.type)||Number("BODY_FIELD"===r.type)-Number("BODY_FIELD"===l.type)||T.compare(t,n):0}}(t)).map(e=>{var l;return r().createElement("div",{key:e,className:o.wrap,title:`${e} appears in ${null===(l=t[e])||void 0===l?void 0:l.percentOfLinesWithLabel}% of log lines`},r().createElement(S,{showCount:!0,label:e,onChange:()=>n(e),labels:t}))})):r().createElement(I,null)};const P=e=>{const t=function(e){return{columnHeader:(0,u.css)({background:e.colors.background.secondary,display:"flex",fontSize:e.typography.h6.fontSize,justifyContent:"space-between",left:0,marginBottom:e.spacing(2),paddingBottom:e.spacing(.75),paddingLeft:e.spacing(1.5),paddingRight:e.spacing(.75),paddingTop:e.spacing(.75),position:"sticky",top:0,zIndex:3}),columnHeaderButton:(0,u.css)({appearance:"none",background:"none",border:"none",fontSize:e.typography.pxToRem(11)}),sidebarWrap:(0,u.css)({"&::-webkit-scrollbar":{display:"none"},height:"calc(100% - 50px)",overflowY:"scroll",scrollbarWidth:"none"})}}((0,x.useTheme2)());var n,l;return r().createElement("div",{className:t.sidebarWrap},r().createElement(r().Fragment,null,r().createElement("div",{className:t.columnHeader},"Selected fields",r().createElement("button",{onClick:e.clear,className:t.columnHeaderButton},"Reset")),r().createElement(N,{reorderColumn:e.reorderColumn,toggleColumn:e.toggleColumn,labels:null!==(n=e.filteredColumnsWithMeta)&&void 0!==n?n:e.columnsWithMeta,valueFilter:t=>{var n,l;return null!==(l=null===(n=e.columnsWithMeta[t])||void 0===n?void 0:n.active)&&void 0!==l&&l},id:"selected-fields"}),r().createElement("div",{className:t.columnHeader},"Fields"),r().createElement(W,{toggleColumn:e.toggleColumn,labels:null!==(l=e.filteredColumnsWithMeta)&&void 0!==l?l:e.columnsWithMeta,valueFilter:t=>{var n;return!(null===(n=e.columnsWithMeta[t])||void 0===n?void 0:n.active)}})))};function M(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},l=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),l.forEach(function(t){M(e,t,n[t])})}return e}function R(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,l)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function B(e){return(t,n,l)=>{if(n===l)return;const r=D({},t),o=Object.keys(r).filter(e=>r[e].active).map(e=>{var t;return{fieldName:e,index:null!==(t=r[e].index)&&void 0!==t?t:0}}).sort((e,t)=>e.index-t.index),[a]=o.splice(n,1);o.splice(l,0,a),o.filter(e=>void 0!==e).forEach((e,t)=>{r[e.fieldName].index=t}),e(r)}}function z(e){const{columns:t,filteredColumns:n,setColumns:o,setFilteredColumns:a}=(0,p.lI)(),[i,c]=(0,l.useState)(""),s=B(o);return r().createElement(r().Fragment,null,r().createElement(k,{isTableSidebarCollapsed:e.isTableSidebarCollapsed,onToggleTableSidebarCollapse:e.onToggleTableSidebarCollapse,searchValue:i,setSearchValue:c}),!e.isTableSidebarCollapsed&&r().createElement(P,{toggleColumn:e=>{if(!t||!(e in t))return void function(e,t){let n;try{n={columnName:e,columns:JSON.stringify(t)}}catch(t){n={columnName:e,msg:"Table: ColumnSelectionDrawerWrap failed to encode context"}}C.v.warn("failed to get column",n)}(e,t);const l=Object.keys(t).filter(e=>t[e].active).length,r=!t[e].active||void 0;let i;if(i=R(D({},t),r?{[e]:R(D({},t[e]),{active:r,index:l})}:{[e]:R(D({},t[e]),{active:!1,index:void 0})}),function(e){if(t){var n,l;const r=!(null===(n=t[e])||void 0===n?void 0:n.active),o=null===(l=Object.keys(t).filter(e=>{var n;return null===(n=t[e])||void 0===n?void 0:n.active}))||void 0===l?void 0:l.length,a={columnAction:r?"add":"remove",columnCount:r?o+1:o-1};(0,h.reportInteraction)("grafana_logs_app_table_column_filter_clicked",a)}}(e),o(i),n){var s;const t=!(null===(s=n[e])||void 0===s?void 0:s.active);let r;r=R(D({},n),t?{[e]:R(D({},n[e]),{active:t,index:l})}:{[e]:R(D({},n[e]),{active:!1,index:void 0})}),a(r),c("")}},filteredColumnsWithMeta:n,columnsWithMeta:t,clear:()=>{const e=D({},t);let n=0;Object.keys(e).forEach(t=>{const l="BODY_FIELD"===e[t].type||"TIME_FIELD"===e[t].type;e[t].active=l,e[t].index=l?n++:void 0}),o(e),a(e),c("")},reorderColumn:s}))}const H=(0,l.createContext)({cellIndex:{index:null,numberOfMenuItems:3},setActiveCellIndex:e=>!1}),V=({children:e})=>{const[t,n]=(0,l.useState)({index:null}),o=(0,l.useCallback)(e=>{n(e)},[]);return r().createElement(H.Provider,{value:{cellIndex:t,setActiveCellIndex:o}},e)},$=()=>(0,l.useContext)(H),_=(0,l.createContext)({isHeaderMenuActive:!1,setHeaderMenuActive:e=>!1}),A=({children:e})=>{const[t,n]=(0,l.useState)(!1),o=(0,l.useCallback)(e=>{n(e)},[]);return r().createElement(_.Provider,{value:{isHeaderMenuActive:t,setHeaderMenuActive:o}},e)},Z=e=>r().createElement(U,{onMouseOut:e.onMouseOut,onMouseIn:e.onMouseIn,onClick:e.onClick,field:e.field,rowIndex:e.rowIndex},e.children),U=e=>{var t;const n=(0,x.useTheme2)(),l=$(),o=((e,t)=>({active:(0,u.css)({background:"transparent",height:"calc(100% + 36px)",zIndex:e.zIndex.tooltip}),wrap:(0,u.css)({background:null!=t?t:"transparent",height:"100%",left:0,margin:"auto",overflowX:"hidden",position:"absolute",top:0,whiteSpace:"nowrap",width:"100%"})}))(n,void 0,null===(t=l.cellIndex)||void 0===t||t.numberOfMenuItems);return r().createElement("div",{onMouseLeave:e.onMouseOut,onMouseEnter:e.onMouseIn,onClick:e.onClick,className:l.cellIndex.index===e.rowIndex&&l.cellIndex.fieldName===e.field.name?(0,u.cx)(o.wrap,o.active):o.wrap,onKeyDown:t=>{var n;"Enter"!==t.key&&" "!==t.key||(null===(n=e.onClick)||void 0===n||n.call(e))},role:"button",tabIndex:0},e.children)};var K=n(5091),Y=n(6854);const q=e=>{const t=((e,t)=>({menu:(0,u.css)({display:"flex",justifyContent:"flex-start",minWidth:"60px",paddingRight:"5px",position:"relative"}),menuItem:(0,u.css)({alignItems:"center",cursor:"pointer",display:"flex",overflow:"auto",paddingLeft:"5px",paddingRight:"5px",textOverflow:"ellipsis"}),menuItemsWrap:(0,u.css)({background:e.colors.background.secondary,boxShadow:e.shadows.z3,display:"flex",marginLeft:"column"===t?"5px":void 0,padding:"5px 0"})}))((0,x.useTheme2)(),e.pillType),{addFilter:n}=d();return r().createElement("span",{className:t.menu},r().createElement("span",{className:t.menuItemsWrap},"derived"!==e.fieldType&&r().createElement(r().Fragment,null,r().createElement("div",{className:t.menuItem,role:"button",tabIndex:0,onClick:()=>{n({key:e.label,operator:Y.w7.Equal,value:e.value})},onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||n({key:e.label,operator:Y.w7.Equal,value:e.value})}},r().createElement(x.Icon,{title:"Add to search",size:"md",name:"plus-circle"})),r().createElement("div",{className:t.menuItem,role:"button",tabIndex:0,onClick:()=>{n({key:e.label,operator:Y.w7.NotEqual,value:e.value})},onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||n({key:e.label,operator:Y.w7.NotEqual,value:e.value})}},r().createElement(x.Icon,{title:"Exclude from search",size:"md",name:"minus-circle"}))),e.showColumn&&r().createElement("div",{title:"Add column",role:"button",tabIndex:0,className:t.menuItem,onClick:e.showColumn,onKeyDown:t=>{var n;"Enter"!==t.key&&" "!==t.key||(null===(n=e.showColumn)||void 0===n||n.call(e))}},r().createElement("svg",{width:"18",height:"16",viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},r().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.38725 1.33301H13.3872C13.5641 1.33301 13.7336 1.40325 13.8587 1.52827C13.9837 1.65329 14.0539 1.82286 14.0539 1.99967V2.33333C14.0539 2.70152 13.7554 3 13.3872 3H13.0542C12.87 3 12.7206 2.85062 12.7206 2.66634H8.05391V13.333H12.7206C12.7206 13.1491 12.8697 13 13.0536 13H13.3872C13.7554 13 14.0539 13.2985 14.0539 13.6667V13.9997C14.0539 14.1765 13.9837 14.3461 13.8587 14.4711C13.7336 14.5961 13.5641 14.6663 13.3872 14.6663H1.38725C1.21044 14.6663 1.04087 14.5961 0.915843 14.4711C0.790819 14.3461 0.720581 14.1765 0.720581 13.9997V1.99967C0.720581 1.82286 0.790819 1.65329 0.915843 1.52827C1.04087 1.40325 1.21044 1.33301 1.38725 1.33301ZM2.05391 13.333H6.72058V2.66634H2.05391V13.333Z",fill:"#CCCCDC",fillOpacity:"1"}),r().createElement("path",{d:"M13.8538 7.19999H16.2538C16.466 7.19999 16.6695 7.28429 16.8195 7.4343C16.9696 7.58432 17.0538 7.78783 17.0538 7.99999C17.0538 8.21214 16.9696 8.41566 16.8195 8.56567C16.6695 8.71569 16.466 8.79999 16.2538 8.79999H13.8538V11.2C13.8538 11.4121 13.7696 11.6156 13.6195 11.7657C13.4695 11.9157 13.266 12 13.0538 12C12.8416 12 12.6382 11.9157 12.4881 11.7657C12.3381 11.6156 12.2538 11.4121 12.2538 11.2V8.79999H9.85384C9.64165 8.79999 9.43819 8.71569 9.28815 8.56567C9.13811 8.41566 9.05383 8.21214 9.05383 7.99999C9.05383 7.78783 9.13811 7.58432 9.28815 7.4343C9.43819 7.28429 9.64165 7.19999 9.85384 7.19999H12.2538V4.8C12.2538 4.58784 12.3381 4.38433 12.4881 4.23431C12.6382 4.0843 12.8416 4 13.0538 4C13.266 4 13.4695 4.0843 13.6195 4.23431C13.7696 4.38433 13.8538 4.58784 13.8538 4.8V7.19999Z",fill:"#CCCCDC",fillOpacity:"1"}))),e.links&&e.links.map(e=>{var n;return r().createElement("div",{className:t.menuItem,role:"button",tabIndex:0,onClick:()=>{window.open(e.href,"_blank")},onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||window.open(e.href,"_blank")},key:e.href},r().createElement(x.Icon,{title:null!==(n=e.title)&&void 0!==n?n:"Link",key:e.href,size:"md",name:"link"}))})))},J=e=>{const{label:t,value:n}=e,l=(0,x.useTheme2)(),{cellIndex:a}=$();let i;if(t===K.R){const e=je().options;"string"==typeof n&&n in e&&(i=e[n].color)}const c=a.index===e.rowIndex&&e.field.name===a.fieldName,s=((e,t)=>({activePillWrap:(0,u.css)({}),menu:(0,u.css)({width:"100%"}),menuItem:(0,u.css)({overflow:"auto",textOverflow:"ellipsis"}),menuItemText:(0,u.css)({display:"inline-block",width:"65px"}),pill:(0,u.css)({"&:before":{backgroundColor:t,content:'""',height:"100%",left:0,position:"absolute",top:0,width:`${e.spacing(.25)}`},"&:hover":{border:`1px solid ${e.colors.border.strong}`},backgroundColor:"transparent",border:`1px solid ${e.colors.border.weak}`,display:"inline-flex",flexDirection:"row-reverse",marginLeft:"5px",marginRight:"5px",marginTop:"4px",padding:"2px 5px",paddingLeft:t?`${e.spacing(.75)}`:"2px",position:"relative"}),pillWrap:(0,u.css)({width:"100%"})}))(l,i);return r().createElement("div",{className:(0,u.cx)(s.pillWrap,c?s.activePillWrap:void 0)},!!n&&r().createElement(r().Fragment,null,r().createElement("span",{className:s.pill},r().createElement(r().Fragment,null,n)),c&&"string"==typeof n&&e.field.type!==o.FieldType.time&&r().createElement(q,{label:e.label,value:n,pillType:"column"})))};var X=n(5002);function G(e){var t;const n="string"==typeof e.value&&!isNaN(Number(e.value)),o=((e,t)=>({clipboardButton:(0,u.css)({height:"100%",lineHeight:"1",padding:0,width:"20px"}),iconWrapper:(0,u.css)({background:e.colors.background.secondary,boxShadow:e.shadows.z2,display:"flex",flexDirection:t?"row-reverse":"row",height:"35px",left:0,padding:`0 ${e.spacing(.5)}`,position:t?"absolute":"sticky",zIndex:1}),inspect:(0,u.css)({"& button svg":{marginRight:t?"0":"auto"},"&:hover":{color:e.colors.text.link,cursor:"pointer"},padding:"5px 3px"}),inspectButton:(0,u.css)({borderRadius:"5px",display:"inline-flex",margin:0,overflow:"hidden",verticalAlign:"middle"})}))((0,x.useTheme2)(),n),{logsFrame:a,timeRange:i}=d(),c=null==a||null===(t=a.idField)||void 0===t?void 0:t.values[e.rowIndex],s=null==a?void 0:a.bodyField.values[e.rowIndex],[m,p]=(0,l.useState)(!1),g=(0,l.useCallback)(()=>i?(0,X.gW)("selectedLine",{id:c,row:e.rowIndex},i):"",[c,e.rowIndex,i]);return r().createElement(r().Fragment,null,r().createElement("div",{className:o.iconWrapper},r().createElement("div",{className:o.inspect},r().createElement(x.IconButton,{"data-testid":w.b.table.inspectLine,className:o.inspectButton,tooltip:"View log line",variant:"secondary","aria-label":"View log line",tooltipPlacement:"top",size:"md",name:"eye",onClick:()=>p(!0),tabIndex:0})),r().createElement("div",{className:o.inspect},r().createElement(x.ClipboardButton,{className:o.clipboardButton,icon:"share-alt",variant:"secondary",fill:"text",size:"md",tooltip:"Copy link to log line",tooltipPlacement:"top",tabIndex:0,getText:g}))),r().createElement(r().Fragment,null,m&&r().createElement(x.Modal,{onDismiss:()=>p(!1),isOpen:!0,title:"Inspect value"},r().createElement("pre",null,s),r().createElement(x.Modal.ButtonRow,null,r().createElement(x.ClipboardButton,{icon:"copy",getText:()=>e.value},"Copy to Clipboard")))))}const Q=e=>{var t;let n=e.value;const l=e.field,a=l.display(n),i=(e=>({content:(0,u.css)({display:"flex",height:"100%",overflow:"hidden",position:"relative"}),flexWrap:(0,u.css)({alignItems:"flex-start",display:"flex"}),linkWrapper:(0,u.css)({"&:hover":{textDecoration:"underline"},color:e.colors.text.link,marginLeft:"7px",marginTop:"7px"})}))((0,x.useTheme2)(),e.field.type),{cellIndex:c,setActiveCellIndex:s}=$(),d={index:e.rowIndex},m=Boolean(null===(t=(0,x.getCellLinks)(e.field,d))||void 0===t?void 0:t.length);if(null===n)return r().createElement(r().Fragment,null);n=r().isValidElement(e.value)?e.value:"object"==typeof n?JSON.stringify(e.value):(0,o.formattedValueToString)(a);return r().createElement(Z,{onClick:()=>e.rowIndex===c.index&&e.field.name===c.fieldName?s({index:null}):s({fieldName:e.field.name,index:e.rowIndex,numberOfMenuItems:3}),field:e.field,rowIndex:e.rowIndex},r().createElement("div",{className:i.content},0===e.fieldIndex&&r().createElement(G,{value:n,rowIndex:e.rowIndex}),r().createElement("div",{className:i.flexWrap}),!m&&((t,n)=>r().createElement(J,{field:e.field,rowIndex:e.rowIndex,label:n,value:t}))(n,l.name),m&&l.getLinks&&r().createElement(x.DataLinksContextMenu,{links:()=>{var e;return null!==(e=(0,x.getCellLinks)(l,d))&&void 0!==e?e:[]}},e=>e.openMenu?r().createElement("button",{className:i.linkWrapper,onClick:e.openMenu},r().createElement(r().Fragment,null,n)):r().createElement("div",{className:i.linkWrapper},r().createElement(r().Fragment,null,n)))))},ee=()=>(0,x.useStyles2)(e=>({linkButton:(0,u.css)({"&:focus":{outline:"none"},appearance:"none",background:"none",border:"none",color:"inherit",cursor:"pointer",font:"inherit",lineHeight:"normal",margin:0,MozOsxFontSmoothing:"inherit",padding:0,textAlign:"inherit",WebkitAppearance:"none",WebkitFontSmoothing:"inherit"})}));function te(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ne(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},l=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),l.forEach(function(t){te(e,t,n[t])})}return e}function le(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,l)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function re(e){const t=(0,x.useTheme2)(),{linkButton:n}=ee();let l;if(e.label===K.R){const t=je().options;e.value in t&&(l=t[e.value].color)}const o=((e,t)=>({activePill:(0,u.css)({}),pill:(0,u.css)({display:"inline-flex",flex:"0 1 auto",flexDirection:"column",marginLeft:e.spacing(.5),marginRight:e.spacing(.5),marginTop:e.spacing(.5),position:"relative"}),valueWrap:(0,u.css)({"&:before":{backgroundColor:t,content:'""',height:"100%",left:0,position:"absolute",top:0,width:`${e.spacing(.25)}`},"&:hover":{border:`1px solid ${e.colors.border.strong}`},backgroundColor:"transparent",border:`1px solid ${e.colors.border.weak}`,cursor:"pointer",padding:"5px 5px 4px 2px",paddingLeft:t?`${e.spacing(.75)}`:`${e.spacing(.5)}`,position:"relative"})}))(t,l);return r().createElement("button",{className:(0,u.cx)(n,o.pill,e.menuActive?o.activePill:void 0),onClick:e.onClick},r().createElement("span",{className:o.valueWrap},e.label,"=",e.value),e.menuActive&&r().createElement(q,{pillType:"logPill",fieldType:e.fieldType,links:e.links,label:e.label,value:e.value,showColumn:e.onClickAdd}))}const oe=e=>{const{label:t}=e,{cellIndex:n,setActiveCellIndex:a}=$(),{columns:i,setColumns:c}=(0,p.lI)(),s=e.value,d=(0,h.getTemplateSrv)(),u=(0,l.useMemo)(()=>d.replace.bind(d),[d]),m=e.field;if(!m||(null==m?void 0:m.type)===o.FieldType.other)return null;const g={index:e.rowIndex};e.originalField&&e.isDerivedField&&e.originalFrame&&(e.originalField.getLinks=(0,o.getLinksSupplier)(e.originalFrame,e.originalField,{},u));const f=e.originalField&&(0,x.getCellLinks)(e.originalField,g);return r().createElement(re,{onClick:()=>e.rowIndex===n.index&&m.name===n.fieldName&&t===n.subFieldName?a({index:null}):a({fieldName:m.name,index:e.rowIndex,numberOfMenuItems:e.isDerivedField?2:3,subFieldName:t}),menuActive:n.index===e.rowIndex&&n.fieldName===m.name&&n.subFieldName===t,fieldType:e.isDerivedField?"derived":void 0,label:t,value:s,onClickAdd:()=>(e=>{const t=ne({},i),n=Object.keys(i).filter(e=>i[e].active).length;t[e].active?t[e]=le(ne({},t[e]),{active:!1,index:void 0}):t[e]=le(ne({},t[e]),{active:!0,index:n}),c(t)})(t),links:f})};function ae(e){const t=(0,x.useTheme2)(),n=ie(t);return r().createElement("div",{"data-testid":w.b.table.rawLogLine,className:n.rawLogLine},r().createElement(r().Fragment,null,e.value))}const ie=(e,t)=>({rawLogLine:(0,u.css)({fontFamily:e.typography.fontFamilyMonospace,fontSize:e.typography.bodySmall.fontSize,height:"35px",lineHeight:"35px",paddingLeft:e.spacing(1),paddingRight:e.spacing(1.5)})}),ce=e=>{var t,n;null==e||null===(n=e.current)||void 0===n||n.scrollTo({left:null===(t=e.current)||void 0===t?void 0:t.scrollLeft})};function se({scrollerRef:e}){const t=(e=>({scroller:u.css`
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 20px;
    top: 32px;
    margin-top: -24px;
    // For some reason clicking on this button causes text to be selected in the following row
    user-select: none;
  `,scrollLeft:u.css`
    cursor: pointer;
    background: ${e.colors.background.primary};

    &:hover {
      background: ${e.colors.background.secondary};
    }
  `,scrollRight:u.css`
    cursor: pointer;
    background: ${e.colors.background.primary};

    &:hover {
      background: ${e.colors.background.secondary};
    }
  `}))((0,x.useTheme2)());return r().createElement("div",{className:t.scroller},r().createElement("span",{onPointerDown:()=>{var t,n;null==(t=e)||null===(n=t.current)||void 0===n||n.scrollTo({behavior:"smooth",left:0,top:0})},onPointerUp:()=>ce(e),className:t.scrollLeft},r().createElement(x.Icon,{name:"arrow-left"})),r().createElement("span",{onPointerDown:()=>{var t,n;null==(t=e)||null===(n=t.current)||void 0===n||n.scrollTo({behavior:"smooth",left:t.current.scrollWidth,top:0})},onPointerUp:()=>ce(e),className:t.scrollRight},r().createElement(x.Icon,{name:"arrow-right"})))}const de=e=>{let t=e.value;const n=e.field,i=n.display(t),c=(0,x.useTheme2)(),s=ue(c),{bodyState:u,columns:m}=(0,p.lI)(),{logsFrame:g}=d(),[f,v]=(0,l.useState)(!1),h=(0,l.useRef)(null);t=r().isValidElement(e.value)?e.value:"object"==typeof t?JSON.stringify(e.value):(0,o.formattedValueToString)(i);const y=(t=>Object.keys(m).filter(e=>e!==(0,a.Il)(g)).sort((e,t)=>e===K.R?-1:t===K.R?1:"LINK_FIELD"===m[e].type?-1:"LINK_FIELD"===m[t].type?1:m[e].cardinality>m[t].cardinality?-1:1).filter(e=>!m[e].active&&m[e].cardinality>1).map(l=>{var a;const i=t[l],c=null==g||null===(a=g.raw)||void 0===a?void 0:a.fields.find(e=>e.name===l),s=null==n?void 0:n.values[e.rowIndex],d=!i&&!!s;if(i)return r().createElement(oe,{originalFrame:void 0,field:n,columns:m,rowIndex:e.rowIndex,frame:e.frame,key:l,label:l,isDerivedField:!1,value:i});if(d&&(null==c?void 0:c.name)){const t=null==c?void 0:c.values[e.rowIndex];if((null==c?void 0:c.type)===o.FieldType.string&&t)return r().createElement(oe,{originalFrame:null==g?void 0:g.raw,originalField:c,field:n,value:t,columns:m,rowIndex:e.rowIndex,frame:e.frame,key:c.name,label:c.name,isDerivedField:!0})}return null}).filter(e=>e))(e.labels),w=u===p.Wg.auto,C=y.length>0;return r().createElement(Z,{onMouseIn:()=>{v(!0)},onMouseOut:()=>{v(!1)},rowIndex:e.rowIndex,field:e.field},r().createElement(b.ScrollSyncPane,{innerRef:h,group:"horizontal"},r().createElement("div",{className:s.content},0===e.fieldIndex&&r().createElement(G,{rowIndex:e.rowIndex,value:t}),w&&C&&r().createElement(r().Fragment,null,y),u===p.Wg.labels&&C&&r().createElement(r().Fragment,null,y),u===p.Wg.labels&&!C&&r().createElement(ae,{value:t}),w&&!C&&r().createElement(ae,{value:t}),u===p.Wg.text&&r().createElement(ae,{value:t}),f&&r().createElement(se,{scrollerRef:h}))))},ue=e=>({content:u.css`
    white-space: nowrap;
    overflow-x: auto;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    padding-right: 30px;
    display: flex;
    align-items: flex-start;
    height: 100%;
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
    }

    &:after {
      pointer-events: none;
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      // Fade out text in last 10px to background color to add affordance to horiziontal scroll
      background: linear-gradient(to right, transparent calc(100% - 10px), ${e.colors.background.primary});
    }
  `});function me(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function pe(e,t){if(null==e)return{};var n,l,r=function(e,t){if(null==e)return{};var n,l,r={},o=Object.keys(e);for(l=0;l<o.length;l++)n=o[l],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(l=0;l<o.length;l++)n=o[l],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ge(e){var t=function(e,t){if("object"!==fe(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,t||"default");if("object"!==fe(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===fe(t)?t:String(t)}function fe(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}const be=e=>{const{isHeaderMenuActive:t,setHeaderMenuActive:n}=(0,l.useContext)(_),{logsFrame:o}=d(),i=(0,l.useRef)(null),c=((e,t,n)=>({clearButton:(0,u.css)({marginLeft:"5px"}),defaultContentWrapper:(0,u.css)({borderLeft:t?`1px solid ${e.colors.border.weak}`:"none",display:"flex",marginLeft:t?"-6px":0,paddingLeft:t?"12px":0}),leftAlign:(0,u.css)({display:"flex",label:"left-align",width:"calc(100% - 20px)"}),logLineButton:(0,u.css)({marginLeft:"5px"}),rightAlign:(0,u.css)({display:"flex",label:"right-align",marginRight:"5px"}),tableHeaderMenu:(0,u.css)({backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3,height:"100%",label:"tableHeaderMenu",margin:e.spacing(1,0),maxHeight:"400px",minWidth:"250px",padding:e.spacing(2),width:"100%"}),wrapper:(0,u.css)({borderRight:`1px solid ${e.colors.border.weak}`,display:"flex",label:"wrapper",marginLeft:t?"56px":"6px",marginRight:"-6px",width:n?"calc(100% + 6px)":"100%"})}))((0,x.useTheme2)(),0===e.fieldIndex,e.field.name===(0,a.Il)(o)),{bodyState:s,columnWidthMap:m,setBodyState:g,setColumnWidthMap:f}=(0,p.lI)(),b=e.field.name===(0,a.Il)(o),v=()=>{g(s===p.Wg.text?p.Wg.labels:p.Wg.text)};return r().createElement("span",{className:c.wrapper},r().createElement("span",{className:c.leftAlign},r().createElement("span",{className:c.defaultContentWrapper},e.defaultContent),m&&f&&void 0!==(null==m?void 0:m[e.field.name])&&r().createElement(x.IconButton,{tooltip:"Reset column width",tooltipPlacement:"top",className:c.clearButton,"aria-label":"Reset column width",name:"x",onClick:()=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},l=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),l.forEach(function(t){me(e,t,n[t])})}return e}({},m),n=e.field.name,{[n]:l}=t,r=pe(t,[n].map(ge));null==f||f(r)}}),b&&r().createElement(r().Fragment,null,s===p.Wg.text?r().createElement(x.IconButton,{tooltipPlacement:"top",tooltip:"Show log labels","aria-label":"Show log labels",onClick:v,className:c.logLineButton,name:"tag-alt",size:"md"}):r().createElement(x.IconButton,{tooltipPlacement:"top",tooltip:"Show log text","aria-label":"Show log text",onClick:v,className:c.logLineButton,name:"text-fields",size:"md"}))),r().createElement("span",{className:c.rightAlign},r().createElement(x.IconButton,{tooltip:`Show ${e.field.name} menu`,tooltipPlacement:"top",ref:i,"aria-label":`Show ${e.field.name} menu`,onClick:e=>{n(!t)},name:"ellipsis-v"})),i.current&&r().createElement(x.Popover,{show:t,content:r().createElement(x.ClickOutsideWrapper,{onClick:()=>n(!1),useCapture:!0},r().createElement("div",{className:c.tableHeaderMenu},e.children)),referenceElement:i.current}))};function ve(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function he(e,t){if(null==e)return{};var n,l,r=function(e,t){if(null==e)return{};var n,l,r={},o=Object.keys(e);for(l=0;l<o.length;l++)n=o[l],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(l=0;l<o.length;l++)n=o[l],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ye(e){var t=function(e,t){if("object"!==xe(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,t||"default");if("object"!==xe(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===xe(t)?t:String(t)}function xe(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}function we(e){const{bodyState:t,columns:n,setBodyState:o,setColumns:i,columnWidthMap:c,setColumnWidthMap:s}=(0,p.lI)(),{logsFrame:m}=d(),g=Ce(),{linkButton:f}=ee(),b=(0,l.useCallback)(e=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},l=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),l.forEach(function(t){ve(e,t,n[t])})}return e}({},n);if(Object.keys(t).filter(n=>{const l=t[n].index,r=t[e.name].index;return t[n].active&&r&&l&&l>r}).map(e=>t[e]).forEach(e=>{void 0!==e.index&&e.index--}),t[e.name].active=!1,t[e.name].index=void 0,i(t),void 0!==c[e.name]){const t=e.name,{[t]:n}=c,l=he(c,[t].map(ye));s(l)}},[n,i,c,s]),v=e.headerProps.field.name===(0,a.Il)(m);return r().createElement(be,e.headerProps,r().createElement("div",{className:g.linkWrap},r().createElement("button",{className:(0,u.cx)(f,g.link),onClick:()=>b(e.headerProps.field)},r().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17 16",width:"17",height:"16",className:"css-q2u0ig-Icon"},r().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.73446 1.33301H12.2345C12.3892 1.33301 12.5375 1.40325 12.6469 1.52827C12.7563 1.65329 12.8178 1.82286 12.8178 1.99967V4.74967C12.8178 5.07184 12.5566 5.33301 12.2345 5.33301C11.9123 5.33301 11.6511 5.07184 11.6511 4.74967V2.66634H7.56779V13.333H11.6511V10.9163C11.6511 10.5942 11.9123 10.333 12.2345 10.333C12.5566 10.333 12.8178 10.5942 12.8178 10.9163V13.9997C12.8178 14.1765 12.7563 14.3461 12.6469 14.4711C12.5375 14.5961 12.3892 14.6663 12.2345 14.6663H1.73446C1.57975 14.6663 1.43137 14.5961 1.32198 14.4711C1.21258 14.3461 1.15112 14.1765 1.15112 13.9997V1.99967C1.15112 1.82286 1.21258 1.65329 1.32198 1.52827C1.43137 1.40325 1.57975 1.33301 1.73446 1.33301ZM2.31779 13.333H6.40112V2.66634H2.31779V13.333Z",fill:"#CCCCDC",fillOpacity:"1"}),r().createElement("path",{d:"M15.9893 10.6315C15.9498 10.7263 15.8919 10.8123 15.819 10.8846C15.7467 10.9575 15.6607 11.0154 15.5659 11.0549C15.4712 11.0943 15.3695 11.1147 15.2668 11.1147C15.1641 11.1147 15.0625 11.0943 14.9677 11.0549C14.8729 11.0154 14.7869 10.9575 14.7146 10.8846L12.9335 9.09573L11.1524 10.8846C11.0801 10.9575 10.9941 11.0154 10.8993 11.0549C10.8045 11.0943 10.7028 11.1147 10.6002 11.1147C10.4975 11.1147 10.3958 11.0943 10.301 11.0549C10.2063 11.0154 10.1202 10.9575 10.0479 10.8846C9.97504 10.8123 9.91717 10.7263 9.87769 10.6315C9.8382 10.5367 9.81787 10.4351 9.81787 10.3324C9.81787 10.2297 9.8382 10.1281 9.87769 10.0333C9.91717 9.9385 9.97504 9.85248 10.0479 9.78017L11.8368 7.99906L10.0479 6.21795C9.90148 6.07149 9.8192 5.87285 9.8192 5.66573C9.8192 5.4586 9.90148 5.25996 10.0479 5.1135C10.1944 4.96705 10.393 4.88477 10.6002 4.88477C10.8073 4.88477 11.0059 4.96705 11.1524 5.1135L12.9335 6.90239L14.7146 5.1135C14.8611 4.96705 15.0597 4.88477 15.2668 4.88477C15.4739 4.88477 15.6726 4.96705 15.819 5.1135C15.9655 5.25996 16.0478 5.4586 16.0478 5.66573C16.0478 5.87285 15.9655 6.07149 15.819 6.21795L14.0302 7.99906L15.819 9.78017C15.8919 9.85248 15.9498 9.9385 15.9893 10.0333C16.0288 10.1281 16.0491 10.2297 16.0491 10.3324C16.0491 10.4351 16.0288 10.5367 15.9893 10.6315Z",fill:"#CCCCDC",fillOpacity:"1"})),"Remove column")),e.slideLeft&&r().createElement("div",{className:g.linkWrap},r().createElement("button",{className:(0,u.cx)(f,g.link),onClick:()=>{var t;return null===(t=e.slideLeft)||void 0===t?void 0:t.call(e,n)}},r().createElement(x.Icon,{className:(0,u.cx)(g.icon,g.reverse),name:"arrow-from-right",size:"md"}),"Move left")),e.slideRight&&r().createElement("div",{className:g.linkWrap},r().createElement("button",{className:(0,u.cx)(f,g.link),onClick:()=>{var t;return null===(t=e.slideRight)||void 0===t?void 0:t.call(e,n)}},r().createElement(x.Icon,{className:g.icon,name:"arrow-from-right",size:"md"}),"Move right")),v&&r().createElement("div",{className:g.linkWrap},r().createElement("button",{className:(0,u.cx)(f,g.link),onClick:()=>{t===p.Wg.text?o(p.Wg.labels):o(p.Wg.text)}},t===p.Wg.text?r().createElement(x.Icon,{className:g.icon,name:"brackets-curly",size:"md"}):r().createElement(x.Icon,{className:g.icon,name:"text-fields",size:"md"}),t===p.Wg.text?"Show labels":"Show log text")),e.autoColumnWidths&&r().createElement("div",{className:g.linkWrap},r().createElement("button",{className:(0,u.cx)(f,g.link),onClick:()=>{var t;return null===(t=e.autoColumnWidths)||void 0===t?void 0:t.call(e)}},r().createElement(x.Icon,{className:g.icon,name:"arrows-h",size:"md"}),"Reset column widths")))}const Ce=()=>({icon:(0,u.css)({marginRight:"10px"}),link:(0,u.css)({paddingBottom:"5px",paddingTop:"5px"}),linkWrap:(0,u.css)({}),reverse:(0,u.css)({transform:"scaleX(-1)"})});function Ee(e,t,n,l,r,o,a){try{var i=e[o](a),c=i.value}catch(e){return void n(e)}i.done?t(c):Promise.resolve(c).then(l,r)}function ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},l=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(l=l.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),l.forEach(function(t){ke(e,t,n[t])})}return e}function Ie(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,l)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Se(e){return r().createElement(x.Table,{onColumnResize:e.onResize,initialRowIndex:e.selectedLine,cellHeight:y.qM.Sm,data:e.data,height:e.height,width:e.width,footerOptions:{countRows:!0,reducer:["count"],show:!0}})}const Le=e=>{const{height:t,labels:n,logsFrame:i,timeZone:c,width:s}=e,m=(0,x.useTheme2)(),[y,C]=(0,l.useState)(void 0),[E,k]=(0,l.useState)(220),[O,I]=(0,l.useState)(!1),S=s-(O?40:E),L=((e,t,n)=>({collapsedTableSidebar:(0,u.css)({alignItems:"center",display:"flex",flexDirection:"column",justifyContent:"flex-start",paddingRight:e.spacing(1),paddingTop:e.spacing(8),width:"40px !important"}),collapseTableSidebarButton:(0,u.css)({"&:hover":{background:e.colors.background.primary,borderColor:e.colors.border.medium},background:e.colors.background.secondary,border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,cursor:"pointer",padding:e.spacing(.5),position:"absolute",right:e.spacing(1),top:e.spacing(1),transition:"all 0.2s ease-in-out",zIndex:10}),rzHandle:(0,u.css)({[e.transitions.handleMotion("no-preference","reduce")]:{transition:"0.3s background ease-in-out"},"&:hover":{background:e.colors.secondary.shade},background:e.colors.secondary.main,borderRadius:e.shape.radius.pill,cursor:"grab",height:"50% !important",position:"relative",right:`${e.spacing(1)} !important`,top:"25% !important",width:`${e.spacing(1)} !important`}),sidebar:(0,u.css)({fontSize:e.typography.pxToRem(11),height:t,overflowY:"hidden",paddingRight:e.spacing(3),position:"relative",width:n}),tableWrap:(0,u.css)({".cellActions":{display:"none !important"}}),wrapper:(0,u.css)({display:"flex",position:"relative",flexWrap:"wrap"})}))(m,t,E),{clearSelectedLine:F,columns:j,columnWidthMap:N,setColumns:T,setColumnWidthMap:W}=(0,p.lI)(),{selectedLine:P}=d(),[M]=(0,l.useState)(P),D=B(T),R=(0,h.getTemplateSrv)(),H=(0,l.useMemo)(()=>R.replace.bind(R),[R]),$=(0,l.useCallback)(e=>{if(!e.length)return e;const[t]=(0,o.applyFieldOverrides)({data:[e],fieldConfig:{defaults:{custom:{}},overrides:[]},replaceVariables:H,theme:m,timeZone:c});for(const[c,d]of t.fields.entries()){var l,a;d.type=d.type===o.FieldType.string?null!==(l=Fe(d))&&void 0!==l?l:o.FieldType.string:d.type,d.config=Ie(Oe({},d.config),{custom:Oe({cellOptions:Ne(d,c,n,i),filterable:!0,headerComponent:t=>r().createElement(A,null,r().createElement(we,{headerProps:Ie(Oe({},t),{fieldIndex:c}),slideLeft:0!==c?e=>D(e,c,c-1):void 0,slideRight:c!==e.fields.length-1?e=>D(e,c,c+1):void 0,autoColumnWidths:Object.keys(N).length>0?()=>{W({})}:void 0})),inspect:!0,width:null!==(a=N[d.name])&&void 0!==a?a:Te(d,c,j,s,t.fields.length,i)},d.config.custom),filterable:!0})}return t},[c,m,n,s,H,N]);(0,l.useEffect)(()=>{var e;(e=function*(){const e=(t=i.raw).fields.filter(e=>{var n,l,r;const a="json.RawMessage"===(null===(n=e.typeInfo)||void 0===n?void 0:n.frame)&&"labels"===e.name&&(null==t||null===(l=t.meta)||void 0===l?void 0:l.type)!==o.DataFrameType.LogLines,i="labels"===e.name&&e.type===o.FieldType.other&&(null==t||null===(r=t.meta)||void 0===r?void 0:r.type)===o.DataFrameType.LogLines;return a||i}).flatMap(e=>[{id:"extractFields",options:{format:"json",keepTime:!1,replace:!1,source:e.name}}]);var t;const n=function(e){let t={};for(const n in e)t[n]=!0;return Object.keys(e).length>0?{id:"organize",options:{includeByName:t,indexByName:e}}:null}(function(e){let t={};return Object.keys(e).filter(t=>e[t].active).forEach(n=>{const l=e[n].index;void 0!==l&&(t[n]=l)}),t}(j));if(n)e.push(n);else{const t={body:i.bodyField,extraFields:i.extraFields,time:i.timeField};t&&void 0!==t.body&&void 0!==t.time&&e.push(function(e){return{id:"organize",options:{includeByName:{[e.body.name]:!0,[e.time.name]:!0},indexByName:{[e.time.name]:0,[e.body.name]:1}}}}(t))}if(e.length>0){const t=yield(0,v.lastValueFrom)((0,o.transformDataFrame)(e,[i.raw])),n=$(t[0]);C(n)}else C($(i.raw))},function(){var t=this,n=arguments;return new Promise(function(l,r){var o=e.apply(t,n);function a(e){Ee(o,l,r,a,i,"next",e)}function i(e){Ee(o,l,r,a,i,"throw",e)}a(void 0)})})()},[i.raw,i.bodyField,i.timeField,i.extraFields,$,j]),(0,l.useEffect)(()=>{M&&P&&F()},[M,F,P]);const _=i.raw.fields.find(e=>e.name===(0,a.po)(i)),Z=null==_?void 0:_.values.findIndex(e=>e===(null==M?void 0:M.id)),U=Z&&-1!==Z?Z:void 0;if(!y)return r().createElement(r().Fragment,null);return r().createElement("div",{"data-testid":w.b.table.wrapper,className:L.wrapper},r().createElement(f.c,{enable:{right:!O},handleClasses:{right:L.rzHandle},onResize:(e,t,n)=>{const l=Number(n.style.width.slice(0,-2));isNaN(l)||k(l)},minWidth:O?40:150,maxWidth:O?40:.8*s,size:{height:t,width:O?40:E}},r().createElement("section",{className:`${L.sidebar} ${O?L.collapsedTableSidebar:""}`},r().createElement(z,{isTableSidebarCollapsed:O,onToggleTableSidebarCollapse:()=>{I(!O)},collapseTableSidebarButtonClassName:L.collapseTableSidebarButton}))),r().createElement("div",{className:L.tableWrap},r().createElement(V,null,r().createElement(b.ScrollSync,{horizontal:!0,vertical:!1,proportional:!1},r().createElement(Se,{logsFrame:i,selectedLine:U,data:y,height:t,width:S,onResize:(0,g.debounce)((e,t)=>{const n=Object.keys(j).filter(e=>j[e].active).find(t=>t===e);if(n&&t>0){const e=Oe({},N);e[n]=t,W(e)}},100),logsSortOrder:e.logsSortOrder})))))};function Fe(e){if(e.name){const t=e.name.toLowerCase();if("date"===t||"time"===t)return o.FieldType.time}for(let t=0;t<e.values.length;t++){const n=e.values[t];if(null!=n)return Re(n)}}const je=()=>({options:{crit:{color:"#705da0",index:1},critical:{color:"#705da0",index:0},debug:{color:"#1f78c1",index:8},eror:{color:"#e24d42",index:4},err:{color:"#e24d42",index:3},error:{color:"#e24d42",index:2},info:{color:"#7eb26d",index:7},trace:{color:"#6ed0e0",index:9},warn:{color:"#FF9900",index:6},warning:{color:"#FF9900",index:5}},type:o.MappingType.ValueToText});function Ne(e,t,n,l){return e.name===(0,a.Il)(l)?{cellComponent:e=>r().createElement(de,Ie(Oe({},e),{fieldIndex:t,labels:n[e.rowIndex]})),type:x.TableCellDisplayMode.Custom}:{cellComponent:e=>r().createElement(Q,Ie(Oe({},e),{fieldIndex:t})),type:x.TableCellDisplayMode.Custom}}function Te(e,t,n,l,r,i){var c,s;const d=r<=2?l:Math.min(l/2),u=0===t?50:0;if(e.type===o.FieldType.time)return 200+u;const m=n[e.name];if(void 0===m)return;var p;const g=Math.max(null!==(p=m.maxLength)&&void 0!==p?p:0,e.name.length);return m.maxLength?Math.min(Math.max(6.5*g+95+35+u,90+u),d):e.name!==(0,a.Il)(i)?Math.min(Math.max(6.5*(null!==(f=null===(s=e.values)||void 0===s||null===(c=s[0])||void 0===c?void 0:c.length)&&void 0!==f?f:80)+95+35+u,90+u),d):void 0;var f}var We=n(4907);const Pe=/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3,})?(?:Z|[-+]\d{2}:?\d{2})$/,Me=e=>{const{logsFrame:t}=d(),[n,a]=(0,l.useState)({height:0,width:0});(0,m.w)({onResize:()=>{const t=e.panelWrap.current;t&&(n.width===t.clientWidth&&n.height===t.clientHeight||a({height:t.clientHeight,width:t.clientWidth}))},ref:e.panelWrap});const i={section:(0,u.css)({position:"relative"})},c=(0,o.getTimeZone)(),s=(0,l.useCallback)(t=>{const n=e.urlColumns;return(null==n?void 0:n.length)&&Object.values(n).forEach((e,n)=>{t[e]&&(t[e].active=!0,t[e].index=n)}),t},[e.urlColumns]);if(!t||!t.raw.length)return null;var g;const f=null!==(g=t.getLogFrameLabelsAsLabels())&&void 0!==g?g:[],b=t?t.raw.length:0;let v=function(e,t){let n={};const l=new Map,r=function(e){const t=new Map;return e.forEach(e=>{Object.keys(e).forEach(n=>{if(t.has(n)){const l=t.get(n),r=null==l?void 0:l.valueSet,o=null==l?void 0:l.maxLength;r&&!(null==r?void 0:r.has(e[n]))&&(null==r||r.add(e[n]),o&&e[n].length>o&&t.set(n,{maxLength:e[n].length,valueSet:r}))}else t.set(n,{maxLength:e[n].length,valueSet:new Set([e[n]])})})}),t}(t),o=e?e.length:0;(null==t?void 0:t.length)&&o&&(t.forEach(e=>{Object.keys(e).forEach(e=>{var t;const n=r.get(e);var o;const a=null!==(o=null==n||null===(t=n.valueSet)||void 0===t?void 0:t.size)&&void 0!==o?o:0;if(l.has(e)){const t=l.get(e);t&&((null==t?void 0:t.active)?l.set(e,{active:!0,cardinality:a,index:t.index,maxLength:null==n?void 0:n.maxLength,percentOfLinesWithLabel:t.percentOfLinesWithLabel+1}):l.set(e,{active:!1,cardinality:a,index:void 0,maxLength:null==n?void 0:n.maxLength,percentOfLinesWithLabel:t.percentOfLinesWithLabel+1}))}else l.set(e,{active:!1,cardinality:a,index:void 0,maxLength:null==n?void 0:n.maxLength,percentOfLinesWithLabel:1})})}),n=Object.fromEntries(l),Object.keys(n).forEach(e=>{n[e].percentOfLinesWithLabel=De(n[e].percentOfLinesWithLabel,o)}));return n}(t.raw,f);const h={body:t.bodyField,extraFields:t.extraFields,time:t.timeField};if(h){!function(e,t,n){e.forEach(e=>{var l,r;if(!e)return;const o=null===(l=t[e.name])||void 0===l?void 0:l.active,a=null===(r=t[e.name])||void 0===r?void 0:r.index;t[e.name]=o&&void 0!==a?{active:!0,cardinality:n,index:a,percentOfLinesWithLabel:De(e.values.filter(e=>null!=e).length,n)}:{active:!1,cardinality:n,index:void 0,percentOfLinesWithLabel:De(e.values.filter(e=>null!=e).length,n)}})}([h.time,h.body,...h.extraFields],v,b),v=s(v);!function(e,t,n){var l,r;if(0===e.length){var o,a,i,c,s,d;if(null===(o=t.body)||void 0===o?void 0:o.name)n[null===(i=t.body)||void 0===i?void 0:i.name].active=!0,n[null===(c=t.body)||void 0===c?void 0:c.name].index=1;if(null===(a=t.time)||void 0===a?void 0:a.name)n[null===(s=t.time)||void 0===s?void 0:s.name].active=!0,n[null===(d=t.time)||void 0===d?void 0:d.name].index=0}if((null===(l=t.time)||void 0===l?void 0:l.name)&&(null===(r=t.body)||void 0===r?void 0:r.name)){var u,m;n[null===(u=t.body)||void 0===u?void 0:u.name].type="BODY_FIELD",n[null===(m=t.time)||void 0===m?void 0:m.name].type="TIME_FIELD"}t.extraFields.length&&t.extraFields.forEach(e=>{var t;(null===(t=e.config.links)||void 0===t?void 0:t.length)&&(n[e.name].type="LINK_FIELD")})}(Object.keys(v).filter(e=>v[e].active),h,v)}return r().createElement("section",{className:i.section},r().createElement(p.nz,{setUrlTableBodyState:e.setUrlTableBodyState,logsFrame:t,initialColumns:v,setUrlColumns:e.setUrlColumns,clearSelectedLine:e.clearSelectedLine,urlTableBodyState:e.urlTableBodyState},r().createElement(Le,{logsFrame:t,timeZone:c,height:n.height-50,width:n.width-25+(We.CT?-32:0),labels:f,logsSortOrder:e.logsSortOrder})))},De=(e,t)=>Math.ceil(100*e/t);function Re(e){let t=(0,o.guessFieldTypeFromValue)(e);return"string"===t&&Pe.test(e)&&(t=o.FieldType.time),t}function Be({addFilter:e,clearSelectedLine:t,dataFrame:n,logsSortOrder:i,panelWrap:c,selectedLine:d,setUrlColumns:u,setUrlTableBodyState:m,timeRange:p,urlColumns:g,urlTableBodyState:f}){const b=(0,l.useMemo)(()=>{if(!n)return null;const e=n.fields.findIndex(e=>e.type===o.FieldType.time),t=(0,o.sortDataFrame)(n,e,i===o.LogsSortOrder.Descending);return(0,a.Os)(t)},[n,i]);return b?r().createElement(s,{addFilter:e,selectedLine:d,timeRange:p,logsFrame:b},r().createElement(Me,{urlTableBodyState:f,setUrlColumns:u,setUrlTableBodyState:m,urlColumns:g,panelWrap:c,clearSelectedLine:t,logsSortOrder:i})):null}}}]);
//# sourceMappingURL=675.js.map?_cache=0a1079bbbee9699b0bea