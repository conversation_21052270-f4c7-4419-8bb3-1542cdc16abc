

export const ProcessStatus = {
    QUEUED: 0,
    RUNNING: 1,
    FINISHED: 2,
    FAILED: 3,
    CANCELED: 4,
    ASSIGNED: 5,
}

export function getProcessStatusDisplayString(status){
  switch (status){
    case ProcessStatus.QUEUED:
      return `in der Warteschlange`;
    case ProcessStatus.RUNNING:
      return `Verarbeitung läuft`;
    case ProcessStatus.FINISHED:
      return `fertig`;
    case ProcessStatus.FAILED:
      return `fehlgeschlagen`;
    case ProcessStatus.CANCELED:
      return `gestoppt`
    case ProcessStatus.ASSIGNED:
      return `in Vorbereitung`;
  }
}

export function getProcessStatusColor(processStatus){
  switch (processStatus) {
    case ProcessStatus.QUEUED:
      return 'light-blue-14';
    case ProcessStatus.RUNNING:
      return 'amber-7';
    case ProcessStatus.FINISHED:
      return 'light-green-9';
    case ProcessStatus.FAILED:
      return 'red-10';
    case ProcessStatus.CANCELED:
      return 'grey-8';
    case ProcessStatus.ASSIGNED:
      return 'light-blue-14';
  }
}

export function formatDeviceSize (size) {
    if(size === null){
        return null;
    }
    // gets info like "3816816.64 MiB" and returns "3.8TB"
    let sizeValue = size.split(' ')[0]
    sizeValue = parseFloat(sizeValue)
    let factor = 0

    while (sizeValue > 1024) {
      factor++
      sizeValue = sizeValue / 1024
    }

    sizeValue = sizeValue.toFixed(2)
    const sizeValueString = String(sizeValue).replace('.', ',')

    switch (factor) {
      case 1:
        return sizeValueString + ' GB'
      case 2:
        return sizeValueString + ' TB'
    }
    return sizeValueString + ' MB'
  }