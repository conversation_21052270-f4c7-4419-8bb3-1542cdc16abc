from pydantic import BaseModel

from workflow_backend.src.Define import Step, ProcessStatus
from workflow_backend.src.models.process_data_models import XwaysDuResultData


class AddProcessPayload(BaseModel):
    evidence_item_id: int | None = None
    step_id: Step
    status: ProcessStatus
    step_status: str | None = None
    input_data: str | None = ""
    result_data: str | XwaysDuResultData | None = None
    prioritize: bool | None = False


class UpdateProcessPayload(BaseModel):
    status: ProcessStatus | None = None
    step_status: str | None = None
    evidence_item_id: int | None = None
    input_data: str | None = ""
    result_data: str | XwaysDuResultData | None = None
    prioritize: bool | None = False

