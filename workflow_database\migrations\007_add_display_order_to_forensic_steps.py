from yoyo import step

__depends__ = {"006_add_further_forensic_step"}

steps = [
    step(
        """
        ALTER TABLE forensic_steps
        ADD COLUMN display_position INT UNIQUE;
        """,
        """
        ALTER TABLE forensic_steps
        DROP COLUMN display_position;
        """
    ),
    step(
        """
        UPDATE forensic_steps SET display_position = 0 WHERE name = 'LagerDB';
        UPDATE forensic_steps SET display_position = 1 WHERE name = 'ImageStation';
        UPDATE forensic_steps SET display_position = 2 WHERE name = 'FOCUS.AI';
        UPDATE forensic_steps SET display_position = 3 WHERE name = 'XwaysDÜ';
        UPDATE forensic_steps SET display_position = 4 WHERE name = 'Manual analysis';
        """,
        """
        UPDATE forensic_steps SET display_position = null WHERE name = 'LagerDB';
        UPDATE forensic_steps SET display_position = null WHERE name = 'ImageStation';
        UPDATE forensic_steps SET display_position = null WHERE name = 'FOCUS.AI';
        UPDATE forensic_steps SET display_position = null WHERE name = 'XwaysDÜ';
        UPDATE forensic_steps SET display_position = null WHERE name = 'Manual analysis';
        """
    ),
    step(
        """
        ALTER TABLE forensic_steps
        ALTER COLUMN display_position SET NOT NULL;
        """,
        """
        ALTER TABLE forensic_steps
        ALTER COLUMN display_position DROP NOT NULL;
        """
    )

]
