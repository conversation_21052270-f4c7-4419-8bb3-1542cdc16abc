{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "PCC52D03280B7034C"}, "fieldConfig": {"defaults": {"color": {"fixedColor": "red", "mode": "thresholds"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [{"options": {"Asservate": {"color": "#bfbebe", "index": 1}, "Fälle": {"color": "#6a6a6a", "index": 0}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 10, "x": 0, "y": 0}, "id": 3, "options": {"barRadius": 0, "barWidth": 0.97, "colorByField": "label", "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "orientation": "horizontal", "showValue": "always", "stacking": "normal", "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}, "xField": "label", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "12.0.2", "targets": [{"editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT 'Fälle' as label, COUNT(*) AS count FROM cases \nUNION ALL\nSELECT 'Asservate', COUNT(*) FROM evidence_items\n;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Aktuelle Anzahl Fälle und Asservate im Workflow-System", "type": "barchart"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "PCC52D03280B7034C"}, "description": "Warteschlange: Gesamtgröße der Asservate, für die ein Prozess mit dem Status QUEUED existiert\n\nDurchsatz: Gesamtgröße voranalysierte Asservate / ausgewählte Anzahl Tage", "fieldConfig": {"defaults": {"color": {"fixedColor": "#B877D9", "mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "decmbytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "queued_size_mib"}, "properties": [{"id": "displayName", "value": "Aktuelle Warteschlange"}, {"id": "color", "value": {"fixedColor": "super-light-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_mib_per_day"}, "properties": [{"id": "displayName", "value": "Ø Gesamtdurchsatz pro Tag"}, {"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 5, "w": 11, "x": 10, "y": 0}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.2", "targets": [{"editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "WITH work_queued\r\n         AS (SELECT SUM(CAST(REGEXP_REPLACE(e.device_size, '[^0-9.]', '', 'g') AS DOUBLE PRECISION)) AS total_size_mib\r\n             FROM processes AS p\r\n                      LEFT JOIN evidence_items AS e ON p.evidence_item_id = e.id\r\n             WHERE p.status = 0 and p.step_id = 2),\r\n    \r\n     mib_per_day\r\n         AS (SELECT (SUM(size_mib) / SUM(duration_in_days)) as avg_mib_per_day\r\n             FROM (SELECT CEIL(EXTRACT(EPOCH FROM ($__timeTo()::timestamptz - $__timeFrom()::timestamptz)) / (60 * 60 * 24))    AS duration_in_days,\r\n                          CAST(REGEXP_REPLACE(e.device_size, '[^0-9.]', '', 'g') AS DOUBLE PRECISION) AS size_mib\r\n                   FROM processes AS p\r\n                            LEFT JOIN evidence_items AS e ON p.evidence_item_id = e.id\r\n             WHERE p.status = 2 and p.step_id = 2 and $__timeFilter(p.ended_at)))\r\n    \r\n\r\nSELECT\r\n    work_queued.total_size_mib                                  as queued_size_mib, \r\n    mib_per_day.avg_mib_per_day                                 as avg_mib_per_day\r\n       \r\nFROM work_queued, mib_per_day;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Voranalyse mit InvestiGator", "type": "stat"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "PCC52D03280B7034C"}, "description": "Der Effizienzwert beschreibt das Verhältnis von Durchsatz fertige Prozesse Imaging und Durchsatz fertige Prozesse Voranalyse.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-RdYlGr"}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 21, "y": 0}, "id": 10, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "12.0.2", "targets": [{"editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "WITH imagestation\r\n         AS (SELECT (SUM(size_mib) / SUM(duration_secs)) as avg_mib_per_secs\r\n             FROM (SELECT EXTRACT(EPOCH FROM (p.ended_at - p.started_running_at))                     AS duration_secs,\r\n                          CAST(REGEXP_REPLACE(e.device_size, '[^0-9.]', '', 'g') AS DOUBLE PRECISION) AS size_mib\r\n                   FROM processes AS p\r\n                            LEFT JOIN evidence_items AS e ON p.evidence_item_id = e.id\r\n                   WHERE p.status = 2 and p.step_id = 1)),\r\n\r\n     investigator\r\n         AS (SELECT (SUM(size_mib) / SUM(duration_secs)) as avg_mib_per_secs\r\n             FROM (SELECT EXTRACT(EPOCH FROM (p.ended_at - p.started_running_at))                     AS duration_secs,\r\n                          CAST(REGEXP_REPLACE(e.device_size, '[^0-9.]', '', 'g') AS DOUBLE PRECISION) AS size_mib\r\n                   FROM processes AS p\r\n                            LEFT JOIN evidence_items AS e ON p.evidence_item_id = e.id\r\n                   WHERE p.status = 2 and p.step_id = 2 and $__timeFilter(p.ended_at)))\r\n\r\n\r\nSELECT (1 - (ABS(imagestation.avg_mib_per_secs - investigator.avg_mib_per_secs) /\r\n             GREATEST(imagestation.avg_mib_per_secs, investigator.avg_mib_per_secs))) as efficiency\r\nFROM imagestation,\r\n     investigator\r\nGROUP BY imagestation.avg_mib_per_secs, investigator.avg_mib_per_secs\r\n;\r\n", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Effizienz der Verarbeitung (Imaging, Voranalyse)", "type": "gauge"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 7, "panels": [], "title": "Imaging", "type": "row"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "PCC52D03280B7034C"}, "description": "Der Durchsatz für einen Prozess wird aus der Zeitspanne zwischen Start- und Endzeitstempel und der Größe des zugeordneten Asservats berechnet.", "fieldConfig": {"defaults": {"color": {"fixedColor": "super-light-blue", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 86400000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red"}]}, "unit": "Mbits"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 6}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "PCC52D03280B7034C"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \n    record_time,\n    mib_per_secs\nFROM (\n  SELECT *,\n         size_mib / duration_secs  AS mib_per_secs\n  FROM (\n    SELECT p.step_id, p.ended_at AS record_time,\n           EXTRACT(EPOCH FROM (p.ended_at - p.started_running_at)) AS duration_secs,\n           CAST(REGEXP_REPLACE(e.device_size, '[^0-9.]', '', 'g') AS DOUBLE PRECISION) AS size_mib\n    FROM processes AS p\n    LEFT JOIN evidence_items AS e ON p.evidence_item_id = e.id\n    WHERE p.status = 2 and step_id = 1 and $__timeFilter(p.ended_at)\n  )\n)\nGROUP BY \n  record_time, mib_per_secs\nORDER BY \n  record_time\n;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Durchsatz fertige Prozesse (im Zeitverlauf)", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 6, "panels": [], "title": "Voranalyse", "type": "row"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "PCC52D03280B7034C"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "failed_size_mib"}, "properties": [{"id": "displayName", "value": "Fehlgeschlagen"}, {"id": "unit", "value": "decmbytes"}, {"id": "color", "value": {"fixedColor": "#a9a9a9", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "failed_count"}, "properties": [{"id": "displayName", "value": "Anzahl fehlgeschlagene Prozesse"}, {"id": "color", "value": {"fixedColor": "#a9a9a9", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "done_size_mib"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON><PERSON>"}, {"id": "unit", "value": "decmbytes"}, {"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "done_count"}, "properties": [{"id": "displayName", "value": "Anzahl fertige Prozesse"}, {"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}]}, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 15}, "id": 8, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.0.2", "targets": [{"editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "WITH work_failed\r\n         AS (SELECT SUM(CAST(REGEXP_REPLACE(e.device_size, '[^0-9.]', '', 'g') AS DOUBLE PRECISION)) AS total_size_mib,\r\n                    COUNT(p.id) as count\r\n             FROM processes AS p\r\n                      LEFT JOIN evidence_items AS e ON p.evidence_item_id = e.id\r\n             WHERE p.status = 3 and p.step_id = 2 and $__timeFilter(p.ended_at)),\r\n\r\n     work_done\r\n         AS (SELECT SUM(CAST(REGEXP_REPLACE(e.device_size, '[^0-9.]', '', 'g') AS DOUBLE PRECISION)) AS total_size_mib,\r\n                    COUNT(p.id) as count\r\n             FROM processes AS p\r\n                      LEFT JOIN evidence_items AS e ON p.evidence_item_id = e.id\r\n             WHERE p.status = 2 and p.step_id = 2 and $__timeFilter(p.ended_at))\r\n\r\n\r\nSELECT work_done.count            as done_count,\r\n       work_done.total_size_mib   as done_size_mib,\r\n       work_failed.count          as failed_count,\r\n       work_failed.total_size_mib as failed_size_mib\r\n\r\nFROM work_failed,\r\n     work_done;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "", "type": "stat"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "PCC52D03280B7034C"}, "description": "Die Wartezeit eines Prozesses ist die Dauer von der Erstellung des Prozesses in der Datenbank bis zum Start des Prozesses (erstmaliges Erhalten des Status RUNNING).", "fieldConfig": {"defaults": {"color": {"fixedColor": "blue", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 15, "x": 9, "y": 15}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.2", "targets": [{"editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT p.started_running_at                                      AS record_time,\r\n       EXTRACT(EPOCH FROM (p.started_running_at - p.created_at)) AS waiting_time_secs\r\nFROM processes AS p\r\nWHERE p.step_id = 2 and $__timeFilter(p.started_running_at)\r\nGROUP BY record_time, waiting_time_secs\r\nORDER BY record_time\r\n;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Wartezeit (im Zeitverlauf)", "type": "timeseries"}, {"datasource": {"type": "grafana-postgresql-datasource", "uid": "PCC52D03280B7034C"}, "description": "Der Durchsatz für einen Prozess wird aus der Zeitspanne zwischen Start- und Endzeitstempel und der Größe des zugeordneten Asservats berechnet.", "fieldConfig": {"defaults": {"color": {"fixedColor": "super-light-blue", "mode": "fixed"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": 86400000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange"}]}, "unit": "MBs"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 22}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.2", "targets": [{"editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT \n    record_time,\n    mib_per_secs\nFROM (\n  SELECT *,\n         size_mib / duration_secs  AS mib_per_secs\n  FROM (\n    SELECT p.step_id, p.ended_at AS record_time,\n           EXTRACT(EPOCH FROM (p.ended_at - p.started_running_at)) AS duration_secs,\n           CAST(REGEXP_REPLACE(e.device_size, '[^0-9.]', '', 'g') AS DOUBLE PRECISION) AS size_mib\n    FROM processes AS p\n    LEFT JOIN evidence_items AS e ON p.evidence_item_id = e.id\n    WHERE p.status = 2 and p.step_id = 2 and $__timeFilter(p.ended_at)\n  )\n)\nGROUP BY record_time, mib_per_secs\nORDER BY record_time\n;", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "title": "Durchsatz fertige Prozesse (im Zeitverlauf)", "type": "timeseries"}], "preload": false, "schemaVersion": 41, "tags": [], "templating": {"list": []}, "time": {"from": "2025-03-21T13:40:52.289Z", "to": "2025-03-21T14:59:49.045Z"}, "timepicker": {}, "timezone": "browser", "title": "Durchsatz Dashboard", "uid": "8294a2ba-597f-4f61-aaf8-b819b1d402f1", "version": 1}