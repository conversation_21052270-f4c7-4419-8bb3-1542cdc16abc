apiVersion: 1

datasources:
  - name: PostgreSQL
    type: grafana-postgresql-datasource
    uid: PCC52D03280B7034C
    access: proxy
    url: ${DB_HOST}:${DB_PORT}
    database: ${DB_NAME}
    user: ${DB_USER}
    secureJsonData:
      password: ${DB_PASSWORD}
    jsonData:
      postgresVersion: 1200
      sslmode: disable
      database: ${DB_NAME}
      connMaxLifetime: 14400
      maxOpenConns: 100
      maxIdleConns: 100
      autoIdle: true
    isDefault: true
    editable: false
