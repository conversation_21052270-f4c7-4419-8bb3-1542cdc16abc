import json

import requests

########## ImageStation
input("Image Station sends a new process with status RUNNING? \n (note: the process cannot be assigned to an evidence item if no result_data is sent, which usually is the case)")
data1 = {"step_id": 1, "status": 1}
print("POST http://localhost:8000/processes")
print(data1)
response1 = requests.post("http://localhost:8000/processes",
               headers={"accept": "application/json", "Content-Type": "application/json"},
               json=data1)
print("Response Code: ", response1.status_code)
#store process id from response
response_data1 = response1.json()
workflow_process_id = response_data1[0]["id"]
print(f"Added process with id {workflow_process_id}")


input("\nImage Station process now FAILED?")
image_folder_path = "path/to/LKA Börlin/AZ-777_Name_Hans/Name_Hans_Ass_99"
result_data = json.dumps({"image_folder_path": image_folder_path,
                   "image_name": "<PERSON>_<PERSON>_Ass_99",
                   "image_file_extension": ".E01"}, ensure_ascii=False)
data1["status"] = 3
data1["result_data"] = result_data
print(f"PATCH http://localhost:8000/processes/{workflow_process_id}")
print(data1)
requests.patch(f"http://localhost:8000/processes/{workflow_process_id}",
               headers={"accept": "application/json", "Content-Type": "application/json"},
               json=data1)

input("\nMake Image Station try again. Add a new process with status RUNNING? \n (note: the process cannot be assigned to an evidence item if no result_data is sent, which usually is the case)")
data1 = {"step_id": 1, "status": 1}
print("POST http://localhost:8000/processes")
print(data1)
response1 = requests.post("http://localhost:8000/processes",
               headers={"accept": "application/json", "Content-Type": "application/json"},
               json=data1)
print("Response Code: ", response1.status_code)
#store process id from response
response_data1 = response1.json()
workflow_process_id = response_data1[0]["id"]
print(f"Added process with id {workflow_process_id}")

input("\nSend FINISHED for this re-tried process? (note: then you will be able to see it in the frontend)")
data1 = {"step_id": 1, "status": 2, "result_data": result_data}
print("POST http://localhost:8000/processes")
print(data1)
response1 = requests.post("http://localhost:8000/processes",
               headers={"accept": "application/json", "Content-Type": "application/json"},
               json=data1)
print("Response Code: ", response1.status_code)
#store process id from response
response_data1 = response1.json()
workflow_process_id = response_data1[0]["id"]
print(f"Added process with id {workflow_process_id}")


input("\nAdd finished ImageStation process manually?")
image_folder_path11 = "path/to/LKA Börlin/AZ-777_Name_Hans/Name_Hans_Ass_50"
result_data11 = json.dumps({"image_folder_path": image_folder_path11,
                   "image_name": "Name_Hans_Ass_50",
                   "image_file_extension": ".E01"}, ensure_ascii=False)
data11 = {"step_id": 1, "status": 2, "result_data": result_data11}
print("POST http://localhost:8000/processes")
print(data11)
response11 = requests.post("http://localhost:8000/processes",
               headers={"accept": "application/json", "Content-Type": "application/json"},
               json=data11)

#### FOCUS
input("\nFOCUS request next job?")
print("http://localhost:8000/forensic-steps/3/next-job")
response2 = requests.get("http://localhost:8000/forensic-steps/3/next-job",
               headers={"accept": "application/json"})
print("Response Code: ", response2.status_code)
if response2.status_code == 200:
    #store process id from response
    response_data2 = response2.json()
    print("Response Data: ", response_data2)
    workflow_process_id2 = response_data2["id"]

    input("\nFOCUS process now RUNNING?")
    data2 = {"status": 1, "result_data": ""}
    print(f"PATCH http://localhost:8000/processes/{workflow_process_id2}")
    print(data2)
    requests.patch(f"http://localhost:8000/processes/{workflow_process_id2}",
                   headers={"accept": "application/json", "Content-Type": "application/json"},
                   json=data2)

    input("\nFOCUS process now FAILED?")
    data2 = {"status": 3, "result_data": ""}
    print(f"PATCH http://localhost:8000/processes/{workflow_process_id2}")
    print(data2)
    requests.patch(f"http://localhost:8000/processes/{workflow_process_id2}",
                   headers={"accept": "application/json", "Content-Type": "application/json"},
                   json=data2)

    input("\nFOCUS process now FINISHED instead?")
    data2 = {"status": 2, "result_data": ""}
    print(f"PATCH http://localhost:8000/processes/{workflow_process_id2}")
    print(data2)
    requests.patch(f"http://localhost:8000/processes/{workflow_process_id2}",
                   headers={"accept": "application/json", "Content-Type": "application/json"},
                   json=data2)
else:
    print("No next job found.")

input("\nIs there again a next FOCUS job?")
print("http://localhost:8000/forensic-steps/3/next-job")
response2 = requests.get("http://localhost:8000/forensic-steps/3/next-job",
               headers={"accept": "application/json"})
print("Response Code: ", response2.status_code)
if response2.status_code == 200:
    print("Yes, it is.")
    response_data2 = response2.json()
    print("Response Data: ", response_data2)
else:
    print("No, it isn't.")

#### InvestiGator
input("\nInvestiGator request next job?")
print("http://localhost:8000/forensic-steps/2/next-job")
response3 = requests.get("http://localhost:8000/forensic-steps/2/next-job",
               headers={"accept": "application/json"})
print("Response Code: ", response3.status_code)
if response3.status_code == 200:
    #store process id from response
    response_data3 = response3.json()
    print("Response Data: ", response_data3)
    workflow_process_id3 = response_data3["id"]

    input("\nInvestiGator process now RUNNING?")
    data3 = {"status": 1, "result_data": ""}
    print(f"PATCH http://localhost:8000/processes/{workflow_process_id3}")
    print(data3)
    requests.patch(f"http://localhost:8000/processes/{workflow_process_id3}",
                   headers={"accept": "application/json", "Content-Type": "application/json"},
                   json=data3)


    input("\nInvestiGator process now FINISHED?")
    data3 = {"status": 2, "result_data": ""}
    print(f"PATCH http://localhost:8000/processes/{workflow_process_id3}")
    print(data3)
    requests.patch(f"http://localhost:8000/processes/{workflow_process_id3}",
                   headers={"accept": "application/json", "Content-Type": "application/json"},
                   json=data3)
else:
    print("No next job found.")

