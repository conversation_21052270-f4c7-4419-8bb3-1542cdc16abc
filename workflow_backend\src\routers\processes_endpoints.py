import logging
from datetime import datetime
from typing import Optional, List

from fastapi import APIRouter, Query, HTTPException

from workflow_backend.src.database.database_helpers import get_unassigned_processes_from_database, \
    add_new_process_to_database, update_process_in_database
from workflow_backend.src.models.processes_models import AddProcessPayload, UpdateProcessPayload
from workflow_backend.src.postprocess_process_utils import postprocessing_on_process_insert_or_update
from sqlalchemy import MetaData, select, delete, and_
from workflow_backend.src.database.database import engine

processes_router = APIRouter(prefix="/processes", tags=["processes"])


@processes_router.get("")
async def get_processes(evidence_item_ids: Optional[List[int]] = Query(None,
                                                                       description="(Optional) List of evidence item IDs to filter by"),
                        step_id: int = Query(None,
                                             description="(Optional) Step id to filter by"
                                                         " **0** for case intake @ LagerDB,"
                                                         " **1** for imaging @ ImageStation,"
                                                         " **2** for XwaysDÜ @ InvestiGator,"
                                                         " **3** for triage @FOCUS.AI,"
                                                         " **4** for it-forensic analysis and report"),
                        status: int = Query(None,
                                            description="(Optional) Status to filter by "
                                                        " **0** for queued,"
                                                        " **1** for running,"
                                                        " **2** for finished,"
                                                        " **3** for failed,"
                                                        " **4** for canceled,"
                                                        " **5** for assigned,"),
                        date_threshold: datetime = Query(None,
                                                         description="(Optional) Filter by processes updated at this date or later"
                                                                     "**(in ISO 8601 format, e.g. 2008-09-15T15:53:00+05:00 or 2008-09-15)**"),
                        ):
    """ Get all processes. Optionally, filter by evidence item IDs, step id and process status."""
    metadata = MetaData()
    metadata.reflect(bind=engine)
    processes = metadata.tables["processes"]

    filters = [
        processes.c.evidence_item_id.in_(evidence_item_ids) if evidence_item_ids else None,
        processes.c.step_id == step_id if step_id is not None else None,
        processes.c.status == status if status is not None else None,
        processes.c.updated_at >= date_threshold if date_threshold else None,
    ]
    # Avoid evaluating SQLAlchemy expressions as bool
    filters = [f for f in filters if f is not None]
    query = select(processes).order_by(
        processes.c.prioritize.desc(),
        processes.c.created_at.asc()
    )

    if filters:
        query = query.where(and_(*filters))

    with engine.connect() as conn:
        results = conn.execute(query).mappings().all()

    return results


@processes_router.get("/unassigned")
async def get_unassigned_processes(date_threshold: datetime = Query(None,
                                                                    description="(Optional) Filter by processes updated at this date or later"
                                                                                "*(in ISO 8601 format, e.g. 2008-09-15T15:53:00+05:00 or 2008-09-15)*")):
    """
    Fetch all unassigned processes meaning processes that
    - have no evidence_item_id
    - OR are linked to an evidence item that has no case_id.
    """
    result = get_unassigned_processes_from_database(date_threshold)
    logging.info(f"Fetched {len(result)} unassigned processes.")
    return result


@processes_router.get("/{process_id}")
async def get_process(process_id: int):
    """ Get the process with the given id."""
    metadata = MetaData()
    metadata.reflect(bind=engine)
    processes = metadata.tables["processes"]
    query = select(processes).where(processes.c.id == process_id)
    with engine.connect() as conn:
        result = conn.execute(query).mappings().first()
    if result is None:
        raise HTTPException(status_code=404, detail=f"Process with id {process_id} not found.")
    return dict(result)


@processes_router.post("")
async def add_process(payload: AddProcessPayload):
    """
    Add a new process.

    If 'result_data' of an ImageStation process is given, it is expected to contain the following fields:
    image_folder_path, image_file_extension, image_name, device_size

    If an ImageStation process has no evidence item id, yet, and contains result data,
    the result data is being parsed to assign the process to an evidence item and a case (both are created if not exist).
    If given, the device size of the evidence item is set to "device_size" of the result data.

    If the status of the process is FINISHED, automatically queue the next step if applicable.
    (The next step can only be queued if the process is assigned to an evidence item.)
    """
    logging.info(f"Adding new process ({payload}) ...")
    added_process = add_new_process_to_database(payload)

    postprocessed_process = postprocessing_on_process_insert_or_update(payload=payload,
                                                                       process_id=added_process[0]['id'],
                                                                       upserted_process=added_process)

    if postprocessed_process is not None:
        return postprocessed_process
    else:
        return added_process


@processes_router.patch("/{process_id}")
async def update_process(process_id: int, payload: UpdateProcessPayload):
    """
    Update the process given by the id.

    If 'result_data' of an ImageStation process is given, it is expected to contain the following fields:
    image_folder_path, image_file_extension, image_name, device_size

    If an ImageStation process has no evidence item id, yet, and contains result data,
    the result data is being parsed to assign the process to an evidence item and a case (both are created if not exist).
    If given, the device size of the evidence item is set to "device_size" of the result data.

    If the status of the process is FINISHED, automatically queue the next step if applicable.
    (The next step can only be queued if the process is assigned to an evidence item.)
    """
    logging.info(f"Updating process with id {process_id} to ({payload}) ...")
    updated_process = update_process_in_database(process_id, payload)

    if not updated_process:
        raise HTTPException(status_code=404, detail="Process ID not found.")

    postprocessed_process = postprocessing_on_process_insert_or_update(payload=payload,
                                                                       process_id=process_id,
                                                                       upserted_process=updated_process)

    if postprocessed_process is not None:
        return postprocessed_process
    else:
        return updated_process


@processes_router.delete("/{process_id}")
async def delete_process(process_id: int):
    """
    Delete the process with the given id.
    """
    logging.info(f"Deleting process with id {process_id}")

    metadata = MetaData()
    metadata.reflect(bind=engine)
    processes = metadata.tables["processes"]

    # first check if the process exists, if not rise an error that process_id does not exist
    select_query = select(processes).where(processes.c.id == process_id)

    with engine.connect() as conn:
        existing = conn.execute(select_query).mappings().first()
        if existing is None:
            raise HTTPException(status_code=404, detail=f"Process with id {process_id} not found.")

        delete_query = (
            delete(processes)
            .where(processes.c.id == process_id)
            .returning(*processes.columns)
        )

        deleted = conn.execute(delete_query).mappings().first()
        conn.commit()

    return {"deleted_process": dict(deleted)}
