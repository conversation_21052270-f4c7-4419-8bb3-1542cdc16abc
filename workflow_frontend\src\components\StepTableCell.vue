<template>
  <q-td :props="tableProps" :class="getTableCellColor()">
    <div class="row no-wrap"
        v-for="(process, index) in processes">

      <q-badge
          :color="getProcessStatusColor(process.status)"
          :label="getProcessStatusDisplayString(process.status)">
        <q-tooltip anchor="center end" self="center left">
          <div style="font-weight: 600">Prozess {{ process.id }}</div>
          <div><span style="font-weight: 600">Start:</span>
            {{ process.started_running_at !== null ? new Date(process.started_running_at).toLocaleString('de-DE') : 'k. A.' }}</div>
          <div><span style="font-weight: 600">Ende:</span>
            {{ process.ended_at !== null ? new Date(process.ended_at).toLocaleString('de-DE') : 'k. A.' }}</div>
          <div><span style="font-weight: 600">zuletzt aktualisiert:</span>
            {{ process.updated_at !== null ? new Date(process.updated_at).toLocaleString('de-DE') : 'k. A.' }}</div>
        </q-tooltip>
      </q-badge>

      <q-badge v-if="[ProcessStatus.QUEUED, ProcessStatus.FAILED].includes(process.status)" :color="getProcessStatusColor(process.status)">
        <q-icon v-if="process.status === ProcessStatus.QUEUED" name="close"
                @click="toggleCancelProcessDialog(process.id)" style="cursor: pointer">
          <q-tooltip anchor="center end" self="center left">Stoppen/Abbrechen</q-tooltip>
        </q-icon>
        <q-icon v-else-if="process.status === ProcessStatus.FAILED" name="restart_alt"
                @click="showRetryProcessDialog = true" style="cursor: pointer">
          <q-tooltip anchor="center end" self="center left">Neustarten</q-tooltip>
        </q-icon>
      </q-badge>
    </div>

  </q-td>

  <q-dialog v-model="showCancelProcessDialog">
    <q-card style="width: 500px">
      <q-card-section>
        <div class="text-h6">Diesen Prozess wirklich stoppen/abbrechen?</div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn flat label="Zurück" color="primary" @click="toggleCancelProcessDialog"/>
        <q-btn flat label="Bestätigen" color="primary" @click="sendProcessStatusCanceled(currentDialogsProcessId)"/>
      </q-card-actions>
    </q-card>
  </q-dialog>

    <q-dialog v-model="showRetryProcessDialog">
    <q-card style="width: 500px">
      <q-card-section>
        <div class="text-h6">Diesen Schritt erneut versuchen?</div>
      </q-card-section>
      <q-card-section style="font-style: italic">
        [Die Funktion ist leider noch nicht verfügbar.]
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="Zurück" color="grey-10" @click="showRetryProcessDialog = false"/>
        <q-btn flat label="Bestätigen" color="primary" disable/>
      </q-card-actions>
    </q-card>
  </q-dialog>


</template>


<script setup>

import {computed, ref} from "vue";
import {Notify} from "quasar";
import {
  getProcessStatusColor,
  getProcessStatusDisplayString,
  ProcessStatus
} from "src/utils/processes_utils.js";

const showCancelProcessDialog = ref(false);
const showRetryProcessDialog = ref(false);

const props = defineProps({
  processes: Array,
  tableProps: Object,
})

function getTableCellColor(){
  // find out what is the highest progress status for any process in the cell
  const progressStatusOrder = [
    ProcessStatus.FAILED,
    ProcessStatus.CANCELED,
    ProcessStatus.QUEUED,
    ProcessStatus.ASSIGNED,
    ProcessStatus.RUNNING,
    ProcessStatus.FINISHED
  ]
  let mostProceededStatus = -1;
  props.processes.forEach(p => {
   if(progressStatusOrder.indexOf(p.status) > progressStatusOrder.indexOf(mostProceededStatus)){
     mostProceededStatus = p.status;
   }
  })
  // determine cell color based on highest process status
  let tableCellColor = 'white';
  switch (mostProceededStatus){
    case -1:
      tableCellColor = 'white'; break;
    case ProcessStatus.QUEUED:
      tableCellColor = 'bg-blue-3'; break;
    case ProcessStatus.RUNNING:
      tableCellColor = 'bg-amber-5'; break;
    case ProcessStatus.FINISHED:
      tableCellColor = 'bg-light-green-8'; break;
    case ProcessStatus.FAILED:
      tableCellColor = 'bg-deep-orange-10'; break;
    case ProcessStatus.CANCELED:
      tableCellColor = 'bg-grey-7'; break;
    case ProcessStatus.ASSIGNED:
      tableCellColor = 'bg-blue-4'; break;
  }
  console.log(props.processes)
  return tableCellColor;
}

const currentDialogsProcessId = ref(null);

const workflowApiUrl = computed(() => {
  let url = new URL(window.location.toString());
  url.port = '8000';
  url.hash = ''
  url.pathname = '/'
  // remove ending '/' (appending path segments later would fail otherwise)
  return url.toString().replace(/\/$/, '');
});

function toggleCancelProcessDialog(processId=null){
  currentDialogsProcessId.value = processId;
  showCancelProcessDialog.value = !showCancelProcessDialog.value;
}

async function sendProcessStatusCanceled(processId){
  try {
    const data = JSON.stringify({status: ProcessStatus.CANCELED});
    const updateProcessUrl = `${workflowApiUrl.value}/processes/${processId}`;
    console.log("PATCH " + updateProcessUrl + " with data " + data);
    const responseCase = await fetch(updateProcessUrl,
        {
          method: 'PATCH',
          headers: {'Content-Type': 'application/json'},
          body: data
        });
    if (!responseCase.ok) throw new Error(`Failed to patch process with id ${processId}.`);

    Notify.create({ message: 'Process erfolgreich gestoppt. Bitte Tabelle neu laden.', type: 'positive'});
    toggleCancelProcessDialog();
  } catch (err) {
    console.log(err)
    Notify.create({message: `Beim Stoppen des Prozesses ${processId} ist ein Fehler aufgetreten!`, type: 'negative', timeout: 10000});
  }
}

</script>

<style scoped>
.q-btn {
  text-transform: none;
}
</style>