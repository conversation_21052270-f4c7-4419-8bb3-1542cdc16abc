<template>

  <!-- <PERSON><PERSON> for entering a process -->
  <q-btn class="addStep4ProcessBtn"
         dense
         :label="btnLabel" icon="check"
         id="addProzessBtn" no-caps
         @click="showAddProcessDialog = true"
  />

      <!-- Popup Dialog for Adding Process -->
      <q-dialog v-model="showAddProcessDialog">
        <q-card style="width: 500px">
          <q-card-section>
            <div class="text-h6">{{btnLabel}}</div>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat label="Abbrechen" color="primary" @click="showAddProcessDialog = false" />
            <q-btn flat label="Bestätigen" color="primary" @click="addProcess" />
          </q-card-actions>
        </q-card>
      </q-dialog>


</template>

<script setup>

import {computed, ref} from "vue";
import {Notify} from "quasar";

const workflowApiUrl = computed(() => {
  let url = new URL(window.location.toString());
  url.port = '8000';
  url.hash = ''
  url.pathname = '/'
  // remove ending '/' (appending path segments later would fail otherwise)
  return url.toString().replace(/\/$/, '');
});

const showAddProcessDialog = ref(false);

const props = defineProps({
  btnLabel: String,
  evidenceItemId: Number,
  stepId: Number,
  processStatus: Number
})

async function addProcess(){
  try {
    const data = JSON.stringify({
      evidence_item_id: props.evidenceItemId,
      step_id: props.stepId,
      status: props.processStatus
    });
    const url = `${workflowApiUrl.value}/processes`
    console.log("POST " + url + " with data " + data)
    const response = await fetch(url, {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: data,
    });

    if (!response.ok) throw new Error('Failed to add process');

    Notify.create({ message: 'Schritt erfolgreich hinzugefügt. Bitte Tabelle neu laden.', type: 'positive' });
    // await fetchEvidenceItemsAndProcesses();
  //   TODO: refresh table
    showAddProcessDialog.value = false
  } catch (err) {
    console.error(err);
    Notify.create({ message: 'Beim Hinzufügen des Prozesses ist ein Fehler aufgetreten!', type: 'negative' });
  }
}

</script>

<style>
.addStep4ProcessBtn{
  font-weight: normal;
  font-size: 11px;
  color: lightgrey;

  :hover{
  color: black;
}
}
</style>
