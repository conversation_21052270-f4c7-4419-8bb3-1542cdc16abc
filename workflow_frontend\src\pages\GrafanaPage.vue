<template>
  <q-page class="q-pa-md" id="monitoringspage-container">
    <q-item class="technical_info">
      ?
      <q-tooltip anchor="center middle" data-html="true">
        Grafana-URL: {{ grafanaDashboardUrl }}
      </q-tooltip>
    </q-item>
    <iframe id="grafanawindow"
            :src="grafanaDashboardUrl" frameborder="0"/>

  </q-page>
</template>

<script setup>
 import {computed} from "vue";

 const grafanaDashboardUrl = computed(() => {
  let url = new URL(window.location.toString());
  url.port = '3001';
  url.hash = '';
  url.pathname = '/';
  const dashboardUrl = url.toString() + "d/8294a2ba-597f-4f61-aaf8-b819b1d402f1/durchsatz-dashboard?orgId=1&kiosk=tv&from=now-14d&to=now&&timezone=browser&theme=light&refresh=1m"
  console.log('Grafana Dashboard URL: ' + dashboardUrl)
  return dashboardUrl;
});
</script>

<style scoped>
#monitoringspage-container{
    margin: 0 0px;
    position: relative;
    background: #fff;
    padding: 0;
    overflow-y: scroll;
}

#grafanawindow{
  border-radius: 16px;
  border: 1px solid #d4d4d4;
  width: 100%;
  height: 97%;
  position: absolute;
}

.technical_info{
    margin: 0 35px;
    color: #d4d4d4;
    padding: 0;
    min-height: 0;
    width: 12px;
    position: absolute;
    bottom: 0; right: 0;
}
</style>
