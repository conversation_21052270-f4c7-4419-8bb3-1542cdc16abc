{"version": 3, "file": "712.js", "mappings": "gMAUO,MAAMA,EAAqB,EAChCC,cACAC,WAAW,OAEX,MAAMC,GAASC,EAAAA,EAAAA,YAAWC,EAAWH,GAErC,OAAKD,EAKH,kBAACK,EAAAA,QAAOA,CAACC,QAAS,aAChB,kBAACC,EAAAA,KAAIA,CAACC,KAAM,cAAeC,KAAK,KAAKC,UAAWR,EAAOS,sBALlD,MAULP,EAAY,CAACQ,EAAsBX,KAChC,CACLU,oBAAoBE,EAAAA,EAAAA,KAAI,CACtBC,MAAO,GAAGb,MACVc,OAAQ,GAAGd,MACXe,gBAAiBJ,EAAMK,OAAOC,QAAQC,KACtCC,KAAMR,EAAMK,OAAOC,QAAQC,KAC3BE,aAAc,MACdC,QAAS,kB,kGC9BR,SAASC,EAAqBC,EAAoBC,GACvD,MAAMC,GAASC,EAAAA,EAAAA,IAAuBH,GAChCI,EAAQC,IACXC,gBAAe,GAEfC,UAAU,gBAAiB,MAC3BC,QAwCH,OAvCAJ,EAAMK,SAAS,CACbC,mBAAoB,CAACC,EAAUC,KAG7BA,EAAQC,cAAiBC,I,IAUHC,EACFA,EAQeD,EACHA,EAnB9B,GAAoB,IAAhBA,EAAKE,OAEP,YADAd,EAAOO,SAAS,CAAEQ,eAAWC,IAG/B,MAAMH,EAAeD,EAAK,GAEpBK,EAAoC,CAAEC,KAAM,SAAUC,IAAKN,GAQjE,GANAI,EAAaG,UAAY,CACvBC,KAAMC,KAAKC,QAAqB,QAAdV,EAAAA,EAAaW,SAAbX,IAAAA,OAAAA,EAAAA,EAAgBQ,OAAQ,GAAK,KAC/CI,GAAIH,KAAKC,QAAqB,QAAdV,EAAAA,EAAaW,SAAbX,IAAAA,OAAAA,EAAAA,EAAgBY,KAAM,GAAK,MAIzCR,EAAaG,UAAUC,OAASJ,EAAaG,UAAUK,GACzD,OAGF,MAAMC,EAAQC,IAA4B,QAATf,EAAAA,EAAK,GAAGgB,SAARhB,IAAAA,OAAAA,EAAAA,EAAWS,OAAQ,GAAK,EAAGtB,GACtD8B,EAAMF,GAA2B,QAATf,EAAAA,EAAK,GAAGgB,SAARhB,IAAAA,OAAAA,EAAAA,EAAWa,KAAM,EAAG1B,GAClDkB,EAAaa,SAAW,CAAET,KAAMK,EAAOD,GAAII,GAE3C7B,EAAO+B,sBAAsBd,IACxBe,EAAAA,EAAAA,GAAoBhC,EAAOiC,MAAMC,aACpClC,EAAOmC,cAAc,eAGvBC,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAeE,oBAAqB,CAC7GzB,UAAWE,EACXwB,OAAQ,iBAKT,IAAIC,EAAAA,GAAgB,CACzBC,UAAW,MACXC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM5C,MAId,CAEO,MAAMC,EAAuB,IAC3B4C,EAAAA,GAAcC,UAClB3C,UAAU,SAAU,CAAE4C,MAAM,IAC5B5C,UAAU,QAAS,CAClB6C,KAAM,IACNC,UAAW,aAEZ9C,UAAU,QAAS,CAClB+C,OAAQ,QACRC,MAAO,KAERhD,UAAU,YAAa,CAAEiD,MAAO,UAG9B,SAAS3B,EAAkB4B,EAAgBC,EAAoBC,GACpE,IAAKD,EACH,MAAO,GAET,GAAID,EAAS,EACX,MAAO,IAGT,MAAMG,EAAWF,EAAQlC,KAAKqC,MAAMJ,KAAYE,GAAc,GAC9D,OAAKC,GAAYE,MAAMF,GACd,GAELA,GAAY,EACP,GAAGA,EAASG,QAAQ,MAEtB,IAAe,IAAXH,GAAiBG,QAAQ,MACtC,C,yFCvFA,MAKazB,EAAuB,CAClC0B,EACAC,EACAC,MAEAC,EAAAA,EAAAA,mBAV4B,EAACH,EAA0BC,IAChD,GAAGG,EAAAA,GAAcC,QAAQ,KAAM,QAAQL,KAAQC,IASpCK,CAAsBN,EAAMC,GAASC,IAG5C3B,EAAoB,CAC/BC,eAAgB,iBAChB+B,KAAM,OACNC,OAAQ,UASG/B,EAAsB,CACjC,CAACF,EAAkBC,gBAAiB,CAClCiC,oBAAqB,sBACrBC,2BAA4B,6BAC5BC,iCAAkC,mCAClCC,kCAAmC,oCACnCC,uCAAwC,yCACxCC,oBAAqB,sBACrBpC,oBAAqB,sBACrBqC,mBAAoB,qBACpBC,WAAY,aACZC,wBAAyB,0BACzBC,6BAA8B,+BAC9BC,wCAAyC,0CACzCC,0BAA2B,4BAC3BC,wBAAyB,0BACzBC,uBAAwB,yBACxBC,0BAA2B,6BAE7B,CAAChD,EAAkBgC,MAAO,CACxBiB,qBAAsB,uBACtBC,kBAAmB,oBACnBC,uBAAwB,yBACxBC,2BAA4B,6BAC5BC,eAAgB,iBAChBC,uBAAwB,0BAE1B,CAACtD,EAAkBiC,QAAS,CAC1BsB,eAAgB,iBAChBC,0BAA2B,4BAC3BC,gBAAiB,kBACjBC,yBAA0B,2BAC1BC,yBAA0B,2BAC1BC,sBAAuB,wBACvBC,uBAAwB,0B,+kBC5CrB,MAAMC,EAAiC,uBACxCC,EAAmB,6CACnBC,EAAyC,yBACzCC,EAAuC,iBAStC,MAAMC,UAAkBC,EAAAA,GA8C7BC,OAAAA,CAAQC,GACFC,KAAK1E,MAAMa,MACb6D,KAAK1E,MAAMa,KAAK2D,QAAQC,EAE5B,CAEAE,QAAAA,CAASC,GACHF,KAAK1E,MAAMa,MACb6D,KAAK1E,MAAMa,KAAK8D,SAASC,EAE7B,CAvDA,WAAAC,CAAY7E,GACV8E,MAAM9E,GACN0E,KAAKK,qBAAqB,KACxB,MAAMH,EAAyB,CAC7B,CACEpH,KAAM,aACNyB,KAAM,SAER,CACEzB,KAAM,UACNwH,cAAe,UACfC,KAAMC,EAAeR,MACrBS,QAAS,IAAMC,MAInBV,KAAKpG,SAAS,CACZuC,KAAM,IAAIwE,EAAAA,GAAa,CACrBT,YAIJ,MAAMU,GAAmBC,EAAAA,EAAAA,IAAyBb,MAC5Cc,GAAQC,EAAAA,EAAAA,IAAcH,GAEtBI,EAA2B,IAAIC,EAAAA,EAAyB,CAC5DC,MAAOlB,KAAK1E,MAAM4F,MAClBJ,U,QAGFE,EAAyBG,WACzBnB,KAAKpG,SAAS,CAAEoH,6BAChBhB,KAAKoB,MAAMC,IACTL,aAAAA,EAAAA,EAA0BM,iBAAiB,KAoFnD,IAA6CC,IAnFLvB,K,cAoFtC,MAAMgB,EAA2BO,EAAKjG,MAAM0F,yBAC5C,GAAIA,EAA0B,C,IAEFO,EAD1B,MAAMC,QAAaC,EAAqBT,G,IACdO,EAA1B,MAAMG,EAAgD,QAA5BH,EAAe,QAAfA,EAAAA,EAAKjG,MAAMa,YAAXoF,IAAAA,OAAAA,EAAAA,EAAiBjG,MAAM4E,aAAvBqB,IAAAA,EAAAA,EAAgC,GACpDI,EAAiCD,EAAkBE,KACtD7B,GAASA,EAAKjH,OAAS0G,G,IA6BpB+B,EAxBFA,EAIAA,EAIAA,EAVAC,IACGG,EAwBCA,IACa,QAAfJ,EAAAA,EAAKjG,MAAMa,YAAXoF,IAAAA,GAAAA,EAAiBtB,SACfyB,EAAkBG,OACf9B,IAK2B,IAJ1B,CACEL,EACAC,EACAH,GACAsC,SAAS/B,EAAKjH,UA/BT,QAAfyI,EAAAA,EAAKjG,MAAMa,YAAXoF,IAAAA,GAAAA,EAAiBzB,QAAQ,CACvBhH,KAAM4G,EACNnF,KAAM,YAEO,QAAfgH,EAAAA,EAAKjG,MAAMa,YAAXoF,IAAAA,GAAAA,EAAiBzB,QAAQ,CACvBhH,KAAM6G,EACNpF,KAAM,UAEO,QAAfgH,EAAAA,EAAKjG,MAAMa,YAAXoF,IAAAA,GAAAA,EAAiBzB,QAAQ,CACvBhH,KAAM0G,EACNc,cAAe,cACfG,QAAUsB,IACJP,EAAKf,SACPe,EAAKf,QAAQsB,IAGftG,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAe0C,kCAmB/C,CACF,E,MAhIM2C,EAAyBpH,U,oUAAS,IAC7BoH,EAAyB1F,O,WAAK,CACjC0G,WAAYhC,KAAK1E,MAAM0G,a,+UAG7B,EAcA,EA1DWpC,EA0DGqC,YAAY,EAAGC,YAC3B,MAAM,KAAE/F,GAAS+F,EAAMC,WAEvB,OAAIhG,EACK,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,IAGzB,uCAIX,MAAMqE,EAAkB0B,IACtB,MAAMtB,GAAmBC,EAAAA,EAAAA,IAAyBqB,GAC5CE,GAAarB,EAAAA,EAAAA,IAAcH,GAC3BnG,EAAY4H,EAAAA,GAAWC,aAAaJ,GAAO5G,MAAMqB,MACjD4F,GAAOC,EAAAA,EAAAA,IAAeN,GAEtBO,EAAeC,KAAKC,UAAU,CAClC,iBAAoB,CAClBC,OAAOC,EAAAA,EAAAA,YAAWpI,EAAUD,KAC5BsI,QAAS,CAAC,CAAEC,MAAO,IAAKX,aAAYlB,MAAOgB,EAAM5G,MAAM4F,MAAOqB,Y,IAGnDS,EAAf,MAAMC,EAAyB,QAAhBD,EAAAA,EAAAA,OAAOE,iBAAPF,IAAAA,EAAAA,EAAoB,GAEnC,OADmBG,EAAAA,QAAQC,UAAU,GAAGH,YAAkB,CAAEI,MAAOZ,EAAca,cAAe,KAI5F5C,EAAiB,MACrBjF,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAeyC,0BAG/EqD,EAA8B8B,GAAAA,EAAAA,YACzC,MAAMxJ,EAAUwJ,EAAoBjI,MAAMvB,QAG1C,YAAgCM,IAA5BmJ,EAAAA,yBACYA,EAAAA,EAAAA,yBAAwB,CACpC/D,mBACA1F,YAGW0J,WAAW,QAIOpJ,IAA7BqJ,EAAAA,gCACyCC,EAAAA,EAAAA,iBACzCD,EAAAA,EAAAA,0BAAyB,CACvBjE,mBACA1F,cAIS,QARf,CAYF,EA1B2CwJ,E,8HC9GpC,MAAMK,UAA0B/D,EAAAA,I,EACX,EAAGqC,YAC3B,MAAM3J,GAAQsL,EAAAA,EAAAA,aACRhM,GAASC,EAAAA,EAAAA,YAAWC,IACpB,UAAE+L,GAAc5B,EAAMC,WAE5B,OACE,kBAAC4B,MAAAA,CAAI1L,UAAWR,EAAOmM,UAAWC,cAAaC,EAAAA,EAAQC,cACrD,kBAACC,EAAAA,EAAaA,CACZC,UAAW9L,EAAMK,OAAO0L,UAAU/L,EAAMK,OAAO2L,WAAWC,WAC1DC,eAAgBlM,EAAMK,OAAO0L,UAAU/L,EAAMK,OAAO2L,WAAWC,UAAW,IAC1ExL,aAAcT,EAAMmM,MAAMC,OAAOC,SAEhCd,Q,EAZK7B,e,EADH2B,G,sFAoBb,MAAMiB,GAASC,EAAAA,EAAAA,WAAU,CACvB,KAAM,CACJC,QAAS,GAEX,OAAQ,CACNA,QAAS,KAIb,SAAShN,IACP,MAAO,CACLiM,WAAWxL,EAAAA,EAAAA,KAAI,CACbwM,MAAO,sBAEPC,cAAeJ,EACfK,eAAgB,QAChBC,wBAAyB,UACzBC,kBAAmB,QACnBC,kBAAmB,cAGzB,C,iBCpDA,IAAIC,EAAM,CACT,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,IACX,aAAc,IACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,IACX,aAAc,IACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,IACX,aAAc,IACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,OAAQ,GACR,UAAW,GACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,aAAc,KACd,gBAAiB,KACjB,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,EACX,aAAc,EACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,WAAY,KACZ,cAAe,KACf,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,KACX,aAAc,KACd,UAAW,IACX,OAAQ,KACR,UAAW,KACX,WAAY,KACZ,cAAe,KACf,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,YAAa,IACb,eAAgB,IAChB,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,QAAS,GACT,WAAY,GACZ,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,QAAS,KACT,aAAc,KACd,gBAAiB,KACjB,WAAY,KACZ,UAAW,IACX,aAAc,IACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,YAAa,IACb,eAAgB,IAChB,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,UAAW,GACX,aAAc,GACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,MAIf,SAASC,EAAeC,GACvB,IAAIC,EAAKC,EAAsBF,GAC/B,OAAOG,EAAoBF,EAC5B,CACA,SAASC,EAAsBF,GAC9B,IAAIG,EAAoBC,EAAEN,EAAKE,GAAM,CACpC,IAAIzD,EAAI,IAAI8D,MAAM,uBAAyBL,EAAM,KAEjD,MADAzD,EAAE+D,KAAO,mBACH/D,CACP,CACA,OAAOuD,EAAIE,EACZ,CACAD,EAAeQ,KAAO,WACrB,OAAOC,OAAOD,KAAKT,EACpB,EACAC,EAAeU,QAAUP,EACzBQ,EAAOC,QAAUZ,EACjBA,EAAeE,GAAK,I,s2BC5Qb,MAAMxE,UAAiCpB,EAAAA,GAC5C,WAAAM,CAAY7E,GACV8E,MAAM,OAAK9E,GAAAA,CAAOwH,QAAS,MAK7B,OAAQsD,cAAc,KACpBpG,KAAKoB,MAAMC,IACTrB,KAAKsB,iBAAiB,KACpBtB,KAAKqG,aACLrG,KAAKsG,kBAKX,OAAiBD,aAAa,KAC5B,MAAME,EAAOlE,EAAAA,GAAWmE,QAAQxG,MAC1ByG,EAAcpE,EAAAA,GAAWqE,WAAWH,EAAMI,GAEhD,GAAIA,EAAcF,GAAc,CAC9B,MAAM3D,EAAU2D,EAAYnL,MAAMwH,QAAQwC,IAAKsB,GAAO,OACjDA,GAAAA,CACH1F,MAAOlB,KAAK1E,MAAM4F,SAGhBwB,KAAKC,UAAUG,KAAaJ,KAAKC,UAAU3C,KAAK1E,MAAMwH,UACxD9C,KAAKpG,SAAS,CAAEkJ,WAEpB,IAGF,OAAiBwD,aAAa,KAC5B,MAAM,QAAExD,EAAO,MAAEhC,EAAK,WAAEkB,EAAU,KAAEzH,EAAO,gBAAmByF,KAAK1E,MAC7Db,EAAY4H,EAAAA,GAAWC,aAAatC,MAE1C,IAAKvF,IAAcqI,IAAYhC,EAC7B,OAEF,MAAM+F,EAAM,CACVC,OAAQ,iBACRvM,OACAuI,UACArI,UAAW,KAAKA,EAAUa,MAAMqB,OAChCyF,WAAY,CAAE2E,IAAKjG,GACnBkG,IAAKC,OAAOC,SAAS3G,KACrBkF,GAAI,GAAG/C,KAAKC,UAAUG,KACtBqE,MAAO,GAAGnF,IACVoF,S,yEAEE1E,KAAKC,UAAUkE,KAASnE,KAAKC,UAAU3C,KAAK1E,MAAMvB,UACpDiG,KAAKpG,SAAS,CAAEG,QAAS8M,MA/C3B7G,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EAmDF,SAAS2G,EAAcf,GACrB,OAAOA,aAAa0B,EAAAA,EACtB,C,uDClFO,MAAMC,EAA4BC,I,IAOlBC,EANrB,MAAMA,EAAgBD,EAAME,OAAO9F,KAAM+F,GAAiB,aAAXA,EAAExP,MAC3CyP,EAAiBJ,EAAME,OAAO9F,KAAM+F,GAAiB,cAAXA,EAAExP,MAElD,IAAI0P,EAAgB,EAChBC,EAAqB,EAEzB,IAAK,IAAIC,EAAI,EAAGA,IAAKN,SAAqB,QAArBA,EAAAA,EAAeO,cAAfP,IAAAA,OAAAA,EAAAA,EAAuBtN,SAAU,GAAI4N,IAAK,CAC7D,MAAME,IAAQL,aAAAA,EAAAA,EAAgBI,OAAOD,KAAM,KAAMN,aAAAA,EAAAA,EAAeO,OAAOD,KAAM,GACzEpN,KAAKuN,IAAID,GAAQtN,KAAKuN,IAAIL,GAAiB,KAC7CA,EAAgBI,EAChBH,EAAqBC,EAEzB,CAEA,MAAO,CAAEF,gBAAeC,uBAGbK,EAAgCrM,IAC3C,GAAe,aAAXA,EAGJ,MAAO,CAAEoF,MAAO,iBAAkB3G,KAAM,Q,yPCXnC,MAAM6N,UAA2BvI,EAAAA,G,kBAAjC,YACL,OAAOY,UAAU,K,IAGA,EAFf,MAAM4H,GAAWC,EAAAA,EAAAA,IAAmBtI,M,IAErB,EAAf,MAAMuI,EAA8D,QAArD,EAA4C,QAA5C,EAAAvI,KAAK1E,MAAMkM,MAAME,OAAO9F,KAAM+F,GAAMA,EAAEY,eAAtC,eAA+CA,cAA/C,QAAyD,CAAC,EACzE,GAAIvI,KAAK1E,MAAMkN,UACb,IAAKD,EAAOvI,KAAK1E,MAAMkN,UACrB,YAGF,GAAmC,IAA/BxC,OAAOD,KAAKwC,GAAQpO,OACtB,O,IAIc,EAAlB,MAAMsO,EAA+B,QAAnB,EAAAzI,KAAK1E,MAAMkN,gBAAX,QAAuBxC,OAAOD,KAAKwC,GAAQ,GACvD5L,GAAQ+L,EAAAA,EAAAA,IAAc1I,KAAK1E,MAAMkM,MAAOxH,KAAK1E,MAAMkN,UAEzDG,EAAaN,EAAUI,EAAW9L,GAElCqD,KAAK1E,MAAMmF,QAAQ,CAAEgI,e,EAGvB,EAvBWL,EAuBGnG,YAAY,EAAGC,Y,IACfA,EACEA,EACA0G,EAAAA,EAFF1G,EAAZ,MAAM2G,EAA2B,QAArB3G,EAAW,QAAXA,EAAAA,EAAM5G,aAAN4G,IAAAA,OAAAA,EAAAA,EAAasG,gBAAbtG,IAAAA,EAAAA,EAAyB,GAC/B0G,EAAmB,QAAX1G,EAAAA,EAAM5G,aAAN4G,IAAAA,OAAAA,EAAAA,EAAasF,MAAME,OAAO7F,OAAQhH,GAAiB,SAAXA,EAAEN,M,IAC1CqO,EAAd,MAAMjM,EAAiC,QAAzBiM,EAAAA,SAAU,QAAVA,EAAAA,EAAQ,UAARA,IAAAA,GAAkB,QAAlBA,EAAAA,EAAYL,cAAZK,IAAAA,OAAAA,EAAAA,EAAqBC,UAArBD,IAAAA,EAAAA,EAA6B,GAG3C,OAFqBE,GAAmBR,EAAAA,EAAAA,IAAmBpG,GAAQ2G,EAAKlM,EAAMa,QAAQ,KAAM,KASrF,qCALH,kBAACuL,EAAAA,OAAMA,CAACC,QAAQ,UAAU5Q,KAAK,KAAKW,KAAK,OAAO0H,QAASyB,EAAMzB,QAASwI,KAAM,eAAe,oBAS9F,MAAMN,EAAe,CAACN,EAAgCrD,EAAerI,KAI1E,MAAMuM,EAAoBb,EAAS/M,MAAM6N,QAAQtH,OAAQ8F,GAAMA,EAAEkB,MAAQO,EAAAA,IAAsBzB,EAAEkB,MAAQ7D,GAIzGqE,QAAQC,UAAU,KAAM,IAExBjB,EAASzO,SAAS,CAChBuP,QAAS,IACJD,EACH,CACEL,IAAK7D,EACLuE,SAAU,IACV5M,MAAOA,OAMFmM,EAAqB,CAAC5G,EAA6B2G,EAAalM,KAC1D2L,EAAAA,EAAAA,IAAmBpG,GACpB5G,MAAM6N,QAAQvH,KAAM+F,GAAMA,EAAEkB,MAAQA,GAAOlB,EAAEhL,QAAUA,E,2KClElE,MAAM6M,EAAyB,EAAGC,kBACvC,MAAM,OAAE3C,IAAW4C,EAAAA,EAAAA,MACZC,EAASC,IAAczH,EAAAA,EAAAA,UAAS,YAYvC,OAAO,kBAAC0H,EAAAA,cAAaA,CAACb,QAAS,SAAUC,KAAM,YAAaU,QAASA,EAASlJ,QAV9D,KACVqJ,UAAUC,YACZD,UAAUC,UAAUC,UAAUlD,GAASmD,EAAAA,EAAAA,IAAqBR,IAC5DG,EAAW,WACXM,WAAW,KACTN,EAAW,aACV,U,q1BCfT,MAAMO,EAAyB,CAC7B,mBACA,2BACA,kBACA,iBACA,wBACA,kCASIC,EAAa,CAAC,cAAe,WAAY,OAAQ,SAEhD,SAASC,GAAwB,QAAEC,EAAO,MAAE3N,EAAK,SAAE4N,I,IAyCb5N,EAxC3C,MAAM9E,GAASC,EAAAA,EAAAA,YAAWC,GAEpByS,GAAMC,EAAAA,EAAAA,SACV,IACEzE,OAAOgC,OACLsC,EAAQI,OAAO,CAACC,EAAKC,KACnB,GAAIA,EAAK5F,MAAO,CACd,MAAMA,EAAQ4F,EAAK5F,MAAM6F,MAAMD,EAAK5F,MAAM8F,QAAQ,KAAO,GAGzD,GAAIX,EAAuBrI,SAAS8I,EAAK5F,OAAQ,C,IACjC2F,EAAd,MAAMI,EAA0B,QAAlBJ,EAAAA,EAAiB,mBAAjBA,IAAAA,EAAAA,EAAsB,CAAE3F,MAAO,cAAesF,QAAS,IACrES,EAAMT,QAAQU,KAAK,OAAKJ,GAAAA,CAAM5F,WAC9B2F,EAAiB,YAAII,CACvB,MAAO,GAAIH,EAAK5F,MAAMiG,WAAW,aAAc,C,IAC/BN,EAAd,MAAMI,EAAuB,QAAfJ,EAAAA,EAAc,gBAAdA,IAAAA,EAAAA,EAAmB,CAAE3F,MAAO,WAAYsF,QAAS,IAC/DS,EAAMT,QAAQU,KAAK,OAAKJ,GAAAA,CAAM5F,WAC9B2F,EAAc,SAAII,CACpB,MACE,GAAIH,EAAK5F,MAAMiG,WAAW,SAAU,C,IACpBN,EAAd,MAAMI,EAAmB,QAAXJ,EAAAA,EAAU,YAAVA,IAAAA,EAAAA,EAAe,CAAE3F,MAAO,OAAQsF,QAAS,IACvDS,EAAMT,QAAQU,KAAK,OAAKJ,GAAAA,CAAM5F,WAC9B2F,EAAU,KAAII,CAChB,KAAO,C,IACSJ,EAAd,MAAMI,EAAoB,QAAZJ,EAAAA,EAAW,aAAXA,IAAAA,EAAAA,EAAgB,CAAE3F,MAAO,QAASsF,QAAS,IACzDS,EAAMT,QAAQU,KAAKJ,GACnBD,EAAW,MAAII,CACjB,CAEJ,CACA,OAAOJ,GACN,CAAC,IACJO,KAAK,CAACC,EAAGC,IAAMhB,EAAWU,QAAQK,EAAEnG,OAASoF,EAAWU,QAAQM,EAAEpG,QACtE,CAACsF,I,IAOwC3N,EAJ3C,OACE,kBAACoH,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACqH,EAAAA,MAAKA,CAACrG,MAAM,qBACX,kBAACsG,EAAAA,OAAMA,CACL3O,MAA6B,MAAtBA,aAAAA,EAAAA,EAAO4O,aAA8C,QAAzB5O,EAAAA,SAAe,QAAfA,EAAAA,EAAO4O,kBAAP5O,IAAAA,OAAAA,EAAAA,EAAmB6O,MAAM,YAAzB7O,IAAAA,EAAAA,EAAuC,GAC1E8O,YAAa,sBACbnB,QAASE,EACTD,SAAW1P,GAAM0P,EAAS1P,EAAEyK,IAAKzK,GAAuBA,EAAE8B,OAAO+O,KAAK,MACtEC,SAAS,EACTC,aAAAA,EACAC,aAAAA,EACAC,OAAQ,kBAAC5T,EAAAA,KAAIA,CAACC,KAAK,eAK7B,CAEA,MAAMJ,EAAY,KACT,CACLiM,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACT8S,SAAU,QAEV,UAAW,CACTtT,MAAO,Y,ycC/CR,MAAMuT,UAAsBnM,EAAAA,GAwBzBoM,oBAAAA,GACN,MAAO,CACL,IAAOC,GACEA,EAAOC,MACZ7G,EAAAA,EAAAA,KAAKiB,GACIA,EAAKjB,IAAK8G,I,IAoCXC,EAnCJ,MAAM3E,EAAS0E,EAAG1E,OACZ2E,EAAY3E,EAAO9F,KAAM+F,GAAiB,cAAXA,EAAExP,MAEjCmS,EAAkC,CACtC/P,KAAM+R,EAAAA,qBAAqBC,OAC3BC,cAAgBC,IACd,MAAMlG,EAAOkG,EAAMjF,MACbkF,EAAenG,aAAAA,EAAAA,EAAMmB,OAAO9F,KAAM+F,GAAiB,kBAAXA,EAAExP,MAC1CwU,EAAcpG,aAAAA,EAAAA,EAAMmB,OAAO9F,KAAM+F,GAAiB,WAAXA,EAAExP,MACzCyU,EAAUF,aAAAA,EAAAA,EAAc1E,OAAOyE,EAAMI,UACrCC,EAASH,aAAAA,EAAAA,EAAa3E,OAAOyE,EAAMI,UAEzC,IAAKD,EACH,OAAOH,EAAM9P,MAGf,MAAMxE,EAAOsU,EAAM9P,MAAS8P,EAAM9P,MAAmB,2BACrD,OACE,kBAACoH,MAAAA,CAAI1L,UAAW,qBACd,kBAAC0L,MAAAA,CACC1L,UAAW,YACX8O,MAAOhP,EACPsI,QAAS,KACPT,KAAK+M,aAAa,IAAIC,EAAAA,GAAiB,CAAEJ,UAASE,YAAW,KAG9D3U,GAEH,kBAAC8U,EAAAA,KAAIA,CAAC1M,KAAMP,KAAKkN,iBAAiBN,EAASE,GAASK,OAAQ,SAAUhG,MAAO,mBAC3E,kBAACjP,EAAAA,KAAIA,CAACC,KAAM,oBAAqBC,KAAM,WASjD,OAHIiU,SAAiB,QAAjBA,EAAAA,EAAWrJ,cAAXqJ,IAAAA,OAAAA,EAAAA,EAAmBe,UACrBf,EAAUrJ,OAAOoK,OAAOC,YAAc/C,G,2WAEjC,MACF8B,GAAAA,CACH1E,eAOd,CAuBQ4F,WAAAA,CAAY/G,G,IAK4BA,EAAAA,EAJ9C,IACEA,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaC,UAC7BjH,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaE,aAC5BlH,aAAAA,EAAAA,EAAMjL,UACNiL,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaG,YAAyB,QAAXnH,EAAAA,EAAKoH,cAALpH,IAAAA,GAAgB,QAAhBA,EAAAA,EAAc,UAAdA,IAAAA,OAAAA,EAAAA,EAAkBpM,UAkBhE,IAAIoM,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaK,OAAQrH,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaG,UACpE,GAA2B,IAAvBnH,EAAKoH,OAAOxT,QAA0C,IAA1BoM,EAAKoH,OAAO,GAAGxT,OAAc,CAC3D,GAA6B,UAAzB6F,KAAK1E,MAAMuS,WAAyB7N,KAAK1E,MAAM/B,MACjD,OAEFyG,KAAKpG,SAAS,CACZiU,UAAW,QACXtU,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAI2R,EAAAA,EAAgB,CACxBC,QAASC,EAAAA,GACTC,cAAeC,EAAAA,GACfC,QAAS,eAMrB,KAAoC,SAAzBnO,KAAK1E,MAAMuS,WACpB7N,KAAKpG,SAAS,CACZiU,UAAW,OACXtU,MAAO,IAAIwC,EAAAA,GAAgB,CACzBC,UAAW,MACXC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAMC,EAAAA,GAAcgS,QACjB3U,gBAAe,GACf4U,aAAcC,GACNA,EACJC,oBAAoB,UACpBC,0BAA0B,UAAU,GACpCD,oBAAoB,gBACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,aACpBC,0BAA0B,QAAS,MAEvC7U,mBA3Df,CAME,GAA6B,YAAzBqG,KAAK1E,MAAMuS,UACb,OAEF7N,KAAKpG,SAAS,CACZiU,UAAW,UACXtU,MAAO,IAAIwC,EAAAA,GAAgB,CACzBC,UAAW,MACXC,SAAU,CACR,IAAI2H,EAAAA,EAAkB,CACpBE,UAAW2K,QAMrB,CA6CF,CAtKA,WAAAtO,CAAY7E,GACV8E,MAAM,GACJyN,UAAW,SACRvS,IA2EP,OAAQ4R,mBAAmB,CAACN,EAAiBE,KAC3C,MAAM4B,GAAwB7N,EAAAA,EAAAA,IAAyBb,MACjDoC,GAAarB,EAAAA,EAAAA,IAAc2N,GAE3BjU,EAAY4H,EAAAA,GAAWC,aAAatC,MAAM1E,MAAMqB,MAChD8F,EAAeC,KAAKC,UAAU,CAClC,iBAAoB,CAClBC,OAAOC,EAAAA,EAAAA,YAAWpI,EAAUD,KAC5BsI,QAAS,CAAC,CAAEC,MAAO,UAAW4L,UAAW,UAAWzN,MAAO0L,EAASxK,eACpEwM,YAAa,CACXC,MAAO,CACL/B,WAGJ1K,gB,IAGWY,EAAf,MAAMC,EAAyB,QAAhBD,EAAAA,EAAAA,OAAOE,iBAAPF,IAAAA,EAAAA,EAAoB,GACnC,OAAOG,EAAAA,QAAQC,UAAU,GAAGH,YAAkB,CAAEI,MAAOZ,EAAca,cAAe,MAwEtF,OAAOiH,WAAYuE,IACjB,MAAMzG,GAAW0G,EAAAA,EAAAA,IAA2B/O,MACxCqI,EAAS2G,aAAeF,IAC1BzG,EAAS4G,cAAcH,IAEvBrT,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAe4C,0BACnC,CACEuQ,eA3KN9O,KAAKK,qBAAqB,KACxBL,KAAKpG,SAAS,CACZsV,MAAO,IAAIC,EAAAA,GAAqB,CAC9BC,gBAAiBpP,KAAKiM,2BAG1B,MAAMoD,EAAYhN,EAAAA,GAAWmE,QAAQxG,MAErCA,KAAKsN,YAAY+B,EAAU/T,MAAMiL,MACjCvG,KAAKoB,MAAMC,IACTgO,EAAU/N,iBAAkBiF,IAC1BvG,KAAKsN,YAAY/G,EAAKA,UAI9B,EAkKA,EAxLWyF,EAwLG/J,YAAY,EAAGC,YAC3B,MAAM,MAAE3I,GAAU2I,EAAMC,WAClBtK,EAASE,GAAU8L,EAAAA,EAAAA,cACnBwE,GAAW0G,EAAAA,EAAAA,IAA2B7M,IACtC,WAAEoN,IAAehW,EAAAA,EAAAA,IAAuB4I,GAAOC,W,IAWpCmN,EATjB,GAAK/V,EAIL,OACE,kBAACwK,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAO0X,QACrB,kBAACxL,MAAAA,CAAI1L,UAAWR,EAAO2X,aAAa,wDACpC,kBAACnF,EAAuBA,CACtBC,QAAyCzP,QAAhCyU,EAAAA,aAAAA,EAAAA,EAAYhK,IAAKzK,IAAM4U,EAAAA,EAAAA,UAAS5U,WAAhCyU,IAAAA,EAAAA,EAAuC,GAChD3S,MAAO0L,EAAS2G,WAChBzE,SAAUrI,EAAMqI,YAGpB,kBAAChR,EAAM0I,UAAS,CAACC,MAAO3I,OAMhC,MAAMxB,EAAaQ,IACV,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,WAET,sBAAuB,CACrBA,QAAS,OACTR,MAAO,QAGT,qBAAsB,CACpBQ,QAAS,OACTyW,IAAK,MACLC,eAAgB,gBAChBC,WAAY,SACZnX,MAAO,OAEP0S,EAAG,CACDgD,QAAS,EACT0B,SAAU,EAEV,SAAU,CACRtL,WAAYhM,EAAMK,OAAO2L,WAAWC,aAK1C,aAAc,CACZsL,MAAOvX,EAAMK,OAAOE,KAAK0I,KACzBuO,OAAQ,UACRC,SAAU,QACVC,SAAU,SACVC,aAAc,WAEd,SAAU,CACRC,eAAgB,gBAItBX,aAAahX,EAAAA,EAAAA,KAAI,CACfqX,SAAUtX,EAAM6X,WAAWC,GAAGR,SAC9B1B,QAAS,GAAG5V,EAAM+X,QAAQ,QAAQ/X,EAAM+X,QAAQ,SAElDf,QAAQ/W,EAAAA,EAAAA,KAAI,CACVS,QAAS,OACT0W,eAAgB,gBAChBC,WAAY,aACZF,IAAK,WAKLjB,EAAoB,KACxB,MAAM5W,GAASC,EAAAA,EAAAA,YAAWyY,GAE1B,OACE,kBAACxM,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAOsP,OACrB,kBAACqJ,EAAAA,EAAQA,CAACC,MAAO,EAAGhY,MAAO,MAE5B,IAAIiY,MAAM,IAAIpL,IAAI,CAACqL,EAAG5I,IACrB,kBAAChE,MAAAA,CAAI1L,UAAWR,EAAO+Y,IAAK/H,IAAKd,GAC9B,IAAI2I,MAAM,IAAIpL,IAAI,CAACqL,EAAGE,IACrB,kBAACC,OAAAA,CAAKzY,UAAWR,EAAOkZ,QAASlI,IAAKgI,GACpC,kBAACL,EAAAA,EAAQA,CAACC,MAAO,UAS/B,SAASF,EAAkBhY,GACzB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbE,OAAQ,OACRD,MAAO,OACPuY,SAAU,WACVrY,gBAAiBJ,EAAMK,OAAO2L,WAAW0M,QACzCC,OAAQ,aAAa3Y,EAAMK,OAAOsY,OAAOC,OACzChD,QAAS,QAEXhH,OAAO3O,EAAAA,EAAAA,KAAI,CACT4Y,aAAc,SAEhBR,KAAKpY,EAAAA,EAAAA,KAAI,CACP4Y,aAAc,MACdnY,QAAS,OACT0W,eAAgB,iBAElBoB,SAASvY,EAAAA,EAAAA,KAAI,CACXC,MAAO,QAGb,C,wHC9UO,MAAM4Y,UAAmBxR,EAAAA,GAOtBuG,WAAAA,G,IAEJ9M,EADF0G,KAAKoB,MAAMC,IAC+B,QAAxC/H,GAAAA,EAAAA,EAAAA,IAAuB0G,MAAM1E,MAAM4T,aAAnC5V,IAAAA,OAAAA,EAAAA,EAA0CgI,iBAAiB,KACzDtB,KAAKsR,gBAITtR,KAAKoB,MAAMC,KACT/H,EAAAA,EAAAA,IAAuB0G,MAAMsB,iBAAiB,CAACiQ,EAAUC,K,IACnDD,EAA8BC,GAAhB,QAAdD,EAAAA,EAASrC,aAATqC,IAAAA,OAAAA,EAAAA,EAAgBjW,MAAMuN,QAAuB,QAAf2I,EAAAA,EAAUtC,aAAVsC,IAAAA,OAAAA,EAAAA,EAAiBlW,MAAMuN,MACvD7I,KAAKsR,gBAKXtR,KAAKoB,MAAMC,KACToQ,EAAAA,EAAAA,IAAkBzR,MAAMsB,iBAAiB,CAACiQ,EAAUC,KAC9CD,EAAS5U,QAAU6U,EAAU7U,OAC/BqD,KAAKsR,gBAKXtR,KAAKsR,YACP,CAEQA,UAAAA,GACNtR,KAAKpG,SAAS,CAAEuC,KAAM,IAAI6P,EAAc,CAAC,IAC3C,CAlCA,WAAA7L,CAAY7E,GACV8E,M,kUAAM,IAAK9E,IAEX0E,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,ECbK,SAAS0R,EAAcZ,GAC5B,GAAIA,EAAKxB,WACP,IAAK,MAAMnE,KAAK2F,EAAKxB,WAAY,C,IAEOnE,EADtC,GAAc,kBAAVA,EAAEtC,IACJ,OAAO8I,SAASxG,EAAExO,MAAMiV,WAAyB,QAAbzG,EAAAA,EAAExO,MAAMkV,aAAR1G,IAAAA,OAAAA,EAAAA,EAAe2G,YAAa,IAAK,GAEzE,CAGF,MAAM,IAAIjM,MAAM,2BAClB,CAEO,SAASkM,EAAejB,GAC7B,GAAIA,EAAKxB,WACP,IAAK,MAAMnE,KAAK2F,EAAKxB,WAAY,C,IAEOnE,EADtC,GAAc,mBAAVA,EAAEtC,IACJ,OAAO8I,SAASxG,EAAExO,MAAMiV,WAAyB,QAAbzG,EAAAA,EAAExO,MAAMkV,aAAR1G,IAAAA,OAAAA,EAAAA,EAAe2G,YAAa,IAAK,GAEzE,CAGF,MAAM,IAAIjM,MAAM,4BAClB,C,wHDuBE,EArCWwL,EAqCGpP,YAAY,EAAGC,YAC3B,MAAM,KAAE/F,GAAS+F,EAAMC,WACvB,OAAOhG,GAAQ,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,ME9CnC,MAAM6V,EAuCXC,OAAAA,CAAQnB,GAEN9Q,KAAKkS,KAAOvX,KAAKwX,IAAIT,EAAcZ,GAAO9Q,KAAKkS,MAC/ClS,KAAKoS,MAAQzX,KAAK0X,IAAIN,EAAejB,GAAO9Q,KAAKoS,OACjDpS,KAAKsS,MAAMtH,KAAK8F,EAClB,CAEAyB,QAAAA,CAASC,GACPA,EAAKnZ,OAAS2G,KACdA,KAAK/D,SAAS+O,KAAKwH,EACrB,CAEAC,OAAAA,CAAQ3B,GACN,OAAOY,EAAcZ,GAAQ9Q,KAAKkS,MAAQH,EAAejB,GAAQ9Q,KAAKoS,KACxE,CAEAM,iBAAAA,CAAkB5B,GAChB,MAAM3Y,EAAOwa,EAAS7B,GAEtB,IAAK,MAAM8B,KAAS5S,KAAK/D,SACvB,GAAI2W,EAAMza,OAASA,EACjB,OAAOya,EAIX,OAAO,IACT,CAtDA,WAAAzS,EAAY,KACVhI,EAAI,YACJ0a,EAAW,cACXC,EAAa,MACbR,EAAK,KACLJ,EAAI,MACJE,EAAK,QACLW,IAjBF5a,EAAAA,KAAAA,YAAAA,GACA0a,EAAAA,KAAAA,mBAAAA,GACAC,EAAAA,KAAAA,qBAAAA,GACAR,EAAAA,KAAAA,aAAAA,GACAJ,EAAAA,KAAAA,YAAAA,GACAE,EAAAA,KAAAA,aAAAA,GACAnW,EAAAA,KAAAA,gBAAAA,GACA5C,EAAAA,KAAAA,cAAAA,GACA0Z,EAAAA,KAAAA,eAAAA,GAmBE/S,KAAK7H,KAAOA,EACZ6H,KAAK6S,YAAcA,EACnB7S,KAAK8S,cAAgBA,EACrB9S,KAAKsS,MAAQA,EACbtS,KAAKkS,KAAOA,EACZlS,KAAKoS,MAAQA,EACbpS,KAAK/D,SAAW,GAChB+D,KAAK3G,OAAS,KACd2G,KAAK+S,QAAUA,CACjB,EA+BK,SAASC,EAAWC,G,IACDA,EAK6BC,EAAAA,EALrD,MAAMA,EAA8B,QAAZD,EAAAA,EAAE3D,kBAAF2D,IAAAA,OAAAA,EAAAA,EAAcrR,KAAMuJ,GAAgB,iBAAVA,EAAEtC,K,IAKrCqK,EAAAA,EACED,EAENA,EAPX,OAAO,IAAIjB,EAAS,CAClBE,KAAMR,EAAcuB,GACpBb,MAAOL,EAAekB,GACtB9a,KAAMwa,EAASM,GACfJ,YAA8F,QAAjFK,EAAkC,QAAlCA,EAAAA,aAAAA,EAAAA,EAAiBvW,MAAMwW,mBAAvBD,IAAAA,EAAAA,EAAsCA,SAAsB,QAAtBA,EAAAA,EAAiBvW,aAAjBuW,IAAAA,GAA6B,QAA7BA,EAAAA,EAAwBrB,aAAxBqB,IAAAA,OAAAA,EAAAA,EAA+BE,oBAArEF,IAAAA,EAAAA,EAAqF,GAClGJ,cAAqB,QAANG,EAAAA,EAAE9a,YAAF8a,IAAAA,EAAAA,EAAU,GACzBX,MAAO,CAACW,GACRF,QAAkB,QAATE,EAAAA,EAAErG,eAAFqG,IAAAA,EAAAA,EAAa,IAE1B,CAEA,SAASN,EAASM,GAChB,IAAII,EAAU,GACd,IAAK,MAAMlI,KAAK8H,EAAE3D,YAAc,GAChB,iBAAVnE,EAAEtC,KAA0BsC,EAAExO,MAAMwW,cACtCE,EAAUlI,EAAExO,MAAMwW,aAItB,MAAO,GAAGE,KAAWJ,EAAE9a,MACzB,CChBA,SAASmb,EAAeC,GACtBA,EAAErB,KAAOsB,OAAOC,iBAChBF,EAAEnB,MAAQoB,OAAOE,iBAEjB,IAAK,MAAMC,KAAKJ,EAAEtX,SAChBqX,EAAeK,EAEnB,C,0cC3CA,MAAMC,EAAe,mBAEd,MAAMC,UAA0BhU,EAAAA,GAiB9BuG,WAAAA,G,IAEH,EADFpG,KAAKoB,MAAMC,IACO,QAAhB,EAAArB,KAAK1E,MAAM4T,aAAX,eAAkB5N,iBAAkBhG,I,IAC9BA,EAA8CA,EAK9CA,EAA2CA,EAL/C,IAAc,QAAVA,EAAAA,EAAMiL,YAANjL,IAAAA,OAAAA,EAAAA,EAAYA,SAAUiS,EAAAA,aAAaC,UAAqB,QAAVlS,EAAAA,EAAMiL,YAANjL,IAAAA,OAAAA,EAAAA,EAAYA,SAAUiS,EAAAA,aAAaG,WAKrF,IAAc,QAAVpS,EAAAA,EAAMiL,YAANjL,IAAAA,OAAAA,EAAAA,EAAYA,SAAUiS,EAAAA,aAAaK,OAAkB,QAAVtS,EAAAA,EAAMiL,YAANjL,IAAAA,OAAAA,EAAAA,EAAYqS,OAAOxT,QAAQ,C,IAC1DmB,EAAd,MAAMkM,EAAkB,QAAVlM,EAAAA,EAAMiL,YAANjL,IAAAA,OAAAA,EAAAA,EAAYqS,OAAO,GAAGjG,OAAO,GAAGM,OAAO,GACrD,GAAIR,EAAO,CACT,MACMsM,EDpEX,SAAqBC,GAC1B,MAAMD,EAAO,IAAI9B,EAAS,CACxB7Z,KAAM,OACN0a,YAAa,GACbC,cAAe,GACfZ,KAAMsB,OAAOE,iBACbtB,MAAOoB,OAAOC,iBACdnB,MAAO,GACPS,QAAS,KAGX,GAAIgB,GAAUA,EAAO5Z,OAAS,EAC5B,IAAK,MAAM0U,KAASkF,EAAQ,C,IACtBlF,EAAJ,GAA+B,KAAb,QAAdA,EAAAA,EAAMmF,gBAANnF,IAAAA,OAAAA,EAAAA,EAAgB1U,QAClB,MAAM,IAAI0L,MAAM,mCAGlB,MAAMoO,EAAiBtC,SAAS9C,EAAMqF,mBAAqB,IAAK,IAE1DC,EAAKtF,EAAMmF,SAAS,GAE1BG,EAAG7B,MAAMpH,KAAK,CAACkJ,EAAIC,IAAO3C,EAAc0C,GAAM1C,EAAc2C,IAG5D,IAAIC,EAAoBR,EAExBR,EAAeQ,GACf,IAAK,MAAMhD,KAAQqD,EAAG7B,MAAO,CAM3B,IAJAxB,EAAKlE,QAAUiC,EAAMkE,QACrBjC,EAAKoD,kBAAoB,IAAGvC,SAASb,EAAKoD,kBAAmB,IAAMD,GAGzC,OAAnBK,EAAQjb,SACTib,EAAQ7B,QAAQ3B,IAGpBwD,EAAUA,EAAQjb,OAIpB,MAAMuZ,EAAQ0B,EAAQ5B,kBAAkB5B,GACxC,GAAI8B,EAAO,CACTA,EAAMX,QAAQnB,GAEdwD,EAAU1B,EACV,QACF,CAGA,MAAM2B,EAAUvB,EAAWlC,GAC3ByD,EAAQxB,QAAUlE,EAAMkE,QACxBuB,EAAQ/B,SAASgC,GACjBD,EAAUC,CACZ,CACF,CAGF,OAAOT,CACT,CCSyBU,CADI9R,KAAK+R,MAAMjN,IAE5BsM,EAAK7X,SAASiP,KAAK,CAACC,EAAGC,IAAMsJ,EAAWtJ,GAAKsJ,EAAWvJ,IAExDnL,KAAKpG,SAAS,CACZ+a,SAAS,EACTb,OACAva,MAAO,IAAIwC,EAAAA,GAAgB,CACzBrD,OAAQ,OACRkc,KAAM,OACN3Y,SAAU+D,KAAK6U,UAAUf,MAG/B,CACF,OArBE9T,KAAKpG,SAAS,CAAE+a,SAAS,MAwBjC,CAEQE,SAAAA,CAAUf,GAChB,OAAOA,EAAK7X,SAASqJ,IAAKsN,GACjB,IAAI1W,EAAAA,GAAc,CACvBxD,OAAQ,IACRD,MAAO,OACPqc,UAAW,QACX3Y,KAAM6D,KAAK+U,SAASnC,KAG1B,CAEQmC,QAAAA,CAASjB,GACf,MAAMrZ,EAAY4H,EAAAA,GAAWC,aAAatC,MACpCtF,EAAOD,EAAUa,MAAMqB,MAAMjC,KAC7BI,EAAKL,EAAUa,MAAMqB,MAAM7B,GAE3Bka,GAAYC,EAAAA,EAAAA,IAAajV,MAE/B,OAAO5D,EAAAA,GAAc2X,SAClBmB,SAAS,iBAAiBpB,EAAKjB,gBAAgB6B,EAAWZ,kBAC1Dpa,UAAU,sBAA8B,CAACkT,EAAiBE,KAClD,CACL3F,MAAO,aACP5G,KAAM,IACNE,QAAS,IAAMuU,EAAUpI,EAASE,GAClChG,OAAQ,CAAC,EACTqG,OAAQ,WAGXgI,QACC,IAAIC,EAAAA,GAAc,CAChB7O,KAAM,CACJjL,MAAOiS,EAAAA,aAAaK,KACpBnT,UAAW,CACTC,OACAI,KACAN,IAAK,CAAEE,OAAMI,OAEf6S,OAAQ,CACN,KACK3N,KAAKqV,UAAUvB,SAM3Bna,OACL,CAEQ0b,SAAAA,CAAUvB,GAChB,MAAMjF,EAAQ7O,KAAKsV,SAASxB,EAAMF,GAC5B2B,EAAY1G,EAAM,GAAGgE,YAAc,IAAMhE,EAAM,GAAGiE,cAExD,OAAO0C,EAAAA,EAAAA,iBAAgB,CACrBrd,KAAM,SAASod,IACfxS,MAAO,SAASwS,IAChB7N,OAAQ,CACN,CACEvP,KAAM,aACNoC,KAAMkb,EAAAA,UAAUC,MAChB1N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAE8a,aAE7B,CACExd,KAAM,UACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAEkY,UAE7B,CACE5a,KAAM,SACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAEgb,SAE7B,CACE1d,KAAM,eACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAEib,eAE7B,CACE3d,KAAM,cACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAEgY,cAE7B,CACE1a,KAAM,gBACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAEiY,gBAE7B,CACE3a,KAAM,WACNoC,KAAMkb,EAAAA,UAAUM,OAChB/N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAEM,WAE7B,CACEhD,KAAM,YACNoC,KAAMkb,EAAAA,UAAUM,OAChB/N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAEmb,YAE7B,CACE7d,KAAM,aACNoC,KAAMkb,EAAAA,UAAUM,OAChB/N,OAAQ6G,EAAMvJ,IAAKzK,GAAMA,EAAEob,eAInC,CAEQX,QAAAA,CAAS9C,EAAgBqD,GAC/B,MAAMK,EAAe1D,EAAKF,MAAM5H,OAC9B,CAACC,EAAKgJ,K,IAAOA,EAAAA,E,MAAqE,WAAzD,QAAZA,EAAAA,EAAErE,kBAAFqE,IAAAA,GAAoC,QAApCA,EAAAA,EAAc/R,KAAMuJ,GAAgB,WAAVA,EAAEtC,YAA5B8K,IAAAA,OAAAA,EAAAA,EAA+ChX,MAAMwW,aAA0BxI,EAAM,EAAIA,GACtG,GAIF,IAAIqL,EAAY,KACZH,IAAWjC,IACboC,EACExD,EAAKF,MAAM5H,OAAO,CAACC,EAAKgJ,IAAMhJ,EAAMgH,SAASgC,EAAEO,kBAAmB,IAAK,GAAK1B,EAAKF,MAAMnY,OAAS,KAGpG,MAAM6N,EAAS,CACb,CAGE2N,WAAYnD,EAAKF,MAAMzH,OAAO,GAAGvF,IAAKzK,IAAO,CAC3Csb,QAAS,WACTpD,QAASlY,EAAE+R,QACXiJ,OAAQhb,EAAEgb,UAEZ9C,QAASP,EAAKO,QACd8C,OAAQrD,EAAKF,MAAM,GAAGuD,OACtBC,aAAcD,EACdhD,YAAaL,EAAKK,YAClBC,cAAeN,EAAKM,cACpBmD,WAAYC,EAAe,EAAI,EAAc,EAC7C/a,SAAUqX,EAAKF,MAAM5H,OAAO,CAACC,EAAKgJ,IAAMhJ,EAAMgH,SAASgC,EAAEyC,cAAe,IAAK,GAAK5D,EAAKF,MAAMnY,OAAS,IACtG6b,cAIJ,IAAK,MAAMpD,KAASJ,EAAKvW,SACvB+L,EAAOgD,QAAQhL,KAAKsV,SAAS1C,EAAOJ,EAAKF,MAAM,GAAGuD,SAEpD,OAAO7N,CACT,CA7LA,WAAA7H,CAAY7E,GACV8E,MAAM,GACJ8O,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BlF,WAAYiU,EAAAA,GACZvT,QAAS,CAACwT,EAAWhb,EAAMQ,WAE7BsT,gBAAiBmH,EAAAA,KAEnB5B,SAAS,GACNrZ,IAGL0E,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EAiSF,SAASsW,EAAWxa,GAClB,IAAI0a,EACAC,EAAiB,GACrB,OAAQ3a,GACN,IAAK,SACH0a,EAAc,iBACdC,EAAiB,iBACjB,MACF,IAAK,WACHD,EAAc,cAAcE,EAAAA,KAC5BD,EAAiB,cAAcE,EAAAA,KAC/B,MACF,QACEH,EAAc,gBAIlB,MAAO,CACLzT,MAAO,IACP7B,MAAO,IAAI0V,EAAAA,MACTH,EAAetc,OAAS,MAAMsc,IAAmB,aACxCD,oGACX7H,UAAW,UACXkI,UAAW,MACXC,MAAO,IACPC,KAAM,GACN5N,QAAS,GAEb,CA5IE,EAhMW0K,EAgMG5R,YAAY,EAAGC,Y,IAWvBgN,EA6FCA,EAvGL,MAAM,KAAE4E,EAAI,QAAEa,EAAO,MAAEpb,EAAK,MAAE2V,GAAUhN,EAAMC,WACxCtK,EAASE,GAAU8L,EAAAA,EAAAA,cACnBtL,GAAQsL,EAAAA,EAAAA,aAER4F,GAAc5I,EAAAA,EAAAA,IAAyBqB,IACvC,MAAEvF,GAAU8M,EAAYgI,oBAAoBtP,WAE5CrG,EAASa,EAEf,IAKI6S,EALAwH,EAAYrC,KAAYb,aAAAA,EAAAA,EAAM7X,SAAS9B,SACvC+U,SAAiB,QAAjBA,EAAAA,EAAO5T,MAAMiL,YAAb2I,IAAAA,OAAAA,EAAAA,EAAmB5T,SAAUiS,EAAAA,aAAaK,OAC5CoJ,GAAY,GAId,IAAIC,EAAW,GACf,OAAQnb,GACN,IAAK,OACH0T,EACE,oCACE,kBAACzL,MAAAA,KAAI,+EACL,kBAACA,MAAAA,KAAI,uFAGTkT,EAAW,SACX,MACF,IAAK,SACHzH,EACE,oCACE,kBAACzL,MAAAA,KAAI,8EACL,kBAACA,MAAAA,KAAI,uFAGTkT,EAAW,QACX,MACF,IAAK,WACHzH,EACE,oCACE,kBAACzL,MAAAA,KAAI,uFACL,kBAACA,MAAAA,KAAI,uFAGTkT,EAAW,OAIf,MAAMC,EAAUC,GAAqBrb,GAE/Bsb,EACJ,oCACE,kBAACC,EAAAA,KAAIA,CAACC,cAAe,SAAUtO,QAAQ,MACpCgF,EAAAA,IAEH,kBAACqJ,EAAAA,KAAIA,CAACC,cAAe,SAAUtO,QAAQ,QACrC,kBAACjF,MAAAA,CAAI1L,UAAWR,EAAO0f,UAAU,2BACNN,EAAS,2FACnBA,EAAS,oDAG5B,kBAACO,EAAAA,MAAKA,CAAC9H,IAAK,GAAKE,WAAY,UAC3B,kBAAC1X,EAAAA,KAAIA,CAACC,KAAK,gBACX,kBAACkf,EAAAA,KAAIA,CAACC,cAAe,SAAUtO,QAAQ,QAAO,mDAKhD,kBAACjF,MAAAA,CAAI1L,UAAWR,EAAO4f,iBAAiB,kBAEtC,kBAAC1T,MAAAA,CAAI1L,UAAWR,EAAOuF,QACrB,kBAACsa,EAAAA,WAAUA,CACTzO,KAAK,oBACLlQ,KAAK,QACLX,KAAM,KACN+U,OAAQ,SACR5M,KACE,yGAGD,GAAG2W,EAAQS,oBAOtB,OACE,kBAACH,EAAAA,MAAKA,CAACxb,UAAW,SAAU0T,IAAK,GAC/B,kBAAC3L,MAAAA,CAAI1L,UAAWR,EAAO2X,aAAcA,GACpCwH,GACC,kBAACQ,EAAAA,MAAKA,CAACxb,UAAW,SAAU0T,IAAK,GAC/B,kBAACc,EAAAA,EAAQA,CACPC,MAAO,EACP/X,OAAQ,IACR2L,UAAW9L,EAAMK,OAAO2L,WAAWC,UACnCC,eAAgBlM,EAAMK,OAAO2L,WAAW0M,YAK5C+F,GAAalD,GAAQA,EAAK7X,SAAS9B,OAAS,GAC5C,kBAAC4J,MAAAA,CAAI1L,UAAWR,EAAO+f,eAAgBre,GAAS,kBAACA,EAAM0I,UAAS,CAACC,MAAO3I,MAGzE2V,SAAiB,QAAjBA,EAAAA,EAAO5T,MAAMiL,YAAb2I,IAAAA,OAAAA,EAAAA,EAAmB5T,SAAUiS,EAAAA,aAAaK,QAASkG,aAAAA,EAAAA,EAAM7X,SAAS9B,SACjE,kBAAC0d,EAAAA,EAAUA,CAAC9J,QAASqJ,EAAejJ,QAAS,YAqCvD,MAAMpW,EAAaQ,IACV,CACLiX,aAAahX,EAAAA,EAAAA,KAAI,CACfqX,SAAUtX,EAAM6X,WAAWC,GAAGR,SAC9B1B,QAAS,GAAG5V,EAAM+X,QAAQ,SAE5BsH,eAAepf,EAAAA,EAAAA,KAAI,CACjBS,QAAS,OACT6e,cAAe,SACfpI,IAAKnX,EAAM+X,QAAQyH,GAEnB,oCAAqC,CACnC9H,SAAU,OACV,yCAA0C,CACxChX,QAAS,SAIb,2EAA4E,CAC1EA,QAAS,QAIX,mBAAoB,CAClBA,QAAS,QAIX,qCAAsC,CACpC,wBAAyB,CACvB8W,OAAQ,SAGZ,+BAAgC,CAC9BA,OAAQ,qBAGZwH,UAAU/e,EAAAA,EAAAA,KAAI,CACZwX,SAAU,QACVgI,OAAQ,WAEV5a,QAAQ5E,EAAAA,EAAAA,KAAI,CACVyf,WAAY1f,EAAM+X,QAAQ,KAE5BmH,iBAAiBjf,EAAAA,EAAAA,KAAI,CACnBS,QAAS,OACT0W,eAAgB,gBAChBC,WAAY,aAKlB,SAAS8E,EAAWZ,GAClB,IAAIrD,EAAQqD,EAAKxB,MAAMnY,OACvB,IAAK,MAAMyY,KAASkB,EAAK7X,SACvBwU,GAASiE,EAAW9B,GAEtB,OAAOnC,CACT,C,cC5ZO,SAASyH,GAAgB,QAAE5N,EAAO,gBAAE6N,EAAe,MAAExb,EAAK,SAAE4N,EAAQ,QAAE6N,GAAU,EAAK,MAAElW,I,IAgFrDmW,EAA0BC,EA/EjE,MAAMzgB,GAASC,EAAAA,EAAAA,YAAWC,GACpBQ,GAAQsL,EAAAA,EAAAA,cACR,SAAEgM,GAAatX,EAAM6X,YAEpBmI,EAAaC,IAAkBrW,EAAAA,EAAAA,UAAiB,KAChDsW,EAAiBC,IAAsBvW,EAAAA,EAAAA,WAAkB,IAEzDwW,EAAgBC,IAAqBzW,EAAAA,EAAAA,UAAiB,GACvD0W,GAAoBC,EAAAA,EAAAA,QAAuB,OAE3C,eAAEC,IAAmBlY,EAAAA,EAAAA,IAAyBqB,GAAOC,YACrD,QAAEgH,IAAYb,EAAAA,EAAAA,IAAmBpG,GAAOC,YACtCxF,MAAOb,IAAW2V,EAAAA,EAAAA,IAAkBvP,GAAOC,WAC7C6W,EAAcld,GAEpBmd,EAAAA,EAAAA,GAAkB,CAChBC,IAAKL,EACLM,SAAU,KACR,MAAMC,EAAUP,EAAkBQ,QAC9BD,GACFR,EAAkBQ,EAAQE,gBAKhC,MAAMjB,GAAe5N,EAAAA,EAAAA,SAAQ,KAC3B,IAAI8O,EAAoB,EACxB,OAAOpB,EACJtW,OAAQ2X,IAEP,IAAIC,IAAWnP,EAAQ1I,KAAMgE,GAAMA,EAAEjJ,QAAU6c,GAG/C,OAAIrQ,EAAQvH,KAAM+F,GAAMA,EAAEkB,MAAQ2Q,IAAsB,MAAf7R,EAAE4B,UAAmC,OAAf5B,EAAE4B,aAM7DJ,EAAQvH,KAAM+F,GAAgB,oBAAVA,EAAEkB,OACxB4Q,EAASA,GAAiB,aAAPD,GAA4B,oBAAPA,GAKtB,SAAhBR,GAA0C,WAAhBA,IAC5BS,EAASA,GAAiB,WAAPD,GAGdC,KAERnU,IAAKoU,IAAe,CACnB1U,MAAO0U,EAAUlc,QAAQmc,EAAAA,GAAW,IAAInc,QAAQoc,EAAAA,GAAe,IAC/D9gB,KAAM4gB,EACN/c,MAAO+c,KAER7X,OAAQgY,IACP,MAAM/gB,EAAO+gB,EAAO7U,OAAS6U,EAAO/gB,MAAQ,GACtCghB,GAAYC,EAAAA,EAAAA,aAAYjhB,EAAM+W,GAAUpX,MAC9C,OAAI8gB,EAAoBO,EA/DD,GACA,IA8D+DnB,IACpFY,GAAqBO,EAhEA,IAiEd,MAKZ,CAAC3B,EAAiB7N,EAASnB,EAAS6P,EAAanJ,EAAU8I,IAExDL,GAAmB7N,EAAAA,EAAAA,SAAQ,KAC/B,MAAMuP,EAAM1P,EAAQzI,OAAQ2X,IAAQnB,EAAazW,KAAMqY,I,IAAoBT,E,OAAbS,EAAGtd,SAAkB,QAAR6c,EAAAA,EAAG7c,aAAH6c,IAAAA,OAAAA,EAAAA,EAAUjO,eACrF,OAAO2O,EAAgBF,EAAKzB,IAC3B,CAACA,EAAajO,EAAS+N,IAEpB8B,EAA4B7P,GACzBA,EACJzI,OAAQ2X,I,IAAmCA,E,OAA3BY,EAAAA,GAAkBtY,SAAiB,QAAR0X,EAAAA,EAAG7c,aAAH6c,IAAAA,OAAAA,EAAAA,EAAUjO,cACrDjG,IAAKkU,I,IAAiBA,E,MAAT,CAAExU,MAAe,QAARwU,EAAAA,EAAGxU,aAAHwU,IAAAA,OAAAA,EAAAA,EAAUhc,QAAQmc,EAAAA,GAAW,IAAInc,QAAQoc,EAAAA,GAAe,IAAKjd,MAAO6c,EAAG7c,S,IAG7Eoc,EAArB,MAAMsB,EAAuD,QAAxCtB,EAAAA,QAAAA,EAAiC,QAAfV,EAAAA,EAAa,UAAbA,IAAAA,OAAAA,EAAAA,EAAiB1b,aAAnCoc,IAAAA,EAAAA,EAA+D,QAAnBT,EAAAA,EAAiB,UAAjBA,IAAAA,OAAAA,EAAAA,EAAqB3b,OAGtF2d,EAAAA,EAAAA,WAAU,KACJD,IAAiBjC,GAAWK,IAC9BlO,EAAS8P,GAAc,GACvB3B,GAAmB,KAEpB,CAAC/b,EAAO0d,EAAcjC,EAAS7N,EAAUkO,KAE5C6B,EAAAA,EAAAA,WAAU,KACJnC,EAAgBhe,OAAS,GAC3Bue,GAAmB,IAEpB,CAACP,KAEJmC,EAAAA,EAAAA,WAAU,KACJnR,EAAQoR,KAAM5S,GAAMA,EAAEkB,MAAQlM,IAChC+b,GAAmB,IAEpB,CAACvP,EAASxM,IAEb,MAAM6d,EAAgBpC,EAAU,CAAC,CAAEpT,MAAOyV,EAAAA,GAAK9d,MAAO8d,EAAAA,KAAS,GACzDC,EAAuBtC,EAAUqC,EAAAA,GAAM,GAE7C,OACE,kBAACpP,EAAAA,MAAKA,CAACrG,MAAM,YACX,kBAACjB,MAAAA,CAAImV,IAAKL,EAAmBxgB,UAAWR,EAAOmM,WAC5CqU,EAAale,OAAS,GACrB,kBAACwgB,EAAAA,iBAAgBA,CAACrQ,QAAS,IAAIkQ,KAAkBnC,GAAe1b,MAAOA,EAAO4N,SAAUA,IAE1F,kBAACe,EAAAA,OAAMA,CACL3O,MAAOA,GAASwd,EAAyB7B,GAAkBiC,KAAM1f,GAAMA,EAAE8B,QAAUA,GAASA,EAAQ,KACpG8O,YAAa,mBACbnB,QAAS6P,EAAyB7B,GAClC/N,SAAWqQ,I,IACWA,EAApB,MAAMC,EAA6B,QAAfD,EAAAA,aAAAA,EAAAA,EAAUje,aAAVie,IAAAA,EAAAA,EAAmBF,EACvCnQ,EAASsQ,IAEXxiB,UAAWR,EAAOijB,OAClBlP,aAAAA,EACAmP,cAAe,CAACpe,GAAiBS,aAChB,iBAAXA,GACFob,EAAe7b,IAGnBqe,YAAa,IAAMxC,EAAe,IAClC3M,aAAAA,KAKV,CAEA,SAAS9T,EAAUQ,GACjB,MAAO,CACLuiB,QAAQtiB,EAAAA,EAAAA,KAAI,CACVwX,SAAUzX,EAAM+X,QAAQ,MAE1BtM,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,KAGzB,CAEO,MAAM4J,EAAkB,CAAC5P,EAAyCpJ,KACvE,GAAuB,IAAnBoJ,EAAQnQ,OACV,MAAO,GAGT,GAAqB,IAAjB+G,EAAM/G,OACR,OAAOmQ,EAAQO,MAAM,EAAGoQ,EAAAA,IAG1B,MAAMC,EAAiBha,EAAMyW,cAC7B,OAAOrN,EACJzI,OAAQsZ,MACHA,EAAIxe,OAASwe,EAAIxe,MAAMxC,OAAS,IAC3BghB,EAAIxe,MAAMgb,cAAc7V,SAASoZ,IAI3CrQ,MAAM,EAAGoQ,EAAAA,K,yHC3KP,MAAMG,WAAuBvb,EAAAA,GAC3Bwb,QAAAA,EAAS,MAAEnZ,IAChB,MAAM,OAAEoZ,EAAM,QAAEhR,GAAYpI,EAAMC,WAElC,OACE,kBAACkJ,EAAAA,MAAKA,CAACrG,MAAM,QACX,kBAAC2V,EAAAA,iBAAgBA,CAACrQ,QAASA,EAAS3N,MAAO2e,EAAQ/Q,SAAUrI,EAAMqZ,iBAGzE,C,kBATK,YAWL,QAAOA,iBAAkBD,IACvBtb,KAAKpG,SAAS,CAAE0hB,YAChB7f,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAesC,oBAAqB,CAC7Gud,OAAQF,K,EAIZ,GAlBWF,GAkBGnZ,YAAY,EAAGC,YAC3B,MAAM,QAAEuZ,EAAO,QAAEnR,EAAO,OAAEgR,GAAWpZ,EAAMC,WAErCuZ,EAAQpR,EAAQqR,UAAW/V,GAAMA,EAAEjJ,QAAU2e,GACnD,IAAe,IAAXI,EACF,OAAO,KAGT,MAAMF,EAASC,EAAQC,GAEvB,OAAO,kBAACF,EAAOvZ,UAAS,CAACC,MAAOsZ,M,gDCxC7B,MAAMI,GAAmB,IACvBxf,EAAAA,GAAcyf,aAClBniB,UAAU,SAAU,CAAEoiB,YAAY,IAClCpiB,UAAU,UAAW,CAAEqiB,KAAMC,EAAAA,mBAAmBC,QAChDC,qBAAqB,cAAe,I,g2BCmBlC,SAASC,GACdhjB,EACAkP,EACA+T,GAEA,MACMtgB,GADmB+E,EAAAA,EAAAA,IAAyB1H,GAClBsY,oBAAoBzC,WAC9C9N,GAAQmb,EAAAA,GAAAA,GAAmBvgB,EAAQuM,EAASiU,gBAC5CC,EAA2C,CAAC,EAElD,OAAO,IAAInB,GAAe,CACxBoB,WAAY,CChCN1iB,IACN,MAAM2iB,EAAS,IAAIC,IAEbC,EAAW7iB,EAAS8iB,iBAAiBC,EAAAA,GAA8BC,IACvE,MAAMnP,EAASmP,EAAMC,QAAQpP,OAE7BA,SAAAA,EAAQqP,QAAS/J,IACfA,EAAEvL,OAAOmD,MAAM,GAAGmS,QAASrV,IACzB8U,EAAOQ,IAAIhK,EAAElQ,MAAiBpI,KAAK0X,OAAO1K,EAAEK,OAAOnG,OAAQqb,GAAMA,SAa3E,SAA8BpjB,EAAuBuY,GAEnD,MAAMwJ,EAAaxZ,EAAAA,GAAW8a,eAAerjB,EAAW8L,GAAMA,aAAawX,EAAAA,IAE3E,IAAK,MAAM7J,KAAKsI,EACdtI,EAAE8J,wBAEF9J,EAAE3Z,SAAS,CACT0jB,aAAaC,EAAAA,GAAAA,QAAMC,EAAAA,GAAAA,WAAUjK,EAAEjY,MAAMgiB,aAAc,CAAEG,SAAU,CAAEpL,UAGvE,CApBMqL,CAAqB5jB,EAAUa,KAAK0X,OAAOoK,EAAOzU,aAGpD,MAAO,KACL2U,EAASgB,iBDiBXzO,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI0O,GAAAA,EAAgB,CACzBC,cAAe,GACfzb,WAAYiU,EAAAA,GACZvT,QAAS,CAAC5B,KAEZkO,gBAAiB,KACZ0O,EAAAA,GAAAA,IAAyB7I,EAAAA,EAAAA,IAAa9b,IACzC,IAAO+S,GACEA,EAAOC,MACZ7G,EAAAA,EAAAA,KAAKiB,IACHA,EAAKyW,QAAS7R,IAAM4S,EAAAA,EAAAA,aAAY,CAAEnV,MAAOuC,EAAEzD,OAAO,GAAIsW,SAAU,CAACC,EAAAA,UAAU5L,QACpE9L,EAAK2E,KAAK,CAACC,EAAGC,K,IACXA,EAAAA,EAAuCD,EAAAA,EAA/C,QAAyB,QAAjBC,EAAAA,EAAE1D,OAAO,GAAGpM,aAAZ8P,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB8S,aAAnB9S,IAAAA,OAAAA,EAAAA,EAA0BiH,MAAO,KAAuB,QAAjBlH,EAAAA,EAAEzD,OAAO,GAAGpM,aAAZ6P,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB+S,aAAnB/S,IAAAA,OAAAA,EAAAA,EAA0BkH,MAAO,WAO5F/H,QAAS,CACP,CAAE3N,MAAO,SAAUqI,MAAO,UAC1B,CAAErI,MAAO,OAAQqI,MAAO,QACxB,CAAErI,MAAO,OAAQqI,MAAO,SAE1BsW,OAAQ,OACRG,QAAS,CACP,IAAI1f,EAAAA,GAAgB,CAClBC,UAAW,SACXC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB4Y,UAAW,IACX3Y,MAAkB,aAAXL,EAAwB8f,KAAmBuC,QAAQ,KAAOvC,MAAoBjiB,aAI3F,IAAIykB,GAAAA,GAAgB,CAClBjiB,KAAM,IAAIkiB,EAAAA,GAAmB,CAC3BC,gBAAiBC,EAAAA,GACjBC,SAAU,QACVC,QAAQ,EACRxiB,SAAU,KAEZyiB,SAAS,EACTC,eAAgBA,GAAepC,EAAQ7T,EAAAA,GAAeL,EAAUvM,EAAQsgB,KAE1E,IAAIgC,GAAAA,GAAgB,CAClBjiB,KAAM,IAAIkiB,EAAAA,GAAmB,CAC3BC,gBAAiB,MACjBE,SAAU,QACVC,QAAQ,EACRxiB,SAAU,KAEZyiB,SAAS,EACTC,eAAgBA,GAAepC,EAAQ7T,EAAAA,GAAeL,EAAUvM,EAAQsgB,OAIhF,CAEO,SAASuC,GACdpC,EACAqC,EACAvW,EACAvM,EACAsgB,GAEA,MAAO,CAAC7V,EAAiBiB,K,IAMNjB,EALjB,MAAMsY,EAAmBrX,EAAMrP,KAAOokB,EAAO/U,EAAMrP,WAAQkC,EAErDykB,EAAW,IAAI1J,EAAAA,GAAc,CACjC7O,KAAM,SACDA,GAAAA,CACHwY,YAA6B,QAAhBxY,EAAAA,EAAKwY,mBAALxY,IAAAA,OAAAA,EAAAA,EAAkB1E,OAAQsJ,GAAMA,EAAEpI,QAAUyE,EAAMzE,OAC/D4K,OAAQ,CACN,SACKnG,GAAAA,CACHE,OAAQF,EAAME,OAAOwD,KAAK,CAACC,EAAGC,K,IAAsCA,EAAhCD,EAAAA,E,OAAQ,QAARA,EAAAA,EAAE5C,cAAF4C,IAAAA,GAAgB,QAAhBA,EAAAA,EAAU6T,cAAV7T,IAAAA,OAAAA,EAAAA,EAAkB8T,eAAsB,QAAR7T,EAAAA,EAAE7C,cAAF6C,IAAAA,OAAAA,EAAAA,EAAU4T,SAAU,MAAO,W,IAOrGH,EADF,GAAIA,EAEF,OAD2B,QAA3BA,EAAAA,EAAiBvjB,MAAMa,YAAvB0iB,IAAAA,GAAAA,EAA6BjlB,SAAS,CAAEsV,MAAO4P,IACxCD,EAGT,MAAM3d,EAAQmB,EAAAA,GAAW6c,YACvB7W,GACA8W,EAAAA,GAAAA,GAAqB,CACnBrjB,SACAsjB,aAAc,GAAG/W,EAASiU,mBAAkB+C,EAAAA,EAAAA,KAAiB3W,EAAAA,EAAAA,IAAclB,SAIzEjO,GAAoB,aAAXuC,EAAwB8f,KAAmBuC,QAAQ,MAAOmB,EAAAA,GAAAA,GAAgBxjB,IACtFoZ,SAAS0J,EAASpX,EAAOa,EAASiU,iBAClCiD,QAAQ,IAAI3f,GAAAA,GAAU,CAAEsB,QAAOc,YAAY0G,EAAAA,EAAAA,IAAclB,MACzD2N,QAAQ2J,GAELU,EAAUpD,EAAU5U,GACtBgY,GACFjmB,EAAMkmB,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCxjB,KAAM5C,EAAMI,UAMd,OAJI6N,EAAMrP,OACRokB,EAAO/U,EAAMrP,MAAQunB,GAGhBA,EAEX,CEzIO,SAASE,IAAsB,YAAEpQ,EAAW,KAAEqQ,IACnD,MACMhoB,GAgBWU,GAjBHsL,EAAAA,EAAAA,aAkBP,CACLic,UAAUtnB,EAAAA,EAAAA,KAAI,CACZS,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,GACnBV,WAAY,SACZzB,QAAS,GAAG5V,EAAM+X,QAAQ,QAAQ/X,EAAM+X,QAAQ,SAElDyP,UAAUvnB,EAAAA,EAAAA,KAAI,CACZS,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,GACnBV,WAAY,WAEduL,KAAK3iB,EAAAA,EAAAA,KAAI,CACPS,QAAS,eACTR,MAAOF,EAAM+X,QAAQ,GACrB5X,OAAQH,EAAM+X,QAAQ,IACtBtX,aAAcT,EAAM+X,QAAQ,QAjBlC,IAAmB/X,EAdjB,OACE,kBAACwL,MAAAA,CAAI1L,UAAWR,EAAOioB,UACrB,kBAAC/b,MAAAA,CAAI1L,UAAWR,EAAOkoB,UAAWvQ,GACjCqQ,EAAK1lB,OAAS,GACb0lB,EAAKva,IAAK6V,GACR,kBAACpX,MAAAA,CAAI1L,UAAWR,EAAOkoB,SAAUlX,IAAKsS,EAAInW,OACxC,kBAACjB,MAAAA,CAAI1L,UAAWR,EAAOsjB,IAAK6E,MAAO,CAAErnB,gBAAiBwiB,EAAIrL,SAC1D,kBAAC/L,MAAAA,KAAKoX,EAAInW,SAKtB,C,yHCWO,MAAMib,WAAiCpgB,EAAAA,GAcpCuG,WAAAA,GACN,MAAMiC,GAAW6X,EAAAA,EAAAA,IAAmBlgB,MAEpCqI,EAAS/G,iBAAiB,KACxBtB,KAAKmgB,QAAQ9X,MAGf/O,EAAAA,EAAAA,IAAuB0G,MAAMsB,iBAAiB,KAC5CtB,KAAKmgB,QAAQ9X,KAGfrI,KAAKmgB,QAAQ9X,EACf,CAEQ+X,gCAAAA,GACN,MAAM/X,GAAW6X,EAAAA,EAAAA,IAAmBlgB,MACpCqI,EAAS4G,cAAcoR,EAAAA,GAAwB,IAC/CrgB,KAAKmgB,QAAQ9X,EACf,CAEQiY,mBAAAA,CAAoBvD,IAC1BthB,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAemC,iCACnCif,EAEJ,CAlCA,WAAA5c,CAAY7E,GACV8E,M,mUAAM,IACD9E,IAPP,QAAUilB,sBAAsB,IAAIC,EAAAA,GAAyBxgB,KAAM,CACjEygB,cAAe,CAACC,EAAAA,GAAaC,EAAAA,IAC7BP,iCAAkCpgB,KAAKogB,iCAAiC/Y,KAAKrH,SAuC/E,QAAQmgB,UAAW9X,IACjBrI,KAAKpG,SAAS,CACZuC,KAAMggB,GAAkBnc,KAAMqI,EAAWb,GAAqB,CAC5D,IAAIY,GAAAA,GAAmB,CAAEZ,QAAOgB,SAAUH,EAASiU,eAAgB7b,QAAST,KAAKsgB,4BAKvF,QAAO/V,WAAW,CAAC5N,EAAeikB,KAChC,MAAMvY,GAAW6X,EAAAA,EAAAA,IAAmBlgB,MAChCqI,EAASiU,iBAAmB3f,IAC9B0L,EAAS4G,cAActS,OAAOtC,GAAYumB,IAE1CnlB,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAekC,2BACnC,CACE6gB,QAAS/hB,OAhDfqD,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EA4IF,SAASjI,GAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbqoB,SAAU,EACV5nB,QAAS,OACT6b,UAAW,OACXgD,cAAe,WAEjB7f,SAASO,EAAAA,EAAAA,KAAI,CACXqoB,SAAU,EACV5nB,QAAS,OACT6nB,WAAYvoB,EAAM+X,QAAQ,KAE5ByQ,UAAUvoB,EAAAA,EAAAA,KAAI,CACZqoB,SAAU,EACV5nB,QAAS,OACT2W,WAAY,MACZF,IAAKnX,EAAM+X,QAAQ,KAErB0Q,eAAexoB,EAAAA,EAAAA,KAAI,CACjBqoB,SAAU,EACV5nB,QAAS,OACT0W,eAAgB,aAElBsR,OAAOzoB,EAAAA,EAAAA,KAAI,CACT0oB,YAAa3oB,EAAM+X,QAAQ,KAE7BoO,SAASlmB,EAAAA,EAAAA,KAAI,CACXC,MAAO,SAET0oB,cAAc3oB,EAAAA,EAAAA,KAAI,CAChBS,QAAS,OACT0W,eAAgB,YAChByR,aAAc,OACd3oB,MAAO,OACPqf,cAAe,QAGrB,C,yHA7HE,GAjEWmI,GAiEGhe,YAAY,EAAGC,YAC3B,MAAQvF,MAAO0kB,IAAiBnB,EAAAA,EAAAA,IAAmBhe,GAAOC,WACpDuc,EAAU2C,EACVC,EAAe5C,EAAQ5c,SAAS6X,EAAAA,KAAc4H,EAAAA,GAAoBzf,SAAS4c,GAAW8C,EAAAA,GAAOC,EAAAA,IAC5FR,EAAOS,IAAYvf,EAAAA,EAAAA,UAASmf,IAC7B,KAAEnlB,GAAS+F,EAAMC,WACjBtK,GAASC,EAAAA,EAAAA,YAAWC,KAEpB,WAAEuX,IAAehW,EAAAA,EAAAA,IAAuB4I,GAAOC,WAC/Cwf,EAAaV,IAAUQ,EAAAA,GAAW7H,EAAAA,GAAgBD,EAAAA,GACxD,IAAIiI,EAAqBtS,aAAAA,EAAAA,EAAYzN,OAAQggB,GAASA,EAAK/f,SAAS6f,IAChEV,IAAUO,EAAAA,KACZI,EAAqBA,aAAAA,EAAAA,EAAoBE,OAAOP,EAAAA,KAGlD,MAAM9X,GAAc5I,EAAAA,EAAAA,IAAyBqB,IACrCvF,MAAOb,GAAW2N,EAAYgI,oBAAoBtP,WAapDqN,EAZiB,CAAC1T,IACtB,OAAQA,GACN,IAAK,OACH,MAAO,+DACT,IAAK,SACH,MAAO,6DACT,IAAK,WACH,MAAO,oDACT,QACE,MAAM,IAAI+J,MAAM,0BAGFkc,CAAejmB,GAQnC,OANAwe,EAAAA,EAAAA,WAAU,KACJ2G,IAAUK,GACZI,EAASJ,IAEV,CAAC5C,IAGF,kBAAC3a,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAAC4b,GAAqBA,CACpBpQ,YAAaA,EACbqQ,KACa,aAAX/jB,EACI,GACA,CACE,CAAEkJ,MAAO,OAAQ8K,MAAO,SACxB,CAAE9K,MAAO,QAAS8K,MAAO,UAKnC,kBAAC/L,MAAAA,CAAI1L,UAAWR,EAAOkpB,WACpBa,aAAAA,EAAAA,EAAoBznB,SACnB,kBAAC4J,MAAAA,CAAI1L,UAAWR,EAAOspB,cACrB,kBAACpd,MAAAA,CAAI1L,UAAWR,EAAOopB,OACrB,kBAAC5V,EAAAA,MAAKA,CAACrG,MAAM,SACX,kBAAC2V,EAAAA,iBAAgBA,CACfrQ,SAAS0X,EAAAA,EAAAA,IAAuB,CAACP,EAAAA,GAAUD,EAAAA,KAC3C7kB,MAAOskB,EACP1W,SAAUmX,MAKhB,kBAAC3d,MAAAA,CAAI1L,UAAWR,EAAO6mB,SACrB,kBAACxG,EAAeA,CACd5N,SAAS0X,EAAAA,EAAAA,IAAuBJ,GAChCzJ,gBAAiB8I,IAAUQ,EAAAA,GAAWpB,EAAAA,GAA0BkB,EAAAA,GAChE5kB,MAAO+hB,EACPnU,SAAUrI,EAAMqI,SAChBrI,MAAOA,MAKd/F,aAAgBif,IACf,kBAACrX,MAAAA,CAAI1L,UAAWR,EAAOmpB,eACrB,kBAAC7kB,EAAKkf,SAAQ,CAACnZ,MAAO/F,MAI5B,kBAAC4H,MAAAA,CAAI1L,UAAWR,EAAOI,SAAUkE,GAAQ,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,QC5KjE,MAAM8lB,WAAuBpiB,EAAAA,GAW1BuG,WAAAA,GACNpG,KAAKsR,YACP,CAEQA,UAAAA,GACNtR,KAAKpG,SAAS,CAAEuC,KAAM,IAAI8jB,GAAyB,CAAC,IACtD,CAZA,WAAA9f,CAAY7E,GACV8E,M,mUAAM,IAAK9E,IALb,QAAUilB,sBAAsB,IAAIC,EAAAA,GAAyBxgB,KAAM,CACjEygB,cAAe,CAACE,EAAAA,OAMhB3gB,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EAUA,GAnBWiiB,GAmBGhgB,YAAY,EAAGC,YAC3B,MAAM,KAAE/F,GAAS+F,EAAMC,WACvB,OAAOhG,GAAQ,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,M,0BCgDnC,SAAS+lB,GAAiBC,GAC/B,IAAKA,EAAWhoB,OAAS,MAAO,GAEhCgoB,EAAWjX,KAAK,CAACC,EAAGC,IAAMD,EAAIC,GAE9B,MACMgX,GADcD,EAAWA,EAAWhoB,OAAS,GAAKgoB,EAAW,IAC5B,IAEjCE,EAAmC,KADfC,EAAAA,GAAAA,IAAoBF,EAAkB,IAE1DvlB,EAAU,IAAI6f,IAEpB,IAAK,MAAM6F,KAAaJ,EAAY,CAClC,MAAMK,EAAY7nB,KAAKqC,MAAMulB,EAAYF,GAAgBA,EACzDxlB,EAAQogB,IAAIuF,GAAY3lB,EAAQ4lB,IAAID,IAAc,GAAK,EACzD,CAGA,OAAO9R,MAAMhW,KAAKmC,EAAQ6lB,WACvBpd,IAAI,EAAEqd,EAAMlS,MAAY,CAAEkS,OAAMlS,WAChCvF,KAAK,CAACC,EAAGC,IAAMD,EAAEwX,KAAOvX,EAAEuX,KAC/B,CAEO,SAASC,GAA0B7U,GACxC,OAAKA,EACEA,EAAQvQ,QAAQ,OAAQ,KAAKqlB,OADb,EAEzB,C,+bC1EO,MAAMC,WAAwBjjB,EAAAA,GAgC3ByN,WAAAA,CAAY/G,G,IAK4BA,EAAAA,EAedA,EAAAA,EAnBhC,IACEA,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaC,UAC7BjH,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaE,cAC5BlH,aAAAA,EAAAA,EAAMjL,SACNiL,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaG,aAAyB,QAAXnH,EAAAA,EAAKoH,cAALpH,IAAAA,GAAgB,QAAhBA,EAAAA,EAAc,UAAdA,IAAAA,OAAAA,EAAAA,EAAkBpM,QAE9D6F,KAAKpG,SAAS,CACZiU,UAAW,UACXtU,MAAO,IAAIwC,EAAAA,GAAgB,CACzBC,UAAW,MACXC,SAAU,CACR,IAAI2H,EAAAA,EAAkB,CACpBE,UAAW2K,eAKd,IACJlI,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaK,OAAQrH,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaG,WAC3C,IAAvBnH,EAAKoH,OAAOxT,SAA4B,QAAXoM,EAAAA,EAAKoH,cAALpH,IAAAA,GAAgB,QAAhBA,EAAAA,EAAc,UAAdA,IAAAA,OAAAA,EAAAA,EAAkBpM,SAiB3C,KACJoM,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaK,OAAQrH,aAAAA,EAAAA,EAAMjL,SAAUiS,EAAAA,aAAaG,YACnEnH,EAAKoH,OAAOxT,OAAS,EACrB,CACA,MAAM4oB,EAAkB/iB,KAAKgjB,yBAAyBzc,GAEtDvG,KAAKpG,SAAS,CACZiU,UAAW,OACXkV,kBACAxpB,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAMC,EAAAA,GAAcgS,QACjB1U,UAAU,aAAcupB,GAAAA,GAAgBC,IACxCzpB,gBAAe,GACf4U,aAAcC,GACNA,EACJC,oBAAoB,WACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,eACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,eACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,aACpBC,0BAA0B,QAAS,MAEvC7U,cAKb,OA9CEqG,KAAKpG,SAAS,CACZiU,UAAW,QACXkV,gBAAiB,EACjBxpB,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAI2R,EAAAA,EAAgB,CACxBC,QAASC,EAAAA,GACTC,cAAeC,EAAAA,GACfC,QAAS,eAsCvB,CAEQgV,oBAAAA,GACN,MAAO,IAAOjX,GACLA,EAAOC,MACZ7G,EAAAA,EAAAA,KAAKiB,GACIA,EAAKjB,IAAK8G,IACf,MAAMgX,EAAehX,EAAG1E,OAAO9F,KAAM+F,GAAiB,sBAAXA,EAAExP,MACvCkrB,EAAYjX,EAAG1E,OAAO9F,KAAM+F,GAAiB,mBAAXA,EAAExP,MACpCmrB,EAAelX,EAAG1E,OAAO9F,KAAM+F,GAAiB,iBAAXA,EAAExP,MACvCorB,EAAYnX,EAAG1E,OAAO9F,KAAM+F,GAAiB,SAAXA,EAAExP,MAG1C,IAAIqrB,EAAqB,GACrBC,EAAkB,GAClBC,EAAwB,GACxBC,EAA0B,GAC1BC,EAAqB,GACrBC,EAA4D,GAEhE,GATgBT,GAAiBA,EAAapb,OAAO7N,OASxC,CACX,MAAM2pB,ED5Jb,SAA6BV,EAA6BC,EAA2BE,EAAwBD,GAClH,MAAMI,EAAc,IAAIhH,IAClB+G,EAAQ,IAAI/G,IACZiH,EAAgB,IAAIjH,IACpBkH,EAAW,IAAIlH,IACfmH,EAAa,IAAInH,IAGjBqH,EAAoB,IAAIrH,IAE9B,IAAK,IAAI3U,EAAI,EAAGA,EAAIqb,EAAapb,OAAO7N,OAAQ4N,IAAK,CACnD,MAAMgG,EAAUqV,EAAapb,OAAOD,GAC9BxN,EAAO8oB,aAAAA,EAAAA,EAAWrb,OAAOD,GACzBwa,EAAYgB,aAAAA,EAAAA,EAAWvb,OAAOD,GAC9Bic,EAAUV,aAAAA,EAAAA,EAActb,OAAOD,GAErC,GAAIgG,EAAS,CACX,MAAMkW,EAAoBrB,GAA0B7U,GAWpD,GAVA2V,EAAYzG,IAAIgH,GAAoBP,EAAYjB,IAAIwB,IAAsB,GAAK,IAE1ER,EAAMS,IAAID,IAAsB1pB,GACnCkpB,EAAMxG,IAAIgH,EAAmB1pB,IAG1BqpB,EAASM,IAAID,IAAsBD,GACtCJ,EAAS3G,IAAIgH,EAAmBD,GAG9BzB,EAAW,CACb,MAAM4B,EAAmC,iBAAd5B,EAAyB6B,WAAW7B,GAAaA,EACvEwB,EAAkBG,IAAID,IACzBF,EAAkB9G,IAAIgH,EAAmB,IAE3CF,EAAkBtB,IAAIwB,GAAoBjZ,KAAKmZ,GAG3CA,GADoBR,EAAclB,IAAIwB,IAAsB,IAE9DN,EAAc1G,IAAIgH,EAAmBE,EAEzC,CACF,CACF,CAGA,IAAK,MAAOpW,EAASoU,KAAe4B,EAAkBrB,UAAW,CAC/D,MAAM2B,EAAiBnC,GAAiBC,GACxC0B,EAAW5G,IAAIlP,EAASsW,EAC1B,CAEA,MAAMC,EAAgB5T,MAAMhW,KAAKgpB,EAAYhB,WAAWxX,KAAK,CAACC,EAAGC,IAAMA,EAAE,GAAKD,EAAE,IAEhF,MAAO,CACLqY,SAAUc,EAAchf,IAAI,EAAEyI,KAAaA,GAC3C0V,MAAOa,EAAchf,IAAI,EAAEyI,KAAa0V,EAAMhB,IAAI1U,IAAY,IAC9D2V,YAAaY,EAAchf,IAAI,EAAE,CAAEmL,KAAWA,GAC9CmT,SAAUU,EAAchf,IAAI,EAAEyI,KAAa6V,EAASnB,IAAI1U,IAAY,IACpE8V,WAAYS,EAAchf,IAAI,EAAEyI,KAAa8V,EAAWpB,IAAI1U,IAAY,IACxE4V,cAAeW,EAAchf,IAAI,EAAEyI,MACjC,MAAMwW,EAAaZ,EAAclB,IAAI1U,GAErC,IAAKwW,EACH,MAAO,GAGT,MACMC,EADMC,KAAKC,MACIH,EAErB,OAAIC,EAAS,IACJ,WACEA,EAAS,KAEX,GADS7pB,KAAKqC,MAAMwnB,EAAS,YAE3BA,EAAS,MAEX,GADO7pB,KAAKqC,MAAMwnB,EAAS,aAI3B,GADM7pB,KAAKqC,MAAMwnB,EAAS,gBAKzC,CC2EiCG,CAAoBvB,EAAcC,EAAWE,EAAWD,GAC3EE,EAAWM,EAAWN,SACtBC,EAAQK,EAAWL,MACnBC,EAAcI,EAAWJ,YACzBC,EAAgBG,EAAWH,cAC3BC,EAAWE,EAAWF,SACtBC,EAAaC,EAAWD,UAC1B,CAEA,MAAMvZ,EAAkC,CACtC/P,KAAM+R,EAAAA,qBAAqBC,OAC3BC,cAAgBC,IACd,MAAMmY,EAAanY,EAAM9P,MACzB,OAAOqD,KAAK6kB,oBAAoBD,KAIpC,O,2WAAO,OACFxY,GAAAA,CACHjS,OAAQqpB,EAASrpB,OACjBuN,OAAQ,CACN,CACEvP,KAAM,UACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQwb,EACRxgB,OAAQ,CACN8hB,MAAOtB,EAASrpB,OAAS,EAAI,CAAC6F,KAAK+kB,kBAAoB,KAG3D,CACE5sB,KAAM,OACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQyb,EACRzgB,OAAQ,CAAC,GAEX,CACE7K,KAAM,gBACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ4b,EACR5gB,OAAQ,CAAC,GAEX,CACE7K,KAAM,cACNoC,KAAMkb,EAAAA,UAAUM,OAChB/N,OAAQ0b,EACR1gB,OAAQ,CAAC,GAEX,CACE7K,KAAM,YACNoC,KAAMkb,EAAAA,UAAUC,MAChB1N,OAAQ6b,EACR7gB,OAAQ,CACNoK,OAAQ,CACNC,YAAa/C,KAInB,CACEnS,KAAM,YACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ2b,EACR3gB,OAAQ,CAAC,SAQzB,CAuFQ+hB,cAAAA,GACN,MAAO,CACL5d,MAAO,iCACPH,IAAK,GACLvG,QAAUqc,I,IACSA,EAAjB,MAAMjQ,EAAuB,QAAZiQ,EAAAA,EAAMhW,cAANgW,IAAAA,OAAAA,EAAAA,EAAcjQ,SAC/B,QAAiBxS,IAAbwS,EAAwB,C,IACViQ,EAAAA,EAAAA,EAAhB,MAAM/O,EAAsB,QAAZ+O,EAAAA,EAAMhW,cAANgW,IAAAA,GAAmB,QAAnBA,EAAAA,EAAclU,aAAdkU,IAAAA,GAA2B,QAA3BA,EAAAA,EAAqB9U,cAArB8U,IAAAA,OAAAA,EAAAA,EAA8BjQ,GAC1CkB,KACFtS,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAe+C,2BAC1FsB,KAAKglB,2BAA2BjX,GAEpC,GAGN,CA8BQkX,iBAAAA,CAAkBtoB,GACxB,OAAOA,EACJa,QAAQ,YAAa,KACrBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,QACfA,QAAQ,KAAM,KACdqlB,MACL,CAEQG,wBAAAA,CAAyBzc,G,IAC1BA,EAAL,KAAKA,SAAY,QAAZA,EAAAA,EAAMoH,cAANpH,IAAAA,OAAAA,EAAAA,EAAe,IAClB,OAAO,EAGT,MAAM2e,EAAmB3e,EAAKoH,OAAO,GAAGjG,OAAO9F,KAAMgH,GAAyB,gBAAfA,EAAMzQ,MACrE,OAAK+sB,aAAAA,EAAAA,EAAkBld,QAIhBkd,EAAiBld,OAAO0C,OAAO,CAACya,EAAexoB,IAAkBwoB,GAASxoB,GAAS,GAAI,GAHrF,CAIX,CAEOyoB,kBAAAA,GACL,OAAOplB,KAAK1E,MAAMynB,iBAAmB,CACvC,CA1VA,WAAA5iB,CAAY7E,GACV8E,MAAM,IACJ8O,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BlF,WAAYiU,EAAAA,GACZvT,QAAS,CCxCV,CACLC,MAAO,IACP7B,MAAO,IAAI0V,EAAAA,wJACXjI,UAAW,UACXkI,UAAW,QACXC,MAAO,IACPC,KAAM,GACN5N,QAAS,ODmCLiG,gBAAiB,KAEnBvB,UAAW,SACRvS,IAsLP,QAAQupB,sBAAuBD,IAC7B,MAAM/sB,GAASC,EAAAA,EAAAA,YAAWC,IAEpBstB,EAAgB,KACpB,MAAM9sB,GAAQsL,EAAAA,EAAAA,aAEd,IAAK+gB,IAAeA,EAAWzqB,OAC7B,OAAO,kBAAC4J,MAAAA,CAAI1L,UAAWR,EAAOytB,kBAAkB,WAGlD,MAAMC,EAAcX,EAAWtf,IAAKkgB,GAAUA,EAAM/U,OAC9CgV,EAAab,EAAWtf,IAAKkgB,GAAUA,EAAM7C,MAE7C+C,EAAmBH,EAAY1jB,OAAQqb,GAAMyI,SAASzI,KAAOjgB,MAAMigB,IACnE0I,EAAkBH,EAAW5jB,OAAQqb,GAAMyI,SAASzI,KAAOjgB,MAAMigB,IACvE,GAAIwI,EAAiBvrB,OAAS,GAAKyrB,EAAgBzrB,OAAS,EAC1D,OAAO,kBAAC4J,MAAAA,CAAI1L,UAAWR,EAAOytB,kBAAkB,mBAGlD,MAAMO,EAAWlrB,KAAKwX,OAAOuT,GACvBI,EAAWnrB,KAAK0X,OAAOqT,GACvBK,EAAUprB,KAAKwX,OAAOyT,GACtBI,EAAUrrB,KAAK0X,OAAOuT,GAGtBK,EAAaH,EAAWD,EACxBK,EAAYF,EAAUD,EAGtBI,EAAgC,IAAfF,EAAmB,EAAIA,EACxCG,EAA8B,IAAdF,EAAkB,EAAIA,EAEtCG,EAAgB,CACpBprB,EAAG,CACD9C,KAAM,QACNoC,KAAMkb,EAAAA,UAAUM,OAChB/N,OAAQ0d,EACR1iB,OAAQ,CAAC,EACT1H,MAAO,CACLsH,MAAO,CACLuP,IAAK0T,EACLxT,IAAKyT,EACLQ,MAAOH,KAIbtrB,EAAG,CACD1C,KAAM,OACNoC,KAAMkb,EAAAA,UAAUkN,KAChB3a,OAAQ4d,EACR5iB,OAAQ,CAAC,EACT1H,MAAO,CACLsH,MAAO,CACLuP,IAAK4T,EACL1T,IAAK2T,EACLM,MAAOF,MAMf,OACE,kBAACriB,MAAAA,CAAI1L,UAAWR,EAAO0uB,oBACrB,kBAACC,EAAAA,UAASA,CACR/tB,MAAO,IACPC,OAAQ,GACR+tB,UAAWJ,EACX9tB,MAAOA,EACPyK,OAAQ,CACNoK,OAAQ,CACNsZ,UAAWC,GAAAA,GAAeC,KAC1BC,YAAa,EACbC,UAAWvuB,EAAMK,OAAO2L,WAAWC,UACnCuiB,UAAW,EACXC,WAAYC,GAAAA,GAAeC,YAQvC,OAAO,kBAAC7B,EAAAA,QAoBV,QAAQL,6BAA8BmC,IACpC,MAAMC,GAAkB9e,EAAAA,EAAAA,IAAmBtI,MAC3C,IAAKonB,EACH,OAGF,MAAMC,GAAsB/tB,EAAAA,EAAAA,IAAuB0G,MACnDqnB,SAAAA,EAAqB7rB,cAAc,aAEnC,MAAM8rB,EAAiBF,EAAgB9rB,MAAM6N,SAAW,GAClDoe,EAAiBvnB,KAAKilB,kBAAkBkC,GAExCK,EAAsBF,EAAe3L,UAAW9Z,GAA0B,4BAAfA,EAAOgH,KAElE4e,EAAY,CAChB5e,IAAK,0BACLU,SAAU,IACV5M,MAAO4qB,GAGHG,EACJF,GAAuB,EACnBF,EAAehiB,IAAI,CAACqC,EAAGI,IAAOA,IAAMyf,EAAsBC,EAAY9f,GACtE,IAAI2f,EAAgBG,GAE1BL,EAAgBxtB,SAAS,CAAEuP,QAASue,MAlTZ1nB,KAAK1E,MAAM4T,MACnBtV,SAAS,CACvBwV,gBAAiB,IAAImH,EAAAA,GAAwCvW,KAAKmjB,0BAGpEnjB,KAAKK,qBAAqB,KACxB,MAAMsnB,EAAkB3nB,KAAK1E,MAAM4T,MAEnClP,KAAKoB,MAAMC,IACTsmB,EAAgBrmB,iBAAiB,CAACiQ,EAAUC,KACtCD,EAAShL,OAASiL,EAAUjL,MAC9BvG,KAAKsN,YAAYiE,EAAShL,UAKpC,EA+TA,GA7VWuc,GA6VG7gB,YAAY,EAAGC,YAC3B,MAAMrK,GAASC,EAAAA,EAAAA,YAAWC,IACpBQ,GAAQsL,EAAAA,EAAAA,cACR,MAAEtK,EAAK,UAAEsU,GAAc3L,EAAMC,WAEnC,OACE,kBAAC4B,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAO2X,aAAa,8EAGrB,YAAd3B,GACC,kBAAC9J,MAAAA,CAAI1L,UAAWR,EAAO+vB,kBACrB,kBAACpX,EAAAA,EAAQA,CACPC,MAAO,GACP/X,OAAQ,GACR2L,UAAW9L,EAAMK,OAAO2L,WAAWC,UACnCC,eAAgBlM,EAAMK,OAAO2L,WAAW0M,WAI7C1X,GAAS,kBAACA,EAAM0I,UAAS,CAACC,MAAO3I,OAM1C,MAAMxB,GAAaQ,IACV,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACT6e,cAAe,SACfpI,IAAKnX,EAAM+X,QAAQ,GACnB5X,OAAQ,SAEV8W,aAAahX,EAAAA,EAAAA,KAAI,CACfqX,SAAUtX,EAAM6X,WAAWC,GAAGR,SAC9B1B,QAAS,GAAG5V,EAAM+X,QAAQ,SAE5BsX,kBAAkBpvB,EAAAA,EAAAA,KAAI,CACpB2V,QAAS5V,EAAM+X,QAAQ,KAEzBiW,oBAAoB/tB,EAAAA,EAAAA,KAAI,CACtBC,MAAO,QACPQ,QAAS,OACT2W,WAAY,SACZD,eAAgB,WAElB2V,kBAAkB9sB,EAAAA,EAAAA,KAAI,CACpBqX,SAAUtX,EAAM6X,WAAWyX,UAAUhY,SACrCC,MAAOvX,EAAMK,OAAOE,KAAK0L,UACzB2J,QAAS5V,EAAM+X,QAAQ,OAKvB7B,GAAoB,KACxB,MAAM5W,GAASC,EAAAA,EAAAA,YAAWyY,IACpBhY,GAAQsL,EAAAA,EAAAA,aAEd,OACE,kBAACE,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACwM,EAAAA,EAAQA,CACPC,MAAO,GACP/X,OAAQ,GACR2L,UAAW9L,EAAMK,OAAO2L,WAAWC,UACnCC,eAAgBlM,EAAMK,OAAO2L,WAAW0M,YAMhD,SAASV,GAAkBhY,GACzB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACb2V,QAAS5V,EAAM+X,QAAQ,KAG7B,C,0BE7cO,SAASwX,GAAgB1tB,G,IAY1BA,EAGAA,EAUkBA,EACFA,EAzBpB,IAAI2tB,EAAW,GAEf,IAAK3tB,EACH,MAAO,KAGLA,EAAU8G,QACZ6mB,GAAY3tB,EAAU8G,OAGxB,MAAM/F,EAAW,IACK,QAAlBf,EAAAA,EAAUe,gBAAVf,IAAAA,OAAAA,EAAAA,EAAoBM,KAAKP,SAC3BgB,EAAS6P,KAAK,eAAe5Q,EAAUe,SAAST,SAE5B,QAAlBN,EAAAA,EAAUe,gBAAVf,IAAAA,OAAAA,EAAAA,EAAoBU,GAAGX,SACzBgB,EAAS6P,KAAK,eAAe5Q,EAAUe,SAASL,MAE9CK,EAAShB,SACP4tB,EAAS5tB,SACX4tB,GAAY,QAEdA,GAAY5sB,EAASuQ,KAAK,SAG5B,MAAMsc,EAAmC,QAAnB5tB,EAAAA,EAAUK,iBAAVL,IAAAA,OAAAA,EAAAA,EAAqBM,KACrCutB,EAAiC,QAAnB7tB,EAAAA,EAAUK,iBAAVL,IAAAA,OAAAA,EAAAA,EAAqBU,GACzC,MAAO,IAAIitB,SACTC,GAAiBC,EAAc,KAAqB,IAAhBD,MAA6C,IAAdC,IAA6B,IAEpG,C,6yBChBO,SAASC,GACd/uB,EACAkP,EACA+T,EACAtgB,G,IAKwByK,EAHxB,MAAM9L,EAAY4H,EAAAA,GAAWC,aAAanJ,GACpCoN,EAAOlE,EAAAA,GAAWmE,QAAQrN,GAC1BugB,EAAYrR,EAASiU,eACrB6L,EAAiC,QAAf5hB,EAAAA,EAAKjL,MAAMiL,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBoH,OAAO/L,KAAMwmB,GAAMA,EAAEjwB,OAASuhB,GACjE2O,EAA2B,GAC3Bhc,EAAY8b,aAAAA,EAAAA,EAAiBzgB,OAAO9F,KAAM+F,GAAiB,UAAXA,EAAExP,MAClDsP,EAAgB0gB,aAAAA,EAAAA,EAAiBzgB,OAAO9F,KAAM+F,GAAiB,aAAXA,EAAExP,MACtDyP,EAAiBugB,aAAAA,EAAAA,EAAiBzgB,OAAO9F,KAAM+F,GAAiB,cAAXA,EAAExP,MAI7D,GAAIkU,GAAa5E,GAAiBG,EAChC,IAAK,IAAIG,EAAI,EAAGA,EAAIsE,EAAUrE,OAAO7N,OAAQ4N,IACtCsE,EAAUrE,OAAOD,KAAQN,EAAcO,OAAOD,IAAOH,EAAeI,OAAOD,KAIhFsgB,EAAYrd,KAAK,CACf7S,KAAMkU,EAAUrE,OAAOD,GAAGvK,QAAQ,KAAM,IACxCrD,OAAQ,EACRuN,OAAQ,CACN,CACEvP,KAAM,QACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ,CAAC,WAAY,cACrBhF,OAAQ,CAAC,GAEX,SACKyE,GAAAA,CACHO,OAAQ,CAACP,EAAcO,OAAOD,IAC9BQ,OAAQ,CACN,CAACmR,GAAYrN,EAAUrE,OAAOD,IAEhC/E,OAAQ,CACNslB,YAAa,cAGjB,SACK1gB,GAAAA,CACHI,OAAQ,CAACJ,EAAeI,OAAOD,SAOzC,OAAO,IAAIqW,GAAAA,GAAgB,CACzBlP,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAIkG,EAAAA,GAAc,CACvB7O,KAAM,CACJ9L,UAAWA,EAAUa,MAAMqB,MAC3BrB,MAAOiS,EAAAA,aAAaK,KACpBD,OAAQ0a,KAGZjZ,gBAAiB,CACf,IAAOlD,GACEA,EAAOC,MACZ7G,EAAAA,EAAAA,KAAKiB,IACHA,EAAKyW,QAAS7R,IAAM4S,EAAAA,EAAAA,aAAY,CAAEnV,MAAOuC,EAAEzD,OAAO,GAAIsW,SAAU,CAACC,EAAAA,UAAU5L,QACpE9L,EAAK2E,KAAK,CAACC,EAAGC,K,IACXA,EAAAA,EAAuCD,EAAAA,EAA/C,QAAyB,QAAjBC,EAAAA,EAAE1D,OAAO,GAAGpM,aAAZ8P,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB8S,aAAnB9S,IAAAA,OAAAA,EAAAA,EAA0BiH,MAAO,KAAuB,QAAjBlH,EAAAA,EAAEzD,OAAO,GAAGpM,aAAZ6P,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB+S,aAAnB/S,IAAAA,OAAAA,EAAAA,EAA0BkH,MAAO,WAO5FlW,KAAM,IAAIkiB,EAAAA,GAAmB,CAC3BC,gBAAiBC,EAAAA,GACjBC,SAAU,QACVC,QAAQ,EACRxiB,SAAU,KAEZ0iB,eAAgBA,GAjE+B,CAAC,EAiET4J,GAAUnM,EAAWtgB,IAEhE,CAEA,MAAMysB,GAAYnc,GACTA,EAAGjU,MAAQ,oBAGpB,SAASwmB,GACPpC,EACAqC,EACAxC,EACAtgB,GAEA,MAAO,CAACyK,EAAiBiB,KACvB,MAAMqX,EAAmBrX,EAAMrP,KAAOokB,EAAO/U,EAAMrP,WAAQkC,EAErDykB,EAAW,IAAI1J,EAAAA,GAAc,CACjC7O,KAAM,SACDA,GAAAA,CACHoH,OAAQ,CACN,MACKnG,Q,IAOTqX,EADF,GAAIA,EAEF,OAD2B,QAA3BA,EAAAA,EAAiBvjB,MAAMa,YAAvB0iB,IAAAA,GAAAA,EAA6BjlB,SAAS,CAAEsV,MAAO4P,IACxCD,EAGT,MAAMtlB,GAAQivB,EAAAA,GAAAA,GAAe1sB,GAAQoZ,SAAS0J,EAASpX,IAAQ2N,QAAQ2J,GAEjEU,EAAUpD,EAAU5U,GACtBgY,GACFjmB,EAAMkmB,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCxjB,KAAM5C,EAAMI,UAMd,OAJI6N,EAAMrP,OACRokB,EAAO/U,EAAMrP,MAAQunB,GAGhBA,EAEX,CCnIO,MAAM+I,WAA+B5oB,EAAAA,I,gBAChB,EAAGqC,WACtBA,EAAM5G,MAAMoe,UAKf,kBAAC3Q,EAAAA,OAAMA,CAACC,QAAQ,YAAY5Q,KAAK,KAAKW,KAAK,QAAQ0H,QAAS,IAAMyB,EAAM5G,MAAMmF,WAAW,WAJlF,M,GAFGwB,e,GADHwmB,I,oOCgCN,MAAMC,WAAkC7oB,EAAAA,GAcrCuG,WAAAA,GACN,MAAMiC,GAAW6X,EAAAA,EAAAA,IAAmBlgB,MAEpCqI,EAAS4G,cAAcwL,EAAAA,IAEvBza,KAAK2oB,aAELtgB,EAAS/G,iBAAiB,CAACiQ,EAAUC,KAC/BD,EAAS5U,QAAU6U,EAAU7U,OAC/BqD,KAAKmgB,QAAQ9X,MAIjBugB,EAAAA,EAAAA,IAAyB5oB,MAAMsB,iBAAiB,KAC9CtB,KAAK2oB,aACL3oB,KAAKmgB,QAAQ9X,MAGf/O,EAAAA,EAAAA,IAAuB0G,MAAMsB,iBAAiB,CAACiQ,EAAUC,MAClDqX,EAAAA,GAAAA,SAAQtX,EAASnX,UAAWoX,EAAUpX,aACzC4F,KAAK2oB,aACL3oB,KAAKmgB,QAAQ9X,MAIjBhG,EAAAA,GAAWC,aAAatC,MAAMsB,iBAAiB,KAC7CtB,KAAK2oB,eAGP3oB,KAAKmgB,QAAQ9X,EACf,CAMQsgB,UAAAA,GACN,MAAMG,GAAiBxvB,EAAAA,EAAAA,IAAuB0G,MACxC+oB,EAAiB1mB,EAAAA,GAAWC,aAAatC,MACzCtF,EAAOquB,EAAeztB,MAAMqB,MAAMjC,KAAKsuB,OACvCluB,EAAKiuB,EAAeztB,MAAMqB,MAAM7B,GAAGkuB,OACnCC,GAAgBL,EAAAA,EAAAA,IAAyB5oB,MAAM1E,MAAMqB,MACrDilB,EAAqB5hB,KAAKkpB,sBAAsBD,GAEtDjpB,KAAKpG,SAAS,CACZsV,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BlF,WAAYiU,EAAAA,GACZvT,QAAS,CAACwT,GAAW5b,EAAMI,EAAIgtB,GAAgBgB,EAAextB,MAAMlB,eAEtEgV,gBAAiB,CACf,IAAOlD,GACEA,EAAOC,MACZ7G,EAAAA,EAAAA,KAAKiB,IACH,MAAM4iB,EAAgBC,GAA0B7iB,GAChD,OAAOP,OAAO0c,QAAQyG,GACnBtnB,OAAO,EAAE6X,EAAW/I,MAAQiR,EAAmB9f,SAAS4X,IACxDpU,IAAI,EAAEoU,EAAW2P,KAAYC,GAAsB5P,EAAW2P,IAC9Dne,KAAK,CAACC,EAAGC,KACR,MAAMme,GAAWhiB,EAAAA,GAAAA,GAAyB4D,GACpCqe,GAAWjiB,EAAAA,GAAAA,GAAyB6D,GAC1C,OAAOzQ,KAAKuN,IAAIshB,EAAS3hB,eAAiBlN,KAAKuN,IAAIqhB,EAAS1hB,wBAQ9E,CAEQuY,gCAAAA,GACN,MAAM/X,GAAW6X,EAAAA,EAAAA,IAAmBlgB,MACpCqI,EAAS4G,cAAcwL,EAAAA,IACvBza,KAAKmgB,QAAQ9X,EACf,CAEQiY,mBAAAA,CAAoBvD,IAC1BthB,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAeoC,kCACnCgf,EAEJ,CA3FA,WAAA5c,CAAY7E,GACV8E,M,mUAAM,IACD9E,IAPP,QAAUilB,sBAAsB,IAAIC,EAAAA,GAAyBxgB,KAAM,CACjEygB,cAAe,CAACC,EAAAA,GAAa+I,EAAAA,IAC7BrJ,iCAAkCpgB,KAAKogB,iCAAiC/Y,KAAKrH,SA2C/E,QAAQkpB,wBAAyBD,GACN,sBAAlBA,EAAwC,CAAC,WAAY,mBAAqB,IAoDnF,QAAQ9I,UAAW9X,IACjB,MAAMzH,GAAmBC,EAAAA,EAAAA,IAAyBb,MAClDA,KAAKpG,SAAS,CACZuC,KACEkM,EAASqhB,eAAiBrhB,EAAS2G,aAAeyL,EAAAA,IAC9CkP,EAAAA,GAAAA,IACGniB,GACC,IAAIihB,GAAuB,CACzB/O,UAAWlS,EAAMrP,KACjBsI,QAAS,IAAMT,KAAKuK,SAAS/C,EAAMrP,MAAQ,MAE/CyI,EAAiBgpB,qBAEnB1B,GACEloB,KACAqI,EACCb,GAAqB,CACpB,IAAIY,GAAAA,GAAmB,CACrBZ,QACAgB,SAAUH,EAASiU,eACnB7b,QAAST,KAAKsgB,uBAGlB1f,EAAiBgpB,yBAK7B,QAAOrf,WAAW,CAAC5N,EAAeikB,MACfV,EAAAA,EAAAA,IAAmBlgB,MAC3BiP,cAActS,OAAOtC,GAAYumB,IAE1CnlB,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAeqC,uCACnC,CAAErB,YA3HJqD,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EAoLK,SAASsW,GAAW5b,EAAcI,EAAY+uB,GACnD,MACMC,EAAY,IADN3uB,EAAAA,GAAAA,UAASL,EAAKJ,EAAM,KACPqvB,eACzB,MAAO,CACLhnB,MAAO,IACP7B,MAAO,IAAI0V,EAAAA,iBAA+BiT,KAC1CtnB,KAAMunB,EACNnb,UAAW,UACXkI,UAAW,QACXC,MAAO,IACPC,KAAM,GACN5N,QAAS,GAEb,CAnEE,GA1IWuf,GA0IGzmB,YAAY,EAAGC,YAC3B,MAAM,KAAE/F,GAAS+F,EAAMC,WACjBkG,GAAW6X,EAAAA,EAAAA,IAAmBhe,GAC9BtB,GAAmBC,EAAAA,EAAAA,IAAyBqB,IAC5C,WAAEoN,IAAehW,EAAAA,EAAAA,IAAuB4I,GAAOC,WAC/CtK,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAAC4b,GAAqBA,CACpBpQ,YAAY,qGACZqQ,KAAM,CACJ,CACE7a,MAAO,WACP8K,MAC2C,aAAzClP,EAAiBgpB,oBACbI,GAAAA,IACAC,EAAAA,EAAAA,YAAWC,cAAcC,eAAe,oBAEhD,CACEnlB,MAAO,YACP8K,MAC2C,aAAzClP,EAAiBgpB,oBACbQ,GAAAA,IACAH,EAAAA,EAAAA,YAAWC,cAAcC,eAAe,qBAKpD,kBAACpmB,MAAAA,CAAI1L,UAAWR,EAAOkpB,WACpBzR,aAAAA,EAAAA,EAAYnV,SACX,kBAAC4J,MAAAA,CAAI1L,UAAWR,EAAOspB,cACrB,kBAACjJ,EAAeA,CACd5N,SAAS0X,EAAAA,EAAAA,IAAuB1S,GAChC6I,gBAAiBoJ,EAAAA,GACjB5kB,MAAO0L,EAASiU,eAChB/R,SAAUrI,EAAMqI,SAChB6N,SAAS,EACTlW,MAAOA,KAIZ/F,aAAgBif,IACf,kBAACrX,MAAAA,CAAI1L,UAAWR,EAAOmpB,eACrB,kBAAC7kB,EAAKkf,SAAQ,CAACnZ,MAAO/F,MAI5B,kBAAC4H,MAAAA,CAAI1L,UAAWR,EAAOI,SAAUkE,GAAQ,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,QAqBxE,MAAMitB,GAA6BC,GAC1BA,EAAO3e,OAAO,CAACC,EAAkCgD,KACtD,MAAM0c,EAAc1c,EAAOjG,OAAO9F,KAAMgH,GAAyB,WAAfA,EAAMrO,MAClD+vB,EAAiBtkB,OAAOD,MAAKskB,aAAAA,EAAAA,EAAa9hB,SAAU,CAAC,GAAG3G,KAAMiH,IAASA,EAAIoC,WAAW,OAI5F,OAHIqf,IACF3f,EAAI2f,GAAkB,IAAK3f,EAAI2f,IAAmB,GAAK3c,IAElDhD,GACN,CAAC,GAGA2e,GAAwB,CAAC5P,EAAmB2P,KAChD,MAAMkB,EAAsB,CAC1BpyB,KAAMuhB,EACN3W,MAAO2W,EACPhS,OAAQ,GACRvN,OAAQ,GAGJqwB,EAAwB,CAC5BryB,KAAM,QACNoC,KAAMkb,EAAAA,UAAUG,OAChB5N,OAAQ,GACRhF,OAAQ,CAAC,EACTuF,OAAQ,CAAE,CAACmR,GAAYA,IAEnBjS,EAAuB,CAC3BtP,KAAM,WACNoC,KAAMkb,EAAAA,UAAUM,OAChB/N,OAAQ,GACRhF,OAAQ,CAAC,GAEL4E,EAAwB,CAC5BzP,KAAM,YACNoC,KAAMkb,EAAAA,UAAUM,OAChB/N,OAAQ,GACRhF,OAAQ,CAAC,GAGLgF,EAASqhB,EAAO3e,OAAO,CAACC,EAA8BnD,K,IAE9C6iB,EADZ,MAAMA,EAAc7iB,EAAME,OAAO9F,KAAMgH,GAAyB,WAAfA,EAAMrO,MACjDkwB,EAAMJ,SAAmB,QAAnBA,EAAAA,EAAa9hB,cAAb8hB,IAAAA,OAAAA,EAAAA,EAAsB3Q,GAIlC,OAHI+Q,IACF9f,EAAI8f,GAAO,IAAK9f,EAAI8f,IAAQ,GAAKJ,IAE5B1f,GACN,CAAC,GAEE+f,EAAgBC,GAAoBtB,EAAQ,WAAYrhB,GACxD4iB,EAAiBD,GAAoBtB,EAAQ,YAAarhB,GAchE,OAZAuiB,EAASpwB,OAAS6L,OAAOD,KAAKiC,GAAQ7N,OAEtC6L,OAAO0c,QAAQ1a,GAAQgV,QAAQ,EAAErgB,EAAO+K,M,IAGpCA,EAGAA,EALF8iB,EAAexiB,OAAOgD,KAAKrO,GAC3B8K,EAAcO,OAAOgD,M,QACnBtD,EAAAA,EAAO9F,KAAMgH,I,IAAUA,E,MAAkC,gBAAtB,QAAZA,EAAAA,EAAML,cAANK,IAAAA,OAAAA,EAAAA,EAA4B,sBAAnDlB,IAAAA,OAAAA,EAAAA,EAAwEM,OAAO,IAAK0iB,GAEtF9iB,EAAeI,OAAOgD,M,QACpBtD,EAAAA,EAAO9F,KAAMgH,I,IAAUA,E,MAAkC,iBAAtB,QAAZA,EAAAA,EAAML,cAANK,IAAAA,OAAAA,EAAAA,EAA4B,sBAAnDlB,IAAAA,OAAAA,EAAAA,EAAyEM,OAAO,IAAK4iB,KAGzFL,EAAS7iB,OAAS,CAAC8iB,EAAgB/iB,EAAeG,GAC3C2iB,GAGT,SAASI,GAAoBtB,EAAqBwB,EAAkB7iB,GAElE,MAAM8iB,EAAkB9kB,OAAOgC,OAAOA,GAAQ0C,OAAO,CAACya,EAAOzd,KAC3D,MAAMkB,EAAQlB,EAAO9F,KAAMgH,I,IAAUA,E,OAAY,QAAZA,EAAAA,EAAML,cAANK,IAAAA,OAAAA,EAAAA,EAA4B,eAAM,IAAIiiB,OAC3E,OAAO1F,IAASvc,aAAAA,EAAAA,EAAOZ,OAAO,KAAM,IACnC,GAEH,IAAImd,EAAQkE,EAAO3e,OAAO,CAACqgB,EAAcvjB,K,IAEnCoB,EADJ,MAAMA,EAAQpB,EAAME,OAAO9F,KAAM+F,GAAiB,WAAXA,EAAEpN,MACzC,OAAIqO,SAAa,QAAbA,EAAAA,EAAOL,cAAPK,IAAAA,OAAAA,EAAAA,EAA6B,eAAM,IAAIiiB,WAClCjiB,EAAMZ,OAAO,GAEf+iB,GACN,GAOH,OAAI5F,EAAQ2F,GAME,IAAV3F,GAAyB,IAAVA,EALU,IAApB2F,EAAwB,EAAIA,EAS9B3F,CACT,CAEA,SAASptB,GAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbqoB,SAAU,EACV5nB,QAAS,OACT6b,UAAW,OACXgD,cAAe,WAEjB7f,SAASO,EAAAA,EAAAA,KAAI,CACXqoB,SAAU,EACV5nB,QAAS,OACT6nB,WAAYvoB,EAAM+X,QAAQ,KAE5ByQ,UAAUvoB,EAAAA,EAAAA,KAAI,CACZqoB,SAAU,EACV5nB,QAAS,OACT2W,WAAY,MACZF,IAAKnX,EAAM+X,QAAQ,KAErB0Q,eAAexoB,EAAAA,EAAAA,KAAI,CACjBqoB,SAAU,EACV5nB,QAAS,OACT0W,eAAgB,aAElBwR,cAAc3oB,EAAAA,EAAAA,KAAI,CAChBS,QAAS,OACT0W,eAAgB,YAChByR,aAAc,OACd3oB,MAAO,OACPqf,cAAe,WAGrB,C,yHC7WO,MAAMkT,WAAwBnrB,EAAAA,GAW3BuG,WAAAA,GACN,MACMtK,GADY2V,EAAAA,EAAAA,IAAkBzR,MACXgP,WAEnBic,GAAkB3xB,EAAAA,EAAAA,IAAuB0G,MAC/C,IAAKirB,EAAgB3vB,MAAMlB,UAAW,CACpC,MAAMA,GAAY+N,EAAAA,GAAAA,GAA6BrM,GAC3C1B,GACF6wB,EAAgBrxB,SAAS,CAAEQ,aAE/B,CAEA4F,KAAKsR,YACP,CAEQA,UAAAA,GACNtR,KAAKpG,SAAS,CAAEuC,KAAM,IAAIusB,GAA0B,CAAC,IACvD,CAvBA,WAAAvoB,CAAY7E,GACV8E,M,mUAAM,IAAK9E,IALb,QAAUilB,sBAAsB,IAAIC,EAAAA,GAAyBxgB,KAAM,CACjEygB,cAAe,CAACE,EAAAA,OAMhB3gB,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EAqBA,GA9BWgrB,GA8BG/oB,YAAY,EAAGC,YAC3B,MAAM,KAAE/F,GAAS+F,EAAMC,WACvB,OAAOhG,GAAQ,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,M,eCvBnC,MAAM+uB,GAAiD,CAC5D,CAAE5C,YA4HJ,SAA8B3X,GAC5B,MAAO,WACT,EA9HuChU,MAAO,YAAawuB,STapD,WACL,OAAO,IAAIjvB,EAAAA,GAAc,CACvBC,KAAM,IAAI8lB,GAAe,CAAC,IAE9B,GShBE,CAAEqG,YAAanR,GAAsBxa,MAAO,YAAawuB,SjBuZpD,SAA6BrvB,GAClC,OAAO,IAAII,EAAAA,GAAc,CACvBC,KAAM,IAAI0X,EAAkB,CAAE/X,YAElC,GiB1ZE,CAAEwsB,YA8HJ,SAA+B3X,GAC7B,MAAO,YACT,EAhIwChU,MAAO,aAAcwuB,SDwBtD,WACL,OAAO,IAAIjvB,EAAAA,GAAc,CACvBC,KAAM,IAAI6uB,GAAgB,CAAC,IAE/B,GC3BE,CAAE1C,YAgJJ,SAA+B3X,GAC7B,MAAO,YACT,EAlJwChU,MAAO,aAAcwuB,SPibtD,WACL,OAAO,IAAIjvB,EAAAA,GAAc,CACvBC,KAAM,IAAI2mB,GAAgB,CAAC,IAE/B,GOpbE,CACEwF,YA0IJ,SAA2BxsB,GACzB,MAAkB,WAAXA,EAAsB,iBAA8B,aAAXA,EAAwB,cAAgB,QAC1F,EA3IIa,MAAO,YACPwuB,SrBiBG,WACL,OAAO,IAAIjvB,EAAAA,GAAc,CACvBC,KAAM,IAAIkV,EAAW,CAAC,IAE1B,IqBfO,MAAM+Z,WAAqBvrB,EAAAA,IAuH3B,SAASsX,GAAqBrb,GACnC,OAAQA,GACN,IAAK,OACH,MAAO,oBACT,IAAK,SACH,MAAO,oBACT,IAAK,WACH,MAAO,qBAEb,CAUA,SAAS/D,GAAUQ,GACjB,MAAO,CACLinB,SAAShnB,EAAAA,EAAAA,KAAI,CACX,CAACD,EAAM8yB,YAAYC,GAAG/yB,EAAM8yB,YAAYrjB,OAAOujB,KAAM,CACnDva,SAAU,WACVoB,MAAO,EACPoZ,IAAK,EACLC,OAAQ,KAIhB,E,6GApJE,CADWL,GACGnpB,YAAY,EAAGC,Y,IAWP2L,EAAAA,EAAAA,EA2DhBA,EArEJ,MAAMhW,GAASC,EAAAA,EAAAA,YAAWC,KACnBgrB,EAAiB2I,IAAsBvpB,EAAAA,EAAAA,UAAS,GAEjDwpB,GAAcryB,EAAAA,EAAAA,IAAuB4I,GACrCuH,GAAc5I,EAAAA,EAAAA,IAAyBqB,IAEvC,WAAE3G,GAAeowB,EAAYxpB,YAC3BxF,MAAOb,GAAW2N,EAAYgI,oBAAoBtP,YACpD,mBAAEypB,GAAuBniB,EAAYtH,WACrC0L,EAAYxL,EAAAA,GAAWmE,QAAQtE,GAAOC,WACtC0pB,EAA4B,QAAdhe,EAAAA,EAAUtH,YAAVsH,IAAAA,GAAsB,QAAtBA,EAAAA,EAAgBF,cAAhBE,IAAAA,GAA2B,QAA3BA,EAAAA,EAAyB,UAAzBA,IAAAA,OAAAA,EAAAA,EAA6B1T,OAE3C2xB,EAAeZ,GAAuBrpB,OAAQkqB,IAC/B,eAAfA,EAAKpvB,OAAqC,WAAXb,OAK3B8vB,aAAAA,EAAAA,EAAoBzxB,SAAUyxB,EAAmB9pB,SAASiqB,EAAKpvB,SAInEyqB,GAAkB9e,EAAAA,EAAAA,IAAmBpG,GACrC8pB,GAAwBpD,EAAAA,EAAAA,IAAyB1mB,GACjDzH,EAAY4H,EAAAA,GAAWC,aAAaJ,IACpC,QAAEiH,GAAYie,EAAgBjlB,YAC5BxF,MAAOssB,GAAkB+C,EAAsB7pB,YAC/CxF,MAAOsvB,GAAmBxxB,EAAU0H,WAkD5C,OAhDAmY,EAAAA,EAAAA,WAAU,KACR,GAAe,WAAXxe,EAEF,YADA4vB,EAAmB,GAIrB,MAAMQ,GAAkBC,EAAAA,EAAAA,IAAmBjqB,GAC3C,IAAKgqB,EAEH,YADAR,EAAmB,GAIrBA,EAAmBQ,EAAgB9G,sBACnC,MAAMgH,EAAeF,EAAgB5qB,iBAAiB,CAACiQ,EAAUC,KAC3DD,EAASwR,kBAAoBvR,EAAUuR,iBACzC2I,EAAmBna,EAASwR,iBAAmB,KAInD,MAAO,KACLqJ,EAAazO,gBAEd,CAAC7hB,EAAQoG,EAAO3G,EAAY4N,EAAS8f,EAAegD,KAEvD3R,EAAAA,EAAAA,WAAU,K,IAQNzM,EAPF,IAAI8d,EAAYrwB,MAAM+wB,WAKtB,OACE5iB,EAAYnO,MAAMgxB,WACJ,QAAdze,EAAAA,EAAUtH,YAAVsH,IAAAA,OAAAA,EAAAA,EAAgBvS,SAAUiS,EAAAA,aAAaK,WACvBvT,IAAhBwxB,GACAA,EAAc,IAEdF,EAAY/xB,SAAS,CAAEyyB,YAAY,SACnCV,EAAYnwB,cAAc,mBAP5B,GAUC,CAAe,QAAdqS,EAAAA,EAAUtH,YAAVsH,IAAAA,OAAAA,EAAAA,EAAgBvS,MAAOmO,EAAYnO,MAAMgxB,SAAUX,EAAaE,KAEpEU,EAAAA,GAAAA,GAAS,KACqB,IAAxBT,EAAa3xB,QACfwxB,EAAYnwB,cAAcswB,EAAa,GAAGnvB,SAIlB,IAAxBmvB,EAAa3xB,OACR,KAIP,kBAACqyB,EAAAA,IAAGA,KACF,kBAACzoB,MAAAA,CAAI1L,UAAWR,EAAO2nB,SACrB,kBAAChI,EAAAA,MAAKA,CAAC9H,IAAK,GACV,kBAAClG,EAAsBA,CAACC,YAAaA,MAIzC,kBAACgjB,EAAAA,QAAOA,KACLX,EAAaxmB,IAAI,CAAConB,EAAKhR,IAEpB,kBAACiR,EAAAA,IAAGA,CACF9jB,IAAK6S,EACL1W,MAAO0nB,EAAIpE,YAAYxsB,GACvBwf,OAAQ/f,IAAemxB,EAAI/vB,MAC3BiwB,YAAa,IAAMjB,EAAYnwB,cAAckxB,EAAI/vB,OACjDkwB,QACgB,cAAdH,EAAI/vB,MAAwBkvB,EAA4B,eAAda,EAAI/vB,MAAyBomB,OAAkB1oB,S,4JC3GpG,MAAMyyB,WAAqBjtB,EAAAA,GAoDxBuG,WAAAA,GACNpG,KAAKpG,SAAS,CACZsV,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI0O,GAAAA,EAAgB,CACzBC,cAAqC,aAAtB7d,KAAK1E,MAAMQ,OAAwB,GAAK,GACvDsG,WAAYiU,EAAAA,GACZvT,QAAS,CAAuB,aAAtB9C,KAAK1E,MAAMQ,QAAwBixB,EAAAA,GAAAA,MAAwB1Q,EAAAA,GAAAA,GAAmBrc,KAAK1E,MAAMQ,WAErGsT,gBACwB,aAAtBpP,KAAK1E,MAAMQ,OACP,KAAIkxB,EAAAA,GAAAA,MACJ,KAAIlP,EAAAA,GAAAA,IAAyB7I,EAAAA,EAAAA,IAAajV,UAElDzG,MAAOyG,KAAKitB,YAAYjtB,KAAK1E,MAAMQ,SAEvC,CAEQmxB,WAAAA,CAAYnxB,GAClB,OAAO,IAAIC,EAAAA,GAAgB,CACzBC,UAAW,MACXC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAiB,aAAXL,EAAwBkE,KAAKktB,sBAAwBltB,KAAKmtB,oBAAoBrxB,OAI5F,CAEQqxB,mBAAAA,CAAoBrxB,GAC1B,MAAMvC,GAAQ+lB,EAAAA,GAAAA,GAAgBxjB,GAAQrC,gBAAe,GAAM2zB,eAAe,eAU1E,MATe,SAAXtxB,EACFvC,EAAM2iB,qBAAqB,YAAa,UACpB,WAAXpgB,GACTvC,EAAM2b,SAAS,eAAegH,qBAAqB,YAAa,WAAWmR,SAAS,CAClFC,WAAY,gBACZvR,KAAM,UAIHxiB,EAAMI,OACf,CAEQuzB,mBAAAA,GACN,OAAO1zB,EAAAA,GAAAA,MACJ0b,SAAS,yBACTzb,gBAAe,GACf2zB,eAAe,eACfzzB,OACL,CAnGA,WAAAwG,CAAY7E,GACV8E,M,mUAAM,EACJzI,aAAa,GACV2D,IAGL0E,KAAKK,qBAAqB,KACxBL,KAAKoG,cACL,MAAMG,EAAOlE,EAAAA,GAAWmE,QAAQxG,MAEhCA,KAAKoB,MAAMC,IACTkF,EAAKjF,iBAAkBiF,I,IACQA,EAEzBA,EAkBOA,EApBXvG,KAAKpG,SAAS,CAAEjC,aAAsB,QAAT4O,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaG,aAElD,QAATnH,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaK,KACJ,IAA5BrH,EAAKA,KAAKoH,OAAOxT,QAA+C,IAA/BoM,EAAKA,KAAKoH,OAAO,GAAGxT,SAAgBozB,EAAAA,EAAAA,IAAoBhnB,GAC3FvG,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAI2R,EAAAA,EAAgB,CACxB0f,SAAU,aAOpBxtB,KAAKpG,SAAS,CACZL,MAAOyG,KAAKitB,YAAYjtB,KAAK1E,MAAMQ,WAGrB,QAATyK,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaC,SAC3CxN,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBC,UAAW,SACXyxB,UAAWC,GACXh1B,OAAQg1B,GACRzxB,SAAU,CACR,IAAI2H,EAAAA,EAAkB,CACpBE,UAAW,KAAM2K,EAAAA,GAAAA,IAAkB,cASrD,EA2FF,SAAS1W,GAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbm1B,KAAM,EACNl1B,MAAO,OACPQ,QAAS,OACT6e,cAAe,SACf5G,OAAQ,aAAa3Y,EAAMK,OAAOsY,OAAOC,OACzCnY,aAAc,MACduL,WAAYhM,EAAMK,OAAO2L,WAAW0M,QACpC6P,WAAY,MAEZ,yBAA0B,CACxB8M,YAAa,eAGf,mBAAoB,CAClB30B,QAAS,UAGb40B,eAAer1B,EAAAA,EAAAA,KAAI,CACjBS,QAAS,OACT2W,WAAY,SACZoB,SAAU,WACVwa,IAAK,MACLtZ,KAAM,MACNuZ,OAAQ,IAEVqC,WAAWt1B,EAAAA,EAAAA,KAAI,CACbuX,OAAQ,UACR0d,UAAWC,GAEX,iCAAoC,CAClCh1B,OAAQg1B,GACRzd,SAAU,UAGZ,SAAU,CACR1L,WAAYhM,EAAMK,OAAO2L,WAAWC,UACpCupB,MAAO,CACLp1B,gBAAiB,UACjBuY,OAAQ,oBACRnB,OAAQ,cAIdie,aAAax1B,EAAAA,EAAAA,KAAI,CACfS,QAAS,UAEXg1B,kBAAkBz1B,EAAAA,EAAAA,KAAI,CACpBwY,SAAU,WACVwa,IAAK,MACLpZ,MAAO,MACPqZ,OAAQ,IAGd,C,yjBA/FE,GAtGWqB,GAsGG7qB,YAAY,EAAGC,YAC3B,MAAM,MAAE3I,EAAK,YAAE5B,GAAgBuK,EAAMC,WAC/BtK,GAASC,EAAAA,EAAAA,YAAWC,IACpB6I,GAAmBC,EAAAA,EAAAA,IAAyBqB,GAE5CgsB,EAAe,MACnBzyB,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBiC,OAAQ/B,EAAAA,GAAoB+B,OAAOsB,eAAgB,CACxFnD,OAAQoG,EAAM5G,MAAMQ,OACpBoL,SAAU,UAEZtG,EAAiButB,uBAAuBjsB,EAAM5G,MAAMQ,SAGtD,GAAKvC,EAIL,OACE,kBAACwK,MAAAA,CAAI1L,WAAWG,EAAAA,EAAAA,KAAI,CAACX,EAAOmM,UAAWnM,EAAOi2B,YAAartB,QAASytB,GAClE,kBAACnqB,MAAAA,CAAI1L,UAAWR,EAAOg2B,eACrB,kBAACO,EAAAA,gBAAeA,CACd/1B,UAAWR,EAAOm2B,YAClB71B,KAAM,UAAU+J,EAAM5G,MAAMQ,SAC5BwO,QAAS,CAAC,CAAEnD,MAAO,GAAIxK,MAAO,aAC9B4N,SAAU,IAAM2jB,IAChBvxB,MAAO,kBAGVhF,GACC,kBAACoM,MAAAA,CAAI1L,UAAWR,EAAOo2B,kBACrB,kBAACv2B,GAAAA,EAAkBA,CAACC,aAAa,EAAMC,SAAU,MAGrD,kBAAC2B,EAAM0I,UAAS,CAACC,MAAO3I,OCxGzB,MAAM80B,WAA6BxuB,EAAAA,GAYhCuG,WAAAA,GAEN,MACMkoB,EADS,IAAIC,gBAAgBtnB,OAAOC,SAASsnB,QACtB/L,IAAI,cAC7B6L,GAAiBpD,GAAuBtpB,KAAMsb,GAAMA,EAAEvgB,QAAU2xB,IAClEtuB,KAAKpG,SAAS,CAAE2B,WAAY+yB,IAG9BtuB,KAAKsR,aAEL,MACMmd,GADc5tB,EAAAA,EAAAA,IAAyBb,MACVyR,oBACnCzR,KAAKoB,MAAMC,IACTotB,EAAentB,iBAAiB,CAACiQ,EAAUC,KACzC,GAAID,EAAS5U,QAAU6U,EAAU7U,MAAO,CACtC,MAAMvC,GAAY+N,EAAAA,GAAAA,GAA6BoJ,EAAS5U,OACpDvC,GACF4F,KAAKpG,SAAS,CAAEQ,cAElB4F,KAAK0uB,kBAAkBnd,EAAS5U,OAChCqD,KAAK2uB,sBAAsBpd,EAAS5U,OACpCqD,KAAKsR,YACP,KAKJtR,KAAK2uB,sBAAsBF,EAAezf,YAE1ChP,KAAKoB,MAAMC,IACTrB,KAAKsB,iBAAiB,CAACiQ,EAAUC,K,IAETD,EAAAA,EADtB,MAAM9W,EAAY4H,EAAAA,GAAWC,aAAatC,MACpC4uB,EAAkC,QAAlBrd,EAAAA,EAASnX,iBAATmX,IAAAA,GAA6B,QAA7BA,EAAAA,EAAoB9W,iBAApB8W,IAAAA,OAAAA,EAAAA,EAA+B7W,KAEjDk0B,GAAiBA,EAAgBn0B,EAAUa,MAAMqB,MAAMjC,KAAKsuB,QAC9DhpB,KAAKpG,SAAS,CAAEQ,eAAWC,KAIxBwuB,EAAAA,GAAAA,SAAQtX,EAASnX,UAAWoX,EAAUpX,cACtB8lB,EAAAA,EAAAA,IAAmBlgB,MAC3BiP,cAAcwL,EAAAA,IACzBza,KAAK0uB,kBAAkBD,EAAezf,gBAK5ChP,KAAKoB,MAAMC,KACTwtB,EAAAA,EAAAA,IAAsB7uB,MAAMsB,iBAAiB,KAC3CtB,KAAK8uB,sBAIT9uB,KAAKoB,MAAMC,KACT0N,EAAAA,EAAAA,IAA2B/O,MAAMsB,iBAAiB,KAChDtB,KAAK0uB,kBAAkBD,EAAezf,eAI1ChP,KAAK0uB,kBAAkBD,EAAezf,YACtChP,KAAK8uB,kBACP,CAEAxd,UAAAA,GACE,MACMxV,GADmB+E,EAAAA,EAAAA,IAAyBb,MAClByR,oBAAoBzC,WAC9C+f,EAAgB7D,GAAuBtpB,KAAMsb,GAAMA,EAAEvgB,QAAUqD,KAAK1E,MAAMC,YAEhFyE,KAAKpG,SAAS,CACZuC,KAAM6yB,GACJlzB,EACAizB,EAAgB,CAACA,aAAAA,EAAAA,EAAe5D,SAASrvB,SAA6BzB,UAI5CA,IAA1B2F,KAAK1E,MAAMC,YACbyE,KAAKxE,cAAc,YAEvB,CAEUmzB,qBAAAA,CAAsB7yB,GAC9B,GAAe,WAAXA,GACF,IAAKkE,KAAK1E,MAAM4wB,gBAAiB,CAC/B,MAAMA,EAAkB,IAAIpJ,GAAgB,CAAC,GAC7C9iB,KAAKpG,SAAS,CACZsyB,oBAIFhiB,WAAW,KACTgiB,EAAgB/qB,YACf,EACL,OAGInB,KAAK1E,MAAM4wB,iBACblsB,KAAKpG,SAAS,CACZsyB,qBAAiB7xB,GAIzB,CAEcy0B,gBAAAA,G,yBAaZG,EAZA,MAAMA,QAAWC,EAAAA,EAAAA,oBAAmBzM,IAAI0M,EAAAA,GAAqB,CAAEC,cAAe,CAAEzyB,MAAOqD,QAEvF,IAAKivB,EACH,OAGF,MACM3kB,EAAU,CACd7P,UAFgB4H,EAAAA,GAAWC,aAAatC,MAEnB1E,MAAMqB,MAC3BwM,QAAS,IAGE,QAAb8lB,EAAAA,EAAGI,kBAAHJ,IAAAA,GAAAA,EAAAA,KAAAA,EAAgB3kB,GAASglB,KAAMC,IAC7B,IAAIxpB,EAA0B,GAE5BA,EADE,SAAUwpB,EACL,EAA4BhpB,KAE5BgpB,EAET,MAAMjgB,EAAavJ,EAAKT,IAAKkqB,GAAMA,EAAE12B,MACjCwW,IAAetP,KAAK1E,MAAMgU,YAC5BtP,KAAKpG,SAAS,CAAE0V,gBAGtB,E,+KAAA,W,MAEAmgB,WAAAA,GACE,MAAO,CACLl0B,WAAYyE,KAAK1E,MAAMC,WACvBnB,UAAW4F,KAAK1E,MAAMlB,UAAYsI,KAAKC,UAAU3C,KAAK1E,MAAMlB,gBAAaC,EAE7E,CAEAq1B,aAAAA,CAAc1nB,GACZ,GAAiC,iBAAtBA,EAAOzM,YAChB,GAAIyE,KAAK1E,MAAMC,aAAeyM,EAAOzM,WAAY,CAC/C,MAAMwzB,EAAgB7D,GAAuBtpB,KAAMsb,GAAMA,EAAEvgB,QAAUqL,EAAOzM,YACxEwzB,GACF/uB,KAAKxE,cAAcuzB,EAAcpyB,MAErC,OAC+B,OAAtBqL,EAAOzM,YAChByE,KAAKxE,cAAc,aAGrB,GAAgC,iBAArBwM,EAAO5N,UAAwB,CACxC,MAAME,EAAeoI,KAAK+R,MAAMzM,EAAO5N,YAClCyuB,EAAAA,GAAAA,SAAQvuB,EAAc0F,KAAK1E,MAAMlB,YACpC4F,KAAKpG,SAAS,CAAEQ,UAAWE,GAE/B,CACF,CAEAc,qBAAAA,CAAsBd,GACpB0F,KAAK2vB,SAASC,4BAA4B,KACxC5vB,KAAKpG,SAAS,CAAEQ,UAAWE,KAE/B,CAEOkB,aAAAA,CAAcD,GACnB,MAAM,KAAEY,GAAS6D,KAAK1E,MAChByzB,EAAgB7D,GAAuBtpB,KAAMsb,GAAMA,EAAEvgB,QAAUpB,GAE/DO,GADmB+E,EAAAA,EAAAA,IAAyBb,MAClByR,oBAAoBzC,WAEpD,GAAI7S,EAAKb,MAAMW,SAAS9B,OAAS,GAC3B40B,EAAe,CACjB,IAAI51B,EAGFA,EAFiB,eAAfoC,GAA+ByE,KAAK1E,MAAM4wB,gBAEpC,IAAIhwB,EAAAA,GAAc,CACxBC,KAAM6D,KAAK1E,MAAM4wB,kBAGX6C,EAAc5D,SAASrvB,GAGjCK,EAAKvC,SAAS,CACZqC,SAAU,IAAIE,EAAKb,MAAMW,SAAS4O,MAAM,EAAG,GAAI1R,MAEjDsC,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAeiC,oBAAqB,CAC7GiyB,UAAW7vB,KAAK1E,MAAMC,WACtBu0B,UAAWv0B,IAEbyE,KAAKpG,SAAS,CAAE2B,WAAYwzB,EAAcpyB,OAC5C,CAEJ,CAEQ+xB,iBAAAA,CAAkB5yB,G,IAERiT,EADhB,MAAM3U,EAAY4F,KAAK1E,MAAMlB,U,IACb2U,EAAhB,MAAMD,EAA+D,QAArDC,EAAyC,QAAzCA,GAAAA,EAAAA,EAAAA,IAA2B/O,MAAMgP,kBAAjCD,IAAAA,OAAAA,EAAAA,EAA6CxD,kBAA7CwD,IAAAA,EAAAA,EAA2D,GAE3E/O,KAAKpG,SAAS,CACZsV,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BlF,WAAYiU,EAAAA,GACZvT,QAAS,CAACwT,GAAWxa,EAAQgT,EAAS1U,IACtC21B,WAAYC,GAAuB51B,KAErCgV,gBAAiB,IAAImH,EAAAA,MAA2C0Z,OAGtE,CAxNA,YAAmB30B,G,IAETA,EADR8E,MAAM,IACJjE,KAAgB,QAAVb,EAAAA,EAAMa,YAANb,IAAAA,EAAAA,EAAc,IAAIS,EAAAA,GAAgB,CAAEE,SAAU,MACjDX,IALP,QAAUq0B,WAAW,IAAIO,EAAAA,GAAyBlwB,KAAM,CAAE+F,KAAM,CAAC,aAAc,gBAQ7E/F,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EAmNA,GA7NWquB,GA6NJpsB,YAAY,EAAGC,YACpB,MAAM,KAAE/F,GAAS+F,EAAMC,WACjBtK,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,oCACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOsP,OACrB,kBAACnP,EAAAA,QAAOA,CAACC,QAAS,kBAACk4B,GAAAA,MAAsBC,UAAW,cAAeC,aAAAA,GACjE,kBAACvf,OAAAA,CAAKzY,UAAWR,EAAOy4B,MAAM,sBACT,kBAACp4B,EAAAA,KAAIA,CAACC,KAAM,mBAIrC,kBAACgE,EAAK8F,UAAS,CAACC,MAAO/F,OAM/B,MAAMg0B,GAAoB,KACxB,MAAMt4B,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACyf,EAAAA,MAAKA,CAACxb,UAAW,SAAU0T,IAAK,GAC/B,kBAAC3L,MAAAA,CAAI1L,UAAWR,EAAO8R,QAAQxC,OAAO,0BACtC,kBAAC2J,OAAAA,CAAKzY,UAAWR,EAAO8R,QAAQ4mB,UAAU,oFAG1C,kBAACxsB,MAAAA,CAAI1L,UAAWR,EAAO8R,QAAQ7Q,MAC7B,kBAACiL,MAAAA,KACC,kBAAC+M,OAAAA,CAAKzY,UAAWR,EAAO8R,QAAQrF,WAAW,QAAW,yFAGxD,kBAACP,MAAAA,KACC,kBAAC+M,OAAAA,CAAKzY,UAAWR,EAAO8R,QAAQrF,WAAW,UAAa,iEAG1D,kBAACP,MAAAA,KACC,kBAAC+M,OAAAA,CAAKzY,UAAWR,EAAO8R,QAAQrF,WAAW,YAAe,2FAK9D,kBAACP,MAAAA,CAAI1L,UAAWR,EAAO8R,QAAQ6mB,QAC7B,kBAAC9Y,EAAAA,WAAUA,CACTzO,KAAK,oBACLlQ,KAAK,QACLX,KAAM,KACN+U,OAAQ,SACR5M,KACE,gIAEFE,QAAS,KACPhF,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBiC,OAAQ/B,EAAAA,GAAoB+B,OAAO0B,2BAE7E,yBAQT,SAAStH,GAAUQ,GACjB,MAAO,CACL4O,OAAO3O,EAAAA,EAAAA,KAAI,CACTwM,MAAO,QACP/L,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQmgB,KACnB5gB,SAAUtX,EAAM6X,WAAWyX,UAAUhY,SACrC6gB,cAAen4B,EAAM+X,QAAQmgB,KAC7B7gB,WAAY,WAEd0gB,MAAM93B,EAAAA,EAAAA,KAAI,CACRwM,MAAO,OACP+K,OAAQ,YAEVpG,QAAS,CACP3E,MAAO,UACPmC,OAAO3O,EAAAA,EAAAA,KAAI,CACTqX,SAAU,OACV8gB,WAAY,MAEdJ,UAAU/3B,EAAAA,EAAAA,KAAI,CACZ4Y,aAAc7Y,EAAM+X,QAAQyH,KAE9Bjf,MAAMN,EAAAA,EAAAA,KAAI,CACRwM,MAAO,OACP8K,MAAOvX,EAAMK,OAAOE,KAAK0L,UAEzBT,IAAK,CACHqN,aAAc7Y,EAAM+X,QAAQmgB,QAGhCnsB,WAAW9L,EAAAA,EAAAA,KAAI,CACbwM,MAAO,YACP8K,MAAOvX,EAAMK,OAAOE,KAAKmY,UAE3Buf,QAAQh4B,EAAAA,EAAAA,KAAI,CACV4Y,aAAc7Y,EAAM+X,QAAQmgB,QAIpC,CAEA,MAAMG,GAAoB,IACblD,IAAqBkD,GAAoB,GAAK,EAEpD,SAASta,GAAW/b,EAAsBuU,EAAiB1U,GAChE,MAAMme,EAA0B,KAAZzJ,EAAiB,aAAaA,KAAa,GAC/D,IAAI+hB,EAAY,GAChB,OAAQt2B,GACN,IAAK,SACHs2B,EAAY,qBACZ,MACF,IAAK,WACH,GAAIz2B,EAAW,C,IAETA,EAGAA,EAJJ,MAAMe,EAAW,IACK,QAAlBf,EAAAA,EAAUe,gBAAVf,IAAAA,OAAAA,EAAAA,EAAoBM,KAAKP,SAC3BgB,EAAS6P,KAAK,eAAe5Q,EAAUe,SAAST,SAE5B,QAAlBN,EAAAA,EAAUe,gBAAVf,IAAAA,OAAAA,EAAAA,EAAoBU,GAAGX,SACzBgB,EAAS6P,KAAK,eAAe5Q,EAAUe,SAASL,MAE9CK,EAAShB,SACX02B,GAAa,MAAQ11B,EAASuQ,KAAK,QAEvC,CACKmlB,EAAU12B,SACb02B,EAAY,iBAAiBla,EAAAA,MAInC,MAAO,CACL5T,MAAO,IACP7B,MAAO,IAAI0V,EAAAA,KAAmBia,KAAatY,IAC3C5J,UAAW,UACXkI,UAAW,QACXC,MAAO,IACPC,KAAM,GACN5N,QAAS,GAEb,CAEA,SAAS6mB,GAAuB51B,G,IACPA,EACFA,EADrB,MAAM4tB,EAAoD,MAAnC5tB,SAAoB,QAApBA,EAAAA,EAAWK,iBAAXL,IAAAA,OAAAA,EAAAA,EAAsBM,OAAQ,GAC/CutB,EAAgD,MAAjC7tB,SAAoB,QAApBA,EAAAA,EAAWK,iBAAXL,IAAAA,OAAAA,EAAAA,EAAsBU,KAAM,GACjD,OAAOktB,GAAiBC,EACpB,IAAI6I,EAAAA,GAAe,CACjBp2B,KAAMstB,EAAc9qB,QAAQ,GAC5BpC,GAAImtB,EAAY/qB,QAAQ,GACxBP,MAAO,CACLjC,MAAMq2B,EAAAA,EAAAA,UAAS/I,GACfltB,IAAIi2B,EAAAA,EAAAA,UAAS9I,GACbztB,IAAK,CAAEE,MAAMq2B,EAAAA,EAAAA,UAAS/I,GAAgBltB,IAAIi2B,EAAAA,EAAAA,UAAS9I,YAGvD5tB,CACN,CAEA,SAAS20B,GAAgBlzB,EAAwBG,GAC/C,MAAM+0B,EAEA,IAAIlE,GADG,SAAXhxB,EACqB,CAAEA,OAAQ,UACV,CACfA,OAAQ,SAGVm1B,EAEA,IAAInE,GADG,aAAXhxB,EACqB,CACfA,OAAQ,UAEO,CAAEA,OAAQ,aAEjC,OAAO,IAAIC,EAAAA,GAAgB,CACzBC,UAAW,SACXwgB,WAAY,CACV,IAAI0U,EAAAA,GAAAA,GAAqB,CACvBroB,IAAK,sBACLsoB,KAAMC,EAAAA,oBAAoBC,aAG9Bp1B,SAAU,CACR,IAAIF,EAAAA,GAAgB,CAClBC,UAAW,MACXs1B,QAAS,UACTr1B,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB4Y,UAAW8b,GACXnD,UAAWmD,GACXn4B,MAAO,MACP0D,KAAM,IAAIo1B,EAAAA,GAAS,CAAC,KAEtB,IAAIx1B,EAAAA,GAAgB,CAClBC,UAAW,SACX8Y,UAAW8b,GACXnD,UAAWmD,GACX30B,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB4Y,UAAW4Y,GACXD,UAAWC,GACXh1B,OAAQg1B,GAERvxB,KAAM60B,IAER,IAAI90B,EAAAA,GAAc,CAChB4Y,UAAW4Y,GACXD,UAAWC,GACXh1B,OAAQg1B,GAER4D,QAAS,OAETn1B,KAAM80B,UAMhB,IAAI/0B,EAAAA,GAAc,CAChBo1B,QAAS,UACTn1B,KAAM,IAAIivB,GAAa,CAAC,QAEtBnvB,GAAY,KAGtB,CAEA,MAAMg0B,GAA0B,CAC9B,IAAO/jB,GACEA,EAAOC,MACZ7G,EAAAA,EAAAA,KAAKiB,GACIA,EAAKjB,IAAK8G,G,2WAAQ,OACpBA,GAAAA,CACH1E,OAAQ0E,EAAG1E,OAAO7F,OAAQ8F,IAAOA,EAAExP,KAAK8S,WAAW,mBAK3D,CACExF,GAAI,SACJ6E,QAAS,CACP5C,OAAQ,CAAC,EACTwD,KAAM,CACJ,CACEtC,MAAO,WACP4oB,MAAM,MAKd,CACE/rB,GAAI,WACJ6E,QAAS,CACPmnB,YAAa,CACX,aAAc,EACdzS,OAAQ,EACR,gBAAiB,EACjB,aAAc,EACd0S,SAAU,EACV,UAAW,EACX,mBAAoB,EACpB,2BAA4B,EAC5B,iBAAkB,EAClB,kBAAmB,EACnB,wBAAyB,GACzB,iCAAkC,M,6DCviBnC,SAAS3E,IACd,MAAO,CACLhqB,MAAO,IACP7B,MAAO,IAAI0V,EAAAA,sCACXjI,UAAW,UACXkI,UAAW,QACXC,MAAO,IACPC,KAAM,GACN5N,QAAS,GAEb,C,6mBCIO,MAAMwoB,UAA+B9xB,EAAAA,GASlCuG,WAAAA,GACN,MAAM,MAAEoB,GAAUxH,KAAK1E,MACvB0E,KAAKpG,SAAS,MAAK2N,EAAAA,EAAAA,GAAyBC,KAE5CxH,KAAKoB,MAAMC,IACTrB,KAAKsB,iBAAiB,CAACiQ,EAAUC,KAC/B,GAAID,EAAS/J,QAAUgK,EAAUhK,MAAO,CACtC,MAAM,MAAEA,GAAU+J,EAClBvR,KAAKpG,SAAS,MAAK2N,EAAAA,EAAAA,GAAyBC,IAC9C,IAGN,CAEQoqB,YAAAA,GACN,OAAO5xB,KAAK1E,MAAMkM,MAAMrP,IAC1B,CAEQ6W,QAAAA,GACN,MAAM6iB,EAAa7xB,KAAK1E,MAAMkM,MAAME,OAAO9F,KAAM+F,GAAiB,UAAXA,EAAExP,MACzD,OAAO05B,aAAAA,EAAAA,EAAY7pB,OAAOhI,KAAK1E,MAAMwM,oBAAsB,EAC7D,CAEQgqB,cAAAA,GACN,MAAMzpB,GAAWC,EAAAA,EAAAA,IAAmBtI,MAC9B0Z,EAAY1Z,KAAK4xB,eACnBlY,IACF/Q,EAAAA,EAAAA,IAAaN,EAAUqR,EAAW1Z,KAAKgP,WAE3C,CArCA,WAAA7O,CAAY7E,GACV8E,MAAM,KACD9E,IAGL0E,KAAKK,qBAAqB,IAAML,KAAKoG,cACvC,EAwEF,SAASrO,EAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACT6e,cAAe,SACf+I,SAAU,EACVnoB,OAAQ,SAEVq5B,qBAAqBv5B,EAAAA,EAAAA,KAAI,CACvBS,QAAS,OACT6e,cAAe,SACf+I,SAAU,EACV3P,OAAQ,aAAa3Y,EAAMK,OAAO4L,UAAU0M,SAC5C3M,WAAYhM,EAAMK,OAAO2L,WAAW0M,QACpC9C,QAAS,MACTiD,aAAc7Y,EAAM+X,QAAQ,GAC5BT,SAAU,OACVnX,OAAQ,UAEVs5B,iBAAiBx5B,EAAAA,EAAAA,KAAI,CACnBqX,SAAU,OACV8gB,WAAY,OACZsB,UAAW,WAEbt1B,OAAOnE,EAAAA,EAAAA,KAAI,CACTy5B,UAAW,SACXniB,MAAOvX,EAAMK,OAAO4L,UAAU1L,KAC9Bo5B,SAAU,SACVC,WAAY,SACZliB,SAAU,SACVC,aAAc,aAEhB/I,OAAO3O,EAAAA,EAAAA,KAAI,CACTm4B,WAAY,MAGlB,CA3EE,EAxCWgB,EAwCG1vB,YAAY,EAAGC,YAC3B,MAAM,cAAE2F,EAAa,mBAAEC,EAAkB,MAAEvO,GAAU2I,EAAMC,WACrDtK,GAASC,EAAAA,EAAAA,YAAWC,GACpB4E,EAAQuF,EAAM8M,W,IACR9M,EAAZ,MAAM2G,EAA4B,QAAtB3G,EAAAA,EAAM5G,MAAMkM,MAAMrP,YAAlB+J,IAAAA,EAAAA,EAA0B,GAChCkwB,GAAetpB,EAAAA,EAAAA,KAAmBR,EAAAA,EAAAA,IAAmBpG,GAAQ2G,EAAKlM,EAAMa,QAAQ,KAAM,KAE5F,OACE,kBAACuG,MAAAA,CAAI1L,UAAWR,EAAOmM,WACpB,kBAACzK,EAAM0I,UAAS,CAACC,MAAO3I,IACzB,kBAACwK,MAAAA,CAAI1L,UAAWR,EAAOk6B,0BACF13B,IAAlBwN,QAAsDxN,IAAvByN,GAC9B,oCACE,kBAAC0P,EAAAA,MAAKA,CAAC9H,IAAK,EAAGC,eAAgB,gBAAiBC,WAAY,UAC1D,kBAAC7L,MAAAA,CAAI1L,UAAWR,EAAOsP,OAAO,uBAC5BirB,GACA,kBAACrpB,EAAAA,OAAMA,CACL3Q,KAAK,KACL4Q,QAAQ,UACRC,KAAM,cACNlQ,KAAK,OACL0H,QAAS,IAAMyB,EAAM4vB,kBACtB,mBAKL,kBAAC/tB,MAAAA,CAAI1L,UAAWR,EAAOm6B,kBACO,IAA1Br3B,KAAKuN,IAAIL,IAAsB3K,QAA0B,IAAlB2K,EAAsB,EAAI,GAAG,KAExE,kBAAC9D,MAAAA,CAAI1L,UAAWR,EAAO8E,OAAQA,Q,0cC9EtC,MAAMqtB,EAAgB,YAChBI,EAAiB,UAEvB,SAAST,EACdvN,EACAtgB,GAIA,OAAO,IAAIsiB,EAAAA,GAAgB,CACzBjiB,KAAM,IAAIkiB,EAAAA,GAAmB,CAC3BC,gBAAiBC,EAAAA,GACjBC,SAAU,QACVviB,SAAU,KAEZ0iB,eAAgBA,EAR+B,CAAC,EAQT0T,EAAcjW,EAAWtgB,IAEpE,CAEA,MAAMu2B,EAAgBjmB,GACbA,EAAGjU,MAAQ,oBAGpB,SAASwmB,EACPpC,EACAqC,EACAxC,EACAtgB,GAEA,MAAO,CAACyK,EAAiBiB,KACvB,MAAMqX,EAAmBrX,EAAMrP,KAAOokB,EAAO/U,EAAMrP,WAAQkC,EAErDykB,EAAW,IAAI1J,EAAAA,GAAc,CACjC7O,M,EAAM,KACDA,G,EAAAA,CACHoH,OAAQ,CACN,KACKnG,K,iWAMX,GAAIqX,EAAkB,CACpB,MAAM1iB,EAAO0iB,EAAiBvjB,MAAMa,KAGpC,OAFAA,EAAKvC,SAAS,CAAE4N,UAChBrL,EAAKb,MAAM/B,MAAMK,SAAS,CAAEsV,MAAO4P,IAC5BD,CACT,CAEA,MAAMtlB,EAAQivB,EAAe1sB,GAAQoZ,SAAS0J,EAASpX,IAAQ2N,QAAQ2J,GAEjEU,EAAUpD,EAAU5U,GACtBgY,GACFjmB,EAAMkmB,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCxjB,KAAM,IAAIw1B,EAAuB,CAAEnqB,QAAOjO,MAAOA,EAAMI,YAMzD,OAJI6N,EAAMrP,OACRokB,EAAO/U,EAAMrP,MAAQunB,GAGhBA,EAEX,CAEO,SAAS8I,EAAe1sB,GAC7B,OAAOM,EAAAA,GAAck2B,WAClB54B,UAAU,SAAU,CAAEoiB,YAAY,IAClCpiB,UAAU,UAAW,CAAEqiB,KAAMC,EAAAA,GAAmBC,QAChDsW,OAAO,GACPlkB,aAAcmkB,IACbA,EAAUjkB,oBAAoB,SAASC,0BAA0B,gBAAiBikB,EAAAA,cAAcC,QAChGF,EACGjkB,oBAAoB,YACpBokB,cAAc,CACb5W,KAAM,QACNuR,WAAuB,aAAXxxB,EAAwBkuB,EAAgB,oBAErD4I,aAAa,eAChBJ,EACGjkB,oBAAoB,aACpBokB,cAAc,CACb5W,KAAM,QACNuR,WAAuB,aAAXxxB,EAAwBsuB,EAAiB,kBAEtDwI,aAAa,gBAEtB,C,yPCzFA,MAAMC,EAAcpmB,IAClB,MAAM5U,GAASC,EAAAA,EAAAA,YAAWC,GAC1B,OAAO,kBAAC+6B,EAAAA,EAAWC,M,oUAAI,IAAKtmB,G,WAAAA,CAAOpU,UAAWR,EAAOm7B,a,uVAGhD,SAASC,GAAkB,YAAEC,IAClC,MACMjqB,EADSiqB,EAAYC,WACL,WAAa,aAEnC,OAAO,kBAACj7B,EAAAA,KAAIA,CAACC,KAAM8Q,EAAM7Q,KADZ,MAEf,CAEA,MAAMg7B,EAAe,KACnB,MAAMv7B,GAASC,EAAAA,EAAAA,YAAWC,GAC1B,OACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOw7B,SACrB,kBAAChc,EAAAA,KAAIA,CAACic,OAAO,OAAOtqB,QAAQ,YAAY8G,MAAM,aAAY,oBAOzD,MAAMyjB,UAA8BC,EAAAA,IACzC,EADWD,EACJtxB,YAAY,EAAGC,YACpB,MAAMrK,GAASC,EAAAA,EAAAA,YAAWC,IACpB,MAAE4E,EAAK,WAAE82B,GAAevxB,EAAMC,YAGpCmY,EAAAA,EAAAA,WAAU,KACH3d,GACHuF,EAAM+M,cAAcwkB,EAAaC,EAAAA,GAAqB,GAAG/2B,MAAS+2B,EAAAA,GAAqB,GAAG/2B,SAI9F,MAAMg3B,EAAqBD,EAAAA,GAAqB7oB,MAAM,EAAG,GACnD+oB,EAAgBF,EAAAA,GAAqB9xB,KAAMiY,GAAWA,EAAOld,QAAUA,GACzEi3B,IAAkBD,EAAmBpZ,KAAMV,GAAWA,EAAOhY,OAAOgH,MAAQ+qB,EAAc/xB,OAAOgH,MACnG8qB,EAAmB3oB,KAAK4oB,GAE1B,MAAMC,EAAgBH,EAAAA,GAAqB7xB,OACxCgY,IAAY8Z,EAAmBpZ,KAAMnP,GAAMA,EAAEzO,QAAUkd,EAAOld,QAG3D4N,EAAY2S,KAChBzhB,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAe8C,uBACnC,CACEq1B,eAAgB5W,IAGpBhb,EAAM+M,cAAciO,OAAI7iB,GAAW,IAGrC,OAAIo5B,EACK,qCAIP,oCACE,kBAAC9Y,EAAAA,iBAAgBA,CACfrQ,QAASqpB,EACTh3B,MAAOA,EACP4N,SAAUA,EACVwpB,SAAUN,EACVp7B,UAAWR,EAAOm8B,cAEpB,kBAAC1oB,EAAAA,OAAMA,CACLhB,QAAS,CAAC,CAAEtF,MAAO,iBAAkBsF,QAASupB,IAC9Cl3B,MAAO,GACP8O,YAAY,GACZwoB,cAAc,EACdroB,aAAa,EACbnT,MAAO,EACP8R,SAAW2S,GAAM3S,EAAS2S,EAAEvgB,OAC5BtE,UAAWR,EAAOijB,OAClBgY,WAAY,CACVoB,mBAAoB,IAAM,KAC1BC,YAAa,IAAM,KACnBpB,KAAMF,EACNI,oBACAG,qBAQZ,MAAMr7B,EAAaQ,IAA0B,CAC3CuiB,OAAQtiB,EAAAA,GAAG;;;;;;;;;;;;;;;;;;;;;IAsBXw7B,YAAax7B,EAAAA,GAAG;;IAGhBw6B,WAAYx6B,EAAAA,GAAG;;;;;;;;;;IAWf66B,SAAS76B,EAAAA,EAAAA,KAAI,CACX2V,QAAS5V,EAAM+X,QAAQ,EAAG,EAAG,IAAM,KACnC8jB,WAAY,wBACZC,aAAc,aAAa97B,EAAMK,OAAOsY,OAAOC,U,qECnI5C,SAASgO,GAAqB,OAAErjB,EAAM,WAAEw4B,EAAU,aAAElV,IAEzD,IAAIjW,EAAU,GAAGyN,EAAAA,KAEF,WAAX9a,IACFqN,GAAW,oBAGTiW,IACFjW,GAAW,OAAOiW,KAGhBkV,GAAcA,IAAe7Z,EAAAA,KAC/BtR,GAAW,OAAOmrB,YAIpB,IAAIC,EAAW,SACf,OAAQz4B,GACN,IAAK,SACHy4B,EAAW,SACX,MACF,IAAK,WACHA,EAAW,oCAKf,IAAIC,EAAe,GAOnB,OANIF,GAAcA,IAAe7Z,EAAAA,IAC/B+Z,EAAaxpB,KAAKspB,GAKb,IAAInrB,QAAcorB,KAFTC,EAAar6B,OAAS,MAAMq6B,EAAa9oB,KAAK,SAAW,IAG3E,CAEO,SAAS2Q,EAAmBvgB,EAAwB24B,GACzD,MAAO,CACL1xB,MAAO,IACP7B,MAAOie,EAAqB,CAAErjB,SAAQw4B,WAAYG,IAClD9lB,UAAW,UACXkI,UAAW,QACXC,MAAO,IACPC,KAAM,GACN5N,QAAS,GAEb,C,gGC7CO,MAAM2E,UAAwBjO,EAAAA,I,EACT,EAAGqC,YAC3B,MAAM,QAAE6L,EAAO,cAAEE,EAAa,SAAEuf,EAAQ,QAAErf,GAAYjM,EAAMC,WAC5D,OAAO,kBAAC0V,EAAAA,EAAUA,CAAC9J,QAASA,EAASE,cAAeA,EAAeuf,SAAUA,EAAUrf,QAASA,M,EAFpFlM,e,EADH6L,G,q8BCRN,MAAM8P,UAAwBtW,EAAAA,GAM3BotB,eAAAA,GACN,MAAMnyB,GAAOoyB,EAAAA,EAAAA,IAAoB30B,KAAMA,KAAK1E,MAAMuiB,eAClD7d,KAAKpG,SAAS,CACZkJ,QAAS9C,KAAK1E,MAAMwH,QAAQwC,IAAKpE,GACxB,OACFA,GAAAA,CACHqB,YAKiBF,EAAAA,GAAWC,aAAatC,MAChCsB,iBAAiB,CAACiQ,EAAUC,KACzC,GAAID,EAAS5U,MAAMjC,OAAS8W,EAAU7U,MAAMjC,MAAQ6W,EAAS5U,MAAM7B,KAAO0W,EAAU7U,MAAM7B,GAAI,CAC5F,MAAM85B,GAAUD,EAAAA,EAAAA,IAAoB30B,KAAMA,KAAK1E,MAAMuiB,eACrD7d,KAAKpG,SAAS,CACZkJ,QAAS9C,KAAK1E,MAAMwH,QAAQwC,IAAKpE,GACxB,OACFA,GAAAA,CACHqB,KAAMqyB,MAId,GAEJ,CA9BA,WAAAz0B,CAAY7E,GACV8E,MAAM9E,GACN0E,KAAKK,qBAAqBL,KAAK00B,gBAAgBrtB,KAAKrH,MACtD,E,8GCEF,MCca60B,EAAe,EAAGp8B,QAAQ,OAAQC,SAAQo8B,WAAU,MAC/D,MAAMv8B,GAAQsL,EAAAA,EAAAA,cACR,EAAEhJ,EAAC,EAAEI,GDdY,EAAC85B,EAFW,MAGnC,MAAOC,EAAeC,IAAoB9yB,EAAAA,EAAAA,UAAwB,CAAEtH,EAAG,KAAMI,EAAG,OAahF,OAXAqf,EAAAA,EAAAA,WAAU,KACR,MAAM4a,GAAsBC,EAAAA,EAAAA,UAAUrY,IACpCmY,EAAiB,CAAEp6B,EAAGiiB,EAAMsY,QAASn6B,EAAG6hB,EAAMuY,WAC7CN,GAGH,OAFA9tB,OAAOquB,iBAAiB,YAAaJ,GAE9B,KACLjuB,OAAOsuB,oBAAoB,YAAaL,KAEzC,CAACH,IAEGC,GCAUQ,GACX39B,GAASC,EAAAA,EAAAA,YAAWC,EAAW8C,EAAGI,EAAG65B,GAC3C,OAAO,kBAACW,EAAAA,EAAGA,CAACC,IAAKn9B,EAAMo9B,O,gJAA6Bt9B,UAAWR,EAAO+9B,IAAKl9B,OAAQA,EAAQD,MAAOA,KAGpGo8B,EAAavM,YAAc,eAE3B,MAAMvwB,EAAY,CAACQ,EAAsBs9B,EAAqBC,EAAqBhB,KACjF,MAAM,WAAEiB,EAAU,YAAEC,GAAgB/uB,OAC9BgvB,EAAcH,GAAQA,EAAOE,EAC7BE,EAAaL,GAAQA,EAAOE,EAC5BI,EAA2B,OAAhBF,EAAuBG,EAAqBH,GAxBtC,GACA,GAuByF,EAC1GI,EACW,OAAfH,EAAsBE,EAAqBF,GAxBnB,EACA,GAuB2E,EAErG,MAAO,CACLN,KAAKp9B,EAAAA,EAAAA,KAAI,CACP,qCAAsC,CACpC89B,UAAW,UAAUH,oBAA2BE,MAChDE,gBAAiB,SACjBC,WAAY,yBAEd,iBAAkB,CAChBv9B,QAAS67B,EAAU,QAAU,YAU/BsB,EAAuB,CAACK,EAAeC,EAAeC,IAC5CF,GAASE,EAAMD,GAASA,E,cC3CjC,MAAM7e,EAAa,EAAG9J,UAASE,gBAAeuf,WAAUrf,cAC7D,MAAMtW,GAASC,EAAAA,EAAAA,YAAWC,EAAWoW,GAErC,OACE,kBAACpK,MAAAA,CAAI1L,UAAWR,EAAOmM,UAAWC,cAAaC,EAAAA,EAAQ0yB,YACrD,kBAACpf,EAAAA,MAAKA,CAACxb,UAAU,SAAS4T,WAAW,SAASF,IAAK,GACjD,kBAACmlB,EAAYA,CAACp8B,MAAO+0B,QAAAA,EAAY,MACb,iBAAZzf,GAAyB,kBAACsJ,EAAAA,KAAIA,CAACC,cAAe,SAAUtO,QAAQ,MAAM+E,GAC1D,iBAAZA,GAAyBA,EAEhCE,GACC,kBAAClK,MAAAA,CAAI1L,UAAWR,EAAOg/B,QACrB,kBAACrf,EAAAA,MAAKA,CAAC9H,IAAK,GAAKE,WAAY,UAC3B,kBAAC1X,EAAAA,KAAIA,CAACC,KAAK,gBACX,kBAACkf,EAAAA,KAAIA,CAACC,cAAe,SAAUtO,QAAQ,QACpCiF,QAYjB,SAASlW,EAAUQ,EAAsB4V,GACvC,MAAO,CACLnK,WAAWxL,EAAAA,EAAAA,KAAI,CACbC,MAAO,OACPQ,QAAS,OACT0W,eAAgB,eAChBmI,cAAe,SACf3J,QAASA,GAAoB,IAE/B0oB,QAAQr+B,EAAAA,EAAAA,KAAI,CACV4Y,aAAc7Y,EAAM+X,QAAQ,KAGlC,CAfAuH,EAAWyQ,YAAc,Y,uECrClB,MAAMhJ,EAAmBxjB,IAC9B,MAAMg7B,EAA4B,WAAXh7B,IAAuB,EAkB9C,OAhBgBM,EAAAA,GAAcyf,aAC3BniB,UAAU,SAAU,CAAEoiB,YAAY,IAClCI,qBAAqB,YAAa6a,EAAAA,UAAUC,MAC5C9a,qBAAqB,WAAY,CAAEH,KAAMkb,EAAAA,aAAaC,SACtDhb,qBAAqB,cAAe,IACpCA,qBAAqB,YAAa,GAClCA,qBAAqB,YAAa,GAClCA,qBAAqB,YAAa,QAClC7N,aAAcmkB,IACbA,EAAU2E,2BAA2B,MAAMxE,cAAc,CACvD5W,KAAM,QACNuR,WAAYwJ,EAAiB,gBAAkB,YAGlDp9B,UAAU,UAAW,CAAEqiB,KAAMC,EAAAA,mBAAmBC,Q,kYCP9C,MAAMmb,UAAkCv3B,EAAAA,GAC7C,aAAmB,UAAEzF,IACnBgG,MAAM,CAAEhG,cAGV,OAAOi9B,qBAAqB,KAC1B,MAAMvO,GAAiBxvB,EAAAA,EAAAA,IAAuB0G,MAC9C8oB,EAAelvB,SAAS,CAAEQ,UAAW4F,KAAK1E,MAAMlB,aAC3CiB,EAAAA,EAAAA,GAAoBytB,EAAextB,MAAMC,aAC5CutB,EAAettB,cAAc,eAG/BC,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAeE,oBAAqB,CAC7GzB,UAAW4F,KAAK1E,MAAMlB,UACtB0B,QAAQw7B,EAAAA,EAAAA,IAAet3B,SAX3B,EA0CF,SAASjI,EAAUQ,GACjB,MAAO,CACLg/B,SAAS/+B,EAAAA,EAAAA,KAAI,CACXS,QAAS,OACTyW,IAAK,OACLE,WAAY,WAEdnE,aAAajT,EAAAA,EAAAA,KAAI,CACfsX,MAAOvX,EAAMK,OAAOE,KAAK0L,UACzBqL,SAAUtX,EAAM6X,WAAWyX,UAAUhY,SACrC5W,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQmgB,OAGzB,CAzCE,EAlBW2G,EAkBGn1B,YAAY,EAAGC,YAC3B,MAAM,UAAE9H,IAAcd,EAAAA,EAAAA,IAAuB4I,GAAOC,WAC9CtK,GAASC,EAAAA,EAAAA,YAAWC,GAEpBy/B,EAAiC,UAApBp9B,aAAAA,EAAAA,EAAWG,MACxBoP,EAAU6tB,EACZ,oGACAn9B,EAEJ,OACE,kBAAC0J,MAAAA,CAAI1L,UAAWR,EAAO0/B,SACrB,kBAACxuB,EAAAA,OAAMA,CACLC,QAAQ,YACR5Q,KAAK,KACLW,KAAK,QACLg7B,SAAUyD,EACVvuB,KAAM,OACNxI,QAASyB,EAAMm1B,mBACf1tB,QAASA,GAER6tB,EAAa,0BAA4B,4B,uzBCL7C,MAAMjG,UAAiB1xB,EAAAA,GA+HpB43B,UAAAA,GACN,MAA+C,cAAxChmB,EAAAA,EAAAA,IAAkBzR,MAAM1E,MAAMqB,KACvC,CAEQyJ,WAAAA,GACN,MAAMtK,GAAS2V,EAAAA,EAAAA,IAAkBzR,MAAM1E,MAAMqB,MAE7CqD,KAAKpG,SAAS,CACZsV,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI0O,EAAAA,EAAgB,CACzBC,cAAe7d,KAAKy3B,aAAe,GAAK,GACxCr1B,WAAYiU,EAAAA,GACZvT,QAAS,CAAC9C,KAAKy3B,cAAe1K,EAAAA,EAAAA,MAAwB1Q,EAAAA,EAAAA,GAAmBvgB,MAE3EsT,gBAAiBpP,KAAKy3B,aAClB,KAAIzK,EAAAA,EAAAA,MACJ,KAAIlP,EAAAA,EAAAA,IAAyB7I,EAAAA,EAAAA,IAAajV,UAEhDzG,MAAOyG,KAAKitB,eAEhB,CAEQA,WAAAA,GACN,MAAMnxB,GAAS2V,EAAAA,EAAAA,IAAkBzR,MAAM1E,MAAMqB,M,IAET,EADpC,OAAIqD,KAAKy3B,cACAv+B,EAAAA,EAAAA,IAAqB8G,KAAyB,QAAnB,EAAAA,KAAK1E,MAAMlC,gBAAX,QAAuB,IAGpD4G,KAAK03B,uBAAuB57B,EACrC,CAEQ47B,sBAAAA,CAAuBn9B,GAC7B,MAAMhB,GAAQ+lB,EAAAA,EAAAA,GAAgB/kB,GAAMd,gBAAe,GAAM2zB,eAAe,eASxE,MARa,SAAT7yB,EACFhB,EAAM2iB,qBAAqB,YAAa,UACtB,WAAT3hB,GACThB,EAAM2iB,qBAAqB,YAAa,WAAWmR,SAAS,CAC1DC,WAAY,gBACZvR,KAAM,UAGH,IAAIhgB,EAAAA,GAAgB,CACzBC,UAAW,MACXC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM5C,EAAMI,YAIpB,CAEQg+B,wBAAAA,CAAyBr8B,G,IAKlBA,EAAAA,EACAA,EAAAA,EALb,KAAKD,EAAAA,EAAAA,GAAoBC,EAAMC,YAC7B,OAGF,MAAMq8B,EAAsB,QAAft8B,EAAAA,EAAMlB,iBAANkB,IAAAA,GAAoB,QAApBA,EAAAA,EAAiBd,WAAjBc,IAAAA,OAAAA,EAAAA,EAAsBT,EAC7Bg9B,EAAsB,QAAfv8B,EAAAA,EAAMlB,iBAANkB,IAAAA,GAAoB,QAApBA,EAAAA,EAAiBd,WAAjBc,IAAAA,OAAAA,EAAAA,EAAsBL,EAE7BuM,GAAQswB,EAAAA,EAAAA,kBAAiB,CAC7B,CACEnV,MAAMiV,aAAAA,EAAAA,EAAMl9B,OAAQ,EACpBq9B,MAAMH,aAAAA,EAAAA,EAAMl9B,OAAQ,EACpBs9B,MAAMJ,aAAAA,EAAAA,EAAM98B,KAAM,EAClBm9B,SAASL,aAAAA,EAAAA,EAAM98B,KAAM,EACrBo9B,KAAML,aAAAA,EAAAA,EAAMn9B,KACZy9B,KAAMN,aAAAA,EAAAA,EAAM/8B,GACZs9B,UAAU,EACVvR,YAAa,IACbE,UAAW,EACXsR,UAAW,QACXvoB,MAAOsa,EAAAA,GACPtxB,KAAM,0BAKV,OAFA0O,EAAMrP,KAAO,SAEN,CAACqP,EACV,CA5MA,WAAArH,CAAY7E,GACV8E,MAAM,GACJhH,SAAU,GACVomB,QAAS,GACT7nB,aAAa,GACV2D,IAGL0E,KAAKK,qBAAqB,KACxBL,KAAKoG,cACL,MAAMG,EAAOlE,EAAAA,GAAWmE,QAAQxG,MAC1B3G,GAASC,EAAAA,EAAAA,IAAuB0G,MAChCvF,EAAY4H,EAAAA,GAAWC,aAAatC,MAE1CA,KAAKoB,MAAMC,IACTkF,EAAKjF,iBAAkBg3B,I,IACQA,EAEzBA,EAyEOA,EAzEX,GAFAt4B,KAAKpG,SAAS,CAAEjC,aAAyB,QAAZ2gC,EAAAA,EAAQ/xB,YAAR+xB,IAAAA,OAAAA,EAAAA,EAAch9B,SAAUiS,EAAAA,aAAaG,aAElD,QAAZ4qB,EAAAA,EAAQ/xB,YAAR+xB,IAAAA,OAAAA,EAAAA,EAAch9B,SAAUiS,EAAAA,aAAaK,KACvC,GACiC,IAA/B0qB,EAAQ/xB,KAAKoH,OAAOxT,QACc,IAAlCm+B,EAAQ/xB,KAAKoH,OAAO,GAAGxT,SACvBozB,EAAAA,EAAAA,IAAoB+K,GAEpBt4B,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAI2R,EAAAA,EAAgB,CACxBC,QAASC,EAAAA,GACTwf,SAAU,iBAMf,CACL,IAAIp0B,EAAiC,GACrC,GAAI4G,KAAKy3B,aAAc,C,IACElxB,EACO+xB,EAA9B,GADAl/B,EAAWm/B,GAA2B,QAAfhyB,EAAAA,EAAKjL,MAAMiL,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBoH,SAAU,IAC9CtU,EAAOiC,MAAMlB,YAAyB,QAAZk+B,EAAAA,EAAQ/xB,YAAR+xB,IAAAA,OAAAA,EAAAA,EAAch9B,SAAUiS,EAAAA,aAAaK,KAAM,C,IAInDrH,EAAAA,EAFpB,MAAMwY,EAAc/e,KAAK23B,yBAAyBt+B,EAAOiC,OAErDyjB,KAA+B,QAAfxY,EAAAA,EAAKjL,MAAMiL,YAAXA,IAAAA,GAA4B,QAA5BA,EAAAA,EAAiBwY,mBAAjBxY,IAAAA,OAAAA,EAAAA,EAA8BpM,SAChDoM,EAAK3M,SAAS,CACZ2M,KAAM,OACDA,EAAKjL,MAAMiL,MAAI,CAClBwY,YAAaA,KAIrB,CAEA,GAAI3lB,aAAAA,EAAAA,EAAUe,OAAQ,C,IAyBfd,EAxBL,MAAM,YAAEm/B,EAAW,UAAEC,GAAcC,EAAuBt/B,GACpDgB,EAAiC,CAAEG,KAAM,SAE/Co+B,EAAAA,EAAAA,IAA4B34B,MAAMiP,cAAcupB,IAChDI,EAAAA,EAAAA,IAAmC54B,MAAMiP,eACvCjU,EAAAA,EAAAA,IAAkBy9B,EAAY,EAAGr/B,EAAU,KAG7CgB,EAAUe,SAAW,CAAET,KAAM89B,EAAa19B,GAAI,IAC9CV,EAAUI,IAAM,CACdK,EAAG,CACDH,KAA0C,IAApCD,EAAUa,MAAMqB,MAAMjC,KAAKsuB,OACjCluB,GAAsC,IAAlCL,EAAUa,MAAMqB,MAAM7B,GAAGkuB,QAE/B/tB,EAAG,CAAEP,KAAM+9B,EAAY,GAAK39B,GAAI1B,EAASe,OAAS,KAGpD6F,KAAKpG,SAAS,CACZ4lB,QAAS,CACP,IAAI4X,EAA0B,CAC5Bh9B,kBAIqB,QAAtBf,EAAAA,EAAOiC,MAAMlB,iBAAbf,IAAAA,OAAAA,EAAAA,EAAwB8B,WAA4C,SAAhC9B,EAAOiC,MAAMlB,UAAUG,MAC9DlB,EAAOO,SAAS,CAAEQ,aAEtB,CACF,CAGA4F,KAAKpG,SAAS,CACZR,WACAG,MAAOyG,KAAKitB,eAEhB,MACqB,QAAZqL,EAAAA,EAAQ/xB,YAAR+xB,IAAAA,OAAAA,EAAAA,EAAch9B,SAAUiS,EAAAA,aAAaC,SAC9CxN,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBC,UAAW,SACXC,SAAU,CACR,IAAI2H,EAAAA,EAAkB,CACpBE,UAAW,KAAM2K,EAAAA,EAAAA,IAAkB,aASjDzO,KAAKoB,MAAMC,IACThI,EAAOiI,iBAAiB,CAACiQ,EAAUC,K,IAC7BjL,EAAJ,IAAmB,QAAfA,EAAAA,EAAKjL,MAAMiL,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBjL,SAAUiS,EAAAA,aAAaK,SACrCib,EAAAA,EAAAA,SAAQtX,EAASnX,UAAWoX,EAAUpX,YAAcmX,EAAShW,aAAeiW,EAAUjW,aACrFyE,KAAKy3B,aAAc,CACrB,MAAM1Y,EAAc/e,KAAK23B,yBAAyBpmB,GAClDhL,EAAK3M,SAAS,CACZ2M,KAAM,OACDA,EAAKjL,MAAMiL,MAAI,CAClBwY,YAAaA,KAGnB,MAMZ,EAkFA,EA/MWwS,EA+MGtvB,YAAY,EAAGC,YAC3B,MAAM,MAAE3I,EAAK,QAAEimB,EAAO,YAAE7nB,GAAgBuK,EAAMC,YACtCxF,MAAOb,IAAW2V,EAAAA,EAAAA,IAAkBvP,GAAOC,WAC7CtK,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,IAAKwB,EACH,OAGF,MAsBMg3B,EAPG,aADCz0B,EAEG,qDAEA,GAMb,OACE,kBAACiI,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAOghC,iBACrB,kBAAC90B,MAAAA,CAAI1L,UAAWR,EAAOihC,gBACrB,kBAAC/0B,MAAAA,CAAI1L,UAAWR,EAAOkhC,mBACrB,kBAAC3K,EAAAA,gBAAeA,CACdj2B,KAAM,UAAU2D,IAChBwO,QAAS,CAAC,CAAEnD,MAAO,GAAIxK,MAAO,aAC9BA,MAAO,aAET,kBAACmU,OAAAA,KAlCM,MACf,OAAQhV,GACN,IAAK,SACH,MAAO,cACT,IAAK,OACH,MAAO,YACT,IAAK,WACH,MAAO,wBACT,QACE,MAAO,KAyBI8iB,KAER2R,GAAY,kBAACxsB,MAAAA,CAAI1L,UAAWR,EAAO04B,UAAWA,IAEjD,kBAACxsB,MAAAA,CAAI1L,UAAWR,EAAO2nB,SACpB7nB,GAAe,kBAACD,EAAAA,EAAkBA,CAACC,aAAa,EAAMC,SAAU,KAChE4nB,aAAAA,EAAAA,EAASla,IAAKlI,GAAW,kBAACA,EAAO6E,UAAS,CAACC,MAAO9E,EAAQyL,IAAKzL,EAAO9B,MAAMuN,SAGjF,kBAACtP,EAAM0I,UAAS,CAACC,MAAO3I,OAMzB,MAAMg/B,EAAe5qB,GACnBA,EAAOrI,IAAK2N,GAAMmR,WAAWnR,EAAEvL,OAAO,GAAGvP,OAAO+S,KAAK,CAACC,EAAGC,IAAMD,EAAIC,GAG/DstB,EAA0Bt/B,IACrC,MAAM4/B,EAAiBr+B,KAAKqC,MAAM5D,EAASe,OAAS,GACpD,IAAIs+B,EAAYr/B,EAASe,OAAS6+B,EAAiB,EAKnD,OAJIP,EAAY,IACdA,EAAY,GAGP,CACLD,aAAax9B,EAAAA,EAAAA,IAAkBy9B,EAAY,EAAGr/B,GAC9Cq/B,cAIJ,SAAS1gC,EAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbC,MAAO,OACPQ,QAAS,OACT6e,cAAe,SACf5G,OAAQ,aAAa3Y,EAAMK,OAAOsY,OAAOC,OACzCnY,aAAc,MACduL,WAAYhM,EAAMK,OAAO2L,WAAW0M,QAEpC,iBAAkB,CAChBhY,QAAS,QAEX,yBAA0B,CACxB20B,YAAa,eAEf,cAAe,CACb1c,OAAQ,yBAGZ2nB,iBAAiBrgC,EAAAA,EAAAA,KAAI,CACnBC,MAAO,OACPQ,QAAS,OACT6e,cAAe,MACf3J,QAAS,MACTuB,IAAK,MACLC,eAAgB,gBAChBC,WAAY,aACZ+gB,WAAYp4B,EAAM6X,WAAW6oB,iBAE/BH,gBAAgBtgC,EAAAA,EAAAA,KAAI,CAClBS,QAAS,OACT6e,cAAe,SACfpI,IAAK,QAEPqpB,mBAAmBvgC,EAAAA,EAAAA,KAAI,CACrBS,QAAS,OACT2W,WAAY,WAEd4P,SAAShnB,EAAAA,EAAAA,KAAI,CACXS,QAAS,OACTyW,IAAK,MACLE,WAAY,WAEd2gB,UAAU/3B,EAAAA,EAAAA,KAAI,CACZS,QAAS,OACT6W,MAAOvX,EAAMK,OAAOE,KAAK0L,UACzBqL,SAAU,OACV8gB,WAAY,IAEZ,QAAS,CACP3Y,OAAQ,WAIhB,C,+CChYO,MAAM9T,EAAU,CACrB0yB,WAAY,0BACZsC,WAAY,0BACZ/0B,aAAc,4B,sKCMT,MAAMg1B,UAAwBt5B,EAAAA,I,EACT,EAAGqC,YAC3B,MAAM,QAAE6L,GAAY7L,EAAMC,WAC1B,OACE,kBAACi3B,EAAAA,MAAKA,CAACjyB,MAAO,cAAekyB,SAAU,QAASp1B,cAAaC,EAAAA,EAAQg1B,YAClEnrB,K,EAJO9L,e,EADHk3B,G,oGCCN,MAAMG,EAAU7sB,IACrB,MAAM5U,GAASC,EAAAA,EAAAA,YAAWC,IACpB,YAAEwhC,EAAW,oBAAEC,GAAwB/sB,EAE7C,OACE,kBAACpB,EAAAA,MAAKA,CAAChT,UAAWR,EAAO4hC,aACvB,kBAACC,EAAAA,MAAKA,CACJjuB,YAAY,SACZK,OAAQ,kBAAC5T,EAAAA,KAAIA,CAACC,KAAM,WACpBwE,MAAO48B,EACPhvB,SAAUivB,EACV/zB,GAAG,uBAMX,SAAS1N,EAAUQ,GACjB,MAAO,CACLkhC,aAAajhC,EAAAA,EAAAA,KAAI,CACf4Y,aAAc7Y,EAAM+X,QAAQ,KAGlC,C,i0BC/BO,SAASqpB,EAAenyB,GAC7B,OAAO,OACFA,GAAAA,CACHE,OAAQF,EAAME,OAAOpC,IAAKsD,GAAkB,OACvCA,GAAAA,CACHZ,OAAQY,EAAMZ,WAGpB,C,yyBC4BO,MAAMoW,UAAwBve,EAAAA,GAmF3B+5B,kBAAAA,CAAmBC,GACrBA,EAASlsB,QAAUksB,EAASlsB,OAAOxT,OAAS,EAC9C6F,KAAK85B,cAAcD,GAEnB75B,KAAK1E,MAAMa,KAAKvC,SAAS,CACvBqC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAI2R,EAAAA,EAAgB,CACxBC,QAAS,0BACTI,QAAS,aAMrB,CAEQ4rB,aAAAA,CAAcxzB,EAAiBmY,GACrC,MAAMsb,EAAczzB,EAAKoH,OAAOjD,OAC9B,CAACC,EAAKgD,K,IACQA,EAAAA,EAAZ,MAAM9E,EAA2D,QAArD8E,EAAAA,EAAOjG,OAAO9F,KAAM+F,GAAMA,EAAEpN,OAASkb,EAAAA,UAAUM,eAA/CpI,IAAAA,GAA8D,QAA9DA,EAAAA,EAAwDpF,cAAxDoF,IAAAA,OAAAA,EAAAA,EAAiE+Q,GAC7E,OAAK7V,GAGA8B,EAAI9B,KACP8B,EAAI9B,GAAO,IAEb8B,EAAI9B,GAAKmC,KAAK2C,GACPhD,GANEA,GAQX,CAAC,GAGGsvB,EAAY,GAClB,IAAK,MAAMpxB,KAAOmxB,EAAa,CAC7B,MAAM3Q,EAAS2Q,EAAYnxB,GAAKqC,KAAK,CAACC,EAAGC,K,IAAMD,E,OAAM,QAANA,EAAAA,EAAEhT,YAAFgT,IAAAA,OAAAA,EAAAA,EAAQ8T,cAAc7T,EAAEjT,QAAU,IAC3E+hC,EAAYP,EAAetQ,EAAO,IACxCA,EAAOxe,MAAM,EAAGwe,EAAOlvB,QAAQ6iB,QAASxV,GAAU0yB,EAAUxyB,OAAOsD,KAAKxD,EAAME,OAAO,KACrFuyB,EAAUjvB,MAAKmvB,EAAAA,EAAAA,eAAcD,EAAW,GAC1C,CACA,OAAOD,CACT,CAEQH,aAAAA,CAAcvzB,GACpB,MAAM6zB,EAA+B,GACrC,IAAI/Q,EAAS9iB,EAAKoH,OAEd3N,KAAK1E,MAAMojB,UACb2K,EAASrpB,KAAK+5B,cAAcxzB,GAAM2Z,EAAAA,EAAAA,IAAmBlgB,MAAMsc,iBAG7D,IAAK,IAAI+d,EAAa,EAAGA,EAAahR,EAAOlvB,OAAQkgC,IAAc,CAMjE,GAAY,IALShR,EAAOgR,GAEH3yB,OACtB7F,OAAQ8F,GAAMA,EAAEpN,OAASkb,EAAAA,UAAUM,QACnCrL,OAAO,CAAC4vB,EAAK3yB,IAAM2yB,EAAM3yB,EAAEK,OAAO0C,OAAO,CAAC6vB,EAAMrd,IAAMqd,GAAQrd,GAAK,GAAI,IAAM,EAAG,GAEjF,SAGF,MAAMsd,EAAcx6B,KAAK1E,MAAMqjB,eAAepY,EAAM8iB,EAAOgR,GAAaA,GACxED,EAAYpvB,KAAKwvB,EACnB,CAEAx6B,KAAK1E,MAAMa,KAAKvC,SAAS,CAAEqC,SAAUm+B,GACvC,CApJA,YAAmB9+B,GACjB8E,MAAM9E,GAoER,OAAQk+B,sBAAuBiB,IAC7Bz6B,KAAKpG,SAAS,CAAE2/B,YAAakB,EAAIC,cAAc/9B,UAGjD,OAAQg+B,gCAA+BC,EAAAA,EAAAA,UAAUrB,I,IAIrChzB,EAHV,MAAMA,EAAOlE,EAAAA,GAAWmE,QAAQxG,MAC1B65B,EAAW,OACZtzB,EAAKjL,MAAMiL,MAAI,CAClBoH,OAAuB,QAAfpH,EAAAA,EAAKjL,MAAMiL,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBoH,OAAO9L,OAAOg5B,EAA8BtB,MAEvEv5B,KAAK45B,mBAAmBC,IACvB,MA7ED75B,KAAKK,qBAAqB,KACxB,MAAMkG,EAAOlE,EAAAA,GAAWmE,QAAQxG,MAEhCA,KAAKoB,MAAMC,IACTkF,EAAKjF,iBAAkBiF,I,IACjBA,EAA0CA,EAqBnCA,EApB4BA,EAY1BA,EAbb,IAAa,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaK,OAAiB,QAATrH,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaG,WAC9E,GAAgC,IAA5BnH,EAAKA,KAAKoH,OAAOxT,SAAyB,QAAToM,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaG,UACrE1N,KAAK1E,MAAMa,KAAKvC,SAAS,CACvBqC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAI2R,EAAAA,EAAgB,CACxBC,QAASC,EAAAA,GACTC,cAAeC,EAAAA,GACfC,QAAS,mBAKZ,IAAa,QAAT5H,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaK,KAAM,C,IAGvCrH,EAFV,MAAMszB,EAAW,OACZtzB,EAAKA,MAAI,CACZoH,OAAiB,QAATpH,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWoH,OAAO9L,OAAOg5B,EAA8B76B,KAAK1E,MAAMi+B,gBAE5Ev5B,KAAK45B,mBAAmBC,GACxB75B,KAAK+M,aAAa,IAAI8P,EAAAA,GAA4B,CAAElP,OAAQpH,EAAKA,KAAKoH,UAAW,EACnF,OACK,IAAa,QAATpH,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAa1H,MAAO,C,IAM/BU,EAAAA,EAAAA,EALnBvG,KAAK1E,MAAMa,KAAKvC,SAAS,CACvBqC,SAAU,CACR,IAAIoiB,EAAAA,GAAmB,CACrBpiB,SAAU,CACR,IAAIk9B,EAAgB,CAClBprB,QAAuC,QAA9BxH,EAAgB,QAAhBA,EAAAA,EAAKA,KAAKu0B,cAAVv0B,IAAAA,GAAqB,QAArBA,EAAAA,EAAmB,UAAnBA,IAAAA,OAAAA,EAAAA,EAAuBwH,eAAvBxH,IAAAA,EAAAA,EAAkC,wCAMvD,MACEvG,KAAK1E,MAAMa,KAAKvC,SAAS,CACvBqC,SAAU,CACR,IAAIoiB,EAAAA,GAAmB,CACrBpiB,SAAU,CACR,IAAI2H,EAAAA,EAAkB,CACpBE,UAAW,IAAM2K,EAAkB,cAUnDzO,KAAKsB,iBAAiB,CAACiQ,EAAUC,K,IAEKD,EADhCA,EAASgoB,cAAgB/nB,EAAU+nB,aACrCv5B,KAAK26B,6BAAiD,QAApBppB,EAAAA,EAASgoB,mBAAThoB,IAAAA,EAAAA,EAAwB,MAI1DhL,EAAKjL,MAAMiL,MACbvG,KAAK85B,cAAcvzB,EAAKjL,MAAMiL,OAGpC,EAgGF,SAASxO,IACP,MAAO,CACLiM,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACT6e,cAAe,SACf+I,SAAU,IAGhB,CArBE,EAvJWzC,EAuJGnc,YAAY,EAAGC,YAC3B,MAAM,KAAE/F,EAAI,YAAEo9B,GAAgBr3B,EAAMC,WAC9BtK,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACs1B,EAAMA,CAACC,YAAaA,QAAAA,EAAe,GAAIC,oBAAqBt3B,EAAMs3B,sBACnE,kBAACr9B,EAAK8F,UAAS,CAACC,MAAO/F,OAgBxB,MAAMsS,EAAqBssB,IAChC,MAAMljC,GAASC,EAAAA,EAAAA,YAAWyY,GAE1B,OACE,kBAACxM,MAAAA,CAAI1L,UAAWR,EAAOmM,WACpB,IAAI0M,MAAMqqB,IAASz1B,IAAI,CAACqL,EAAG5I,IAC1B,kBAAChE,MAAAA,CAAI1L,UAAWR,EAAOmjC,cAAenyB,IAAKd,GACzC,kBAAChE,MAAAA,CAAI1L,UAAWR,EAAO0X,QACrB,kBAACxL,MAAAA,CAAI1L,UAAWR,EAAOsP,OACrB,kBAACqJ,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAOuF,QACrB,kBAACoT,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAOojC,OACpB,IAAIvqB,MAAM,IAAIpL,IAAI,CAACqL,EAAG5I,IACrB,kBAAChE,MAAAA,CAAI1L,UAAWR,EAAOqjC,UAAWryB,IAAKd,GACrC,kBAACyI,EAAAA,EAAQA,CAACC,MAAO,OAIvB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAOsjC,OACpB,IAAIzqB,MAAM,IAAIpL,IAAI,CAACqL,EAAG5I,IACrB,kBAAChE,MAAAA,CAAI1L,UAAWR,EAAOujC,UAAWvyB,IAAKd,GACrC,kBAACyI,EAAAA,EAAQA,CAACC,MAAO,WAUjC,SAASF,EAAkBhY,GACzB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACToiC,oBAAqB9c,EAAAA,GACrB+c,aAAc,QACdC,OAAQhjC,EAAM+X,QAAQ,GACtBkrB,UAAWjjC,EAAM+X,QAAQ,KAE3B0qB,eAAexiC,EAAAA,EAAAA,KAAI,CACjBG,gBAAiBJ,EAAMK,OAAO2L,WAAW0M,QACzCC,OAAQ,aAAa3Y,EAAMK,OAAO2L,WAAWC,YAC7C2J,QAAS,QAEXoB,QAAQ/W,EAAAA,EAAAA,KAAI,CACVS,QAAS,OACT0W,eAAgB,kBAElBxI,OAAO3O,EAAAA,EAAAA,KAAI,CACTC,MAAO,UAET2E,QAAQ5E,EAAAA,EAAAA,KAAI,CACVC,MAAO,SAETwiC,OAAOziC,EAAAA,EAAAA,KAAI,CACTS,QAAS,OACT6e,cAAe,SACfnI,eAAgB,eAChB8rB,UAAW,SAEbP,WAAW1iC,EAAAA,EAAAA,KAAI,CACbC,MAAO,OACPC,OAAQ,SAEVyiC,OAAO3iC,EAAAA,EAAAA,KAAI,CACTS,QAAS,OACT0W,eAAgB,iBAElByrB,WAAW5iC,EAAAA,EAAAA,KAAI,CACbC,MAAO,SAGb,CAEO,MAAMoiC,EAAiCtB,GAA0BmC,IACtE,MAAMC,EAAUpC,aAAAA,EAAAA,EAAa1W,OAC7B,IAAK8Y,EACH,OAAO,EAGT,MAAMC,EAAQ,IAAIC,OAAOF,EAAS,KAElC,OAAOD,EAAUh0B,OAAO6S,KAAM5S,KAAQA,EAAEY,QAAiBvC,OAAOgC,OAAOL,EAAEY,QAAQ3G,KAAMoD,GAAU42B,EAAME,KAAK92B,K,kECzSvG,MAAMoE,EAAqB,sBAErBsqB,EAAuD,CAClE,CACE1uB,MAAO,aACPrI,MAAO,oBACPkF,OAAQ,CAAEgH,IAAK,kBAAmBU,SAAU,IAAK5M,MAAO,KACxD6S,YAAa,sDAEf,CACExK,MAAO,YACPrI,MAAO,OACPkF,OAAQ,CAAEgH,IAAK,GAAIU,SAAU,GAAI5M,OAAO,GACxC6S,YAAa,+EAEf,CACExK,MAAO,eACPrI,MAAO,cACPkF,OAAQ,CAAEgH,IAAK,OAAQU,SAAU,IAAK5M,MAAO,UAC7C6S,YAAa,8CAEf,CACExK,MAAO,iBACPrI,MAAO,gBACPkF,OAAQ,CAAEgH,IAAK,OAAQU,SAAU,IAAK5M,MAAO,YAC7C6S,YAAa,uDAEf,CACExK,MAAO,iBACPrI,MAAO,GAAGyM,QACVvH,OAAQ,CAAEgH,IAAKO,EAAoBG,SAAU,KAAM5M,MAAO,MAC1D6S,YAAa,6DAIJusB,EAAmBlzB,GACvB6qB,EAAqB9xB,KAAMiY,GAAWA,EAAOld,QAAUkM,E,kcCpBzD,MAAMmzB,EAAqBvvB,IAChC,MAAM,MAAEiP,EAAK,KAAEnhB,EAAI,MAAEyK,EAAK,WAAEi3B,EAAU,MAAEt/B,EAAK,WAAEu/B,EAAU,IAAEl1B,GAAQyF,EAC7D5U,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAACgM,MAAAA,CAAI8E,IAAK6S,GACG,IAAVA,GACC,kBAAC3X,MAAAA,CAAI1L,UAAWR,EAAOskC,WACrB,kBAACrrB,OAAAA,KAAMmrB,GACP,kBAACnrB,OAAAA,CAAKzY,UAAWR,EAAOqkC,YAAaA,IAIzC,kBAACn4B,MAAAA,CACC1L,UAAWR,EAAO+Y,IAClB/H,IAAK6S,EACLjb,QAAS,MACPhF,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBgC,KAAM9B,EAAAA,GAAoB8B,KAAKkB,kBAAmB,CACvFrE,OACAmhB,QACA/e,UAEFy/B,EAAAA,gBAAgBpxB,KAAKhE,KAGvB,kBAACjD,MAAAA,CAAI1L,UAAW,YAAa2M,GAE7B,kBAACjB,MAAAA,CAAI1L,UAAWR,EAAOuF,QACrB,kBAAC0T,OAAAA,CAAKzY,UAAWR,EAAOwkC,YAAa1/B,GACrC,kBAACzE,EAAAA,KAAIA,CAACG,UAAWR,EAAOykC,WAAYnkC,KAAK,cAAcC,KAAK,WAOtE,SAASL,EAAUQ,GACjB,MAAO,CACL4jC,WAAW3jC,EAAAA,EAAAA,KAAI,CACbsX,MAAOvX,EAAMK,OAAOE,KAAK0L,UACzBvL,QAAS,OACT0W,eAAgB,gBAChBC,WAAY,SACZzB,QAAS,KAAK5V,EAAM+X,QAAQ,MAAM/X,EAAM+X,QAAQ,MAAM/X,EAAM+X,QAAQ,OAEtE4rB,YAAY1jC,EAAAA,EAAAA,KAAI,CACdwf,OAAQ,eAEVpH,KAAKpY,EAAAA,EAAAA,KAAI,CACPS,QAAS,OACT0W,eAAgB,gBAChBC,WAAY,SACZF,IAAKnX,EAAM+X,QAAQ,GACnBnC,QAAS,GAAG5V,EAAM+X,QAAQ,QAAS/X,EAAM+X,QAAQ,KAEjD,UAAW,CACT3X,gBAAiBJ,EAAMo9B,OAASp9B,EAAMK,OAAO2L,WAAWC,UAAYjM,EAAMK,OAAO2L,WAAW0M,QAC5FlB,OAAQ,UACR,YAAa,CACXI,eAAgB,gBAItB/S,QAAQ5E,EAAAA,EAAAA,KAAI,CACVS,QAAS,OACT2W,WAAY,WAEdysB,YAAY7jC,EAAAA,EAAAA,KAAI,CACdsX,MAAO,UACP3B,QAAS,KAAK5V,EAAM+X,QAAQ,KAC5B7X,MAAO,gBAET6jC,YAAY9jC,EAAAA,EAAAA,KAAI,CACduX,OAAQ,UACRiI,OAAQ,KAAKzf,EAAM+X,QAAQ,SAAU/X,EAAM+X,QAAQ,OAGzD,CClFO,MAAMisB,EAAuB9vB,I,IAgC7BkB,EA/BL,MAAM,OAAEA,EAAM,KAAEpT,GAASkS,EACnB5U,GAASC,EAAAA,EAAAA,YAAWC,GAEpBwwB,EAAYnc,I,IAETowB,EADP,MAAMA,EAAcpwB,EAAG1E,OAAO9F,KAAM+F,GAAiB,SAAXA,EAAExP,M,IACrCqkC,EAAP,OAAoE,QAA7DA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAaj0B,cAAbi0B,IAAAA,OAAAA,EAAAA,EAAsB,yBAAyBh/B,QAAQ,KAAM,WAA7Dg/B,IAAAA,EAAAA,EAAoE,0BAGvEC,EAAUrwB,IACd,MACMswB,EAAS,CACb,cAAe,2BAFGnU,EAASnc,KAG3B,aAAc,UAEhB,OAAOjJ,EAAAA,QAAQC,UAAUu5B,EAAAA,GAAoBD,IAGzCE,EAAgBxwB,I,IAGlBowB,EAFF,MAAMA,EAAcpwB,EAAG1E,OAAO9F,KAAM+F,GAAiB,SAAXA,EAAExP,M,IAE1CqkC,EADF,OAMK,QALHA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAax0B,cAAbw0B,IAAAA,OAAAA,EAAAA,EAAqB9xB,OAAO,CAAC7P,EAAG8P,IACb,iBAAN9P,GAAmBoC,MAAMpC,GAG7B8P,EAFE9P,EAAI8P,EAGZ,UALH6xB,IAAAA,EAAAA,EAKS,GAIb,OACE,kBAACz4B,MAAAA,CAAI1L,UAAWR,EAAOmM,WAGT,QAFX2J,EAAAA,EACEzC,KAAK,CAACC,EAAGC,IAAMwxB,EAAaxxB,GAAKwxB,EAAazxB,IAC9CN,MAAM,EAAG,WAFX8C,IAAAA,OAAAA,EAAAA,EAGGrI,IAAI,CAAC8G,EAAIsP,IACT,kBAAC5K,OAAAA,CAAKjI,IAAK6S,GACT,kBAACsgB,EAAiBA,CAChBzhC,KAAMA,EACNmhB,MAAOA,EACP1W,MAAOujB,EAASnc,GAChB6vB,WAAW,UACXt/B,MAAOigC,EAAaxwB,GACpB8vB,WAAW,eACXl1B,IAAKy1B,EAAOrwB,SAQ1B,SAASrU,EAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACb2V,QAAS,GAAG5V,EAAM+X,QAAQ,SAGhC,C,sICzDO,MAAMusB,EAAqBpwB,IAChC,MAAM,OAAEkB,EAAM,KAAEpT,GAASkS,EACnB5U,GAASC,EAAAA,EAAAA,YAAWC,GAEpB+kC,EAAWnvB,EAAO,GAAGjG,OAAO9F,KAAM+F,GAAiB,aAAXA,EAAExP,MAChD,GAAI2kC,GAAYA,EAAS90B,OAAQ,C,IACN80B,EA0DpBpwB,EA1DL,MAAMqwB,EAAmBD,SACR/0B,QADQ+0B,EAAAA,EAAU90B,OAChC1C,IAAI,CAACqL,EAAG5I,IAAMA,UADQ+0B,IAAAA,OAAAA,EAAAA,EAErB5xB,KAAK,CAACC,EAAGC,KAAM0xB,aAAAA,EAAAA,EAAU90B,OAAOoD,KAAK0xB,aAAAA,EAAAA,EAAU90B,OAAOmD,KACpD6xB,EAAervB,EAAO,GAAGjG,OAAOpC,IAAKqC,IACzC,O,oUAAO,IACFA,G,WAAAA,CACHK,OAAQ+0B,aAAAA,EAAAA,EAAkBz3B,IAAKyC,GAAMJ,EAAEK,OAAOD,M,sVAI5CwgB,EAAW,CAAC0U,EAAsCC,EAAmCxhB,KACzF,IAAI1W,EAAQ,GAOZ,OANIi4B,aAAAA,EAAAA,EAAmBj1B,OAAO0T,MAC5B1W,EAAQi4B,EAAkBj1B,OAAO0T,KAE/BwhB,aAAAA,EAAAA,EAAgBl1B,OAAO0T,MACzB1W,EAAyB,IAAjBA,EAAM7K,OAAe+iC,EAAel1B,OAAO0T,GAAS,GAAG1W,MAAUk4B,EAAel1B,OAAO0T,MAEzE,IAAjB1W,EAAM7K,OAAe,iCAAmC6K,GAG3Dy3B,EAAS,CACb7vB,EACAD,EACAswB,EACAvhB,KAEA,KAAK/O,GAAgBA,EAAY3E,OAAO0T,IAAWuhB,GAAsBA,EAAkBj1B,OAAO0T,IAEhG,OADAyhB,QAAQC,MAAM,oCACPC,EAAAA,GAAOC,QAGhB,MAAMZ,EAAS,CACb9vB,UACAE,OAAQH,EAAY3E,OAAO0T,GAC3B,cAAe,2BAA2BuhB,EAAkBj1B,OAAO0T,KACnE,aAAc,YAGhB,OAAOvY,EAAAA,QAAQC,UAAUu5B,EAAAA,GAAoBD,IAGzCa,EAAc,CAACC,EAAkC9hB,IAChD8hB,GAAkBA,EAAcx1B,QAI9By1B,EAAAA,EAAAA,IAAeD,EAAcx1B,OAAO0T,GAAS,KAH3C,qBAMLhP,EAAeswB,EAAap7B,KAAM+F,GAAiB,kBAAXA,EAAExP,MAC1CwU,EAAcqwB,EAAap7B,KAAM+F,GAAiB,WAAXA,EAAExP,MACzC+kC,EAAiBF,EAAap7B,KAAM+F,GAAiB,cAAXA,EAAExP,MAC5C8kC,EAAoBD,EAAap7B,KAAM+F,GAAiB,iBAAXA,EAAExP,MAC/CqlC,EAAgBR,EAAap7B,KAAM+F,GAAiB,aAAXA,EAAExP,MAEjD,OACE,kBAAC4L,MAAAA,CAAI1L,UAAWR,EAAOmM,WACpB0I,SAAoB,QAApBA,EAAAA,EAAc1E,cAAd0E,IAAAA,OAAAA,EAAAA,EAAsBpH,IAAI,CAACsH,EAAS8O,IACnC,kBAAC5K,OAAAA,CAAKjI,IAAK6S,GACT,kBAACsgB,EAAiBA,CAChBzhC,KAAMA,EACNmhB,MAAOA,EACP1W,MAAOujB,EAAS0U,EAAmBC,EAAgBxhB,GACnDugB,WAAW,QACXt/B,MAAO4gC,EAAYC,EAAe9hB,GAClCwgB,WAAW,WACXl1B,IAAKy1B,EAAO7vB,EAASD,EAAaswB,EAAmBvhB,OAMjE,CACA,OAAO,MAGT,SAAS3jB,EAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACb2V,QAAS,GAAG5V,EAAM+X,QAAQ,SAGhC,CCzFO,MAAMotB,EAAuBjxB,I,IAgC7BkB,EA/BL,MAAM,OAAEA,EAAM,KAAEpT,GAASkS,EACnB5U,GAASC,EAAAA,EAAAA,YAAWC,GAEpBwwB,EAAYnc,I,IAETowB,EADP,MAAMA,EAAcpwB,EAAG1E,OAAO9F,KAAM+F,GAAiB,SAAXA,EAAExP,M,IACrCqkC,EAAP,OAAoE,QAA7DA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAaj0B,cAAbi0B,IAAAA,OAAAA,EAAAA,EAAsB,yBAAyBh/B,QAAQ,KAAM,WAA7Dg/B,IAAAA,EAAAA,EAAoE,0BAGvEC,EAAUrwB,IACd,MACMswB,EAAS,CACb,cAAe,2BAFGnU,EAASnc,KAG3B,aAAc,YAEhB,OAAOjJ,EAAAA,QAAQC,UAAUu5B,EAAAA,GAAoBD,IAGzCa,EAAenxB,I,IAGjBowB,EAFF,MAAMA,EAAcpwB,EAAG1E,OAAO9F,KAAM+F,GAAiB,SAAXA,EAAExP,M,IAE1CqkC,EADF,OAMK,QALHA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAax0B,cAAbw0B,IAAAA,OAAAA,EAAAA,EAAqB9xB,OAAO,CAAC7P,EAAG8P,IACb,iBAAN9P,GAAmBoC,MAAMpC,GAG7B8P,EAFE9P,EAAI8P,EAGZ,UALH6xB,IAAAA,EAAAA,EAKS,GAIb,OACE,kBAACz4B,MAAAA,CAAI1L,UAAWR,EAAOmM,WAGT,QAFX2J,EAAAA,EACEzC,KAAK,CAACC,EAAGC,IAAMmyB,EAAYnyB,GAAKmyB,EAAYpyB,IAC5CN,MAAM,EAAG,WAFX8C,IAAAA,OAAAA,EAAAA,EAGGrI,IAAI,CAAC8G,EAAIsP,IACT,kBAAC5K,OAAAA,CAAKjI,IAAK6S,GACT,kBAACsgB,EAAiBA,CAChBzhC,KAAMA,EACNmhB,MAAOA,EACP1W,MAAOujB,EAASnc,GAChB6vB,WAAW,UACXt/B,OAAO8gC,EAAAA,EAAAA,IAAiC,IAAlBF,EAAYnxB,IAClC8vB,WAAW,MACXl1B,IAAKy1B,EAAOrwB,SAQ1B,SAASrU,EAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACb2V,QAAS,GAAG5V,EAAM+X,QAAQ,SAGhC,CCzDO,MAAMqtB,EAAsBlxB,IACjC,MAAM,OAAEkB,EAAM,KAAEpT,EAAI,QAAEwT,GAAYtB,EAC5B5U,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,GAAIgW,EACF,OACE,kBAAChK,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAOkW,SACrB,kBAAC7V,EAAAA,KAAIA,CAACG,UAAWR,EAAOoR,KAAM9Q,KAAK,qBAAqBC,KAAK,OAC5D2V,IAMT,GAAIJ,GAAUA,EAAOxT,OAAS,EAC5B,OAAQI,GACN,IAAK,iBACH,OAAO,kBAACsiC,EAAiBA,CAAClvB,OAAQA,EAAQpT,KAAMA,IAClD,IAAK,mBACH,OAAO,kBAACgiC,EAAmBA,CAAC5uB,OAAQA,EAAQpT,KAAMA,IACpD,IAAK,mBACH,OAAO,kBAACmjC,EAAmBA,CAAC/vB,OAAQA,EAAQpT,KAAMA,IAGxD,OAAO,kBAACwJ,MAAAA,CAAI1L,UAAWR,EAAOmM,WAAW,mBAG3C,SAASjM,EAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACb2V,QAAS,GAAG5V,EAAM+X,QAAQ,SAE5BrH,MAAMzQ,EAAAA,EAAAA,KAAI,CACRwf,OAAQ,KAAKzf,EAAM+X,QAAQ,SAAU/X,EAAM+X,QAAQ,OAErDvC,SAASvV,EAAAA,EAAAA,KAAI,CACXS,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,KACnB0H,OAAQ,GAAGzf,EAAM+X,QAAQ,UACzB7X,MAAO,QAGb,CC3CO,MAAMmlC,UAA4B/9B,EAAAA,I,UAiBzC,SAASg+B,EAAQtjC,GACf,OAAQA,GACN,IAAK,mBACH,MAAO,aACT,IAAK,iBACH,MAAO,YAGT,QACE,MAAO,uBAEb,CAEA,SAASxC,EAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACb0Y,OAAQ,aAAa3Y,EAAMo9B,OAASp9B,EAAMK,OAAOsY,OAAO4sB,OAASvlC,EAAMK,OAAOsY,OAAOC,OACrFnY,aAAcT,EAAM+X,QAAQ,IAC5Bc,aAAc7Y,EAAM+X,QAAQ,GAC5B7X,MAAO,SAET0O,OAAO3O,EAAAA,EAAAA,KAAI,CACTsX,MAAOvX,EAAMo9B,OAASp9B,EAAMK,OAAOE,KAAK0L,UAAYjM,EAAMK,OAAOE,KAAKmY,QACtEtY,gBAAiBJ,EAAMo9B,OAASp9B,EAAMK,OAAO2L,WAAWC,UAAYjM,EAAMK,OAAO2L,WAAW0M,QAC5F8sB,oBAAqBxlC,EAAM+X,QAAQ,IACnC0tB,qBAAsBzlC,EAAM+X,QAAQ,IACpCrX,QAAS,OACT0W,eAAgB,SAChBC,WAAY,SACZC,SAAU,SACV1B,QAAS,GAAG5V,EAAM+X,QAAQ,QAAQ/X,EAAM+X,QAAQ,OAElD2tB,WAAWzlC,EAAAA,EAAAA,KAAI,CACbyf,WAAY1f,EAAM+X,QAAQ,KAGhC,C,EApD4B,EAAGpO,YAC3B,MAAM,OAAEyL,EAAM,MAAExG,EAAK,KAAE5M,EAAI,QAAEwT,GAAY7L,EAAMC,WACzCtK,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAOsP,OACrB,kBAACjP,EAAAA,KAAIA,CAACC,KAAM0lC,EAAQtjC,GAAOnC,KAAK,OAChC,kBAAC0Y,OAAAA,CAAKzY,UAAWR,EAAOomC,WAAY92B,IAEtC,kBAACw2B,EAAkBA,CAAChwB,OAAQA,EAAQpT,KAAMA,EAAMwT,QAASA,O,EAVjD9L,e,EADH27B,G,0iBCoBN,MAAMM,UAAuBr+B,EAAAA,GAClC,WAAAM,CAAY7E,G,QACV8E,MAAM,GACJ8O,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BlF,WAAYiU,EAAAA,GACZvT,QAAS,E,EAAC,GAAEC,MAAO,IAAK4L,UAAW,UAAWkI,UAAW,QAASC,MAAO,IAAOxb,EAAM4F,O,EAAK,CAAEi9B,UAAW,G,2VAEvG7iC,IAGL0E,KAAKK,qBAAqB,KACxB,MAAMkG,EAAOlE,EAAAA,GAAWmE,QAAQxG,MAEhCA,KAAKoB,MAAMC,IACTkF,EAAKjF,iBAAkBiF,I,IACjBA,EAA0CA,EAmDnCA,EAjDPA,EAFJ,IAAa,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaK,OAAiB,QAATrH,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaG,UAC9E,IACW,QAATnH,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaK,MACL,IAA5BrH,EAAKA,KAAKoH,OAAOxT,QAA+C,IAA/BoM,EAAKA,KAAKoH,OAAO,GAAGxT,QAajD,GAAIoM,EAAKA,KAAKoH,OAAOxT,OAAS,EAAG,C,IAa3BoM,EAZX,GAAmB,mBAAfjL,EAAMf,MAA6Be,EAAM8iC,oBAC3Cp+B,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAI2hC,EAAoB,CACtBjwB,OAAQpH,EAAKA,KAAKoH,OAClBxG,MAAO7L,EAAM6L,MACb5M,KAAMe,EAAMf,iBAKf,IAAa,QAATgM,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaK,KAAM,C,IACtBrH,EAAAA,EAA3B,IAAInN,GAAWm/B,EAAAA,EAAAA,IAA6B,QAAjBhyB,EAAS,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWoH,cAAXpH,IAAAA,EAAAA,EAAqB,IAChD,GAAInN,aAAAA,EAAAA,EAAUe,OAAQ,CACpB,MAAM,YAAEq+B,IAAgBE,EAAAA,EAAAA,IAAuBt/B,G,IAOqBkC,EALpE0E,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAIiiC,EAAe,CACjBh9B,MAAO,CACLA,MAAO,oCAAoCs3B,KAA2B,QAAZl9B,EAAAA,EAAMuG,cAANvG,IAAAA,EAAAA,EAAgB,OAE5E6L,MAAO7L,EAAM6L,MACb5M,KAAMe,EAAMf,KACZ6jC,qBAAqB,QAK/B,CACF,CACF,OA7CEp+B,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAI2hC,EAAoB,CACtB7vB,QAASswB,GAAiB/iC,EAAM6L,MAAMwQ,eACtCxQ,MAAO7L,EAAM6L,MACb5M,KAAMe,EAAMf,iBAwCJ,QAATgM,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAa1H,MAC3C7F,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBE,SAAU,CACR,IAAI2hC,EAAoB,CACtB7vB,QAASuwB,GAAgB/3B,GACzBY,MAAO7L,EAAM6L,MACb5M,KAAMe,EAAMf,YAMpByF,KAAKpG,SAAS,CACZL,MAAO,IAAIwC,EAAAA,GAAgB,CACzBC,UAAW,SACXyxB,UAAWC,EAAAA,GACXh1B,OAAQg1B,EAAAA,GACRzxB,SAAU,CACR,IAAI2H,EAAAA,EAAkB,CACpBE,UAAW,IAAM2K,eASnC,EAkBF,SAAS1W,IACP,MAAO,CACLiM,WAAWxL,EAAAA,EAAAA,KAAI,CACbuT,SAAU,QACVtT,MAAO,2BAGb,CAvBE,EAjGWylC,EAiGGj8B,YAAY,EAAGC,YAC3B,MAAM,MAAE3I,GAAU2I,EAAMC,WAClBtK,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,GAAKwB,EAIL,OACE,kBAACwK,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACzK,EAAM0I,UAAS,CAACC,MAAO3I,OAezB,MAAMkV,EAAoB,KAC/B,MAAM5W,GAASC,EAAAA,EAAAA,YAAWyY,GAE1B,OACE,kBAACxM,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAOsP,OACrB,kBAACqJ,EAAAA,EAAQA,CAACC,MAAO,EAAGhY,MAAO,OAE7B,kBAACsL,MAAAA,CAAI1L,UAAWR,EAAO0mC,iBACpB,IAAI7tB,MAAM,KAAKpL,IAAI,CAACqL,EAAG5I,IACtB,kBAAChE,MAAAA,CAAI1L,UAAWR,EAAO+Y,IAAK/H,IAAKd,GAC/B,kBAAChE,MAAAA,CAAI1L,UAAWR,EAAO2mC,SACrB,kBAAChuB,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAO4mC,UACrB,kBAACjuB,EAAAA,EAAQA,CAACC,MAAO,UAS/B,SAASF,EAAkBhY,GACzB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACb0Y,OAAQ,aAAa3Y,EAAMo9B,OAASp9B,EAAMK,OAAOsY,OAAO4sB,OAASvlC,EAAMK,OAAOsY,OAAOC,OACrFnY,aAAcT,EAAM+X,QAAQ,IAC5Bc,aAAc7Y,EAAM+X,QAAQ,GAC5B7X,MAAO,SAET0O,OAAO3O,EAAAA,EAAAA,KAAI,CACTsX,MAAOvX,EAAMK,OAAOE,KAAK0L,UACzB7L,gBAAiBJ,EAAMK,OAAO2L,WAAWC,UACzCqL,SAAU,SACV1B,QAAS,GAAG5V,EAAM+X,QAAQ,QAAQ/X,EAAM+X,QAAQ,KAChD2hB,UAAW,WAEbsM,iBAAiB/lC,EAAAA,EAAAA,KAAI,CACnB2V,QAAS,QAAQ5V,EAAM+X,QAAQ,OAEjCM,KAAKpY,EAAAA,EAAAA,KAAI,CACPS,QAAS,OACT0W,eAAgB,kBAElB6uB,SAAShmC,EAAAA,EAAAA,KAAI,CACXwf,OAAQ,QACRvf,MAAO,UAETgmC,UAAUjmC,EAAAA,EAAAA,KAAI,CACZC,MAAO,SAGb,CCjNO,MAAMimC,EAAkB,IAC7B,kBAAC9I,MAAAA,CAAI+I,MAAM,6BAA6BlmC,MAAM,KAAKC,OAAO,KAAKkmC,QAAQ,YAAY7lC,KAAK,QACtF,kBAAC8lC,OAAAA,CACCzW,EAAE,u9CACFrvB,KAAK,UACL8tB,YAAY,UAKLiY,EAAiB,IAC5B,kBAAClJ,MAAAA,CAAI+I,MAAM,6BAA6BlmC,MAAM,KAAKC,OAAO,KAAKkmC,QAAQ,YAAY7lC,KAAK,QACtF,kBAAC8lC,OAAAA,CACCzW,EAAE,u9CACFrvB,KAAK,UACL8tB,YAAY,U,gcCTlB,MAAMkY,EAAiBrC,IAGrBA,EAAOsC,OAAOC,EAAAA,IACdvC,EAAOsC,OAAO,OAAOE,EAAAA,MACrBxC,EAAOsC,OAAO,OAAOG,EAAAA,MACdzC,GAGI0C,EAAsB,KACjC,MAAMC,GAAUC,EAAAA,EAAAA,wBAEhB,MAAO,CACLC,aAAc,IAAMA,EAAaF,GACjCG,eAAiBC,GAAuBD,GAAeH,EAASI,GAChEC,eAAiBD,GAAuBC,GAAeL,EAASI,GAChEE,eAAgB,IAAMA,EAAeN,KAsB5BO,EAAqBH,IAChC,IAAKA,IAAaA,EAAS/C,OACzB,OAAOC,EAAAA,GAGT,MAAMD,EAAS,IAAInO,gBAAgBkR,EAAS/C,QACtCmD,EAAc75B,OAAO85B,YAAYpD,EAAOha,WAExCvZ,EAAUuzB,EAAOqD,OAAO,OAAOrf,EAAAA,MAE/B1Z,EAAM7D,EAAAA,QAAQC,UAAUu5B,EAAAA,I,oUAAoB,IAC7CkD,G,WAAAA,CACH,CAAC,OAAOnf,EAAAA,MAAgBvX,I,sVAG1B,OAAOnC,GAGHg5B,EAAe,CAAOX,EAAwBY,IAAAA,EAAAA,YAClD,UACQZ,EAAQa,QAAQC,EAAAA,GAAkBz9B,KAAKC,UAAUs9B,GACzD,CAAE,MAAOl+B,GACPo7B,QAAQC,MAAM,uCAAwCr7B,EACxD,CACF,EANoDk+B,GAQvCV,EAAsBF,GAAAA,EAAAA,YACjC,IACE,MAAM1iC,QAAc0iC,EAAQe,QAAQD,EAAAA,IACpC,OAAIxjC,EACK+F,KAAK+R,MAAM9X,GAEb,EACT,CAAE,MAAOoF,GAEP,OADAo7B,QAAQC,MAAM,wCAAyCr7B,GAChD,EACT,CACF,EAXmCs9B,GAatBM,EAAwBN,GAAAA,EAAAA,YACnC,MAAMI,EA3CC,CAAE/C,OADMqC,EAAc,IAAIxQ,gBAAgBtnB,OAAOC,SAASsnB,SACzCjjB,YA8CxB,aAFqBm0B,GAAeL,EAASI,WAGrCD,GAAeH,EAASI,IACvB,UAEDY,GAAYhB,EAASI,IACpB,EAEX,EAXqCJ,GAa/BgB,GAAc,CAAOhB,EAAwBI,IAAAA,EAAAA,YACjD,MAAMQ,QAAkBV,EAAaF,GACrCY,EAAUj1B,KAAKy0B,SACTO,EAAaX,EAASY,EAC9B,EAJmDR,GAMtCD,GAAiB,CAAOH,EAAwBI,IAAAA,EAAAA,YAC3D,MACMa,SADwBf,EAAaF,IACDx9B,OAAQ0+B,IAAoBC,GAAkBf,EAAUc,UAC5FP,EAAaX,EAASiB,EAC9B,EAJ6Db,GAMhDC,GAAiB,CAAOL,EAAwBI,IAAAA,EAAAA,YAE3D,aADwBF,EAAaF,IACpB9kB,KAAMnP,GAAMo1B,GAAkBf,EAAUr0B,GAC3D,EAH6Dq0B,GAKhDe,GAAoB,CAACf,EAAoBc,KACpD,MAAME,EAAiB1B,EAAc,IAAIxQ,gBAAgBkR,EAAS/C,SAC5DgE,EAAuB3B,EAAc,IAAIxQ,gBAAgBgS,EAAe7D,SAExEiE,EAAY,OAAOjgB,EAAAA,KACnBkgB,EAAelwB,MAAMhW,KAAK+lC,EAAe16B,QAAQlE,OAAOg/B,GAAKA,IAAMF,GACnEG,EAAapwB,MAAMhW,KAAKgmC,EAAqB36B,QAAQlE,OAAOg/B,GAAKA,IAAMF,GAG7E,GAAIC,EAAazmC,SAAW2mC,EAAW3mC,OACrC,OAAO,EAIT,MAAM4mC,EAAeH,EAAaI,MAAMn4B,GACtC63B,EAAqBxc,IAAIrb,IAAQ43B,EAAehe,IAAI5Z,KAAS63B,EAAqBje,IAAI5Z,IAExF,IAAKk4B,EACH,OAAO,EAIT,MAAME,EAAkBR,EAAeV,OAAOY,GACxCO,EAAgBR,EAAqBX,OAAOY,GAClD,OAAIM,EAAgB9mC,SAAW+mC,EAAc/mC,QAMtC8mC,EAAgBD,MAAMn/B,GAAUq/B,EAAcp/B,SAASD,K,eCxIzD,MAAMs/B,GAAe,EAAG1B,eAC7B,IAAI,WAAElkC,EAAU,cAAE0tB,EAAa,OAAEntB,EAAM,QAAEqN,GDkBV,CAACs2B,IAChC,IAAKA,IAAaA,EAAS/C,OACzB,MAAO,CAAEnhC,WAAY,GAAI0tB,cAAe,GAAI9f,QAAS,GAAIrN,OAAQ,IAGnE,MAAM4gC,EAAS,IAAInO,gBAAgBkR,EAAS/C,Q,IACzBA,EACGA,EAEPA,EACf,MAAO,CAAEnhC,WAJgC6lC,QAAtB1E,EAAAA,EAAOja,IAAI2e,EAAAA,WAAX1E,IAAAA,EAAAA,EAA2B,GAIzBzT,cAH0BoY,QAAzB3E,EAAAA,EAAOja,IAAI4e,EAAAA,WAAX3E,IAAAA,EAAAA,EAA8B,GAGhBvzB,QAFpBuzB,EAAOqD,OAAO,OAAOrf,EAAAA,MAAehV,KAAK41B,EAAAA,IAEZxlC,OADA,QAA9B4gC,EAAAA,EAAOja,IAAI,OAAO9B,EAAAA,aAAlB+b,IAAAA,EAAAA,EAAmC,KC3BG6E,CAAkB9B,GACvE,MAAM5nC,GAASC,EAAAA,EAAAA,YAAWC,IA2B1B,OAJAoR,EAPuC,EAACA,EAAiB8f,KACvD,MAAMuY,EAfuB,CAACvY,IAC9B,MAAMwY,GAAa1F,EAAAA,GAAAA,IAAgB9S,GACnC,IAAKwY,IAAeA,EAAW5/B,OAC7B,MAAO,GAET,MAAMA,EAAS4/B,EAAW5/B,OAE1B,OAAIA,EAAOgH,KAAOhH,EAAO0H,eAA6BlP,IAAjBwH,EAAOlF,MACnC,GAAGkF,EAAOgH,OAAOhH,EAAO0H,YAAY1H,EAAOlF,QAE7C,IAKqB+kC,CAAuBzY,GACnD,IAAI0Y,EAAex4B,EAAQqC,MAAM81B,EAAAA,IAEjC,OADAK,EAAeA,EAAa9/B,OAAO8F,GAAKA,IAAM65B,GACvCG,EAAaj2B,KAAK41B,EAAAA,KAGjBM,CAA+Bz4B,EAAS8f,GAClD9f,EAAUA,EAAQ3L,QAAQ,SAAU,OACpC2L,EAAUA,EAAQ3L,QAAQoc,EAAAA,GAAe,IAAIpc,QAAQmc,EAAAA,GAAW,IAAInc,QAAQqkC,EAAAA,GAAY,IAGtF,kBAAC99B,MAAAA,CAAIoD,MAAOgC,GACV,kBAACpF,MAAAA,KACC,kBAACqH,IAAAA,KAAG02B,GAAoBhmC,IAAY,OAAI,kBAACsP,IAAAA,KAAG6d,EAAczrB,QAAQ,IAAK,MAAS,KAAGjC,EAAW,KAEhG,kBAACwI,MAAAA,CAAI1L,UAAWR,EAAOsR,SACpBA,KAMT,SAASpR,KACP,MAAO,CACLoR,SAAS3Q,EAAAA,EAAAA,KAAI,CACX0X,aAAc,WACdD,SAAU,SACV8xB,gBAAiB,EACjB9oC,QAAS,cACT+oC,gBAAiB,aAGvB,C,8TCjDO,MAAMC,GAAY,KACvB,MAAMpqC,GAASC,EAAAA,EAAAA,YAAWC,KACpB,aAAEwnC,EAAY,eAAEC,GAAmBJ,KAClCa,EAAWD,IAAgB79B,EAAAA,EAAAA,UAAqB,KAChD6U,EAAWkrB,IAAgB//B,EAAAA,EAAAA,WAAkB,IAC7CggC,EAAYC,IAAiBjgC,EAAAA,EAAAA,WAAkB,GAkCtD,OAhCAmY,EAAAA,EAAAA,WAAU,KACe,eACrB4nB,GAAa,GACb,IACE,MAAMG,QAAwB9C,IAC9BS,EAAaqC,EACf,CAAE,MAAOjF,GACPD,QAAQC,MAAM,2BAA4BA,GAC1C4C,EAAa,GACf,CAAE,QACAkC,GAAa,EACf,CACF,EAXuB,IActB,IAiBClrB,EAEA,kBAACjT,MAAAA,KACC,kBAACA,MAAAA,CAAI1L,UAAWR,EAAO0X,QACrB,kBAAC+yB,KAAAA,KAAG,sBAEN,kBAACv+B,MAAAA,CAAI1L,UAAWR,EAAO8c,SACrB,kBAAC4tB,EAAAA,mBAAkBA,CAACzpC,KAAK,2BAO/B,kBAACiL,MAAAA,KACC,kBAACA,MAAAA,CAAI1L,UAAWR,EAAO0X,QACrB,kBAAC+yB,KAAAA,KAAG,sBAEgB,IAArBrC,EAAU9lC,OACT,kBAACqoC,IAAAA,CAAEnqC,UAAWR,EAAO4qC,aAAa,qDAElC,kBAAC1+B,MAAAA,CAAI1L,UAAWR,EAAOooC,WACpBA,EAAU36B,IAAI,CAACm6B,EAAoB13B,IAClC,kBAAChE,MAAAA,CACC1L,UAAWR,EAAO4nC,SAClB52B,IAAKd,EACLtH,QAAS,IFwEK,CAACg/B,KAC3BhkC,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBgC,KAAM9B,EAAAA,GAAoB8B,KAAKsB,wBACtE,MAAMgI,EAAM44B,EAAkBH,GAC9BrD,EAAAA,gBAAgBpxB,KAAKhE,IE3EM07B,CAAajD,IAE5B,kBAAC17B,MAAAA,CAAI1L,UAAWR,EAAO8qC,cACrB,kBAACxB,GAAYA,CAAC1B,SAAUA,KAE1B,kBAAC17B,MAAAA,CAAI1L,UAAWR,EAAO+qC,QACrB,kBAAC75B,EAAAA,OAAMA,CACLC,QAAQ,YACRjQ,KAAK,OACLkQ,KAAK,YACL8qB,SAAUoO,EACV1hC,QAAUsB,GApDI,EAAO09B,EAAoB3iB,IAAAA,GAAAA,YACvDA,EAAM+lB,kBACNT,GAAc,GAEd,UACQ5C,EAAeC,GACrB,MAAMqD,QAAyBvD,IAC/BS,EAAa8C,EACf,CAAE,MAAO1F,GACPD,QAAQC,MAAM,2BAA4BA,EAC5C,CAAE,QACAgF,GAAc,EAChB,CACF,EAbyDtlB,GAoDzBimB,CAAsBtD,EAAU19B,WAWlE,SAAShK,GAAUQ,GACjB,MAAO,CACLgX,QAAQ/W,EAAAA,EAAAA,KAAI,CACVy5B,UAAW,SACX,GAAM,CACJja,OAAQ,KAGZioB,WAAWznC,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACT+pC,SAAU,OACVtzB,IAAKnX,EAAM+X,QAAQ,GACnB0H,OAAQ,GAAGzf,EAAM+X,QAAQ,QAAQ/X,EAAM+X,QAAQ,OAC/CX,eAAgB,WAElB8vB,UAAUjnC,EAAAA,EAAAA,KAAI,CACZS,QAAS,OACT6e,cAAe,SACfnI,eAAgB,gBAChBI,OAAQ,UACRtX,MAAO,QACPyY,OAAQ,aAAa3Y,EAAMK,OAAOsY,OAAO4sB,SACzC9kC,aAAcT,EAAMmM,MAAMC,OAAOC,QAEjC,UAAW,CACTjM,gBAAiBJ,EAAMo9B,OAASp9B,EAAMK,OAAO2L,WAAWC,UAAYjM,EAAMK,OAAO2L,WAAW0M,WAGhG0xB,cAAcnqC,EAAAA,EAAAA,KAAI,CAChB2V,QAAS,GAAG5V,EAAM+X,QAAQ,QAAQ/X,EAAM+X,QAAQ,UAAU/X,EAAM+X,QAAQ,OACxEL,SAAU,WAEZ9G,SAAS3Q,EAAAA,EAAAA,KAAI,CACX0X,aAAc,WACdD,SAAU,SACV8xB,gBAAiB,EACjB9oC,QAAS,cACT+oC,gBAAiB,aAEnBY,QAAQpqC,EAAAA,EAAAA,KAAI,CACVS,QAAS,OACT0W,eAAgB,aAElB8yB,aAAajqC,EAAAA,EAAAA,KAAI,CACfwf,OAAQ,GAAGzf,EAAM+X,QAAQ,QAAQ/X,EAAM+X,QAAQ,OAC/C2hB,UAAW,WAEbtd,SAASnc,EAAAA,EAAAA,KAAI,CACXS,QAAS,OACT0W,eAAgB,SAChBqI,OAAQ,GAAGzf,EAAM+X,QAAQ,SAG/B,CCnIO,MAAM2yB,WAAoBpjC,EAAAA,IAgFjC,SAAS9H,GAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,GACnBwH,cAAe,SACfE,OAAQ,OAAOzf,EAAM+X,QAAQ,OAC7BX,eAAgB,WAElBJ,QAAQ/W,EAAAA,EAAAA,KAAI,CACVS,QAAS,OACT2W,WAAY,SACZjX,gBAAiBJ,EAAMo9B,OAASp9B,EAAMK,OAAO2L,WAAWC,UAAYjM,EAAMK,OAAO2L,WAAW0M,QAC5FjY,aAAcT,EAAM+X,QAAQ,IAC5B0yB,SAAU,OACVrzB,eAAgB,SAChBxB,QAAS5V,EAAM+X,QAAQ,GACvBZ,IAAKnX,EAAM+X,QAAQ,KAErB4yB,sBAAsB1qC,EAAAA,EAAAA,KAAI,CACxBS,QAAS,OACT2W,WAAY,WAEdzI,OAAO3O,EAAAA,EAAAA,KAAI,CACTwf,OAAQ,SAASzf,EAAM+X,QAAQ,OAGjC6yB,eAAe3qC,EAAAA,EAAAA,KAAI,CACjBoX,WAAY,SACZD,eAAgB,aAChB1W,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,KAErB8yB,mBAAmB5qC,EAAAA,EAAAA,KAAI,CACrB2X,eAAgB,YAChB,UAAW,CACTA,eAAgB,eAIpBkzB,WAAW7qC,EAAAA,EAAAA,KAAI,CACby5B,UAAW,SACX,GAAM,CACJja,OAAQ,QAAQzf,EAAM+X,QAAQ,UAIlCtL,OAAOxM,EAAAA,EAAAA,KAAI,CACTqX,SAAU,SAEZyzB,sBAAsB9qC,EAAAA,EAAAA,KAAI,CACxBoX,WAAY,SACZF,IAAKnX,EAAM+X,QAAQ,GACnBrX,QAAS,OACT0W,eAAgB,gBAChBlX,MAAO,SAET8qC,WAAW/qC,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,KAErByQ,UAAUvoB,EAAAA,EAAAA,KAAI,CACZS,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,KAGzB,C,0HChKO,SAAekzB,GAAmBn7B,G,qBACvC,MAAMo7B,EAAQphC,EAAAA,GAAW6c,YAAY7W,EAAU8mB,EAAAA,IACzCuU,QAAoBxU,EAAAA,EAAAA,oBAAmBzM,IAAIghB,GACjD,KAAMC,aAAuBC,EAAAA,uBAE3B,MADAxG,QAAQC,MAAM,IAAIv3B,MAAM,4CAClB,IAAIA,MAAM,2CAGlB,MAAMzD,EAAashC,EACnB,GAAIthC,GAAcA,EAAWitB,WAAY,CACvC,MAAME,QAAgBntB,EAAWitB,aAEjC,OAAI3e,MAAMkzB,QAAQrU,GAET,CAAE/xB,SAAS,EAAMwK,QAWHjC,EAZWwpB,EAoB7B,IAPoBxpB,EAAKlE,OAAQg/B,I,IAAMA,E,OAAM,QAANA,EAAAA,EAAE/nC,YAAF+nC,IAAAA,OAAAA,EAAAA,EAAQ/+B,SAAS8X,EAAAA,SACxC7T,EAAKlE,OAAQg/B,I,IAAMA,E,OAAM,QAANA,EAAAA,EAAE/nC,YAAF+nC,IAAAA,OAAAA,EAAAA,EAAQ/+B,SAAS6X,EAAAA,SACnC5T,EAAKlE,OAAQg/B,I,IAC3BA,EAAoCA,EACtCA,EAAiCA,EADvC,SAAc,QAANA,EAAAA,EAAE/nC,YAAF+nC,IAAAA,OAAAA,EAAAA,EAAQ/+B,SAAS8X,EAAAA,OAAyB,QAANinB,EAAAA,EAAE/nC,YAAF+nC,IAAAA,OAAAA,EAAAA,EAAQ/+B,SAAS6X,EAAAA,OACjD,QAANknB,EAAAA,EAAE/nC,YAAF+nC,IAAAA,OAAAA,EAAAA,EAAQ/+B,SAAS+/B,EAAAA,OAAsB,QAANhB,EAAAA,EAAE/nC,YAAF+nC,IAAAA,OAAAA,EAAAA,EAAQ/+B,SAAS+hC,EAAAA,OACyB,IAA5EzpB,EAAAA,GAAkB0H,OAAOgiB,EAAAA,IAA6Bh5B,QAAQ+1B,EAAE/nC,aAfnEqkC,QAAQC,MAAM,IAAIv3B,MAAM,yCACjB,CAAEmC,OAAQ,IAErB,CAMK,IAAoBjC,EAJvB,OADAo3B,QAAQC,MAAM,IAAIv3B,MAAM,uDACjB,CAAEmC,OAAQ,GAErB,E,qSDRE,CADWi7B,GACGhhC,YAAY,EAAGC,YAC3B,MAAMxE,EAAOqmC,GAAa7hC,GACpB8hC,GAAWC,EAAAA,EAAAA,gBACX,SAAEljB,GAAarjB,EAAKyE,WACpBtK,GAASC,EAAAA,EAAAA,YAAWC,IACpBQ,GAAQsL,EAAAA,EAAAA,aAERqgC,EAAarV,GAAsBnxB,GACnCymC,EAAiBC,GAAsB1mC,GAE7C,OACE,kBAACqG,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAO0X,QACrB,kBAACxL,MAAAA,CAAI1L,UAAWR,EAAOqrC,sBACpB3qC,EAAMo9B,OAAS,kBAACmJ,EAAcA,MAAM,kBAACJ,EAAeA,MACrD,kBAAC2F,KAAAA,CAAGhsC,UAAWR,EAAOsP,OAAO,mCAE/B,kBAACpD,MAAAA,KACC,kBAACy+B,IAAAA,KAAE,oEACH,kBAACz+B,MAAAA,CAAI1L,UAAWR,EAAOsrC,eACrB,kBAACp6B,EAAAA,OAAMA,CAACC,QAAQ,UAAUvI,QAAS,MAC/BhF,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBgC,KAAM9B,EAAAA,GAAoB8B,KAAKmB,wBACtEmlC,EAASrH,EAAAA,MACR,cAEH,kBAACzkC,EAAAA,KAAIA,CAACC,KAAK,cAAcC,KAAK,QAEhC,kBAACsf,EAAAA,WAAUA,CACTzO,KAAK,oBACLlQ,KAAK,OACLX,KAAM,KACN+U,OAAQ,SACR5M,KACE,sFAEFlI,UAAWR,EAAOurC,kBAClB3iC,QAAS,KAAMhF,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBgC,KAAM9B,EAAAA,GAAoB8B,KAAKoB,6BACtF,yBAOP,kBAACmjC,GAASA,MAEV,kBAACl+B,MAAAA,CAAI1L,UAAWR,EAAOwrC,WACrB,kBAACf,KAAAA,KAAG,0CAGN,kBAAC9qB,EAAAA,MAAKA,CAAC9H,IAAK,GACV,kBAAC3L,MAAAA,CAAI1L,UAAWR,EAAOyrC,sBACrB,kBAACv/B,MAAAA,CAAI1L,UAAWR,EAAO0rC,WACpBW,GACC,kBAAC1sB,EAAAA,MAAKA,CAAC9H,IAAK,EAAGE,WAAY,UACzB,kBAAC7L,MAAAA,CAAI1L,UAAWR,EAAOmN,OAAO,eAC9B,kBAACk/B,EAAWjiC,UAAS,CAACC,MAAOgiC,KAGhCC,GACC,kBAAC3sB,EAAAA,MAAKA,CAAC9H,IAAK,EAAGE,WAAY,UACzB,kBAAC7L,MAAAA,CAAI1L,UAAWR,EAAOmN,OAAO,UAC9B,kBAACm/B,EAAeliC,UAAS,CAACC,MAAOiiC,MAKvC,kBAACpgC,MAAAA,CAAI1L,UAAWR,EAAOkpB,UACpBA,aAAAA,EAAAA,EAAUzb,IAAKg/B,GACd,kBAACA,EAAQriC,UAAS,CAAC4G,IAAKy7B,EAAQhpC,MAAMuN,IAAK3G,MAAOoiC,W,yHE9C3D,MAAMC,WAAa1kC,EAAAA,GAYhBuG,WAAAA,GACN,MAAM2iB,EAAiB1mB,EAAAA,GAAWC,aAAatC,MACzCmkC,EAAiBC,GAAsBpkC,MAC7CmkC,EAAevqC,SAAS,CACtB4pC,mBAAoBA,KAGtB3U,GAAsB7uB,MAAMsB,iBAAkBiQ,IACxCA,EAAS5U,OACX6nC,aAAatE,QAAQuE,EAAAA,GAAmBlzB,EAAS5U,MAAM4O,cAI3D64B,GAAsBpkC,MAAMsB,iBAAiB,CAACiQ,EAAUC,KACtD,GAAID,EAASpI,UAAYqI,EAAUrI,QAAS,CAC1CnJ,KAAK0kC,YAAY3b,EAAgBxX,EAASpI,SAG1Cq7B,aAAatE,QAAQyE,EAAAA,GAAyBjiC,KAAKC,UAAU4O,EAASpI,UAEtE,MAAMue,EAAanW,EAASpI,QAAQtH,OAAQ8F,IAAO6J,EAAUrI,QAAQvH,KAAMgjC,GAAOA,EAAG/7B,MAAQlB,EAAEkB,MAC3F6e,EAAWvtB,OAAS,IACtBsB,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBgC,KAAM9B,EAAAA,GAAoB8B,KAAKqB,eAAgB,CACpF8J,IAAK6e,EAAW,GAAG7e,KAGzB,IAGFkgB,EAAeznB,iBAAiB,CAACiQ,EAAUC,KACrCD,EAAS5U,MAAMjC,OAAS8W,EAAU7U,MAAMjC,MAAQ6W,EAAS5U,MAAM7B,KAAO0W,EAAU7U,MAAM7B,IACxFkF,KAAK0kC,YAAY3b,EAAgBob,EAAe7oC,MAAM6N,WAG1DnJ,KAAK0kC,YAAY3b,EAAgBob,EAAe7oC,MAAM6N,QACxD,CAEAu7B,WAAAA,CAAY3b,EAAoC5f,GAC9C,MAAMzO,EAAOquB,EAAeztB,MAAMqB,MAAMjC,KAAKsuB,OACvCluB,EAAKiuB,EAAeztB,MAAMqB,MAAM7B,GAAGkuB,OAEnCc,EAAY,IADN3uB,EAAAA,EAAAA,UAASL,EAAKJ,EAAM,KACPqvB,eACnB8a,EDxDH,SAAmC17B,GACxC,MAAM27B,EAAO37B,EACVtH,OAAQ8F,GAAMA,EAAEkB,KAAOlB,EAAE4B,UAAY5B,EAAEhL,OACvC2I,IAAKzD,GAKW,CAACA,IACpB,IAAKA,EACH,MAAO,GAGT,IAAI4oB,EAAM5oB,EAAOlF,MACjB,OAAI8tB,SAA6C,KAARA,EAChC,IAGJsa,GAASjJ,KAAKrR,IAAS,CAAC,QAAQ3oB,SAASD,EAAOgH,MAChC,iBAAR4hB,GAAqBA,EAAIxf,WAAW,MAASwf,EAAIua,SAAS,OACnEva,EAAM,IAAIA,MAIP,GAAG5oB,EAAOgH,MAAMhH,EAAO0H,WAAWkhB,MArBtBwa,CAAapjC,IAC7B6J,KAAK41B,EAAAA,IACR,OAAOwD,EAAK3qC,OAAS,MAAM2qC,IAAS,EACtC,CCkD4BI,CAA0B/7B,GAElDnJ,KAAKpG,SAAS,CACZuC,KAAM,IAAIkiB,EAAAA,GAAmB,CAC3BpiB,SAAU,CACR,IAAIoiB,EAAAA,GAAmB,CACrBG,SAAU,cACVgd,UAAW,EACXD,OAAQ,EACRt/B,SAAU,CACR,IAAI0jB,EAAAA,GAAiB,CACnBxjB,KAAM,IAAI+hC,EAAe,CACvBh9B,MAAO,CACLA,MAAO,0CAA0C2jC,oDACjDtiC,KAAMunB,GAER3iB,MAAO,mBACP5M,KAAM,uBAGV,IAAIolB,EAAAA,GAAiB,CACnBxjB,KAAM,IAAI+hC,EAAe,CACvBh9B,MAAO,CACLA,MAAO,wBAAwB2jC,oEAC/BtiC,KAAMunB,GAER3iB,MAAO,gBACP5M,KAAM,uBAGV,IAAIolB,EAAAA,GAAiB,CACnBxjB,KAAM,IAAI+hC,EAAe,CACvBh9B,MAAO,CACLA,MAAO,sBAAsB2jC,sCAE/B19B,MAAO,cACP5M,KAAM,iBACNsH,OAAQgjC,aAQxB,CAlGA,YAAmBvpC,G,IAEHA,EACAA,EACFA,EA6GQ6pC,EAAuCC,EAhH3DhlC,M,mUAAM,EACJ2vB,WAA4B,QAAhBz0B,EAAAA,EAAMy0B,kBAANz0B,IAAAA,EAAAA,EAAoB,IAAIw1B,EAAAA,GAAe,CAAC,GACpDuU,WAA4B,QAAhB/pC,EAAAA,EAAM+pC,kBAAN/pC,IAAAA,EAAAA,GA8GM6pC,EA9G6B7pC,EAAM6pC,eA8GIC,EA9GY9pC,EAAM8pC,UA+GxE,IAAIE,EAAAA,GAAiB,CAC1B/B,UAAW,CACT,IAAIgC,EAAAA,GAAmB,CACrBptC,KAAMqtC,EAAAA,GACNxgC,MAAO,cACPrI,MAAOyoC,EACPK,SAAU,UAEZ,IAAIC,EAAAA,GAAqB,CACvBvtC,KAAMwtC,EAAAA,GACNvjC,WAAYiU,EAAAA,GACZmF,OAAQ,WACRrS,QAASg8B,EACTS,kBAAkB,QA3HpB7kB,SAAwB,QAAdzlB,EAAAA,EAAMylB,gBAANzlB,IAAAA,EAAAA,EAAkB,CAAC,IAAIuqC,EAAAA,GAAgB,CAAC,GAAI,IAAIC,EAAAA,GAAmB,CAAC,KAC3ExqC,IAGL0E,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EA4HF,SAASjI,GAAUQ,GACjB,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbwf,OAAQ,GAAGzf,EAAM+X,QAAQ,UACzB7X,MAAO,MAEP,4BAA6B,CAC3BA,MAAO,SAIf,CA5CE,GArGW8rC,GAqGJtiC,YAAY,EAAGC,YACpB,MAAM,KAAE/F,GAAS+F,EAAMC,WACjBtK,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACi/B,GAAYhhC,UAAS,CAACC,MAAOA,IAC7B/F,GAAQ,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,O,eCrHjC,SAAS0E,GAAyBqB,GACvC,OAAOG,EAAAA,GAAW0jC,YAAY7jC,EAAO8jC,EAAAA,EACvC,CAEO,SAASjC,GAAa7hC,GAC3B,OAAOG,EAAAA,GAAW0jC,YAAY7jC,EAAOqiC,GACvC,CAEO,SAASjrC,GAAuB4I,GACrC,OAAOG,EAAAA,GAAW0jC,YAAY7jC,EAAOmsB,EAAAA,GACvC,CAEO,SAASlC,GAAmBjqB,GACjC,MAAM+jC,EAAuB3sC,GAAuB4I,GACpD,OAAO+jC,aAAAA,EAAAA,EAAsB3qC,MAAM4wB,eACrC,CAEO,SAASga,GAAqBd,EAAoBD,GACvD,OAAO,IAAIa,EAAAA,EAAiB,CAC1BZ,YACAD,eAAgBA,QAAAA,EAAkB,GAClCpV,WAAY,IAAIe,EAAAA,GAAe,CAAEp2B,KAAM,UAAWI,GAAI,SAE1D,CAUO,SAASwjC,GAAgB/3B,G,IACvBA,EAAAA,EAAAA,EAAP,OAAiC,QAA1BA,EAAAA,SAAU,QAAVA,EAAAA,EAAMA,YAANA,IAAAA,GAAiB,QAAjBA,EAAAA,EAAY62B,aAAZ72B,IAAAA,OAAAA,EAAAA,EAAmBwH,eAAnBxH,IAAAA,EAAAA,EAA8B,iCACvC,CAEO,SAAS83B,GAAiBtkC,GAC/B,MAAO,sEAAsEA,IAC/E,CAEO,SAASkQ,GAAqBR,GAEnC,OAG8BzB,EAJfm+B,EAAAA,GAAW1W,YAAYhmB,GAK/BtG,EAAAA,QAAQC,UAAUu5B,EAAAA,GAAoB30B,GADxC,IAAyBA,CAFhC,CAMO,SAASjH,GAAc0I,GAC5B,OAAOpH,EAAAA,GAAW6c,YAAYzV,EAAa0lB,EAAAA,GAC7C,CAMO,SAASnN,GAAuB1S,GACrC,OAAOA,EAAWhK,IAAKoU,IAAe,CAAE1U,MAAO0U,EAAW/c,MAAO+c,IACnE,CAiBO,SAAShR,GAAclB,EAAkBiB,G,IAC/BjB,EAAf,MAAMe,EAA6C,QAApCf,EAAAA,EAAME,OAAO9F,KAAM+F,GAAiB,WAAXA,EAAEpN,aAA3BiN,IAAAA,OAAAA,EAAAA,EAA+Ce,OAE9D,IAAKA,EACH,MAAO,YAGT,MAAMxC,EAAOC,OAAOD,KAAKwC,GAAQ1G,OAAQg/B,GAAY,MAANA,GAC/C,OAAoB,IAAhB96B,EAAK5L,OACA,YAGFoO,EAAOE,GAAa1C,EAAK,IAAIvI,QAAQ,KAAM,GACpD,CAEO,SAAS0iB,GAAmB/mB,GACjC,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAeC,EAAAA,EAAaltC,GACxD,KAAMkP,aAAoBmrB,EAAAA,IACxB,MAAM,IAAI3tB,MAAM,+BAElB,OAAOwC,CACT,CAEO,SAAS0G,GAA2B5V,GACzC,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAeE,EAAAA,GAAuBntC,GAClE,KAAMkP,aAAoBmrB,EAAAA,IACxB,MAAM,IAAI3tB,MAAM,wCAElB,OAAOwC,CACT,CAEO,SAASswB,GAA4Bx/B,GAC1C,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAelH,EAAAA,GAAuB/lC,GAClE,KAAMkP,aAAoBmrB,EAAAA,IACxB,MAAM,IAAI3tB,MAAM,wCAElB,OAAOwC,CACT,CAEO,SAASuwB,GAAmCz/B,GACjD,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAejH,EAAAA,GAA+BhmC,GAC1E,KAAMkP,aAAoBmrB,EAAAA,IACxB,MAAM,IAAI3tB,MAAM,gDAElB,OAAOwC,CACT,CAEO,SAASoJ,GAAkBtY,GAChC,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAezlB,EAAAA,GAAYxnB,GACvD,KAAMkP,aAAoBmrB,EAAAA,IACxB,MAAM,IAAI3tB,MAAM,6BAElB,OAAOwC,CACT,CAEO,SAASC,GAAmBnP,GACjC,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAe1lB,EAAAA,GAAavnB,GACxD,KAAMkP,aAAoBq9B,EAAAA,IACxB,MAAM,IAAI7/B,MAAM,8BAElB,OAAOwC,CACT,CAEO,SAASugB,GAAyBzvB,GACvC,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAe3c,EAAAA,GAAoBtwB,GAC/D,KAAMkP,aAAoBkrB,GAAAA,GACxB,MAAM,IAAI1tB,MAAM,qCAElB,OAAOwC,CACT,CAEO,SAAS+7B,GAAsBjrC,GACpC,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAeT,EAAAA,GAAiBxsC,GAC5D,KAAMkP,aAAoBq9B,EAAAA,IACxB,MAAM,IAAI7/B,MAAM,kCAElB,OAAOwC,CACT,CAEO,SAASwmB,GAAsB11B,GACpC,MAAMkP,EAAWhG,EAAAA,GAAW+jC,eAAeZ,EAAAA,GAAgBrsC,GAC3D,KAAMkP,aAAoBk9B,EAAAA,IACxB,MAAM,IAAI1/B,MAAM,iCAElB,OAAOwC,CACT,CAEO,SAAS7F,GAAerJ,G,IAEToN,EADpB,MAAMA,EAAOlE,EAAAA,GAAWmE,QAAQrN,GAAOmC,MAAMiL,KACvCggC,EAAchgC,SAAa,QAAbA,EAAAA,EAAMigC,eAANjgC,IAAAA,OAAAA,EAAAA,EAAekgC,QAAQ,GAC3C,OAAOF,EAAc,EAAgChkC,UAAOlI,CAC9D,CAEO,SAASgB,GAAoBqxB,GAClC,MAAe,eAARA,GAAgC,cAARA,CACjC,CAEO,SAAS4K,GAAen+B,GAC7B,OAAOsY,GAAkBtY,GAAOgJ,WAAWxF,KAC7C,CAEO,SAAS4wB,GAAoBhnB,G,IAC3BA,EAAAA,EAAAA,EAAP,OAA6ElM,QAAtEkM,EAAAA,SAAU,QAAVA,EAAAA,EAAMA,YAANA,IAAAA,GAA4B,QAA5BA,EAAAA,EAAYoH,OAAO,GAAGjG,cAAtBnB,IAAAA,OAAAA,EAAAA,EAA8BgU,KAAM2C,GAAMA,EAAElV,OAAOg5B,MAAOj/B,QAAY1H,IAAN0H,WAAhEwE,IAAAA,GAAAA,CACT,CAEO,MAAMw+B,GAAW,gBAEX1lB,GAAoB1iB,GAC1BooC,GAASjJ,KAAKn/B,IAA2B,iBAAVA,GAAuBA,EAAMsO,WAAW,MAAStO,EAAMqoC,SAAS,KAG7FroC,EAFE,IAAIA,KAKFmlC,GAAuB4E,I,IAAgBA,E,OAAAA,SAAQ,QAARA,EAAAA,EAAM,UAANA,IAAAA,OAAAA,EAAAA,EAAUC,gBAAgBD,aAAAA,EAAAA,EAAK77B,MAAM,KAAM,IAElFoK,GAAgB9b,GACpB,CAACyT,EAAiBE,KACvB3T,EAAM4T,aAAa,IAAIC,EAAAA,GAAiB,CAAEJ,UAASE,YAAW,G,mSC9M3D,MAAM85B,UAA4B/mC,EAAAA,GA+B/BotB,WAAAA,GACN,MAAM1zB,EAAQ6C,EAAAA,GAAc2X,SAASta,gBAAe,GAIpD,OAHIuG,KAAK1E,MAAMwR,QACbvT,EAAMG,UAAU,gBAAwBsG,KAAK1E,MAAMwR,QAE9CvT,CACT,CApCA,WAAA4G,CAAY7E,GACV8E,M,kUAAM,EACJ8O,MAAO,IAAI5H,EAAAA,GAAiB,CAC1BlF,WAAYiU,EAAAA,GACZvT,QAAS,CAAC,CAAEC,MAAO,IAAK7B,MAAO5F,EAAMsR,QAAS+B,UAAW,eAExDrT,IAGL0E,KAAKK,qBAAqB,KACxB,MAAMkG,EAAOlE,EAAAA,GAAWmE,QAAQxG,MAEhCA,KAAKoB,MAAMC,IACTkF,EAAKjF,iBAAkBiF,I,IACjBA,EAIOA,GAJE,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaK,KACpC5N,KAAKpG,SAAS,CACZL,MAAOyG,KAAKitB,cAActzB,WAEV,QAAT4M,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWjL,SAAUiS,EAAAA,aAAaC,SAC3CxN,KAAKpG,SAAS,CACZL,MAAO,IAAIqK,EAAAA,EAAkB,CAC3BE,UAAW2K,UAOzB,EAUA,EAvCWm4B,EAuCG3kC,YAAY,EAAGC,YAC3B,MAAM,MAAE3I,GAAU2I,EAAMC,WAClBtK,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,GAAKwB,EAIL,OACE,kBAACwK,MAAAA,CAAI1L,UAAWR,EAAOgvC,gBACrB,kBAACttC,EAAM0I,UAAS,CAACC,MAAO3I,OAMhC,MAAMkV,EAAoB,KACxB,MAAM5W,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAO0X,QACrB,kBAACiB,EAAAA,EAAQA,CAACC,MAAO,EAAGhY,MAAO,KAC3B,kBAAC+X,EAAAA,EAAQA,CAACC,MAAO,EAAGhY,MAAO,MAE7B,kBAAC+X,EAAAA,EAAQA,CAACC,MAAO,EAAGhY,MAAO,QAC3B,kBAACsL,MAAAA,CAAI1L,UAAWR,EAAOyN,KACrB,kBAACkL,EAAAA,EAAQA,CAACC,MAAO,IACjB,kBAACD,EAAAA,EAAQA,CAACC,MAAO,EAAG/X,OAAQ,MAG9B,kBAACqL,MAAAA,CAAI1L,UAAWR,EAAOiZ,MACrB,kBAACA,OAAAA,CAAKzY,UAAWR,EAAOivC,UACtB,kBAACt2B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACK,OAAAA,CAAKzY,UAAWR,EAAOkvC,MACtB,kBAACv2B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAOiZ,MACrB,kBAACA,OAAAA,CAAKzY,UAAWR,EAAOmvC,UACtB,kBAACx2B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACK,OAAAA,CAAKzY,UAAWR,EAAOovC,MACtB,kBAACz2B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAOiZ,MACrB,kBAACA,OAAAA,CAAKzY,UAAWR,EAAOqvC,UACtB,kBAAC12B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACK,OAAAA,CAAKzY,UAAWR,EAAOsvC,MACtB,kBAAC32B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAOiZ,MACrB,kBAACA,OAAAA,CAAKzY,UAAWR,EAAOuvC,UACtB,kBAAC52B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACK,OAAAA,CAAKzY,UAAWR,EAAOwvC,MACtB,kBAAC72B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAOiZ,MACrB,kBAACA,OAAAA,CAAKzY,UAAWR,EAAOyvC,UACtB,kBAAC92B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACK,OAAAA,CAAKzY,UAAWR,EAAO0vC,MACtB,kBAAC/2B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAAC1M,MAAAA,CAAI1L,UAAWR,EAAOiZ,MACrB,kBAACA,OAAAA,CAAKzY,UAAWR,EAAO2vC,UACtB,kBAACh3B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACK,OAAAA,CAAKzY,UAAWR,EAAO4vC,MACtB,kBAACj3B,EAAAA,EAAQA,CAACC,MAAO,QAO3B,SAAS1Y,EAAUQ,GACjB,MAAO,CACLsuC,gBAAgBruC,EAAAA,EAAAA,KAAI,CAClBS,QAAS,OACTP,OAAQ,OAER,oDAAqD,CACnDuX,SAAU,QAGZ,mBAAoB,CAClBhX,QAAS,UAGb+K,WAAWxL,EAAAA,EAAAA,KAAI,CACbE,OAAQ,oBACRD,MAAO,oBACPuY,SAAU,WACVrY,gBAAiBJ,EAAMK,OAAO2L,WAAW0M,QACzCC,OAAQ,aAAa3Y,EAAMK,OAAOsY,OAAOC,OACzChD,QAAS,QAEXoB,QAAQ/W,EAAAA,EAAAA,KAAI,CACV4Y,aAAc,OACdnY,QAAS,OACT0W,eAAgB,kBAElBrK,KAAK9M,EAAAA,EAAAA,KAAI,CACPijC,UAAW,OACXrqB,aAAc,SAEhBN,MAAMtY,EAAAA,EAAAA,KAAI,CACRS,QAAS,SAEX6tC,UAAUtuC,EAAAA,EAAAA,KAAI,CACZC,MAAO,QAETsuC,MAAMvuC,EAAAA,EAAAA,KAAI,CACRyf,WAAY,KACZxf,MAAO,QAETuuC,UAAUxuC,EAAAA,EAAAA,KAAI,CACZC,MAAO,QAETwuC,MAAMzuC,EAAAA,EAAAA,KAAI,CACRyf,WAAY,MACZxf,MAAO,QAETyuC,UAAU1uC,EAAAA,EAAAA,KAAI,CACZC,MAAO,MACPwf,WAAY,OAEdkvB,MAAM3uC,EAAAA,EAAAA,KAAI,CACRyf,WAAY,MACZxf,MAAO,QAET2uC,UAAU5uC,EAAAA,EAAAA,KAAI,CACZC,MAAO,MACPwf,WAAY,OAEdovB,MAAM7uC,EAAAA,EAAAA,KAAI,CACRyf,WAAY,MACZxf,MAAO,QAET6uC,UAAU9uC,EAAAA,EAAAA,KAAI,CACZC,MAAO,MACPwf,WAAY,QAEdsvB,MAAM/uC,EAAAA,EAAAA,KAAI,CACRyf,WAAY,MACZxf,MAAO,QAET+uC,UAAUhvC,EAAAA,EAAAA,KAAI,CACZC,MAAO,MACPwf,WAAY,QAEdwvB,MAAMjvC,EAAAA,EAAAA,KAAI,CACRyf,WAAY,MACZxf,MAAO,QAGb,C,qICvNO,MAAMivC,UAAyB7nC,EAAAA,GAS5BuG,WAAAA,GACNpG,KAAKsR,cAEoBzQ,EAAAA,EAAAA,IAAyBb,MAEjCsB,iBAAiB,CAACiQ,EAAUC,KACvCD,EAAS3E,UAAY4E,EAAU5E,SAAW2E,EAASzE,SAAW0E,EAAU1E,SAC1E9M,KAAKsR,cACL7V,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAewC,WAAY,CACpGyO,QAAS2E,EAAS3E,QAClBE,OAAQyE,EAASzE,WAIzB,CAEQwE,UAAAA,GACN,MAAM1Q,GAAmBC,EAAAA,EAAAA,IAAyBb,MAE9CY,EAAiBtF,MAAMsR,QACzB5M,KAAKpG,SAAS,CACZuC,KAAM,IAAIyqC,EAAoB,CAC5Bh6B,QAAShM,EAAiBtF,MAAMsR,QAChCE,OAAQlM,EAAiBtF,MAAMwR,WAInC9M,KAAKpG,SAAS,CACZuC,KAAM,IAAI2R,EAAAA,EAAgB,CACxBC,QAAS,uBAIjB,CAzCA,WAAA5N,CAAY7E,GACV8E,M,kUAAM,IACD9E,IAGL0E,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EAqCA,EA5CW0nC,EA4CGzlC,YAAY,EAAGC,YAC3B,MAAM,KAAE/F,GAAS+F,EAAMC,WACvB,OAAOhG,GAAQ,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,M,kCC3CnC,MAAMwrC,UAA6B9nC,EAAAA,GAShCuG,WAAAA,GACNpG,KAAK4nC,yBAEL,MAAMC,GAAgBhZ,EAAAA,EAAAA,IAAsB7uB,MAC5CA,KAAKoB,MAAMC,IACTwmC,EAAcvmC,iBAAiB,CAACiQ,EAAUC,KACpCD,EAAS5U,QAAU6U,EAAU7U,QAC/BqD,KAAK8nC,cACL9nC,KAAK4nC,4BAIb,CAEQA,sBAAAA,GACN,MAAMC,GAAgBhZ,EAAAA,EAAAA,IAAsB7uB,MAGtC0kB,GAAMqM,EAAAA,EAAAA,YACNr2B,GAAOq2B,EAAAA,EAAAA,UAASrM,GAAKqjB,SAAS,EAAG,UACjCC,EAAmB,IAAIlX,EAAAA,GAAe,CAC1Cp2B,KAAMA,EAAKutC,cACXntC,GAAI4pB,EAAIujB,gBAGJC,EAAgB,IAAI5gC,EAAAA,GAAiB,CACzCuW,cAAe,EACfzb,WAAY,CAAE2E,IAAKohC,OAAON,EAAcvsC,MAAMqB,QAC9CozB,WAAYiY,EACZllC,QAAS,CAAC,CACRC,MAAO,qBACP7B,MAAO,cACPyN,UAAW,UACXkI,UAAW,QACXC,MAAO,EACPC,KAAM,EACN5N,QAAS,OAIbnJ,KAAKoB,MAAMC,IACT6mC,EAAc5mC,iBAAkBhG,I,IAC1BA,EACcA,EAAAA,EAAAA,GADJ,QAAVA,EAAAA,EAAMiL,YAANjL,IAAAA,OAAAA,EAAAA,EAAYA,SAAUiS,EAAAA,aAAa1H,UACX,QAAVvK,EAAAA,EAAMiL,YAANjL,IAAAA,GAAkB,QAAlBA,EAAAA,EAAYw/B,cAAZx/B,IAAAA,GAAuB,QAAvBA,EAAAA,EAAqB,UAArBA,IAAAA,OAAAA,EAAAA,EAAyByS,UAAW,IAGxCjM,SAAS,oCACnB9B,KAAKpG,SAAS,CAAEwuC,UAAU,QAMlCF,EAAc/mC,UAChB,CAEO2mC,WAAAA,GACL9nC,KAAKpG,SAAS,CACZwuC,UAAU,GAEd,CApEA,WAAAjoC,GACEC,MAAM,CACJgoC,UAAU,IAGZpoC,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EAiEF,MAGaqoC,EAAqE,EAAGC,eACnF,MAAM,SAAEF,GAAaE,EAASnmC,WAE9B,OAAKimC,EAKH,kBAAChP,EAAAA,MAAKA,CACJC,SAAS,UACTlyB,MAbsB,kCAetB,kBAACq7B,IAAAA,KAduB,8NAgBtB,kBAAC9qB,EAAAA,WAAUA,CACTzO,KAAK,oBACLlQ,KAAK,OACLX,KAAK,KACL+U,OAAO,SACP5M,KAAK,oEACN,wBAhBE,M,iCCpEJ,SAASgoC,GAAuB,YAAE11B,EAAW,MAAE3Q,IACpD,MAAM,UAAE8U,EAAWlT,UAAW0kC,IAAmCC,EAAAA,EAAAA,oBAC/D,oDAEKhuC,EAAWiuC,IAAgBvmC,EAAAA,EAAAA,YAelC,OAbAmY,EAAAA,EAAAA,WAAU,KACR,MAAMyO,EAAiB1mB,EAAAA,GAAWC,aAAaJ,GAC/CwmC,EAAa3f,EAAeztB,MAAMqB,OAElC,MAAMgsC,EAAM5f,EAAeznB,iBAAkBhG,IAC3CotC,EAAaptC,EAAMqB,SAGrB,MAAO,KACLgsC,EAAIhrB,gBAEL,CAACzb,KAEA8U,GAAcwxB,GAAmC/tC,EAKnD,kBAAC+tC,EAAAA,CACCpwC,KAAK,KACL8T,OAAO,mBACPhL,MAAO,CACLw1B,MAAOj8B,EAAUC,KAAKkuC,UACtBjS,IAAKl8B,EAAUK,GAAG8tC,UAClBC,WAAYh2B,EACZi2B,WAAY,WAEdC,kBAAkB,IAbb,IAgBX,C,ogBCpBA,MAAMC,EAAc,CAClBC,GAAI,CAAExwC,MAAO,MAAOsT,SAAU,KAC9Bwf,GAAI,CAAE9yB,MAAO,MAAOsT,SAAU,KAC9Bm9B,GAAI,CAAEzwC,MAAO,MAAOsT,SAAU,MAGzB,SAASo9B,GAAO,SACrBltC,EAAQ,QACRmtC,EAAO,iBACPC,GAAmB,EAAI,kBACvBC,GAAoB,EAAI,MACxBniC,EAAK,SACLopB,EAAQ,KACRn4B,EAAO,KAAI,KACXmxC,IAEA,MAAOC,EAAaC,EAAaC,GAuGnC,WAKE,MAAOF,EAAaG,IAAkBxnC,EAAAA,EAAAA,eAA6B9H,GAE7DuvC,GAAcC,EAAAA,EAAAA,aAAa9nC,IAC/B4nC,EAAeG,EAAqB/nC,EAAEqzB,WACrC,IAEG2U,GAAcF,EAAAA,EAAAA,aAAa9nC,IAC/B,MAAMioC,EAAQjoC,EAAEkoC,QAAQ,GACxBN,EAAeG,EAAqBE,EAAM5U,WACzC,IAEG8U,GAAYL,EAAAA,EAAAA,aACf9nC,IACCooC,SAAS5U,oBAAoB,YAAaqU,GAC1CO,SAAS5U,oBAAoB,UAAW2U,IAE1C,CAACN,IAGGQ,GAAaP,EAAAA,EAAAA,aAChB9nC,IACCooC,SAAS5U,oBAAoB,YAAawU,GAC1CI,SAAS5U,oBAAoB,WAAY6U,IAE3C,CAACL,IAmBH,MAAO,CAACP,EAhBR,SAAqBznC,GACnBA,EAAE8gC,kBACF9gC,EAAEsoC,iBAEFF,SAAS7U,iBAAiB,YAAasU,GACvCO,SAAS7U,iBAAiB,UAAW4U,EACvC,EAEA,SAAsBnoC,GACpBA,EAAE8gC,kBACF9gC,EAAEsoC,iBAEFF,SAAS7U,iBAAiB,YAAayU,GACvCI,SAAS7U,iBAAiB,WAAY8U,EACxC,EAGF,CAxJmDE,GAE3CzyC,GAASC,EAAAA,EAAAA,YAAWC,GACpBwyC,GAAgBzyC,EAAAA,EAAAA,YAAW0yC,EAAkBpyC,GAC7CqyC,GAAa3yC,EAAAA,EAAAA,YAAW4yC,EAAAA,eAExBC,EAAaC,EAAAA,OAAa,OAC1B,YAAEC,EAAW,WAAEC,IAAeC,EAAAA,EAAAA,GAAU,CAAC,EAAGJ,IAC5C,aAAEK,IAAiBC,EAAAA,EAAAA,GACvB,CACEC,eAAe,EACfC,QAAQ,EACR/B,WAEFuB,IAmJFrwB,EAAAA,EAAAA,WAAU,KACR,GAAK6vB,SAAShuC,KAMd,OAFAguC,SAAShuC,KAAKivC,UAAU/pC,IAAI,oBAErB,KACL8oC,SAAShuC,KAAKivC,UAAUxI,OAAO,sBAEhC,IAvJH,MAAM3qC,EAAU,gBAAC8L,MAAAA,CAAI1L,UAAWR,EAAOI,SAAUgE,GAC3CovC,EAAgB7B,QAAAA,EAAeR,EAAY5wC,GAAMK,MACjDsT,EAAWi9B,EAAY5wC,GAAM2T,SAEnC,OACE,gBAACu/B,EAAAA,EAAQA,CACPC,MAAM,EACNnC,QAASA,EACThZ,UAAU,QACVob,aAAc,qBACdnzC,UAAWR,EAAO4zC,cAClBC,cAAe7zC,EAAO8zC,OACtBC,WAAY,CACVrU,QAASgT,GAEX1yC,OAAQ,CACN0/B,QAAS,CACP9+B,MAAO4yC,EACPt/B,aAGJtT,MAAO,GACPozC,OAAQ,CACNC,cAAc,EACdC,WAAYl0C,EAAOm0C,cAErBC,cAAep0C,EAAOq0C,KACtBC,aAAc9C,EACd+C,WAAY,CACVN,cAAc,EACdC,WAAYl0C,EAAOu0C,aAGrB,gBAACC,EAAAA,GAAUA,CAACC,cAAAA,EAAaC,SAAAA,EAAQC,WAAAA,GAC/B,gBAACzoC,O,EAAAA,EAAAA,CACC0oC,aACmB,iBAAVtlC,EACHulC,EAAAA,GAAU5Z,WAAWqW,OAAOwD,QAAQxlC,MAAMA,GAC1CulC,EAAAA,GAAU5Z,WAAWqW,OAAOwD,QAAQxlC,MAAM,YAEhD9O,UAAWR,EAAOmM,WACdgnC,EACAH,G,WAAAA,CACJ3xB,IAAKyxB,I,6UAEL,gBAAC5mC,MAAAA,CACC1L,WAAWu0C,EAAAA,EAAAA,IAAGnC,EAAWoC,mBAAoBh1C,EAAOi1C,SACpDrD,YAAaA,EACbC,aAAcA,IAEhB,gBAAC3lC,MAAAA,CAAI1L,WAAWu0C,EAAAA,EAAAA,IAAG/0C,EAAO0X,OAAQw9B,QAAQxD,IAAS1xC,EAAOm1C,iBACxD,gBAACjpC,MAAAA,CAAI1L,UAAWR,EAAO2nB,SACrB,gBAACytB,EAAAA,WAAUA,CACT90C,KAAK,QACL6Q,QAAQ,YACRvI,QAAS2oC,EACTnlC,cAAayoC,EAAAA,GAAU5Z,WAAWqW,OAAOwD,QAAQO,MACjDvjC,SAAS4J,EAAAA,EAAAA,GAAE,0BAA2B,YAGxB,iBAAVpM,EACN,gBAACpD,MAAAA,CAAI1L,UAAWR,EAAOs1C,cACrB,gBAAC91B,EAAAA,KAAIA,EAAAA,CAAC+B,QAAQ,MAAS0xB,GACpB3jC,GAEFopB,GACC,gBAACxsB,MAAAA,CAAI1L,UAAWR,EAAO04B,SAAUtsB,cAAayoC,EAAAA,GAAU5Z,WAAWqW,OAAOwD,QAAQpc,UAC/EA,IAKPppB,EAEDoiC,GAAQ,gBAACxlC,MAAAA,CAAI1L,UAAWR,EAAOu1C,aAAc7D,IAE9CD,EAA8B,gBAAC+D,EAAAA,gBAAeA,CAACC,sBAAAA,GAAsBr1C,GAAjDA,K,OAKhC,CAqDA,SAAS6xC,EAAqB1U,GAC5B,IAAImY,EAAcpD,SAAShuC,KAAKqxC,aAAepY,EAAU+U,SAAShuC,KAAKsxC,YAEvE,MAAO,GADY9yC,KAAKwX,IAAI,EAAeg4B,SAAShuC,KAAKmd,YAAe,IAAK,IAAIpc,QAAQ,MAE3F,CAgBA,MAAMnF,EAAaQ,I,IAyFUA,EAAAA,EAxF3B,MAAO,CACLyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbS,QAAS,OACT6e,cAAe,SACfpf,OAAQ,OACRi1B,KAAM,QACN7Y,UAAW,OACX9D,SAAU,aAEZ26B,QAAQnzC,EAAAA,EAAAA,KAAI,CACVgzB,IAAK,EACLxa,SAAU,sBAEV,6BAA8B,CAC5B08B,UAAWn1C,EAAMo1C,QAAQC,MAG7BnC,eAAejzC,EAAAA,EAAAA,KAAI,CACjBG,gBAAiB,GAAGJ,EAAMK,OAAO2L,WAAW0M,qBAC5ChY,QAAS,OACTgX,SAAU,mBACV6H,cAAe,WAEjBk0B,cAAcxzC,EAAAA,EAAAA,KAAI,CAChB,WAAY,CACV89B,UAAW,mBACXE,WAAY,kBAEZ,WAAY,CACVA,WAAY,GAAGj+B,EAAMs1C,YAAYC,OAAO,0BACxCxX,UAAW,oBAQjB4V,MAAM1zC,EAAAA,EAAAA,KAAI,CAERG,gBAAiB,yBAEjBqY,SAAU,sBAEV,WAAY,CACVrY,gBAAiB,GAAGJ,EAAMu6B,WAAWib,QAAQxpC,wBAC7CypC,OAAQ,EACR/1C,QAAS,KACTia,KAAM,EACNlB,SAAU,WACVoB,MAAO,EACPoZ,IAAK,KAGT4gB,YAAY5zC,EAAAA,EAAAA,KAAI,CACd,WAAY,CACVuM,QAAS,EAET,WAAY,CACVA,QAAS,EACTyxB,WAAYj+B,EAAMs1C,YAAYC,OAAO,eAI3Cv+B,QAAQ/W,EAAAA,EAAAA,KAAI,CACVwM,MAAO,gBACP6b,SAAU,EACV1S,QAAS5V,EAAM+X,QAAQ,EAAG,EAAG,GAC7B+jB,aAAc,aAAa97B,EAAMK,OAAOsY,OAAOC,SAEjD67B,gBAAgBx0C,EAAAA,EAAAA,KAAI,CAClB67B,aAAc,SAEhB7U,SAAShnB,EAAAA,EAAAA,KAAI,CACXwY,SAAU,WACVoB,MAAO7Z,EAAM+X,QAAQ,GACrBkb,IAAKjzB,EAAM+X,QAAQ,KAErB68B,cAAc30C,EAAAA,EAAAA,KAAI,CAChBwM,MAAO,eACPipC,aAAc,eAEhB1d,UAAU/3B,EAAAA,EAAAA,KAAI,CACZwM,MAAO,kBACP8K,MAAOvX,EAAMK,OAAOE,KAAK0L,UACzBsc,WAAYvoB,EAAM+X,QAAQ,KAE5BrY,SAASO,EAAAA,EAAAA,KAAI,CACX2V,QAAS5V,EAAM+X,QAAwC,QAAhC/X,EAAuB,QAAvBA,EAAAA,EAAMu6B,WAAW6Y,cAAjBpzC,IAAAA,OAAAA,EAAAA,EAAyB4V,eAAzB5V,IAAAA,EAAAA,EAAoC,GAC3DG,OAAQ,OACRmoB,SAAU,EACV/L,UAAW,IAEbs4B,aAAa50C,EAAAA,EAAAA,KAAI,CACfwM,MAAO,cACPkpC,YAAa31C,EAAM+X,QAAQ,GAC3B0H,OAAQzf,EAAM+X,QAAQ,GAAI,GAAI,GAAI,KAEpCw8B,SAASt0C,EAAAA,EAAAA,KAAI,CACXgzB,IAAK,EACLtZ,KAAM3Z,EAAM+X,SAAS,GACrB09B,OAAQ,EACRh9B,SAAU,WACVya,OAAQlzB,EAAMkzB,OAAO0iB,UAK3B,SAAS3D,EAAiBjyC,EAAsBH,GAC9C,OAAOI,EAAAA,EAAAA,KAAI,CACTwM,MAAO,0BAA0B5M,IACjC6X,SAAU,mBAEV,CAAC1X,EAAM8yB,YAAY+iB,KAAK,OAAQ,CAC9B31C,MAAO,eAAeF,EAAM+X,QAAQ,iBACpCvE,SAAU,iBAGhB,CC/UO,MAAMsiC,EAAc,EACzBpyC,WACAkL,QACAgkC,SACA/B,UACA9c,YAAW,EACXgiB,iBAAgB,EAChBC,0BAEA,MAAM12C,GAASC,EAAAA,EAAAA,YAAWC,GAI1B,OAAKozC,EAFoBmD,GAAkBhiB,EAezC,kBAACvoB,MAAAA,CAAI1L,UAAWR,EAAOmM,WACrB,kBAACD,MAAAA,CAAI1L,UAAWR,EAAO22C,cACrB,kBAACzlC,EAAAA,OAAMA,CAACC,QAAQ,UAAUjQ,KAAK,OAAOX,KAAK,KAAK6Q,KAAM,aAAcxI,QAAS2oC,GAAS,sBAGrF9c,GAAYiiB,GAEdtyC,GAdD,kBAACktC,EAAMA,CAAC/wC,KAAK,KAAK+O,MAAOA,EAAOiiC,QAASA,GACtCntC,GANE,MAwBLlE,EAAaQ,IAA0B,CAC3CyL,WAAWxL,EAAAA,EAAAA,KAAI,CACbE,OAAQ,OACRD,MAAO,OACP8L,WAAYhM,EAAMK,OAAO2L,WAAW0M,QACpC9C,QAAS5V,EAAM+X,QAAQ,GACvBrX,QAAS,OACT6e,cAAe,SACf9G,SAAU,WACVwa,IAAK,EACLtZ,KAAM,EACNuZ,OAAQ,IAEV+iB,cAAch2C,EAAAA,EAAAA,KAAI,CAChBS,QAAS,OACT0W,eAAgB,gBAChBC,WAAY,SACZ8gB,cAAen4B,EAAM+X,QAAQ,GAE7BgyB,GAAI,CACFtqB,OAAQ,OCxEP,SAASktB,EAA0B/7B,GACxC,MAAM27B,EAAO37B,EACVtH,OAAQ8F,GAAMA,EAAEkB,KAAOlB,EAAE4B,UAAY5B,EAAEhL,OACvC2I,IAAKzD,GAOV,SAAsBA,GACpB,IAAI4oB,EAAM5oB,EAAOlF,MA0BnB,IAAkBA,EAHhB,OArBE,CAAC,0CAA2C,iCAAiCmF,SAASD,EAAOgH,OAyB/E,OADAlM,EAvBH8tB,IAwBqB,KAAV9tB,IAAiBM,MAAMuW,OAAO7W,EAAM4O,WAAWsX,UAvBlE,CACC,SACA,OACA,cACA,YACA,WACA,gBACA,iBACA,wBACA/gB,SAASD,EAAOgH,MACjB,CAAC,OAAQ,SAAS/G,SAAS2oB,KAEX,iBAARA,IAETA,EAAMA,EAAIjtB,QAAQ,SAAWyV,GAAM,KAAKA,KACxCwX,EAAM,IAAIA,MAIP,GAAG5oB,EAAOgH,MAAMhH,EAAO0H,WAAWkhB,GAC3C,CAhCqBwa,CAAapjC,IAC7B6J,KAAK,MAGR,OAAOo5B,EAAK3qC,OAAS2qC,EAAO,MAC9B,C,yyBCCO,MAAM2J,UAAiC/I,EAAAA,GAK5C,WAAAvlC,CAAYsM,G,IAOEA,EANZrM,MAAM,CACJsuC,oBAAqB,aACrBv2C,KAAMuoB,EAAAA,GACNte,WAAYiU,EAAAA,GACZs4B,KAAMC,EAAAA,aAAaC,UACnBrzB,OAAQ,WACRrS,SAA8B,QAApBsD,EAAAA,EAAM04B,sBAAN14B,IAAAA,EAAAA,EAAwB,IAAInH,IAAKqC,GAAO,OAC7CA,GAAAA,CACHmnC,SAAUriC,EAAM6f,SAChBxlB,OAAQ2F,EAAMsiC,gBAEhBnJ,kBAAkB,EAClBoJ,kBAAmB9J,IAjBvB,OAAQC,sBAAR,GACA,OAAQ4J,oBAAR,GACA,OAAQziB,gBAAR,GAkBEtsB,KAAKmlC,eAAiB14B,EAAM04B,eAC5BnlC,KAAK+uC,aAAetiC,EAAMsiC,aAC1B/uC,KAAKssB,SAAW7f,EAAM6f,SAGtBtsB,KAAKsB,iBAAkBiQ,IACrB,GAAIA,EAASpI,SAAWnJ,KAAKssB,SAAU,CACrC,IAAI2iB,GAAa,EACjB,MAAMC,EAAiB39B,EAASpI,QAAQ7D,IAAKzD,I,IAEb,EAO9B,OAPiD,QAAnB,EAAA7B,KAAKmlC,sBAAL,eAAqBvjC,KAChDutC,GACCA,EAActmC,MAAQhH,EAAOgH,KAC7BsmC,EAAc5lC,WAAa1H,EAAO0H,UAClC4lC,EAAcxyC,QAAUkF,EAAOlF,UAGLkF,EAAOitC,UAAYjtC,EAAOiF,SAAW9G,KAAK+uC,cACtEE,GAAa,EACN,OACFptC,GAAAA,CACHitC,UAAU,EACVhoC,OAAQ9G,KAAK+uC,gBAIVltC,IAILotC,GACFjvC,KAAKpG,SAAS,CAAEuP,QAAS+lC,GAE7B,GAEJ,E,m6BCOF,MAGME,GAAmB,GAFPC,2BAEqB7jC,MAAM,KAAK,eAE3C,MAAMw6B,WAAyBnmC,EAAAA,GAiB7BuG,WAAAA,GACApG,KAAK1E,MAAMg0C,UACdtvC,KAAKpG,SAAS,CAAE01C,SAuXb,IAAIjhB,EAAAA,GAAqB,CAAC,KApX/BruB,KAAKoB,MAAMC,IACTrB,KAAK4c,iBAAiB5P,EAAAA,GAAmB8P,IACvC9c,KAAKuvC,yBAAyBzyB,EAAMC,QAAQnQ,SAC5C5M,KAAKpG,SAAS,CAAEgT,QAASkQ,EAAMC,QAAQnQ,QAASE,OAAQgQ,EAAMC,QAAQjQ,YAItE9M,KAAK1E,MAAMsR,SACb5M,KAAKuvC,yBAAyBvvC,KAAK1E,MAAMsR,SAGrBvK,EAAAA,GAAW+jC,eAAeZ,EAAAA,GAAgBxlC,MAClDsB,iBAAkBiQ,IAC1BA,EAAS5U,OACX6nC,aAAatE,QAAQuE,EAAAA,GAAmBlzB,EAAS5U,MAAM4O,cAIvDvL,KAAK1E,MAAM4sC,gBACRloC,KAAK1E,MAAM4sC,cAAcsH,UAC5BxvC,KAAK1E,MAAM4sC,cAAc/mC,WAG/B,CAEAsuB,WAAAA,GACE,MAAO,CAAE7iB,QAAS5M,KAAK1E,MAAMsR,QAASE,OAAQ9M,KAAK1E,MAAMwR,OAC3D,CAEA4iB,aAAAA,CAAc1nB,GACZ,MAAMynC,EAA8C,CAAC,GAEjDznC,EAAO4E,SAAW5E,EAAO8E,UAC3B2iC,EAAY7iC,QAAU5E,EAAO4E,QAAW5E,EAAO4E,aAAqBvS,EACpEo1C,EAAY3iC,OAAS9E,EAAO8E,OAAU9E,EAAO8E,YAAoBzS,GAGnE2F,KAAKpG,SAAS61C,EAChB,CAEOh+B,iBAAAA,GACL,MAAMpJ,EAAWhG,EAAAA,GAAW+jC,eAAezlB,EAAAA,GAAY3gB,MACvD,KAAMqI,aAAoBmrB,EAAAA,IACxB,MAAM,IAAI3tB,MAAM,6B,IAIO,EAGzB,OAJKwC,EAAS2G,YACZ3G,EAAS4G,cAAsC,QAAxB,EAAAjP,KAAK1E,MAAMo0C,qBAAX,QAA4B,QAG9CrnC,CACT,CAWOuhB,iBAAAA,GACL,OAAO5pB,KAAKyR,oBAAoBzC,UAClC,CAEO2gC,WAAAA,GACL3vC,KAAKpG,SAAS,CAAEgT,aAASvS,EAAWyS,YAAQzS,GAC9C,CAEQk1C,wBAAAA,CAAyB3iC,GAC/B,MAAMhM,GAAmBC,EAAAA,EAAAA,IAAyBb,MAC5Cc,GAAQC,EAAAA,EAAAA,IAAcH,GAEtB6F,EAAc,IAAIa,EAAAA,GAAiB,CACvClF,WAAY,CAAE2E,IAAKjG,GACnBgC,QAAS,CACP,CACEC,MAAO,IACP7B,MAAO0L,EACP+B,UAAW,cAKX3N,EAA2B,IAAIC,EAAAA,EAAyB,CAC5DC,MAAO0L,EACPrS,KAAM,QACNuG,QACAoO,MAAOzI,IAGTzF,EAAyBG,WACzBnB,KAAKpG,SAAS,CAAEoH,6BAChBhB,KAAKoB,MAAMC,IACTL,EAAyBM,iBAAiB,KACxCtB,KAAK4vC,6BAITnpC,EAAYtF,WAEZnB,KAAKoB,MAAMC,IACToF,EAAYnF,iBAAkBhG,I,IACxBA,EAA2CA,EAAAA,EAA/C,IAAc,QAAVA,EAAAA,EAAMiL,YAANjL,IAAAA,OAAAA,EAAAA,EAAYA,SAAUiS,EAAAA,aAAaK,OAAkB,QAAVtS,EAAAA,EAAMiL,YAANjL,IAAAA,GAAkB,QAAlBA,EAAAA,EAAYqS,cAAZrS,IAAAA,OAAAA,EAAAA,EAAoBnB,QAAS,EAAG,C,IACpDmB,EAAAA,EAAzB,MAAMu0C,EAAuC,QAApBv0C,EAAAA,EAAMiL,KAAKoH,OAAO,UAAlBrS,IAAAA,GAA4B,QAA5BA,EAAAA,EAAsBoM,cAAtBpM,IAAAA,OAAAA,EAAAA,EAA8BsG,KAAM+F,GAAiB,gBAAXA,EAAExP,MAEjE03C,GAAoBA,EAAiB7nC,OAAO,IAC9ChH,EAAyBpH,SAAS,QAC7BoH,EAAyB1F,OAAK,CACjC0G,WAAY,GAAG6tC,EAAiB7nC,OAAO,OAG7C,KAIJhH,EAAyBpH,SAAS,QAC7BoH,EAAyB1F,OAAK,CACjC0G,WAAY4K,IAEhB,CAEcgjC,uBAAAA,G,qBACZ,MAAM,yBAAE5uC,GAA6BhB,KAAK1E,MAC1C,IAAK0F,EACH,OAGF,MAAMQ,QAAaC,EAAAA,EAAAA,IAAqBT,GACpCQ,GACFxB,KAAKpG,SAAS,CAAEk2C,kBAAmBtuC,GAEvC,E,6KAAA,W,MAxJA,YAAmBlG,G,IAEHA,EACAA,EACFA,EAHZ8E,MAAM,GACJ2vB,WAA4B,QAAhBz0B,EAAAA,EAAMy0B,kBAANz0B,IAAAA,EAAAA,EAAoB,IAAIw1B,EAAAA,GAAe,CAAC,GACpDuU,WAA4B,QAAhB/pC,EAAAA,EAAM+pC,kBAAN/pC,IAAAA,EAAAA,EAAoBy0C,GAAez0C,GAC/CylB,SAAwB,QAAdzlB,EAAAA,EAAMylB,gBAANzlB,IAAAA,EAAAA,EAAkB,CAAC,IAAIuqC,EAAAA,GAAgB,CAAC,GAAI,IAAIC,EAAAA,GAAmB,CAAC,IAC9E3pC,KAAM,IAAI6zC,GAAsB,CAAC,GACjCC,YAAa,IAAIvI,EAAiB,CAAC,GACnCQ,cAAe,IAAIP,GAChBrsC,IAVP,OAAUq0B,WAAW,IAAIO,EAAAA,GAAyBlwB,KAAM,CAAE+F,KAAM,CAAC,UAAW,aA0E5E,OAAOooB,yBAA0BryB,IAC/B,MAAMuM,EAAWrI,KAAKyR,oBACjB3V,GAAUuM,EAAS2G,aAAelT,GAIvCuM,EAAS4G,cAAcnT,OAAQzB,GAAW,KAnE1C2F,KAAKK,qBAAqBL,KAAKoG,YAAYiB,KAAKrH,MAClD,EA8IA,EA7JWgmC,GA6JJ/jC,YAAY,EAAGC,YACpB,MAAM,KAAE/F,GAAS+F,EAAMC,WACjBtK,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OAAO,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOq4C,eAAe,IAAE/zC,GAAQ,kBAACA,EAAK8F,UAAS,CAACC,MAAO/F,IAAS,OAIpF,MAAM6zC,WAA8BnwC,EAAAA,IACzC,EADWmwC,GACJ/tC,YAAY,EAAGC,YACpB,MAAMtB,GAAmBC,EAAAA,EAAAA,IAAyBqB,IAC5C,SACJ6e,EAAQ,SACRuuB,EAAQ,YACRW,EAAW,QACXrjC,EAAO,cACPs7B,EAAa,kBACb4H,EAAiB,yBACjB9uC,EAAwB,SACxBsrB,GACE1rB,EAAiBuB,YACf,SAAEimC,IAAaF,aAAAA,EAAAA,EAAe/lC,aAAc,CAChDimC,UAAU,GAENvwC,GAASC,EAAAA,EAAAA,YAAWC,IAe1B,OACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOmM,UAAWyB,GAAG,qBAClC2iC,GAAYF,GAAiB,kBAACG,EAAoBA,CAACC,SAAUJ,IAC7D5b,EAAW,kBAAC6jB,GAAAA,CAAejuC,MAAOA,IAAY,kBAACkuC,GAAAA,CAAuBrvB,SAAUA,EAAU7e,MAAOA,IAClG,kBAAC6B,MAAAA,CAAI1L,UAAWR,EAAOsE,MAAOmzC,GAAY,kBAACA,EAASrtC,UAAS,CAACC,MAAOotC,KACrE,kBAACjB,EAAWA,CACVlD,SAAU8E,KAAiBrjC,EAC3Bw8B,QAAS,IAAMxoC,EAAiB+uC,cAChCxoC,MAAO,cAAcyF,IACrB0f,SAAUA,EACVgiB,cAAehiB,EACfiiB,oBACEvtC,GACA8uC,GACE,kBAAC/mC,EAAAA,OAAMA,CAACC,QAAQ,YAAY5Q,KAAK,KAAK6Q,KAAK,cAAcxI,QA3BhCsB,KAC7B+tC,aAAAA,EAAAA,EAAmBrvC,UACrBqvC,EAAkBrvC,QAAQsB,IAG5BtG,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAe2C,yCAGrC4L,WAAW,IAAMtJ,EAAiB+uC,cAAe,OAkBtCnwC,EAAAA,KAKNywC,GAAe,kBAACA,EAAYhuC,UAAS,CAACC,MAAO+tC,QAOxD,MA2BME,GAAiB,EAAGjuC,Y,IASDtB,EARvB,MAAMyvC,GAAsBC,EAAAA,EAAAA,uBACtBz4C,GAASC,EAAAA,EAAAA,YAAWC,IAAW,GAC/B6I,GAAmBC,EAAAA,EAAAA,IAAyBqB,IAC5C,uBAAEquC,GAA2B3vC,EAAiBuB,WAC9CilB,GAAkB9e,EAAAA,EAAAA,IAAmB1H,GACrCorB,GAAwBpD,EAAAA,EAAAA,IAAyBhoB,GACjD4vC,EAAmB5vC,EAAiBtF,MAAMylB,SAASnf,KAAM0iC,GAAYA,aAAmBuB,EAAAA,IAExF4K,EAAkD,QAAjC7vC,EAAAA,EAAiBtF,MAAMy0B,kBAAvBnvB,IAAAA,OAAAA,EAAAA,EAAmCuB,WACpDuuC,EAAuBtpB,EAAgBjlB,WACvCwuC,EAAsB/vC,EAAiB6Q,oBAAoBtP,YAC1DyuC,EAAgBC,GAAqBjG,IAAAA,SAAe,KAAM3gC,EAAAA,EAAAA,IAAqBrJ,IAStF,OANAorB,SAAAA,EAAuB/c,cAAcykB,EAAAA,GAAqB,GAAG/2B,QAE7D2d,EAAAA,EAAAA,WAAU,KACRu2B,GAAkB5mC,EAAAA,EAAAA,IAAqBrJ,KACtC,CAAC6vC,EAAgBC,EAAsBC,EAAqB/vC,IAG7D,kBAACmD,MAAAA,CAAI1L,UAAWR,EAAOghC,iBACrB,kBAACrhB,EAAAA,MAAKA,CAAC9H,IAAK,EAAGE,WAAY,SAAUgF,KAAM,OAAQjF,eAAe,iBAChE,kBAACqc,EAAsB/pB,UAAS,CAACC,MAAO8pB,IACvC5E,GACC,kBAACrjB,MAAAA,KACC,kBAACqjB,EAAgBnlB,UAAS,CAACC,MAAOklB,KAGtC,kBAAC5P,EAAAA,MAAKA,CAAC9H,IAAK,EAAGE,WAAY,UACzB,kBAAC8H,EAAAA,WAAUA,CACTnX,KAAMqwC,EACN5nC,QAAQ,YACRC,KAAK,cACLxI,QAAS,KACP4vC,EAAoBE,GAA0B,aAC9C90C,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBiC,OAAQ/B,EAAAA,GAAoB+B,OAAO4B,0BAE7E,oBAGAixC,GAAoB,kBAACA,EAAiBvuC,UAAS,CAACC,MAAOsuC,QAY5DJ,GAAyB,EAAGrvB,WAAU7e,YAC1C,MAAMrK,GAASC,EAAAA,EAAAA,YAAWC,KACnB+4C,EAAaC,GAAkBnG,IAAAA,UAAe,GAC/C/3B,EApFe,CAAC3Q,IACtB,MAAO2Q,EAAam+B,GAAkBpG,IAAAA,WAChChqC,GAAmBC,EAAAA,EAAAA,IAAyBqB,GAC5CklB,GAAkB9e,EAAAA,EAAAA,IAAmB1H,GAErCqwC,EAA6B9nC,I,IAG7B+nC,EAFJ,MAAMA,EAAoB/nC,EAAQvH,KAAM+F,GAAgB,0BAAVA,EAAEkB,KAChD,MAAuC,OAAhCqoC,aAAAA,EAAAA,EAAmB3nC,WAAoD,QAAhC2nC,aAAAA,EAAAA,EAAmB3nC,UAC7D2nC,SAAwB,QAAxBA,EAAAA,EAAmBv0C,aAAnBu0C,IAAAA,OAAAA,EAAAA,EAA0B1zC,QAAQ,KAAM,SACxCnD,GAeN,OAZAigB,EAAAA,EAAAA,WAAU,KACR02B,EAAeC,EAA0B7pB,EAAgB9rB,MAAM6N,UAE/D,MAAMw/B,EAAMvhB,EAAgB9lB,iBAAkBiQ,IAC5Cy/B,EAAeC,EAA0B1/B,EAASpI,YAGpD,MAAO,KACLw/B,EAAIhrB,gBAEL,CAACyJ,IAEGvU,GA4Das+B,CAAejvC,GAC7BtB,GAAmBC,EAAAA,EAAAA,IAAyBqB,GAE5CgiC,EAAa7hC,EAAAA,GAAW+jC,eAAeZ,EAAAA,GAAgB5kC,GACvDwmB,GAAkB9e,EAAAA,EAAAA,IAAmB1H,GACrCorB,GAAwBpD,EAAAA,EAAAA,IAAyBhoB,GAEvD,SAASwwC,IACP,MAAMv5C,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACgM,MAAAA,CAAI1L,UAAWR,EAAOw5C,YACrB,kBAACC,KAAAA,KAAG,6BApUIjC,SAqUR,kBAACtrC,MAAAA,CAAI1L,UAAWR,EAAO05C,oBAAoB,gBAAcnC,IAG/D,CAEA,MAAM7tC,EACJ,kBAACwxB,EAAAA,KAAIA,CAACxjB,OAAQ,kBAAC6hC,EAAAA,OACb,kBAACrtC,MAAAA,CAAI1L,UAAWR,EAAO0J,MACpByB,EAAAA,OAAOwuC,sBACN,kBAACze,EAAAA,KAAK0e,KAAI,CACRzsC,MAAM,gBACN0sC,UAAU,gBACVzoC,KAAM,sBACNjC,IAAI,4DACJmG,OAAO,SACP1M,QAAS,KACPhF,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBiC,OAAQ/B,EAAAA,GAAoB+B,OAAOyB,4BAIhF,kBAAC2zB,EAAAA,KAAK0e,KAAI,CACRzsC,MAAM,gBACN0sC,UAAU,gBACVzoC,KAAM,oBACNjC,IAAI,+EACJmG,OAAO,SACP1M,QAAS,KACPhF,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBiC,OAAQ/B,EAAAA,GAAoB+B,OAAO2B,2BAOpF,OACE,kBAACyE,MAAAA,CAAI1L,UAAWR,EAAOghC,iBACrB,kBAACrhB,EAAAA,MAAKA,CAAC9H,IAAK,EAAGC,eAAgB,gBAAiBiF,KAAM,QACpD,kBAAC4C,EAAAA,MAAKA,CAAC9H,IAAK,EAAGE,WAAY,SAAUgF,KAAM,QACxCsvB,GACC,kBAAC1sB,EAAAA,MAAKA,CAAC9H,IAAK,EAAGE,WAAY,UACzB,kBAAC7L,MAAAA,CAAI1L,UAAWR,EAAO85C,iBAAiB,eACxC,kBAACzN,EAAWjiC,UAAS,CAACC,MAAOgiC,MAInC,kBAACngC,MAAAA,CAAI1L,UAAWR,EAAOkpB,UACrB,kBAACwnB,EAAsBA,CAAC11B,YAAaA,GAAe,GAAI3Q,MAAOA,IAC/D,kBAAC0vC,EAAAA,SAAQA,CAAC7D,QAASxsC,EAAMswC,gBAAiB,IAAMd,GAAgBD,IAC9D,kBAAC/nC,EAAAA,OAAMA,CAACC,QAAQ,YAAYC,KAAK,eAAc,YAE7C,kBAAC/Q,EAAAA,KAAIA,CAACG,UAAWR,EAAOi6C,SAAU35C,KAAM24C,EAAc,WAAa,aAAc14C,KAAK,SAGzF2oB,EAASzb,IAAKg/B,GACb,kBAACA,EAAQriC,UAAS,CAAC4G,IAAKy7B,EAAQhpC,MAAMuN,IAAK3G,MAAOoiC,OAIxD,kBAAC9sB,EAAAA,MAAKA,CAAC9H,IAAK,EAAGE,WAAY,SAAUgF,KAAM,QACzC,kBAAC4C,EAAAA,MAAKA,CAAC9H,IAAK,EAAGE,WAAY,UACzB,kBAAC7L,MAAAA,CAAI1L,UAAWR,EAAO85C,iBAAiB,WACvC3lB,GAAyB,kBAACA,EAAsB/pB,UAAS,CAACC,MAAO8pB,KAEnE5E,GACC,kBAACrjB,MAAAA,KACC,kBAACqjB,EAAgBnlB,UAAS,CAACC,MAAOklB,QAY9C,SAAS2oB,GAAez0C,GACtB,OAAO,IAAIgqC,EAAAA,GAAiB,CAC1B/B,UAAW,CACT,IAAIgC,EAAAA,GAAmB,CACrBptC,KAAMqtC,EAAAA,GACNxgC,MAAO,cACPrI,MAAOrB,EAAM8pC,UACbK,SAAU,QACVhS,WAAYn4B,EAAMgxB,WAEpB,IAAIiH,EAAAA,EAAsB,CACxBp7B,KAAMsxB,EAAAA,GACNgK,WAAYn4B,EAAMgxB,WAEpB,IAAImiB,EAAyB,CAC3BtJ,eAAgB7pC,EAAM6pC,eACtB4J,aAAczzC,EAAMyzC,aACpBziB,SAAUhxB,EAAMgxB,WAElB,IAAIkH,EAAAA,GAAe,CACjBr7B,KAAMwoB,EAAAA,GACNguB,KAAMC,EAAAA,GAAamD,eAErB,IAAIve,EAAAA,GAAe,CACjBr7B,KAAMkuC,EAAAA,EACN2L,cAAc,EACdr1C,MAAOrB,EAAMyd,iBAEf,IAAIya,EAAAA,GAAe,CACjBr7B,KAAMmuC,EAAAA,GACN0L,cAAc,IAEhB,IAAIxe,EAAAA,GAAe,CACjBr7B,KAAM+mC,EAAAA,GACN8S,cAAc,EACdrD,KAAMC,EAAAA,GAAamD,eAErB,IAAIve,EAAAA,GAAe,CACjBr7B,KAAMgnC,EAAAA,GACN6S,cAAc,EACdrD,KAAMC,EAAAA,GAAamD,iBAI3B,CAEA,SAASh6C,GAAUQ,EAAsB+zB,GACvC,MAAO,CACL4jB,eAAe13C,EAAAA,EAAAA,KAAI,CACjBwM,MAAO,gBACP6b,SAAU,EACV5nB,QAAS,OACT6b,UAAW,OACXgD,cAAe,WAEjB9T,WAAWxL,EAAAA,EAAAA,KAAI,CACbwM,MAAO,YACP6b,SAAU,EACV5nB,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,GACnBwE,UAAW,OACXgD,cAAe,SACf3J,QAAS,KAAK5V,EAAM+X,QAAQ,MAAM/X,EAAM+X,QAAQ,MAAM/X,EAAM+X,QAAQ,KACpEL,SAAU,OACVwd,UAAW,OACXzc,SAAU,aAEZw9B,cAAch2C,EAAAA,EAAAA,KAAI,CAChBS,QAAS,OACT0W,eAAgB,gBAChBC,WAAY,SACZykB,aAAc,aAAa97B,EAAMK,OAAOsY,OAAOC,OAC/Cuf,cAAen4B,EAAM+X,QAAQ,GAC7Bc,aAAc7Y,EAAM+X,QAAQ,GAE5B2hC,GAAI,CACFj6B,OAAQ,KAGZk6B,qBAAqB15C,EAAAA,EAAAA,KAAI,CACvBS,QAAS,OACT0W,eAAgB,WAChBD,IAAKnX,EAAM+X,QAAQ,OAErBnU,MAAM3D,EAAAA,EAAAA,KAAI,CACRwM,MAAO,OACP6b,SAAU,EACV5nB,QAAS,OACT6e,cAAe,SACfpI,IAAKnX,EAAM+X,QAAQ,KAErBuoB,iBAAiBrgC,EAAAA,EAAAA,KAAI,CACnBwM,MAAO,kBACPrM,gBAAiB2zB,EAAW/zB,EAAMK,OAAO2L,WAAW0M,QAAU1Y,EAAMK,OAAO2L,WAAW4tC,OACtFl5C,QAAS,OACT6e,cAAe,SACf9G,SAAU,SACVwa,IAAK,EACLC,OAAQ,EACRtd,QAAS,GAAG5V,EAAM+X,QAAQ,SAC1BZ,IAAKnX,EAAM+X,QAAQ,KAErBqhC,iBAAiBn5C,EAAAA,EAAAA,KAAI,CACnBwM,MAAO,kBACP6K,SAAU,OACV1B,QAAS,KAAK5V,EAAM+X,QAAQ,KAC5B5X,OAAQ,OACRO,QAAS,OACT2W,WAAY,SACZD,eAAgB,aAChBghB,WAAYp4B,EAAM6X,WAAWgiC,iBAC7BphC,SAAU,WACVoB,OAAQ,EACR3Z,MAAO,SAETsoB,UAAUvoB,EAAAA,EAAAA,KAAI,CACZwM,MAAO,WACP/L,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,GACnBmb,OAAQ,EACRuX,SAAU,SAEZzhC,MAAM/I,EAAAA,EAAAA,KAAI,CACRwM,MAAO,OACP,YAAa,CACX8K,MAAOvX,EAAMK,OAAOE,KAAK0I,QAG7B6vC,WAAY74C,EAAAA,GAAG;iBACFD,EAAM+X,QAAQ,GAAK;;MAGhCihC,mBAAoB/4C,EAAAA,GAAG;eACZD,EAAMK,OAAOE,KAAK0L;mBACdjM,EAAM6X,WAAWyX,UAAUhY;MAE1ClG,SAASnR,EAAAA,EAAAA,KAAI,CACXwM,MAAO,UACP6K,SAAU,OACVwiC,WAAY,OACZ55C,MAAO,QACPw5B,UAAW,WAEb6f,UAAUt5C,EAAAA,EAAAA,KAAI,CACZwM,MAAO,WACPiT,WAAY1f,EAAM+X,QAAQ,KAE5BnH,SAAS3Q,EAAAA,EAAAA,KAAI,CACXwM,MAAO,UACPy2B,UAAWljC,EAAM+X,QAAQ,GACzBrX,QAAS,OACTyW,IAAKnX,EAAM+X,QAAQ,KAGzB,C,+EClnBO,MAAMwN,EACX9I,GACkC,CAClC,CACEs9B,MAAOC,EAAAA,UAAUC,YACjBjpC,SAAU,IAAO2C,GACRA,EAAOC,MACZ7G,EAAAA,EAAAA,KAAKiB,GACIA,EAAKjB,IAAKkC,IACf,GAAmB,aAAfA,EAAMrP,KAAqB,CAC7B,MAAMs6C,EAAejrC,EAAME,OAAO9F,KAAMgH,GAAgC,YAAfA,EAAMzQ,MAC3Ds6C,IAGFA,EAAazvC,OAAO8hB,MAAQ,CAC1B,CACE3d,MAAO,aACPH,IAAK,kBACLvG,QAAUqc,I,IAEiBA,EAAAA,EAAAA,EADzBA,EAAM/a,EAAE8gC,kBACR,MAAM6P,EAAiC,QAAd51B,EAAAA,EAAM/a,EAAEoL,cAAR2P,IAAAA,GAA6B,QAA7BA,EAAAA,EAAgB61B,qBAAhB71B,IAAAA,GAA4C,QAA5CA,EAAAA,EAA+B61B,qBAA/B71B,IAAAA,OAAAA,EAAAA,EAA8Cvc,KACvE,IAAKmyC,IAAuD,IAAnCA,EAAiB5nC,QAAQ,KAChD,OAEF,MAAM8B,EAAU8lC,EAAiBlnC,MAAM,KAAK,GACvCoB,GAAuB,KAAZA,IAGhBoI,SAAAA,EAAYpI,OAKtB,CAEA,OAAOpF,QAQNwlB,EAAgC,IAAqC,CAChF,CACEslB,MAAOC,EAAAA,UAAUC,YACjBjpC,SAAU,IAAO2C,GACRA,EAAOC,MACZ7G,EAAAA,EAAAA,KAAKiB,GACIA,EAAK1E,OAAQ2F,GAAyB,aAAfA,EAAMrP,S,oGCjDvC,MAODy6C,GAF8Bj4C,KAAKk4C,MALV,KAOuD,CACpF,CAAEt2C,KAAM,IAAKu2C,aAJQ,MAIeC,WAAY,IAChD,CAAEx2C,KAAM,IAAKu2C,aANS,KAMeC,WAAY,IACjD,CAAEx2C,KAAM,IAAKu2C,aARW,IAQeC,WAAY,IACnD,CAAEx2C,KAAM,IAAKu2C,aAVW,IAUeC,WAAY,KACnD,CAAEx2C,KAAM,KAAMu2C,aAZe,IAYgBC,WAAY,KACzD,CAAEx2C,KAAM,KAAMu2C,aAAc,EAAGC,WAAY,OAchCtV,EAAkBtiC,IAE7B,MAAO63C,EAAaC,IAAiBC,EAAAA,EAAAA,WACnCN,EACA,EAAGE,gBAAgBp3B,IAAUA,EAAQk3B,EAAWz4C,OAAS,GAAK24C,EAAe33C,GAG/E,GAA+B,MAA3B63C,EAAYD,WAEd,MAAO,IAAGI,EAAAA,EAAAA,OAAOh4C,EAAW63C,EAAYF,aAAc,KAAKE,EAAYz2C,OAGzE,IAAI62C,EAAez4C,KAAKqC,MAAM7B,EAAW63C,EAAYF,cACjDO,EAAiB,EAAYJ,EAAcH,aAAgBE,EAAYD,WAC3E,MAAMO,EAAwB34C,KAAKC,MAAMy4C,GAGrCC,IAA0BN,EAAYD,YACxCK,GAAgB,EAChBC,EAAiB,GAEjBA,EAAiBC,EAGnB,MAAMC,EAAoB,GAAGH,IAAeJ,EAAYz2C,OAExD,OAAuB,IAAnB82C,EACKE,EAIF,GAAGA,KADqBF,IAAiBJ,EAAc12C,QAUnD+lB,EAAsB,CAACF,EAA0BoxB,EAAa,KAClE74C,KAAKqC,MAAMolB,EAAmBoxB,IAAe,EAGzC7e,EAAsB,CAACx7B,EAAoBq6C,KACtD,MAAMzqB,EAAiB1mB,EAAAA,GAAWC,aAAanJ,GACzCuB,EAAOquB,EAAeztB,MAAMqB,MAAMjC,KAAKsuB,OACvCluB,EAAKiuB,EAAeztB,MAAMqB,MAAM7B,GAAGkuB,OAEnCyqB,GAAMt4C,EAAAA,EAAAA,UAASL,EAAKJ,EAAM,KAEhC,MAAO,GADmB4nB,EAAoBmxB,EAAI1pB,YAAaypB,M", "sources": ["webpack://grafana-exploretraces-app/./components/Explore/StreamingIndicator.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/histogram.ts", "webpack://grafana-exploretraces-app/./utils/analytics.ts", "webpack://grafana-exploretraces-app/./components/Explore/panels/PanelMenu.tsx", "webpack://grafana-exploretraces-app/./components/states/LoadingState/LoadingStateScene.tsx", "webpack://grafana-exploretraces-app/../node_modules/moment/locale/ sync ^\\.\\/.*$", "webpack://grafana-exploretraces-app/./components/Explore/actions/AddToInvestigationButton.tsx", "webpack://grafana-exploretraces-app/./utils/comparison.ts", "webpack://grafana-exploretraces-app/./components/Explore/actions/AddToFiltersAction.tsx", "webpack://grafana-exploretraces-app/./components/Explore/actions/ShareExplorationAction.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpanListColumnsSelector.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpanListScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpansScene.tsx", "webpack://grafana-exploretraces-app/./utils/trace-merge/utils.ts", "webpack://grafana-exploretraces-app/./utils/trace-merge/tree-node.ts", "webpack://grafana-exploretraces-app/./utils/trace-merge/merge.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Structure/StructureScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/GroupBySelector.tsx", "webpack://grafana-exploretraces-app/./components/Explore/LayoutSwitcher.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/linesPanel.ts", "webpack://grafana-exploretraces-app/./components/Explore/layouts/attributeBreakdown.ts", "webpack://grafana-exploretraces-app/./components/Explore/behaviors/syncYaxis.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/AttributesDescription.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/AttributesBreakdownScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/BreakdownScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Exceptions/ExceptionUtils.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Exceptions/ExceptionsScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/queries/exceptions.ts", "webpack://grafana-exploretraces-app/./components/Explore/queries/comparisonQuery.ts", "webpack://grafana-exploretraces-app/./components/Explore/layouts/attributeComparison.ts", "webpack://grafana-exploretraces-app/./components/Explore/actions/InspectAttributeAction.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Comparison/AttributesComparisonScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Comparison/ComparisonScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/TabsBarScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/MiniREDPanel.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/TracesByServiceScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/queries/histogram.ts", "webpack://grafana-exploretraces-app/./components/Explore/layouts/HighestDifferencePanel.tsx", "webpack://grafana-exploretraces-app/./components/Explore/layouts/allComparison.ts", "webpack://grafana-exploretraces-app/./pages/Explore/PrimarySignalVariable.tsx", "webpack://grafana-exploretraces-app/./components/Explore/queries/generateMetricsQuery.ts", "webpack://grafana-exploretraces-app/./components/states/EmptyState/EmptyStateScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/queries/StepQueryRunner.ts", "webpack://grafana-exploretraces-app/./components/states/EmptyState/useMousePosition.ts", "webpack://grafana-exploretraces-app/./components/states/EmptyState/GrotNotFound.tsx", "webpack://grafana-exploretraces-app/./components/states/EmptyState/EmptyState.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/barsPanel.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/DurationComparisonControl.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/REDPanel.tsx", "webpack://grafana-exploretraces-app/./utils/testIds.ts", "webpack://grafana-exploretraces-app/./components/states/ErrorState/ErrorStateScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/Search.tsx", "webpack://grafana-exploretraces-app/./utils/frames.ts", "webpack://grafana-exploretraces-app/./components/Explore/ByFrameRepeater.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/primary-signals.ts", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelRow.tsx", "webpack://grafana-exploretraces-app/./components/Home/ErroredServicesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/SlowestTracesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/SlowestServicesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelScene.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanel.tsx", "webpack://grafana-exploretraces-app/./utils/rockets.tsx", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/utils.ts", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/BookmarkItem.tsx", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/Bookmarks.tsx", "webpack://grafana-exploretraces-app/./components/Home/HeaderScene.tsx", "webpack://grafana-exploretraces-app/./pages/Home/utils.ts", "webpack://grafana-exploretraces-app/./pages/Home/Home.tsx", "webpack://grafana-exploretraces-app/./utils/utils.ts", "webpack://grafana-exploretraces-app/./components/Explore/panels/TraceViewPanelScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/TraceDrawerScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TraceQLIssueDetector.tsx", "webpack://grafana-exploretraces-app/./addedComponents/EntityAssertionsWidget/EntityAssertionsWidget.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/Drawer.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/SmartDrawer.tsx", "webpack://grafana-exploretraces-app/./utils/filters-renderer.ts", "webpack://grafana-exploretraces-app/./pages/Explore/AttributeFiltersVariable.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/TraceExploration.tsx", "webpack://grafana-exploretraces-app/./utils/exemplars.ts", "webpack://grafana-exploretraces-app/./utils/dates.ts"], "sourcesContent": ["import React from 'react';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Icon, Tooltip, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\n\ninterface StreamingIndicatorProps {\n  isStreaming: boolean;\n  iconSize?: number;\n}\n\nexport const StreamingIndicator = ({ \n  isStreaming, \n  iconSize = 14,\n}: StreamingIndicatorProps) => {\n  const styles = useStyles2(getStyles, iconSize);\n\n  if (!isStreaming) {\n    return null;\n  }\n\n  return (\n    <Tooltip content={'Streaming'}>\n      <Icon name={'circle-mono'} size=\"md\" className={styles.streamingIndicator} />\n    </Tooltip>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2, iconSize: number) => {\n  return {\n    streamingIndicator: css({\n      width: `${iconSize}px`,\n      height: `${iconSize}px`,\n      backgroundColor: theme.colors.success.text,\n      fill: theme.colors.success.text,\n      borderRadius: '50%',\n      display: 'inline-block',\n    }),\n  };\n}; \n", "import { getTraceByServiceScene, shouldShowSelection } from '../../../utils/utils';\nimport { ComparisonSelection } from '../../../utils/shared';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { PanelBuilders, SceneFlexItem, SceneFlexLayout, SceneObject } from '@grafana/scenes';\n\nexport function getHistogramVizPanel(scene: SceneObject, yBuckets: number[]) {\n  const parent = getTraceByServiceScene(scene);\n  const panel = histogramPanelConfig()\n    .setHoverHeader(true)\n    // @ts-ignore\n    .setOption('selectionMode', 'xy')\n    .build();\n  panel.setState({\n    extendPanelContext: (vizPanel, context) => {\n      // TODO remove when we the Grafana version with #88107 is released\n      // @ts-ignore\n      context.onSelectRange = (args) => {\n        if (args.length === 0) {\n          parent.setState({ selection: undefined });\n          return;\n        }\n        const rawSelection = args[0];\n        // @ts-ignore\n        const newSelection: ComparisonSelection = { type: 'manual', raw: rawSelection };\n\n        newSelection.timeRange = {\n          from: Math.round((rawSelection.x?.from || 0) / 1000),\n          to: Math.round((rawSelection.x?.to || 0) / 1000),\n        };\n\n        // Ignore selection and return if the selection is invalid\n        if (newSelection.timeRange.from === newSelection.timeRange.to) {\n          return;\n        }\n\n        const yFrom = yBucketToDuration((args[0].y?.from || 0) - 1, yBuckets);\n        const yTo = yBucketToDuration(args[0].y?.to || 0, yBuckets);\n        newSelection.duration = { from: yFrom, to: yTo };\n\n        parent.onUserUpdateSelection(newSelection);\n        if (!shouldShowSelection(parent.state.actionView)) {\n          parent.setActionView('comparison');\n        }\n\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.start_investigation, {\n          selection: newSelection,\n          metric: 'duration',\n        });\n      };\n    },\n  });\n  return new SceneFlexLayout({\n    direction: 'row',\n    children: [\n      new SceneFlexItem({\n        body: panel,\n      }),\n    ],\n  });\n}\n\nexport const histogramPanelConfig = () => {\n  return PanelBuilders.heatmap()\n    .setOption('legend', { show: false })\n    .setOption('yAxis', {\n      unit: 's',\n      axisLabel: 'duration',\n    })\n    .setOption('color', {\n      scheme: 'Blues',\n      steps: 16,\n    })\n    .setOption('rowsFrame', { value: 'Spans' });\n};\n\nexport function yBucketToDuration(yValue: number, buckets?: number[], multiplier?: number) {\n  if (!buckets) {\n    return '';\n  }\n  if (yValue < 0) {\n    return '0';\n  }\n\n  const rawValue = buckets[Math.floor(yValue)] * (multiplier || 1);\n  if (!rawValue || isNaN(rawValue)) {\n    return '';\n  }\n  if (rawValue >= 1) {\n    return `${rawValue.toFixed(0)}s`;\n  }\n  return `${(rawValue * 1000).toFixed(0)}ms`;\n}\n", "import { reportInteraction } from '@grafana/runtime';\nimport pluginJson from '../plugin.json';\n\n// Helper function to create a unique interaction name for analytics\nconst createInteractionName = (page: UserEventPagesType, action: string) => {\n  return `${pluginJson.id.replace(/-/g, '_')}_${page}_${action}`;\n};\n\n// Runs reportInteraction with a standardized interaction name\nexport const reportAppInteraction = (\n  page: UserEventPagesType,\n  action: UserEventActionType,\n  properties?: Record<string, unknown>\n) => {\n  reportInteraction(createInteractionName(page, action), properties);\n};\n\nexport const USER_EVENTS_PAGES = {\n  analyse_traces: 'analyse_traces',\n  home: 'home',\n  common: 'common',\n} as const;\n\nexport type UserEventPagesType = keyof typeof USER_EVENTS_PAGES;\ntype UserEventActionType =\n  | keyof (typeof USER_EVENTS_ACTIONS)['analyse_traces']\n  | keyof (typeof USER_EVENTS_ACTIONS)['home']\n  | keyof (typeof USER_EVENTS_ACTIONS)['common'];\n\nexport const USER_EVENTS_ACTIONS = {\n  [USER_EVENTS_PAGES.analyse_traces]: {\n    action_view_changed: 'action_view_changed',\n    breakdown_group_by_changed: 'breakdown_group_by_changed',\n    breakdown_add_to_filters_clicked: 'breakdown_add_to_filters_clicked',\n    comparison_add_to_filters_clicked: 'comparison_add_to_filters_clicked',\n    select_attribute_in_comparison_clicked: 'select_attribute_in_comparison_clicked',\n    layout_type_changed: 'layout_type_changed',\n    start_investigation: 'start_investigation',\n    stop_investigation: 'stop_investigation',\n    open_trace: 'open_trace',\n    open_in_explore_clicked: 'open_in_explore_clicked',\n    add_to_investigation_clicked: 'add_to_investigation_clicked',\n    add_to_investigation_trace_view_clicked: 'add_to_investigation_trace_view_clicked',\n    span_list_columns_changed: 'span_list_columns_changed',\n    toggle_bookmark_clicked: 'toggle_bookmark_clicked',\n    primary_signal_changed: 'primary_signal_changed',\n    exception_message_clicked: 'exception_message_clicked',\n  },\n  [USER_EVENTS_PAGES.home]: {\n    homepage_initialized: 'homepage_initialized',\n    panel_row_clicked: 'panel_row_clicked',\n    explore_traces_clicked: 'explore_traces_clicked',\n    read_documentation_clicked: 'read_documentation_clicked',\n    filter_changed: 'filter_changed',\n    go_to_bookmark_clicked: 'go_to_bookmark_clicked',\n  },\n  [USER_EVENTS_PAGES.common]: {\n    metric_changed: 'metric_changed',\n    new_filter_added_manually: 'new_filter_added_manually',\n    app_initialized: 'app_initialized',\n    global_docs_link_clicked: 'global_docs_link_clicked',\n    metric_docs_link_clicked: 'metric_docs_link_clicked',\n    feedback_link_clicked: 'feedback_link_clicked',\n    go_to_full_app_clicked: 'go_to_full_app_clicked',\n  },\n} as const;\n", "import { PanelMenuItem, PluginExtensionLink, toURLRange, urlUtil } from '@grafana/data';\nimport {\n  SceneObjectBase,\n  VizPanelMenu,\n  SceneObject,\n  SceneComponentProps,\n  sceneGraph,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport React from 'react';\nimport { AddToInvestigationButton } from '../actions/AddToInvestigationButton';\n// Certain imports are not available in the dependant package, but can be if the plugin is running in a different Grafana version.\n// We need both imports to support Grafana v11 and v12.\n// @ts-expect-error\nimport { config, getPluginLinkExtensions, getObservablePluginLinks } from '@grafana/runtime';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\nimport { getCurrentStep, getDataSource, getTraceExplorationScene } from 'utils/utils';\nimport { firstValueFrom } from 'rxjs';\n\nexport const ADD_TO_INVESTIGATION_MENU_TEXT = 'Add to investigation';\nconst extensionPointId = 'grafana-exploretraces-app/investigation/v1';\nconst ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT = 'investigations_divider'; // Text won't be visible\nconst ADD_TO_INVESTIGATION_MENU_GROUP_TEXT = 'Investigations';\n\ninterface PanelMenuState extends SceneObjectState {\n  body?: VizPanelMenu;\n  query?: string;\n  labelValue?: string;\n  addToInvestigationButton?: AddToInvestigationButton;\n}\n\nexport class PanelMenu extends SceneObjectBase<PanelMenuState> implements VizPanelMenu, SceneObject {\n  constructor(state: Partial<PanelMenuState>) {\n    super(state);\n    this.addActivationHandler(() => {\n      const items: PanelMenuItem[] = [\n        {\n          text: 'Navigation',\n          type: 'group',\n        },\n        {\n          text: 'Explore',\n          iconClassName: 'compass',\n          href: getExploreHref(this),\n          onClick: () => onExploreClick(),\n        },\n      ];\n\n      this.setState({\n        body: new VizPanelMenu({\n          items,\n        }),\n      });\n\n      const traceExploration = getTraceExplorationScene(this);\n      const dsUid = getDataSource(traceExploration);\n\n      const addToInvestigationButton = new AddToInvestigationButton({\n        query: this.state.query,\n        dsUid,\n      });\n\n      addToInvestigationButton.activate();\n      this.setState({ addToInvestigationButton });\n      this._subs.add(\n        addToInvestigationButton?.subscribeToState(() => {\n          subscribeToAddToInvestigation(this);\n        })\n      );\n    \n      addToInvestigationButton.setState({\n        ...addToInvestigationButton.state,\n        labelValue: this.state.labelValue,\n      });\n    });\n  }\n\n  addItem(item: PanelMenuItem): void {\n    if (this.state.body) {\n      this.state.body.addItem(item);\n    }\n  }\n\n  setItems(items: PanelMenuItem[]): void {\n    if (this.state.body) {\n      this.state.body.setItems(items);\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<PanelMenu>) => {\n    const { body } = model.useState();\n\n    if (body) {\n      return <body.Component model={body} />;\n    }\n\n    return <></>;\n  };\n}\n\nconst getExploreHref = (model: SceneObject<PanelMenuState>) => {\n  const traceExploration = getTraceExplorationScene(model);\n  const datasource = getDataSource(traceExploration);\n  const timeRange = sceneGraph.getTimeRange(model).state.value;\n  const step = getCurrentStep(model);\n\n  const exploreState = JSON.stringify({\n    ['traces-explore']: {\n      range: toURLRange(timeRange.raw),\n      queries: [{ refId: 'A', datasource, query: model.state.query, step }],\n    },\n  });\n  const subUrl = config.appSubUrl ?? '';\n  const exploreUrl = urlUtil.renderUrl(`${subUrl}/explore`, { panes: exploreState, schemaVersion: 1 });\n  return exploreUrl;\n};\n\nconst onExploreClick = () => {\n  reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.open_in_explore_clicked);\n};\n\nexport const getInvestigationLink = async (addToInvestigations: AddToInvestigationButton) => {\n  const context = addToInvestigations.state.context;\n\n  // `getPluginLinkExtensions` is removed in Grafana v12\n  if (getPluginLinkExtensions !== undefined) {\n    const links = getPluginLinkExtensions({\n      extensionPointId,\n      context,\n    });\n\n    return links.extensions[0];\n  }\n\n  // `getObservablePluginLinks` is introduced in Grafana v12\n  if (getObservablePluginLinks !== undefined) {\n    const links: PluginExtensionLink[] = await firstValueFrom(\n      getObservablePluginLinks({\n        extensionPointId,\n        context,\n      })\n    );\n\n    return links[0];\n  }\n\n  return undefined;\n};\n\nasync function subscribeToAddToInvestigation(menu: PanelMenu) {\n  const addToInvestigationButton = menu.state.addToInvestigationButton;\n  if (addToInvestigationButton) {\n    const link = await getInvestigationLink(addToInvestigationButton);\n    const existingMenuItems = menu.state.body?.state.items ?? [];\n    const existingAddToInvestigationLink = existingMenuItems.find(\n      (item) => item.text === ADD_TO_INVESTIGATION_MENU_TEXT\n    );\n\n    if (link) {\n      if (!existingAddToInvestigationLink) {\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT,\n          type: 'divider',\n        });\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_GROUP_TEXT,\n          type: 'group',\n        });\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_TEXT,\n          iconClassName: 'plus-square',\n          onClick: (e) => {\n            if (link.onClick) {\n              link.onClick(e);\n            }\n\n            reportAppInteraction(\n              USER_EVENTS_PAGES.analyse_traces,\n              USER_EVENTS_ACTIONS.analyse_traces.add_to_investigation_clicked\n            );\n          },\n        });\n      } else {\n        if (existingAddToInvestigationLink) {\n          menu.state.body?.setItems(\n            existingMenuItems.filter(\n              (item) =>\n                [\n                  ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT,\n                  ADD_TO_INVESTIGATION_MENU_GROUP_TEXT,\n                  ADD_TO_INVESTIGATION_MENU_TEXT,\n                ].includes(item.text) === false\n            )\n          );\n        }\n      }\n    }\n  }\n}\n", "import { css, keyframes } from '@emotion/css';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport { useStyles2, useTheme2 } from '@grafana/ui';\nimport React from 'react';\nimport { SkeletonTheme } from 'react-loading-skeleton';\nimport { testIds } from 'utils/testIds';\n\ninterface LoadingStateSceneState extends SceneObjectState {\n  component: () => React.JSX.Element;\n}\n\nexport class LoadingStateScene extends SceneObjectBase<LoadingStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<LoadingStateScene>) => {\n    const theme = useTheme2();\n    const styles = useStyles2(getStyles);\n    const { component } = model.useState();\n\n    return (\n      <div className={styles.container} data-testid={testIds.loadingState}>\n        <SkeletonTheme\n          baseColor={theme.colors.emphasize(theme.colors.background.secondary)}\n          highlightColor={theme.colors.emphasize(theme.colors.background.secondary, 0.1)}\n          borderRadius={theme.shape.radius.default}\n        >\n          {component()}\n        </SkeletonTheme>\n      </div>\n    );\n  };\n}\n\nconst fadeIn = keyframes({\n  '0%': {\n    opacity: 0,\n  },\n  '100%': {\n    opacity: 1,\n  },\n});\n\nfunction getStyles() {\n  return {\n    container: css({\n      label: 'loading-state-scene',\n      // animation prevents flickering when loading\n      animationName: fadeIn,\n      animationDelay: '100ms',\n      animationTimingFunction: 'ease-in',\n      animationDuration: '100ms',\n      animationFillMode: 'backwards',\n    }),\n  };\n}\n", "var map = {\n\t\"./af\": 9805,\n\t\"./af.js\": 9805,\n\t\"./ar\": 4449,\n\t\"./ar-dz\": 4468,\n\t\"./ar-dz.js\": 4468,\n\t\"./ar-kw\": 3480,\n\t\"./ar-kw.js\": 3480,\n\t\"./ar-ly\": 4197,\n\t\"./ar-ly.js\": 4197,\n\t\"./ar-ma\": 2180,\n\t\"./ar-ma.js\": 2180,\n\t\"./ar-ps\": 9343,\n\t\"./ar-ps.js\": 9343,\n\t\"./ar-sa\": 230,\n\t\"./ar-sa.js\": 230,\n\t\"./ar-tn\": 2808,\n\t\"./ar-tn.js\": 2808,\n\t\"./ar.js\": 4449,\n\t\"./az\": 5865,\n\t\"./az.js\": 5865,\n\t\"./be\": 6627,\n\t\"./be.js\": 6627,\n\t\"./bg\": 901,\n\t\"./bg.js\": 901,\n\t\"./bm\": 3179,\n\t\"./bm.js\": 3179,\n\t\"./bn\": 1966,\n\t\"./bn-bd\": 969,\n\t\"./bn-bd.js\": 969,\n\t\"./bn.js\": 1966,\n\t\"./bo\": 6317,\n\t\"./bo.js\": 6317,\n\t\"./br\": 6474,\n\t\"./br.js\": 6474,\n\t\"./bs\": 5961,\n\t\"./bs.js\": 5961,\n\t\"./ca\": 7270,\n\t\"./ca.js\": 7270,\n\t\"./cs\": 1564,\n\t\"./cs.js\": 1564,\n\t\"./cv\": 3239,\n\t\"./cv.js\": 3239,\n\t\"./cy\": 2366,\n\t\"./cy.js\": 2366,\n\t\"./da\": 2453,\n\t\"./da.js\": 2453,\n\t\"./de\": 6601,\n\t\"./de-at\": 5027,\n\t\"./de-at.js\": 5027,\n\t\"./de-ch\": 8101,\n\t\"./de-ch.js\": 8101,\n\t\"./de.js\": 6601,\n\t\"./dv\": 6080,\n\t\"./dv.js\": 6080,\n\t\"./el\": 2655,\n\t\"./el.js\": 2655,\n\t\"./en-au\": 6836,\n\t\"./en-au.js\": 6836,\n\t\"./en-ca\": 2086,\n\t\"./en-ca.js\": 2086,\n\t\"./en-gb\": 2103,\n\t\"./en-gb.js\": 2103,\n\t\"./en-ie\": 5964,\n\t\"./en-ie.js\": 5964,\n\t\"./en-il\": 4379,\n\t\"./en-il.js\": 4379,\n\t\"./en-in\": 765,\n\t\"./en-in.js\": 765,\n\t\"./en-nz\": 1502,\n\t\"./en-nz.js\": 1502,\n\t\"./en-sg\": 1152,\n\t\"./en-sg.js\": 1152,\n\t\"./eo\": 50,\n\t\"./eo.js\": 50,\n\t\"./es\": 3350,\n\t\"./es-do\": 9338,\n\t\"./es-do.js\": 9338,\n\t\"./es-mx\": 1326,\n\t\"./es-mx.js\": 1326,\n\t\"./es-us\": 9947,\n\t\"./es-us.js\": 9947,\n\t\"./es.js\": 3350,\n\t\"./et\": 8231,\n\t\"./et.js\": 8231,\n\t\"./eu\": 8512,\n\t\"./eu.js\": 8512,\n\t\"./fa\": 9083,\n\t\"./fa.js\": 9083,\n\t\"./fi\": 5059,\n\t\"./fi.js\": 5059,\n\t\"./fil\": 2607,\n\t\"./fil.js\": 2607,\n\t\"./fo\": 3369,\n\t\"./fo.js\": 3369,\n\t\"./fr\": 7390,\n\t\"./fr-ca\": 6711,\n\t\"./fr-ca.js\": 6711,\n\t\"./fr-ch\": 6152,\n\t\"./fr-ch.js\": 6152,\n\t\"./fr.js\": 7390,\n\t\"./fy\": 2419,\n\t\"./fy.js\": 2419,\n\t\"./ga\": 3002,\n\t\"./ga.js\": 3002,\n\t\"./gd\": 4914,\n\t\"./gd.js\": 4914,\n\t\"./gl\": 6557,\n\t\"./gl.js\": 6557,\n\t\"./gom-deva\": 8944,\n\t\"./gom-deva.js\": 8944,\n\t\"./gom-latn\": 5387,\n\t\"./gom-latn.js\": 5387,\n\t\"./gu\": 7462,\n\t\"./gu.js\": 7462,\n\t\"./he\": 9237,\n\t\"./he.js\": 9237,\n\t\"./hi\": 9617,\n\t\"./hi.js\": 9617,\n\t\"./hr\": 6544,\n\t\"./hr.js\": 6544,\n\t\"./hu\": 341,\n\t\"./hu.js\": 341,\n\t\"./hy-am\": 1388,\n\t\"./hy-am.js\": 1388,\n\t\"./id\": 5251,\n\t\"./id.js\": 5251,\n\t\"./is\": 1146,\n\t\"./is.js\": 1146,\n\t\"./it\": 7891,\n\t\"./it-ch\": 7,\n\t\"./it-ch.js\": 7,\n\t\"./it.js\": 7891,\n\t\"./ja\": 3727,\n\t\"./ja.js\": 3727,\n\t\"./jv\": 5198,\n\t\"./jv.js\": 5198,\n\t\"./ka\": 8974,\n\t\"./ka.js\": 8974,\n\t\"./kk\": 7308,\n\t\"./kk.js\": 7308,\n\t\"./km\": 7786,\n\t\"./km.js\": 7786,\n\t\"./kn\": 4807,\n\t\"./kn.js\": 4807,\n\t\"./ko\": 1584,\n\t\"./ko.js\": 1584,\n\t\"./ku\": 1906,\n\t\"./ku-kmr\": 5305,\n\t\"./ku-kmr.js\": 5305,\n\t\"./ku.js\": 1906,\n\t\"./ky\": 9190,\n\t\"./ky.js\": 9190,\n\t\"./lb\": 7396,\n\t\"./lb.js\": 7396,\n\t\"./lo\": 8503,\n\t\"./lo.js\": 8503,\n\t\"./lt\": 3010,\n\t\"./lt.js\": 3010,\n\t\"./lv\": 5192,\n\t\"./lv.js\": 5192,\n\t\"./me\": 1944,\n\t\"./me.js\": 1944,\n\t\"./mi\": 6492,\n\t\"./mi.js\": 6492,\n\t\"./mk\": 2934,\n\t\"./mk.js\": 2934,\n\t\"./ml\": 1463,\n\t\"./ml.js\": 1463,\n\t\"./mn\": 8377,\n\t\"./mn.js\": 8377,\n\t\"./mr\": 8733,\n\t\"./mr.js\": 8733,\n\t\"./ms\": 8030,\n\t\"./ms-my\": 9445,\n\t\"./ms-my.js\": 9445,\n\t\"./ms.js\": 8030,\n\t\"./mt\": 5887,\n\t\"./mt.js\": 5887,\n\t\"./my\": 7228,\n\t\"./my.js\": 7228,\n\t\"./nb\": 8294,\n\t\"./nb.js\": 8294,\n\t\"./ne\": 9559,\n\t\"./ne.js\": 9559,\n\t\"./nl\": 600,\n\t\"./nl-be\": 8796,\n\t\"./nl-be.js\": 8796,\n\t\"./nl.js\": 600,\n\t\"./nn\": 9570,\n\t\"./nn.js\": 9570,\n\t\"./oc-lnc\": 5662,\n\t\"./oc-lnc.js\": 5662,\n\t\"./pa-in\": 7101,\n\t\"./pa-in.js\": 7101,\n\t\"./pl\": 6118,\n\t\"./pl.js\": 6118,\n\t\"./pt\": 9198,\n\t\"./pt-br\": 7203,\n\t\"./pt-br.js\": 7203,\n\t\"./pt.js\": 9198,\n\t\"./ro\": 5565,\n\t\"./ro.js\": 5565,\n\t\"./ru\": 3315,\n\t\"./ru.js\": 3315,\n\t\"./sd\": 8473,\n\t\"./sd.js\": 8473,\n\t\"./se\": 1258,\n\t\"./se.js\": 1258,\n\t\"./si\": 8798,\n\t\"./si.js\": 8798,\n\t\"./sk\": 6404,\n\t\"./sk.js\": 6404,\n\t\"./sl\": 7057,\n\t\"./sl.js\": 7057,\n\t\"./sq\": 5718,\n\t\"./sq.js\": 5718,\n\t\"./sr\": 5363,\n\t\"./sr-cyrl\": 478,\n\t\"./sr-cyrl.js\": 478,\n\t\"./sr.js\": 5363,\n\t\"./ss\": 7260,\n\t\"./ss.js\": 7260,\n\t\"./sv\": 2231,\n\t\"./sv.js\": 2231,\n\t\"./sw\": 7104,\n\t\"./sw.js\": 7104,\n\t\"./ta\": 7493,\n\t\"./ta.js\": 7493,\n\t\"./te\": 7705,\n\t\"./te.js\": 7705,\n\t\"./tet\": 4457,\n\t\"./tet.js\": 4457,\n\t\"./tg\": 2727,\n\t\"./tg.js\": 2727,\n\t\"./th\": 2206,\n\t\"./th.js\": 2206,\n\t\"./tk\": 3419,\n\t\"./tk.js\": 3419,\n\t\"./tl-ph\": 7243,\n\t\"./tl-ph.js\": 7243,\n\t\"./tlh\": 16,\n\t\"./tlh.js\": 16,\n\t\"./tr\": 7020,\n\t\"./tr.js\": 7020,\n\t\"./tzl\": 8026,\n\t\"./tzl.js\": 8026,\n\t\"./tzm\": 8537,\n\t\"./tzm-latn\": 7899,\n\t\"./tzm-latn.js\": 7899,\n\t\"./tzm.js\": 8537,\n\t\"./ug-cn\": 818,\n\t\"./ug-cn.js\": 818,\n\t\"./uk\": 8478,\n\t\"./uk.js\": 8478,\n\t\"./ur\": 7893,\n\t\"./ur.js\": 7893,\n\t\"./uz\": 9133,\n\t\"./uz-latn\": 311,\n\t\"./uz-latn.js\": 311,\n\t\"./uz.js\": 9133,\n\t\"./vi\": 2179,\n\t\"./vi.js\": 2179,\n\t\"./x-pseudo\": 2455,\n\t\"./x-pseudo.js\": 2455,\n\t\"./yo\": 3310,\n\t\"./yo.js\": 3310,\n\t\"./zh-cn\": 7244,\n\t\"./zh-cn.js\": 7244,\n\t\"./zh-hk\": 76,\n\t\"./zh-hk.js\": 76,\n\t\"./zh-mo\": 2305,\n\t\"./zh-mo.js\": 2305,\n\t\"./zh-tw\": 8588,\n\t\"./zh-tw.js\": 8588\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 1738;", "import { TimeRange } from '@grafana/data';\nimport { sceneGraph, SceneObject, SceneObjectBase, SceneObjectState, SceneQueryRunner } from '@grafana/scenes';\nimport { DataQuery, DataSourceRef } from '@grafana/schema';\n\nimport Logo from '../../../../src/img/logo.svg';\n\nexport interface AddToInvestigationButtonState extends SceneObjectState {\n  dsUid?: string;\n  query?: string;\n  labelValue?: string;\n  type?: string;\n  context?: ExtensionContext;\n  queries: DataQuery[];\n}\n\ninterface ExtensionContext {\n  timeRange: TimeRange;\n  queries: DataQuery[];\n  datasource: DataSourceRef;\n  origin: string;\n  url: string;\n  type: string;\n  title: string;\n  id: string;\n  logoPath: string;\n}\n\nexport class AddToInvestigationButton extends SceneObjectBase<AddToInvestigationButtonState> {\n  constructor(state: Omit<AddToInvestigationButtonState, 'queries'>) {\n    super({ ...state, queries: [] });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate = () => {\n    this._subs.add(\n      this.subscribeToState(() => {\n        this.getQueries();\n        this.getContext();\n      })\n    );\n  };\n\n  private readonly getQueries = () => {\n    const data = sceneGraph.getData(this);\n    const queryRunner = sceneGraph.findObject(data, isQueryRunner);\n\n    if (isQueryRunner(queryRunner)) {\n      const queries = queryRunner.state.queries.map((q) => ({\n        ...q,\n        query: this.state.query,\n      }));\n\n      if (JSON.stringify(queries) !== JSON.stringify(this.state.queries)) {\n        this.setState({ queries });\n      }\n    }\n  };\n\n  private readonly getContext = () => {\n    const { queries, dsUid, labelValue, type = 'traceMetrics' } = this.state;\n    const timeRange = sceneGraph.getTimeRange(this);\n\n    if (!timeRange || !queries || !dsUid) {\n      return;\n    }\n    const ctx = {\n      origin: 'Explore Traces',\n      type,\n      queries,\n      timeRange: { ...timeRange.state.value },\n      datasource: { uid: dsUid },\n      url: window.location.href,\n      id: `${JSON.stringify(queries)}`,\n      title: `${labelValue}`,\n      logoPath: Logo,\n    };\n    if (JSON.stringify(ctx) !== JSON.stringify(this.state.context)) {\n      this.setState({ context: ctx });\n    }\n  };\n}\n\nfunction isQueryRunner(o: SceneObject<SceneObjectState> | null): o is SceneQueryRunner {\n  return o instanceof SceneQueryRunner;\n}\n", "import { DataFrame } from '@grafana/data';\nimport { ComparisonSelection, MetricFunction } from './shared';\n\nexport const computeHighestDifference = (frame: DataFrame) => {\n  const baselineField = frame.fields.find((f) => f.name === 'Baseline');\n  const selectionField = frame.fields.find((f) => f.name === 'Selection');\n\n  let maxDifference = 0;\n  let maxDifferenceIndex = 0;\n\n  for (let i = 0; i < (baselineField?.values?.length || 0); i++) {\n    const diff = (selectionField?.values[i] || 0) - (baselineField?.values[i] || 0);\n    if (Math.abs(diff) > Math.abs(maxDifference || 0)) {\n      maxDifference = diff;\n      maxDifferenceIndex = i;\n    }\n  }\n\n  return { maxDifference, maxDifferenceIndex };\n};\n\nexport const getDefaultSelectionForMetric = (metric: MetricFunction): ComparisonSelection | undefined => {\n  if (metric === 'duration') {\n    return undefined;\n  }\n  return { query: 'status = error', type: 'auto' };\n};\n", "import React from 'react';\n\nimport { DataFrame } from '@grafana/data';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps, AdHocFiltersVariable } from '@grafana/scenes';\nimport { Button } from '@grafana/ui';\nimport { getFiltersVariable, getLabelValue } from '../../../utils/utils';\nimport { DATABASE_CALLS_KEY } from 'pages/Explore/primary-signals';\n\nexport interface AddToFiltersActionState extends SceneObjectState {\n  frame: DataFrame;\n  onClick: (payload: any) => void;\n  labelKey?: string;\n}\n\nexport class AddToFiltersAction extends SceneObjectBase<AddToFiltersActionState> {\n  public onClick = () => {\n    const variable = getFiltersVariable(this);\n\n    const labels = this.state.frame.fields.find((f) => f.labels)?.labels ?? {};\n    if (this.state.labelKey) {\n      if (!labels[this.state.labelKey]) {\n        return;\n      }\n    } else {\n      if (Object.keys(labels).length !== 1) {\n        return;\n      }\n    }\n\n    const labelName = this.state.labelKey ?? Object.keys(labels)[0];\n    const value = getLabelValue(this.state.frame, this.state.labelKey);\n\n    addToFilters(variable, labelName, value);\n\n    this.state.onClick({ labelName });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AddToFiltersAction>) => {\n    const key = model.state?.labelKey ?? '';\n    const field = model.state?.frame.fields.filter((x) => x.type !== 'time');\n    const value = field?.[0]?.labels?.[key] ?? '';\n    const filterExists = filterExistsForKey(getFiltersVariable(model), key, value.replace(/\"/g, ''));\n\n    if (!filterExists) {\n      return (\n        <Button variant=\"primary\" size=\"sm\" fill=\"text\" onClick={model.onClick} icon={'search-plus'}>\n          Add to filters\n        </Button>\n      );\n    }\n    return <></>;\n  };\n}\n\nexport const addToFilters = (variable: AdHocFiltersVariable, label: string, value: string) => {\n  // ensure we set the new filter with latest value\n  // and remove any existing filter for the same key\n  // and also keep span.db.system.name as it is a primary filter\n  const filtersWithoutNew = variable.state.filters.filter((f) => f.key === DATABASE_CALLS_KEY || f.key !== label);\n\n  // TODO: Replace it with new API introduced in https://github.com/grafana/scenes/issues/1103\n  // At the moment AdHocFiltersVariable doesn't support pushing new history entry on change\n  history.pushState(null, '');\n\n  variable.setState({\n    filters: [\n      ...filtersWithoutNew,\n      {\n        key: label,\n        operator: '=',\n        value: value,\n      },\n    ],\n  });\n};\n\nexport const filterExistsForKey = (model: AdHocFiltersVariable, key: string, value: string) => {\n  const variable = getFiltersVariable(model);\n  return variable.state.filters.find((f) => f.key === key && f.value === value);\n};\n", "import React, { useState } from 'react';\nimport { useLocation } from 'react-use';\n\nimport { ToolbarButton } from '@grafana/ui';\n\nimport { TraceExploration } from '../../../pages/Explore';\nimport { getUrlForExploration } from '../../../utils/utils';\n\ninterface ShareExplorationActionState {\n  exploration: TraceExploration;\n}\n\nexport const ShareExplorationAction = ({ exploration }: ShareExplorationActionState) => {\n  const { origin } = useLocation();\n  const [tooltip, setTooltip] = useState('Copy url');\n\n  const onShare = () => {\n    if (navigator.clipboard) {\n      navigator.clipboard.writeText(origin + getUrlForExploration(exploration));\n      setTooltip('Copied!');\n      setTimeout(() => {\n        setTooltip('Copy url');\n      }, 2000);\n    }\n  };\n\n  return <ToolbarButton variant={'canvas'} icon={'share-alt'} tooltip={tooltip} onClick={onShare} />;\n};\n", "import React, { useMemo } from 'react';\n\nimport { SelectableValue } from '@grafana/data';\nimport { Icon, Select, Field, useStyles2 } from '@grafana/ui';\nimport { VariableValue } from '@grafana/scenes';\nimport { css } from '@emotion/css';\n\nconst RECOMMENDED_ATTRIBUTES = [\n  'span.http.method', \n  'span.http.request.method', \n  'span.http.route', \n  'span.http.path', \n  'span.http.status_code', \n  'span.http.response.status_code'\n]; \n\ntype Props = {\n  options: Array<SelectableValue<string>>;\n  onChange: (columns: string[]) => void;\n  value?: VariableValue;\n};\n\nconst labelOrder = ['Recommended', 'Resource', 'Span', 'Other'];\n\nexport function SpanListColumnsSelector({ options, value, onChange }: Props) {\n  const styles = useStyles2(getStyles);\n\n  const opt = useMemo(\n    () =>\n      Object.values(\n        options.reduce((acc, curr) => {\n          if (curr.label) {\n            const label = curr.label.slice(curr.label.indexOf('.') + 1);\n\n            // use text until first dot as key\n            if (RECOMMENDED_ATTRIBUTES.includes(curr.label)) {\n              const group = acc['recommended'] ?? { label: 'Recommended', options: [] };\n              group.options.push({ ...curr, label });\n              acc['recommended'] = group;\n            } else if (curr.label.startsWith('resource.')) {\n              const group = acc['resource'] ?? { label: 'Resource', options: [] };\n              group.options.push({ ...curr, label });\n              acc['resource'] = group;\n            } else {\n              if (curr.label.startsWith('span.')) {\n                const group = acc['span'] ?? { label: 'Span', options: [] };\n                group.options.push({ ...curr, label });\n                acc['span'] = group;\n              } else {\n                const group = acc['other'] ?? { label: 'Other', options: [] };\n                group.options.push(curr);\n                acc['other'] = group;\n              }\n            }\n          }\n          return acc;\n        }, {})\n      ).sort((a, b) => labelOrder.indexOf(a.label) - labelOrder.indexOf(b.label)),\n    [options]\n  );\n\n  return (\n    <div className={styles.container}>\n      <Field label=\"Add extra columns\">\n        <Select\n          value={value?.toString() !== '' ? (value?.toString()?.split(',') ?? '') : ''}\n          placeholder={'Select an attribute'}\n          options={opt}\n          onChange={(x) => onChange(x.map((x: SelectableValue) => x.value).join(','))}\n          isMulti={true}\n          isClearable\n          virtualized\n          prefix={<Icon name=\"columns\" />}\n        />\n      </Field>\n    </div>\n  );\n}\n\nconst getStyles = () => {\n  return {\n    container: css({\n      display: 'flex',\n      minWidth: '300px',\n\n      '& > div': {\n        width: '100%',\n      },\n    }),\n  };\n};\n", "import React from 'react';\n\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { DataFrame, GrafanaTheme2, LoadingState, PanelData, toURLRange, urlUtil, toOption } from '@grafana/data';\nimport { config } from '@grafana/runtime';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { css } from '@emotion/css';\nimport Skeleton from 'react-loading-skeleton';\nimport { Icon, Link, TableCellDisplayMode, TableCustomCellOptions, useStyles2, useTheme2 } from '@grafana/ui';\nimport { map, Observable } from 'rxjs';\nimport {\n  getDataSource,\n  getSpanListColumnsVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from '../../../../../utils/utils';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n  EventTraceOpened,\n} from '../../../../../utils/shared';\nimport { SpanListColumnsSelector } from './SpanListColumnsSelector';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\n\nexport interface SpanListSceneState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  dataState: 'empty' | 'loading' | 'done';\n}\n\nexport class SpanListScene extends SceneObjectBase<SpanListSceneState> {\n  constructor(state: Partial<SpanListSceneState>) {\n    super({\n      dataState: 'empty',\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this.setState({\n        $data: new SceneDataTransformer({\n          transformations: this.setupTransformations(),\n        }),\n      });\n      const sceneData = sceneGraph.getData(this);\n\n      this.updatePanel(sceneData.state.data);\n      this._subs.add(\n        sceneData.subscribeToState((data) => {\n          this.updatePanel(data.data);\n        })\n      );\n    });\n  }\n\n  private setupTransformations() {\n    return [\n      () => (source: Observable<DataFrame[]>) => {\n        return source.pipe(\n          map((data: DataFrame[]) => {\n            return data.map((df) => {\n              const fields = df.fields;\n              const nameField = fields.find((f) => f.name === 'traceName');\n\n              const options: TableCustomCellOptions = {\n                type: TableCellDisplayMode.Custom,\n                cellComponent: (props) => {\n                  const data = props.frame;\n                  const traceIdField = data?.fields.find((f) => f.name === 'traceIdHidden');\n                  const spanIdField = data?.fields.find((f) => f.name === 'spanID');\n                  const traceId = traceIdField?.values[props.rowIndex];\n                  const spanId = spanIdField?.values[props.rowIndex];\n\n                  if (!traceId) {\n                    return props.value as string;\n                  }\n\n                  const name = props.value ? (props.value as string) : '<name not yet available>';\n                  return (\n                    <div className={'cell-link-wrapper'}>\n                      <div\n                        className={'cell-link'}\n                        title={name}\n                        onClick={() => {\n                          this.publishEvent(new EventTraceOpened({ traceId, spanId }), true);\n                        }}\n                      >\n                        {name}\n                      </div>\n                      <Link href={this.getLinkToExplore(traceId, spanId)} target={'_blank'} title={'Open in new tab'}>\n                        <Icon name={'external-link-alt'} size={'sm'} />\n                      </Link>\n                    </div>\n                  );\n                },\n              };\n              if (nameField?.config?.custom) {\n                nameField.config.custom.cellOptions = options;\n              }\n              return {\n                ...df,\n                fields,\n              };\n            });\n          })\n        );\n      },\n    ];\n  }\n\n  private getLinkToExplore = (traceId: string, spanId: string) => {\n    const traceExplorationScene = getTraceExplorationScene(this);\n    const datasource = getDataSource(traceExplorationScene);\n\n    const timeRange = sceneGraph.getTimeRange(this).state.value;\n    const exploreState = JSON.stringify({\n      ['explore-traces']: {\n        range: toURLRange(timeRange.raw),\n        queries: [{ refId: 'traceId', queryType: 'traceql', query: traceId, datasource }],\n        panelsState: {\n          trace: {\n            spanId,\n          },\n        },\n        datasource,\n      },\n    });\n    const subUrl = config.appSubUrl ?? '';\n    return urlUtil.renderUrl(`${subUrl}/explore`, { panes: exploreState, schemaVersion: 1 });\n  };\n\n  private updatePanel(data?: PanelData) {\n    if (\n      data?.state === LoadingState.Loading ||\n      data?.state === LoadingState.NotStarted ||\n      !data?.state ||\n      (data?.state === LoadingState.Streaming && !data.series?.[0]?.length)\n    ) {\n      if (this.state.dataState === 'loading') {\n        return;\n      }\n      this.setState({\n        dataState: 'loading',\n        panel: new SceneFlexLayout({\n          direction: 'row',\n          children: [\n            new LoadingStateScene({\n              component: SkeletonComponent,\n            }),\n          ],\n        }),\n      });\n      return;\n    }\n    if (data?.state === LoadingState.Done || data?.state === LoadingState.Streaming) {\n      if (data.series.length === 0 || data.series[0].length === 0) {\n        if (this.state.dataState === 'empty' && this.state.panel) {\n          return;\n        }\n        this.setState({\n          dataState: 'empty',\n          panel: new SceneFlexLayout({\n            children: [\n              new SceneFlexItem({\n                body: new EmptyStateScene({\n                  message: EMPTY_STATE_ERROR_MESSAGE,\n                  remedyMessage: EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n                  padding: '32px',\n                }),\n              }),\n            ],\n          }),\n        });\n      } else if (this.state.dataState !== 'done') {\n        this.setState({\n          dataState: 'done',\n          panel: new SceneFlexLayout({\n            direction: 'row',\n            children: [\n              new SceneFlexItem({\n                body: PanelBuilders.table()\n                  .setHoverHeader(true)\n                  .setOverrides((builder) => {\n                    return builder\n                      .matchFieldsWithName('spanID')\n                      .overrideCustomFieldConfig('hidden', true)\n                      .matchFieldsWithName('traceService')\n                      .overrideCustomFieldConfig('width', 350)\n                      .matchFieldsWithName('traceName')\n                      .overrideCustomFieldConfig('width', 350);\n                  })\n                  .build(),\n              }),\n            ],\n          }),\n        });\n      }\n    }\n  }\n\n  public onChange = (columns: string[]) => {\n    const variable = getSpanListColumnsVariable(this);\n    if (variable.getValue() !== columns) {\n      variable.changeValueTo(columns);\n\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.span_list_columns_changed,\n        {\n          columns,\n        }\n      );\n    }\n  };\n\n  public static Component = ({ model }: SceneComponentProps<SpanListScene>) => {\n    const { panel } = model.useState();\n    const styles = getStyles(useTheme2());\n    const variable = getSpanListColumnsVariable(model);\n    const { attributes } = getTraceByServiceScene(model).useState();\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <div className={styles.description}>View a list of spans for the current set of filters.</div>\n          <SpanListColumnsSelector\n            options={attributes?.map((x) => toOption(x)) ?? []}\n            value={variable.getValue()}\n            onChange={model.onChange}\n          />\n        </div>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    container: css({\n      display: 'contents',\n\n      '[role=\"cell\"] > div': {\n        display: 'flex',\n        width: '100%',\n      },\n\n      '.cell-link-wrapper': {\n        display: 'flex',\n        gap: '4px',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        width: '100%',\n\n        a: {\n          padding: 4,\n          fontSize: 0,\n\n          ':hover': {\n            background: theme.colors.background.secondary,\n          },\n        },\n      },\n\n      '.cell-link': {\n        color: theme.colors.text.link,\n        cursor: 'pointer',\n        maxWidth: '300px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n\n        ':hover': {\n          textDecoration: 'underline',\n        },\n      },\n    }),\n    description: css({\n      fontSize: theme.typography.h6.fontSize,\n      padding: `${theme.spacing(1)} 0 ${theme.spacing(2)} 0`,\n    }),\n    header: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start',\n      gap: '10px',\n    }),\n  };\n};\n\nconst SkeletonComponent = () => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.title}>\n        <Skeleton count={1} width={80} />\n      </div>\n      {[...Array(3)].map((_, i) => (\n        <div className={styles.row} key={i}>\n          {[...Array(6)].map((_, j) => (\n            <span className={styles.rowItem} key={j}>\n              <Skeleton count={1} />\n            </span>\n          ))}\n        </div>\n      ))}\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      height: '100%',\n      width: '100%',\n      position: 'absolute',\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.border.weak}`,\n      padding: '5px',\n    }),\n    title: css({\n      marginBottom: '20px',\n    }),\n    row: css({\n      marginBottom: '5px',\n      display: 'flex',\n      justifyContent: 'space-around',\n    }),\n    rowItem: css({\n      width: '14%',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport { SceneComponentProps, SceneFlexItem, SceneObject, SceneObjectBase, SceneObjectState } from '@grafana/scenes';\nimport { SpanListScene } from 'components/Explore/TracesByService/Tabs/Spans/SpanListScene';\nimport { getMetricVariable, getTraceByServiceScene } from 'utils/utils';\n\nexport interface SpansSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class SpansScene extends SceneObjectBase<SpansSceneState> {\n  constructor(state: Partial<SpansSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this._subs.add(\n      getTraceByServiceScene(this).state.$data?.subscribeToState(() => {\n        this.updateBody();\n      })\n    );\n\n    this._subs.add(\n      getTraceByServiceScene(this).subscribeToState((newState, prevState) => {\n        if (newState.$data?.state.key !== prevState.$data?.state.key) {\n          this.updateBody();\n        }\n      })\n    );\n\n    this._subs.add(\n      getMetricVariable(this).subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          this.updateBody();\n        }\n      })\n    );\n\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new SpanListScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<SpansScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildSpansScene() {\n  return new SceneFlexItem({\n    body: new SpansScene({}),\n  });\n}\n", "import { Span } from '../../types';\n\nexport function nestedSetLeft(span: Span): number {\n  if (span.attributes) {\n    for (const a of span.attributes) {\n      if (a.key === 'nestedSetLeft') {\n        return parseInt(a.value.intValue || a.value.Value?.int_value || '0', 10);\n      }\n    }\n  }\n\n  throw new Error('nestedSetLeft not found!');\n}\n\nexport function nestedSetRight(span: Span): number {\n  if (span.attributes) {\n    for (const a of span.attributes) {\n      if (a.key === 'nestedSetRight') {\n        return parseInt(a.value.intValue || a.value.Value?.int_value || '0', 10);\n      }\n    }\n  }\n\n  throw new Error('nestedSetRight not found!');\n}\n", "import { Span } from '../../types';\nimport { nestedSetLeft, nestedSetRight } from './utils';\n\nexport class TreeNode {\n  name: string;\n  serviceName: string;\n  operationName: string;\n  spans: Span[];\n  left: number;\n  right: number;\n  children: TreeNode[];\n  parent: TreeNode | null;\n  traceID: string;\n\n  constructor({\n    name,\n    serviceName,\n    operationName,\n    spans,\n    left,\n    right,\n    traceID,\n  }: {\n    name: string;\n    serviceName: string;\n    operationName: string;\n    spans: Span[];\n    left: number;\n    right: number;\n    traceID: string;\n  }) {\n    this.name = name;\n    this.serviceName = serviceName;\n    this.operationName = operationName;\n    this.spans = spans;\n    this.left = left;\n    this.right = right;\n    this.children = [];\n    this.parent = null;\n    this.traceID = traceID;\n  }\n\n  addSpan(span: Span) {\n    // expand our left/right based on this span\n    this.left = Math.min(nestedSetLeft(span), this.left);\n    this.right = Math.max(nestedSetRight(span), this.right);\n    this.spans.push(span);\n  }\n\n  addChild(node: TreeNode) {\n    node.parent = this;\n    this.children.push(node);\n  }\n\n  isChild(span: Span): boolean {\n    return nestedSetLeft(span) > this.left && nestedSetRight(span) < this.right;\n  }\n\n  findMatchingChild(span: Span): TreeNode | null {\n    const name = nodeName(span);\n\n    for (const child of this.children) {\n      if (child.name === name) {\n        return child;\n      }\n    }\n\n    return null;\n  }\n}\n\nexport function createNode(s: Span): TreeNode {\n  const serviceNameAttr = s.attributes?.find((a) => a.key === 'service.name');\n  return new TreeNode({\n    left: nestedSetLeft(s),\n    right: nestedSetRight(s),\n    name: nodeName(s),\n    serviceName: serviceNameAttr?.value.stringValue ?? serviceNameAttr?.value?.Value?.string_value ?? '',\n    operationName: s.name ?? '',\n    spans: [s],\n    traceID: s.traceId ?? '',\n  });\n}\n\nfunction nodeName(s: Span): string {\n  let svcName = '';\n  for (const a of s.attributes || []) {\n    if (a.key === 'service.name' && a.value.stringValue) {\n      svcName = a.value.stringValue;\n    }\n  }\n\n  return `${svcName}:${s.name}`;\n}\n", "import { TraceSearchMetadata } from '../../types';\nimport { createNode, TreeNode } from './tree-node';\nimport { nestedSetLeft } from './utils';\n\nexport function mergeTraces(traces: TraceSearchMetadata[]): TreeNode {\n  const tree = new TreeNode({\n    name: 'root',\n    serviceName: '',\n    operationName: '',\n    left: Number.MIN_SAFE_INTEGER,\n    right: Number.MAX_SAFE_INTEGER,\n    spans: [],\n    traceID: '',\n  });\n\n  if (traces && traces.length > 0) {\n    for (const trace of traces) {\n      if (trace.spanSets?.length !== 1) {\n        throw new Error('there should be only 1 spanset!');\n      }\n\n      const traceStartTime = parseInt(trace.startTimeUnixNano || '0', 10);\n\n      const ss = trace.spanSets[0];\n      // sort by nestedSetLeft\n      ss.spans.sort((s1, s2) => nestedSetLeft(s1) - nestedSetLeft(s2));\n\n      // reset curNode to root each loop to re-overlay the next trace\n      let curNode: TreeNode = tree;\n      // left/right is only valid w/i a trace, so reset it each loop\n      resetLeftRight(tree);\n      for (const span of ss.spans) {\n        // force traceID to be the same for all spans in a trace\n        span.traceId = trace.traceID;\n        span.startTimeUnixNano = `${parseInt(span.startTimeUnixNano, 10) - traceStartTime}`;\n\n        // walk up the tree until we find a node that is a parent of this span\n        while (curNode.parent !== null) {\n          if (curNode.isChild(span)) {\n            break;\n          }\n          curNode = curNode.parent;\n        }\n\n        // is there an already existing child that matches the span?\n        const child = curNode.findMatchingChild(span);\n        if (child) {\n          child.addSpan(span);\n          // to the next span!\n          curNode = child;\n          continue;\n        }\n\n        // if not, create a new child node and make it the cur node\n        const newNode = createNode(span);\n        newNode.traceID = trace.traceID;\n        curNode.addChild(newNode);\n        curNode = newNode;\n      }\n    }\n  }\n\n  return tree;\n}\n\nexport function dumpTree(t: TreeNode, depth: number): string {\n  let result = '';\n  const space = ' '.repeat(depth * 2);\n\n  result += `${space}${t.name} ${t.spans.length}\\n`;\n\n  for (const c of t.children) {\n    result += dumpTree(c, depth + 1);\n  }\n  return result;\n}\n\nfunction resetLeftRight(t: TreeNode) {\n  t.left = Number.MAX_SAFE_INTEGER;\n  t.right = Number.MIN_SAFE_INTEGER;\n\n  for (const c of t.children) {\n    resetLeftRight(c);\n  }\n}\n", "import React from 'react';\n\nimport {\n  Panel<PERSON><PERSON>ers,\n  SceneComponentProps,\n  SceneDataNode,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  explorationDS,\n  filterStreamingProgressTransformations,\n  MetricFunction,\n  VAR_FILTERS_EXPR,\n  VAR_LATENCY_PARTIAL_THRESHOLD_EXPR,\n  VAR_LATENCY_THRESHOLD_EXPR,\n} from '../../../../../utils/shared';\nimport { TraceSearchMetadata } from '../../../../../types';\nimport { mergeTraces } from '../../../../../utils/trace-merge/merge';\nimport { createDataFrame, Field, FieldType, GrafanaTheme2, LinkModel, LoadingState } from '@grafana/data';\nimport { TreeNode } from '../../../../../utils/trace-merge/tree-node';\nimport { Icon, LinkButton, Stack, Text, useTheme2 } from '@grafana/ui';\nimport Skeleton from 'react-loading-skeleton';\nimport { EmptyState } from '../../../../states/EmptyState/EmptyState';\nimport { css } from '@emotion/css';\nimport { getOpenTrace, getTraceExplorationScene } from 'utils/utils';\nimport { structureDisplayName } from '../TabsBarScene';\n\nexport interface ServicesTabSceneState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  loading?: boolean;\n  tree?: TreeNode;\n  metric?: MetricFunction;\n}\n\nconst ROOT_SPAN_ID = '0000000000000000';\n\nexport class StructureTabScene extends SceneObjectBase<ServicesTabSceneState> {\n  constructor(state: Partial<ServicesTabSceneState>) {\n    super({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(state.metric as MetricFunction)],\n        }),\n        transformations: filterStreamingProgressTransformations,\n      }),\n      loading: true,\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  public _onActivate() {\n    this._subs.add(\n      this.state.$data?.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Loading || state.data?.state === LoadingState.Streaming) {\n          this.setState({ loading: true });\n          return;\n        }\n\n        if (state.data?.state === LoadingState.Done && state.data?.series.length) {\n          const frame = state.data?.series[0].fields[0].values[0];\n          if (frame) {\n            const response = JSON.parse(frame) as TraceSearchMetadata[];\n            const tree = mergeTraces(response);\n            tree.children.sort((a, b) => countSpans(b) - countSpans(a));\n\n            this.setState({\n              loading: false,\n              tree,\n              panel: new SceneFlexLayout({\n                height: '100%',\n                wrap: 'wrap',\n                children: this.getPanels(tree),\n              }),\n            });\n          }\n        }\n      })\n    );\n  }\n\n  private getPanels(tree: TreeNode) {\n    return tree.children.map((child) => {\n      return new SceneFlexItem({\n        height: 150,\n        width: '100%',\n        minHeight: '400px',\n        body: this.getPanel(child),\n      });\n    });\n  }\n\n  private getPanel(tree: TreeNode) {\n    const timeRange = sceneGraph.getTimeRange(this);\n    const from = timeRange.state.value.from;\n    const to = timeRange.state.value.to;\n\n    const openTrace = getOpenTrace(this);\n\n    return PanelBuilders.traces()\n      .setTitle(`Structure for ${tree.serviceName} [${countSpans(tree)} spans used]`)\n      .setOption('createFocusSpanLink' as any, (traceId: string, spanId: string): LinkModel<Field> => {\n        return {\n          title: 'Open trace',\n          href: '#',\n          onClick: () => openTrace(traceId, spanId),\n          origin: {} as Field,\n          target: '_self',\n        };\n      })\n      .setData(\n        new SceneDataNode({\n          data: {\n            state: LoadingState.Done,\n            timeRange: {\n              from,\n              to,\n              raw: { from, to },\n            },\n            series: [\n              {\n                ...this.buildData(tree),\n              },\n            ],\n          },\n        })\n      )\n      .build();\n  }\n\n  private buildData(tree: TreeNode) {\n    const trace = this.getTrace(tree, ROOT_SPAN_ID);\n    const traceName = trace[0].serviceName + ':' + trace[0].operationName;\n\n    return createDataFrame({\n      name: `Trace ${traceName}`,\n      refId: `trace_${traceName}`,\n      fields: [\n        {\n          name: 'references',\n          type: FieldType.other,\n          values: trace.map((x) => x.references),\n        },\n        {\n          name: 'traceID',\n          type: FieldType.string,\n          values: trace.map((x) => x.traceID),\n        },\n        {\n          name: 'spanID',\n          type: FieldType.string,\n          values: trace.map((x) => x.spanID),\n        },\n        {\n          name: 'parentSpanID',\n          type: FieldType.string,\n          values: trace.map((x) => x.parentSpanId),\n        },\n        {\n          name: 'serviceName',\n          type: FieldType.string,\n          values: trace.map((x) => x.serviceName),\n        },\n        {\n          name: 'operationName',\n          type: FieldType.string,\n          values: trace.map((x) => x.operationName),\n        },\n        {\n          name: 'duration',\n          type: FieldType.number,\n          values: trace.map((x) => x.duration),\n        },\n        {\n          name: 'startTime',\n          type: FieldType.number,\n          values: trace.map((x) => x.startTime),\n        },\n        {\n          name: 'statusCode',\n          type: FieldType.number,\n          values: trace.map((x) => x.statusCode),\n        },\n      ],\n    });\n  }\n\n  private getTrace(node: TreeNode, spanID: string) {\n    const erroredSpans = node.spans.reduce(\n      (acc, c) => (c.attributes?.find((a) => a.key === 'status')?.value.stringValue === 'error' ? acc + 1 : acc),\n      0\n    );\n\n    // start time needs to be different from zero otherwise for the root, otherwise the Trace View won't render it\n    let startTime = 0.0001;\n    if (spanID !== ROOT_SPAN_ID) {\n      startTime =\n        node.spans.reduce((acc, c) => acc + parseInt(c.startTimeUnixNano, 10), 0) / node.spans.length / 1000000;\n    }\n\n    const values = [\n      {\n        // Add last 5 spans of the list as external references\n        // refType = 'EXTERNAL' doesn't mean anything, it's just to be different from CHILD_OF and FOLLOW_FROM\n        references: node.spans.slice(-5).map((x) => ({\n          refType: 'EXTERNAL',\n          traceID: x.traceId,\n          spanID: x.spanID,\n        })),\n        traceID: node.traceID,\n        spanID: node.spans[0].spanID,\n        parentSpanId: spanID,\n        serviceName: node.serviceName,\n        operationName: node.operationName,\n        statusCode: erroredSpans > 0 ? 2 /*error*/ : 0 /*unset*/,\n        duration: node.spans.reduce((acc, c) => acc + parseInt(c.durationNanos, 10), 0) / node.spans.length / 1000000,\n        startTime,\n      },\n    ];\n\n    for (const child of node.children) {\n      values.push(...this.getTrace(child, node.spans[0].spanID));\n    }\n    return values;\n  }\n\n  public static Component = ({ model }: SceneComponentProps<StructureTabScene>) => {\n    const { tree, loading, panel, $data } = model.useState();\n    const styles = getStyles(useTheme2());\n    const theme = useTheme2();\n\n    const exploration = getTraceExplorationScene(model);\n    const { value } = exploration.getMetricVariable().useState();\n\n    const metric = value as MetricFunction;\n\n    let isLoading = loading || !tree?.children.length;\n    if ($data?.state.data?.state === LoadingState.Done) {\n      isLoading = false;\n    }\n\n    let description;\n    let emptyMsg = '';\n    switch (metric) {\n      case 'rate':\n        description = (\n          <>\n            <div>Analyse the service structure of the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'server';\n        break;\n      case 'errors':\n        description = (\n          <>\n            <div>Analyse the errors structure of the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'error';\n        break;\n      case 'duration':\n        description = (\n          <>\n            <div>Analyse the structure of slow spans from the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'slow';\n        break;\n    }\n\n    const tabName = structureDisplayName(metric);\n\n    const noDataMessage = (\n      <>\n        <Text textAlignment={'center'} variant=\"h3\">\n          {EMPTY_STATE_ERROR_MESSAGE}\n        </Text>\n        <Text textAlignment={'center'} variant=\"body\">\n          <div className={styles.longText}>\n            The structure tab shows {emptyMsg} spans beneath what you are currently investigating. Currently, there are\n            no descendant {emptyMsg} spans beneath the spans you are investigating.\n          </div>\n        </Text>\n        <Stack gap={0.5} alignItems={'center'}>\n          <Icon name=\"info-circle\" />\n          <Text textAlignment={'center'} variant=\"body\">\n            The structure tab works best with full traces.\n          </Text>\n        </Stack>\n\n        <div className={styles.actionContainer}>\n          Read more about\n          <div className={styles.action}>\n            <LinkButton\n              icon=\"external-link-alt\"\n              fill=\"solid\"\n              size={'sm'}\n              target={'_blank'}\n              href={\n                'https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/concepts/#trace-structure'\n              }\n            >\n              {`${tabName.toLowerCase()}`}\n            </LinkButton>\n          </div>\n        </div>\n      </>\n    );\n\n    return (\n      <Stack direction={'column'} gap={1}>\n        <div className={styles.description}>{description}</div>\n        {isLoading && (\n          <Stack direction={'column'} gap={2}>\n            <Skeleton\n              count={4}\n              height={200}\n              baseColor={theme.colors.background.secondary}\n              highlightColor={theme.colors.background.primary}\n            />\n          </Stack>\n        )}\n\n        {!isLoading && tree && tree.children.length > 0 && (\n          <div className={styles.traceViewList}>{panel && <panel.Component model={panel} />}</div>\n        )}\n\n        {$data?.state.data?.state === LoadingState.Done && !tree?.children.length && (\n          <EmptyState message={noDataMessage} padding={'32px'} />\n        )}\n      </Stack>\n    );\n  };\n}\n\nfunction buildQuery(metric: MetricFunction) {\n  let metricQuery;\n  let selectionQuery = '';\n  switch (metric) {\n    case 'errors':\n      metricQuery = 'status = error';\n      selectionQuery = 'status = error';\n      break;\n    case 'duration':\n      metricQuery = `duration > ${VAR_LATENCY_PARTIAL_THRESHOLD_EXPR}`;\n      selectionQuery = `duration > ${VAR_LATENCY_THRESHOLD_EXPR}`;\n      break;\n    default:\n      metricQuery = 'kind = server';\n      break;\n  }\n\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR} ${\n      selectionQuery.length ? `&& ${selectionQuery}` : ''\n    }} &>> { ${metricQuery} } | select(status, resource.service.name, name, nestedSetParent, nestedSetLeft, nestedSetRight)`,\n    queryType: 'traceql',\n    tableType: 'raw',\n    limit: 200,\n    spss: 20,\n    filters: [],\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    description: css({\n      fontSize: theme.typography.h6.fontSize,\n      padding: `${theme.spacing(1)} 0`,\n    }),\n    traceViewList: css({\n      display: 'flex',\n      flexDirection: 'column',\n      gap: theme.spacing.x1,\n      // Hide the minimap and header components\n      'div[class*=\"panel-content\"] > div': {\n        overflow: 'auto',\n        '> :not([class*=\"TraceTimelineViewer\"])': {\n          display: 'none',\n        },\n      },\n      // Hide the Span and Resource accordions from span details\n      'div[data-testid=\"span-detail-component\"] > :nth-child(4) > :nth-child(1)': {\n        display: 'none',\n      },\n\n      // Hide span details row\n      '.span-detail-row': {\n        display: 'none',\n      },\n\n      // Remove cursor pointer as span details is hidden\n      'div[data-testid=\"TimelineRowCell\"]': {\n        'button[role=\"switch\"]': {\n          cursor: 'text',\n        },\n      },\n      'div[data-testid=\"span-view\"]': {\n        cursor: 'text !important',\n      },\n    }),\n    longText: css({\n      maxWidth: '800px',\n      margin: '0 auto',\n    }),\n    action: css({\n      marginLeft: theme.spacing(1),\n    }),\n    actionContainer: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n    }),\n  };\n};\n\nfunction countSpans(tree: TreeNode) {\n  let count = tree.spans.length;\n  for (const child of tree.children) {\n    count += countSpans(child);\n  }\n  return count;\n}\n\nexport function buildStructureScene(metric: MetricFunction) {\n  return new SceneFlexItem({\n    body: new StructureTabScene({ metric }),\n  });\n}\n", "import { css } from '@emotion/css';\nimport { useResizeObserver } from '@react-aria/utils';\nimport React, { useEffect, useMemo, useRef, useState } from 'react';\n\nimport { GrafanaTheme2, SelectableValue } from '@grafana/data';\nimport { Select, RadioButtonGroup, useStyles2, useTheme2, measureText, Field, InputActionMeta } from '@grafana/ui';\nimport { ALL, ignoredAttributes, maxOptions, MetricFunction, RESOURCE_ATTR, SPAN_ATTR } from 'utils/shared';\nimport { AttributesBreakdownScene } from './TracesByService/Tabs/Breakdown/AttributesBreakdownScene';\nimport { AttributesComparisonScene } from './TracesByService/Tabs/Comparison/AttributesComparisonScene';\nimport { getFiltersVariable, getMetricVariable, getTraceExplorationScene } from 'utils/utils';\n\ntype Props = {\n  options: Array<SelectableValue<string>>;\n  radioAttributes: string[];\n  value?: string;\n  onChange: (label: string, ignore?: boolean) => void;\n  showAll?: boolean;\n  model: AttributesBreakdownScene | AttributesComparisonScene;\n};\n\nconst additionalWidthPerItem = 40;\nconst widthOfOtherAttributes = 180;\n\nexport function GroupBySelector({ options, radioAttributes, value, onChange, showAll = false, model }: Props) {\n  const styles = useStyles2(getStyles);\n  const theme = useTheme2();\n  const { fontSize } = theme.typography;\n\n  const [selectQuery, setSelectQuery] = useState<string>('');\n  const [allowAutoUpdate, setAllowAutoUpdate] = useState<boolean>(true);\n\n  const [availableWidth, setAvailableWidth] = useState<number>(0);\n  const controlsContainer = useRef<HTMLDivElement>(null);\n\n  const { initialGroupBy } = getTraceExplorationScene(model).useState();\n  const { filters } = getFiltersVariable(model).useState();\n  const { value: metric } = getMetricVariable(model).useState();\n  const metricValue = metric as MetricFunction;\n\n  useResizeObserver({\n    ref: controlsContainer,\n    onResize: () => {\n      const element = controlsContainer.current;\n      if (element) {\n        setAvailableWidth(element.clientWidth);\n      }\n    },\n  });\n\n  const radioOptions = useMemo(() => {\n    let radioOptionsWidth = 0;\n    return radioAttributes\n      .filter((op) => {\n        // remove radio options that are in the dropdown\n        let checks = !!options.find((o) => o.value === op);\n\n        // remove radio options that are in the filters\n        if (filters.find((f) => f.key === op && (f.operator === '=' || f.operator === '!='))) {\n          return false;\n        }\n\n        // if filters (primary signal) has 'Full Traces' selected, then don't add rootName or rootServiceName to options\n        // as you would overwrite it in the query if it's selected\n        if (filters.find((f) => f.key === 'nestedSetParent')) {\n          checks = checks && op !== 'rootName' && op !== 'rootServiceName';\n        }\n\n        // if rate or error rate metric is selected, then don't add status to options\n        // as you would overwrite it in the query if it's selected\n        if (metricValue === 'rate' || metricValue === 'errors') {\n          checks = checks && op !== 'status';\n        }\n\n        return checks;\n      })\n      .map((attribute) => ({\n        label: attribute.replace(SPAN_ATTR, '').replace(RESOURCE_ATTR, ''),\n        text: attribute,\n        value: attribute,\n      }))\n      .filter((option) => {\n        const text = option.label || option.text || '';\n        const textWidth = measureText(text, fontSize).width;\n        if (radioOptionsWidth + textWidth + additionalWidthPerItem + widthOfOtherAttributes < availableWidth) {\n          radioOptionsWidth += textWidth + additionalWidthPerItem;\n          return true;\n        } else {\n          return false;\n        }\n      });\n  }, [radioAttributes, options, filters, metricValue, fontSize, availableWidth]);\n\n  const otherAttrOptions = useMemo(() => {\n    const ops = options.filter((op) => !radioOptions.find((ro) => ro.value === op.value?.toString()));\n    return filteredOptions(ops, selectQuery);\n  }, [selectQuery, options, radioOptions]);\n\n  const getModifiedSelectOptions = (options: Array<SelectableValue<string>>) => {\n    return options\n      .filter((op) => !ignoredAttributes.includes(op.value?.toString()!))\n      .map((op) => ({ label: op.label?.replace(SPAN_ATTR, '').replace(RESOURCE_ATTR, ''), value: op.value }));\n  };\n\n  const defaultValue = initialGroupBy ?? radioOptions[0]?.value ?? otherAttrOptions[0]?.value;\n\n  // Set default value as first value in options\n  useEffect(() => {\n    if (defaultValue && !showAll && allowAutoUpdate) {\n      onChange(defaultValue, true);\n      setAllowAutoUpdate(false);\n    }\n  }, [value, defaultValue, showAll, onChange, allowAutoUpdate]);\n\n  useEffect(() => {\n    if (radioAttributes.length > 0) {\n      setAllowAutoUpdate(true);\n    }\n  }, [radioAttributes]);\n\n  useEffect(() => {\n    if (filters.some((f) => f.key === value)) {\n      setAllowAutoUpdate(true);\n    }\n  }, [filters, value]);\n\n  const showAllOption = showAll ? [{ label: ALL, value: ALL }] : [];\n  const defaultOnChangeValue = showAll ? ALL : '';\n\n  return (\n    <Field label=\"Group by\">\n      <div ref={controlsContainer} className={styles.container}>\n        {radioOptions.length > 0 && (\n          <RadioButtonGroup options={[...showAllOption, ...radioOptions]} value={value} onChange={onChange} />\n        )}\n        <Select\n          value={value && getModifiedSelectOptions(otherAttrOptions).some((x) => x.value === value) ? value : null} // remove value from select when radio button clicked\n          placeholder={'Other attributes'}\n          options={getModifiedSelectOptions(otherAttrOptions)}\n          onChange={(selected) => {\n            const newSelected = selected?.value ?? defaultOnChangeValue;\n            onChange(newSelected);\n          }}\n          className={styles.select}\n          isClearable\n          onInputChange={(value: string, { action }: InputActionMeta) => {\n            if (action === 'input-change') {\n              setSelectQuery(value);\n            }\n          }}\n          onCloseMenu={() => setSelectQuery('')}\n          virtualized\n        />\n      </div>\n    </Field>\n  );\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    select: css({\n      maxWidth: theme.spacing(22),\n    }),\n    container: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n\nexport const filteredOptions = (options: Array<SelectableValue<string>>, query: string) => {\n  if (options.length === 0) {\n    return [];\n  }\n\n  if (query.length === 0) {\n    return options.slice(0, maxOptions);\n  }\n\n  const queryLowerCase = query.toLowerCase();\n  return options\n    .filter((tag) => {\n      if (tag.value && tag.value.length > 0) {\n        return tag.value.toLowerCase().includes(queryLowerCase);\n      }\n      return false;\n    })\n    .slice(0, maxOptions);\n};\n", "import React from 'react';\n\nimport { SelectableValue } from '@grafana/data';\nimport { SceneComponentProps, SceneObject, SceneObjectBase, SceneObjectState } from '@grafana/scenes';\nimport { Field, RadioButtonGroup } from '@grafana/ui';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../utils/analytics';\n\nexport interface LayoutSwitcherState extends SceneObjectState {\n  active: LayoutType;\n  layouts: SceneObject[];\n  options: Array<SelectableValue<LayoutType>>;\n}\n\nexport type LayoutType = 'single' | 'grid' | 'rows';\n\nexport class LayoutSwitcher extends SceneObjectBase<LayoutSwitcherState> {\n  public Selector({ model }: { model: LayoutSwitcher }) {\n    const { active, options } = model.useState();\n\n    return (\n      <Field label=\"View\">\n        <RadioButtonGroup options={options} value={active} onChange={model.onLayoutChange} />\n      </Field>\n    );\n  }\n\n  public onLayoutChange = (active: LayoutType) => {\n    this.setState({ active });\n    reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.layout_type_changed, {\n      layout: active,\n    });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<LayoutSwitcher>) => {\n    const { layouts, options, active } = model.useState();\n\n    const index = options.findIndex((o) => o.value === active);\n    if (index === -1) {\n      return null;\n    }\n\n    const layout = layouts[index];\n\n    return <layout.Component model={layout} />;\n  };\n}\n", "import { PanelBuilders } from '@grafana/scenes';\nimport { TooltipDisplayMode } from '@grafana/ui';\n\nexport const linesPanelConfig = () => {\n  return PanelBuilders.timeseries()\n    .setOption('legend', { showLegend: false })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi })\n    .setCustomFieldConfig('fillOpacity', 15);\n};\n", "import {\n  CustomVariable,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  SceneDataNode,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  VizPanelState,\n} from '@grafana/scenes';\nimport { LayoutSwitcher } from '../LayoutSwitcher';\nimport { explorationDS, GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { formatLabelValue, getLabelValue, getOpenTrace, getTraceExplorationScene } from '../../../utils/utils';\nimport { map, Observable } from 'rxjs';\nimport { DataFrame, PanelData, reduceField, ReducerID } from '@grafana/data';\nimport { generateMetricsQuery, metricByWithStatus } from '../queries/generateMetricsQuery';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { linesPanelConfig } from '../panels/linesPanel';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { syncYAxis } from '../behaviors/syncYaxis';\nimport { exemplarsTransformations } from '../../../utils/exemplars';\nimport { PanelMenu } from '../panels/PanelMenu';\n\nexport function buildNormalLayout(\n  scene: SceneObject,\n  variable: CustomVariable,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions']\n) {\n  const traceExploration = getTraceExplorationScene(scene);\n  const metric = traceExploration.getMetricVariable().getValue() as MetricFunction;\n  const query = metricByWithStatus(metric, variable.getValueText());\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  return new LayoutSwitcher({\n    $behaviors: [syncYAxis()],\n    $data: new SceneDataTransformer({\n      $data: new StepQueryRunner({\n        maxDataPoints: 64,\n        datasource: explorationDS,\n        queries: [query],\n      }),\n      transformations: [\n        ...exemplarsTransformations(getOpenTrace(scene)),\n        () => (source: Observable<DataFrame[]>) => {\n          return source.pipe(\n            map((data: DataFrame[]) => {\n              data.forEach((a) => reduceField({ field: a.fields[1], reducers: [ReducerID.max] }));\n              return data.sort((a, b) => {\n                return (b.fields[1].state?.calcs?.max || 0) - (a.fields[1].state?.calcs?.max || 0);\n              });\n            })\n          );\n        },\n      ],\n    }),\n    options: [\n      { value: 'single', label: 'Single' },\n      { value: 'grid', label: 'Grid' },\n      { value: 'rows', label: 'Rows' },\n    ],\n    active: 'grid',\n    layouts: [\n      new SceneFlexLayout({\n        direction: 'column',\n        children: [\n          new SceneFlexItem({\n            minHeight: 300,\n            body: (metric === 'duration' ? linesPanelConfig().setUnit('s') : linesPanelConfig()).build(),\n          }),\n        ],\n      }),\n      new ByFrameRepeater({\n        body: new SceneCSSGridLayout({\n          templateColumns: GRID_TEMPLATE_COLUMNS,\n          autoRows: '200px',\n          isLazy: true,\n          children: [],\n        }),\n        groupBy: true,\n        getLayoutChild: getLayoutChild(panels, getLabelValue, variable, metric, actionsFn),\n      }),\n      new ByFrameRepeater({\n        body: new SceneCSSGridLayout({\n          templateColumns: '1fr',\n          autoRows: '200px',\n          isLazy: true,\n          children: [],\n        }),\n        groupBy: true,\n        getLayoutChild: getLayoutChild(panels, getLabelValue, variable, metric, actionsFn),\n      }),\n    ],\n  });\n}\n\nexport function getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame, labelName: string) => string,\n  variable: CustomVariable,\n  metric: MetricFunction,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions']\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        annotations: data.annotations?.filter((a) => a.refId === frame.refId),\n        series: [\n          {\n            ...frame,\n            fields: frame.fields.sort((a, b) => a.labels?.status?.localeCompare(b.labels?.status || '') || 0),\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      existingGridItem.state.body?.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const query = sceneGraph.interpolate(\n      variable,\n      generateMetricsQuery({\n        metric,\n        extraFilters: `${variable.getValueText()}=${formatLabelValue(getLabelValue(frame))}`,\n      })\n    );\n\n    const panel = (metric === 'duration' ? linesPanelConfig().setUnit('s') : barsPanelConfig(metric))\n      .setTitle(getTitle(frame, variable.getValueText()))\n      .setMenu(new PanelMenu({ query, labelValue: getLabelValue(frame) }))\n      .setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: panel.build(),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n", "import { sceneGraph, SceneObject, SceneObjectState, VizPanel } from '@grafana/scenes';\nimport { cloneDeep, merge } from 'lodash';\nimport { EventTimeseriesDataReceived } from '../../../utils/shared';\n\nexport function syncYAxis() {\n  return (vizPanel: SceneObject<SceneObjectState>) => {\n    const maxima = new Map<string, number>();\n\n    const eventSub = vizPanel.subscribeToEvent(EventTimeseriesDataReceived, (event) => {\n      const series = event.payload.series;\n\n      series?.forEach((s) => {\n        s.fields.slice(1).forEach((f) => {\n          maxima.set(s.refId as string, Math.max(...f.values.filter((v) => v)));\n        })\n      });\n\n      updateTimeseriesAxis(vizPanel, Math.max(...maxima.values()));\n    });\n\n    return () => {\n      eventSub.unsubscribe();\n    };\n  };\n}\n\nfunction updateTimeseriesAxis(vizPanel: SceneObject, max: number) {\n  // findAllObjects searches down the full scene graph\n  const timeseries = sceneGraph.findAllObjects(vizPanel, (o) => o instanceof VizPanel) as VizPanel[];\n\n  for (const t of timeseries) {\n    t.clearFieldConfigCache(); // required\n\n    t.setState({\n      fieldConfig: merge(cloneDeep(t.state.fieldConfig), { defaults: { max } }),\n    });\n  }\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\ntype Tag = {\n  label: string;\n  color: string;\n};\n\ntype Props = {\n  description: string;\n  tags: Tag[];\n};\n\nexport function AttributesDescription({ description, tags }: Props) {\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n\n  return (\n    <div className={styles.infoFlex}>\n      <div className={styles.tagsFlex}>{description}</div>\n      {tags.length > 0 &&\n        tags.map((tag) => (\n          <div className={styles.tagsFlex} key={tag.label}>\n            <div className={styles.tag} style={{ backgroundColor: tag.color }} />\n            <div>{tag.label}</div>\n          </div>\n        ))}\n    </div>\n  );\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    infoFlex: css({\n      display: 'flex',\n      gap: theme.spacing(2),\n      alignItems: 'center',\n      padding: `${theme.spacing(1)} 0 ${theme.spacing(2)} 0`,\n    }),\n    tagsFlex: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n      alignItems: 'center',\n    }),\n    tag: css({\n      display: 'inline-block',\n      width: theme.spacing(2),\n      height: theme.spacing(0.5),\n      borderRadius: theme.spacing(0.5),\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport React, { useEffect, useState } from 'react';\n\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport {\n  CustomVariable,\n  SceneComponentProps,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { Field, RadioButtonGroup, useStyles2 } from '@grafana/ui';\n\nimport { GroupBySelector } from '../../../GroupBySelector';\nimport {\n  MetricFunction,\n  RESOURCE,\n  RESOURCE_ATTR,\n  SPAN,\n  SPAN_ATTR,\n  VAR_FILTERS,\n  VAR_METRIC,\n  radioAttributesResource,\n  radioAttributesSpan,\n} from '../../../../../utils/shared';\n\nimport { LayoutSwitcher } from '../../../LayoutSwitcher';\nimport { AddToFiltersAction } from '../../../actions/AddToFiltersAction';\nimport { buildNormalLayout } from '../../../layouts/attributeBreakdown';\nimport {\n  getAttributesAsOptions,\n  getGroupByVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../../../utils/analytics';\nimport { AttributesDescription } from './AttributesDescription';\n\nexport interface AttributesBreakdownSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class AttributesBreakdownScene extends SceneObjectBase<AttributesBreakdownSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_FILTERS, VAR_METRIC],\n    onReferencedVariableValueChanged: this.onReferencedVariableValueChanged.bind(this),\n  });\n\n  constructor(state: Partial<AttributesBreakdownSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const variable = getGroupByVariable(this);\n\n    variable.subscribeToState(() => {\n      this.setBody(variable);\n    });\n\n    getTraceByServiceScene(this).subscribeToState(() => {\n      this.setBody(variable);\n    });\n\n    this.setBody(variable);\n  }\n\n  private onReferencedVariableValueChanged() {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(radioAttributesResource[0]);\n    this.setBody(variable);\n  }\n\n  private onAddToFiltersClick(payload: any) {\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.breakdown_add_to_filters_clicked,\n      payload\n    );\n  }\n\n  private setBody = (variable: CustomVariable) => {\n    this.setState({\n      body: buildNormalLayout(this, variable, (frame: DataFrame) => [\n        new AddToFiltersAction({ frame, labelKey: variable.getValueText(), onClick: this.onAddToFiltersClick }),\n      ]),\n    });\n  };\n\n  public onChange = (value: string, ignore?: boolean) => {\n    const variable = getGroupByVariable(this);\n    if (variable.getValueText() !== value) {\n      variable.changeValueTo(value, undefined, !ignore);\n\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.breakdown_group_by_changed,\n        {\n          groupBy: value,\n        }\n      );\n    }\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AttributesBreakdownScene>) => {\n    const { value: groupByValue } = getGroupByVariable(model).useState();\n    const groupBy = groupByValue as string;\n    const defaultScope = groupBy.includes(SPAN_ATTR) || radioAttributesSpan.includes(groupBy) ? SPAN : RESOURCE;\n    const [scope, setScope] = useState(defaultScope);\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    const { attributes } = getTraceByServiceScene(model).useState();\n    const filterType = scope === RESOURCE ? RESOURCE_ATTR : SPAN_ATTR;\n    let filteredAttributes = attributes?.filter((attr) => attr.includes(filterType));\n    if (scope === SPAN) {\n      filteredAttributes = filteredAttributes?.concat(radioAttributesSpan);\n    }\n\n    const exploration = getTraceExplorationScene(model);\n    const { value: metric } = exploration.getMetricVariable().useState();\n    const getDescription = (metric: MetricFunction) => {\n      switch (metric) {\n        case 'rate':\n          return 'Attributes are ordered by their rate of requests per second.';\n        case 'errors':\n          return 'Attributes are ordered by their rate of errors per second.';\n        case 'duration':\n          return 'Attributes are ordered by their average duration.';\n        default:\n          throw new Error('Metric not supported');\n      }\n    };\n    const description = getDescription(metric as MetricFunction);\n\n    useEffect(() => {\n      if (scope !== defaultScope) {\n        setScope(defaultScope);\n      }\n    }, [groupBy]);\n\n    return (\n      <div className={styles.container}>\n        <AttributesDescription\n          description={description}\n          tags={\n            metric === 'duration'\n              ? []\n              : [\n                  { label: 'Rate', color: 'green' },\n                  { label: 'Error', color: 'red' },\n                ]\n          }\n        />\n\n        <div className={styles.controls}>\n          {filteredAttributes?.length && (\n            <div className={styles.controlsLeft}>\n              <div className={styles.scope}>\n                <Field label=\"Scope\">\n                  <RadioButtonGroup\n                    options={getAttributesAsOptions([RESOURCE, SPAN])}\n                    value={scope}\n                    onChange={setScope}\n                  />\n                </Field>\n              </div>\n\n              <div className={styles.groupBy}>\n                <GroupBySelector\n                  options={getAttributesAsOptions(filteredAttributes!)}\n                  radioAttributes={scope === RESOURCE ? radioAttributesResource : radioAttributesSpan}\n                  value={groupBy}\n                  onChange={model.onChange}\n                  model={model}\n                />\n              </div>\n            </div>\n          )}\n          {body instanceof LayoutSwitcher && (\n            <div className={styles.controlsRight}>\n              <body.Selector model={body} />\n            </div>\n          )}\n        </div>\n        <div className={styles.content}>{body && <body.Component model={body} />}</div>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    content: css({\n      flexGrow: 1,\n      display: 'flex',\n      paddingTop: theme.spacing(0),\n    }),\n    controls: css({\n      flexGrow: 0,\n      display: 'flex',\n      alignItems: 'top',\n      gap: theme.spacing(2),\n    }),\n    controlsRight: css({\n      flexGrow: 0,\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    scope: css({\n      marginRight: theme.spacing(2),\n    }),\n    groupBy: css({\n      width: '100%',\n    }),\n    controlsLeft: css({\n      display: 'flex',\n      justifyContent: 'flex-left',\n      justifyItems: 'left',\n      width: '100%',\n      flexDirection: 'row',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexItem,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { AttributesBreakdownScene } from './AttributesBreakdownScene';\nimport { VAR_METRIC } from '../../../../../utils/shared';\n\ninterface BreakdownSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class BreakdownScene extends SceneObjectBase<BreakdownSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_METRIC],\n  });\n\n  constructor(state: Partial<BreakdownSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new AttributesBreakdownScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<BreakdownScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildBreakdownScene() {\n  return new SceneFlexItem({\n    body: new BreakdownScene({}),\n  });\n}\n", "import { Field } from \"@grafana/data\";\nimport { calculateBucketSize } from \"utils/dates\";\n\nexport function aggregateExceptions(messageField: Field<string>, typeField?: Field<string>, timeField?: Field<any>, serviceField?: Field<string>) {\n  const occurrences = new Map<string, number>();\n  const types = new Map<string, string>();\n  const lastSeenTimes = new Map<string, number>();\n  const services = new Map<string, string>();\n  const timeSeries = new Map<string, Array<{time: number, count: number}>>();\n  \n  // Collect timestamps for each message\n  const messageTimestamps = new Map<string, number[]>();\n  \n  for (let i = 0; i < messageField.values.length; i++) {\n    const message = messageField.values[i];\n    const type = typeField?.values[i];\n    const timestamp = timeField?.values[i];\n    const service = serviceField?.values[i];\n    \n    if (message) {\n      const normalizedMessage = normalizeExceptionMessage(message);\n      occurrences.set(normalizedMessage, (occurrences.get(normalizedMessage) || 0) + 1);\n      \n      if (!types.has(normalizedMessage) && type) {\n        types.set(normalizedMessage, type);\n      }\n\n      if (!services.has(normalizedMessage) && service) {\n        services.set(normalizedMessage, service);\n      }\n\n      if (timestamp) {\n        const timestampMs = typeof timestamp === 'string' ? parseFloat(timestamp) : timestamp;\n        if (!messageTimestamps.has(normalizedMessage)) {\n          messageTimestamps.set(normalizedMessage, []);\n        }\n        messageTimestamps.get(normalizedMessage)!.push(timestampMs);\n                    \n        const currentLastSeen = lastSeenTimes.get(normalizedMessage) || 0;\n        if (timestampMs > currentLastSeen) {\n          lastSeenTimes.set(normalizedMessage, timestampMs);\n        }\n      }\n    }\n  }\n\n  // Create time series data for each message\n  for (const [message, timestamps] of messageTimestamps.entries()) {\n    const timeSeriesData = createTimeSeries(timestamps);\n    timeSeries.set(message, timeSeriesData);\n  }\n\n  const sortedEntries = Array.from(occurrences.entries()).sort((a, b) => b[1] - a[1]);\n\n  return {\n    messages: sortedEntries.map(([message]) => message),\n    types: sortedEntries.map(([message]) => types.get(message) || ''),\n    occurrences: sortedEntries.map(([, count]) => count),\n    services: sortedEntries.map(([message]) => services.get(message) || ''),\n    timeSeries: sortedEntries.map(([message]) => timeSeries.get(message) || []),\n    lastSeenTimes: sortedEntries.map(([message]) => {\n      const lastSeenMs = lastSeenTimes.get(message);\n      \n      if (!lastSeenMs) {\n        return '';\n      }\n      \n      const now = Date.now();\n      const diffMs = now - lastSeenMs;\n      \n      if (diffMs < 60000) { // Less than 1 minute\n        return 'Just now';\n      } else if (diffMs < 3600000) { // Less than 1 hour\n        const minutes = Math.floor(diffMs / 60000);\n        return `${minutes}m ago`;\n      } else if (diffMs < 86400000) { // Less than 1 day\n        const hours = Math.floor(diffMs / 3600000);\n        return `${hours}h ago`;\n      } else { // More than 1 day\n        const days = Math.floor(diffMs / 86400000);\n        return `${days}d ago`;\n      }\n    }),\n  };\n}\n\nexport function createTimeSeries(timestamps: number[]): Array<{time: number, count: number}> {\n  if (!timestamps.length) {return [];}\n  \n  timestamps.sort((a, b) => a - b);\n  \n  const timeRangeMs = timestamps[timestamps.length - 1] - timestamps[0];\n  const timeRangeSeconds = timeRangeMs / 1000;\n  const bucketSizeSeconds = calculateBucketSize(timeRangeSeconds, 50);\n  const bucketSizeMs = bucketSizeSeconds * 1000; // Convert back to milliseconds\n  const buckets = new Map<number, number>();\n  \n  for (const timestamp of timestamps) {\n    const bucketKey = Math.floor(timestamp / bucketSizeMs) * bucketSizeMs;\n    buckets.set(bucketKey, (buckets.get(bucketKey) || 0) + 1);\n  }\n  \n  // Convert to array and sort by time\n  return Array.from(buckets.entries())\n    .map(([time, count]) => ({ time, count }))\n    .sort((a, b) => a.time - b.time);\n}\n\nexport function normalizeExceptionMessage(message: string): string {\n  if (!message) { return '' }\n  return message.replace(/\\s+/g, ' ').trim();\n}\n", "import React from 'react';\n\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport { DataFrame, GrafanaTheme2, LoadingState, PanelData, FieldType, DataLink } from '@grafana/data';\nimport { GraphDrawStyle, VisibilityMode, TableCellHeight } from '@grafana/schema';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { css } from '@emotion/css';\nimport Skeleton from 'react-loading-skeleton';\nimport { useStyles2, useTheme2, TableCellDisplayMode, TableCustomCellOptions, Sparkline } from '@grafana/ui';\nimport { map, Observable } from 'rxjs';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n  explorationDS,\n  filterStreamingProgressTransformations,\n} from '../../../../../utils/shared';\nimport { getTraceByServiceScene, getFiltersVariable } from '../../../../../utils/utils';\nimport { buildExceptionsQuery } from 'components/Explore/queries/exceptions';\nimport { aggregateExceptions } from './ExceptionUtils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\n\nexport interface ExceptionsSceneState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  dataState: 'empty' | 'loading' | 'done';\n  exceptionsCount?: number;\n}\n\nexport class ExceptionsScene extends SceneObjectBase<ExceptionsSceneState> {\n  constructor(state: Partial<ExceptionsSceneState>) {\n    super({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildExceptionsQuery()],\n        }),\n        transformations: [], // Will be set after construction\n      }),\n      dataState: 'empty',\n      ...state,\n    });\n\n    const dataTransformer = this.state.$data as SceneDataTransformer;\n    dataTransformer.setState({\n      transformations: [...filterStreamingProgressTransformations, this.createTransformation()],\n    });\n\n    this.addActivationHandler(() => {\n      const dataTransformer = this.state.$data as SceneDataTransformer;\n\n      this._subs.add(\n        dataTransformer.subscribeToState((newState, prevState) => {\n          if (newState.data !== prevState.data) {\n            this.updatePanel(newState.data);\n          }\n        })\n      );\n    });\n  }\n\n  private updatePanel(data?: PanelData) {\n    if (\n      data?.state === LoadingState.Loading ||\n      data?.state === LoadingState.NotStarted ||\n      !data?.state ||\n      (data?.state === LoadingState.Streaming && !data.series?.[0]?.length)\n    ) {\n      this.setState({\n        dataState: 'loading',\n        panel: new SceneFlexLayout({\n          direction: 'row',\n          children: [\n            new LoadingStateScene({\n              component: SkeletonComponent,\n            }),\n          ],\n        }),\n      });\n    } else if (\n      (data?.state === LoadingState.Done || data?.state === LoadingState.Streaming) &&\n      (data.series.length === 0 || !data.series?.[0]?.length)\n    ) {\n      this.setState({\n        dataState: 'empty',\n        exceptionsCount: 0,\n        panel: new SceneFlexLayout({\n          children: [\n            new SceneFlexItem({\n              body: new EmptyStateScene({\n                message: EMPTY_STATE_ERROR_MESSAGE,\n                remedyMessage: EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n                padding: '32px',\n              }),\n            }),\n          ],\n        }),\n      });\n    } else if (\n      (data?.state === LoadingState.Done || data?.state === LoadingState.Streaming) &&\n      data.series.length > 0\n    ) {\n      const exceptionsCount = this.calculateExceptionsCount(data);\n\n      this.setState({\n        dataState: 'done',\n        exceptionsCount,\n        panel: new SceneFlexLayout({\n          children: [\n            new SceneFlexItem({\n              body: PanelBuilders.table()\n                .setOption('cellHeight', TableCellHeight.Lg)\n                .setHoverHeader(true)\n                .setOverrides((builder) => {\n                  return builder\n                    .matchFieldsWithName('Service')\n                    .overrideCustomFieldConfig('width', 200)\n                    .matchFieldsWithName('Occurrences')\n                    .overrideCustomFieldConfig('width', 120)\n                    .matchFieldsWithName('Time Series')\n                    .overrideCustomFieldConfig('width', 220)\n                    .matchFieldsWithName('Last Seen')\n                    .overrideCustomFieldConfig('width', 120);\n                })\n                .build(),\n            }),\n          ],\n        }),\n      });\n    }\n  }\n\n  private createTransformation() {\n    return () => (source: Observable<DataFrame[]>) => {\n      return source.pipe(\n        map((data: DataFrame[]) => {\n          return data.map((df: DataFrame) => {\n            const messageField = df.fields.find((f) => f.name === 'exception.message');\n            const typeField = df.fields.find((f) => f.name === 'exception.type');\n            const serviceField = df.fields.find((f) => f.name === 'service.name');\n            const timeField = df.fields.find((f) => f.name === 'time');\n            const noData = !messageField || !messageField.values.length;\n\n            let messages: string[] = [];\n            let types: string[] = [];\n            let occurrences: number[] = [];\n            let lastSeenTimes: string[] = [];\n            let services: string[] = [];\n            let timeSeries: Array<Array<{ time: number; count: number }>> = [];\n\n            if (!noData) {\n              const aggregated = aggregateExceptions(messageField, typeField, timeField, serviceField);\n              messages = aggregated.messages;\n              types = aggregated.types;\n              occurrences = aggregated.occurrences;\n              lastSeenTimes = aggregated.lastSeenTimes;\n              services = aggregated.services;\n              timeSeries = aggregated.timeSeries;\n            }\n\n            const options: TableCustomCellOptions = {\n              type: TableCellDisplayMode.Custom,\n              cellComponent: (props) => {\n                const seriesData = props.value as Array<{ time: number; count: number }>;\n                return this.renderSparklineCell(seriesData);\n              },\n            };\n\n            return {\n              ...df,\n              length: messages.length,\n              fields: [\n                {\n                  name: 'Message',\n                  type: FieldType.string,\n                  values: messages,\n                  config: {\n                    links: messages.length > 0 ? [this.createDataLink()] : [],\n                  },\n                },\n                {\n                  name: 'Type',\n                  type: FieldType.string,\n                  values: types,\n                  config: {},\n                },\n                {\n                  name: 'Trace Service',\n                  type: FieldType.string,\n                  values: services,\n                  config: {},\n                },\n                {\n                  name: 'Occurrences',\n                  type: FieldType.number,\n                  values: occurrences,\n                  config: {},\n                },\n                {\n                  name: 'Frequency',\n                  type: FieldType.other,\n                  values: timeSeries,\n                  config: {\n                    custom: {\n                      cellOptions: options,\n                    },\n                  },\n                },\n                {\n                  name: 'Last Seen',\n                  type: FieldType.string,\n                  values: lastSeenTimes,\n                  config: {},\n                },\n              ],\n            };\n          });\n        })\n      );\n    };\n  }\n\n  private renderSparklineCell = (seriesData: Array<{ time: number; count: number }>) => {\n    const styles = useStyles2(getStyles);\n\n    const SparklineCell = () => {\n      const theme = useTheme2();\n\n      if (!seriesData || !seriesData.length) {\n        return <div className={styles.sparklineMessage}>No data</div>;\n      }\n\n      const countValues = seriesData.map((point) => point.count);\n      const timeValues = seriesData.map((point) => point.time);\n\n      const validCountValues = countValues.filter((v) => isFinite(v) && !isNaN(v));\n      const validTimeValues = timeValues.filter((v) => isFinite(v) && !isNaN(v));\n      if (validCountValues.length < 2 || validTimeValues.length < 2) {\n        return <div className={styles.sparklineMessage}>Not enough data</div>;\n      }\n\n      const minCount = Math.min(...validCountValues);\n      const maxCount = Math.max(...validCountValues);\n      const minTime = Math.min(...validTimeValues);\n      const maxTime = Math.max(...validTimeValues);\n\n      // Ensure valid ranges\n      const countDelta = maxCount - minCount;\n      const timeDelta = maxTime - minTime;\n\n      // Handle edge cases where all values are the same\n      const safeCountDelta = countDelta === 0 ? 1 : countDelta;\n      const safeTimeDelta = timeDelta === 0 ? 1 : timeDelta;\n\n      const sparklineData = {\n        y: {\n          name: 'count',\n          type: FieldType.number,\n          values: validCountValues,\n          config: {},\n          state: {\n            range: {\n              min: minCount,\n              max: maxCount,\n              delta: safeCountDelta,\n            },\n          },\n        },\n        x: {\n          name: 'time',\n          type: FieldType.time,\n          values: validTimeValues,\n          config: {},\n          state: {\n            range: {\n              min: minTime,\n              max: maxTime,\n              delta: safeTimeDelta,\n            },\n          },\n        },\n      };\n\n      return (\n        <div className={styles.sparklineContainer}>\n          <Sparkline\n            width={180}\n            height={20}\n            sparkline={sparklineData}\n            theme={theme}\n            config={{\n              custom: {\n                drawStyle: GraphDrawStyle.Line,\n                fillOpacity: 5,\n                fillColor: theme.colors.background.secondary,\n                lineWidth: 1,\n                showPoints: VisibilityMode.Never,\n              },\n            }}\n          />\n        </div>\n      );\n    };\n\n    return <SparklineCell />;\n  };\n\n  private createDataLink(): DataLink {\n    return {\n      title: 'View traces for this exception',\n      url: '',\n      onClick: (event: any) => {\n        const rowIndex = event.origin?.rowIndex;\n        if (rowIndex !== undefined) {\n          const message = event.origin?.field?.values?.[rowIndex];\n          if (message) {\n            reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.exception_message_clicked);\n            this.navigateToTracesWithFilter(message);\n          }\n        }\n      },\n    };\n  }\n\n  private navigateToTracesWithFilter = (exceptionMessage: string) => {\n    const filtersVariable = getFiltersVariable(this);\n    if (!filtersVariable) {\n      return;\n    }\n\n    const traceByServiceScene = getTraceByServiceScene(this);\n    traceByServiceScene?.setActionView('traceList');\n\n    const currentFilters = filtersVariable.state.filters || [];\n    const escapedMessage = this.escapeFilterValue(exceptionMessage);\n\n    const existingFilterIndex = currentFilters.findIndex((filter) => filter.key === 'event.exception.message');\n\n    const newFilter = {\n      key: 'event.exception.message',\n      operator: '=',\n      value: escapedMessage,\n    };\n\n    const newFilters =\n      existingFilterIndex >= 0\n        ? currentFilters.map((f, i) => (i === existingFilterIndex ? newFilter : f))\n        : [...currentFilters, newFilter];\n\n    filtersVariable.setState({ filters: newFilters });\n  };\n\n  private escapeFilterValue(value: string): string {\n    return value\n      .replace(/[\\n\\r\\t]/g, ' ')\n      .replace(/\\s+/g, ' ')\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\"/g, '\\\"')\n      .trim();\n  }\n\n  private calculateExceptionsCount(data?: PanelData): number {\n    if (!data?.series?.[0]) {\n      return 0;\n    }\n\n    const occurrencesField = data.series[0].fields.find((field) => field.name === 'Occurrences');\n    if (!occurrencesField?.values) {\n      return 0;\n    }\n\n    return occurrencesField.values.reduce((total: number, value: number) => total + (value || 0), 0);\n  }\n\n  public getExceptionsCount(): number {\n    return this.state.exceptionsCount || 0;\n  }\n\n  public static Component = ({ model }: SceneComponentProps<ExceptionsScene>) => {\n    const styles = useStyles2(getStyles);\n    const theme = useTheme2();\n    const { panel, dataState } = model.useState();\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.description}>\n          View exception details from errored traces for the current set of filters.\n        </div>\n        {dataState === 'loading' && (\n          <div className={styles.loadingContainer}>\n            <Skeleton\n              count={10}\n              height={40}\n              baseColor={theme.colors.background.secondary}\n              highlightColor={theme.colors.background.primary}\n            />\n          </div>\n        )}\n        {panel && <panel.Component model={panel} />}\n      </div>\n    );\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      gap: theme.spacing(2),\n      height: '100%',\n    }),\n    description: css({\n      fontSize: theme.typography.h6.fontSize,\n      padding: `${theme.spacing(1)} 0`,\n    }),\n    loadingContainer: css({\n      padding: theme.spacing(2),\n    }),\n    sparklineContainer: css({\n      width: '200px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n    }),\n    sparklineMessage: css({\n      fontSize: theme.typography.bodySmall.fontSize,\n      color: theme.colors.text.secondary,\n      padding: theme.spacing(1),\n    }),\n  };\n};\n\nconst SkeletonComponent = () => {\n  const styles = useStyles2(getSkeletonStyles);\n  const theme = useTheme2();\n\n  return (\n    <div className={styles.container}>\n      <Skeleton\n        count={10}\n        height={40}\n        baseColor={theme.colors.background.secondary}\n        highlightColor={theme.colors.background.primary}\n      />\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: theme.spacing(2),\n    }),\n  };\n}\n\nexport function buildExceptionsScene() {\n  return new SceneFlexItem({\n    body: new ExceptionsScene({}),\n  });\n}\n", "import { VAR_FILTERS_EXPR } from 'utils/shared';\n\nexport function buildExceptionsQuery() {\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR} && status = error} | select(resource.service.name, event.exception.message,event.exception.stacktrace,event.exception.type) with(most_recent=true)`,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 400,\n    spss: 10,\n    filters: [],\n  };\n}\n", "import { ComparisonSelection } from '../../../utils/shared';\n\nexport function comparisonQuery(selection?: ComparisonSelection) {\n  let selector = '';\n\n  if (!selection) {\n    return '{}';\n  }\n\n  if (selection.query) {\n    selector += selection.query;\n  }\n\n  const duration = [];\n  if (selection.duration?.from.length) {\n    duration.push(`duration >= ${selection.duration.from}`);\n  }\n  if (selection.duration?.to.length) {\n    duration.push(`duration <= ${selection.duration.to}`);\n  }\n  if (duration.length) {\n    if (selector.length) {\n      selector += ' && ';\n    }\n    selector += duration.join(' && ');\n  }\n\n  const fromTimerange = selection.timeRange?.from;\n  const toTimerange = selection.timeRange?.to;\n  return `{${selector}}, 10${\n    fromTimerange && toTimerange ? `, ${fromTimerange * 1000000000}, ${toTimerange * 1000000000}` : ``\n  }`;\n}\n", "import {\n  CustomVariable,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  SceneDataNode,\n  SceneDataTransformer,\n  sceneGraph,\n  SceneObject,\n  VizPanelState,\n} from '@grafana/scenes';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { map, Observable } from 'rxjs';\nimport { DataFrame, FieldType, LoadingState, PanelData, reduceField, ReducerID } from '@grafana/data';\nimport { getPanelConfig } from './allComparison';\nimport { GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\n\nexport function buildAttributeComparison(\n  scene: SceneObject,\n  variable: CustomVariable,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  const timeRange = sceneGraph.getTimeRange(scene);\n  const data = sceneGraph.getData(scene);\n  const attribute = variable.getValueText();\n  const attributeSeries = data.state.data?.series.find((d) => d.name === attribute);\n  const splitFrames: DataFrame[] = [];\n  const nameField = attributeSeries?.fields.find((f) => f.name === 'Value');\n  const baselineField = attributeSeries?.fields.find((f) => f.name === 'Baseline');\n  const selectionField = attributeSeries?.fields.find((f) => f.name === 'Selection');\n\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  if (nameField && baselineField && selectionField) {\n    for (let i = 0; i < nameField.values.length; i++) {\n      if (!nameField.values[i] || (!baselineField.values[i] && !selectionField.values[i])) {\n        continue;\n      }\n\n      splitFrames.push({\n        name: nameField.values[i].replace(/\"/g, ''),\n        length: 1,\n        fields: [\n          {\n            name: 'Value',\n            type: FieldType.string,\n            values: ['Baseline', 'Comparison'],\n            config: {},\n          },\n          {\n            ...baselineField,\n            values: [baselineField.values[i]],\n            labels: {\n              [attribute]: nameField.values[i],\n            },\n            config: {\n              displayName: 'Baseline',\n            },\n          },\n          {\n            ...selectionField,\n            values: [selectionField.values[i]],\n          },\n        ],\n      });\n    }\n  }\n\n  return new ByFrameRepeater({\n    $data: new SceneDataTransformer({\n      $data: new SceneDataNode({\n        data: {\n          timeRange: timeRange.state.value,\n          state: LoadingState.Done,\n          series: splitFrames,\n        },\n      }),\n      transformations: [\n        () => (source: Observable<DataFrame[]>) => {\n          return source.pipe(\n            map((data: DataFrame[]) => {\n              data.forEach((a) => reduceField({ field: a.fields[2], reducers: [ReducerID.max] }));\n              return data.sort((a, b) => {\n                return (b.fields[2].state?.calcs?.max || 0) - (a.fields[2].state?.calcs?.max || 0);\n              });\n            })\n          );\n        },\n      ],\n    }),\n    body: new SceneCSSGridLayout({\n      templateColumns: GRID_TEMPLATE_COLUMNS,\n      autoRows: '200px',\n      isLazy: true,\n      children: [],\n    }),\n    getLayoutChild: getLayoutChild(panels, getLabel, actionsFn, metric),\n  });\n}\n\nconst getLabel = (df: DataFrame) => {\n  return df.name || 'No name available';\n};\n\nfunction getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame) => string,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        series: [\n          {\n            ...frame,\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      existingGridItem.state.body?.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const panel = getPanelConfig(metric).setTitle(getTitle(frame)).setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: panel.build(),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneObjectState,\n  SceneObjectBase,\n  SceneComponentProps,\n} from '@grafana/scenes';\nimport { Button } from '@grafana/ui';\n\ninterface InspectAttributeActionState extends SceneObjectState {\n  attribute?: string;\n  onClick: () => void;\n}\n\nexport class InspectAttributeAction extends SceneObjectBase<InspectAttributeActionState> {\n  public static Component = ({ model }: SceneComponentProps<InspectAttributeAction>) => {\n    if (!model.state.attribute) {\n      return null;\n    }\n\n    return (\n      <Button variant=\"secondary\" size=\"sm\" fill=\"solid\" onClick={() => model.state.onClick()}>\n        Inspect\n      </Button>\n    );\n  };\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { DataFrame, FieldType, GrafanaTheme2, Field } from '@grafana/data';\nimport {\n  CustomVariable,\n  SceneComponentProps,\n  SceneDataTransformer,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n  VariableDependencyConfig,\n  VariableValue,\n} from '@grafana/scenes';\nimport { getTheme, useStyles2 } from '@grafana/ui';\n\nimport { GroupBySelector } from '../../../GroupBySelector';\nimport { VAR_FILTERS, VAR_PRIMARY_SIGNAL, explorationDS, VAR_FILTERS_EXPR, ALL, radioAttributesSpan } from '../../../../../utils/shared';\n\nimport { LayoutSwitcher } from '../../../LayoutSwitcher';\nimport { AddToFiltersAction } from '../../../actions/AddToFiltersAction';\nimport { map, Observable } from 'rxjs';\nimport { BaselineColor, buildAllComparisonLayout, SelectionColor } from '../../../layouts/allComparison';\n// eslint-disable-next-line no-restricted-imports\nimport { duration } from 'moment';\nimport { comparisonQuery } from '../../../queries/comparisonQuery';\nimport { buildAttributeComparison } from '../../../layouts/attributeComparison';\nimport {\n  getAttributesAsOptions,\n  getGroupByVariable,\n  getPrimarySignalVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { InspectAttributeAction } from 'components/Explore/actions/InspectAttributeAction';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../../../utils/analytics';\nimport { computeHighestDifference } from '../../../../../utils/comparison';\nimport { AttributesDescription } from '../Breakdown/AttributesDescription';\nimport { isEqual } from 'lodash';\n\nexport interface AttributesComparisonSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class AttributesComparisonScene extends SceneObjectBase<AttributesComparisonSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_FILTERS, VAR_PRIMARY_SIGNAL],\n    onReferencedVariableValueChanged: this.onReferencedVariableValueChanged.bind(this),\n  });\n\n  constructor(state: Partial<AttributesComparisonSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const variable = getGroupByVariable(this);\n\n    variable.changeValueTo(ALL);\n\n    this.updateData();\n\n    variable.subscribeToState((newState, prevState) => {\n      if (newState.value !== prevState.value) {\n        this.setBody(variable);\n      }\n    });\n\n    getPrimarySignalVariable(this).subscribeToState(() => {\n      this.updateData();\n      this.setBody(variable);\n    });\n\n    getTraceByServiceScene(this).subscribeToState((newState, prevState) => {\n      if (!isEqual(newState.selection, prevState.selection)) {\n        this.updateData();\n        this.setBody(variable);\n      }\n    });\n\n    sceneGraph.getTimeRange(this).subscribeToState(() => {\n      this.updateData();\n    });\n\n    this.setBody(variable);\n  }\n\n  private getFilteredAttributes = (primarySignal: VariableValue): string[] => {\n    return primarySignal === 'nestedSetParent<0' ? ['rootName', 'rootServiceName'] : [];\n  };\n\n  private updateData() {\n    const byServiceScene = getTraceByServiceScene(this);\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    const from = sceneTimeRange.state.value.from.unix();\n    const to = sceneTimeRange.state.value.to.unix();\n    const primarySignal = getPrimarySignalVariable(this).state.value;\n    const filteredAttributes = this.getFilteredAttributes(primarySignal);\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(from, to, comparisonQuery(byServiceScene.state.selection))],\n        }),\n        transformations: [\n          () => (source: Observable<DataFrame[]>) => {\n            return source.pipe(\n              map((data: DataFrame[]) => {\n                const groupedFrames = groupFrameListByAttribute(data);\n                return Object.entries(groupedFrames)\n                  .filter(([attribute, _]) => !filteredAttributes.includes(attribute))\n                  .map(([attribute, frames]) => frameGroupToDataframe(attribute, frames))\n                  .sort((a, b) => {\n                    const aCompare = computeHighestDifference(a);\n                    const bCompare = computeHighestDifference(b);\n                    return Math.abs(bCompare.maxDifference) - Math.abs(aCompare.maxDifference);\n                  });\n              })\n            );\n          },\n        ],\n      }),\n    });\n  }\n\n  private onReferencedVariableValueChanged() {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(ALL);\n    this.setBody(variable);\n  }\n\n  private onAddToFiltersClick(payload: any) {\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.comparison_add_to_filters_clicked,\n      payload\n    );\n  }\n\n  private setBody = (variable: CustomVariable) => {\n    const traceExploration = getTraceExplorationScene(this);\n    this.setState({\n      body:\n        variable.hasAllValue() || variable.getValue() === ALL\n          ? buildAllComparisonLayout(\n              (frame) =>\n                new InspectAttributeAction({\n                  attribute: frame.name,\n                  onClick: () => this.onChange(frame.name || ''),\n                }),\n              traceExploration.getMetricFunction()\n            )\n          : buildAttributeComparison(\n              this,\n              variable,\n              (frame: DataFrame) => [\n                new AddToFiltersAction({\n                  frame,\n                  labelKey: variable.getValueText(),\n                  onClick: this.onAddToFiltersClick,\n                }),\n              ],\n              traceExploration.getMetricFunction()\n            ),\n    });\n  };\n\n  public onChange = (value: string, ignore?: boolean) => {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(value, undefined, !ignore);\n\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.select_attribute_in_comparison_clicked,\n      { value }\n    );\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AttributesComparisonScene>) => {\n    const { body } = model.useState();\n    const variable = getGroupByVariable(model);\n    const traceExploration = getTraceExplorationScene(model);\n    const { attributes } = getTraceByServiceScene(model).useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <AttributesDescription\n          description=\"Attributes are ordered by the difference between the baseline and selection values for each value.\"\n          tags={[\n            {\n              label: 'Baseline',\n              color:\n                traceExploration.getMetricFunction() === 'duration'\n                  ? BaselineColor\n                  : getTheme().visualization.getColorByName('semi-dark-green'),\n            },\n            {\n              label: 'Selection',\n              color:\n                traceExploration.getMetricFunction() === 'duration'\n                  ? SelectionColor\n                  : getTheme().visualization.getColorByName('semi-dark-red'),\n            },\n          ]}\n        />\n\n        <div className={styles.controls}>\n          {attributes?.length && (\n            <div className={styles.controlsLeft}>\n              <GroupBySelector\n                options={getAttributesAsOptions(attributes)}\n                radioAttributes={radioAttributesSpan}\n                value={variable.getValueText()}\n                onChange={model.onChange}\n                showAll={true}\n                model={model}\n              />\n            </div>\n          )}\n          {body instanceof LayoutSwitcher && (\n            <div className={styles.controlsRight}>\n              <body.Selector model={body} />\n            </div>\n          )}\n        </div>\n        <div className={styles.content}>{body && <body.Component model={body} />}</div>\n      </div>\n    );\n  };\n}\n\nexport function buildQuery(from: number, to: number, compareQuery: string) {\n  const dur = duration(to - from, 's');\n  const durString = `${dur.asSeconds()}s`;\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}} | compare(${compareQuery})`,\n    step: durString,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 100,\n    spss: 10,\n    filters: [],\n  };\n}\n\nconst groupFrameListByAttribute = (frames: DataFrame[]) => {\n  return frames.reduce((acc: Record<string, DataFrame[]>, series) => {\n    const numberField = series.fields.find((field) => field.type === 'number');\n    const nonInternalKey = Object.keys(numberField?.labels || {}).find((key) => !key.startsWith('__'));\n    if (nonInternalKey) {\n      acc[nonInternalKey] = [...(acc[nonInternalKey] || []), series];\n    }\n    return acc;\n  }, {});\n};\n\nconst frameGroupToDataframe = (attribute: string, frames: DataFrame[]): DataFrame => {\n  const newFrame: DataFrame = {\n    name: attribute,\n    refId: attribute,\n    fields: [],\n    length: 0,\n  };\n\n  const valueNameField: Field = {\n    name: 'Value',\n    type: FieldType.string,\n    values: [],\n    config: {},\n    labels: { [attribute]: attribute },\n  };\n  const baselineField: Field = {\n    name: 'Baseline',\n    type: FieldType.number,\n    values: [],\n    config: {},\n  };\n  const selectionField: Field = {\n    name: 'Selection',\n    type: FieldType.number,\n    values: [],\n    config: {},\n  };\n\n  const values = frames.reduce((acc: Record<string, Field[]>, frame) => {\n    const numberField = frame.fields.find((field) => field.type === 'number');\n    const val = numberField?.labels?.[attribute];\n    if (val) {\n      acc[val] = [...(acc[val] || []), numberField];\n    }\n    return acc;\n  }, {});\n\n  const baselineTotal = getTotalForMetaType(frames, 'baseline', values);\n  const selectionTotal = getTotalForMetaType(frames, 'selection', values);\n\n  newFrame.length = Object.keys(values).length;\n\n  Object.entries(values).forEach(([value, fields]) => {\n    valueNameField.values.push(value);\n    baselineField.values.push(\n      fields.find((field) => field.labels?.['__meta_type'] === '\"baseline\"')?.values[0] / baselineTotal\n    );\n    selectionField.values.push(\n      fields.find((field) => field.labels?.['__meta_type'] === '\"selection\"')?.values[0] / selectionTotal\n    );\n  });\n  newFrame.fields = [valueNameField, baselineField, selectionField];\n  return newFrame;\n};\n\nfunction getTotalForMetaType(frames: DataFrame[], metaType: string, values: Record<string, Field[]>) {\n  // calculate total from values so that we are properly normalizing the field values when dividing by the total\n  const calculatedTotal = Object.values(values).reduce((total, fields) => {\n    const field = fields.find((field) => field.labels?.['__meta_type'] === `\"${metaType}\"`);\n    return total + (field?.values[0] || 0);\n  }, 0);\n\n  let total = frames.reduce((currentValue, frame) => {\n    const field = frame.fields.find((f) => f.type === 'number');\n    if (field?.labels?.['__meta_type'] === `\"${metaType}_total\"`) {\n      return field.values[0];\n    }\n    return currentValue;\n  }, 1);\n\n  // if the baseline_total or selection_total field is found, but the total value is less than the calculated total\n  // we need to return the calculated total otherwise the values will be skewed\n  // e.g. calculatedTotal = 100, total = 80\n  // if we return the total, the field values will be normalized via 80/100 = 1.25 (incorrect)\n  // if we return the calculated total, the field values will be normalized via 100/100 = 1 (correct)\n  if (total < calculatedTotal) {\n    return calculatedTotal === 0 ? 1 : calculatedTotal; // fallback to 1 to avoid division by zero\n  }\n\n  // 1 if the baseline_total or selection_total field is not found\n  // 0 if the baseline_total or selection_total field is found, but the total value is 0\n  if (total === 1 || total === 0) {\n    return calculatedTotal === 0 ? 1 : calculatedTotal;\n  }\n\n  return total;\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    content: css({\n      flexGrow: 1,\n      display: 'flex',\n      paddingTop: theme.spacing(0),\n    }),\n    controls: css({\n      flexGrow: 0,\n      display: 'flex',\n      alignItems: 'top',\n      gap: theme.spacing(2),\n    }),\n    controlsRight: css({\n      flexGrow: 0,\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    controlsLeft: css({\n      display: 'flex',\n      justifyContent: 'flex-left',\n      justifyItems: 'left',\n      width: '100%',\n      flexDirection: 'column',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexItem,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { AttributesComparisonScene } from './AttributesComparisonScene';\nimport { MetricFunction, VAR_METRIC } from '../../../../../utils/shared';\nimport { getMetricVariable, getTraceByServiceScene } from '../../../../../utils/utils';\nimport { getDefaultSelectionForMetric } from '../../../../../utils/comparison';\n\ninterface ComparisonSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class ComparisonScene extends SceneObjectBase<ComparisonSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_METRIC],\n  });\n\n  constructor(state: Partial<ComparisonSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const metricVar = getMetricVariable(this);\n    const metric = metricVar.getValue() as MetricFunction;\n\n    const tracesByService = getTraceByServiceScene(this);\n    if (!tracesByService.state.selection) {\n      const selection = getDefaultSelectionForMetric(metric);\n      if (selection) {\n        tracesByService.setState({ selection });\n      }\n    }\n\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new AttributesComparisonScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<ComparisonScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildComparisonScene() {\n  return new SceneFlexItem({\n    body: new ComparisonScene({}),\n  });\n}\n", "import { css } from '@emotion/css';\nimport { SceneObjectBase, SceneComponentProps, SceneObject, sceneGraph, SceneObjectState } from '@grafana/scenes';\nimport { GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { useStyles2, Box, Stack, TabsBar, Tab } from '@grafana/ui';\nimport React, { useEffect, useState } from 'react';\nimport {\n  getTraceExplorationScene,\n  getTraceByServiceScene,\n  getExceptionsScene,\n  getFiltersVariable,\n  getPrimarySignalVariable,\n} from 'utils/utils';\nimport { ShareExplorationAction } from '../../actions/ShareExplorationAction';\nimport { buildSpansScene } from './Spans/SpansScene';\nimport { buildStructureScene } from './Structure/StructureScene';\nimport { buildBreakdownScene } from './Breakdown/BreakdownScene';\nimport { buildExceptionsScene } from './Exceptions/ExceptionsScene';\nimport { MetricFunction } from 'utils/shared';\nimport { buildComparisonScene } from './Comparison/ComparisonScene';\nimport { useMount } from 'react-use';\nimport { ActionViewType } from 'exposedComponents/types';\n\ninterface ActionViewDefinition {\n  displayName: (metric: MetricFunction) => string;\n  value: ActionViewType;\n  getScene: (metric: MetricFunction) => SceneObject;\n}\n\nexport const actionViewsDefinitions: ActionViewDefinition[] = [\n  { displayName: breakdownDisplayName, value: 'breakdown', getScene: buildBreakdownScene },\n  { displayName: structureDisplayName, value: 'structure', getScene: buildStructureScene },\n  { displayName: comparisonDisplayName, value: 'comparison', getScene: buildComparisonScene },\n  { displayName: exceptionsDisplayName, value: 'exceptions', getScene: buildExceptionsScene },\n  {\n    displayName: tracesDisplayName,\n    value: 'traceList',\n    getScene: buildSpansScene,\n  },\n];\n\nexport interface TabsBarSceneState extends SceneObjectState {}\n\nexport class TabsBarScene extends SceneObjectBase<TabsBarSceneState> {\n  public static Component = ({ model }: SceneComponentProps<TabsBarScene>) => {\n    const styles = useStyles2(getStyles);\n    const [exceptionsCount, setExceptionsCount] = useState(0);\n\n    const metricScene = getTraceByServiceScene(model);\n    const exploration = getTraceExplorationScene(model);\n\n    const { actionView } = metricScene.useState();\n    const { value: metric } = exploration.getMetricVariable().useState();\n    const { allowedActionViews } = exploration.useState();\n    const dataState = sceneGraph.getData(model).useState();\n    const tracesCount = dataState.data?.series?.[0]?.length;\n\n    const enabledViews = actionViewsDefinitions.filter((view) => {\n      if (view.value === 'exceptions' && metric !== 'errors') {\n        return false;\n      }\n      // If allowedActionViews is defined and has items, use it for filtering\n      // Otherwise, include all views (except exceptions when metric is not errors, handled above)\n      return !allowedActionViews?.length || allowedActionViews.includes(view.value);\n    });\n\n    // Get state variables that affect exceptions data\n    const filtersVariable = getFiltersVariable(model);\n    const primarySignalVariable = getPrimarySignalVariable(model);\n    const timeRange = sceneGraph.getTimeRange(model);\n    const { filters } = filtersVariable.useState();\n    const { value: primarySignal } = primarySignalVariable.useState();\n    const { value: timeRangeValue } = timeRange.useState();\n\n    useEffect(() => {\n      if (metric !== 'errors') {\n        setExceptionsCount(0);\n        return;\n      }\n\n      const exceptionsScene = getExceptionsScene(model);\n      if (!exceptionsScene) {\n        setExceptionsCount(0);\n        return;\n      }\n\n      setExceptionsCount(exceptionsScene.getExceptionsCount());\n      const subscription = exceptionsScene.subscribeToState((newState, prevState) => {\n        if (newState.exceptionsCount !== prevState.exceptionsCount) {\n          setExceptionsCount(newState.exceptionsCount || 0);\n        }\n      });\n\n      return () => {\n        subscription.unsubscribe();\n      };\n    }, [metric, model, actionView, filters, primarySignal, timeRangeValue]);\n\n    useEffect(() => {\n      if (metricScene.state.hasSetView) {\n        return;\n      }\n\n      // Set the view to traceList if the data is loaded and the traces count is greater than 20\n      if (\n        exploration.state.embedded &&\n        dataState.data?.state === LoadingState.Done &&\n        tracesCount !== undefined &&\n        tracesCount > 20\n      ) {\n        metricScene.setState({ hasSetView: true });\n        metricScene.setActionView('traceList');\n        return;\n      }\n    }, [dataState.data?.state, exploration.state.embedded, metricScene, tracesCount]);\n\n    useMount(() => {\n      if (enabledViews.length === 1) {\n        metricScene.setActionView(enabledViews[0].value);\n      }\n    });\n\n    if (enabledViews.length === 1) {\n      return null;\n    }\n\n    return (\n      <Box>\n        <div className={styles.actions}>\n          <Stack gap={1}>\n            <ShareExplorationAction exploration={exploration} />\n          </Stack>\n        </div>\n\n        <TabsBar>\n          {enabledViews.map((tab, index) => {\n            return (\n              <Tab\n                key={index}\n                label={tab.displayName(metric as MetricFunction)}\n                active={actionView === tab.value}\n                onChangeTab={() => metricScene.setActionView(tab.value)}\n                counter={\n                  tab.value === 'traceList' ? tracesCount : tab.value === 'exceptions' ? exceptionsCount : undefined\n                }\n              />\n            );\n          })}\n        </TabsBar>\n      </Box>\n    );\n  };\n}\n\nfunction breakdownDisplayName(_: MetricFunction) {\n  return 'Breakdown';\n}\n\nfunction comparisonDisplayName(_: MetricFunction) {\n  return 'Comparison';\n}\n\nexport function structureDisplayName(metric: MetricFunction) {\n  switch (metric) {\n    case 'rate':\n      return 'Service structure';\n    case 'errors':\n      return 'Root cause errors';\n    case 'duration':\n      return 'Root cause latency';\n  }\n}\n\nfunction tracesDisplayName(metric: MetricFunction) {\n  return metric === 'errors' ? 'Errored traces' : metric === 'duration' ? 'Slow traces' : 'Traces';\n}\n\nfunction exceptionsDisplayName(_: MetricFunction) {\n  return 'Exceptions';\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    actions: css({\n      [theme.breakpoints.up(theme.breakpoints.values.md)]: {\n        position: 'absolute',\n        right: 0,\n        top: 5,\n        zIndex: 2,\n      },\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { explorationDS, MetricFunction } from 'utils/shared';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { SkeletonComponent } from '../ByFrameRepeater';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { metricByWithStatus } from '../queries/generateMetricsQuery';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { RadioButtonList, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { fieldHasEmptyValues, getOpenTrace, getTraceExplorationScene } from '../../../utils/utils';\nimport { MINI_PANEL_HEIGHT } from './TracesByServiceScene';\nimport { buildHistogramQuery } from '../queries/histogram';\nimport { histogramPanelConfig } from '../panels/histogram';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { exemplarsTransformations, removeExemplarsTransformation } from '../../../utils/exemplars';\nimport { StreamingIndicator } from '../StreamingIndicator';\n\nexport interface MiniREDPanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  metric: MetricFunction;\n  isStreaming?: boolean;\n}\n\nexport class MiniREDPanel extends SceneObjectBase<MiniREDPanelState> {\n  constructor(state: MiniREDPanelState) {\n    super({\n      isStreaming: false,\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this._onActivate();\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          this.setState({ isStreaming: data.data?.state === LoadingState.Streaming });\n\n          if (data.data?.state === LoadingState.Done) {\n            if (data.data.series.length === 0 || data.data.series[0].length === 0 || fieldHasEmptyValues(data)) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new SceneFlexItem({\n                      body: new EmptyStateScene({\n                        imgWidth: 110,\n                      }),\n                    }),\n                  ],\n                }),\n              });\n            } else {\n              this.setState({\n                panel: this.getVizPanel(this.state.metric),\n              });\n            }\n          } else if (data.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(1),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  private _onActivate() {\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new StepQueryRunner({\n          maxDataPoints: this.state.metric === 'duration' ? 24 : 64,\n          datasource: explorationDS,\n          queries: [this.state.metric === 'duration' ? buildHistogramQuery() : metricByWithStatus(this.state.metric)],\n        }),\n        transformations:\n          this.state.metric === 'duration'\n            ? [...removeExemplarsTransformation()]\n            : [...exemplarsTransformations(getOpenTrace(this))],\n      }),\n      panel: this.getVizPanel(this.state.metric),\n    });\n  }\n\n  private getVizPanel(metric: MetricFunction) {\n    return new SceneFlexLayout({\n      direction: 'row',\n      children: [\n        new SceneFlexItem({\n          body: metric === 'duration' ? this.getDurationVizPanel() : this.getRateOrErrorPanel(metric),\n        }),\n      ],\n    });\n  }\n\n  private getRateOrErrorPanel(metric: MetricFunction) {\n    const panel = barsPanelConfig(metric).setHoverHeader(true).setDisplayMode('transparent');\n    if (metric === 'rate') {\n      panel.setCustomFieldConfig('axisLabel', 'span/s');\n    } else if (metric === 'errors') {\n      panel.setTitle('Errors rate').setCustomFieldConfig('axisLabel', 'error/s').setColor({\n        fixedColor: 'semi-dark-red',\n        mode: 'fixed',\n      });\n    }\n\n    return panel.build();\n  }\n\n  private getDurationVizPanel() {\n    return histogramPanelConfig()\n      .setTitle('Histogram by duration')\n      .setHoverHeader(true)\n      .setDisplayMode('transparent')\n      .build();\n  }\n\n  public static Component = ({ model }: SceneComponentProps<MiniREDPanel>) => {\n    const { panel, isStreaming } = model.useState();\n    const styles = useStyles2(getStyles);\n    const traceExploration = getTraceExplorationScene(model);\n\n    const selectMetric = () => {\n      reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.metric_changed, {\n        metric: model.state.metric,\n        location: 'panel',\n      });\n      traceExploration.onChangeMetricFunction(model.state.metric);\n    };\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={css([styles.container, styles.clickable])} onClick={selectMetric}>\n        <div className={styles.headerWrapper}>\n          <RadioButtonList\n            className={styles.radioButton}\n            name={`metric-${model.state.metric}`}\n            options={[{ title: '', value: 'selected' }]}\n            onChange={() => selectMetric()}\n            value={'not-selected'}\n          />\n        </div>\n        {isStreaming && (\n          <div className={styles.indicatorWrapper}>\n            <StreamingIndicator isStreaming={true} iconSize={10} />\n          </div>\n        )}\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flex: 1,\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      border: `1px solid ${theme.colors.border.weak}`,\n      borderRadius: '2px',\n      background: theme.colors.background.primary,\n      paddingTop: '8px',\n\n      'section, section:hover': {\n        borderColor: 'transparent',\n      },\n\n      '& .show-on-hover': {\n        display: 'none',\n      },\n    }),\n    headerWrapper: css({\n      display: 'flex',\n      alignItems: 'center',\n      position: 'absolute',\n      top: '4px',\n      left: '8px',\n      zIndex: 2,\n    }),\n    clickable: css({\n      cursor: 'pointer',\n      maxHeight: MINI_PANEL_HEIGHT,\n\n      ['[class*=\"loading-state-scene\"]']: {\n        height: MINI_PANEL_HEIGHT,\n        overflow: 'hidden',\n      },\n\n      ':hover': {\n        background: theme.colors.background.secondary,\n        input: {\n          backgroundColor: '#ffffff',\n          border: '5px solid #3D71D9',\n          cursor: 'pointer',\n        },\n      },\n    }),\n    radioButton: css({\n      display: 'block',\n    }),\n    indicatorWrapper: css({\n      position: 'absolute',\n      top: '4px',\n      right: '8px',\n      zIndex: 2,\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  Dashboard<PERSON>ursorSync,\n  GrafanaTheme2,\n  MetricFindValue,\n  dateTime,\n  DataFrame,\n  GetTagResponse,\n} from '@grafana/data';\nimport {\n  behaviors,\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneObjectUrlSyncConfig,\n  SceneObjectUrlValues,\n  SceneQueryRunner,\n  SceneTimeRange,\n} from '@grafana/scenes';\n\nimport { REDPanel } from './REDPanel';\nimport {\n  MakeOptional,\n  explorationDS,\n  VAR_FILTERS_EXPR,\n  VAR_DATASOURCE_EXPR,\n  MetricFunction,\n  ComparisonSelection,\n  ALL,\n  VAR_LATENCY_THRESHOLD_EXPR,\n  filterStreamingProgressTransformations,\n} from '../../../utils/shared';\nimport { getDataSourceSrv } from '@grafana/runtime';\nimport { TabsBarScene, actionViewsDefinitions } from './Tabs/TabsBarScene';\nimport { isEqual } from 'lodash';\nimport {\n  getDatasourceVariable,\n  getGroupByVariable,\n  getSpanListColumnsVariable,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { MiniREDPanel } from './MiniREDPanel';\nimport { Icon, LinkButton, Stack, Tooltip, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { getDefaultSelectionForMetric } from '../../../utils/comparison';\nimport { map, Observable } from 'rxjs';\nimport { ActionViewType } from 'exposedComponents/types';\nimport { ExceptionsScene } from './Tabs/Exceptions/ExceptionsScene';\n\nexport interface TraceSceneState extends SceneObjectState {\n  body: SceneFlexLayout;\n  actionView?: ActionViewType;\n\n  attributes?: string[];\n  selection?: ComparisonSelection;\n  hasSetView?: boolean;\n  exceptionsScene?: ExceptionsScene;\n}\n\nexport class TracesByServiceScene extends SceneObjectBase<TraceSceneState> {\n  protected _urlSync = new SceneObjectUrlSyncConfig(this, { keys: ['actionView', 'selection'] });\n\n  public constructor(state: MakeOptional<TraceSceneState, 'body'>) {\n    super({\n      body: state.body ?? new SceneFlexLayout({ children: [] }),\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    // Get the initial actionView from URL if it exists i.e. coming from a bookmark\n    const params = new URLSearchParams(window.location.search);\n    const urlActionView = params.get('actionView');\n    if (urlActionView && actionViewsDefinitions.find((v) => v.value === urlActionView)) {\n      this.setState({ actionView: urlActionView as ActionViewType });\n    }\n\n    this.updateBody();\n\n    const exploration = getTraceExplorationScene(this);\n    const metricVariable = exploration.getMetricVariable();\n    this._subs.add(\n      metricVariable.subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          const selection = getDefaultSelectionForMetric(newState.value as MetricFunction);\n          if (selection) {\n            this.setState({ selection });\n          }\n          this.updateQueryRunner(newState.value as MetricFunction);\n          this.updateExceptionsScene(newState.value as MetricFunction);\n          this.updateBody();\n        }\n      })\n    );\n\n    // Initialize exceptions scene for the current metric\n    this.updateExceptionsScene(metricVariable.getValue() as MetricFunction);\n\n    this._subs.add(\n      this.subscribeToState((newState, prevState) => {\n        const timeRange = sceneGraph.getTimeRange(this);\n        const selectionFrom = newState.selection?.timeRange?.from;\n        // clear selection if it's out of time range\n        if (selectionFrom && selectionFrom < timeRange.state.value.from.unix()) {\n          this.setState({ selection: undefined });\n        }\n\n        // Set group by to All when starting a comparison\n        if (!isEqual(newState.selection, prevState.selection)) {\n          const groupByVar = getGroupByVariable(this);\n          groupByVar.changeValueTo(ALL);\n          this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n        }\n      })\n    );\n\n    this._subs.add(\n      getDatasourceVariable(this).subscribeToState(() => {\n        this.updateAttributes();\n      })\n    );\n\n    this._subs.add(\n      getSpanListColumnsVariable(this).subscribeToState(() => {\n        this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n      })\n    );\n\n    this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n    this.updateAttributes();\n  }\n\n  updateBody() {\n    const traceExploration = getTraceExplorationScene(this);\n    const metric = traceExploration.getMetricVariable().getValue();\n    const actionViewDef = actionViewsDefinitions.find((v) => v.value === this.state.actionView);\n\n    this.setState({\n      body: buildGraphScene(\n        metric as MetricFunction,\n        actionViewDef ? [actionViewDef?.getScene(metric as MetricFunction)] : undefined\n      ),\n    });\n\n    if (this.state.actionView === undefined) {\n      this.setActionView('breakdown');\n    }\n  }\n\n    private updateExceptionsScene(metric: MetricFunction) {\n    if (metric === 'errors') {\n      if (!this.state.exceptionsScene) {\n        const exceptionsScene = new ExceptionsScene({});\n        this.setState({\n          exceptionsScene\n        });\n        \n        // Activate the scene after it's been set in state to ensure it starts fetching data\n        setTimeout(() => {\n          exceptionsScene.activate();\n        }, 0);\n      }\n    } else {\n      // Remove exceptions scene if metric is not errors\n      if (this.state.exceptionsScene) {\n        this.setState({\n          exceptionsScene: undefined\n        });\n      }\n    }\n  }\n\n  private async updateAttributes() {\n    const ds = await getDataSourceSrv().get(VAR_DATASOURCE_EXPR, { __sceneObject: { value: this } });\n\n    if (!ds) {\n      return;\n    }\n\n    const timeRange = sceneGraph.getTimeRange(this);\n    const options = {\n      timeRange: timeRange.state.value,\n      filters: []\n    };\n\n    ds.getTagKeys?.(options).then((tagKeys: GetTagResponse | MetricFindValue[]) => {\n      let keys: MetricFindValue[] = [];\n      if ('data' in tagKeys) {\n        keys = (tagKeys as GetTagResponse).data;\n      } else {\n        keys = tagKeys;\n      }\n      const attributes = keys.map((l) => l.text);\n      if (attributes !== this.state.attributes) {\n        this.setState({ attributes });\n      }\n    });\n  }\n\n  getUrlState() {\n    return {\n      actionView: this.state.actionView,\n      selection: this.state.selection ? JSON.stringify(this.state.selection) : undefined,\n    };\n  }\n\n  updateFromUrl(values: SceneObjectUrlValues) {\n    if (typeof values.actionView === 'string') {\n      if (this.state.actionView !== values.actionView) {\n        const actionViewDef = actionViewsDefinitions.find((v) => v.value === values.actionView);\n        if (actionViewDef) {\n          this.setActionView(actionViewDef.value);\n        }\n      }\n    } else if (values.actionView === null) {\n      this.setActionView('breakdown');\n    }\n\n    if (typeof values.selection === 'string') {\n      const newSelection = JSON.parse(values.selection);\n      if (!isEqual(newSelection, this.state.selection)) {\n        this.setState({ selection: newSelection });\n      }\n    }\n  }\n\n  onUserUpdateSelection(newSelection: ComparisonSelection) {\n    this._urlSync.performBrowserHistoryAction(() => {\n      this.setState({ selection: newSelection });\n    });\n  }\n\n  public setActionView(actionView?: ActionViewType) {\n    const { body } = this.state;\n    const actionViewDef = actionViewsDefinitions.find((v) => v.value === actionView);\n    const traceExploration = getTraceExplorationScene(this);\n    const metric = traceExploration.getMetricVariable().getValue();\n\n    if (body.state.children.length > 1) {\n      if (actionViewDef) {\n        let scene: SceneObject;\n        if (actionView === 'exceptions' && this.state.exceptionsScene) {\n          // Use the persistent exceptions scene to maintain data subscription\n          scene = new SceneFlexItem({\n            body: this.state.exceptionsScene,\n          });\n        } else {\n          scene = actionViewDef.getScene(metric as MetricFunction);\n        }\n        \n        body.setState({\n          children: [...body.state.children.slice(0, 2), scene],\n        });\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.action_view_changed, {\n          oldAction: this.state.actionView,\n          newAction: actionView,\n        });\n        this.setState({ actionView: actionViewDef.value });\n      }\n    }\n  }\n\n  private updateQueryRunner(metric: MetricFunction) {\n    const selection = this.state.selection;\n    const columns = getSpanListColumnsVariable(this).getValue()?.toString() ?? '';\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(metric, columns, selection)],\n          $timeRange: timeRangeFromSelection(selection),\n        }),\n        transformations: [...filterStreamingProgressTransformations, ...spanListTransformations],\n      }),\n    });\n  }\n\n  static Component = ({ model }: SceneComponentProps<TracesByServiceScene>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <>\n        <div className={styles.title}>\n          <Tooltip content={<MetricTypeTooltip />} placement={'right-start'} interactive>\n            <span className={styles.hand}>\n              Select metric type <Icon name={'info-circle'} />\n            </span>\n          </Tooltip>\n        </div>\n        <body.Component model={body} />\n      </>\n    );\n  };\n}\n\nconst MetricTypeTooltip = () => {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <Stack direction={'column'} gap={1}>\n      <div className={styles.tooltip.title}>RED metrics for traces</div>\n      <span className={styles.tooltip.subtitle}>\n        Explore rate, errors, and duration (RED) metrics generated from traces by Tempo.\n      </span>\n      <div className={styles.tooltip.text}>\n        <div>\n          <span className={styles.tooltip.emphasize}>Rate</span> - Spans per second that match your filter, useful to\n          find unusual spikes in activity\n        </div>\n        <div>\n          <span className={styles.tooltip.emphasize}>Errors</span> -Spans that are failing, overall issues in tracing\n          ecosystem\n        </div>\n        <div>\n          <span className={styles.tooltip.emphasize}>Duration</span> - Amount of time those spans take, represented as a\n          heat map (responds time, latency)\n        </div>\n      </div>\n\n      <div className={styles.tooltip.button}>\n        <LinkButton\n          icon=\"external-link-alt\"\n          fill=\"solid\"\n          size={'sm'}\n          target={'_blank'}\n          href={\n            'https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces/concepts/#rate-error-and-duration-metrics'\n          }\n          onClick={() =>\n            reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.metric_docs_link_clicked)\n          }\n        >\n          Read documentation\n        </LinkButton>\n      </div>\n    </Stack>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    title: css({\n      label: 'title',\n      display: 'flex',\n      gap: theme.spacing.x0_5,\n      fontSize: theme.typography.bodySmall.fontSize,\n      paddingBottom: theme.spacing.x0_5,\n      alignItems: 'center',\n    }),\n    hand: css({\n      label: 'hand',\n      cursor: 'pointer',\n    }),\n    tooltip: {\n      label: 'tooltip',\n      title: css({\n        fontSize: '14px',\n        fontWeight: 500,\n      }),\n      subtitle: css({\n        marginBottom: theme.spacing.x1,\n      }),\n      text: css({\n        label: 'text',\n        color: theme.colors.text.secondary,\n\n        div: {\n          marginBottom: theme.spacing.x0_5,\n        },\n      }),\n      emphasize: css({\n        label: 'emphasize',\n        color: theme.colors.text.primary,\n      }),\n      button: css({\n        marginBottom: theme.spacing.x0_5,\n      }),\n    },\n  };\n}\n\nconst MAIN_PANEL_HEIGHT = 240;\nexport const MINI_PANEL_HEIGHT = (MAIN_PANEL_HEIGHT - 8) / 2;\n\nexport function buildQuery(type: MetricFunction, columns: string, selection?: ComparisonSelection) {\n  const selectQuery = columns !== '' ? ` | select(${columns})` : '';\n  let typeQuery = '';\n  switch (type) {\n    case 'errors':\n      typeQuery = ' && status = error';\n      break;\n    case 'duration':\n      if (selection) {\n        const duration = [];\n        if (selection.duration?.from.length) {\n          duration.push(`duration >= ${selection.duration.from}`);\n        }\n        if (selection.duration?.to.length) {\n          duration.push(`duration <= ${selection.duration.to}`);\n        }\n        if (duration.length) {\n          typeQuery += '&& ' + duration.join(' && ');\n        }\n      }\n      if (!typeQuery.length) {\n        typeQuery = `&& duration > ${VAR_LATENCY_THRESHOLD_EXPR}`;\n      }\n      break;\n  }\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}${typeQuery}}${selectQuery}`,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 200,\n    spss: 10,\n    filters: [],\n  };\n}\n\nfunction timeRangeFromSelection(selection?: ComparisonSelection) {\n  const fromTimerange = (selection?.timeRange?.from || 0) * 1000;\n  const toTimerange = (selection?.timeRange?.to || 0) * 1000;\n  return fromTimerange && toTimerange\n    ? new SceneTimeRange({\n        from: fromTimerange.toFixed(0),\n        to: toTimerange.toFixed(0),\n        value: {\n          from: dateTime(fromTimerange),\n          to: dateTime(toTimerange),\n          raw: { from: dateTime(fromTimerange), to: dateTime(toTimerange) },\n        },\n      })\n    : undefined;\n}\n\nfunction buildGraphScene(metric: MetricFunction, children?: SceneObject[]) {\n  const secondaryPanel =\n    metric === 'rate'\n      ? new MiniREDPanel({ metric: 'errors' })\n      : new MiniREDPanel({\n          metric: 'rate',\n        });\n\n  const tertiaryPanel =\n    metric === 'duration'\n      ? new MiniREDPanel({\n          metric: 'errors',\n        })\n      : new MiniREDPanel({ metric: 'duration' });\n\n  return new SceneFlexLayout({\n    direction: 'column',\n    $behaviors: [\n      new behaviors.CursorSync({\n        key: 'metricCrosshairSync',\n        sync: DashboardCursorSync.Crosshair,\n      }),\n    ],\n    children: [\n      new SceneFlexLayout({\n        direction: 'row',\n        ySizing: 'content',\n        children: [\n          new SceneFlexItem({\n            minHeight: MAIN_PANEL_HEIGHT,\n            maxHeight: MAIN_PANEL_HEIGHT,\n            width: '60%',\n            body: new REDPanel({}),\n          }),\n          new SceneFlexLayout({\n            direction: 'column',\n            minHeight: MAIN_PANEL_HEIGHT,\n            maxHeight: MAIN_PANEL_HEIGHT,\n            children: [\n              new SceneFlexItem({\n                minHeight: MINI_PANEL_HEIGHT,\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n\n                body: secondaryPanel,\n              }),\n              new SceneFlexItem({\n                minHeight: MINI_PANEL_HEIGHT,\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n\n                ySizing: 'fill',\n\n                body: tertiaryPanel,\n              }),\n            ],\n          }),\n        ],\n      }),\n      new SceneFlexItem({\n        ySizing: 'content',\n        body: new TabsBarScene({}),\n      }),\n      ...(children || []),\n    ],\n  });\n}\n\nconst spanListTransformations = [\n  () => (source: Observable<DataFrame[]>) => {\n    return source.pipe(\n      map((data: DataFrame[]) => {\n        return data.map((df) => ({\n          ...df,\n          fields: df.fields.filter((f) => !f.name.startsWith('nestedSet')),\n        }));\n      })\n    );\n  },\n  {\n    id: 'sortBy',\n    options: {\n      fields: {},\n      sort: [\n        {\n          field: 'Duration',\n          desc: true,\n        },\n      ],\n    },\n  },\n  {\n    id: 'organize',\n    options: {\n      indexByName: {\n        'Start time': 0,\n        status: 1,\n        'Trace Service': 2,\n        'Trace Name': 3,\n        Duration: 4,\n        'Span ID': 5,\n        'span.http.method': 6,\n        'span.http.request.method': 7,\n        'span.http.path': 8,\n        'span.http.route': 9,\n        'span.http.status_code': 10,\n        'span.http.response.status_code': 11,\n      },\n    },\n  },\n];\n", "import { VAR_FILTERS_EXPR } from '../../../utils/shared';\n\nexport function buildHistogramQuery() {\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}} | histogram_over_time(duration)`,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 1000,\n    spss: 10,\n    filters: [],\n  };\n}\n", "import { SceneComponentProps, SceneObjectBase, SceneObjectState, VizPanel } from '@grafana/scenes';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { But<PERSON>, Stack, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport React from 'react';\nimport { getFiltersVariable } from '../../../utils/utils';\nimport { addToFilters, filterExistsForKey } from '../actions/AddToFiltersAction';\nimport { computeHighestDifference } from '../../../utils/comparison';\n\nexport interface HighestDifferencePanelState extends SceneObjectState {\n  frame: DataFrame;\n  panel: VizPanel;\n  maxDifference?: number;\n  maxDifferenceIndex?: number;\n}\n\nexport class HighestDifferencePanel extends SceneObjectBase<HighestDifferencePanelState> {\n  constructor(state: HighestDifferencePanelState) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(() => this._onActivate());\n  }\n\n  private _onActivate() {\n    const { frame } = this.state;\n    this.setState({ ...computeHighestDifference(frame) });\n\n    this._subs.add(\n      this.subscribeToState((newState, prevState) => {\n        if (newState.frame !== prevState.frame) {\n          const { frame } = newState;\n          this.setState({ ...computeHighestDifference(frame) });\n        }\n      })\n    );\n  }\n\n  private getAttribute() {\n    return this.state.frame.name;\n  }\n\n  private getValue() {\n    const valueField = this.state.frame.fields.find((f) => f.name === 'Value');\n    return valueField?.values[this.state.maxDifferenceIndex || 0];\n  }\n\n  private onAddToFilters() {\n    const variable = getFiltersVariable(this);\n    const attribute = this.getAttribute();\n    if (attribute) {\n      addToFilters(variable, attribute, this.getValue());\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<HighestDifferencePanel>) => {\n    const { maxDifference, maxDifferenceIndex, panel } = model.useState();\n    const styles = useStyles2(getStyles);\n    const value = model.getValue();\n    const key = model.state.frame.name ?? '';\n    const filterExists = filterExistsForKey(getFiltersVariable(model), key, value.replace(/\"/g, ''));\n\n    return (\n      <div className={styles.container}>\n        {<panel.Component model={panel} />}\n        <div className={styles.differenceContainer}>\n          {maxDifference !== undefined && maxDifferenceIndex !== undefined && (\n            <>\n              <Stack gap={1} justifyContent={'space-between'} alignItems={'center'}>\n                <div className={styles.title}>Highest difference</div>\n                {!filterExists && (\n                  <Button\n                    size=\"sm\"\n                    variant=\"primary\"\n                    icon={'search-plus'}\n                    fill=\"text\"\n                    onClick={() => model.onAddToFilters()}\n                  >\n                    Add to filters\n                  </Button>\n                )}\n              </Stack>\n              <div className={styles.differenceValue}>\n                {(Math.abs(maxDifference) * 100).toFixed(maxDifference === 0 ? 0 : 2)}%\n              </div>\n              <div className={styles.value}>{value}</div>\n            </>\n          )}\n        </div>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n      height: '100%',\n    }),\n    differenceContainer: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n      border: `1px solid ${theme.colors.secondary.border}`,\n      background: theme.colors.background.primary,\n      padding: '8px',\n      marginBottom: theme.spacing(2),\n      fontSize: '12px',\n      height: '116px',\n    }),\n    differenceValue: css({\n      fontSize: '36px',\n      fontWeight: 'bold',\n      textAlign: 'center',\n    }),\n    value: css({\n      textAlign: 'center',\n      color: theme.colors.secondary.text,\n      textWrap: 'nowrap',\n      whiteSpace: 'nowrap',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n    }),\n    title: css({\n      fontWeight: 500,\n    }),\n  };\n}\n", "import { PanelBuilders, SceneCSSGridItem, SceneCSSGridLayout, SceneDataNode, VizPanelState } from '@grafana/scenes';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { DataFrame, PanelData } from '@grafana/data';\nimport { AxisPlacement } from '@grafana/ui';\nimport { TooltipDisplayMode } from '@grafana/schema';\nimport { HighestDifferencePanel } from './HighestDifferencePanel';\nimport { GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\n\nexport const BaselineColor = '#5794F299';\nexport const SelectionColor = '#FF9930';\n\nexport function buildAllComparisonLayout(\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  return new ByFrameRepeater({\n    body: new SceneCSSGridLayout({\n      templateColumns: GRID_TEMPLATE_COLUMNS,\n      autoRows: '320px',\n      children: [],\n    }),\n    getLayoutChild: getLayoutChild(panels, getFrameName, actionsFn, metric),\n  });\n}\n\nconst getFrameName = (df: DataFrame) => {\n  return df.name || 'No name available';\n};\n\nfunction getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame) => string,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        series: [\n          {\n            ...frame,\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      const body = existingGridItem.state.body as HighestDifferencePanel;\n      body.setState({ frame });\n      body.state.panel.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const panel = getPanelConfig(metric).setTitle(getTitle(frame)).setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: new HighestDifferencePanel({ frame, panel: panel.build() }),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n\nexport function getPanelConfig(metric: MetricFunction) {\n  return PanelBuilders.barchart()\n    .setOption('legend', { showLegend: false })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi })\n    .setMax(1)\n    .setOverrides((overrides) => {\n      overrides.matchFieldsWithName('Value').overrideCustomFieldConfig('axisPlacement', AxisPlacement.Hidden);\n      overrides\n        .matchFieldsWithName('Baseline')\n        .overrideColor({\n          mode: 'fixed',\n          fixedColor: metric === 'duration' ? BaselineColor : 'semi-dark-green',\n        })\n        .overrideUnit('percentunit');\n      overrides\n        .matchFieldsWithName('Selection')\n        .overrideColor({\n          mode: 'fixed',\n          fixedColor: metric === 'duration' ? SelectionColor : 'semi-dark-red',\n        })\n        .overrideUnit('percentunit');\n    });\n}\n", "import React, { useEffect } from 'react';\nimport { CustomVariable, MultiValueVariable, MultiValueVariableState, SceneComponentProps } from '@grafana/scenes';\nimport { primarySignalOptions } from './primary-signals';\nimport { Icon, RadioButtonGroup, Select, useStyles2, Text } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { components, DropdownIndicatorProps } from 'react-select';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { GrafanaTheme2 } from '@grafana/data';\n\nconst CustomMenu = (props: any) => {\n  const styles = useStyles2(getStyles);\n  return <components.Menu {...props} className={styles.customMenu} />;\n};\n\nexport function DropdownIndicator({ selectProps }: DropdownIndicatorProps) {\n  const isOpen = selectProps.menuIsOpen;\n  const icon = isOpen ? 'angle-up' : 'angle-down';\n  const size = 'md';\n  return <Icon name={icon} size={size} />;\n}\n\nconst GroupHeading = () => {\n  const styles = useStyles2(getStyles);\n  return (\n    <div className={styles.heading}>\n      <Text weight=\"bold\" variant=\"bodySmall\" color=\"secondary\">\n        Primary signal\n      </Text>\n    </div>\n  );\n};\n\nexport class PrimarySignalVariable extends CustomVariable {\n  static Component = ({ model }: SceneComponentProps<MultiValueVariable<MultiValueVariableState>>) => {\n    const styles = useStyles2(getStyles);\n    const { value, isReadOnly } = model.useState();\n\n    // ensure the variable is set to the default value\n    useEffect(() => {\n      if (!value) {\n        model.changeValueTo(isReadOnly ? primarySignalOptions[1].value! : primarySignalOptions[0].value!);\n      }\n    });\n\n    const buttonGroupOptions = primarySignalOptions.slice(0, 2);\n    const currentSignal = primarySignalOptions.find((option) => option.value === value);\n    if (currentSignal && !buttonGroupOptions.some((option) => option.filter.key === currentSignal.filter.key)) {\n      buttonGroupOptions.push(currentSignal);\n    }\n    const selectOptions = primarySignalOptions.filter(\n      (option) => !buttonGroupOptions.some((b) => b.value === option.value)\n    );\n\n    const onChange = (v: string) => {\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.primary_signal_changed,\n        {\n          primary_signal: v,\n        }\n      );\n      model.changeValueTo(v!, undefined, true);\n    };\n\n    if (isReadOnly) {\n      return <></>;\n    }\n\n    return (\n      <>\n        <RadioButtonGroup\n          options={buttonGroupOptions}\n          value={value as string}\n          onChange={onChange}\n          disabled={isReadOnly}\n          className={styles.buttonGroup}\n        />\n        <Select\n          options={[{ label: 'Primary signal', options: selectOptions }]}\n          value={''}\n          placeholder=\"\"\n          isSearchable={false}\n          isClearable={false}\n          width={4}\n          onChange={(v) => onChange(v.value!)}\n          className={styles.select}\n          components={{\n            IndicatorSeparator: () => null,\n            SingleValue: () => null,\n            Menu: CustomMenu,\n            DropdownIndicator,\n            GroupHeading,\n          }}\n        />\n      </>\n    );\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  select: css`\n    [class$='input-suffix'] {\n      position: absolute;\n      z-index: 2;\n    }\n\n    :focus-within {\n      outline: none;\n      box-shadow: none;\n    }\n\n    > div {\n      padding: 0;\n    }\n\n    input {\n      opacity: 0 !important;\n    }\n\n    border-radius: 0 2px 2px 0;\n    border-left: none;\n  `,\n  buttonGroup: css`\n    border-radius: 2px 0 0 2px;\n  `,\n  customMenu: css`\n    width: 230px;\n\n    [class$='grafana-select-option-grafana-select-option-focused'] {\n      background: transparent;\n\n      ::before {\n        display: none;\n      }\n    }\n  `,\n  heading: css({\n    padding: theme.spacing(1, 1, 0.75, 0.75),\n    borderLeft: '2px solid transparent',\n    borderBottom: `1px solid ${theme.colors.border.weak}`,\n  }),\n});\n", "import { ALL, MetricFunction, VAR_FILTERS_EXPR } from '../../../utils/shared';\n\ninterface QueryOptions {\n  metric: MetricFunction;\n  extraFilters?: string;\n  groupByKey?: string;\n}\n\nexport function generateMetricsQuery({ metric, groupByKey, extraFilters}: QueryOptions) {\n  // Generate span set filters\n  let filters = `${VAR_FILTERS_EXPR}`;\n\n  if (metric === 'errors') {\n    filters += ' && status=error';\n  }\n\n  if (extraFilters) {\n    filters += ` && ${extraFilters}`;\n  }\n\n  if (groupByKey && groupByKey !== ALL) {\n    filters += ` && ${groupByKey} != nil`;\n  }\n\n  // Generate metrics function\n  let metricFn = 'rate()';\n  switch (metric) {\n    case 'errors':\n      metricFn = 'rate()';\n      break;\n    case 'duration':\n      metricFn = 'quantile_over_time(duration, 0.9)';\n      break;\n  }\n\n  // Generate group by section\n  let groupByAttrs = [];\n  if (groupByKey && groupByKey !== ALL) {\n    groupByAttrs.push(groupByKey);\n  }\n\n  const groupBy = groupByAttrs.length ? `by(${groupByAttrs.join(', ')})` : '';\n\n  return `{${filters}} | ${metricFn} ${groupBy}`;\n}\n\nexport function metricByWithStatus(metric: MetricFunction, tagKey?: string) {\n  return {\n    refId: 'A',\n    query: generateMetricsQuery({ metric, groupByKey: tagKey}),\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 100,\n    spss: 10,\n    filters: [],\n  };\n}\n", "import { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport React from 'react';\nimport { EmptyState } from './EmptyState';\n\ninterface EmptyStateSceneState extends SceneObjectState {\n  message?: string;\n  remedyMessage?: string;\n  imgWidth?: number;\n  padding?: string;\n}\n\nexport class EmptyStateScene extends SceneObjectBase<EmptyStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<EmptyStateScene>) => {\n    const { message, remedyMessage, imgWidth, padding } = model.useState();\n    return <EmptyState message={message} remedyMessage={remedyMessage} imgWidth={imgWidth} padding={padding} />;\n  };\n}\n", "import { QueryRunnerState, sceneGraph, SceneQueryRunner } from '@grafana/scenes';\nimport { getStepForTimeRange } from '../../../utils/dates';\n\nexport class StepQueryRunner extends SceneQueryRunner {\n  constructor(state: QueryRunnerState) {\n    super(state);\n    this.addActivationHandler(this._onActivateStep.bind(this));\n  }\n\n  private _onActivateStep() {\n    const step = getStepForTimeRange(this, this.state.maxDataPoints);\n    this.setState({\n      queries: this.state.queries.map((query) => {\n        return {\n          ...query,\n          step,\n        };\n      }),\n    });\n\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    sceneTimeRange.subscribeToState((newState, prevState) => {\n      if (newState.value.from !== prevState.value.from || newState.value.to !== prevState.value.to) {\n        const newStep = getStepForTimeRange(this, this.state.maxDataPoints);\n        this.setState({\n          queries: this.state.queries.map((query) => {\n            return {\n              ...query,\n              step: newStep,\n            };\n          }),\n        });\n      }\n    });\n  }\n}\n", "import { throttle } from 'lodash';\nimport { useState, useEffect } from 'react';\n\ninterface MousePosition {\n  x: number | null;\n  y: number | null;\n}\n\n// For performance reasons, we throttle the mouse position updates\nconst DEFAULT_THROTTLE_INTERVAL_MS = 50;\n\nconst useMousePosition = (throttleInterval = DEFAULT_THROTTLE_INTERVAL_MS) => {\n  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: null, y: null });\n\n  useEffect(() => {\n    const updateMousePosition = throttle((event: MouseEvent) => {\n      setMousePosition({ x: event.clientX, y: event.clientY });\n    }, throttleInterval);\n    window.addEventListener('mousemove', updateMousePosition);\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition);\n    };\n  }, [throttleInterval]);\n\n  return mousePosition;\n};\n\nexport default useMousePosition;\n", "import { css } from '@emotion/css';\nimport React, { SVGProps } from 'react';\nimport SVG from 'react-inlinesvg';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2, useTheme2 } from '@grafana/ui';\n\nimport dark404 from './img/grot-404-dark.svg';\nimport light404 from './img/grot-404-light.svg';\n\nimport useMousePosition from './useMousePosition';\n\nconst MIN_ARM_ROTATION = -20;\nconst MAX_ARM_ROTATION = 5;\nconst MIN_ARM_TRANSLATION = -5;\nconst MAX_ARM_TRANSLATION = 5;\n\nexport interface Props {\n  width?: SVGProps<SVGElement>['width'];\n  height?: SVGProps<SVGElement>['height'];\n  show404?: boolean;\n}\n\nexport const GrotNotFound = ({ width = 'auto', height, show404 = false }: Props) => {\n  const theme = useTheme2();\n  const { x, y } = useMousePosition();\n  const styles = useStyles2(getStyles, x, y, show404);\n  return <SVG src={theme.isDark ? dark404 : light404} className={styles.svg} height={height} width={width} />;\n};\n\nGrotNotFound.displayName = 'GrotNotFound';\n\nconst getStyles = (theme: GrafanaTheme2, xPos: number | null, yPos: number | null, show404: boolean) => {\n  const { innerWidth, innerHeight } = window;\n  const heightRatio = yPos && yPos / innerHeight;\n  const widthRatio = xPos && xPos / innerWidth;\n  const rotation = heightRatio !== null ? getIntermediateValue(heightRatio, MIN_ARM_ROTATION, MAX_ARM_ROTATION) : 0;\n  const translation =\n    widthRatio !== null ? getIntermediateValue(widthRatio, MIN_ARM_TRANSLATION, MAX_ARM_TRANSLATION) : 0;\n\n  return {\n    svg: css({\n      '#grot-404-arm, #grot-404-magnifier': {\n        transform: `rotate(${rotation}deg) translateX(${translation}%)`,\n        transformOrigin: 'center',\n        transition: 'transform 50ms linear',\n      },\n      '#grot-404-text': {\n        display: show404 ? 'block' : 'none',\n      },\n    }),\n  };\n};\n\n/**\n * Given a start value, end value, and a ratio, return the intermediate value\n * Works with negative and inverted start/end values\n */\nconst getIntermediateValue = (ratio: number, start: number, end: number) => {\n  const value = ratio * (end - start) + start;\n  return value;\n};\n", "import React from 'react';\n\nimport { Icon, Stack, Text, useStyles2 } from '@grafana/ui';\nimport { GrafanaTheme2 } from '@grafana/data';\n\nimport { GrotNotFound } from './GrotNotFound';\nimport { css } from '@emotion/css';\nimport { testIds } from 'utils/testIds';\n\nexport interface Props {\n  message?: string | React.ReactNode;\n  remedyMessage?: string;\n  imgWidth?: number;\n  padding?: string;\n}\n\nexport const EmptyState = ({ message, remedyMessage, imgWidth, padding }: Props) => {\n  const styles = useStyles2(getStyles, padding);\n\n  return (\n    <div className={styles.container} data-testid={testIds.emptyState}>\n      <Stack direction=\"column\" alignItems=\"center\" gap={3}>\n        <GrotNotFound width={imgWidth ?? 300} />\n        {typeof message === 'string' &&  <Text textAlignment={'center'} variant=\"h5\">{message}</Text>}\n        {typeof message !== 'string' &&  message}\n\n        {remedyMessage && (\n          <div className={styles.remedy}>\n            <Stack gap={0.5} alignItems={'center'}>\n              <Icon name=\"info-circle\" />\n              <Text textAlignment={'center'} variant=\"body\">\n                {remedyMessage}\n              </Text>\n            </Stack>\n          </div>\n        )}\n      </Stack>\n    </div>\n  );\n};\n\nEmptyState.displayName = 'EmptyState';\n\nfunction getStyles(theme: GrafanaTheme2, padding?: string) {\n  return {\n    container: css({\n      width: '100%',\n      display: 'flex',\n      justifyContent: 'space-evenly',\n      flexDirection: 'column',\n      padding: padding ? padding : 0,\n    }),\n    remedy: css({\n      marginBottom: theme.spacing(4),\n    })\n  };\n}\n", "import { PanelBuilders } from '@grafana/scenes';\nimport { DrawStyle, StackingMode, TooltipDisplayMode } from '@grafana/ui';\nimport { MetricFunction } from 'utils/shared';\n\nexport const barsPanelConfig = (metric: MetricFunction) => {\n  const isErrorsMetric = metric === 'errors' || false;\n  \n  const builder = PanelBuilders.timeseries()\n    .setOption('legend', { showLegend: false })\n    .setCustomFieldConfig('drawStyle', DrawStyle.Bars)\n    .setCustomFieldConfig('stacking', { mode: StackingMode.Normal })\n    .setCustomFieldConfig('fillOpacity', 75)\n    .setCustomFieldConfig('lineWidth', 0)\n    .setCustomFieldConfig('pointSize', 0)\n    .setCustomFieldConfig('axisLabel', 'Rate')\n    .setOverrides((overrides) => {\n      overrides.matchFieldsWithNameByRegex('.*').overrideColor({\n        mode: 'fixed',\n        fixedColor: isErrorsMetric ? 'semi-dark-red' : 'green',\n      });\n    })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi });\n\n  return builder;\n};\n", "import React from 'react';\n\nimport { SceneObjectBase, SceneComponentProps, SceneObjectState } from '@grafana/scenes';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Button, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { getMetricValue, getTraceByServiceScene, shouldShowSelection } from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { ComparisonSelection } from '../../../utils/shared';\n\nexport interface ComparisonControlState extends SceneObjectState {\n  selection?: ComparisonSelection;\n}\n\nexport class DurationComparisonControl extends SceneObjectBase<ComparisonControlState> {\n  public constructor({ selection }: ComparisonControlState) {\n    super({ selection });\n  }\n\n  public startInvestigation = () => {\n    const byServiceScene = getTraceByServiceScene(this);\n    byServiceScene.setState({ selection: this.state.selection });\n    if (!shouldShowSelection(byServiceScene.state.actionView)) {\n      byServiceScene.setActionView('comparison');\n    }\n\n    reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.start_investigation, {\n      selection: this.state.selection,\n      metric: getMetricValue(this),\n    });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<DurationComparisonControl>) => {\n    const { selection } = getTraceByServiceScene(model).useState();\n    const styles = useStyles2(getStyles);\n\n    const isDisabled = selection?.type === 'auto';\n    const tooltip = isDisabled\n      ? 'Slowest traces are selected, navigate to the Comparison or Slow Traces tab for more details.'\n      : undefined;\n\n    return (\n      <div className={styles.wrapper}>\n        <Button\n          variant=\"secondary\"\n          size=\"sm\"\n          fill=\"solid\"\n          disabled={isDisabled}\n          icon={'bolt'}\n          onClick={model.startInvestigation}\n          tooltip={tooltip}\n        >\n          {isDisabled ? 'Slowest traces selected' : 'Select slowest traces'}\n        </Button>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    wrapper: css({\n      display: 'flex',\n      gap: '16px',\n      alignItems: 'center',\n    }),\n    placeholder: css({\n      color: theme.colors.text.secondary,\n      fontSize: theme.typography.bodySmall.fontSize,\n      display: 'flex',\n      gap: theme.spacing.x0_5,\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { arrayToDataFrame, DataFrame, GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { ComparisonSelection, EMPTY_STATE_ERROR_MESSAGE, explorationDS, MetricFunction } from 'utils/shared';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { SkeletonComponent } from '../ByFrameRepeater';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { metricByWithStatus } from '../queries/generateMetricsQuery';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { css } from '@emotion/css';\nimport { RadioButtonList, useStyles2 } from '@grafana/ui';\nimport { StreamingIndicator } from '../StreamingIndicator';\nimport {\n  fieldHasEmptyValues,\n  getLatencyPartialThresholdVariable,\n  getLatencyThresholdVariable,\n  getMetricVariable,\n  getOpenTrace,\n  getTraceByServiceScene,\n  shouldShowSelection,\n} from '../../../utils/utils';\nimport { getHistogramVizPanel, yBucketToDuration } from '../panels/histogram';\nimport { TraceSceneState } from './TracesByServiceScene';\nimport { SelectionColor } from '../layouts/allComparison';\nimport { buildHistogramQuery } from '../queries/histogram';\nimport { isEqual } from 'lodash';\nimport { DurationComparisonControl } from './DurationComparisonControl';\nimport { exemplarsTransformations, removeExemplarsTransformation } from '../../../utils/exemplars';\n\nexport interface RateMetricsPanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  actions?: SceneObject[];\n  yBuckets?: number[];\n  isStreaming?: boolean;\n}\n\nexport class REDPanel extends SceneObjectBase<RateMetricsPanelState> {\n  constructor(state: RateMetricsPanelState) {\n    super({\n      yBuckets: [],\n      actions: [],\n      isStreaming: false,\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this._onActivate();\n      const data = sceneGraph.getData(this);\n      const parent = getTraceByServiceScene(this);\n      const timeRange = sceneGraph.getTimeRange(this);\n\n      this._subs.add(\n        data.subscribeToState((newData) => {\n          this.setState({ isStreaming: newData.data?.state === LoadingState.Streaming });\n\n          if (newData.data?.state === LoadingState.Done) {\n            if (\n              newData.data.series.length === 0 ||\n              newData.data.series[0].length === 0 ||\n              fieldHasEmptyValues(newData)\n            ) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new SceneFlexItem({\n                      body: new EmptyStateScene({\n                        message: EMPTY_STATE_ERROR_MESSAGE,\n                        imgWidth: 150,\n                      }),\n                    }),\n                  ],\n                }),\n              });\n            } else {\n              let yBuckets: number[] | undefined = [];\n              if (this.isDuration()) {\n                yBuckets = getYBuckets(data.state.data?.series || []);\n                if (parent.state.selection && newData.data?.state === LoadingState.Done) {\n                  // set selection annotation if it exists\n                  const annotations = this.buildSelectionAnnotation(parent.state);\n\n                  if (annotations && !data.state.data?.annotations?.length) {\n                    data.setState({\n                      data: {\n                        ...data.state.data!,\n                        annotations: annotations,\n                      },\n                    });\n                  }\n                }\n\n                if (yBuckets?.length) {\n                  const { minDuration, minBucket } = getMinimumsForDuration(yBuckets);\n                  const selection: ComparisonSelection = { type: 'auto' };\n\n                  getLatencyThresholdVariable(this).changeValueTo(minDuration);\n                  getLatencyPartialThresholdVariable(this).changeValueTo(\n                    yBucketToDuration(minBucket - 1, yBuckets, 0.3)\n                  );\n\n                  selection.duration = { from: minDuration, to: '' };\n                  selection.raw = {\n                    x: {\n                      from: timeRange.state.value.from.unix() * 1000,\n                      to: timeRange.state.value.to.unix() * 1000,\n                    },\n                    y: { from: minBucket - 0.5, to: yBuckets.length - 0.5 },\n                  };\n\n                  this.setState({\n                    actions: [\n                      new DurationComparisonControl({\n                        selection,\n                      }),\n                    ],\n                  });\n                  if (!parent.state.selection?.duration || parent.state.selection.type === 'auto') {\n                    parent.setState({ selection });\n                  }\n                }\n              }\n\n              // update panel\n              this.setState({\n                yBuckets,\n                panel: this.getVizPanel(),\n              });\n            }\n          } else if (newData.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(1),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n\n      this._subs.add(\n        parent.subscribeToState((newState, prevState) => {\n          if (data.state.data?.state === LoadingState.Done) {\n            if (!isEqual(newState.selection, prevState.selection) || newState.actionView !== prevState.actionView) {\n              if (this.isDuration()) {\n                const annotations = this.buildSelectionAnnotation(newState);\n                data.setState({\n                  data: {\n                    ...data.state.data!,\n                    annotations: annotations,\n                  },\n                });\n              }\n            }\n          }\n        })\n      );\n    });\n  }\n\n  private isDuration() {\n    return getMetricVariable(this).state.value === 'duration';\n  }\n\n  private _onActivate() {\n    const metric = getMetricVariable(this).state.value as MetricFunction;\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new StepQueryRunner({\n          maxDataPoints: this.isDuration() ? 24 : 64,\n          datasource: explorationDS,\n          queries: [this.isDuration() ? buildHistogramQuery() : metricByWithStatus(metric)],\n        }),\n        transformations: this.isDuration()\n          ? [...removeExemplarsTransformation()]\n          : [...exemplarsTransformations(getOpenTrace(this))],\n      }),\n      panel: this.getVizPanel(),\n    });\n  }\n\n  private getVizPanel() {\n    const metric = getMetricVariable(this).state.value as MetricFunction;\n    if (this.isDuration()) {\n      return getHistogramVizPanel(this, this.state.yBuckets ?? []);\n    }\n\n    return this.getRateOrErrorVizPanel(metric);\n  }\n\n  private getRateOrErrorVizPanel(type: MetricFunction) {\n    const panel = barsPanelConfig(type).setHoverHeader(true).setDisplayMode('transparent');\n    if (type === 'rate') {\n      panel.setCustomFieldConfig('axisLabel', 'span/s');\n    } else if (type === 'errors') {\n      panel.setCustomFieldConfig('axisLabel', 'error/s').setColor({\n        fixedColor: 'semi-dark-red',\n        mode: 'fixed',\n      });\n    }\n    return new SceneFlexLayout({\n      direction: 'row',\n      children: [\n        new SceneFlexItem({\n          body: panel.build(),\n        }),\n      ],\n    });\n  }\n\n  private buildSelectionAnnotation(state: TraceSceneState) {\n    if (!shouldShowSelection(state.actionView)) {\n      return undefined;\n    }\n\n    const xSel = state.selection?.raw?.x;\n    const ySel = state.selection?.raw?.y;\n\n    const frame = arrayToDataFrame([\n      {\n        time: xSel?.from || 0,\n        xMin: xSel?.from || 0,\n        xMax: xSel?.to || 0,\n        timeEnd: xSel?.to || 0,\n        yMin: ySel?.from,\n        yMax: ySel?.to,\n        isRegion: true,\n        fillOpacity: 0.15,\n        lineWidth: 1,\n        lineStyle: 'solid',\n        color: SelectionColor,\n        text: 'Comparison selection',\n      },\n    ]);\n    frame.name = 'xymark';\n\n    return [frame];\n  }\n\n  public static Component = ({ model }: SceneComponentProps<REDPanel>) => {\n    const { panel, actions, isStreaming } = model.useState();\n    const { value: metric } = getMetricVariable(model).useState();\n    const styles = useStyles2(getStyles);\n\n    if (!panel) {\n      return;\n    }\n\n    const getTitle = () => {\n      switch (metric) {\n        case 'errors':\n          return 'Errors rate';\n        case 'rate':\n          return 'Span rate';\n        case 'duration':\n          return 'Histogram by duration';\n        default:\n          return '';\n      }\n    };\n\n    const getSubtitle = () => {\n      switch (metric) {\n        case 'duration':\n          return 'Click and drag to compare selection with baseline.';\n        default:\n          return '';\n      }\n    };\n\n    const subtitle = getSubtitle();\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.headerContainer}>\n          <div className={styles.titleContainer}>\n            <div className={styles.titleRadioWrapper}>\n              <RadioButtonList\n                name={`metric-${metric}`}\n                options={[{ title: '', value: 'selected' }]}\n                value={'selected'}\n              />\n              <span>{getTitle()}</span>\n            </div>\n            {subtitle && <div className={styles.subtitle}>{subtitle}</div>}\n          </div>\n          <div className={styles.actions}>\n            {isStreaming && <StreamingIndicator isStreaming={true} iconSize={10} />}\n            {actions?.map((action) => <action.Component model={action} key={action.state.key} />)}\n          </div>\n        </div>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nexport const getYBuckets = (series: DataFrame[]) => {\n  return series.map((s) => parseFloat(s.fields[1].name)).sort((a, b) => a - b);\n};\n\nexport const getMinimumsForDuration = (yBuckets: number[]) => {\n  const slowestBuckets = Math.floor(yBuckets.length / 4);\n  let minBucket = yBuckets.length - slowestBuckets - 1;\n  if (minBucket < 0) {\n    minBucket = 0;\n  }\n\n  return {\n    minDuration: yBucketToDuration(minBucket - 1, yBuckets),\n    minBucket,\n  };\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      border: `1px solid ${theme.colors.border.weak}`,\n      borderRadius: '2px',\n      background: theme.colors.background.primary,\n\n      '.show-on-hover': {\n        display: 'none',\n      },\n      'section, section:hover': {\n        borderColor: 'transparent',\n      },\n      '& .u-select': {\n        border: '1px solid #ffffff75',\n      },\n    }),\n    headerContainer: css({\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'row',\n      padding: '8px',\n      gap: '8px',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start',\n      fontWeight: theme.typography.fontWeightBold,\n    }),\n    titleContainer: css({\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '4px',\n    }),\n    titleRadioWrapper: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    actions: css({\n      display: 'flex',\n      gap: '8px',\n      alignItems: 'center',\n    }),\n    subtitle: css({\n      display: 'flex',\n      color: theme.colors.text.secondary,\n      fontSize: '12px',\n      fontWeight: 400,\n\n      '& svg': {\n        margin: '0 2px',\n      },\n    }),\n  };\n}\n", "export const testIds = {\n  emptyState: 'data-testid empty-state',\n  errorState: 'data-testid error-state',\n  loadingState: 'data-testid loading-state',\n};\n", "import { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport React from 'react';\nimport { Alert } from '@grafana/ui';\nimport { testIds } from 'utils/testIds';\n\ninterface ErrorStateSceneState extends SceneObjectState {\n  message: string;\n}\n\nexport class ErrorStateScene extends SceneObjectBase<ErrorStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<ErrorStateScene>) => {\n    const { message } = model.useState();\n    return (\n      <Alert title={'Query error'} severity={'error'} data-testid={testIds.errorState}>\n        {message}\n      </Alert>\n    );\n  };\n}\n", "import { Field, Input, Icon, useStyles2 } from \"@grafana/ui\"\nimport React from \"react\"\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { css } from \"@emotion/css\";\n\ntype Props = {\n  searchQuery: string;\n  onSearchQueryChange: (event: React.ChangeEvent<HTMLInputElement>) => void;\n}\n\nexport const Search = (props: Props) => {\n  const styles = useStyles2(getStyles);\n  const { searchQuery, onSearchQueryChange } = props;\n\n  return (\n    <Field className={styles.searchField}>\n      <Input\n        placeholder='Search'\n        prefix={<Icon name={'search'} />}\n        value={searchQuery}\n        onChange={onSearchQueryChange}\n        id='searchFieldInput'\n      />\n    </Field>\n  )\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    searchField: css({\n      marginBottom: theme.spacing(1),\n    }),\n  };\n}\n", "import { DataQueryResponseData, Field } from '@grafana/data';\n\nexport function cloneDataFrame(frame: DataQueryResponseData): DataQueryResponseData {\n  return {\n    ...frame,\n    fields: frame.fields.map((field: Field) => ({\n      ...field,\n      values: field.values,\n    })),\n  };\n}\n", "import React from 'react';\n\nimport { DataFrame, FieldType, GrafanaTheme2, LoadingState, PanelData, sortDataFrame } from '@grafana/data';\nimport {\n  SceneComponentProps,\n  SceneCSSGridLayout,\n  SceneFlexItem,\n  sceneGraph,\n  SceneLayout,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { css } from '@emotion/css';\nimport { useStyles2 } from '@grafana/ui';\nimport Skeleton from 'react-loading-skeleton';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { ErrorStateScene } from 'components/states/ErrorState/ErrorStateScene';\nimport { debounce } from 'lodash';\nimport { Search } from './Search';\nimport { getGroupByVariable } from 'utils/utils';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n  EventTimeseriesDataReceived,\n  GRID_TEMPLATE_COLUMNS,\n} from '../../utils/shared';\nimport { cloneDataFrame } from '../../utils/frames';\n\ninterface ByFrameRepeaterState extends SceneObjectState {\n  body: SceneLayout;\n  groupBy?: boolean;\n\n  getLayoutChild(data: PanelData, frame: DataFrame, frameIndex: number): SceneFlexItem;\n\n  searchQuery?: string;\n}\n\nexport class ByFrameRepeater extends SceneObjectBase<ByFrameRepeaterState> {\n  public constructor(state: ByFrameRepeaterState) {\n    super(state);\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done || data.data?.state === LoadingState.Streaming) {\n            if (data.data.series.length === 0 && data.data?.state !== LoadingState.Streaming) {\n              this.state.body.setState({\n                children: [\n                  new SceneFlexItem({\n                    body: new EmptyStateScene({\n                      message: EMPTY_STATE_ERROR_MESSAGE,\n                      remedyMessage: EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n                      padding: '32px',\n                    }),\n                  }),\n                ],\n              });\n            } else if (data.data?.state === LoadingState.Done) {\n              const filtered = {\n                ...data.data,\n                series: data.data?.series.filter(doesQueryMatchDataFrameLabels(this.state.searchQuery)),\n              };\n              this.renderFilteredData(filtered as PanelData);\n              this.publishEvent(new EventTimeseriesDataReceived({ series: data.data.series }), true);\n            }\n          } else if (data.data?.state === LoadingState.Error) {\n            this.state.body.setState({\n              children: [\n                new SceneCSSGridLayout({\n                  children: [\n                    new ErrorStateScene({\n                      message: data.data.errors?.[0]?.message ?? 'An error occurred in the query',\n                    }),\n                  ],\n                }),\n              ],\n            });\n          } else {\n            this.state.body.setState({\n              children: [\n                new SceneCSSGridLayout({\n                  children: [\n                    new LoadingStateScene({\n                      component: () => SkeletonComponent(8),\n                    }),\n                  ],\n                }),\n              ],\n            });\n          }\n        })\n      );\n\n      this.subscribeToState((newState, prevState) => {\n        if (newState.searchQuery !== prevState.searchQuery) {\n          this.onSearchQueryChangeDebounced(newState.searchQuery ?? '');\n        }\n      });\n\n      if (data.state.data) {\n        this.performRepeat(data.state.data);\n      }\n    });\n  }\n\n  private onSearchQueryChange = (evt: React.SyntheticEvent<HTMLInputElement>) => {\n    this.setState({ searchQuery: evt.currentTarget.value });\n  };\n\n  private onSearchQueryChangeDebounced = debounce((searchQuery: string) => {\n    const data = sceneGraph.getData(this);\n    const filtered = {\n      ...data.state.data,\n      series: data.state.data?.series.filter(doesQueryMatchDataFrameLabels(searchQuery)),\n    };\n    this.renderFilteredData(filtered as PanelData);\n  }, 250);\n\n  private renderFilteredData(filtered: PanelData) {\n    if (filtered.series && filtered.series.length > 0) {\n      this.performRepeat(filtered as PanelData);\n    } else {\n      this.state.body.setState({\n        children: [\n          new SceneFlexItem({\n            body: new EmptyStateScene({\n              message: 'No data for search term',\n              padding: '32px',\n            }),\n          }),\n        ],\n      });\n    }\n  }\n\n  private groupSeriesBy(data: PanelData, groupBy: string) {\n    const groupedData = data.series.reduce(\n      (acc, series) => {\n        const key = series.fields.find((f) => f.type === FieldType.number)?.labels?.[groupBy];\n        if (!key) {\n          return acc;\n        }\n        if (!acc[key]) {\n          acc[key] = [];\n        }\n        acc[key].push(series);\n        return acc;\n      },\n      {} as Record<string, DataFrame[]>\n    );\n\n    const newSeries = [];\n    for (const key in groupedData) {\n      const frames = groupedData[key].sort((a, b) => a.name?.localeCompare(b.name!) || 0);\n      const mainFrame = cloneDataFrame(frames[0]);\n      frames.slice(1, frames.length).forEach((frame) => mainFrame.fields.push(frame.fields[1]));\n      newSeries.push(sortDataFrame(mainFrame, 0));\n    }\n    return newSeries;\n  }\n\n  private performRepeat(data: PanelData) {\n    const newChildren: SceneFlexItem[] = [];\n    let frames = data.series;\n\n    if (this.state.groupBy) {\n      frames = this.groupSeriesBy(data, getGroupByVariable(this).getValueText());\n    }\n\n    for (let frameIndex = 0; frameIndex < frames.length; frameIndex++) {\n      const currentFrame = frames[frameIndex];\n      // Skip frames with no data\n      const sum = currentFrame.fields\n        .filter((f) => f.type === FieldType.number)\n        .reduce((sum, f) => sum + f.values.reduce((vSum, v) => vSum + (v || 0), 0) || 0, 0);\n      if (sum === 0) {\n        continue;\n      }\n      // Build the layout child\n      const layoutChild = this.state.getLayoutChild(data, frames[frameIndex], frameIndex);\n      newChildren.push(layoutChild);\n    }\n\n    this.state.body.setState({ children: newChildren });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<ByFrameRepeater>) => {\n    const { body, searchQuery } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <Search searchQuery={searchQuery ?? ''} onSearchQueryChange={model.onSearchQueryChange} />\n        <body.Component model={body} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles() {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n    }),\n  };\n}\n\nexport const SkeletonComponent = (repeat: number) => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      {[...Array(repeat)].map((_, i) => (\n        <div className={styles.itemContainer} key={i}>\n          <div className={styles.header}>\n            <div className={styles.title}>\n              <Skeleton count={1} />\n            </div>\n            <div className={styles.action}>\n              <Skeleton count={1} />\n            </div>\n          </div>\n          <div className={styles.yAxis}>\n            {[...Array(2)].map((_, i) => (\n              <div className={styles.yAxisItem} key={i}>\n                <Skeleton count={1} />\n              </div>\n            ))}\n          </div>\n          <div className={styles.xAxis}>\n            {[...Array(2)].map((_, i) => (\n              <div className={styles.xAxisItem} key={i}>\n                <Skeleton count={1} />\n              </div>\n            ))}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'grid',\n      gridTemplateColumns: GRID_TEMPLATE_COLUMNS,\n      gridAutoRows: '200px',\n      rowGap: theme.spacing(1),\n      columnGap: theme.spacing(1),\n    }),\n    itemContainer: css({\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.background.secondary}`,\n      padding: '5px',\n    }),\n    header: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    title: css({\n      width: '100px',\n    }),\n    action: css({\n      width: '60px',\n    }),\n    yAxis: css({\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'space-around',\n      marginTop: '35px',\n    }),\n    yAxisItem: css({\n      width: '60px',\n      height: '55px',\n    }),\n    xAxis: css({\n      display: 'flex',\n      justifyContent: 'space-evenly',\n    }),\n    xAxisItem: css({\n      width: '55px',\n    }),\n  };\n}\n\nexport const doesQueryMatchDataFrameLabels = (searchQuery?: string) => (dataFrame: DataFrame) => {\n  const pattern = searchQuery?.trim();\n  if (!pattern) {\n    return true;\n  }\n\n  const regex = new RegExp(pattern, 'i');\n\n  return dataFrame.fields.some((f) => (!f.labels ? false : Object.values(f.labels).find((label) => regex.test(label))));\n};\n", "import { SelectableValue } from '@grafana/data';\n\nexport const DATABASE_CALLS_KEY = 'span.db.system.name';\n\nexport const primarySignalOptions: Array<SelectableValue<string>> = [\n  {\n    label: 'Root spans',\n    value: 'nestedSetParent<0',\n    filter: { key: 'nestedSetParent', operator: '<', value: '0' },\n    description: 'Focus your analysis on the root span of each trace',\n  },\n  {\n    label: 'All spans',\n    value: 'true',\n    filter: { key: '', operator: '', value: true },\n    description: 'View and analyse raw span data. This option may result in long query times.',\n  },\n  {\n    label: 'Server spans',\n    value: 'kind=server',\n    filter: { key: 'kind', operator: '=', value: 'server' },\n    description: 'Explore server-specific segments of traces',\n  },\n  {\n    label: 'Consumer spans',\n    value: 'kind=consumer',\n    filter: { key: 'kind', operator: '=', value: 'consumer' },\n    description: 'Analyze interactions initiated by consumer services',\n  },\n  {\n    label: 'Database calls',\n    value: `${DATABASE_CALLS_KEY}!=\"\"`,\n    filter: { key: DATABASE_CALLS_KEY, operator: '!=', value: '\"\"' },\n    description: 'Evaluate the performance issues in database interactions',\n  },\n];\n\nexport const getSignalForKey = (key?: string) => {\n  return primarySignalOptions.find((option) => option.value === key);\n};\n", "import { css } from '@emotion/css';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { locationService } from '@grafana/runtime';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { HomepagePanelType } from './AttributePanel';\n\ntype Props = {\n  index: number;\n  type: HomepagePanelType;\n  label: string;\n  labelTitle: string;\n  value: string;\n  valueTitle: string;\n  url: string;\n};\n\nexport const AttributePanelRow = (props: Props) => {\n  const { index, type, label, labelTitle, value, valueTitle, url } = props;\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div key={index}>\n      {index === 0 && (\n        <div className={styles.rowHeader}>\n          <span>{labelTitle}</span>\n          <span className={styles.valueTitle}>{valueTitle}</span>\n        </div>\n      )}\n\n      <div\n        className={styles.row}\n        key={index}\n        onClick={() => {\n          reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.panel_row_clicked, {\n            type,\n            index,\n            value,\n          });\n          locationService.push(url);\n        }}\n      >\n        <div className={'rowLabel'}>{label}</div>\n\n        <div className={styles.action}>\n          <span className={styles.actionText}>{value}</span>\n          <Icon className={styles.actionIcon} name=\"arrow-right\" size=\"xl\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    rowHeader: css({\n      color: theme.colors.text.secondary,\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      padding: `0 ${theme.spacing(2)} ${theme.spacing(1)} ${theme.spacing(2)}`,\n    }),\n    valueTitle: css({\n      margin: '0 45px 0 0',\n    }),\n    row: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      gap: theme.spacing(2),\n      padding: `${theme.spacing(0.75)} ${theme.spacing(2)}`,\n\n      '&:hover': {\n        backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n        cursor: 'pointer',\n        '.rowLabel': {\n          textDecoration: 'underline',\n        },\n      },\n    }),\n    action: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    actionText: css({\n      color: '#d5983c',\n      padding: `0 ${theme.spacing(1)}`,\n      width: 'max-content',\n    }),\n    actionIcon: css({\n      cursor: 'pointer',\n      margin: `0 ${theme.spacing(0.5)} 0 ${theme.spacing(1)}`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const ErroredServicesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const getLabel = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return valuesField?.labels?.['resource.service.name'].replace(/\"/g, '') ?? 'Service name not found';\n  };\n\n  const getUrl = (df: DataFrame) => {\n    const serviceName = getLabel(df);\n    const params = {\n      'var-filters': `resource.service.name|=|${serviceName}`,\n      'var-metric': 'errors',\n    };\n    return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n  };\n\n  const getTotalErrs = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return (\n      valuesField?.values?.reduce((x, acc) => {\n        if (typeof x === 'number' && !isNaN(x)) {\n          return x + acc;\n        }\n        return acc;\n      }, 0) ?? 1\n    );\n  };\n\n  return (\n    <div className={styles.container}>\n      {series\n        .sort((a, b) => getTotalErrs(b) - getTotalErrs(a))\n        .slice(0, 10)\n        ?.map((df, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(df)}\n              labelTitle=\"Service\"\n              value={getTotalErrs(df)}\n              valueTitle=\"Total errors\"\n              url={getUrl(df)}\n            />\n          </span>\n        ))}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, Field, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE, ROUTES } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\nimport { formatDuration } from '../../utils/dates';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const SlowestTracesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const durField = series[0].fields.find((f) => f.name === 'duration');\n  if (durField && durField.values) {\n    const sortedByDuration = durField?.values\n      .map((_, i) => i)\n      ?.sort((a, b) => durField?.values[b] - durField?.values[a]);\n    const sortedFields = series[0].fields.map((f) => {\n      return {\n        ...f,\n        values: sortedByDuration?.map((i) => f.values[i]),\n      };\n    });\n\n    const getLabel = (traceServiceField: Field | undefined, traceNameField: Field | undefined, index: number) => {\n      let label = '';\n      if (traceServiceField?.values[index]) {\n        label = traceServiceField.values[index];\n      }\n      if (traceNameField?.values[index]) {\n        label = label.length === 0 ? traceNameField.values[index] : `${label}: ${traceNameField.values[index]}`;\n      }\n      return label.length === 0 ? 'Trace service & name not found' : label;\n    };\n\n    const getUrl = (\n      traceId: string,\n      spanIdField: Field | undefined,\n      traceServiceField: Field | undefined,\n      index: number\n    ) => {\n      if (!spanIdField || !spanIdField.values[index] || !traceServiceField || !traceServiceField.values[index]) {\n        console.error('SpanId or traceService not found');\n        return ROUTES.Explore;\n      }\n\n      const params = {\n        traceId,\n        spanId: spanIdField.values[index],\n        'var-filters': `resource.service.name|=|${traceServiceField.values[index]}`,\n        'var-metric': 'duration',\n      };\n\n      return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n    };\n\n    const getDuration = (durationField: Field | undefined, index: number) => {\n      if (!durationField || !durationField.values) {\n        return 'Duration not found';\n      }\n\n      return formatDuration(durationField.values[index] / 1000);\n    };\n\n    const traceIdField = sortedFields.find((f) => f.name === 'traceIdHidden');\n    const spanIdField = sortedFields.find((f) => f.name === 'spanID');\n    const traceNameField = sortedFields.find((f) => f.name === 'traceName');\n    const traceServiceField = sortedFields.find((f) => f.name === 'traceService');\n    const durationField = sortedFields.find((f) => f.name === 'duration');\n\n    return (\n      <div className={styles.container}>\n        {traceIdField?.values?.map((traceId, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(traceServiceField, traceNameField, index)}\n              labelTitle=\"Trace\"\n              value={getDuration(durationField, index)}\n              valueTitle=\"Duration\"\n              url={getUrl(traceId, spanIdField, traceServiceField, index)}\n            />\n          </span>\n        ))}\n      </div>\n    );\n  }\n  return null;\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\nimport { formatDuration } from '../../utils/dates';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const SlowestServicesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const getLabel = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return valuesField?.labels?.['resource.service.name'].replace(/\"/g, '') ?? 'Service name not found';\n  };\n\n  const getUrl = (df: DataFrame) => {\n    const serviceName = getLabel(df);\n    const params = {\n      'var-filters': `resource.service.name|=|${serviceName}`,\n      'var-metric': 'duration',\n    };\n    return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n  };\n\n  const getDuration = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return (\n      valuesField?.values?.reduce((x, acc) => {\n        if (typeof x === 'number' && !isNaN(x)) {\n          return x + acc;\n        }\n        return acc;\n      }, 0) ?? 1\n    );\n  };\n\n  return (\n    <div className={styles.container}>\n      {series\n        .sort((a, b) => getDuration(b) - getDuration(a))\n        .slice(0, 10)\n        ?.map((df, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(df)}\n              labelTitle=\"Service\"\n              value={formatDuration(getDuration(df) * 1000000 /*s to μs*/)}\n              valueTitle=\"p90\"\n              url={getUrl(df)}\n            />\n          </span>\n        ))}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { HomepagePanelType } from './AttributePanel';\nimport { ErroredServicesRows } from './ErroredServicesRows';\nimport { SlowestTracesRows } from './SlowestTracesRows';\nimport { SlowestServicesRows } from './SlowestServicesRows';\n\ntype Props = {\n  series?: DataFrame[];\n  type: HomepagePanelType;\n  message?: string;\n};\n\nexport const AttributePanelRows = (props: Props) => {\n  const { series, type, message } = props;\n  const styles = useStyles2(getStyles);\n\n  if (message) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.message}>\n          <Icon className={styles.icon} name=\"exclamation-circle\" size=\"xl\" />\n          {message}\n        </div>\n      </div>\n    );\n  }\n\n  if (series && series.length > 0) {\n    switch (type) {\n      case 'slowest-traces':\n        return <SlowestTracesRows series={series} type={type} />;\n      case 'errored-services':\n        return <ErroredServicesRows series={series} type={type} />;\n      case 'slowest-services':\n        return <SlowestServicesRows series={series} type={type} />;\n    }\n  }\n  return <div className={styles.container}>No series data</div>;\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n    icon: css({\n      margin: `0 ${theme.spacing(0.5)} 0 ${theme.spacing(1)}`,\n    }),\n    message: css({\n      display: 'flex',\n      gap: theme.spacing(1.5),\n      margin: `${theme.spacing(2)} auto`,\n      width: '60%',\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { AttributePanelRows } from './AttributePanelRows';\nimport { HomepagePanelType } from './AttributePanel';\n\ninterface AttributePanelSceneState extends SceneObjectState {\n  series?: DataFrame[];\n  title: string;\n  type: HomepagePanelType;\n  message?: string;\n}\n\nexport class AttributePanelScene extends SceneObjectBase<AttributePanelSceneState> {\n  public static Component = ({ model }: SceneComponentProps<AttributePanelScene>) => {\n    const { series, title, type, message } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.title}>\n          <Icon name={getIcon(type)} size=\"lg\" />\n          <span className={styles.titleText}>{title}</span>\n        </div>\n        <AttributePanelRows series={series} type={type} message={message} />\n      </div>\n    );\n  };\n}\n\nfunction getIcon(type: HomepagePanelType) {\n  switch (type) {\n    case 'slowest-services':\n      return 'clock-nine';\n    case 'slowest-traces':\n      return 'crosshair';\n    case 'errored-services':\n      return 'exclamation-triangle';\n    default:\n      return 'exclamation-triangle';\n  }\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      border: `1px solid ${theme.isDark ? theme.colors.border.medium : theme.colors.border.weak}`,\n      borderRadius: theme.spacing(0.5),\n      marginBottom: theme.spacing(4),\n      width: '100%',\n    }),\n    title: css({\n      color: theme.isDark ? theme.colors.text.secondary : theme.colors.text.primary,\n      backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      borderTopLeftRadius: theme.spacing(0.5),\n      borderTopRightRadius: theme.spacing(0.5),\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      fontSize: '1.3rem',\n      padding: `${theme.spacing(1.5)} ${theme.spacing(2)}`,\n    }),\n    titleText: css({\n      marginLeft: theme.spacing(1),\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport { GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { explorationDS } from 'utils/shared';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { MINI_PANEL_HEIGHT } from 'components/Explore/TracesByService/TracesByServiceScene';\nimport { AttributePanelScene } from './AttributePanelScene';\nimport Skeleton from 'react-loading-skeleton';\nimport { getErrorMessage, getNoDataMessage } from 'utils/utils';\nimport { getMinimumsForDuration, getYBuckets } from 'components/Explore/TracesByService/REDPanel';\n\nexport type HomepagePanelType = 'errored-services' | 'slowest-services' | 'slowest-traces';\n\nexport interface AttributePanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  query: {\n    query: string;\n    step?: string;\n  };\n  title: string;\n  type: HomepagePanelType;\n  renderDurationPanel?: boolean;\n  filter?: string;\n}\n\nexport class AttributePanel extends SceneObjectBase<AttributePanelState> {\n  constructor(state: AttributePanelState) {\n    super({\n      $data: new SceneQueryRunner({\n        datasource: explorationDS,\n        queries: [{ refId: 'A', queryType: 'traceql', tableType: 'spans', limit: 10, ...state.query, exemplars: 0 }],\n      }),\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done || data.data?.state === LoadingState.Streaming) {\n            if (\n              data.data?.state === LoadingState.Done &&\n              (data.data.series.length === 0 || data.data.series[0].length === 0)\n            ) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new AttributePanelScene({\n                      message: getNoDataMessage(state.title.toLowerCase()),\n                      title: state.title,\n                      type: state.type,\n                    }),\n                  ],\n                }),\n              });\n            } else if (data.data.series.length > 0) {\n              if (state.type !== 'slowest-traces' || state.renderDurationPanel) {\n                this.setState({\n                  panel: new SceneFlexLayout({\n                    children: [\n                      new AttributePanelScene({\n                        series: data.data.series,\n                        title: state.title,\n                        type: state.type,\n                      }),\n                    ],\n                  }),\n                });\n              } else if (data.data?.state === LoadingState.Done) {\n                let yBuckets = getYBuckets(data.data?.series ?? []);\n                if (yBuckets?.length) {\n                  const { minDuration } = getMinimumsForDuration(yBuckets);\n\n                  this.setState({\n                    panel: new SceneFlexLayout({\n                      children: [\n                        new AttributePanel({\n                          query: {\n                            query: `{nestedSetParent<0 && duration > ${minDuration} ${state.filter ?? ''}}`,\n                          },\n                          title: state.title,\n                          type: state.type,\n                          renderDurationPanel: true,\n                        }),\n                      ],\n                    }),\n                  });\n                }\n              }\n            }\n          } else if (data.data?.state === LoadingState.Error) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                children: [\n                  new AttributePanelScene({\n                    message: getErrorMessage(data),\n                    title: state.title,\n                    type: state.type,\n                  }),\n                ],\n              }),\n            });\n          } else {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<AttributePanel>) => {\n    const { panel } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.container}>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles() {\n  return {\n    container: css({\n      minWidth: '350px',\n      width: '-webkit-fill-available',\n    }),\n  };\n}\n\nexport const SkeletonComponent = () => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.title}>\n        <Skeleton count={1} width={200} />\n      </div>\n      <div className={styles.tracesContainer}>\n        {[...Array(11)].map((_, i) => (\n          <div className={styles.row} key={i}>\n            <div className={styles.rowLeft}>\n              <Skeleton count={1} />\n            </div>\n            <div className={styles.rowRight}>\n              <Skeleton count={1} />\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      border: `1px solid ${theme.isDark ? theme.colors.border.medium : theme.colors.border.weak}`,\n      borderRadius: theme.spacing(0.5),\n      marginBottom: theme.spacing(4),\n      width: '100%',\n    }),\n    title: css({\n      color: theme.colors.text.secondary,\n      backgroundColor: theme.colors.background.secondary,\n      fontSize: '1.3rem',\n      padding: `${theme.spacing(1.5)} ${theme.spacing(2)}`,\n      textAlign: 'center',\n    }),\n    tracesContainer: css({\n      padding: `13px ${theme.spacing(2)}`,\n    }),\n    row: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    rowLeft: css({\n      margin: '7px 0',\n      width: '150px',\n    }),\n    rowRight: css({\n      width: '50px',\n    }),\n  };\n}\n", "import React from \"react\";\n\nexport const LightModeRocket = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"73\" height=\"72\" viewBox=\"0 0 73 72\" fill=\"none\">\n    <path\n      d=\"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z\"\n      fill=\"#24292E\"\n      fillOpacity=\"0.75\"\n    />\n  </svg>\n);\n\nexport const DarkModeRocket = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"73\" height=\"72\" viewBox=\"0 0 73 72\" fill=\"none\">\n    <path\n      d=\"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z\"\n      fill=\"#CCCCDC\"\n      fillOpacity=\"0.65\"\n    />\n  </svg>\n);\n", "import { ACTION_VIEW, PRIMARY_SIGNAL, VAR_FILTERS, FILTER_SEPARATOR, BOOKMARKS_LS_KEY, EXPLORATIONS_ROUTE, VAR_LATENCY_PARTIAL_THRESHOLD, VAR_LATENCY_THRESHOLD, SELECTION, VAR_METRIC } from \"utils/shared\";\nimport { Bookmark } from \"./Bookmarks\";\nimport { urlUtil } from \"@grafana/data\";\nimport { locationService, usePluginUserStorage } from '@grafana/runtime';\nimport { USER_EVENTS_ACTIONS, USER_EVENTS_PAGES, reportAppInteraction } from \"utils/analytics\";\n\ntype PluginStorage = ReturnType<typeof usePluginUserStorage>;\n\nconst cleanupParams = (params: URLSearchParams) => {\n  // Remove selection, latency threshold, and latency partial threshold because\n  // selection keeps changing as time moves on, so it's not a good match for bookmarking\n  params.delete(SELECTION);\n  params.delete(`var-${VAR_LATENCY_THRESHOLD}`);\n  params.delete(`var-${VAR_LATENCY_PARTIAL_THRESHOLD}`);\n  return params;\n}\n\nexport const useBookmarksStorage = () => {\n  const storage = usePluginUserStorage();\n  \n  return {\n    getBookmarks: () => getBookmarks(storage),\n    removeBookmark: (bookmark: Bookmark) => removeBookmark(storage, bookmark),\n    bookmarkExists: (bookmark: Bookmark) => bookmarkExists(storage, bookmark),\n    toggleBookmark: () => toggleBookmark(storage),\n  };\n};\n\nexport const getBookmarkParams = (bookmark: Bookmark) => {\n  if (!bookmark || !bookmark.params) {\n    return { actionView: '', primarySignal: '', filters: '', metric: '' };\n  }\n  \n  const params = new URLSearchParams(bookmark.params);\n  const actionView = params.get(ACTION_VIEW) ?? '';\n  const primarySignal = params.get(PRIMARY_SIGNAL) ?? '';\n  const filters = params.getAll(`var-${VAR_FILTERS}`).join(FILTER_SEPARATOR);\n  const metric = params.get(`var-${VAR_METRIC}`) ?? '';\n  return { actionView, primarySignal, filters, metric };\n}\n\nexport const getBookmarkFromURL = (): Bookmark => {\n  const params = cleanupParams(new URLSearchParams(window.location.search));\n  return { params: params.toString() };\n}\n\nexport const getBookmarkForUrl = (bookmark: Bookmark): string => {\n  if (!bookmark || !bookmark.params) {\n    return EXPLORATIONS_ROUTE;\n  }\n  \n  const params = new URLSearchParams(bookmark.params);\n  const urlQueryMap = Object.fromEntries(params.entries());\n  \n  const filters = params.getAll(`var-${VAR_FILTERS}`); \n  \n  const url = urlUtil.renderUrl(EXPLORATIONS_ROUTE, {\n    ...urlQueryMap,\n    [`var-${VAR_FILTERS}`]: filters // Filters need to be added as separate params in the url as there are multiple filters with the same key\n  });\n  \n  return url;\n}\n\nconst setBookmarks = async (storage: PluginStorage, bookmarks: Bookmark[]): Promise<void> => {\n  try {\n    await storage.setItem(BOOKMARKS_LS_KEY, JSON.stringify(bookmarks));\n  } catch (e) {\n    console.error(\"Failed to save bookmarks to storage:\", e);\n  }\n};\n\nexport const getBookmarks = async (storage: PluginStorage): Promise<Bookmark[]> => {\n  try {\n    const value = await storage.getItem(BOOKMARKS_LS_KEY);\n    if (value) {\n      return JSON.parse(value);\n    }\n    return [];\n  } catch (e) {\n    console.error(\"Failed to get bookmarks from storage:\", e);\n    return [];\n  }\n};\n\nexport const toggleBookmark = async (storage: PluginStorage): Promise<boolean> => {\n  const bookmark = getBookmarkFromURL();\n  const exists = await bookmarkExists(storage, bookmark);\n  \n  if (exists) {\n    await removeBookmark(storage, bookmark);\n    return false;\n  } else {\n    await addBookmark(storage, bookmark);\n    return true;\n  }\n};\n\nconst addBookmark = async (storage: PluginStorage, bookmark: Bookmark): Promise<void> => {\n  const bookmarks = await getBookmarks(storage);\n  bookmarks.push(bookmark);\n  await setBookmarks(storage, bookmarks);\n};\n\nexport const removeBookmark = async (storage: PluginStorage, bookmark: Bookmark): Promise<void> => {\n  const storedBookmarks = await getBookmarks(storage);\n  const filteredBookmarks = storedBookmarks.filter((storedBookmark) => !areBookmarksEqual(bookmark, storedBookmark));\n  await setBookmarks(storage, filteredBookmarks);\n};\n\nexport const bookmarkExists = async (storage: PluginStorage, bookmark: Bookmark): Promise<boolean> => {\n  const bookmarks = await getBookmarks(storage);\n  return bookmarks.some((b) => areBookmarksEqual(bookmark, b));\n};\n\nexport const areBookmarksEqual = (bookmark: Bookmark, storedBookmark: Bookmark) => {\n  const bookmarkParams = cleanupParams(new URLSearchParams(bookmark.params));\n  const storedBookmarkParams = cleanupParams(new URLSearchParams(storedBookmark.params));\n\n  const filterKey = `var-${VAR_FILTERS}`;\n  const bookmarkKeys = Array.from(bookmarkParams.keys()).filter(k => k !== filterKey);\n  const storedKeys = Array.from(storedBookmarkParams.keys()).filter(k => k !== filterKey);\n\n  // If they have different number of keys (excluding filters), they can't be equal\n  if (bookmarkKeys.length !== storedKeys.length) {\n    return false;\n  }\n  \n  // Check if every key in bookmarkParams exists in storedBookmarkParams with the same value\n  const allKeysMatch = bookmarkKeys.every(key => \n    storedBookmarkParams.has(key) && bookmarkParams.get(key) === storedBookmarkParams.get(key)\n  );  \n  if (!allKeysMatch) {\n    return false;\n  }\n  \n  // Compare filters (which can have multiple values with the same key)\n  const bookmarkFilters = bookmarkParams.getAll(filterKey);\n  const storedFilters = storedBookmarkParams.getAll(filterKey);  \n  if (bookmarkFilters.length !== storedFilters.length) {\n    return false;\n  }\n  \n  // Check if every filter in bookmarkFilters exists in storedFilters\n  // This handles cases where order might be different\n  return bookmarkFilters.every(filter => storedFilters.includes(filter));\n}\n\nexport const goToBookmark = (bookmark: Bookmark) => {\n  reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.go_to_bookmark_clicked);\n  const url = getBookmarkForUrl(bookmark);\n  locationService.push(url);\n}\n", "import { EVENT_ATTR, FILTER_SEPARATOR, RESOURCE_ATTR, SPAN_ATTR } from \"utils/shared\";\nimport React from \"react\";\nimport { capitalizeFirstChar } from \"utils/utils\";\nimport { css } from \"@emotion/css\";\nimport { useStyles2 } from \"@grafana/ui\";\nimport { Bookmark } from \"./Bookmarks\";\nimport { getBookmarkParams } from \"./utils\";\nimport { getSignalForKey } from \"pages/Explore/primary-signals\";\n\nexport const BookmarkItem = ({ bookmark }: { bookmark: Bookmark }) => {\n  let { actionView, primarySignal, metric, filters } = getBookmarkParams(bookmark);\n  const styles = useStyles2(getStyles);\n\n  const getPrimarySignalFilter = (primarySignal: string): string => {\n    const signalData = getSignalForKey(primarySignal);\n    if (!signalData || !signalData.filter) {\n      return '';\n    }\n    const filter = signalData.filter;\n\n    if (filter.key && filter.operator && filter.value !== undefined) {\n      return `${filter.key}|${filter.operator}|${filter.value}`;\n    }\n    return '';\n  }\n  \n  // Don't render the primary signal filter as the primary signal already represents this information\n  const getFiltersWithoutPrimarySignal = (filters: string, primarySignal: string): string => {\n    const primarySignalFilter = getPrimarySignalFilter(primarySignal);\n    let filtersArray = filters.split(FILTER_SEPARATOR);\n    filtersArray = filtersArray.filter(f => f !== primarySignalFilter);\n    return filtersArray.join(FILTER_SEPARATOR);\n  }\n\n  filters = getFiltersWithoutPrimarySignal(filters, primarySignal);\n  filters = filters.replace(/\\|=\\|/g, ' = ');\n  filters = filters.replace(RESOURCE_ATTR, '').replace(SPAN_ATTR, '').replace(EVENT_ATTR, '');\n\n  return (\n    <div title={filters}>\n      <div>\n        <b>{capitalizeFirstChar(metric)}</b> of <b>{primarySignal.replace('_', ' ')}</b> ({actionView})\n      </div>\n      <div className={styles.filters}>\n        {filters}\n      </div>\n    </div>\n  );\n}\n\nfunction getStyles() {\n  return {\n    filters: css({\n      textOverflow: 'ellipsis', \n      overflow: 'hidden',\n      WebkitLineClamp: 2, \n      display: '-webkit-box', \n      WebkitBoxOrient: 'vertical'\n    }),\n  }\n}\n", "import { css } from \"@emotion/css\";\nimport { GrafanaTheme2 } from \"@grafana/data\";\nimport { Button, useStyles2, LoadingPlaceholder } from \"@grafana/ui\";\nimport React, { useEffect, useState } from \"react\";\nimport { BookmarkItem } from \"./BookmarkItem\";\nimport { useBookmarksStorage, goToBookmark } from \"./utils\";\n\nexport type Bookmark = {\n  params: string;\n}\n\nexport const Bookmarks = () => {\n  const styles = useStyles2(getStyles);\n  const { getBookmarks, removeBookmark } = useBookmarksStorage();\n  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [isRemoving, setIsRemoving] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchBookmarks = async () => {\n      setIsLoading(true);\n      try {\n        const loadedBookmarks = await getBookmarks();\n        setBookmarks(loadedBookmarks);\n      } catch (error) {\n        console.error('Error loading bookmarks:', error);\n        setBookmarks([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    fetchBookmarks();\n  }, []);\n\n  const removeBookmarkClicked = async (bookmark: Bookmark, event: React.MouseEvent) => {\n    event.stopPropagation();\n    setIsRemoving(true);\n    \n    try {\n      await removeBookmark(bookmark);\n      const updatedBookmarks = await getBookmarks();\n      setBookmarks(updatedBookmarks);\n    } catch (error) {\n      console.error('Error removing bookmark:', error);\n    } finally {\n      setIsRemoving(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div>\n        <div className={styles.header}>\n          <h4>Or view bookmarks</h4>\n        </div>\n        <div className={styles.loading}>\n          <LoadingPlaceholder text=\"Loading bookmarks...\" />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className={styles.header}>\n        <h4>Or view bookmarks</h4>\n      </div>\n      {bookmarks.length === 0 ? (\n        <p className={styles.noBookmarks}>Bookmark your favorite queries to view them here.</p>\n      ) : (\n        <div className={styles.bookmarks}>\n          {bookmarks.map((bookmark: Bookmark, i: number) => (\n            <div \n              className={styles.bookmark} \n              key={i} \n              onClick={() => goToBookmark(bookmark)}\n            >\n              <div className={styles.bookmarkItem}>\n                <BookmarkItem bookmark={bookmark} />\n              </div>\n              <div className={styles.remove}>\n                <Button \n                  variant='secondary' \n                  fill='text' \n                  icon='trash-alt'\n                  disabled={isRemoving}\n                  onClick={(e) => removeBookmarkClicked(bookmark, e)}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    header: css({\n      textAlign: 'center',\n      'h4': {\n        margin: 0,\n      }\n    }),\n    bookmarks: css({\n      display: 'flex',\n      flexWrap: 'wrap',\n      gap: theme.spacing(2),\n      margin: `${theme.spacing(4)} 0 ${theme.spacing(2)} 0`,\n      justifyContent: 'center',\n    }),\n    bookmark: css({\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'space-between',\n      cursor: 'pointer',\n      width: '318px',\n      border: `1px solid ${theme.colors.border.medium}`,\n      borderRadius: theme.shape.radius.default,\n\n      '&:hover': {\n        backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      }\n    }),\n    bookmarkItem: css({\n      padding: `${theme.spacing(1.5)} ${theme.spacing(1.5)} 0 ${theme.spacing(1.5)}`,\n      overflow: 'hidden'\n    }),\n    filters: css({\n      textOverflow: 'ellipsis', \n      overflow: 'hidden',\n      WebkitLineClamp: 2, \n      display: '-webkit-box', \n      WebkitBoxOrient: 'vertical'\n    }),\n    remove: css({\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    noBookmarks: css({\n      margin: `${theme.spacing(4)} 0 ${theme.spacing(2)} 0`,\n      textAlign: 'center',\n    }),\n    loading: css({\n      display: 'flex',\n      justifyContent: 'center',\n      margin: `${theme.spacing(4)} 0`,\n    }),\n  }\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport {\n  SceneComponentProps,\n  SceneObjectBase,\n} from '@grafana/scenes';\nimport { Button, Icon, LinkButton, Stack, useStyles2, useTheme2 } from '@grafana/ui';\n\nimport {\n  EXPLORATIONS_ROUTE,\n} from '../../utils/shared';\nimport { getDatasourceVariable, getHomeFilterVariable, getHomeScene } from '../../utils/utils';\nimport { DarkModeRocket, LightModeRocket } from '../../utils/rockets';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { Home } from 'pages/Home/Home';\nimport { useNavigate } from 'react-router-dom';\nimport { Bookmarks } from 'pages/Home/bookmarks/Bookmarks';\n\nexport class HeaderScene extends SceneObjectBase {\n  public static Component = ({ model }: SceneComponentProps<Home>) => {\n    const home = getHomeScene(model);\n    const navigate = useNavigate();\n    const { controls } = home.useState();\n    const styles = useStyles2(getStyles);\n    const theme = useTheme2();\n\n    const dsVariable = getDatasourceVariable(home);\n    const filterVariable = getHomeFilterVariable(home);\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <div className={styles.headerTitleContainer}>\n            {theme.isDark ? <DarkModeRocket /> : <LightModeRocket />}\n            <h2 className={styles.title}>Start your traces exploration!</h2>\n          </div>\n          <div>\n            <p>Drilldown and visualize your trace data without writing a query.</p>\n            <div className={styles.headerActions}>\n              <Button variant='primary' onClick={() => {\n                  reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.explore_traces_clicked);\n                  navigate(EXPLORATIONS_ROUTE);\n                }}>\n                Let’s start\n                <Icon name='arrow-right' size='lg' />\n              </Button>\n              <LinkButton\n                icon=\"external-link-alt\"\n                fill=\"text\"\n                size={'md'}\n                target={'_blank'}\n                href={\n                  'https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces'\n                }\n                className={styles.documentationLink}\n                onClick={() => reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.read_documentation_clicked)}\n              >\n                Read documentation\n              </LinkButton>\n            </div>\n          </div>\n        </div>\n\n        <Bookmarks />\n\n        <div className={styles.subHeader}>\n          <h4>Or quick-start into your tracing data</h4>\n        </div>\n\n        <Stack gap={2}>\n          <div className={styles.variablesAndControls}>\n            <div className={styles.variables}>\n              {dsVariable && (\n                <Stack gap={1} alignItems={'center'}>\n                  <div className={styles.label}>Data source</div>\n                  <dsVariable.Component model={dsVariable} />\n                </Stack>\n              )}\n              {filterVariable && (\n                <Stack gap={1} alignItems={'center'}>\n                  <div className={styles.label}>Filter</div>\n                  <filterVariable.Component model={filterVariable} />\n                </Stack>\n              )}\n            </div>\n\n            <div className={styles.controls}>\n              {controls?.map((control) => (\n                <control.Component key={control.state.key} model={control} />\n              ))}\n            </div>\n          </div>\n        </Stack>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'flex',\n      gap: theme.spacing(7),\n      flexDirection: 'column',\n      margin: `0 0 ${theme.spacing(4)} 0`,\n      justifyContent: 'center',\n    }),\n    header: css({\n      display: 'flex',\n      alignItems: 'center',\n      backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      borderRadius: theme.spacing(0.5),\n      flexWrap: 'wrap',\n      justifyContent: 'center',\n      padding: theme.spacing(3),\n      gap: theme.spacing(4),\n    }),\n    headerTitleContainer: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    title: css({\n      margin: `0 0 0 ${theme.spacing(2)}`,\n    }),\n\n    headerActions: css({\n      alignItems: 'center',\n      justifyContent: 'flex-start',\n      display: 'flex',\n      gap: theme.spacing(2),\n    }),\n    documentationLink: css({\n      textDecoration: 'underline',\n      '&:hover': {\n        textDecoration: 'underline',\n      },\n    }),\n\n    subHeader: css({\n      textAlign: 'center',\n      'h4': {\n        margin: `0 0 -${theme.spacing(2)} 0`,\n      }\n    }),\n\n    label: css({\n      fontSize: '12px',\n    }),\n    variablesAndControls: css({\n      alignItems: 'center',\n      gap: theme.spacing(2),\n      display: 'flex',\n      justifyContent: 'space-between',\n      width: '100%',\n    }),\n    variables: css({\n      display: 'flex',\n      gap: theme.spacing(2),\n    }),\n    controls: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n", "import { AdHocVariableFilter, MetricFindValue } from \"@grafana/data\";\nimport { getDataSourceSrv, DataSourceWithBackend } from \"@grafana/runtime\";\nimport { AdHocFiltersVariable, sceneGraph } from \"@grafana/scenes\";\nimport { EVENT_ATTR, EVENT_INTRINSIC, FILTER_SEPARATOR, ignoredAttributes, ignoredAttributesHomeFilter, RESOURCE_ATTR, SPAN_ATTR, VAR_DATASOURCE_EXPR } from \"utils/shared\";\nimport { isNumber } from \"utils/utils\";\n\nexport async function getTagKeysProvider(variable: AdHocFiltersVariable): Promise<{replace?: boolean, values: MetricFindValue[]}> {\n  const dsVar = sceneGraph.interpolate(variable, VAR_DATASOURCE_EXPR);\n  const datasource_ = await getDataSourceSrv().get(dsVar);\n  if (!(datasource_ instanceof DataSourceWithBackend)) {\n    console.error(new Error('getTagKeysProvider: invalid datasource!'));\n    throw new Error('getTagKeysProvider: invalid datasource!');\n  }\n  \n  const datasource = datasource_ as DataSourceWithBackend;\n  if (datasource && datasource.getTagKeys) {\n    const tagKeys = await datasource.getTagKeys();\n\n    if (Array.isArray(tagKeys)) {\n      const filteredKeys = filterKeys(tagKeys);\n      return { replace: true, values: filteredKeys };\n    } else {\n      console.error(new Error('getTagKeysProvider: invalid tagKeys!'));\n      return { values: [] };\n    }\n  } else {\n    console.error(new Error('getTagKeysProvider: missing or invalid datasource!'));\n    return { values: [] };\n  }\n}\n\nexport function filterKeys(keys: MetricFindValue[]): MetricFindValue[] {\n  const resourceAttributes = keys.filter((k) => k.text?.includes(RESOURCE_ATTR));\n  const spanAttributes = keys.filter((k) => k.text?.includes(SPAN_ATTR));\n  const otherAttributes = keys.filter((k) => {\n    return !k.text?.includes(RESOURCE_ATTR) && !k.text?.includes(SPAN_ATTR)\n      && !k.text?.includes(EVENT_ATTR) && !k.text?.includes(EVENT_INTRINSIC)\n      && ignoredAttributes.concat(ignoredAttributesHomeFilter).indexOf(k.text!) === -1;\n  })\n  return [...resourceAttributes, ...spanAttributes, ...otherAttributes];\n}\n\nexport function renderTraceQLLabelFilters(filters: AdHocVariableFilter[]) {\n  const expr = filters\n    .filter((f) => f.key && f.operator && f.value)\n    .map((filter) => renderFilter(filter))\n    .join(FILTER_SEPARATOR);\n  return expr.length ? `&& ${expr}` : '';\n}\n\nconst renderFilter = (filter: AdHocVariableFilter) => {\n  if (!filter) {\n    return '';\n  } \n  \n  let val = filter.value;\n  if (val === undefined || val === null || val === '') {\n    return '';\n  }\n\n  if (!isNumber.test(val) && !['kind'].includes(filter.key)) {\n    if (typeof val === 'string' && !val.startsWith('\"') && !val.endsWith('\"')) {\n      val = `\"${val}\"`;\n    }\n  }\n\n  return `${filter.key}${filter.operator}${val}`;\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n// eslint-disable-next-line no-restricted-imports\nimport { duration } from 'moment';\n\nimport { AdHocVariableFilter, GrafanaTheme2 } from '@grafana/data';\nimport {\n  AdHocFiltersVariable,\n  DataSourceVariable,\n  SceneComponentProps,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneRefreshPicker,\n  SceneTimePicker,\n  SceneTimeRange,\n  SceneTimeRangeLike,\n  SceneVariableSet,\n} from '@grafana/scenes';\nimport { useStyles2 } from '@grafana/ui';\n\nimport {\n  DATASOURCE_LS_KEY,\n  explorationDS,\n  HOMEPAGE_FILTERS_LS_KEY,\n  VAR_DATASOURCE,\n  VAR_HOME_FILTER,\n} from '../../utils/shared';\nimport { AttributePanel } from 'components/Home/AttributePanel';\nimport { HeaderScene } from 'components/Home/HeaderScene';\nimport { getDatasourceVariable, getHomeFilterVariable } from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\nimport { getTagKeysProvider, renderTraceQLLabelFilters } from './utils';\n\nexport interface HomeState extends SceneObjectState {\n  controls?: SceneObject[];\n  initialDS?: string;\n  initialFilters: AdHocVariableFilter[];\n  body?: SceneCSSGridLayout;\n}\n\nexport class Home extends SceneObjectBase<HomeState> {\n  public constructor(state: HomeState) {\n    super({\n      $timeRange: state.$timeRange ?? new SceneTimeRange({}),\n      $variables: state.$variables ?? getVariableSet(state.initialFilters, state.initialDS),\n      controls: state.controls ?? [new SceneTimePicker({}), new SceneRefreshPicker({})],\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    const filterVariable = getHomeFilterVariable(this);\n    filterVariable.setState({\n      getTagKeysProvider: getTagKeysProvider,\n    });\n\n    getDatasourceVariable(this).subscribeToState((newState) => {\n      if (newState.value) {\n        localStorage.setItem(DATASOURCE_LS_KEY, newState.value.toString());\n      }\n    });\n\n    getHomeFilterVariable(this).subscribeToState((newState, prevState) => {\n      if (newState.filters !== prevState.filters) {\n        this.buildPanels(sceneTimeRange, newState.filters);\n\n        // save the filters to local storage\n        localStorage.setItem(HOMEPAGE_FILTERS_LS_KEY, JSON.stringify(newState.filters));\n\n        const newFilters = newState.filters.filter((f) => !prevState.filters.find((pf) => pf.key === f.key));\n        if (newFilters.length > 0) {\n          reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.filter_changed, {\n            key: newFilters[0].key,\n          });\n        }\n      }\n    });\n\n    sceneTimeRange.subscribeToState((newState, prevState) => {\n      if (newState.value.from !== prevState.value.from || newState.value.to !== prevState.value.to) {\n        this.buildPanels(sceneTimeRange, filterVariable.state.filters);\n      }\n    });\n    this.buildPanels(sceneTimeRange, filterVariable.state.filters);\n  }\n\n  buildPanels(sceneTimeRange: SceneTimeRangeLike, filters: AdHocVariableFilter[]) {\n    const from = sceneTimeRange.state.value.from.unix();\n    const to = sceneTimeRange.state.value.to.unix();\n    const dur = duration(to - from, 's');\n    const durString = `${dur.asSeconds()}s`;\n    const renderedFilters = renderTraceQLLabelFilters(filters);\n\n    this.setState({\n      body: new SceneCSSGridLayout({\n        children: [\n          new SceneCSSGridLayout({\n            autoRows: 'min-content',\n            columnGap: 2,\n            rowGap: 2,\n            children: [\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent < 0 && status = error ${renderedFilters}} | count_over_time() by (resource.service.name)`,\n                    step: durString,\n                  },\n                  title: 'Errored services',\n                  type: 'errored-services',\n                }),\n              }),\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent < 0 ${renderedFilters}} | quantile_over_time(duration, 0.9) by (resource.service.name)`,\n                    step: durString,\n                  },\n                  title: 'Slow services',\n                  type: 'slowest-services',\n                }),\n              }),\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent<0 ${renderedFilters}} | histogram_over_time(duration)`,\n                  },\n                  title: 'Slow traces',\n                  type: 'slowest-traces',\n                  filter: renderedFilters,\n                }),\n              }),\n            ],\n          }),\n        ],\n      }),\n    });\n  }\n\n  static Component = ({ model }: SceneComponentProps<Home>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <HeaderScene.Component model={model} />\n        {body && <body.Component model={body} />}\n      </div>\n    );\n  };\n}\n\nfunction getVariableSet(initialFilters: AdHocVariableFilter[], initialDS?: string) {\n  return new SceneVariableSet({\n    variables: [\n      new DataSourceVariable({\n        name: VAR_DATASOURCE,\n        label: 'Data source',\n        value: initialDS,\n        pluginId: 'tempo',\n      }),\n      new AdHocFiltersVariable({\n        name: VAR_HOME_FILTER,\n        datasource: explorationDS,\n        layout: 'combobox',\n        filters: initialFilters,\n        allowCustomValue: true,\n      }),\n    ],\n  });\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      margin: `${theme.spacing(4)} auto`,\n      width: '75%',\n\n      '@media (max-width: 900px)': {\n        width: '95%',\n      },\n    }),\n  };\n}\n", "import { AdHocVariableFilter, DataFrame, urlUtil } from '@grafana/data';\nimport {\n  AdHocFiltersVariable,\n  CustomVariable,\n  DataSourceVariable,\n  SceneDataQuery,\n  SceneDataState,\n  sceneGraph,\n  SceneObject,\n  SceneObjectUrlValues,\n  SceneTimeRange,\n  sceneUtils,\n} from '@grafana/scenes';\n\nimport { TraceExploration } from '../pages/Explore';\nimport {\n  EventTraceOpened,\n  EXPLORATIONS_ROUTE,\n  VAR_DATASOURCE,\n  VAR_DATASOURCE_EXPR,\n  VAR_FILTERS,\n  VAR_GROUPBY,\n  VAR_HOME_FILTER,\n  VAR_LATENCY_PARTIAL_THRESHOLD,\n  VAR_LATENCY_THRESHOLD,\n  VAR_METRIC,\n  VAR_PRIMARY_SIGNAL,\n  VAR_SPAN_LIST_COLUMNS,\n} from './shared';\nimport { TracesByServiceScene } from 'components/Explore/TracesByService/TracesByServiceScene';\nimport { Home } from 'pages/Home/Home';\nimport { PrimarySignalVariable } from 'pages/Explore/PrimarySignalVariable';\nimport { ActionViewType } from 'exposedComponents/types';\nimport { ExceptionsScene } from 'components/Explore/TracesByService/Tabs/Exceptions/ExceptionsScene';\n\nexport function getTraceExplorationScene(model: SceneObject): TraceExploration {\n  return sceneGraph.getAncestor(model, TraceExploration);\n}\n\nexport function getHomeScene(model: SceneObject): Home {\n  return sceneGraph.getAncestor(model, Home);\n}\n\nexport function getTraceByServiceScene(model: SceneObject): TracesByServiceScene {\n  return sceneGraph.getAncestor(model, TracesByServiceScene);\n}\n\nexport function getExceptionsScene(model: SceneObject): ExceptionsScene | undefined {\n  const tracesByServiceScene = getTraceByServiceScene(model);\n  return tracesByServiceScene?.state.exceptionsScene;\n}\n\nexport function newTracesExploration(initialDS?: string, initialFilters?: AdHocVariableFilter[]): TraceExploration {\n  return new TraceExploration({\n    initialDS,\n    initialFilters: initialFilters ?? [],\n    $timeRange: new SceneTimeRange({ from: 'now-30m', to: 'now' }),\n  });\n}\n\nexport function newHome(initialFilters: AdHocVariableFilter[], initialDS?: string): Home {\n  return new Home({\n    initialDS,\n    initialFilters,\n    $timeRange: new SceneTimeRange({ from: 'now-30m', to: 'now' }),\n  });\n}\n\nexport function getErrorMessage(data: SceneDataState) {\n  return data?.data?.error?.message ?? 'There are no Tempo data sources';\n}\n\nexport function getNoDataMessage(context: string) {\n  return `No data for selected data source and filter. Select another to see ${context}.`;\n}\n\nexport function getUrlForExploration(exploration: TraceExploration) {\n  const params = sceneUtils.getUrlState(exploration);\n  return getUrlForValues(params);\n}\n\nexport function getUrlForValues(values: SceneObjectUrlValues) {\n  return urlUtil.renderUrl(EXPLORATIONS_ROUTE, values);\n}\n\nexport function getDataSource(exploration: TraceExploration) {\n  return sceneGraph.interpolate(exploration, VAR_DATASOURCE_EXPR);\n}\n\nexport const getFilterSignature = (filter: AdHocVariableFilter) => {\n  return `${filter.key}${filter.operator}${filter.value}`;\n};\n\nexport function getAttributesAsOptions(attributes: string[]) {\n  return attributes.map((attribute) => ({ label: attribute, value: attribute }));\n}\n\nexport function getLabelKey(frame: DataFrame) {\n  const labels = frame.fields.find((f) => f.type === 'number')?.labels;\n\n  if (!labels) {\n    return 'No labels';\n  }\n\n  const keys = Object.keys(labels);\n  if (keys.length === 0) {\n    return 'No labels';\n  }\n\n  return keys[0].replace(/\"/g, '');\n}\n\nexport function getLabelValue(frame: DataFrame, labelName?: string) {\n  const labels = frame.fields.find((f) => f.type === 'number')?.labels;\n\n  if (!labels) {\n    return 'No labels';\n  }\n\n  const keys = Object.keys(labels).filter((k) => k !== 'p'); // remove the percentile label\n  if (keys.length === 0) {\n    return 'No labels';\n  }\n\n  return labels[labelName || keys[0]].replace(/\"/g, '');\n}\n\nexport function getGroupByVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_GROUPBY, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Group by variable not found');\n  }\n  return variable;\n}\n\nexport function getSpanListColumnsVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_SPAN_LIST_COLUMNS, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Span list columns variable not found');\n  }\n  return variable;\n}\n\nexport function getLatencyThresholdVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_LATENCY_THRESHOLD, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Latency threshold variable not found');\n  }\n  return variable;\n}\n\nexport function getLatencyPartialThresholdVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_LATENCY_PARTIAL_THRESHOLD, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Partial latency threshold variable not found');\n  }\n  return variable;\n}\n\nexport function getMetricVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_METRIC, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Metric variable not found');\n  }\n  return variable;\n}\n\nexport function getFiltersVariable(scene: SceneObject): AdHocFiltersVariable {\n  const variable = sceneGraph.lookupVariable(VAR_FILTERS, scene);\n  if (!(variable instanceof AdHocFiltersVariable)) {\n    throw new Error('Filters variable not found');\n  }\n  return variable;\n}\n\nexport function getPrimarySignalVariable(scene: SceneObject): PrimarySignalVariable {\n  const variable = sceneGraph.lookupVariable(VAR_PRIMARY_SIGNAL, scene);\n  if (!(variable instanceof PrimarySignalVariable)) {\n    throw new Error('Primary signal variable not found');\n  }\n  return variable;\n}\n\nexport function getHomeFilterVariable(scene: SceneObject): AdHocFiltersVariable {\n  const variable = sceneGraph.lookupVariable(VAR_HOME_FILTER, scene);\n  if (!(variable instanceof AdHocFiltersVariable)) {\n    throw new Error('Home filter variable not found');\n  }\n  return variable;\n}\n\nexport function getDatasourceVariable(scene: SceneObject): DataSourceVariable {\n  const variable = sceneGraph.lookupVariable(VAR_DATASOURCE, scene);\n  if (!(variable instanceof DataSourceVariable)) {\n    throw new Error('Datasource variable not found');\n  }\n  return variable;\n}\n\nexport function getCurrentStep(scene: SceneObject): number | undefined {\n  const data = sceneGraph.getData(scene).state.data;\n  const targetQuery = data?.request?.targets[0];\n  return targetQuery ? (targetQuery as SceneDataQuery).step : undefined;\n}\n\nexport function shouldShowSelection(tab?: ActionViewType): boolean {\n  return tab === 'comparison' || tab === 'traceList';\n}\n\nexport function getMetricValue(scene: SceneObject) {\n  return getMetricVariable(scene).useState().value;\n}\n\nexport function fieldHasEmptyValues(data: SceneDataState) {\n  return data?.data?.series[0].fields?.some((v) => v.values.every((e) => e === undefined)) ?? false;\n}\n\nexport const isNumber = /^-?\\d+\\.?\\d*$/;\n\nexport const formatLabelValue = (value: string) => {\n  if (!isNumber.test(value) && typeof value === 'string' && !value.startsWith('\"') && !value.endsWith('\"')) {\n    return `\"${value}\"`;\n  }\n  return value;\n};\n\nexport const capitalizeFirstChar = (str: string) => str?.[0]?.toUpperCase() + str?.slice(1) || '';\n\nexport const getOpenTrace = (scene: SceneObject) => {\n  return (traceId: string, spanId?: string) => {\n    scene.publishEvent(new EventTraceOpened({ traceId, spanId }), true);\n  };\n};\n", "import React from 'react';\n\nimport {\n  SceneObjectState,\n  SceneObjectBase,\n  SceneComponentProps,\n  PanelBuilders,\n  SceneQueryRunner,\n  sceneGraph,\n  SceneObject,\n} from '@grafana/scenes';\nimport { LoadingState, GrafanaTheme2 } from '@grafana/data';\nimport { explorationDS } from 'utils/shared';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { css } from '@emotion/css';\nimport Skeleton from 'react-loading-skeleton';\nimport { useStyles2 } from '@grafana/ui';\n\nexport interface TracePanelState extends SceneObjectState {\n  panel?: SceneObject;\n  traceId: string;\n  spanId?: string;\n}\n\nexport class TraceViewPanelScene extends SceneObjectBase<TracePanelState> {\n  constructor(state: TracePanelState) {\n    super({\n      $data: new SceneQueryRunner({\n        datasource: explorationDS,\n        queries: [{ refId: 'A', query: state.traceId, queryType: 'traceql' }],\n      }),\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done) {\n            this.setState({\n              panel: this.getVizPanel().build(),\n            });\n          } else if (data.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new LoadingStateScene({\n                component: SkeletonComponent,\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  private getVizPanel() {\n    const panel = PanelBuilders.traces().setHoverHeader(true);\n    if (this.state.spanId) {\n      panel.setOption('focusedSpanId' as any, this.state.spanId as any);\n    }\n    return panel;\n  }\n\n  public static Component = ({ model }: SceneComponentProps<TraceViewPanelScene>) => {\n    const { panel } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.panelContainer}>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nconst SkeletonComponent = () => {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.header}>\n        <Skeleton count={1} width={60} />\n        <Skeleton count={1} width={60} />\n      </div>\n      <Skeleton count={2} width={'80%'} />\n      <div className={styles.map}>\n        <Skeleton count={1} />\n        <Skeleton count={1} height={70} />\n      </div>\n\n      <div className={styles.span}>\n        <span className={styles.service1}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar1}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service2}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar2}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service3}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar3}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service4}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar4}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service5}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar5}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service6}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar6}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    panelContainer: css({\n      display: 'flex',\n      height: '100%',\n\n      '& [data-testid=\"data-testid panel content\"] > div': {\n        overflow: 'auto',\n      },\n\n      '& .show-on-hover': {\n        display: 'none',\n      },\n    }),\n    container: css({\n      height: 'calc(100% - 32px)',\n      width: 'calc(100% - 32px)',\n      position: 'absolute',\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.border.weak}`,\n      padding: '5px',\n    }),\n    header: css({\n      marginBottom: '20px',\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    map: css({\n      marginTop: '20px',\n      marginBottom: '20px',\n    }),\n    span: css({\n      display: 'flex',\n    }),\n    service1: css({\n      width: '25%',\n    }),\n    bar1: css({\n      marginLeft: '5%',\n      width: '70%',\n    }),\n    service2: css({\n      width: '25%',\n    }),\n    bar2: css({\n      marginLeft: '10%',\n      width: '15%',\n    }),\n    service3: css({\n      width: '20%',\n      marginLeft: '5%',\n    }),\n    bar3: css({\n      marginLeft: '10%',\n      width: '65%',\n    }),\n    service4: css({\n      width: '20%',\n      marginLeft: '5%',\n    }),\n    bar4: css({\n      marginLeft: '15%',\n      width: '60%',\n    }),\n    service5: css({\n      width: '15%',\n      marginLeft: '10%',\n    }),\n    bar5: css({\n      marginLeft: '20%',\n      width: '35%',\n    }),\n    service6: css({\n      width: '15%',\n      marginLeft: '10%',\n    }),\n    bar6: css({\n      marginLeft: '30%',\n      width: '15%',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps, SceneObject } from '@grafana/scenes';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { TraceViewPanelScene } from '../panels/TraceViewPanelScene';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { getTraceExplorationScene } from '../../../utils/utils';\n\nexport interface DetailsSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class TraceDrawerScene extends SceneObjectBase<DetailsSceneState> {\n  constructor(state: Partial<DetailsSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this.updateBody();\n\n    const traceExploration = getTraceExplorationScene(this);\n\n    traceExploration.subscribeToState((newState, prevState) => {\n      if (newState.traceId !== prevState.traceId || newState.spanId !== prevState.spanId) {\n        this.updateBody();\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.open_trace, {\n          traceId: newState.traceId,\n          spanId: newState.spanId,\n        });\n      }\n    });\n  }\n\n  private updateBody() {\n    const traceExploration = getTraceExplorationScene(this);\n\n    if (traceExploration.state.traceId) {\n      this.setState({\n        body: new TraceViewPanelScene({\n          traceId: traceExploration.state.traceId,\n          spanId: traceExploration.state.spanId,\n        }),\n      });\n    } else {\n      this.setState({\n        body: new EmptyStateScene({\n          message: 'No trace selected',\n        }),\n      });\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<TraceDrawerScene>) => {\n    const { body } = model.useState();   \n    return body && <body.Component model={body} />;\n  };\n}\n", "import { LoadingState, dateTime } from '@grafana/data';\nimport {\n  SceneObjectBase,\n  SceneObjectState,\n  SceneTimeRange,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport { getDatasourceVariable } from '../../utils/utils';\nimport { Alert, LinkButton } from '@grafana/ui';\nimport React from 'react';\n\nexport interface TraceQLIssueDetectorState extends SceneObjectState {\n  hasIssue: boolean;\n}\n\nexport class TraceQLIssueDetector extends SceneObjectBase<TraceQLIssueDetectorState> {\n  constructor() {\n    super({\n      hasIssue: false,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {    \n    this.runIssueDetectionQuery();\n\n    const datasourceVar = getDatasourceVariable(this);\n    this._subs.add(\n      datasourceVar.subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          this.resetIssues();\n          this.runIssueDetectionQuery();\n        }\n      })\n    );\n  }\n\n  private runIssueDetectionQuery() {\n    const datasourceVar = getDatasourceVariable(this);\n    \n    // Create a minimal time range to reduce resource usage\n    const now = dateTime();\n    const from = dateTime(now).subtract(1, 'minute');\n    const minimalTimeRange = new SceneTimeRange({\n      from: from.toISOString(),\n      to: now.toISOString(),\n    });\n    \n    const issueDetector = new SceneQueryRunner({\n      maxDataPoints: 1,\n      datasource: { uid: String(datasourceVar.state.value) },\n      $timeRange: minimalTimeRange,\n      queries: [{\n        refId: 'issueDetectorQuery',\n        query: '{} | rate()',\n        queryType: 'traceql',\n        tableType: 'spans',\n        limit: 1,\n        spss: 1,\n        filters: [],\n      }],\n    });\n    \n    this._subs.add(\n      issueDetector.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Error) {\n          const message = state.data?.errors?.[0]?.message || '';\n          // This is the error message when the datasource is not configured for TraceQL metrics\n          // https://grafana.com/docs/tempo/latest/operations/traceql-metrics/#activate-and-configure-the-local-blocks-processor\n          if (message.includes('localblocks processor not found')) {\n            this.setState({ hasIssue: true });\n          }\n        }\n      })\n    );\n    \n    issueDetector.activate();\n  }\n\n  public resetIssues() {\n    this.setState({\n      hasIssue: false,\n    });\n  }\n} \n\nconst TraceQLWarningTitle = 'TraceQL metrics not configured';\nconst TraceQLWarningMessage = 'We found an error running a TraceQL metrics query: \"localblocks processor not found\". This typically means the \"local-blocks\" processor is not configured in Tempo, which is required for Grafana Traces Drilldown to work.';\n\nexport const TraceQLConfigWarning: React.FC<{ detector: TraceQLIssueDetector }> = ({ detector }) => {\n  const { hasIssue } = detector.useState();\n\n  if (!hasIssue) {\n    return null;\n  }\n\n  return (\n    <Alert\n      severity=\"warning\"\n      title={TraceQLWarningTitle}\n    >\n      <p>\n        {TraceQLWarningMessage}\n        <LinkButton\n          icon=\"external-link-alt\"\n          fill=\"text\"\n          size=\"sm\"\n          target=\"_blank\"\n          href=\"https://grafana.com/docs/tempo/latest/operations/traceql-metrics\"\n        >\n          Read documentation\n        </LinkButton>\n      </p>\n    </Alert>\n  );\n};\n", "import React, { ReactElement, useEffect, useState } from 'react';\n\nimport { TimeRange } from '@grafana/data';\nimport { ComponentSize } from '@grafana/ui';\nimport { usePluginComponent } from '@grafana/runtime';\nimport { sceneGraph, SceneObject } from '@grafana/scenes';\n\ninterface EntityAssertionsWidgetProps {\n  query: {\n    entityName?: string;\n    entityType?: string;\n    start: number;\n    end: number;\n  };\n  size: ComponentSize;\n  source?: string;\n  returnToPrevious?: boolean;\n}\n\nexport type EntityAssertionsWidgetExternal = (props: EntityAssertionsWidgetProps) => ReactElement | null;\n\ninterface Props {\n  serviceName: string;\n  model: SceneObject;\n}\n\nexport function EntityAssertionsWidget({ serviceName, model }: Props) {\n  const { isLoading, component: EntityAssertionsWidgetExternal } = usePluginComponent<EntityAssertionsWidgetProps>(\n    'grafana-asserts-app/entity-assertions-widget/v1'\n  );\n  const [timeRange, setTimeRange] = useState<TimeRange>();\n\n  useEffect(() => {\n    const sceneTimeRange = sceneGraph.getTimeRange(model);\n    setTimeRange(sceneTimeRange.state.value);\n\n    const sub = sceneTimeRange.subscribeToState((state) => {\n      setTimeRange(state.value);\n    });\n\n    return () => {\n      sub.unsubscribe();\n    };\n  }, [model]);\n\n  if (isLoading || !EntityAssertionsWidgetExternal || !timeRange) {\n    return null;\n  }\n\n  return (\n    <EntityAssertionsWidgetExternal\n      size='md'\n      source='Traces Drilldown'\n      query={{\n        start: timeRange.from.valueOf(),\n        end: timeRange.to.valueOf(),\n        entityName: serviceName,\n        entityType: 'Service',\n      }}\n      returnToPrevious={true}\n    />\n  );\n}\n", "import { css, cx } from '@emotion/css';\nimport { useDialog } from '@react-aria/dialog';\nimport { FocusScope } from '@react-aria/focus';\nimport { useOverlay } from '@react-aria/overlays';\nimport RcDrawer from 'rc-drawer';\nimport { ReactNode, useCallback, useEffect, useState } from 'react';\nimport * as React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { selectors } from '@grafana/e2e-selectors';\nimport { t } from '@grafana/i18n';\n\nimport 'rc-drawer/assets/index.css';\nimport { useStyles2, getDragStyles, IconButton, ScrollContainer, Text } from '@grafana/ui';\n\nexport interface Props {\n  children: ReactNode;\n  /** Title shown at the top of the drawer */\n  title?: ReactNode;\n  /** Subtitle shown below the title */\n  subtitle?: ReactNode;\n  /** Should the Drawer be closable by clicking on the mask, defaults to true */\n  closeOnMaskClick?: boolean;\n  /**\n   * Specifies the width and min-width.\n   * sm = width 25% & min-width 384px\n   * md = width 50% & min-width 568px\n   * lg = width 75% & min-width 744px\n   **/\n  size?: 'sm' | 'md' | 'lg';\n  /** Tabs */\n  tabs?: React.ReactNode;\n  /**\n   * Whether the content should be wrapped in a ScrollContainer\n   * Only change this if you intend to manage scroll behaviour yourself\n   * (e.g. having a split pane with independent scrolling)\n   **/\n  scrollableContent?: boolean;\n  /** Callback for closing the drawer */\n  onClose: () => void;\n}\n\nconst drawerSizes = {\n  sm: { width: '25%', minWidth: 384 },\n  md: { width: '50%', minWidth: 568 },\n  lg: { width: '75%', minWidth: 744 },\n};\n\nexport function Drawer({\n  children,\n  onClose,\n  closeOnMaskClick = true,\n  scrollableContent = true,\n  title,\n  subtitle,\n  size = 'md',\n  tabs,\n}: Props) {\n  const [drawerWidth, onMouseDown, onTouchStart] = useResizebleDrawer();\n\n  const styles = useStyles2(getStyles);\n  const wrapperStyles = useStyles2(getWrapperStyles, size);\n  const dragStyles = useStyles2(getDragStyles);\n\n  const overlayRef = React.useRef(null);\n  const { dialogProps, titleProps } = useDialog({}, overlayRef);\n  const { overlayProps } = useOverlay(\n    {\n      isDismissable: false,\n      isOpen: true,\n      onClose,\n    },\n    overlayRef\n  );\n\n  // Adds body class while open so the toolbar nav can hide some actions while drawer is open\n  useBodyClassWhileOpen();\n\n  const content = <div className={styles.content}>{children}</div>;\n  const overrideWidth = drawerWidth ?? drawerSizes[size].width;\n  const minWidth = drawerSizes[size].minWidth;\n\n  return (\n    <RcDrawer\n      open={true}\n      onClose={onClose}\n      placement=\"right\"\n      getContainer={'#trace-exploration'}\n      className={styles.drawerContent}\n      rootClassName={styles.drawer}\n      classNames={{\n        wrapper: wrapperStyles,\n      }}\n      styles={{\n        wrapper: {\n          width: overrideWidth,\n          minWidth,\n        },\n      }}\n      width={''}\n      motion={{\n        motionAppear: true,\n        motionName: styles.drawerMotion,\n      }}\n      maskClassName={styles.mask}\n      maskClosable={closeOnMaskClick}\n      maskMotion={{\n        motionAppear: true,\n        motionName: styles.maskMotion,\n      }}\n    >\n      <FocusScope restoreFocus contain autoFocus>\n        <div\n          aria-label={\n            typeof title === 'string'\n              ? selectors.components.Drawer.General.title(title)\n              : selectors.components.Drawer.General.title('no title')\n          }\n          className={styles.container}\n          {...overlayProps}\n          {...dialogProps}\n          ref={overlayRef}\n        >\n          <div\n            className={cx(dragStyles.dragHandleVertical, styles.resizer)}\n            onMouseDown={onMouseDown}\n            onTouchStart={onTouchStart}\n          />\n          <div className={cx(styles.header, Boolean(tabs) && styles.headerWithTabs)}>\n            <div className={styles.actions}>\n              <IconButton\n                name=\"times\"\n                variant=\"secondary\"\n                onClick={onClose}\n                data-testid={selectors.components.Drawer.General.close}\n                tooltip={t(`grafana-ui.drawer.close`, 'Close')}\n              />\n            </div>\n            {typeof title === 'string' ? (\n              <div className={styles.titleWrapper}>\n                <Text element=\"h3\" {...titleProps}>\n                  {title}\n                </Text>\n                {subtitle && (\n                  <div className={styles.subtitle} data-testid={selectors.components.Drawer.General.subtitle}>\n                    {subtitle}\n                  </div>\n                )}\n              </div>\n            ) : (\n              title\n            )}\n            {tabs && <div className={styles.tabsWrapper}>{tabs}</div>}\n          </div>\n          {!scrollableContent ? content : <ScrollContainer showScrollIndicators>{content}</ScrollContainer>}\n        </div>\n      </FocusScope>\n    </RcDrawer>\n  );\n}\n\nfunction useResizebleDrawer(): [\n  string | undefined,\n  React.EventHandler<React.MouseEvent>,\n  React.EventHandler<React.TouchEvent>,\n] {\n  const [drawerWidth, setDrawerWidth] = useState<string | undefined>(undefined);\n\n  const onMouseMove = useCallback((e: MouseEvent) => {\n    setDrawerWidth(getCustomDrawerWidth(e.clientX));\n  }, []);\n\n  const onTouchMove = useCallback((e: TouchEvent) => {\n    const touch = e.touches[0];\n    setDrawerWidth(getCustomDrawerWidth(touch.clientX));\n  }, []);\n\n  const onMouseUp = useCallback(\n    (e: MouseEvent) => {\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', onMouseUp);\n    },\n    [onMouseMove]\n  );\n\n  const onTouchEnd = useCallback(\n    (e: TouchEvent) => {\n      document.removeEventListener('touchmove', onTouchMove);\n      document.removeEventListener('touchend', onTouchEnd);\n    },\n    [onTouchMove]\n  );\n\n  function onMouseDown(e: React.MouseEvent<HTMLDivElement>) {\n    e.stopPropagation();\n    e.preventDefault();\n    // we will only add listeners when needed, and remove them afterward\n    document.addEventListener('mousemove', onMouseMove);\n    document.addEventListener('mouseup', onMouseUp);\n  }\n\n  function onTouchStart(e: React.TouchEvent<HTMLDivElement>) {\n    e.stopPropagation();\n    e.preventDefault();\n    // we will only add listeners when needed, and remove them afterward\n    document.addEventListener('touchmove', onTouchMove);\n    document.addEventListener('touchend', onTouchEnd);\n  }\n\n  return [drawerWidth, onMouseDown, onTouchStart];\n}\n\nfunction getCustomDrawerWidth(clientX: number) {\n  let offsetRight = document.body.offsetWidth - (clientX - document.body.offsetLeft);\n  let widthPercent = Math.min((offsetRight / document.body.clientWidth) * 100, 98).toFixed(2);\n  return `${widthPercent}vw`;\n}\n\nfunction useBodyClassWhileOpen() {\n  useEffect(() => {\n    if (!document.body) {\n      return;\n    }\n\n    document.body.classList.add('body-drawer-open');\n\n    return () => {\n      document.body.classList.remove('body-drawer-open');\n    };\n  }, []);\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%',\n      flex: '1 1 0',\n      minHeight: '100%',\n      position: 'relative',\n    }),\n    drawer: css({\n      top: 0,\n      position: 'absolute !important' as 'absolute',\n\n      '.rc-drawer-content-wrapper': {\n        boxShadow: theme.shadows.z3,\n      },\n    }),\n    drawerContent: css({\n      backgroundColor: `${theme.colors.background.primary} !important`,\n      display: 'flex',\n      overflow: 'unset !important',\n      flexDirection: 'column',\n    }),\n    drawerMotion: css({\n      '&-appear': {\n        transform: 'translateX(100%)',\n        transition: 'none !important',\n\n        '&-active': {\n          transition: `${theme.transitions.create('transform')} !important`,\n          transform: 'translateX(0)',\n        },\n      },\n    }),\n    // we want the mask itself to span the whole page including the top bar\n    // this ensures trying to click something in the top bar will close the drawer correctly\n    // but we don't want the backdrop styling to apply over the top bar as it looks weird\n    // instead have a child pseudo element to apply the backdrop styling below the top bar\n    mask: css({\n      // The !important here is to override the default .rc-drawer-mask styles\n      backgroundColor: 'transparent !important',\n      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n      position: 'absolute !important' as 'absolute',\n\n      '&:before': {\n        backgroundColor: `${theme.components.overlay.background} !important`,\n        bottom: 0,\n        content: '\"\"',\n        left: 0,\n        position: 'absolute',\n        right: 0,\n        top: 0,\n      },\n    }),\n    maskMotion: css({\n      '&-appear': {\n        opacity: 0,\n\n        '&-active': {\n          opacity: 1,\n          transition: theme.transitions.create('opacity'),\n        },\n      },\n    }),\n    header: css({\n      label: 'drawer-header',\n      flexGrow: 0,\n      padding: theme.spacing(2, 2, 3),\n      borderBottom: `1px solid ${theme.colors.border.weak}`,\n    }),\n    headerWithTabs: css({\n      borderBottom: 'none',\n    }),\n    actions: css({\n      position: 'absolute',\n      right: theme.spacing(1),\n      top: theme.spacing(1),\n    }),\n    titleWrapper: css({\n      label: 'drawer-title',\n      overflowWrap: 'break-word',\n    }),\n    subtitle: css({\n      label: 'drawer-subtitle',\n      color: theme.colors.text.secondary,\n      paddingTop: theme.spacing(1),\n    }),\n    content: css({\n      padding: theme.spacing(theme.components.drawer?.padding ?? 2),\n      height: '100%',\n      flexGrow: 1,\n      minHeight: 0,\n    }),\n    tabsWrapper: css({\n      label: 'drawer-tabs',\n      paddingLeft: theme.spacing(2),\n      margin: theme.spacing(1, -1, -3, -3),\n    }),\n    resizer: css({\n      top: 0,\n      left: theme.spacing(-1),\n      bottom: 0,\n      position: 'absolute',\n      zIndex: theme.zIndex.modal,\n    }),\n  };\n};\n\nfunction getWrapperStyles(theme: GrafanaTheme2, size: 'sm' | 'md' | 'lg') {\n  return css({\n    label: `drawer-content-wrapper-${size}`,\n    overflow: 'unset !important',\n\n    [theme.breakpoints.down('md')]: {\n      width: `calc(100% - ${theme.spacing(2)}) !important`,\n      minWidth: '0 !important',\n    },\n  });\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Button, useStyles2 } from '@grafana/ui';\nimport { Drawer } from './Drawer';\n\ninterface SmartDrawerProps {\n  children: React.ReactNode;\n  title?: string;\n  isOpen: boolean;\n  onClose: () => void;\n  embedded?: boolean;\n  forceNoDrawer?: boolean;\n  investigationButton?: React.ReactNode;\n}\n\nexport const SmartDrawer = ({\n  children,\n  title,\n  isOpen,\n  onClose,\n  embedded = false,\n  forceNoDrawer = false,\n  investigationButton,\n}: SmartDrawerProps) => {\n  const styles = useStyles2(getStyles);\n\n  const shouldUseDrawer = !forceNoDrawer && !embedded;\n\n  if (!isOpen) {\n    return null;\n  }\n\n  if (shouldUseDrawer) {\n    return (\n      <Drawer size=\"lg\" title={title} onClose={onClose}>\n        {children}\n      </Drawer>\n    );\n  }\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.drawerHeader}>\n        <Button variant=\"primary\" fill=\"text\" size=\"md\" icon={'arrow-left'} onClick={onClose}>\n          Back to all traces\n        </Button>\n        {embedded && investigationButton}\n      </div>\n      {children}\n    </div>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  container: css({\n    height: '100%',\n    width: '100%',\n    background: theme.colors.background.primary,\n    padding: theme.spacing(2),\n    display: 'flex',\n    flexDirection: 'column',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    zIndex: 3,\n  }),\n  drawerHeader: css({\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    paddingBottom: theme.spacing(2),\n\n    h4: {\n      margin: 0,\n    },\n  }),\n});\n", "import { AdHocVariableFilter } from '@grafana/data';\n\nexport function renderTraceQLLabelFilters(filters: AdHocVariableFilter[]) {\n  const expr = filters\n    .filter((f) => f.key && f.operator && f.value)\n    .map((filter) => renderFilter(filter))\n    .join('&&');\n  // Return 'true' if there are no filters to help with cases where we want to concatenate additional filters in the expression\n  // and avoid invalid queries like '{ && key=value }'\n  return expr.length ? expr : 'true';\n}\n\nfunction renderFilter(filter: AdHocVariableFilter) {\n  let val = filter.value;\n  if (\n    ['span.messaging.destination.partition.id', 'span.network.protocol.version'].includes(filter.key) ||\n    (!isNumber(val) &&\n      ![\n        'status',\n        'kind',\n        'span:status',\n        'span:kind',\n        'duration',\n        'span:duration',\n        'trace:duration',\n        'event:timeSinceStart',\n      ].includes(filter.key) &&\n      !['true', 'false'].includes(val))\n  ) {\n    if (typeof val === 'string') {\n      // Escape \" and \\ to \\\" and \\\\ respectively\n      val = val.replace(/[\"\\\\]/g, (s) => `\\\\${s}`);\n      val = `\"${val}\"`;\n    }\n  }\n\n  return `${filter.key}${filter.operator}${val}`;\n}\n\nfunction isNumber(value?: string | number): boolean {\n  return value != null && value !== '' && !isNaN(Number(value.toString().trim()));\n}\n", "import { AdHocFiltersVariable } from '@grafana/scenes';\nimport { AdHocVariableFilter, VariableHide } from '@grafana/data';\nimport { VAR_FILTERS, explorationDS } from 'utils/shared';\nimport { renderTraceQLLabelFilters } from 'utils/filters-renderer';\n\nexport interface AttributeFiltersVariableProps {\n  initialFilters?: AdHocVariableFilter[];\n  embedderName?: string;\n  embedded?: boolean;\n}\n\nexport class AttributeFiltersVariable extends AdHocFiltersVariable {\n  private initialFilters?: AdHocVariableFilter[];\n  private embedderName?: string;\n  private embedded?: boolean;\n\n  constructor(props: Partial<AttributeFiltersVariableProps>) {\n    super({\n      addFilterButtonText: 'Add filter',\n      name: VAR_FILTERS,\n      datasource: explorationDS,\n      hide: VariableHide.hideLabel,\n      layout: 'combobox',\n      filters: (props.initialFilters ?? []).map((f) => ({\n        ...f,\n        readOnly: props.embedded,\n        origin: props.embedderName,\n      })),\n      allowCustomValue: true,\n      expressionBuilder: renderTraceQLLabelFilters,\n    });\n\n    this.initialFilters = props.initialFilters;\n    this.embedderName = props.embedderName;\n    this.embedded = props.embedded;\n\n    // Subscribe to state changes to update readOnly and origin for matching filters\n    this.subscribeToState((newState) => {\n      if (newState.filters && this.embedded) {\n        let hasChanges = false;\n        const updatedFilters = newState.filters.map((filter) => {\n          // Check if this filter matches any of the initial filters\n          const matchingInitialFilter = this.initialFilters?.find(\n            (initialFilter) =>\n              initialFilter.key === filter.key &&\n              initialFilter.operator === filter.operator &&\n              initialFilter.value === filter.value\n          );\n\n          if (matchingInitialFilter && !filter.readOnly && filter.origin !== this.embedderName) {\n            hasChanges = true;\n            return {\n              ...filter,\n              readOnly: true,\n              origin: this.embedderName,\n            };\n          }\n\n          return filter;\n        });\n\n        // Only update if there are actual changes\n        if (hasChanges) {\n          this.setState({ filters: updatedFilters });\n        }\n      }\n    });\n  }\n}\n", "import { css } from '@emotion/css';\nimport React, { useEffect } from 'react';\n\nimport { GrafanaTheme2, LoadingState, PluginExtensionLink, AdHocVariableFilter } from '@grafana/data';\nimport {\n  CustomVariable,\n  DataSourceVariable,\n  SceneComponentProps,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneObjectUrlSyncConfig,\n  SceneObjectUrlValues,\n  SceneQueryRunner,\n  SceneRefreshPicker,\n  SceneTimePicker,\n  SceneTimeRange,\n  SceneVariableSet,\n} from '@grafana/scenes';\nimport { config, useReturnToPrevious } from '@grafana/runtime';\nimport { Button, Dropdown, Icon, Menu, Stack, useStyles2, <PERSON>Button } from '@grafana/ui';\n\nimport {\n  DATASOURCE_LS_KEY,\n  EventTraceOpened,\n  MetricFunction,\n  VAR_DATASOURCE,\n  VAR_GROUPBY,\n  VAR_LATENCY_PARTIAL_THRESHOLD,\n  VAR_LATENCY_THRESHOLD,\n  VAR_METRIC,\n  VAR_PRIMARY_SIGNAL,\n  VAR_SPAN_LIST_COLUMNS,\n} from '../../utils/shared';\nimport {\n  getTraceExplorationScene,\n  getFiltersVariable,\n  getPrimarySignalVariable,\n  getDataSource,\n  getUrlForExploration,\n} from '../../utils/utils';\nimport { TraceDrawerScene } from '../../components/Explore/TracesByService/TraceDrawerScene';\nimport { VariableHide } from '@grafana/schema';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { PrimarySignalVariable } from './PrimarySignalVariable';\nimport { primarySignalOptions } from './primary-signals';\nimport { TraceQLIssueDetector, TraceQLConfigWarning } from '../../components/Explore/TraceQLIssueDetector';\nimport { AddToInvestigationButton } from 'components/Explore/actions/AddToInvestigationButton';\nimport { ADD_TO_INVESTIGATION_MENU_TEXT, getInvestigationLink } from 'components/Explore/panels/PanelMenu';\nimport { TracesByServiceScene } from 'components/Explore/TracesByService/TracesByServiceScene';\nimport { SharedExplorationState } from 'exposedComponents/types';\nimport { EntityAssertionsWidget } from '../../addedComponents/EntityAssertionsWidget/EntityAssertionsWidget';\nimport { SmartDrawer } from './SmartDrawer';\nimport { AttributeFiltersVariable } from './AttributeFiltersVariable';\n\nexport interface TraceExplorationState extends SharedExplorationState, SceneObjectState {\n  topScene?: SceneObject;\n  controls: SceneObject[];\n\n  body: SceneObject;\n\n  drawerScene?: TraceDrawerScene;\n\n  // details scene\n  traceId?: string;\n  spanId?: string;\n\n  issueDetector?: TraceQLIssueDetector;\n\n  investigationLink?: PluginExtensionLink;\n  addToInvestigationButton?: AddToInvestigationButton;\n}\n\nconst version = process.env.VERSION;\nconst buildTime = process.env.BUILD_TIME;\nconst commitSha = process.env.COMMIT_SHA;\nconst compositeVersion = `${buildTime?.split('T')[0]} (${commitSha})`;\n\nexport class TraceExploration extends SceneObjectBase<TraceExplorationState> {\n  protected _urlSync = new SceneObjectUrlSyncConfig(this, { keys: ['traceId', 'spanId'] });\n\n  public constructor(state: Partial<TraceExplorationState>) {\n    super({\n      $timeRange: state.$timeRange ?? new SceneTimeRange({}),\n      $variables: state.$variables ?? getVariableSet(state as TraceExplorationState),\n      controls: state.controls ?? [new SceneTimePicker({}), new SceneRefreshPicker({})],\n      body: new TraceExplorationScene({}),\n      drawerScene: new TraceDrawerScene({}),\n      issueDetector: new TraceQLIssueDetector(),\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  public _onActivate() {\n    if (!this.state.topScene) {\n      this.setState({ topScene: getTopScene() });\n    }\n\n    this._subs.add(\n      this.subscribeToEvent(EventTraceOpened, (event) => {\n        this.setupInvestigationButton(event.payload.traceId);\n        this.setState({ traceId: event.payload.traceId, spanId: event.payload.spanId });\n      })\n    );\n\n    if (this.state.traceId) {\n      this.setupInvestigationButton(this.state.traceId);\n    }\n\n    const datasourceVar = sceneGraph.lookupVariable(VAR_DATASOURCE, this) as DataSourceVariable;\n    datasourceVar.subscribeToState((newState) => {\n      if (newState.value) {\n        localStorage.setItem(DATASOURCE_LS_KEY, newState.value.toString());\n      }\n    });\n\n    if (this.state.issueDetector) {\n      if (!this.state.issueDetector.isActive) {\n        this.state.issueDetector.activate();\n      }\n    }\n  }\n\n  getUrlState() {\n    return { traceId: this.state.traceId, spanId: this.state.spanId };\n  }\n\n  updateFromUrl(values: SceneObjectUrlValues) {\n    const stateUpdate: Partial<TraceExplorationState> = {};\n\n    if (values.traceId || values.spanId) {\n      stateUpdate.traceId = values.traceId ? (values.traceId as string) : undefined;\n      stateUpdate.spanId = values.spanId ? (values.spanId as string) : undefined;\n    }\n\n    this.setState(stateUpdate);\n  }\n\n  public getMetricVariable() {\n    const variable = sceneGraph.lookupVariable(VAR_METRIC, this);\n    if (!(variable instanceof CustomVariable)) {\n      throw new Error('Metric variable not found');\n    }\n\n    if (!variable.getValue()) {\n      variable.changeValueTo(this.state.initialMetric ?? 'rate');\n    }\n\n    return variable;\n  }\n\n  public onChangeMetricFunction = (metric: string) => {\n    const variable = this.getMetricVariable();\n    if (!metric || variable.getValue() === metric) {\n      return;\n    }\n\n    variable.changeValueTo(metric, undefined, true);\n  };\n\n  public getMetricFunction() {\n    return this.getMetricVariable().getValue() as MetricFunction;\n  }\n\n  public closeDrawer() {\n    this.setState({ traceId: undefined, spanId: undefined });\n  }\n\n  private setupInvestigationButton(traceId: string) {\n    const traceExploration = getTraceExplorationScene(this);\n    const dsUid = getDataSource(traceExploration);\n\n    const queryRunner = new SceneQueryRunner({\n      datasource: { uid: dsUid },\n      queries: [\n        {\n          refId: 'A',\n          query: traceId,\n          queryType: 'traceql',\n        },\n      ],\n    });\n\n    const addToInvestigationButton = new AddToInvestigationButton({\n      query: traceId,\n      type: 'trace',\n      dsUid,\n      $data: queryRunner,\n    });\n\n    addToInvestigationButton.activate();\n    this.setState({ addToInvestigationButton });\n    this._subs.add(\n      addToInvestigationButton.subscribeToState(() => {\n        this.updateInvestigationLink();\n      })\n    );\n\n    queryRunner.activate();\n\n    this._subs.add(\n      queryRunner.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Done && state.data?.series?.length > 0) {\n          const serviceNameField = state.data.series[0]?.fields?.find((f) => f.name === 'serviceName');\n\n          if (serviceNameField && serviceNameField.values[0]) {\n            addToInvestigationButton.setState({\n              ...addToInvestigationButton.state,\n              labelValue: `${serviceNameField.values[0]}`,\n            });\n          }\n        }\n      })\n    );\n\n    addToInvestigationButton.setState({\n      ...addToInvestigationButton.state,\n      labelValue: traceId,\n    });\n  }\n\n  private async updateInvestigationLink() {\n    const { addToInvestigationButton } = this.state;\n    if (!addToInvestigationButton) {\n      return;\n    }\n\n    const link = await getInvestigationLink(addToInvestigationButton);\n    if (link) {\n      this.setState({ investigationLink: link });\n    }\n  }\n\n  static Component = ({ model }: SceneComponentProps<TraceExploration>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return <div className={styles.bodyContainer}> {body && <body.Component model={body} />} </div>;\n  };\n}\n\nexport class TraceExplorationScene extends SceneObjectBase {\n  static Component = ({ model }: SceneComponentProps<TraceExplorationScene>) => {\n    const traceExploration = getTraceExplorationScene(model);\n    const {\n      controls,\n      topScene,\n      drawerScene,\n      traceId,\n      issueDetector,\n      investigationLink,\n      addToInvestigationButton,\n      embedded,\n    } = traceExploration.useState();\n    const { hasIssue } = issueDetector?.useState() || {\n      hasIssue: false,\n    };\n    const styles = useStyles2(getStyles);\n\n    const addToInvestigationClicked = (e: React.MouseEvent) => {\n      if (investigationLink?.onClick) {\n        investigationLink.onClick(e);\n      }\n\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.add_to_investigation_trace_view_clicked\n      );\n\n      setTimeout(() => traceExploration.closeDrawer(), 100);\n    };\n\n    return (\n      <div className={styles.container} id=\"trace-exploration\">\n        {hasIssue && issueDetector && <TraceQLConfigWarning detector={issueDetector} />}\n        {embedded ? <EmbeddedHeader model={model} /> : <TraceExplorationHeader controls={controls} model={model} />}\n        <div className={styles.body}>{topScene && <topScene.Component model={topScene} />}</div>\n        <SmartDrawer\n          isOpen={!!drawerScene && !!traceId}\n          onClose={() => traceExploration.closeDrawer()}\n          title={`View trace ${traceId}`}\n          embedded={embedded}\n          forceNoDrawer={embedded}\n          investigationButton={\n            addToInvestigationButton &&\n            investigationLink && (\n              <Button variant=\"secondary\" size=\"sm\" icon=\"plus-square\" onClick={addToInvestigationClicked}>\n                {ADD_TO_INVESTIGATION_MENU_TEXT}\n              </Button>\n            )\n          }\n        >\n          {drawerScene && <drawerScene.Component model={drawerScene} />}\n        </SmartDrawer>\n      </div>\n    );\n  };\n}\n\nconst useServiceName = (model: SceneObject) => {\n  const [serviceName, setServiceName] = React.useState<string>();\n  const traceExploration = getTraceExplorationScene(model);\n  const filtersVariable = getFiltersVariable(traceExploration);\n\n  const getServiceNameFromFilters = (filters: AdHocVariableFilter[]) => {\n    const serviceNameFilter = filters.find((f) => f.key === 'resource.service.name');\n    return serviceNameFilter?.operator === '=' || serviceNameFilter?.operator === '=~'\n      ? serviceNameFilter?.value?.replace(/\"/g, '')\n      : undefined;\n  };\n\n  useEffect(() => {\n    setServiceName(getServiceNameFromFilters(filtersVariable.state.filters));\n\n    const sub = filtersVariable.subscribeToState((newState) => {\n      setServiceName(getServiceNameFromFilters(newState.filters));\n    });\n\n    return () => {\n      sub.unsubscribe();\n    };\n  }, [filtersVariable]);\n\n  return serviceName;\n};\n\nconst EmbeddedHeader = ({ model }: SceneComponentProps<TraceExplorationScene>) => {\n  const setReturnToPrevious = useReturnToPrevious();\n  const styles = useStyles2(getStyles, true);\n  const traceExploration = getTraceExplorationScene(model);\n  const { returnToPreviousSource } = traceExploration.useState();\n  const filtersVariable = getFiltersVariable(traceExploration);\n  const primarySignalVariable = getPrimarySignalVariable(traceExploration);\n  const timeRangeControl = traceExploration.state.controls.find((control) => control instanceof SceneTimePicker);\n\n  const timeRangeState = traceExploration.state.$timeRange?.useState();\n  const filtersVariableState = filtersVariable.useState();\n  const metricVariableState = traceExploration.getMetricVariable().useState();\n  const [explorationUrl, setExplorationUrl] = React.useState(() => getUrlForExploration(traceExploration));\n  \n  // Force the primary signal to be 'All Spans'\n  primarySignalVariable?.changeValueTo(primarySignalOptions[1].value!);\n\n  useEffect(() => {\n    setExplorationUrl(getUrlForExploration(traceExploration));\n  }, [timeRangeState, filtersVariableState, metricVariableState, traceExploration]);\n\n  return (\n    <div className={styles.headerContainer}>\n      <Stack gap={1} alignItems={'center'} wrap={'wrap'} justifyContent=\"space-between\">\n        <primarySignalVariable.Component model={primarySignalVariable} />\n        {filtersVariable && (\n          <div>\n            <filtersVariable.Component model={filtersVariable} />\n          </div>\n        )}\n        <Stack gap={1} alignItems={'center'}>\n          <LinkButton\n            href={explorationUrl}\n            variant=\"secondary\"\n            icon=\"arrow-right\"\n            onClick={() => {\n              setReturnToPrevious(returnToPreviousSource || 'previous');\n              reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.go_to_full_app_clicked);\n            }}\n          >\n            Traces Drilldown\n          </LinkButton>\n          {timeRangeControl && <timeRangeControl.Component model={timeRangeControl} />}\n        </Stack>\n      </Stack>\n    </div>\n  );\n};\n\ninterface TraceExplorationHeaderProps {\n  controls: SceneObject[];\n  model: SceneObject;\n}\n\nconst TraceExplorationHeader = ({ controls, model }: TraceExplorationHeaderProps) => {\n  const styles = useStyles2(getStyles);\n  const [menuVisible, setMenuVisible] = React.useState(false);\n  const serviceName = useServiceName(model);\n  const traceExploration = getTraceExplorationScene(model);\n\n  const dsVariable = sceneGraph.lookupVariable(VAR_DATASOURCE, traceExploration);\n  const filtersVariable = getFiltersVariable(traceExploration);\n  const primarySignalVariable = getPrimarySignalVariable(traceExploration);\n\n  function VersionHeader() {\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.menuHeader}>\n        <h5>Grafana Traces Drilldown v{version}</h5>\n        <div className={styles.menuHeaderSubtitle}>Last update: {compositeVersion}</div>\n      </div>\n    );\n  }\n\n  const menu = (\n    <Menu header={<VersionHeader />}>\n      <div className={styles.menu}>\n        {config.feedbackLinksEnabled && (\n          <Menu.Item\n            label=\"Give feedback\"\n            ariaLabel=\"Give feedback\"\n            icon={'comment-alt-message'}\n            url=\"https://grafana.qualtrics.com/jfe/form/SV_9LUZ21zl3x4vUcS\"\n            target=\"_blank\"\n            onClick={() =>\n              reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.global_docs_link_clicked)\n            }\n          />\n        )}\n        <Menu.Item\n          label=\"Documentation\"\n          ariaLabel=\"Documentation\"\n          icon={'external-link-alt'}\n          url=\"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/\"\n          target=\"_blank\"\n          onClick={() =>\n            reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.feedback_link_clicked)\n          }\n        />\n      </div>\n    </Menu>\n  );\n\n  return (\n    <div className={styles.headerContainer}>\n      <Stack gap={1} justifyContent={'space-between'} wrap={'wrap'}>\n        <Stack gap={1} alignItems={'center'} wrap={'wrap'}>\n          {dsVariable && (\n            <Stack gap={0} alignItems={'center'}>\n              <div className={styles.datasourceLabel}>Data source</div>\n              <dsVariable.Component model={dsVariable} />\n            </Stack>\n          )}\n        </Stack>\n        <div className={styles.controls}>\n          <EntityAssertionsWidget serviceName={serviceName || ''} model={model} />\n          <Dropdown overlay={menu} onVisibleChange={() => setMenuVisible(!menuVisible)}>\n            <Button variant=\"secondary\" icon=\"info-circle\">\n              Need help\n              <Icon className={styles.helpIcon} name={menuVisible ? 'angle-up' : 'angle-down'} size=\"lg\" />\n            </Button>\n          </Dropdown>\n          {controls.map((control) => (\n            <control.Component key={control.state.key} model={control} />\n          ))}\n        </div>\n      </Stack>\n      <Stack gap={1} alignItems={'center'} wrap={'wrap'}>\n        <Stack gap={0} alignItems={'center'}>\n          <div className={styles.datasourceLabel}>Filters</div>\n          {primarySignalVariable && <primarySignalVariable.Component model={primarySignalVariable} />}\n        </Stack>\n        {filtersVariable && (\n          <div>\n            <filtersVariable.Component model={filtersVariable} />\n          </div>\n        )}\n      </Stack>\n    </div>\n  );\n};\n\nfunction getTopScene() {\n  return new TracesByServiceScene({});\n}\n\nfunction getVariableSet(state: TraceExplorationState) {\n  return new SceneVariableSet({\n    variables: [\n      new DataSourceVariable({\n        name: VAR_DATASOURCE,\n        label: 'Data source',\n        value: state.initialDS,\n        pluginId: 'tempo',\n        isReadOnly: state.embedded,\n      }),\n      new PrimarySignalVariable({\n        name: VAR_PRIMARY_SIGNAL,\n        isReadOnly: state.embedded,\n      }),\n      new AttributeFiltersVariable({\n        initialFilters: state.initialFilters,\n        embedderName: state.embedderName,\n        embedded: state.embedded,\n      }),\n      new CustomVariable({\n        name: VAR_METRIC,\n        hide: VariableHide.hideVariable,\n      }),\n      new CustomVariable({\n        name: VAR_GROUPBY,\n        defaultToAll: false,\n        value: state.initialGroupBy,\n      }),\n      new CustomVariable({\n        name: VAR_SPAN_LIST_COLUMNS,\n        defaultToAll: false,\n      }),\n      new CustomVariable({\n        name: VAR_LATENCY_THRESHOLD,\n        defaultToAll: false,\n        hide: VariableHide.hideVariable,\n      }),\n      new CustomVariable({\n        name: VAR_LATENCY_PARTIAL_THRESHOLD,\n        defaultToAll: false,\n        hide: VariableHide.hideVariable,\n      }),\n    ],\n  });\n}\n\nfunction getStyles(theme: GrafanaTheme2, embedded?: boolean) {\n  return {\n    bodyContainer: css({\n      label: 'bodyContainer',\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    container: css({\n      label: 'container',\n      flexGrow: 1,\n      display: 'flex',\n      gap: theme.spacing(1),\n      minHeight: '100%',\n      flexDirection: 'column',\n      padding: `0 ${theme.spacing(2)} ${theme.spacing(2)} ${theme.spacing(2)}`,\n      overflow: 'auto' /* Needed for sticky positioning */,\n      maxHeight: '100%' /* Needed for sticky positioning */,\n      position: 'relative', // Needed for the drawer to be positioned correctly\n    }),\n    drawerHeader: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      borderBottom: `1px solid ${theme.colors.border.weak}`,\n      paddingBottom: theme.spacing(2),\n      marginBottom: theme.spacing(2),\n\n      h3: {\n        margin: 0,\n      },\n    }),\n    drawerHeaderButtons: css({\n      display: 'flex',\n      justifyContent: 'flex-end',\n      gap: theme.spacing(1.5),\n    }),\n    body: css({\n      label: 'body',\n      flexGrow: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      gap: theme.spacing(1),\n    }),\n    headerContainer: css({\n      label: 'headerContainer',\n      backgroundColor: embedded ? theme.colors.background.primary : theme.colors.background.canvas,\n      display: 'flex',\n      flexDirection: 'column',\n      position: 'sticky',\n      top: 0,\n      zIndex: 3,\n      padding: `${theme.spacing(1.5)} 0`,\n      gap: theme.spacing(1),\n    }),\n    datasourceLabel: css({\n      label: 'datasourceLabel',\n      fontSize: '12px',\n      padding: `0 ${theme.spacing(1)}`,\n      height: '32px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'flex-start',\n      fontWeight: theme.typography.fontWeightMedium,\n      position: 'relative',\n      right: -1,\n      width: '90px',\n    }),\n    controls: css({\n      label: 'controls',\n      display: 'flex',\n      gap: theme.spacing(1),\n      zIndex: 3,\n      flexWrap: 'wrap',\n    }),\n    menu: css({\n      label: 'menu',\n      'svg, span': {\n        color: theme.colors.text.link,\n      },\n    }),\n    menuHeader: css`\n      padding: ${theme.spacing(0.5, 1)};\n      white-space: nowrap;\n    `,\n    menuHeaderSubtitle: css`\n      color: ${theme.colors.text.secondary};\n      font-size: ${theme.typography.bodySmall.fontSize};\n    `,\n    tooltip: css({\n      label: 'tooltip',\n      fontSize: '14px',\n      lineHeight: '22px',\n      width: '180px',\n      textAlign: 'center',\n    }),\n    helpIcon: css({\n      label: 'helpIcon',\n      marginLeft: theme.spacing(1),\n    }),\n    filters: css({\n      label: 'filters',\n      marginTop: theme.spacing(1),\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n", "import { map, Observable } from 'rxjs';\nimport { DataFrame, DataTopic, Field } from '@grafana/data';\nimport { CustomTransformerDefinition } from '@grafana/scenes';\n\nexport const exemplarsTransformations = (\n  openTrace?: (traceId: string, spanId?: string) => void\n): CustomTransformerDefinition[] => [\n  {\n    topic: DataTopic.Annotations,\n    operator: () => (source: Observable<DataFrame[]>) => {\n      return source.pipe(\n        map((data: DataFrame[]) => {\n          return data.map((frame) => {\n            if (frame.name === 'exemplar') {\n              const traceIDField = frame.fields.find((field: Field) => field.name === 'traceId');\n              if (traceIDField) {\n                // The traceID will be interpolated in the url\n                // Then, onClick we retrieve the traceId from the url and navigate to the trace exploration scene by setting the state\n                traceIDField.config.links = [\n                  {\n                    title: 'View trace',\n                    url: '#${__value.raw}',\n                    onClick: (event) => {\n                      event.e.stopPropagation(); // Prevent the click event from propagating to the parent anchor\n                      const parentAnchorHref = event.e.target?.parentElement?.parentElement?.href;\n                      if (!parentAnchorHref || parentAnchorHref.indexOf('#') === -1) {\n                        return;\n                      }\n                      const traceId = parentAnchorHref.split('#')[1];\n                      if (!traceId || traceId === '') {\n                        return;\n                      }\n                      openTrace?.(traceId);\n                    },\n                  },\n                ];\n              }\n            }\n\n            return frame;\n          });\n        })\n      );\n    },\n  },\n];\n\nexport const removeExemplarsTransformation = (): CustomTransformerDefinition[] => [\n  {\n    topic: DataTopic.Annotations,\n    operator: () => (source: Observable<DataFrame[]>) => {\n      return source.pipe(\n        map((data: DataFrame[]) => {\n          return data.filter((frame) => frame.name !== 'exemplar');\n        })\n      );\n    },\n  },\n];\n\n", "import { dropWhile as _dropWhile, round as _round } from 'lodash';\nimport { sceneGraph, SceneObject } from '@grafana/scenes';\nimport { duration } from 'moment/moment';\n\nexport const ONE_MILLISECOND = 1000;\nexport const ONE_SECOND = 1000 * ONE_MILLISECOND;\nexport const ONE_MINUTE = 60 * ONE_SECOND;\nexport const ONE_HOUR = 60 * ONE_MINUTE;\nexport const ONE_DAY = 24 * ONE_HOUR;\nexport const DEFAULT_MS_PRECISION = Math.log10(ONE_MILLISECOND);\n\nconst UNIT_STEPS: Array<{ unit: string; microseconds: number; ofPrevious: number }> = [\n  { unit: 'd', microseconds: ONE_DAY, ofPrevious: 24 },\n  { unit: 'h', microseconds: ONE_HOUR, ofPrevious: 60 },\n  { unit: 'm', microseconds: ONE_MINUTE, ofPrevious: 60 },\n  { unit: 's', microseconds: ONE_SECOND, ofPrevious: 1000 },\n  { unit: 'ms', microseconds: ONE_MILLISECOND, ofPrevious: 1000 },\n  { unit: 'μs', microseconds: 1, ofPrevious: 1000 },\n];\n\n/**\n * Humanizes the duration for display.\n *\n * Example:\n * 5000ms => 5s\n * 1000μs => 1ms\n * 183840s => 2d 3h\n *\n * @param {number} duration (in microseconds)\n * @return {string} formatted duration\n */\nexport const formatDuration = (duration: number): string => {\n  // Drop all units that are too large except the last one\n  const [primaryUnit, secondaryUnit] = _dropWhile(\n    UNIT_STEPS,\n    ({ microseconds }, index) => index < UNIT_STEPS.length - 1 && microseconds > duration\n  );\n\n  if (primaryUnit.ofPrevious === 1000) {\n    // If the unit is decimal based, display as a decimal\n    return `${_round(duration / primaryUnit.microseconds, 2)}${primaryUnit.unit}`;\n  }\n\n  let primaryValue = Math.floor(duration / primaryUnit.microseconds);\n  let secondaryValue = (duration / secondaryUnit.microseconds) % primaryUnit.ofPrevious;\n  const secondaryValueRounded = Math.round(secondaryValue);\n\n  // Handle rollover case before rounding (e.g., 60s should become 1m, not 0m 60s)\n  if (secondaryValueRounded === primaryUnit.ofPrevious) {\n    primaryValue += 1;\n    secondaryValue = 0;\n  } else {\n    secondaryValue = secondaryValueRounded;\n  }\n\n  const primaryUnitString = `${primaryValue}${primaryUnit.unit}`;\n\n  if (secondaryValue === 0) {\n    return primaryUnitString;\n  }\n\n  const secondaryUnitString = `${secondaryValue}${secondaryUnit.unit}`;\n  return `${primaryUnitString} ${secondaryUnitString}`;\n}\n\n/**\n * Calculate bucket size based on time range and desired number of data points\n * @param timeRangeSeconds - The time range in seconds\n * @param dataPoints - Desired number of data points (default: 50)\n * @returns Bucket size in seconds\n */\nexport const calculateBucketSize = (timeRangeSeconds: number, dataPoints = 50): number => {\n  return Math.floor(timeRangeSeconds / dataPoints) || 1;\n};\n\nexport const getStepForTimeRange = (scene: SceneObject, dataPoints?: number) => {\n  const sceneTimeRange = sceneGraph.getTimeRange(scene);\n  const from = sceneTimeRange.state.value.from.unix();\n  const to = sceneTimeRange.state.value.to.unix();\n\n  const dur = duration(to - from, 's');\n  const bucketSizeSeconds = calculateBucketSize(dur.asSeconds(), dataPoints);\n  return `${bucketSizeSeconds}s`;\n}\n"], "names": ["StreamingIndicator", "isStreaming", "iconSize", "styles", "useStyles2", "getStyles", "<PERSON><PERSON><PERSON>", "content", "Icon", "name", "size", "className", "streamingIndicator", "theme", "css", "width", "height", "backgroundColor", "colors", "success", "text", "fill", "borderRadius", "display", "getHistogramVizPanel", "scene", "yBuckets", "parent", "getTraceByServiceScene", "panel", "histogramPanelConfig", "setHoverHeader", "setOption", "build", "setState", "extendPanelContext", "vizPanel", "context", "onSelectRange", "args", "rawSelection", "length", "selection", "undefined", "newSelection", "type", "raw", "timeRange", "from", "Math", "round", "x", "to", "yFrom", "yBucketToDuration", "y", "yTo", "duration", "onUserUpdateSelection", "shouldShowSelection", "state", "actionView", "setActionView", "reportAppInteraction", "USER_EVENTS_PAGES", "analyse_traces", "USER_EVENTS_ACTIONS", "start_investigation", "metric", "SceneFlexLayout", "direction", "children", "SceneFlexItem", "body", "PanelBuilders", "heatmap", "show", "unit", "axisLabel", "scheme", "steps", "value", "yValue", "buckets", "multiplier", "rawValue", "floor", "isNaN", "toFixed", "page", "action", "properties", "reportInteraction", "pluginJson", "replace", "createInteractionName", "home", "common", "action_view_changed", "breakdown_group_by_changed", "breakdown_add_to_filters_clicked", "comparison_add_to_filters_clicked", "select_attribute_in_comparison_clicked", "layout_type_changed", "stop_investigation", "open_trace", "open_in_explore_clicked", "add_to_investigation_clicked", "add_to_investigation_trace_view_clicked", "span_list_columns_changed", "toggle_bookmark_clicked", "primary_signal_changed", "exception_message_clicked", "homepage_initialized", "panel_row_clicked", "explore_traces_clicked", "read_documentation_clicked", "filter_changed", "go_to_bookmark_clicked", "metric_changed", "new_filter_added_manually", "app_initialized", "global_docs_link_clicked", "metric_docs_link_clicked", "feedback_link_clicked", "go_to_full_app_clicked", "ADD_TO_INVESTIGATION_MENU_TEXT", "extensionPointId", "ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT", "ADD_TO_INVESTIGATION_MENU_GROUP_TEXT", "PanelMenu", "SceneObjectBase", "addItem", "item", "this", "setItems", "items", "constructor", "super", "addActivationHandler", "iconClassName", "href", "getExploreHref", "onClick", "onExploreClick", "VizPanelMenu", "traceExploration", "getTraceExplorationScene", "dsUid", "getDataSource", "addToInvestigationButton", "AddToInvestigationButton", "query", "activate", "_subs", "add", "subscribeToState", "menu", "link", "getInvestigationLink", "existingMenuItems", "existingAddToInvestigationLink", "find", "filter", "includes", "e", "labelValue", "Component", "model", "useState", "datasource", "sceneGraph", "getTimeRange", "step", "getCurrentStep", "exploreState", "JSON", "stringify", "range", "toURLRange", "queries", "refId", "config", "subUrl", "appSubUrl", "url<PERSON><PERSON>", "renderUrl", "panes", "schemaVersion", "addToInvestigations", "getPluginLinkExtensions", "extensions", "getObservablePluginLinks", "firstValueFrom", "LoadingStateScene", "useTheme2", "component", "div", "container", "data-testid", "testIds", "loadingState", "SkeletonTheme", "baseColor", "emphasize", "background", "secondary", "highlightColor", "shape", "radius", "default", "fadeIn", "keyframes", "opacity", "label", "animationName", "animationDelay", "animationTimingFunction", "animationDuration", "animationFillMode", "map", "webpackContext", "req", "id", "webpackContextResolve", "__webpack_require__", "o", "Error", "code", "keys", "Object", "resolve", "module", "exports", "_onActivate", "getQueries", "getContext", "data", "getData", "queryRunner", "findObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "q", "ctx", "origin", "uid", "url", "window", "location", "title", "logoPath", "bind", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "computeHighestDifference", "frame", "baselineField", "fields", "f", "selection<PERSON><PERSON>", "maxDifference", "maxDifferenceIndex", "i", "values", "diff", "abs", "getDefaultSelectionForMetric", "AddToFiltersAction", "variable", "getFiltersVariable", "labels", "labelKey", "labelName", "getLabelValue", "addToFilters", "field", "key", "filterExistsForKey", "<PERSON><PERSON>", "variant", "icon", "filtersWithoutNew", "filters", "DATABASE_CALLS_KEY", "history", "pushState", "operator", "ShareExplorationAction", "exploration", "useLocation", "tooltip", "setTooltip", "<PERSON><PERSON>barButton", "navigator", "clipboard", "writeText", "getUrlForExploration", "setTimeout", "RECOMMENDED_ATTRIBUTES", "labelOrder", "SpanListColumnsSelector", "options", "onChange", "opt", "useMemo", "reduce", "acc", "curr", "slice", "indexOf", "group", "push", "startsWith", "sort", "a", "b", "Field", "Select", "toString", "split", "placeholder", "join", "is<PERSON><PERSON><PERSON>", "isClearable", "virtualized", "prefix", "min<PERSON><PERSON><PERSON>", "SpanListScene", "setupTransformations", "source", "pipe", "df", "nameField", "TableCellDisplayMode", "Custom", "cellComponent", "props", "traceIdField", "spanIdField", "traceId", "rowIndex", "spanId", "publishEvent", "EventTraceOpened", "Link", "getLinkToExplore", "target", "custom", "cellOptions", "updatePanel", "LoadingState", "Loading", "NotStarted", "Streaming", "series", "Done", "dataState", "EmptyStateScene", "message", "EMPTY_STATE_ERROR_MESSAGE", "remedyMessage", "EMPTY_STATE_ERROR_REMEDY_MESSAGE", "padding", "table", "setOverrides", "builder", "matchFieldsWithName", "overrideCustomFieldConfig", "SkeletonComponent", "traceExplorationScene", "queryType", "panelsState", "trace", "columns", "getSpanListColumnsVariable", "getValue", "changeValueTo", "$data", "SceneDataTransformer", "transformations", "sceneData", "attributes", "header", "description", "toOption", "gap", "justifyContent", "alignItems", "fontSize", "color", "cursor", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "textDecoration", "typography", "h6", "spacing", "getSkeletonStyles", "Skeleton", "count", "Array", "_", "row", "j", "span", "rowItem", "position", "primary", "border", "weak", "marginBottom", "SpansScene", "updateBody", "newState", "prevState", "getMetricVariable", "nestedSetLeft", "parseInt", "intValue", "Value", "int_value", "nestedSetRight", "TreeNode", "addSpan", "left", "min", "right", "max", "spans", "<PERSON><PERSON><PERSON><PERSON>", "node", "<PERSON><PERSON><PERSON><PERSON>", "findMatchingChild", "nodeName", "child", "serviceName", "operationName", "traceID", "createNode", "s", "serviceNameAttr", "stringValue", "string_value", "svcName", "resetLeftRight", "t", "Number", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "c", "ROOT_SPAN_ID", "StructureTabScene", "tree", "traces", "spanSets", "traceStartTime", "startTimeUnixNano", "ss", "s1", "s2", "curNode", "newNode", "mergeTraces", "parse", "countSpans", "loading", "wrap", "getPanels", "minHeight", "getPanel", "openTrace", "getOpenTrace", "setTitle", "setData", "SceneDataNode", "buildData", "getTrace", "traceName", "createDataFrame", "FieldType", "other", "references", "string", "spanID", "parentSpanId", "number", "startTime", "statusCode", "erroredSpans", "refType", "durationNanos", "explorationDS", "buildQuery", "filterStreamingProgressTransformations", "metricQuery", "<PERSON><PERSON><PERSON><PERSON>", "VAR_LATENCY_PARTIAL_THRESHOLD_EXPR", "VAR_LATENCY_THRESHOLD_EXPR", "VAR_FILTERS_EXPR", "tableType", "limit", "spss", "isLoading", "emptyMsg", "tabName", "structureDisplayName", "noDataMessage", "Text", "textAlignment", "longText", "<PERSON><PERSON>", "actionContainer", "LinkButton", "toLowerCase", "traceViewList", "EmptyState", "flexDirection", "x1", "margin", "marginLeft", "GroupBySelector", "radioAttributes", "showAll", "radioOptions", "otherAttrOptions", "select<PERSON><PERSON><PERSON>", "setSelectQuery", "allowAutoUpdate", "setAllowAutoUpdate", "availableWidth", "setAvailableWidth", "controlsContainer", "useRef", "initialGroupBy", "metricValue", "useResizeObserver", "ref", "onResize", "element", "current", "clientWidth", "radioOptionsWidth", "op", "checks", "attribute", "SPAN_ATTR", "RESOURCE_ATTR", "option", "textWidth", "measureText", "ops", "ro", "filteredOptions", "getModifiedSelectOptions", "ignoredAttributes", "defaultValue", "useEffect", "some", "showAllOption", "ALL", "defaultOnChangeValue", "RadioButtonGroup", "selected", "newSelected", "select", "onInputChange", "onCloseMenu", "maxOptions", "queryLowerCase", "tag", "LayoutSwitcher", "Selector", "active", "onLayoutChange", "layout", "layouts", "index", "findIndex", "linesPanelConfig", "timeseries", "showLegend", "mode", "TooltipDisplayMode", "Multi", "setCustomFieldConfig", "buildNormalLayout", "actionsFn", "metricByWithStatus", "getValueText", "panels", "$behaviors", "maxima", "Map", "eventSub", "subscribeToEvent", "EventTimeseriesDataReceived", "event", "payload", "for<PERSON>ach", "set", "v", "findAllObjects", "VizPanel", "clearFieldConfigCache", "fieldConfig", "merge", "cloneDeep", "defaults", "updateTimeseriesAxis", "unsubscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxDataPoints", "exemplarsTransformations", "reduceField", "reducers", "ReducerID", "calcs", "setUnit", "By<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SceneCSSGridLayout", "templateColumns", "GRID_TEMPLATE_COLUMNS", "autoRows", "isLazy", "groupBy", "getLayoutChild", "getTitle", "existingGridItem", "dataNode", "annotations", "status", "localeCompare", "interpolate", "generateMetricsQuery", "extraFilters", "formatLabelValue", "barsPanelConfig", "setMenu", "actions", "setHeaderActions", "gridItem", "SceneCSSGridItem", "AttributesDescription", "tags", "infoFlex", "tagsFlex", "style", "AttributesBreakdownScene", "getGroupByVariable", "setBody", "onReferencedVariableValueChanged", "radioAttributesResource", "onAddToFiltersClick", "_variableDependency", "VariableDependencyConfig", "variableNames", "VAR_FILTERS", "VAR_METRIC", "ignore", "flexGrow", "paddingTop", "controls", "controlsRight", "scope", "marginRight", "controlsLeft", "justifyItems", "groupByValue", "defaultScope", "radioAttributesSpan", "SPAN", "RESOURCE", "setScope", "filterType", "filteredAttributes", "attr", "concat", "getDescription", "getAttributesAsOptions", "BreakdownScene", "createTimeSeries", "timestamps", "timeRangeSeconds", "bucketSizeMs", "calculateBucketSize", "timestamp", "<PERSON><PERSON><PERSON>", "get", "entries", "time", "normalizeExceptionMessage", "trim", "ExceptionsScene", "exceptionsCount", "calculateExceptionsCount", "TableCellHeight", "Lg", "createTransformation", "messageField", "typeField", "serviceField", "timeField", "messages", "types", "occurrences", "lastSeenTimes", "services", "timeSeries", "aggregated", "messageTimestamps", "service", "normalizedMessage", "has", "timestampMs", "parseFloat", "timeSeriesData", "sortedEntries", "lastSeenMs", "diffMs", "Date", "now", "aggregateExceptions", "seriesData", "renderSparklineCell", "links", "createDataLink", "navigateToTracesWithFilter", "escapeFilterV<PERSON>ue", "occurrencesField", "total", "getExceptionsCount", "SparklineCell", "sparklineMessage", "count<PERSON><PERSON><PERSON>", "point", "timeValues", "validCount<PERSON><PERSON><PERSON>", "isFinite", "validTimeValues", "minCount", "maxCount", "minTime", "maxTime", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "safeCountDelta", "safeTimeDelta", "sparklineData", "delta", "sparklineContainer", "Sparkline", "sparkline", "drawStyle", "GraphDrawStyle", "Line", "fillOpacity", "fillColor", "lineWidth", "showPoints", "VisibilityMode", "Never", "exceptionMessage", "filtersVariable", "traceByServiceScene", "currentFilters", "escapedMessage", "existingFilterIndex", "newFilter", "newFilters", "dataTransformer", "loadingContainer", "bodySmall", "comparisonQuery", "selector", "fromTimerange", "toTimerange", "buildAttributeComparison", "attributeSeries", "d", "splitFrames", "displayName", "get<PERSON><PERSON><PERSON>", "getPanelConfig", "InspectAttributeAction", "AttributesComparisonScene", "updateData", "getPrimarySignalVariable", "isEqual", "byServiceScene", "sceneTimeRange", "unix", "primarySignal", "getFilteredAttributes", "grouped<PERSON>rames", "groupFrameListByAttribute", "frames", "frameGroupToDataframe", "aCompare", "b<PERSON>om<PERSON>e", "VAR_PRIMARY_SIGNAL", "hasAllValue", "buildAllComparisonLayout", "getMetricFunction", "compare<PERSON><PERSON>y", "durString", "asSeconds", "BaselineColor", "getTheme", "visualization", "getColorByName", "SelectionColor", "numberField", "nonInternalKey", "newFrame", "valueNameField", "val", "baselineTotal", "getTotalForMetaType", "selectionTotal", "metaType", "calculatedTotal", "currentValue", "ComparisonScene", "tracesByService", "actionViewsDefinitions", "getScene", "TabsBarScene", "breakpoints", "up", "md", "top", "zIndex", "setExceptionsCount", "metricScene", "allowedActionViews", "tracesCount", "enabledViews", "view", "primarySignalVariable", "timeRangeValue", "exceptionsScene", "getExceptionsScene", "subscription", "hasSetView", "embedded", "useMount", "Box", "TabsBar", "tab", "Tab", "onChangeTab", "counter", "MiniREDPanel", "buildHistogramQuery", "removeExemplarsTransformation", "getVizPanel", "getDurationVizPanel", "getRateOrErrorPanel", "setDisplayMode", "setColor", "fixedColor", "fieldHasEmptyValues", "imgWidth", "maxHeight", "MINI_PANEL_HEIGHT", "flex", "borderColor", "headerWrapper", "clickable", "input", "radioButton", "indicatorWrapper", "selectMetric", "onChangeMetricFunction", "RadioButtonList", "TracesByServiceScene", "urlActionView", "URLSearchParams", "search", "metricVariable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateExceptionsScene", "selectionFrom", "getDatasourceVariable", "updateAttributes", "actionViewDef", "buildGraphScene", "ds", "getDataSourceSrv", "VAR_DATASOURCE_EXPR", "__sceneObject", "getTagKeys", "then", "tagKeys", "l", "getUrlState", "updateFromUrl", "_urlSync", "performBrowserHistoryAction", "oldAction", "newAction", "$timeRange", "timeRangeFromSelection", "spanListTransformations", "SceneObjectUrlSyncConfig", "MetricTypeTooltip", "placement", "interactive", "hand", "subtitle", "button", "x0_5", "paddingBottom", "fontWeight", "MAIN_PANEL_HEIGHT", "typeQuery", "SceneTimeRange", "dateTime", "secondaryPanel", "tertiaryPanel", "behaviors", "sync", "DashboardCursorSync", "<PERSON><PERSON><PERSON>", "ySizing", "REDPanel", "desc", "indexByName", "Duration", "HighestDifferencePanel", "getAttribute", "valueField", "onAddToFilters", "differenceContainer", "differenceValue", "textAlign", "textWrap", "whiteSpace", "filterExists", "getFrameName", "barchart", "setMax", "overrides", "AxisPlacement", "Hidden", "overrideColor", "overrideUnit", "CustomMenu", "components", "<PERSON><PERSON>", "customMenu", "DropdownIndicator", "selectProps", "menuIsOpen", "GroupHeading", "heading", "weight", "PrimarySignalVariable", "CustomVariable", "isReadOnly", "primarySignalOptions", "buttonGroupOptions", "currentSignal", "selectOptions", "primary_signal", "disabled", "buttonGroup", "isSearchable", "IndicatorSep<PERSON><PERSON>", "SingleValue", "borderLeft", "borderBottom", "groupByKey", "metricFn", "groupByAttrs", "<PERSON><PERSON><PERSON>", "_onActivateStep", "getStepForTimeRange", "newStep", "GrotNotFound", "show404", "throttleInterval", "mousePosition", "setMousePosition", "updateMousePosition", "throttle", "clientX", "clientY", "addEventListener", "removeEventListener", "useMousePosition", "SVG", "src", "isDark", "svg", "xPos", "yPos", "innerWidth", "innerHeight", "heightRatio", "widthRatio", "rotation", "getIntermediateValue", "translation", "transform", "transform<PERSON><PERSON>in", "transition", "ratio", "start", "end", "emptyState", "remedy", "isErrorsMetric", "DrawStyle", "Bars", "StackingMode", "Normal", "matchFieldsWithNameByRegex", "DurationComparisonControl", "startInvestigation", "getMetricValue", "wrapper", "isDisabled", "isDuration", "getRateOrErrorVizPanel", "buildSelectionAnnotation", "xSel", "ySel", "arrayToDataFrame", "xMin", "xMax", "timeEnd", "yMin", "yMax", "isRegion", "lineStyle", "newData", "getYBuckets", "minDuration", "minBucket", "getMinimumsForDuration", "getLatencyThresholdVariable", "getLatencyPartialThresholdVariable", "headerContainer", "<PERSON><PERSON><PERSON><PERSON>", "titleRadioWrapper", "slowestBuckets", "fontWeightBold", "errorState", "ErrorStateScene", "<PERSON><PERSON>", "severity", "Search", "searchQuery", "onSearchQueryChange", "searchField", "Input", "cloneDataFrame", "renderFilteredData", "filtered", "performRepeat", "groupSeriesBy", "groupedData", "newSeries", "mainFrame", "sortDataFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frameIndex", "sum", "vSum", "<PERSON><PERSON><PERSON><PERSON>", "evt", "currentTarget", "onSearchQueryChangeDebounced", "debounce", "doesQueryMatchDataFrameLabels", "errors", "repeat", "itemContainer", "yAxis", "yAxisItem", "xAxis", "xAxisItem", "gridTemplateColumns", "gridAutoRows", "rowGap", "columnGap", "marginTop", "dataFrame", "pattern", "regex", "RegExp", "test", "getSignalForKey", "AttributePanelRow", "labelTitle", "valueTitle", "<PERSON><PERSON><PERSON><PERSON>", "locationService", "actionText", "actionIcon", "ErroredServicesRows", "valuesField", "getUrl", "params", "EXPLORATIONS_ROUTE", "getTotalErrs", "SlowestTracesRows", "<PERSON>r<PERSON><PERSON>", "sortedByDuration", "sortedFields", "traceServiceField", "traceNameField", "console", "error", "ROUTES", "Explore", "getDuration", "durationField", "formatDuration", "SlowestServicesRows", "AttributePanelRows", "AttributePanelScene", "getIcon", "medium", "borderTopLeftRadius", "borderTopRightRadius", "titleText", "AttributePanel", "exemplars", "renderDurationPanel", "getNoDataMessage", "getErrorMessage", "tracesContainer", "rowLeft", "rowRight", "LightModeRocket", "xmlns", "viewBox", "path", "DarkModeRocket", "cleanupParams", "delete", "SELECTION", "VAR_LATENCY_THRESHOLD", "VAR_LATENCY_PARTIAL_THRESHOLD", "useBookmarksStorage", "storage", "usePluginUserStorage", "getBookmarks", "removeBookmark", "bookmark", "bookmarkExists", "toggleBookmark", "getBookmarkForUrl", "urlQueryMap", "fromEntries", "getAll", "setBookmarks", "bookmarks", "setItem", "BOOKMARKS_LS_KEY", "getItem", "addBookmark", "filteredBookmarks", "storedBookmark", "areBookmarksEqual", "bookmarkParams", "storedBookmarkParams", "<PERSON><PERSON><PERSON>", "bookmarkKeys", "k", "storedKeys", "allKeysMatch", "every", "bookmarkFilters", "storedFilters", "BookmarkItem", "ACTION_VIEW", "PRIMARY_SIGNAL", "FILTER_SEPARATOR", "getBookmarkParams", "primarySignalFilter", "signalData", "getPrimarySignalFilter", "filtersArray", "getFiltersWithoutPrimarySignal", "EVENT_ATTR", "capitalizeFirstChar", "WebkitLineClamp", "WebkitBoxOrient", "Bookmarks", "setIsLoading", "isRemoving", "setIsRemoving", "loadedBookmarks", "h4", "LoadingPlaceholder", "p", "noBookmarks", "goToBookmark", "bookmarkItem", "remove", "stopPropagation", "updatedBookmarks", "removeBookmarkClicked", "flexWrap", "HeaderScene", "headerTitleContainer", "headerActions", "documentationLink", "subHeader", "variablesAndControls", "variables", "getTagKeysProvider", "dsVar", "datasource_", "DataSourceWithBackend", "isArray", "EVENT_INTRINSIC", "ignoredAttributesHomeFilter", "getHomeScene", "navigate", "useNavigate", "dsVariable", "filterVariable", "getHomeFilterVariable", "h2", "control", "Home", "localStorage", "DATASOURCE_LS_KEY", "buildPanels", "HOMEPAGE_FILTERS_LS_KEY", "pf", "renderedFilters", "expr", "isNumber", "endsWith", "renderFilter", "renderTraceQLLabelFilters", "initialFilters", "initialDS", "$variables", "SceneVariableSet", "DataSourceVariable", "VAR_DATASOURCE", "pluginId", "AdHocFiltersVariable", "VAR_HOME_FILTER", "allowCustomValue", "SceneTimePicker", "SceneRefreshPicker", "getAncestor", "TraceExploration", "tracesByServiceScene", "newTracesExploration", "sceneUtils", "lookupVariable", "VAR_GROUPBY", "VAR_SPAN_LIST_COLUMNS", "targetQuery", "request", "targets", "str", "toUpperCase", "TraceViewPanelScene", "panelContainer", "service1", "bar1", "service2", "bar2", "service3", "bar3", "service4", "bar4", "service5", "bar5", "service6", "bar6", "TraceDrawerScene", "TraceQLIssueDetector", "runIssueDetectionQuery", "datasourceVar", "resetIssues", "subtract", "minimalTimeRange", "toISOString", "issueDetector", "String", "hasIssue", "TraceQLConfigWarning", "detector", "EntityAssertionsWidget", "EntityAssertionsWidgetExternal", "usePluginComponent", "setTimeRange", "sub", "valueOf", "entityName", "entityType", "returnT<PERSON><PERSON><PERSON><PERSON>", "drawerSizes", "sm", "lg", "Drawer", "onClose", "closeOnMaskClick", "scrollableContent", "tabs", "drawerWidth", "onMouseDown", "onTouchStart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onMouseMove", "useCallback", "getCustomDrawerWidth", "onTouchMove", "touch", "touches", "onMouseUp", "document", "onTouchEnd", "preventDefault", "useResizebleDrawer", "wrapperStyles", "getWrapperStyles", "dragStyles", "getDragStyles", "overlayRef", "React", "dialogProps", "titleProps", "useDialog", "overlayProps", "useOverlay", "isDismissable", "isOpen", "classList", "overrideWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "open", "getContainer", "drawerContent", "rootClassName", "drawer", "classNames", "motion", "motionAppear", "motionName", "drawerMotion", "maskClassName", "mask", "maskClosable", "maskMotion", "FocusScope", "restoreFocus", "contain", "autoFocus", "aria-label", "selectors", "General", "cx", "dragHandleVertical", "resizer", "Boolean", "headerWithTabs", "IconButton", "close", "titleWrapper", "tabsWrapper", "<PERSON>rollC<PERSON>r", "showScrollIndicators", "offsetRight", "offsetWidth", "offsetLeft", "boxShadow", "shadows", "z3", "transitions", "create", "overlay", "bottom", "overflowWrap", "paddingLeft", "modal", "down", "SmartDrawer", "forceNoDrawer", "investigationButton", "<PERSON><PERSON><PERSON>er", "AttributeFiltersVariable", "addFilterButtonText", "hide", "VariableHide", "<PERSON><PERSON><PERSON><PERSON>", "readOnly", "embedderName", "expressionBuilder", "has<PERSON><PERSON><PERSON>", "updatedFilters", "initialFilter", "compositeVersion", "process", "topScene", "setupInvestigationButton", "isActive", "stateUpdate", "initialMetric", "closeDrawer", "updateInvestigationLink", "serviceNameField", "investigationLink", "getVariableSet", "TraceExplorationScene", "drawerScene", "bodyContainer", "Embedded<PERSON><PERSON><PERSON>", "TraceExplorationHeader", "setReturnToPrevious", "useReturnToPrevious", "returnToPreviousSource", "timeRangeControl", "timeRangeState", "filtersVariableState", "metricVariableState", "explorationUrl", "setExplorationUrl", "menuVisible", "setMenuVisible", "setServiceName", "getServiceNameFromFilters", "serviceNameFilter", "useServiceName", "VersionHeader", "menuHeader", "h5", "menuHeaderSubtitle", "feedbackLinksEnabled", "<PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "datasourceLabel", "Dropdown", "onVisibleChange", "helpIcon", "hideVariable", "defaultToAll", "h3", "drawerHeaderButtons", "canvas", "fontWeightMedium", "lineHeight", "topic", "DataTopic", "Annotations", "traceIDField", "parentAnchorHref", "parentElement", "UNIT_STEPS", "log10", "microseconds", "ofPrevious", "primaryUnit", "secondaryUnit", "_dropWhile", "_round", "primaryValue", "secondaryValue", "secondaryValueRounded", "primaryUnitString", "dataPoints", "dur"], "sourceRoot": ""}