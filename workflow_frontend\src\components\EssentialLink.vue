<template>
  <q-item v-if="to" clickable
          :to="to" exact
          class="my_main_menu_button">
    <q-item-section v-if="icon" avatar>
      <q-icon :name="icon"/>
    </q-item-section>

    <q-item-section>
      <q-item-label>{{ title }}</q-item-label>
      <q-item-label caption>{{ caption }}</q-item-label>
    </q-item-section>
  </q-item>

  <q-item v-if="href" clickable
          :href="href" :target="target" exact
          class="my_main_menu_button">
    <q-item-section v-if="icon" avatar>
      <q-icon :name="icon"/>
    </q-item-section>

    <q-item-section>
      <q-item-label>{{ title }}</q-item-label>
      <q-item-label caption>{{ caption }}</q-item-label>
    </q-item-section>
  </q-item>
</template>

<script>
import {defineComponent} from 'vue'

export default defineComponent({
  name: 'EssentialLink',
  props: {
    title: {
      type: String, required: true
    },
    caption: {
      type: String, default: ''
    },
    icon: {
      type: String, default: ''
    },
    target: {
      type: String, default: '_'
    },
    to: {
      type: String, default: ''
    },
    href: {
      type: String, default: ''
    }
  }
})
</script>

<style scoped>
.my_main_menu_button {
  margin: 0 auto 10px auto;
  width: 230px;
  padding: 5px 0 0 10px;
  vertical-align: middle;
  background-color: #666;
  color: #fff;
  border-radius: 2px;
  border: 1px solid #333;
  background-color: #666;
}

.my_main_menu_button.q-router-link--active,
.my_main_menu_button:hover {
  border: 1px solid #be6666;
  background-color: #be1919;
  color: #fff;
  opacity: 1;
}

.my_main_menu_button .q-item__section--avatar {
  padding-right: 15px;
  padding-bottom: 5px;
  padding-left: 5px;
  min-width: 0;
}
</style>
