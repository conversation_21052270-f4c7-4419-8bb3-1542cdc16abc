# Use a lightweight Python base image
FROM python:3.13

# Set the working directory
WORKDIR /workflow_backend

# Copy the requirements file into the container
COPY requirements.txt .

# Install Python dependencies
RUN pip install -r requirements.txt

# Copy the application code into the container
COPY . .

# Expose the port the app runs on
EXPOSE 8000

# Set the default command to run the app
CMD ["fastapi", "run", "--host", "0.0.0.0", "--port", "8000"]