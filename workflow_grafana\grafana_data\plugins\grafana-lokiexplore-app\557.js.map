{"version": 3, "file": "557.js?_cache=a3b175be8d0fd60ff808", "mappings": "msCAuCA,MAAMA,EAAiB,CACrBC,IAAK,CACHC,U,+BACAC,Q,+BACAC,M,gCAEF,eAAgB,CACdF,U,+BACAC,Q,+BACAC,M,gCAEF,cAAe,CACbF,U,+BACAC,Q,+BACAC,M,gCAEF,YAAa,CACXF,U,+BACAE,M,+BAEAD,QAAS,IAEXE,KAAM,CACJH,UAAWG,EACXD,MAAOE,EAEPH,QAAS,KAIPI,EAAYC,EAAAA,WAA0D,CAACC,EAAOC,KAClF,MAAM,QAAEC,EAAU,YAAW,KAAEC,EAAI,UAAEC,EAAS,QAAEC,GAA0BL,EAAdM,EAAAA,EAAcN,EAAAA,CAAlEE,UAAuBC,OAAMC,YAAWC,YAE1CE,GAASC,EAAAA,EAAAA,YAAWC,EAAWP,EAASC,EAAMZ,GAEpD,IAAImB,EACAC,EAIJ,OAHAD,EAA+B,iBAAZL,EAAuBA,OAAUO,GAG7CC,EAAAA,EAAAA,SACL,IACE,gBAACC,EAAAA,QAAOA,CAACb,IAAKA,EAAKc,QAASV,GAC1B,gBAACW,SAAAA,E,kUAAAA,CAAAA,CAAAA,EACKV,GAAAA,CACJL,IAAKU,EACLM,aAAYP,EACZN,WAAWc,EAAAA,EAAAA,IAAGX,EAAOS,OAAQZ,GAC7Be,KAAK,WAEL,gBAACC,OAAAA,CAAKhB,UAAWG,EAAOc,QAI9B,CAACX,EAAWT,EAAKG,EAAWE,EAAWC,EAAQF,EAASM,MAG5Db,EAAUwB,YAAc,YACxB,UAEMb,EAAY,CAACc,EAAsBrB,EAA4BC,EAAgBZ,KACnF,IAAIiC,EAAYD,EAAME,OAAOC,KAAKhC,QAMlC,MAJgB,YAAZQ,IACFsB,EAAYD,EAAME,OAAO/B,QAAQgC,MAG5B,CACLV,QAAQW,EAAAA,EAAAA,KAAI,CACVC,OAAQ,EACRC,SAAU,WACVC,OAAQ,KAAKP,EAAMQ,QAAQC,WAC3BC,UAAW,OACXC,OAAQ,OACRC,QAAS,cACTC,WAAY,cACZC,eAAgB,SAChBC,WAAY,SACZC,QAAS,EACTC,MAAOhB,EAEP,0BAA2B,CACzBiB,OAAQ,cACRD,MAAOjB,EAAME,OAAOiB,OAAOC,aAC3BC,QAAS,KAGX,2BAA4B,CAC1BC,QAAS,yBACTC,cAAe,MACfb,UAAW,aAAaV,EAAME,OAAOW,WAAWW,uBAAuBxB,EAAME,OAAO/B,QAAQsD,OAC5FC,yBAA0B,iCAC1BC,mBAAoB,OACpBC,mBAAoB,uCAGtB,8BAA+B,CAC7BN,QAAS,OACTZ,UAAW,UAGfmB,MAAMzB,EAAAA,EAAAA,KAAI,CACR0B,cAAe,aAEjBhC,KAAKM,EAAAA,EAAAA,KAAI,CACP2B,gBAA6B,YAAZpD,EAAwB,OAAOX,EAAOY,GAAMT,WAAa,OAAOH,EAAOY,GAAMV,aAC9F8D,MAAO,OACPC,OAAQ,OAER,WAAY,CACVD,MAAO,OACPC,OAAQ,OACRC,KAAM,EACN7B,QAAS,EACTC,SAAU,WACVe,QAAS,EACTc,aAAcnC,EAAMoC,MAAMC,OAAOC,QACjC9C,QAAS,KACT+C,UAAW,cACX,CAACvC,EAAMwC,YAAYC,aAAa,gBAAiB,WAAY,CAC3Dd,mBAAoB,OACpBD,yBAA0B,+BAC1BE,mBAAoB,YAIxB,UAAW,CACTG,gBAAiB,OAAO/D,EAAOY,GAAMR,SACrC,WAAY,CACVsE,gBACc,cAAZ/D,EAA0BqB,EAAME,OAAOiB,OAAO/C,MAAQuE,EAAAA,iBAAiBC,MAAM3C,EAAW,KAC1FoB,QAAS,O", "sources": ["webpack://grafana-lokiexplore-app/./Components/UI/ImgButton.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useMemo } from 'react';\n\nimport { css, cx } from '@emotion/css';\n\nimport { colorManipulator, GrafanaTheme2 } from '@grafana/data';\nimport { PopoverContent, Tooltip, useStyles2 } from '@grafana/ui';\n\nimport copy from 'img/icons/copy.svg';\nimport copyHover from 'img/icons/copy--hover.svg';\nimport eye from 'img/icons/eye.svg';\nimport eyeActive from 'img/icons/eye--active.svg';\nimport eyeHover from 'img/icons/eye--hover.svg';\nimport searchMinus from 'img/icons/search-minus.svg';\nimport searchMinusActive from 'img/icons/search-minus--active.svg';\nimport searchMinusHover from 'img/icons/search-minus--hover.svg';\nimport searchPlus from 'img/icons/search-plus.svg';\nimport searchPlusActive from 'img/icons/search-plus--active.svg';\nimport searchPlusHover from 'img/icons/search-plus--hover.svg';\nimport shareAlt from 'img/icons/share-alt.svg';\nimport shareAltHover from 'img/icons/share-alt--hover.svg';\n\ntype IconButtonVariant = 'primary' | 'secondary';\ntype IconName = 'copy' | 'eye' | 'search-minus' | 'search-plus' | 'share-alt';\n\ninterface BaseProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'aria-label'> {\n  /** Name of the icon **/\n  name: IconName;\n  /** Variant to change the color of the Icon */\n  variant?: IconButtonVariant;\n}\n\nexport interface BasePropsWithTooltip extends BaseProps {\n  /** Tooltip content to display on hover and as the aria-label */\n  tooltip: PopoverContent;\n}\n\ntype Images = Record<IconName, Record<IconButtonVariant | 'hover', string>>;\n\nconst images: Images = {\n  eye: {\n    secondary: eye,\n    primary: eyeActive,\n    hover: eyeHover,\n  },\n  'search-minus': {\n    secondary: searchMinus,\n    primary: searchMinusActive,\n    hover: searchMinusHover,\n  },\n  'search-plus': {\n    secondary: searchPlus,\n    primary: searchPlusActive,\n    hover: searchPlusHover,\n  },\n  'share-alt': {\n    secondary: shareAlt,\n    hover: shareAltHover,\n    // Unused\n    primary: '',\n  },\n  copy: {\n    secondary: copy,\n    hover: copyHover,\n    // Unused\n    primary: '',\n  },\n};\n\nconst ImgButton = React.forwardRef<HTMLButtonElement, BasePropsWithTooltip>((props, ref) => {\n  const { variant = 'secondary', name, className, tooltip, ...restProps } = props;\n\n  const styles = useStyles2(getStyles, variant, name, images);\n\n  let ariaLabel: string | undefined;\n  let buttonRef: typeof ref | undefined;\n  ariaLabel = typeof tooltip === 'string' ? tooltip : undefined;\n\n  // When using tooltip, ref is forwarded to Tooltip component instead for https://github.com/grafana/grafana/issues/65632\n  return useMemo(\n    () => (\n      <Tooltip ref={ref} content={tooltip}>\n        <button\n          {...restProps}\n          ref={buttonRef}\n          aria-label={ariaLabel}\n          className={cx(styles.button, className)}\n          type=\"button\"\n        >\n          <span className={styles.img}></span>\n        </button>\n      </Tooltip>\n    ),\n    [ariaLabel, ref, className, restProps, styles, tooltip, buttonRef]\n  );\n});\nImgButton.displayName = 'ImgButton';\nexport default ImgButton;\n\nconst getStyles = (theme: GrafanaTheme2, variant: IconButtonVariant, name: IconName, images: Images) => {\n  let iconColor = theme.colors.text.primary;\n\n  if (variant === 'primary') {\n    iconColor = theme.colors.primary.text;\n  }\n\n  return {\n    button: css({\n      zIndex: 0,\n      position: 'relative',\n      margin: `0 ${theme.spacing.x0_5} 0 0`,\n      boxShadow: 'none',\n      border: 'none',\n      display: 'inline-flex',\n      background: 'transparent',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 0,\n      color: iconColor,\n\n      '&[disabled], &:disabled': {\n        cursor: 'not-allowed',\n        color: theme.colors.action.disabledText,\n        opacity: 0.65,\n      },\n\n      '&:focus, &:focus-visible': {\n        outline: '2px dotted transparent',\n        outlineOffset: '2px',\n        boxShadow: `0 0 0 2px ${theme.colors.background.canvas}, 0 0 0px 4px ${theme.colors.primary.main}`,\n        transitionTimingFunction: `cubic-bezier(0.19, 1, 0.22, 1)`,\n        transitionDuration: '0.2s',\n        transitionProperty: 'outline, outline-offset, box-shadow',\n      },\n\n      '&:focus:not(:focus-visible)': {\n        outline: 'none',\n        boxShadow: `none`,\n      },\n    }),\n    icon: css({\n      verticalAlign: 'baseline',\n    }),\n    img: css({\n      backgroundImage: variant === 'primary' ? `url(${images[name].primary})` : `url(${images[name].secondary})`,\n      width: '16px',\n      height: '16px',\n\n      '&:before': {\n        width: '16px',\n        height: '16px',\n        left: 0,\n        zIndex: -1,\n        position: 'absolute',\n        opacity: 0,\n        borderRadius: theme.shape.radius.default,\n        content: '\"\"',\n        transform: 'scale(1.45)',\n        [theme.transitions.handleMotion('no-preference', 'reduce')]: {\n          transitionDuration: '0.2s',\n          transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',\n          transitionProperty: 'opacity',\n        },\n      },\n\n      '&:hover': {\n        backgroundImage: `url(${images[name].hover})`,\n        '&:before': {\n          backgroundColor:\n            variant === 'secondary' ? theme.colors.action.hover : colorManipulator.alpha(iconColor, 0.12),\n          opacity: 1,\n        },\n      },\n    }),\n  };\n};\n"], "names": ["images", "eye", "secondary", "primary", "hover", "copy", "copyHover", "ImgButton", "React", "props", "ref", "variant", "name", "className", "tooltip", "restProps", "styles", "useStyles2", "getStyles", "aria<PERSON><PERSON><PERSON>", "buttonRef", "undefined", "useMemo", "<PERSON><PERSON><PERSON>", "content", "button", "aria-label", "cx", "type", "span", "img", "displayName", "theme", "iconColor", "colors", "text", "css", "zIndex", "position", "margin", "spacing", "x0_5", "boxShadow", "border", "display", "background", "justifyContent", "alignItems", "padding", "color", "cursor", "action", "disabledText", "opacity", "outline", "outlineOffset", "canvas", "main", "transitionTimingFunction", "transitionDuration", "transitionProperty", "icon", "verticalAlign", "backgroundImage", "width", "height", "left", "borderRadius", "shape", "radius", "default", "transform", "transitions", "handleMotion", "backgroundColor", "colorManipulator", "alpha"], "sourceRoot": ""}