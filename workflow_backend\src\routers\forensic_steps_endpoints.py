import logging

from fastapi import APIRouter, HTTPException

from workflow_backend.src.Define import Step, ProcessStatus
from workflow_backend.src.config import MAX_FOCUS_INSTANCES

from workflow_backend.src.database.database_helpers import run_query

forensic_steps_router = APIRouter(prefix="/forensic-steps", tags=["forensic-steps"])


@forensic_steps_router.get("")
async def get_forensic_steps():
    """
    Get forensic steps sorted by display_position.
    """
    result = run_query("SELECT * FROM forensic_steps ORDER BY display_position;", {})
    return result


@forensic_steps_router.get("/{step_id}/next-job")
async def get_next_job(step_id: int):
    """
    Get the database entry of the next process to run in the given step given ("next" is thereby determined based on
    'prioritize' and 'created_at'). Set the process to status 'assigned'.

    If FOCUS.AI is requesting its next job, make sure that the maximum of FOCUS instances (that are allowed to run in
     parallel based on the value specified in the .env file) is not exceeded.
    """
    logging.info("Getting next job for step {step_id} ...")
    no_job_found = True

    # If FOCUS.AI is requesting its next job, first check how many FOCUS instances are already running
    if step_id == Step.FOCUS_AI:
        # if there are already n processes running, do not return a next job
        # (this mechanismus is used to limit the amount of FOCUS instances that are started in parallel)
        query_focus = """SELECT COUNT(*) AS running_count FROM processes 
                        WHERE step_id = :step_id AND (status = :status_running or status = :status_assigned);"""
        result = run_query(query_focus, {"step_id":         step_id,
                                         "status_running":  ProcessStatus.RUNNING,
                                         "status_assigned": ProcessStatus.ASSIGNED})
        count_currently_running_focus_instances = result[0]["running_count"] if result else 0

        if count_currently_running_focus_instances >= MAX_FOCUS_INSTANCES:
            logging.warning(f"""Maximal number of FOCUS.AI processes with status RUNNING or ASSIGNED reached (current: {count_currently_running_focus_instances}, allowed: {MAX_FOCUS_INSTANCES}). 
                            Next job will be available as soon as one of those finishes.""")
            raise HTTPException(status_code=404, detail="There are too many running FOCUS.AI processes. Next job will be available as soon as one of those finishes.")

    try:
        # Fetch the next job based on 'prioritize' and 'updated_at'
        query = """UPDATE processes 
                    SET status = :status_assigned, updated_at = now() 
                    WHERE id in 
                        (SELECT id FROM processes WHERE step_id = :step_id and status = :status
                        ORDER BY prioritize DESC, created_at ASC LIMIT 1)
                    RETURNING *;"""

        result_queued_process = run_query(query, {"step_id":         step_id,
                                                  "status":          ProcessStatus.QUEUED,
                                                  "status_assigned": ProcessStatus.ASSIGNED})

        if not result_queued_process or len(result_queued_process) == 0:
            logging.info(f"No queued process found for step_id {step_id}.")
            no_job_found = True
        else:
            logging.info(f"Next job for step {step_id}: {result_queued_process[0]}")
            # return next job
            return result_queued_process[0]

    except Exception as e:
        logging.error(f"Error in get_next_job: {e}")
        raise HTTPException(status_code=500, detail="Internal server error.")

    if no_job_found:
        raise HTTPException(status_code=404, detail="No queued process found.")
    return None
