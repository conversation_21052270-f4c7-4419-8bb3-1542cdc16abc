(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[864],{5755:(e,t,n)=>{"use strict";n.d(t,{JY:()=>_r,sx:()=>ti,gL:()=>Ri});var r=n(5959),i=n.n(r),o=n(8398),a=n.n(o),s=n(7694),l=n(200);function c(e,t){var n=(0,r.useState)(function(){return{inputs:t,result:e()}})[0],i=(0,r.useRef)(!0),o=(0,r.useRef)(n),a=i.current||Boolean(t&&o.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,o.current.inputs))?o.current:{inputs:t,result:e()};return(0,r.useEffect)(function(){i.current=!1,o.current=a},[a]),a.result}var d=c,u=function(e,t){return c(function(){return e},t)},p="Invariant failed";var f=function(e){var t=e.top,n=e.right,r=e.bottom,i=e.left;return{top:t,right:n,bottom:r,left:i,width:n-i,height:r-t,x:i,y:t,center:{x:(n+i)/2,y:(r+t)/2}}},g=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},h=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},m={top:0,right:0,bottom:0,left:0},b=function(e){var t=e.borderBox,n=e.margin,r=void 0===n?m:n,i=e.border,o=void 0===i?m:i,a=e.padding,s=void 0===a?m:a,l=f(g(t,r)),c=f(h(t,o)),d=f(h(c,s));return{marginBox:l,borderBox:f(t),paddingBox:c,contentBox:d,margin:r,border:o,padding:s}},v=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var n=Number(t);return isNaN(n)&&function(e){if(!e)throw new Error(p)}(!1),n},y=function(e,t){var n,r,i=e.borderBox,o=e.border,a=e.margin,s=e.padding,l=(r=t,{top:(n=i).top+r.y,left:n.left+r.x,bottom:n.bottom+r.y,right:n.right+r.x});return b({borderBox:l,border:o,margin:a,padding:s})},x=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),y(e,t)},w=function(e,t){var n={top:v(t.marginTop),right:v(t.marginRight),bottom:v(t.marginBottom),left:v(t.marginLeft)},r={top:v(t.paddingTop),right:v(t.paddingRight),bottom:v(t.paddingBottom),left:v(t.paddingLeft)},i={top:v(t.borderTopWidth),right:v(t.borderRightWidth),bottom:v(t.borderBottomWidth),left:v(t.borderLeftWidth)};return b({borderBox:e,margin:n,padding:r,border:i})},I=function(e){var t=e.getBoundingClientRect(),n=window.getComputedStyle(e);return w(t,n)},D=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function E(e,t){return e===t||!(!D(e)||!D(t))}function S(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!E(e[n],t[n]))return!1;return!0}function A(e,t){void 0===t&&(t=S);var n=null;function r(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];if(n&&n.lastThis===this&&t(r,n.lastArgs))return n.lastResult;var o=e.apply(this,r);return n={lastResult:o,lastArgs:r,lastThis:this},o}return r.clear=function(){n=null},r}const R=function(e){var t=[],n=null,r=function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];t=i,n||(n=requestAnimationFrame(function(){n=null,e.apply(void 0,t)}))};return r.cancel=function(){n&&(cancelAnimationFrame(n),n=null)},r};function C(){return C=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},C.apply(null,arguments)}function O(e,t){}O.bind(null,"warn"),O.bind(null,"error");function P(){}function N(e,t,n){const r=t.map(t=>{const r=function(e,t){return{...e,...t}}(n,t.options);return e.addEventListener(t.eventName,t.fn,r),function(){e.removeEventListener(t.eventName,t.fn,r)}});return function(){r.forEach(e=>{e()})}}const B=!0,T="Invariant failed";class L extends Error{}function M(e,t){if(!e)throw new L(B?T:`${T}: ${t||""}`)}L.prototype.toString=function(){return this.message};class _ extends i().Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=P,this.onWindowError=e=>{const t=this.getCallbacks();t.isDragging()&&t.tryAbort();e.error instanceof L&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=N(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(!(e instanceof L))throw e;this.setState({})}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}const G=e=>e+1,z=(e,t)=>{const n=e.droppableId===t.droppableId,r=G(e.index),i=G(t.index);return n?`\n      You have moved the item from position ${r}\n      to position ${i}\n    `:`\n    You have moved the item from position ${r}\n    in list ${e.droppableId}\n    to list ${t.droppableId}\n    in position ${i}\n  `},W=(e,t,n)=>t.droppableId===n.droppableId?`\n      The item ${e}\n      has been combined with ${n.draggableId}`:`\n      The item ${e}\n      in list ${t.droppableId}\n      has been combined with ${n.draggableId}\n      in list ${n.droppableId}\n    `,k=e=>`\n  The item has returned to its starting position\n  of ${G(e.index)}\n`,F={dragHandleUsageInstructions:"\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n",onDragStart:e=>`\n  You have lifted an item in position ${G(e.source.index)}\n`,onDragUpdate:e=>{const t=e.destination;if(t)return z(e.source,t);const n=e.combine;return n?W(e.draggableId,e.source,n):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`\n      Movement cancelled.\n      ${k(e.source)}\n    `;const t=e.destination,n=e.combine;return t?`\n      You have dropped the item.\n      ${z(e.source,t)}\n    `:n?`\n      You have dropped the item.\n      ${W(e.draggableId,e.source,n)}\n    `:`\n    The item has been dropped while not over a drop area.\n    ${k(e.source)}\n  `}};var j=F;const H={x:0,y:0},U=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),$=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),V=(e,t)=>e.x===t.x&&e.y===t.y,q=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),Y=(e,t,n=0)=>"x"===e?{x:t,y:n}:{x:n,y:t},X=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),J=(e,t)=>Math.min(...t.map(t=>X(e,t))),K=e=>t=>({x:e(t.x),y:e(t.y)});const Q=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),Z=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],ee=(e,t)=>t&&t.shouldClipSubject?((e,t)=>{const n=f({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return n.width<=0||n.height<=0?null:n})(t.pageMarginBox,e):f(e);var te=({page:e,withPlaceholder:t,axis:n,frame:r})=>{const i=((e,t)=>t?Q(e,t.scroll.diff.displacement):e)(e.marginBox,r),o=((e,t,n)=>n&&n.increasedBy?{...e,[t.end]:e[t.end]+n.increasedBy[t.line]}:e)(i,n,t);return{page:e,withPlaceholder:t,active:ee(o,r)}},ne=(e,t)=>{e.frame||M(!1);const n=e.frame,r=$(t,n.scroll.initial),i=q(r),o={...n,scroll:{initial:n.scroll.initial,current:t,diff:{value:r,displacement:i},max:n.scroll.max}},a=te({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:o});return{...e,frame:o,subject:a}};const re=A(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),ie=A(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),oe=A(e=>Object.values(e)),ae=A(e=>Object.values(e));var se=A((e,t)=>{const n=ae(t).filter(t=>e===t.descriptor.droppableId).sort((e,t)=>e.descriptor.index-t.descriptor.index);return n});function le(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function ce(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var de=A((e,t)=>t.filter(t=>t.descriptor.id!==e.descriptor.id)),ue=(e,t)=>e.descriptor.droppableId===t.descriptor.id;const pe={point:H,value:0},fe={invisible:{},visible:{},all:[]};var ge={displaced:fe,displacedBy:pe,at:null},he=(e,t)=>n=>e<=n&&n<=t,me=e=>{const t=he(e.top,e.bottom),n=he(e.left,e.right);return r=>{if(t(r.top)&&t(r.bottom)&&n(r.left)&&n(r.right))return!0;const i=t(r.top)||t(r.bottom),o=n(r.left)||n(r.right);if(i&&o)return!0;const a=r.top<e.top&&r.bottom>e.bottom,s=r.left<e.left&&r.right>e.right;if(a&&s)return!0;return a&&o||s&&i}},be=e=>{const t=he(e.top,e.bottom),n=he(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&n(e.left)&&n(e.right)};const ve={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},ye={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};const xe=({target:e,destination:t,viewport:n,withDroppableDisplacement:r,isVisibleThroughFrameFn:i})=>{const o=r?((e,t)=>{const n=t.frame?t.frame.scroll.diff.displacement:H;return Q(e,n)})(e,t):e;return((e,t,n)=>!!t.subject.active&&n(t.subject.active)(e))(o,t,i)&&((e,t,n)=>n(t)(e))(o,n,i)},we=e=>xe({...e,isVisibleThroughFrameFn:be});function Ie({afterDragging:e,destination:t,displacedBy:n,viewport:r,forceShouldAnimate:i,last:o}){return e.reduce(function(e,a){const s=function(e,t){const n=e.page.marginBox,r={top:t.point.y,right:0,bottom:0,left:t.point.x};return f(g(n,r))}(a,n),l=a.descriptor.id;e.all.push(l);var c;if(!(c={target:s,destination:t,viewport:r,withDroppableDisplacement:!0},xe({...c,isVisibleThroughFrameFn:me})))return e.invisible[a.descriptor.id]=!0,e;const d=((e,t,n)=>{if("boolean"==typeof n)return n;if(!t)return!0;const{invisible:r,visible:i}=t;if(r[e])return!1;const o=i[e];return!o||o.shouldAnimate})(l,o,i),u={draggableId:l,shouldAnimate:d};return e.visible[l]=u,e},{all:[],visible:{},invisible:{}})}function De({insideDestination:e,inHomeList:t,displacedBy:n,destination:r}){const i=function(e,t){if(!e.length)return 0;const n=e[e.length-1].descriptor.index;return t.inHomeList?n:n+1}(e,{inHomeList:t});return{displaced:fe,displacedBy:n,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:i}}}}function Ee({draggable:e,insideDestination:t,destination:n,viewport:r,displacedBy:i,last:o,index:a,forceShouldAnimate:s}){const l=ue(e,n);if(null==a)return De({insideDestination:t,inHomeList:l,displacedBy:i,destination:n});const c=t.find(e=>e.descriptor.index===a);if(!c)return De({insideDestination:t,inHomeList:l,displacedBy:i,destination:n});const d=de(e,t),u=t.indexOf(c);return{displaced:Ie({afterDragging:d.slice(u),destination:n,displacedBy:i,last:o,viewport:r.frame,forceShouldAnimate:s}),displacedBy:i,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:a}}}}function Se(e,t){return Boolean(t.effected[e])}var Ae=({isMovingForward:e,isInHomeList:t,draggable:n,draggables:r,destination:i,insideDestination:o,previousImpact:a,viewport:s,afterCritical:l})=>{const c=a.at;if(c||M(!1),"REORDER"===c.type){const r=(({isMovingForward:e,isInHomeList:t,insideDestination:n,location:r})=>{if(!n.length)return null;const i=r.index,o=e?i+1:i-1,a=n[0].descriptor.index,s=n[n.length-1].descriptor.index;return o<a||o>(t?s:s+1)?null:o})({isMovingForward:e,isInHomeList:t,location:c.destination,insideDestination:o});return null==r?null:Ee({draggable:n,insideDestination:o,destination:i,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:r})}const d=(({isMovingForward:e,destination:t,draggables:n,combine:r,afterCritical:i})=>{if(!t.isCombineEnabled)return null;const o=r.draggableId,a=n[o].descriptor.index;return Se(o,i)?e?a:a-1:e?a+1:a})({isMovingForward:e,destination:i,displaced:a.displaced,draggables:r,combine:c.combine,afterCritical:l});return null==d?null:Ee({draggable:n,insideDestination:o,destination:i,viewport:s,last:a.displaced,displacedBy:a.displacedBy,index:d})},Re=({afterCritical:e,impact:t,draggables:n})=>{const r=ce(t);r||M(!1);const i=r.draggableId,o=n[i].page.borderBox.center,a=(({displaced:e,afterCritical:t,combineWith:n,displacedBy:r})=>{const i=Boolean(e.visible[n]||e.invisible[n]);return Se(n,t)?i?H:q(r.point):i?r.point:H})({displaced:t.displaced,afterCritical:e,combineWith:i,displacedBy:t.displacedBy});return U(o,a)};const Ce=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,Oe=(e,t,n)=>t[e.crossAxisStart]+n.margin[e.crossAxisStart]+n.borderBox[e.crossAxisSize]/2,Pe=({axis:e,moveRelativeTo:t,isMoving:n})=>Y(e.line,t.marginBox[e.end]+Ce(e,n),Oe(e,t.marginBox,n)),Ne=({axis:e,moveRelativeTo:t,isMoving:n})=>Y(e.line,t.marginBox[e.start]-((e,t)=>t.margin[e.end]+t.borderBox[e.size]/2)(e,n),Oe(e,t.marginBox,n));var Be=({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i})=>{const o=se(r.descriptor.id,n),a=t.page,s=r.axis;if(!o.length)return(({axis:e,moveInto:t,isMoving:n})=>Y(e.line,t.contentBox[e.start]+Ce(e,n),Oe(e,t.contentBox,n)))({axis:s,moveInto:r.page,isMoving:a});const{displaced:l,displacedBy:c}=e,d=l.all[0];if(d){const e=n[d];if(Se(d,i))return Ne({axis:s,moveRelativeTo:e.page,isMoving:a});const t=y(e.page,c.point);return Ne({axis:s,moveRelativeTo:t,isMoving:a})}const u=o[o.length-1];if(u.descriptor.id===t.descriptor.id)return a.borderBox.center;if(Se(u.descriptor.id,i)){const e=y(u.page,q(i.displacedBy.point));return Pe({axis:s,moveRelativeTo:e,isMoving:a})}return Pe({axis:s,moveRelativeTo:u.page,isMoving:a})},Te=(e,t)=>{const n=e.frame;return n?U(t,n.scroll.diff.displacement):t};var Le=e=>{const t=(({impact:e,draggable:t,droppable:n,draggables:r,afterCritical:i})=>{const o=t.page.borderBox.center,a=e.at;return n&&a?"REORDER"===a.type?Be({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:i}):Re({impact:e,draggables:r,afterCritical:i}):o})(e),n=e.droppable;return n?Te(n,t):t},Me=(e,t)=>{const n=$(t,e.scroll.initial),r=q(n);return{frame:f({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:n,displacement:r}}}};function _e(e,t){return e.map(e=>t[e])}var Ge=({pageBorderBoxCenter:e,draggable:t,viewport:n})=>{const r=((e,t)=>U(e.scroll.diff.displacement,t))(n,e),i=$(r,t.page.borderBox.center);return U(t.client.borderBox.center,i)},ze=({draggable:e,destination:t,newPageBorderBoxCenter:n,viewport:r,withDroppableDisplacement:i,onlyOnMainAxis:o=!1})=>{const a=$(n,e.page.borderBox.center),s={target:Q(e.page.borderBox,a),destination:t,withDroppableDisplacement:i,viewport:r};return o?(e=>{return xe({...e,isVisibleThroughFrameFn:(t=e.destination.axis,e=>{const n=he(e.top,e.bottom),r=he(e.left,e.right);return e=>t===ve?n(e.top)&&n(e.bottom):r(e.left)&&r(e.right)})});var t})(s):we(s)},We=({isMovingForward:e,draggable:t,destination:n,draggables:r,previousImpact:i,viewport:o,previousPageBorderBoxCenter:a,previousClientSelection:s,afterCritical:l})=>{if(!n.isEnabled)return null;const c=se(n.descriptor.id,r),d=ue(t,n),u=(({isMovingForward:e,draggable:t,destination:n,insideDestination:r,previousImpact:i})=>{if(!n.isCombineEnabled)return null;if(!le(i))return null;function o(e){const t={type:"COMBINE",combine:{draggableId:e,droppableId:n.descriptor.id}};return{...i,at:t}}const a=i.displaced.all,s=a.length?a[0]:null;if(e)return s?o(s):null;const l=de(t,r);if(!s)return l.length?o(l[l.length-1].descriptor.id):null;const c=l.findIndex(e=>e.descriptor.id===s);-1===c&&M(!1);const d=c-1;return d<0?null:o(l[d].descriptor.id)})({isMovingForward:e,draggable:t,destination:n,insideDestination:c,previousImpact:i})||Ae({isMovingForward:e,isInHomeList:d,draggable:t,draggables:r,destination:n,insideDestination:c,previousImpact:i,viewport:o,afterCritical:l});if(!u)return null;const p=Le({impact:u,draggable:t,droppable:n,draggables:r,afterCritical:l});if(ze({draggable:t,destination:n,newPageBorderBoxCenter:p,viewport:o.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})){return{clientSelection:Ge({pageBorderBoxCenter:p,draggable:t,viewport:o}),impact:u,scrollJumpRequest:null}}const f=$(p,a),g=(({impact:e,viewport:t,destination:n,draggables:r,maxScrollChange:i})=>{const o=Me(t,U(t.scroll.current,i)),a=n.frame?ne(n,U(n.frame.scroll.current,i)):n,s=e.displaced,l=Ie({afterDragging:_e(s.all,r),destination:n,displacedBy:e.displacedBy,viewport:o.frame,last:s,forceShouldAnimate:!1}),c=Ie({afterDragging:_e(s.all,r),destination:a,displacedBy:e.displacedBy,viewport:t.frame,last:s,forceShouldAnimate:!1}),d={},u={},p=[s,l,c];return s.all.forEach(e=>{const t=function(e,t){for(let n=0;n<t.length;n++){const r=t[n].visible[e];if(r)return r}return null}(e,p);t?u[e]=t:d[e]=!0}),{...e,displaced:{all:s.all,invisible:d,visible:u}}})({impact:u,viewport:o,destination:n,draggables:r,maxScrollChange:f});return{clientSelection:s,impact:g,scrollJumpRequest:f}};const ke=e=>{const t=e.subject.active;return t||M(!1),t};const Fe=(e,t)=>{const n=e.page.borderBox.center;return Se(e.descriptor.id,t)?$(n,t.displacedBy.point):n},je=(e,t)=>{const n=e.page.borderBox;return Se(e.descriptor.id,t)?Q(n,q(t.displacedBy.point)):n};var He=A(function(e,t){const n=t[e.line];return{value:n,point:Y(e.line,n)}});const Ue=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),$e=(e,t,n)=>{const r=e.frame;ue(t,e)&&M(!1),e.subject.withPlaceholder&&M(!1);const i=He(e.axis,t.displaceBy).point,o=((e,t,n)=>{const r=e.axis;if("virtual"===e.descriptor.mode)return Y(r.line,t[r.line]);const i=e.subject.page.contentBox[r.size],o=se(e.descriptor.id,n).reduce((e,t)=>e+t.client.marginBox[r.size],0)+t[r.line]-i;return o<=0?null:Y(r.line,o)})(e,i,n),a={placeholderSize:i,increasedBy:o,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!r){const t=te({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame});return{...e,subject:t}}const s=o?U(r.scroll.max,o):r.scroll.max,l=Ue(r,s),c=te({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:l});return{...e,subject:c,frame:l}};var Ve=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:n,isOver:r,draggables:i,droppables:o,viewport:a,afterCritical:s})=>{const l=(({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:r,viewport:i})=>{const o=n.subject.active;if(!o)return null;const a=n.axis,s=he(o[a.start],o[a.end]),l=oe(r).filter(e=>e!==n).filter(e=>e.isEnabled).filter(e=>Boolean(e.subject.active)).filter(e=>me(i.frame)(ke(e))).filter(t=>{const n=ke(t);return e?o[a.crossAxisEnd]<n[a.crossAxisEnd]:n[a.crossAxisStart]<o[a.crossAxisStart]}).filter(e=>{const t=ke(e),n=he(t[a.start],t[a.end]);return s(t[a.start])||s(t[a.end])||n(o[a.start])||n(o[a.end])}).sort((t,n)=>{const r=ke(t)[a.crossAxisStart],i=ke(n)[a.crossAxisStart];return e?r-i:i-r}).filter((e,t,n)=>ke(e)[a.crossAxisStart]===ke(n[0])[a.crossAxisStart]);if(!l.length)return null;if(1===l.length)return l[0];const c=l.filter(e=>he(ke(e)[a.start],ke(e)[a.end])(t[a.line]));return 1===c.length?c[0]:c.length>1?c.sort((e,t)=>ke(e)[a.start]-ke(t)[a.start])[0]:l.sort((e,n)=>{const r=J(t,Z(ke(e))),i=J(t,Z(ke(n)));return r!==i?r-i:ke(e)[a.start]-ke(n)[a.start]})[0]})({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:o,viewport:a});if(!l)return null;const c=se(l.descriptor.id,i),d=(({pageBorderBoxCenter:e,viewport:t,destination:n,insideDestination:r,afterCritical:i})=>{const o=r.filter(e=>we({target:je(e,i),destination:n,viewport:t.frame,withDroppableDisplacement:!0})).sort((t,r)=>{const o=X(e,Te(n,Fe(t,i))),a=X(e,Te(n,Fe(r,i)));return o<a?-1:a<o?1:t.descriptor.index-r.descriptor.index});return o[0]||null})({pageBorderBoxCenter:t,viewport:a,destination:l,insideDestination:c,afterCritical:s}),u=(({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:n,draggable:r,draggables:i,destination:o,viewport:a,afterCritical:s})=>{if(!t){if(n.length)return null;const e={displaced:fe,displacedBy:pe,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:0}}},t=Le({impact:e,draggable:r,droppable:o,draggables:i,afterCritical:s}),l=ue(r,o)?o:$e(o,r,i);return ze({draggable:r,destination:l,newPageBorderBoxCenter:t,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}const l=Boolean(e[o.axis.line]<=t.page.borderBox.center[o.axis.line]),c=(()=>{const e=t.descriptor.index;return t.descriptor.id===r.descriptor.id||l?e:e+1})(),d=He(o.axis,r.displaceBy);return Ee({draggable:r,insideDestination:n,destination:o,viewport:a,displacedBy:d,last:fe,index:c})})({previousPageBorderBoxCenter:t,destination:l,draggable:n,draggables:i,moveRelativeTo:d,insideDestination:c,viewport:a,afterCritical:s});if(!u)return null;const p=Le({impact:u,draggable:n,droppable:l,draggables:i,afterCritical:s});return{clientSelection:Ge({pageBorderBoxCenter:p,draggable:n,viewport:a}),impact:u,scrollJumpRequest:null}},qe=e=>{const t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};var Ye=({state:e,type:t})=>{const n=((e,t)=>{const n=qe(e);return n?t[n]:null})(e.impact,e.dimensions.droppables),r=Boolean(n),i=e.dimensions.droppables[e.critical.droppable.id],o=n||i,a=o.axis.direction,s="vertical"===a&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===a&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(s&&!r)return null;const l="MOVE_DOWN"===t||"MOVE_RIGHT"===t,c=e.dimensions.draggables[e.critical.draggable.id],d=e.current.page.borderBoxCenter,{draggables:u,droppables:p}=e.dimensions;return s?We({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,destination:o,draggables:u,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):Ve({isMovingForward:l,previousPageBorderBoxCenter:d,draggable:c,isOver:o,draggables:u,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})};function Xe(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function Je(e){const t=he(e.top,e.bottom),n=he(e.left,e.right);return function(e){return t(e.y)&&n(e.x)}}function Ke({pageBorderBox:e,draggable:t,droppables:n}){const r=oe(n).filter(t=>{if(!t.isEnabled)return!1;const n=t.subject.active;if(!n)return!1;if(i=n,!((r=e).left<i.right&&r.right>i.left&&r.top<i.bottom&&r.bottom>i.top))return!1;var r,i;if(Je(n)(e.center))return!0;const o=t.axis,a=n.center[o.crossAxisLine],s=e[o.crossAxisStart],l=e[o.crossAxisEnd],c=he(n[o.crossAxisStart],n[o.crossAxisEnd]),d=c(s),u=c(l);return!d&&!u||(d?s<a:l>a)});return r.length?1===r.length?r[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:n}){const r=t.page.borderBox.center,i=n.map(t=>{const n=t.axis,i=Y(t.axis.line,e.center[n.line],t.page.borderBox.center[n.crossAxisLine]);return{id:t.descriptor.id,distance:X(r,i)}}).sort((e,t)=>t.distance-e.distance);return i[0]?i[0].id:null}({pageBorderBox:e,draggable:t,candidates:r}):null}const Qe=(e,t)=>f(Q(e,t));function Ze({displaced:e,id:t}){return Boolean(e.visible[t]||e.invisible[t])}var et=({pageOffset:e,draggable:t,draggables:n,droppables:r,previousImpact:i,viewport:o,afterCritical:a})=>{const s=Qe(t.page.borderBox,e),l=Ke({pageBorderBox:s,draggable:t,droppables:r});if(!l)return ge;const c=r[l],d=se(c.descriptor.id,n),u=((e,t)=>{const n=e.frame;return n?Qe(t,n.scroll.diff.value):t})(c,s);return(({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:n,destination:r,insideDestination:i,afterCritical:o})=>{if(!r.isCombineEnabled)return null;const a=r.axis,s=He(r.axis,e.displaceBy),l=s.value,c=t[a.start],d=t[a.end],u=de(e,i).find(e=>{const t=e.descriptor.id,r=e.page.borderBox,i=r[a.size]/4,s=Se(t,o),u=Ze({displaced:n.displaced,id:t});return s?u?d>r[a.start]+i&&d<r[a.end]-i:c>r[a.start]-l+i&&c<r[a.end]-l-i:u?d>r[a.start]+l+i&&d<r[a.end]+l-i:c>r[a.start]+i&&c<r[a.end]-i});return u?{displacedBy:s,displaced:n.displaced,at:{type:"COMBINE",combine:{draggableId:u.descriptor.id,droppableId:r.descriptor.id}}}:null})({pageBorderBoxWithDroppableScroll:u,draggable:t,previousImpact:i,destination:c,insideDestination:d,afterCritical:a})||(({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:n,insideDestination:r,last:i,viewport:o,afterCritical:a})=>{const s=n.axis,l=He(n.axis,t.displaceBy),c=l.value,d=e[s.start],u=e[s.end],p=function({draggable:e,closest:t,inHomeList:n}){return t?n&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:de(t,r).find(e=>{const t=e.descriptor.id,n=e.page.borderBox.center[s.line],r=Se(t,a),o=Ze({displaced:i,id:t});return r?o?u<=n:d<n-c:o?u<=n+c:d<n})||null,inHomeList:ue(t,n)});return Ee({draggable:t,insideDestination:r,destination:n,viewport:o,last:i,displacedBy:l,index:p})})({pageBorderBoxWithDroppableScroll:u,draggable:t,destination:c,insideDestination:d,last:i.displaced,viewport:o,afterCritical:a})},tt=(e,t)=>({...e,[t.descriptor.id]:t});const nt=({previousImpact:e,impact:t,droppables:n})=>{const r=qe(e),i=qe(t);if(!r)return n;if(r===i)return n;const o=n[r];if(!o.subject.withPlaceholder)return n;const a=(e=>{const t=e.subject.withPlaceholder;t||M(!1);const n=e.frame;if(!n){const t=te({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}const r=t.oldFrameMaxScroll;r||M(!1);const i=Ue(n,r),o=te({page:e.subject.page,axis:e.axis,frame:i,withPlaceholder:null});return{...e,subject:o,frame:i}})(o);return tt(n,a)};var rt=({state:e,clientSelection:t,dimensions:n,viewport:r,impact:i,scrollJumpRequest:o})=>{const a=r||e.viewport,s=n||e.dimensions,l=t||e.current.client.selection,c=$(l,e.initial.client.selection),d={offset:c,selection:l,borderBoxCenter:U(e.initial.client.borderBoxCenter,c)},u={selection:U(d.selection,a.scroll.current),borderBoxCenter:U(d.borderBoxCenter,a.scroll.current),offset:U(d.offset,a.scroll.diff.value)},p={client:d,page:u};if("COLLECTING"===e.phase)return{...e,dimensions:s,viewport:a,current:p};const f=s.draggables[e.critical.draggable.id],g=i||et({pageOffset:u.offset,draggable:f,draggables:s.draggables,droppables:s.droppables,previousImpact:e.impact,viewport:a,afterCritical:e.afterCritical}),h=(({draggable:e,draggables:t,droppables:n,previousImpact:r,impact:i})=>{const o=nt({previousImpact:r,impact:i,droppables:n}),a=qe(i);if(!a)return o;const s=n[a];if(ue(e,s))return o;if(s.subject.withPlaceholder)return o;const l=$e(s,e,t);return tt(o,l)})({draggable:f,impact:g,previousImpact:e.impact,draggables:s.draggables,droppables:s.droppables});return{...e,current:p,dimensions:{draggables:s.draggables,droppables:h},impact:g,viewport:a,scrollJumpRequest:o||null,forceShouldAnimate:!o&&null}};var it=({impact:e,viewport:t,draggables:n,destination:r,forceShouldAnimate:i})=>{const o=e.displaced,a=function(e,t){return e.map(e=>t[e])}(o.all,n),s=Ie({afterDragging:a,destination:r,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:i,last:o});return{...e,displaced:s}},ot=({impact:e,draggable:t,droppable:n,draggables:r,viewport:i,afterCritical:o})=>{const a=Le({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:o});return Ge({pageBorderBoxCenter:a,draggable:t,viewport:i})},at=({state:e,dimensions:t,viewport:n})=>{"SNAP"!==e.movementMode&&M(!1);const r=e.impact,i=n||e.viewport,o=t||e.dimensions,{draggables:a,droppables:s}=o,l=a[e.critical.draggable.id],c=qe(r);c||M(!1);const d=s[c],u=it({impact:r,viewport:i,destination:d,draggables:a}),p=ot({impact:u,draggable:l,droppable:d,draggables:a,viewport:i,afterCritical:e.afterCritical});return rt({impact:u,clientSelection:p,state:e,dimensions:o,viewport:i})},st=({draggable:e,home:t,draggables:n,viewport:r})=>{const i=He(t.axis,e.displaceBy),o=se(t.descriptor.id,n),a=o.indexOf(e);-1===a&&M(!1);const s=o.slice(a+1),l=s.reduce((e,t)=>(e[t.descriptor.id]=!0,e),{}),c={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:i,effected:l};var d;return{impact:{displaced:Ie({afterDragging:s,destination:t,displacedBy:i,last:null,viewport:r.frame,forceShouldAnimate:!1}),displacedBy:i,at:{type:"REORDER",destination:(d=e.descriptor,{index:d.index,droppableId:d.droppableId})}},afterCritical:c}};const lt=e=>{0},ct=e=>{0};var dt=({additions:e,updatedDroppables:t,viewport:n})=>{const r=n.scroll.diff.value;return e.map(e=>{const i=e.descriptor.droppableId,o=(e=>{const t=e.frame;return t||M(!1),t})(t[i]),a=o.scroll.diff.value,s=(({draggable:e,offset:t,initialWindowScroll:n})=>{const r=y(e.client,t),i=x(r,n);return{...e,placeholder:{...e.placeholder,client:r},client:r,page:i}})({draggable:e,offset:U(r,a),initialWindowScroll:n.scroll.initial});return s})};const ut=e=>"SNAP"===e.movementMode,pt=(e,t,n)=>{const r=((e,t)=>({draggables:e.draggables,droppables:tt(e.droppables,t)}))(e.dimensions,t);return!ut(e)||n?rt({state:e,dimensions:r}):at({state:e,dimensions:r})};function ft(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}const gt={phase:"IDLE",completed:null,shouldFlush:!1};var ht=(e=gt,t)=>{if("FLUSH"===t.type)return{...gt,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&M(!1);const{critical:n,clientSelection:r,viewport:i,dimensions:o,movementMode:a}=t.payload,s=o.draggables[n.draggable.id],l=o.droppables[n.droppable.id],c={selection:r,borderBoxCenter:s.client.borderBox.center,offset:H},d={client:c,page:{selection:U(c.selection,i.scroll.initial),borderBoxCenter:U(c.selection,i.scroll.initial),offset:U(c.selection,i.scroll.diff.value)}},u=oe(o.droppables).every(e=>!e.isFixedOnPage),{impact:p,afterCritical:f}=st({draggable:s,home:l,draggables:o.draggables,viewport:i});return{phase:"DRAGGING",isDragging:!0,critical:n,movementMode:a,dimensions:o,initial:d,current:d,isWindowScrollAllowed:u,impact:p,afterCritical:f,onLiftImpact:p,viewport:i,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&M(!1);return{...e,phase:"COLLECTING"}}if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&M(!1),(({state:e,published:t})=>{lt();const n=t.modified.map(t=>{const n=e.dimensions.droppables[t.droppableId];return ne(n,t.scroll)}),r={...e.dimensions.droppables,...re(n)},i=ie(dt({additions:t.additions,updatedDroppables:r,viewport:e.viewport})),o={...e.dimensions.draggables,...i};t.removals.forEach(e=>{delete o[e]});const a={droppables:r,draggables:o},s=qe(e.impact),l=s?a.droppables[s]:null,c=a.draggables[e.critical.draggable.id],d=a.droppables[e.critical.droppable.id],{impact:u,afterCritical:p}=st({draggable:c,home:d,draggables:o,viewport:e.viewport}),f=l&&l.isCombineEnabled?e.impact:u,g=et({pageOffset:e.current.page.offset,draggable:a.draggables[e.critical.draggable.id],draggables:a.draggables,droppables:a.droppables,previousImpact:f,viewport:e.viewport,afterCritical:p});ct();const h={...e,phase:"DRAGGING",impact:g,onLiftImpact:u,dimensions:a,afterCritical:p,forceShouldAnimate:!1};return"COLLECTING"===e.phase?h:{...h,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}})({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;Xe(e)||M(!1);const{client:n}=t.payload;return V(n,e.current.client.selection)?e:rt({state:e,clientSelection:n,impact:ut(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase)return ft(e);if("COLLECTING"===e.phase)return ft(e);Xe(e)||M(!1);const{id:n,newScroll:r}=t.payload,i=e.dimensions.droppables[n];if(!i)return e;const o=ne(i,r);return pt(e,o,!1)}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;Xe(e)||M(!1);const{id:n,isEnabled:r}=t.payload,i=e.dimensions.droppables[n];i||M(!1),i.isEnabled===r&&M(!1);const o={...i,isEnabled:r};return pt(e,o,!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;Xe(e)||M(!1);const{id:n,isCombineEnabled:r}=t.payload,i=e.dimensions.droppables[n];i||M(!1),i.isCombineEnabled===r&&M(!1);const o={...i,isCombineEnabled:r};return pt(e,o,!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;Xe(e)||M(!1),e.isWindowScrollAllowed||M(!1);const n=t.payload.newScroll;if(V(e.viewport.scroll.current,n))return ft(e);const r=Me(e.viewport,n);return ut(e)?at({state:e,viewport:r}):rt({state:e,viewport:r})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!Xe(e))return e;const n=t.payload.maxScroll;if(V(n,e.viewport.scroll.max))return e;const r={...e.viewport,scroll:{...e.viewport.scroll,max:n}};return{...e,viewport:r}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&M(!1);const n=Ye({state:e,type:t.type});return n?rt({state:e,impact:n.impact,clientSelection:n.clientSelection,scrollJumpRequest:n.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){const n=t.payload.reason;"COLLECTING"!==e.phase&&M(!1);return{...e,phase:"DROP_PENDING",isWaiting:!0,reason:n}}if("DROP_ANIMATE"===t.type){const{completed:n,dropDuration:r,newHomeClientOffset:i}=t.payload;"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&M(!1);return{phase:"DROP_ANIMATING",completed:n,dropDuration:r,newHomeClientOffset:i,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){const{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};const mt=e=>({type:"LIFT",payload:e}),bt=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),vt=()=>({type:"COLLECTION_STARTING",payload:null}),yt=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),xt=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),wt=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),It=e=>({type:"MOVE",payload:e}),Dt=()=>({type:"MOVE_UP",payload:null}),Et=()=>({type:"MOVE_DOWN",payload:null}),St=()=>({type:"MOVE_RIGHT",payload:null}),At=()=>({type:"MOVE_LEFT",payload:null}),Rt=()=>({type:"FLUSH",payload:null}),Ct=e=>({type:"DROP_COMPLETE",payload:e}),Ot=e=>({type:"DROP",payload:e}),Pt=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});const Nt="cubic-bezier(.2,1,.1,1)",Bt={drop:0,combining:.7},Tt={drop:.75},Lt={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},Mt=`${Lt.outOfTheWay}s ${"cubic-bezier(0.2, 0, 0, 1)"}`,_t={fluid:`opacity ${Mt}`,snap:`transform ${Mt}, opacity ${Mt}`,drop:e=>{const t=`${e}s ${Nt}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${Mt}`,placeholder:`height ${Mt}, width ${Mt}, margin ${Mt}`},Gt=e=>V(e,H)?void 0:`translate(${e.x}px, ${e.y}px)`,zt=Gt,Wt=(e,t)=>{const n=Gt(e);if(n)return t?`${n} scale(${Tt.drop})`:n},{minDropTime:kt,maxDropTime:Ft}=Lt,jt=Ft-kt;var Ht=({getState:e,dispatch:t})=>n=>r=>{if("DROP"!==r.type)return void n(r);const i=e(),o=r.payload.reason;if("COLLECTING"===i.phase)return void t((e=>({type:"DROP_PENDING",payload:e}))({reason:o}));if("IDLE"===i.phase)return;"DROP_PENDING"===i.phase&&i.isWaiting&&M(!1),"DRAGGING"!==i.phase&&"DROP_PENDING"!==i.phase&&M(!1);const a=i.critical,s=i.dimensions,l=s.draggables[i.critical.draggable.id],{impact:c,didDropInsideDroppable:d}=(({draggables:e,reason:t,lastImpact:n,home:r,viewport:i,onLiftImpact:o})=>{if(!n.at||"DROP"!==t)return{impact:it({draggables:e,impact:o,destination:r,viewport:i,forceShouldAnimate:!0}),didDropInsideDroppable:!1};return"REORDER"===n.at.type?{impact:n,didDropInsideDroppable:!0}:{impact:{...n,displaced:fe},didDropInsideDroppable:!0}})({reason:o,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),u=d?le(c):null,p=d?ce(c):null,f={index:a.draggable.index,droppableId:a.droppable.id},g={draggableId:l.descriptor.id,type:l.descriptor.type,source:f,reason:o,mode:i.movementMode,destination:u,combine:p},h=(({impact:e,draggable:t,dimensions:n,viewport:r,afterCritical:i})=>{const{draggables:o,droppables:a}=n,s=qe(e),l=s?a[s]:null,c=a[t.descriptor.droppableId],d=ot({impact:e,draggable:t,draggables:o,afterCritical:i,droppable:l||c,viewport:r});return $(d,t.client.borderBox.center)})({impact:c,draggable:l,dimensions:s,viewport:i.viewport,afterCritical:i.afterCritical}),m={critical:i.critical,afterCritical:i.afterCritical,result:g,impact:c};if(!(!V(i.current.client.offset,h)||Boolean(g.combine)))return void t(Ct({completed:m}));const b=(({current:e,destination:t,reason:n})=>{const r=X(e,t);if(r<=0)return kt;if(r>=1500)return Ft;const i=kt+jt*(r/1500);return Number(("CANCEL"===n?.6*i:i).toFixed(2))})({current:i.current.client.offset,destination:h,reason:o});t((e=>({type:"DROP_ANIMATE",payload:e}))({newHomeClientOffset:h,dropDuration:b,completed:m}))},Ut=()=>({x:window.pageXOffset,y:window.pageYOffset});function $t({onWindowScroll:e}){const t=R(function(){e(Ut())}),n=function(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:t=>{t.target!==window&&t.target!==window.document||e()}}}(t);let r=P;function i(){return r!==P}return{start:function(){i()&&M(!1),r=N(window,[n])},stop:function(){i()||M(!1),t.cancel(),r(),r=P},isActive:i}}var Vt=e=>{const t=$t({onWindowScroll:t=>{e.dispatch({type:"MOVE_BY_WINDOW_SCROLL",payload:{newScroll:t}})}});return e=>n=>{t.isActive()||"INITIAL_PUBLISH"!==n.type||t.start(),t.isActive()&&(e=>"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type)(n)&&t.stop(),e(n)}},qt=()=>{const e=[];return{add:t=>{const n=setTimeout(()=>(t=>{const n=e.findIndex(e=>e.timerId===t);-1===n&&M(!1);const[r]=e.splice(n,1);r.callback()})(n)),r={timerId:n,callback:t};e.push(r)},flush:()=>{if(!e.length)return;const t=[...e];e.length=0,t.forEach(e=>{clearTimeout(e.timerId),e.callback()})}}};const Yt=(e,t)=>{lt(),t(),ct()},Xt=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function Jt(e,t,n,r){if(!e)return void n(r(t));const i=(e=>{let t=!1,n=!1;const r=setTimeout(()=>{n=!0}),i=i=>{t||n||(t=!0,e(i),clearTimeout(r))};return i.wasCalled=()=>t,i})(n);e(t,{announce:i}),i.wasCalled()||n(r(t))}var Kt=(e,t)=>{const n=((e,t)=>{const n=qt();let r=null;const i=n=>{r||M(!1),r=null,Yt(0,()=>Jt(e().onDragEnd,n,t,j.onDragEnd))};return{beforeCapture:(t,n)=>{r&&M(!1),Yt(0,()=>{const r=e().onBeforeCapture;r&&r({draggableId:t,mode:n})})},beforeStart:(t,n)=>{r&&M(!1),Yt(0,()=>{const r=e().onBeforeDragStart;r&&r(Xt(t,n))})},start:(i,o)=>{r&&M(!1);const a=Xt(i,o);r={mode:o,lastCritical:i,lastLocation:a.source,lastCombine:null},n.add(()=>{Yt(0,()=>Jt(e().onDragStart,a,t,j.onDragStart))})},update:(i,o)=>{const a=le(o),s=ce(o);r||M(!1);const l=!((e,t)=>{if(e===t)return!0;const n=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,r=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return n&&r})(i,r.lastCritical);l&&(r.lastCritical=i);const c=(u=a,!(null==(d=r.lastLocation)&&null==u||null!=d&&null!=u&&d.droppableId===u.droppableId&&d.index===u.index));var d,u;c&&(r.lastLocation=a);const p=!((e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId)(r.lastCombine,s);if(p&&(r.lastCombine=s),!l&&!c&&!p)return;const f={...Xt(i,r.mode),combine:s,destination:a};n.add(()=>{Yt(0,()=>Jt(e().onDragUpdate,f,t,j.onDragUpdate))})},flush:()=>{r||M(!1),n.flush()},drop:i,abort:()=>{if(!r)return;const e={...Xt(r.lastCritical,r.mode),combine:null,destination:null,reason:"CANCEL"};i(e)}}})(e,t);return e=>t=>r=>{if("BEFORE_INITIAL_CAPTURE"===r.type)return void n.beforeCapture(r.payload.draggableId,r.payload.movementMode);if("INITIAL_PUBLISH"===r.type){const e=r.payload.critical;return n.beforeStart(e,r.payload.movementMode),t(r),void n.start(e,r.payload.movementMode)}if("DROP_COMPLETE"===r.type){const e=r.payload.completed.result;return n.flush(),t(r),void n.drop(e)}if(t(r),"FLUSH"===r.type)return void n.abort();const i=e.getState();"DRAGGING"===i.phase&&n.update(i.critical,i.impact)}};var Qt=e=>t=>n=>{if("DROP_ANIMATION_FINISHED"!==n.type)return void t(n);const r=e.getState();"DROP_ANIMATING"!==r.phase&&M(!1),e.dispatch(Ct({completed:r.completed}))};var Zt=e=>{let t=null,n=null;return r=>i=>{if("FLUSH"!==i.type&&"DROP_COMPLETE"!==i.type&&"DROP_ANIMATION_FINISHED"!==i.type||(n&&(cancelAnimationFrame(n),n=null),t&&(t(),t=null)),r(i),"DROP_ANIMATE"!==i.type)return;const o={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch({type:"DROP_ANIMATION_FINISHED",payload:null})}};n=requestAnimationFrame(()=>{n=null,t=N(window,[o])})}};var en=e=>t=>n=>{if(t(n),"PUBLISH_WHILE_DRAGGING"!==n.type)return;const r=e.getState();"DROP_PENDING"===r.phase&&(r.isWaiting||e.dispatch(Ot({reason:r.reason})))};const tn=s.compose;var nn=({dimensionMarshal:e,focusMarshal:t,styleMarshal:n,getResponders:r,announce:i,autoScroller:o})=>{return(0,s.createStore)(ht,tn((0,s.applyMiddleware)((a=n,()=>e=>t=>{"INITIAL_PUBLISH"===t.type&&a.dragging(),"DROP_ANIMATE"===t.type&&a.dropping(t.payload.completed.result.reason),"FLUSH"!==t.type&&"DROP_COMPLETE"!==t.type||a.resting(),e(t)}),(e=>()=>t=>n=>{"DROP_COMPLETE"!==n.type&&"FLUSH"!==n.type&&"DROP_ANIMATE"!==n.type||e.stopPublishing(),t(n)})(e),(e=>({getState:t,dispatch:n})=>r=>i=>{if("LIFT"!==i.type)return void r(i);const{id:o,clientSelection:a,movementMode:s}=i.payload,l=t();"DROP_ANIMATING"===l.phase&&n(Ct({completed:l.completed})),"IDLE"!==t().phase&&M(!1),n(Rt()),n({type:"BEFORE_INITIAL_CAPTURE",payload:{draggableId:o,movementMode:s}});const c={draggableId:o,scrollOptions:{shouldPublishImmediately:"SNAP"===s}},{critical:d,dimensions:u,viewport:p}=e.startPublishing(c);n({type:"INITIAL_PUBLISH",payload:{critical:d,dimensions:u,clientSelection:a,movementMode:s,viewport:p}})})(e),Ht,Qt,Zt,en,(e=>t=>n=>r=>{if((e=>"DROP_COMPLETE"===e.type||"DROP_ANIMATE"===e.type||"FLUSH"===e.type)(r))return e.stop(),void n(r);if("INITIAL_PUBLISH"===r.type){n(r);const i=t.getState();return"DRAGGING"!==i.phase&&M(!1),void e.start(i)}n(r),e.scroll(t.getState())})(o),Vt,(e=>{let t=!1;return()=>n=>r=>{if("INITIAL_PUBLISH"===r.type)return t=!0,e.tryRecordFocus(r.payload.critical.draggable.id),n(r),void e.tryRestoreFocusRecorded();if(n(r),t){if("FLUSH"===r.type)return t=!1,void e.tryRestoreFocusRecorded();if("DROP_COMPLETE"===r.type){t=!1;const n=r.payload.completed.result;n.combine&&e.tryShiftRecord(n.draggableId,n.combine.draggableId),e.tryRestoreFocusRecorded()}}}})(t),Kt(r,i))));var a};var rn=({scrollHeight:e,scrollWidth:t,height:n,width:r})=>{const i=$({x:t,y:e},{x:r,y:n});return{x:Math.max(0,i.x),y:Math.max(0,i.y)}},on=()=>{const e=document.documentElement;return e||M(!1),e},an=()=>{const e=on();return rn({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},sn=({critical:e,scrollOptions:t,registry:n})=>{lt();const r=(()=>{const e=Ut(),t=an(),n=e.y,r=e.x,i=on(),o=i.clientWidth,a=i.clientHeight;return{frame:f({top:n,left:r,right:r+o,bottom:n+a}),scroll:{initial:e,current:e,max:t,diff:{value:H,displacement:H}}}})(),i=r.scroll.current,o=e.droppable,a=n.droppable.getAllByType(o.type).map(e=>e.callbacks.getDimensionAndWatchScroll(i,t)),s=n.draggable.getAllByType(e.draggable.type).map(e=>e.getDimension(i)),l={draggables:ie(s),droppables:re(a)};ct();return{dimensions:l,critical:e,viewport:r}};function ln(e,t,n){if(n.descriptor.id===t.id)return!1;if(n.descriptor.type!==t.type)return!1;return"virtual"===e.droppable.getById(n.descriptor.droppableId).descriptor.mode}var cn=(e,t)=>{let n=null;const r=function({registry:e,callbacks:t}){let n={additions:{},removals:{},modified:{}},r=null;const i=()=>{r||(t.collectionStarting(),r=requestAnimationFrame(()=>{r=null,lt();const{additions:i,removals:o,modified:a}=n,s=Object.keys(i).map(t=>e.draggable.getById(t).getDimension(H)).sort((e,t)=>e.descriptor.index-t.descriptor.index),l=Object.keys(a).map(t=>({droppableId:t,scroll:e.droppable.getById(t).callbacks.getScrollWhileDragging()})),c={additions:s,removals:Object.keys(o),modified:l};n={additions:{},removals:{},modified:{}},ct(),t.publish(c)}))};return{add:e=>{const t=e.descriptor.id;n.additions[t]=e,n.modified[e.descriptor.droppableId]=!0,n.removals[t]&&delete n.removals[t],i()},remove:e=>{const t=e.descriptor;n.removals[t.id]=!0,n.modified[t.droppableId]=!0,n.additions[t.id]&&delete n.additions[t.id],i()},stop:()=>{r&&(cancelAnimationFrame(r),r=null,n={additions:{},removals:{},modified:{}})}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),i=t=>{n||M(!1);const i=n.critical.draggable;"ADDITION"===t.type&&ln(e,i,t.value)&&r.add(t.value),"REMOVAL"===t.type&&ln(e,i,t.value)&&r.remove(t.value)},o={updateDroppableIsEnabled:(r,i)=>{e.droppable.exists(r)||M(!1),n&&t.updateDroppableIsEnabled({id:r,isEnabled:i})},updateDroppableIsCombineEnabled:(r,i)=>{n&&(e.droppable.exists(r)||M(!1),t.updateDroppableIsCombineEnabled({id:r,isCombineEnabled:i}))},scrollDroppable:(t,r)=>{n&&e.droppable.getById(t).callbacks.scroll(r)},updateDroppableScroll:(r,i)=>{n&&(e.droppable.exists(r)||M(!1),t.updateDroppableScroll({id:r,newScroll:i}))},startPublishing:t=>{n&&M(!1);const r=e.draggable.getById(t.draggableId),o=e.droppable.getById(r.descriptor.droppableId),a={draggable:r.descriptor,droppable:o.descriptor},s=e.subscribe(i);return n={critical:a,unsubscribe:s},sn({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!n)return;r.stop();const t=n.critical.droppable;e.droppable.getAllByType(t.type).forEach(e=>e.callbacks.dragStopped()),n.unsubscribe(),n=null}};return o},dn=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&(e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason),un=e=>{window.scrollBy(e.x,e.y)};const pn=A(e=>oe(e).filter(e=>!!e.isEnabled&&!!e.frame));var fn=({center:e,destination:t,droppables:n})=>{if(t){const e=n[t];return e.frame?e:null}const r=((e,t)=>{const n=pn(t).find(t=>(t.frame||M(!1),Je(t.frame.pageMarginBox)(e)))||null;return n})(e,n);return r};const gn={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var hn=({startOfRange:e,endOfRange:t,current:n})=>{const r=t-e;if(0===r)return 0;return(n-e)/r},mn=({distanceToEdge:e,thresholds:t,dragStartTime:n,shouldUseTimeDampening:r,getAutoScrollerOptions:i})=>{const o=((e,t,n=()=>gn)=>{const r=n();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return r.maxPixelScroll;if(e===t.startScrollingFrom)return 1;const i=1-hn({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),o=r.maxPixelScroll*r.ease(i);return Math.ceil(o)})(e,t,i);return 0===o?0:r?Math.max(((e,t,n)=>{const r=n(),i=r.durationDampening.accelerateAt,o=r.durationDampening.stopDampeningAt,a=t,s=o,l=Date.now()-a;if(l>=o)return e;if(l<i)return 1;const c=hn({startOfRange:i,endOfRange:s,current:l}),d=e*r.ease(c);return Math.ceil(d)})(o,n,i),1):o},bn=({container:e,distanceToEdges:t,dragStartTime:n,axis:r,shouldUseTimeDampening:i,getAutoScrollerOptions:o})=>{const a=((e,t,n=()=>gn)=>{const r=n();return{startScrollingFrom:e[t.size]*r.startFromPercentage,maxScrollValueAt:e[t.size]*r.maxScrollAtPercentage}})(e,r,o);return t[r.end]<t[r.start]?mn({distanceToEdge:t[r.end],thresholds:a,dragStartTime:n,shouldUseTimeDampening:i,getAutoScrollerOptions:o}):-1*mn({distanceToEdge:t[r.start],thresholds:a,dragStartTime:n,shouldUseTimeDampening:i,getAutoScrollerOptions:o})};const vn=K(e=>0===e?0:e);var yn=({dragStartTime:e,container:t,subject:n,center:r,shouldUseTimeDampening:i,getAutoScrollerOptions:o})=>{const a={top:r.y-t.top,right:t.right-r.x,bottom:t.bottom-r.y,left:r.x-t.left},s=bn({container:t,distanceToEdges:a,dragStartTime:e,axis:ve,shouldUseTimeDampening:i,getAutoScrollerOptions:o}),l=bn({container:t,distanceToEdges:a,dragStartTime:e,axis:ye,shouldUseTimeDampening:i,getAutoScrollerOptions:o}),c=vn({x:l,y:s});if(V(c,H))return null;const d=(({container:e,subject:t,proposedScroll:n})=>{const r=t.height>e.height,i=t.width>e.width;return i||r?i&&r?null:{x:i?0:n.x,y:r?0:n.y}:n})({container:t,subject:n,proposedScroll:c});return d?V(d,H)?null:d:null};const xn=K(e=>0===e?0:e>0?1:-1),wn=(()=>{const e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:n,change:r})=>{const i=U(t,r),o={x:e(i.x,n.x),y:e(i.y,n.y)};return V(o,H)?null:o}})(),In=({max:e,current:t,change:n})=>{const r={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},i=xn(n),o=wn({max:r,current:t,change:i});return!o||(0!==i.x&&0===o.x||0!==i.y&&0===o.y)},Dn=(e,t)=>In({current:e.scroll.current,max:e.scroll.max,change:t}),En=(e,t)=>{const n=e.frame;return!!n&&In({current:n.scroll.current,max:n.scroll.max,change:t})};var Sn=({state:e,dragStartTime:t,shouldUseTimeDampening:n,scrollWindow:r,scrollDroppable:i,getAutoScrollerOptions:o})=>{const a=e.current.page.borderBoxCenter,s=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){const i=(({viewport:e,subject:t,center:n,dragStartTime:r,shouldUseTimeDampening:i,getAutoScrollerOptions:o})=>{const a=yn({dragStartTime:r,container:e.frame,subject:t,center:n,shouldUseTimeDampening:i,getAutoScrollerOptions:o});return a&&Dn(e,a)?a:null})({dragStartTime:t,viewport:e.viewport,subject:s,center:a,shouldUseTimeDampening:n,getAutoScrollerOptions:o});if(i)return void r(i)}const l=fn({center:a,destination:qe(e.impact),droppables:e.dimensions.droppables});if(!l)return;const c=(({droppable:e,subject:t,center:n,dragStartTime:r,shouldUseTimeDampening:i,getAutoScrollerOptions:o})=>{const a=e.frame;if(!a)return null;const s=yn({dragStartTime:r,container:a.pageMarginBox,subject:t,center:n,shouldUseTimeDampening:i,getAutoScrollerOptions:o});return s&&En(e,s)?s:null})({dragStartTime:t,droppable:l,subject:s,center:a,shouldUseTimeDampening:n,getAutoScrollerOptions:o});c&&i(l.descriptor.id,c)},An=({move:e,scrollDroppable:t,scrollWindow:n})=>{const r=(e,n)=>{if(!En(e,n))return n;const r=((e,t)=>{const n=e.frame;return n&&En(e,t)?wn({current:n.scroll.current,max:n.scroll.max,change:t}):null})(e,n);if(!r)return t(e.descriptor.id,n),null;const i=$(n,r);t(e.descriptor.id,i);return $(n,i)},i=(e,t,r)=>{if(!e)return r;if(!Dn(t,r))return r;const i=((e,t)=>{if(!Dn(e,t))return null;const n=e.scroll.max,r=e.scroll.current;return wn({current:r,max:n,change:t})})(t,r);if(!i)return n(r),null;const o=$(r,i);n(o);return $(r,o)};return t=>{const n=t.scrollJumpRequest;if(!n)return;const o=qe(t.impact);o||M(!1);const a=r(t.dimensions.droppables[o],n);if(!a)return;const s=t.viewport,l=i(t.isWindowScrollAllowed,s,a);l&&((t,n)=>{const r=U(t.current.client.selection,n);e({client:r})})(t,l)}},Rn=({scrollDroppable:e,scrollWindow:t,move:n,getAutoScrollerOptions:r})=>{const i=(({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:n=()=>gn})=>{const r=R(e),i=R(t);let o=null;const a=e=>{o||M(!1);const{shouldUseTimeDampening:t,dragStartTime:a}=o;Sn({state:e,scrollWindow:r,scrollDroppable:i,dragStartTime:a,shouldUseTimeDampening:t,getAutoScrollerOptions:n})};return{start:e=>{lt(),o&&M(!1);const t=Date.now();let r=!1;const i=()=>{r=!0};Sn({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:i,scrollDroppable:i,getAutoScrollerOptions:n}),o={dragStartTime:t,shouldUseTimeDampening:r},ct(),r&&a(e)},stop:()=>{o&&(r.cancel(),i.cancel(),o=null)},scroll:a}})({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:r}),o=An({move:n,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{r().disabled||"DRAGGING"!==e.phase||("FLUID"!==e.movementMode?e.scrollJumpRequest&&o(e):i.scroll(e))},start:i.start,stop:i.stop}};const Cn="data-rfd",On=(()=>{const e=`${Cn}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),Pn=(()=>{const e=`${Cn}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),Nn=(()=>{const e=`${Cn}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),Bn={contextId:`${Cn}-scroll-container-context-id`},Tn=(e,t)=>e.map(e=>{const n=e.styles[t];return n?`${e.selector} { ${n} }`:""}).join(" ");var Ln=e=>{const t=(n=e,e=>`[${e}="${n}"]`);var n;const r=(()=>{const e="\n      cursor: -webkit-grab;\n      cursor: grab;\n    ";return{selector:t(On.contextId),styles:{always:"\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        ",resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),i=[(()=>{const e=`\n      transition: ${_t.outOfTheWay};\n    `;return{selector:t(Pn.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),r,{selector:t(Nn.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:"\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      "}}];return{always:Tn(i,"always"),resting:Tn(i,"resting"),dragging:Tn(i,"dragging"),dropAnimating:Tn(i,"dropAnimating"),userCancel:Tn(i,"userCancel")}};var Mn="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?r.useLayoutEffect:r.useEffect;const _n=()=>{const e=document.querySelector("head");return e||M(!1),e},Gn=e=>{const t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function zn(e,t){return Array.from(e.querySelectorAll(t))}var Wn=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function kn(e){return e instanceof Wn(e).HTMLElement}function Fn(e,t){const n=`[${On.contextId}="${e}"]`,r=zn(document,n);if(!r.length)return null;const i=r.find(e=>e.getAttribute(On.draggableId)===t);return i&&kn(i)?i:null}function jn(){const e={draggables:{},droppables:{}},t=[];function n(e){t.length&&t.forEach(t=>t(e))}function r(t){return e.draggables[t]||null}function i(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,n({type:"ADDITION",value:t})},update:(t,n)=>{const r=e.draggables[n.descriptor.id];r&&r.uniqueId===t.uniqueId&&(delete e.draggables[n.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{const i=t.descriptor.id,o=r(i);o&&t.uniqueId===o.uniqueId&&(delete e.draggables[i],e.droppables[t.descriptor.droppableId]&&n({type:"REMOVAL",value:t}))},getById:function(e){const t=r(e);return t||M(!1),t},findById:r,exists:e=>Boolean(r(e)),getAllByType:t=>Object.values(e.draggables).filter(e=>e.descriptor.type===t)},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{const n=i(t.descriptor.id);n&&t.uniqueId===n.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){const t=i(e);return t||M(!1),t},findById:i,exists:e=>Boolean(i(e)),getAllByType:t=>Object.values(e.droppables).filter(e=>e.descriptor.type===t)},subscribe:function(e){return t.push(e),function(){const n=t.indexOf(e);-1!==n&&t.splice(n,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var Hn=i().createContext(null),Un=()=>{const e=document.body;return e||M(!1),e};var $n={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"};const Vn=e=>`rfd-announcement-${e}`;let qn=0;const Yn={separator:"::"};var Xn="useId"in i()?function(e,t=Yn){const n=i().useId();return d(()=>`${e}${t.separator}${n}`,[t.separator,e,n])}:function(e,t=Yn){return d(()=>`${e}${t.separator}${qn++}`,[t.separator,e])};var Jn=i().createContext(null);function Kn(e){0}function Qn(e,t){Kn()}function Zn(e){const t=(0,r.useRef)(e);return(0,r.useEffect)(()=>{t.current=e}),t}function er(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}const tr=9,nr=13,rr=33,ir=34,or=35,ar=36,sr={[nr]:!0,[tr]:!0};var lr=e=>{sr[e.keyCode]&&e.preventDefault()};var cr=(()=>{const e="visibilitychange";if("undefined"==typeof document)return e;return[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find(e=>`on${e}`in document)||e})();const dr={type:"IDLE"};function ur({cancel:e,completed:t,getPhase:n,setPhase:r}){return[{eventName:"mousemove",fn:e=>{const{button:t,clientX:i,clientY:o}=e;if(0!==t)return;const a={x:i,y:o},s=n();if("DRAGGING"===s.type)return e.preventDefault(),void s.actions.move(a);"PENDING"!==s.type&&M(!1);const l=s.point;if(c=l,d=a,!(Math.abs(d.x-c.x)>=5||Math.abs(d.y-c.y)>=5))return;var c,d;e.preventDefault();const u=s.actions.fluidLift(a);r({type:"DRAGGING",actions:u})}},{eventName:"mouseup",fn:r=>{const i=n();"DRAGGING"===i.type?(r.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===n().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"!==n().type)return 27===t.keyCode?(t.preventDefault(),void e()):void lr(t);e()}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===n().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{const r=n();"IDLE"===r.type&&M(!1),r.actions.shouldRespectForcePress()?e():t.preventDefault()}},{eventName:cr,fn:e}]}function pr(){}const fr={[ir]:!0,[rr]:!0,[ar]:!0,[or]:!0};function gr(e,t){function n(){t(),e.cancel()}return[{eventName:"keydown",fn:r=>27===r.keyCode?(r.preventDefault(),void n()):32===r.keyCode?(r.preventDefault(),t(),void e.drop()):40===r.keyCode?(r.preventDefault(),void e.moveDown()):38===r.keyCode?(r.preventDefault(),void e.moveUp()):39===r.keyCode?(r.preventDefault(),void e.moveRight()):37===r.keyCode?(r.preventDefault(),void e.moveLeft()):void(fr[r.keyCode]?r.preventDefault():lr(r))},{eventName:"mousedown",fn:n},{eventName:"mouseup",fn:n},{eventName:"click",fn:n},{eventName:"touchstart",fn:n},{eventName:"resize",fn:n},{eventName:"wheel",fn:n,options:{passive:!0}},{eventName:cr,fn:n}]}const hr={type:"IDLE"};const mr=["input","button","textarea","select","option","optgroup","video","audio"];function br(e,t){if(null==t)return!1;if(mr.includes(t.tagName.toLowerCase()))return!0;const n=t.getAttribute("contenteditable");return"true"===n||""===n||t!==e&&br(e,t.parentElement)}function vr(e,t){const n=t.target;return!!kn(n)&&br(e,n)}var yr=e=>f(e.getBoundingClientRect()).center;const xr=(()=>{const e="matches";if("undefined"==typeof document)return e;return[e,"msMatchesSelector","webkitMatchesSelector"].find(e=>e in Element.prototype)||e})();function wr(e,t){return null==e?null:e[xr](t)?e:wr(e.parentElement,t)}function Ir(e,t){return e.closest?e.closest(t):wr(e,t)}function Dr(e,t){const n=t.target;if(!((r=n)instanceof Wn(r).Element))return null;var r;const i=function(e){return`[${On.contextId}="${e}"]`}(e),o=Ir(n,i);return o&&kn(o)?o:null}function Er(e){e.preventDefault()}function Sr({expected:e,phase:t,isLockActive:n,shouldWarn:r}){return!!n()&&e===t}function Ar({lockAPI:e,store:t,registry:n,draggableId:r}){if(e.isClaimed())return!1;const i=n.draggable.findById(r);return!!i&&(!!i.options.isEnabled&&!!dn(t.getState(),r))}function Rr({lockAPI:e,contextId:t,store:n,registry:r,draggableId:i,forceSensorStop:o,sourceEvent:a}){if(!Ar({lockAPI:e,store:n,registry:r,draggableId:i}))return null;const s=r.draggable.getById(i),l=function(e,t){const n=`[${Pn.contextId}="${e}"]`,r=zn(document,n).find(e=>e.getAttribute(Pn.id)===t);return r&&kn(r)?r:null}(t,s.descriptor.id);if(!l)return null;if(a&&!s.options.canDragInteractiveElements&&vr(l,a))return null;const c=e.claim(o||P);let d="PRE_DRAG";function u(){return s.options.shouldRespectForcePress}function p(){return e.isActive(c)}const f=function(e,t){Sr({expected:e,phase:d,isLockActive:p,shouldWarn:!0})&&n.dispatch(t())}.bind(null,"DRAGGING");function g(t){function r(){e.release(),d="COMPLETED"}function i(e,i={shouldBlockNextClick:!1}){if(t.cleanup(),i.shouldBlockNextClick){const e=N(window,[{eventName:"click",fn:Er,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(e)}r(),n.dispatch(Ot({reason:e}))}return"PRE_DRAG"!==d&&(r(),M(!1)),n.dispatch(mt(t.liftActionArgs)),d="DRAGGING",{isActive:()=>Sr({expected:"DRAGGING",phase:d,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:u,drop:e=>i("DROP",e),cancel:e=>i("CANCEL",e),...t.actions}}return{isActive:()=>Sr({expected:"PRE_DRAG",phase:d,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:u,fluidLift:function(e){const t=R(e=>{f(()=>It({client:e}))});return{...g({liftActionArgs:{id:i,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){const e={moveUp:()=>f(Dt),moveRight:()=>f(St),moveDown:()=>f(Et),moveLeft:()=>f(At)};return g({liftActionArgs:{id:i,clientSelection:yr(l),movementMode:"SNAP"},cleanup:P,actions:e})},abort:function(){Sr({expected:"PRE_DRAG",phase:d,isLockActive:p,shouldWarn:!0})&&e.release()}}}const Cr=[function(e){const t=(0,r.useRef)(dr),n=(0,r.useRef)(P),i=d(()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented)return;if(0!==t.button)return;if(t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;const r=e.findClosestDraggableId(t);if(!r)return;const i=e.tryGetLock(r,s,{sourceEvent:t});if(!i)return;t.preventDefault();const o={x:t.clientX,y:t.clientY};n.current(),p(i,o)}}),[e]),o=d(()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;const n=e.findClosestDraggableId(t);if(!n)return;const r=e.findOptionsForDraggable(n);r&&(r.shouldRespectForcePress||e.canGetLock(n)&&t.preventDefault())}}),[e]),a=u(function(){n.current=N(window,[o,i],{passive:!1,capture:!0})},[o,i]),s=u(()=>{"IDLE"!==t.current.type&&(t.current=dr,n.current(),a())},[a]),l=u(()=>{const e=t.current;s(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[s]),c=u(function(){const e=ur({cancel:l,completed:s,getPhase:()=>t.current,setPhase:e=>{t.current=e}});n.current=N(window,e,{capture:!0,passive:!1})},[l,s]),p=u(function(e,n){"IDLE"!==t.current.type&&M(!1),t.current={type:"PENDING",point:n,actions:e},c()},[c]);Mn(function(){return a(),function(){n.current()}},[a])},function(e){const t=(0,r.useRef)(pr),n=d(()=>({eventName:"keydown",fn:function(n){if(n.defaultPrevented)return;if(32!==n.keyCode)return;const r=e.findClosestDraggableId(n);if(!r)return;const o=e.tryGetLock(r,l,{sourceEvent:n});if(!o)return;n.preventDefault();let a=!0;const s=o.snapLift();function l(){a||M(!1),a=!1,t.current(),i()}t.current(),t.current=N(window,gr(s,l),{capture:!0,passive:!1})}}),[e]),i=u(function(){t.current=N(window,[n],{passive:!1,capture:!0})},[n]);Mn(function(){return i(),function(){t.current()}},[i])},function(e){const t=(0,r.useRef)(hr),n=(0,r.useRef)(P),i=u(function(){return t.current},[]),o=u(function(e){t.current=e},[]),a=d(()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;const r=e.findClosestDraggableId(t);if(!r)return;const i=e.tryGetLock(r,l,{sourceEvent:t});if(!i)return;const o=t.touches[0],{clientX:a,clientY:s}=o,c={x:a,y:s};n.current(),g(i,c)}}),[e]),s=u(function(){n.current=N(window,[a],{capture:!0,passive:!1})},[a]),l=u(()=>{const e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),o(hr),n.current(),s())},[s,o]),c=u(()=>{const e=t.current;l(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[l]),p=u(function(){const e={capture:!0,passive:!1},t={cancel:c,completed:l,getPhase:i},r=N(window,function({cancel:e,completed:t,getPhase:n}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{const r=n();if("DRAGGING"!==r.type)return void e();r.hasMoved=!0;const{clientX:i,clientY:o}=t.touches[0],a={x:i,y:o};t.preventDefault(),r.actions.move(a)}},{eventName:"touchend",fn:r=>{const i=n();"DRAGGING"===i.type?(r.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),t()):e()}},{eventName:"touchcancel",fn:t=>{"DRAGGING"===n().type?(t.preventDefault(),e()):e()}},{eventName:"touchforcechange",fn:t=>{const r=n();"IDLE"===r.type&&M(!1);const i=t.touches[0];if(!i)return;if(!(i.force>=.15))return;const o=r.actions.shouldRespectForcePress();if("PENDING"!==r.type)return o?r.hasMoved?void t.preventDefault():void e():void t.preventDefault();o&&e()}},{eventName:cr,fn:e}]}(t),e),o=N(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:n=>{"DRAGGING"===t().type?(27===n.keyCode&&n.preventDefault(),e()):e()}},{eventName:cr,fn:e}]}(t),e);n.current=function(){r(),o()}},[c,i,l]),f=u(function(){const e=i();"PENDING"!==e.type&&M(!1);const t=e.actions.fluidLift(e.point);o({type:"DRAGGING",actions:t,hasMoved:!1})},[i,o]),g=u(function(e,t){"IDLE"!==i().type&&M(!1);const n=setTimeout(f,120);o({type:"PENDING",point:t,actions:e,longPressTimerId:n}),p()},[p,i,o,f]);Mn(function(){return s(),function(){n.current();const e=i();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),o(hr))}},[i,s,o]),Mn(function(){return N(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}];function Or({contextId:e,store:t,registry:n,customSensors:i,enableDefaultSensors:o}){const a=[...o?Cr:[],...i||[]],s=(0,r.useState)(()=>function(){let e=null;function t(){e||M(!1),e=null}return{isClaimed:function(){return Boolean(e)},isActive:function(t){return t===e},claim:function(t){e&&M(!1);const n={abandon:t};return e=n,n},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}}())[0],l=u(function(e,t){er(e)&&!er(t)&&s.tryAbandon()},[s]);Mn(function(){let e=t.getState();return t.subscribe(()=>{const n=t.getState();l(e,n),e=n})},[s,t,l]),Mn(()=>s.tryAbandon,[s.tryAbandon]);const c=u(e=>Ar({lockAPI:s,registry:n,store:t,draggableId:e}),[s,n,t]),p=u((r,i,o)=>Rr({lockAPI:s,registry:n,contextId:e,store:t,draggableId:r,forceSensorStop:i||null,sourceEvent:o&&o.sourceEvent?o.sourceEvent:null}),[e,s,n,t]),f=u(t=>function(e,t){const n=Dr(e,t);return n?n.getAttribute(On.draggableId):null}(e,t),[e]),g=u(e=>{const t=n.draggable.findById(e);return t?t.options:null},[n.draggable]),h=u(function(){s.isClaimed()&&(s.tryAbandon(),"IDLE"!==t.getState().phase&&t.dispatch(Rt()))},[s,t]),m=u(()=>s.isClaimed(),[s]),b=d(()=>({canGetLock:c,tryGetLock:p,findClosestDraggableId:f,findOptionsForDraggable:g,tryReleaseLock:h,isLockClaimed:m}),[c,p,f,g,h,m]);Kn();for(let e=0;e<a.length;e++)a[e](b)}const Pr=e=>({onBeforeCapture:t=>{const n=()=>{e.onBeforeCapture&&e.onBeforeCapture(t)};i().version.startsWith("16")||i().version.startsWith("17")?n():(0,o.flushSync)(n)},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),Nr=e=>({...gn,...e.autoScrollerOptions,durationDampening:{...gn.durationDampening,...e.autoScrollerOptions}});function Br(e){return e.current||M(!1),e.current}function Tr(e){const{contextId:t,setCallbacks:n,sensors:o,nonce:a,dragHandleUsageInstructions:c}=e,p=(0,r.useRef)(null);Qn();const f=Zn(e),g=u(()=>Pr(f.current),[f]),h=u(()=>Nr(f.current),[f]),m=function(e){const t=d(()=>Vn(e),[e]),n=(0,r.useRef)(null);return(0,r.useEffect)(function(){const e=document.createElement("div");return n.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),C(e.style,$n),Un().appendChild(e),function(){setTimeout(function(){const t=Un();t.contains(e)&&t.removeChild(e),e===n.current&&(n.current=null)})}},[t]),u(e=>{const t=n.current;t&&(t.textContent=e)},[])}(t),b=function({contextId:e,text:t}){const n=Xn("hidden-text",{separator:"-"}),i=d(()=>function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`}({contextId:e,uniqueId:n}),[n,e]);return(0,r.useEffect)(function(){const e=document.createElement("div");return e.id=i,e.textContent=t,e.style.display="none",Un().appendChild(e),function(){const t=Un();t.contains(e)&&t.removeChild(e)}},[i,t]),i}({contextId:t,text:c}),v=function(e,t){const n=d(()=>Ln(e),[e]),i=(0,r.useRef)(null),o=(0,r.useRef)(null),a=u(A(e=>{const t=o.current;t||M(!1),t.textContent=e}),[]),s=u(e=>{const t=i.current;t||M(!1),t.textContent=e},[]);Mn(()=>{(i.current||o.current)&&M(!1);const r=Gn(t),l=Gn(t);return i.current=r,o.current=l,r.setAttribute(`${Cn}-always`,e),l.setAttribute(`${Cn}-dynamic`,e),_n().appendChild(r),_n().appendChild(l),s(n.always),a(n.resting),()=>{const e=e=>{const t=e.current;t||M(!1),_n().removeChild(t),e.current=null};e(i),e(o)}},[t,s,a,n.always,n.resting,e]);const l=u(()=>a(n.dragging),[a,n.dragging]),c=u(e=>{a("DROP"!==e?n.userCancel:n.dropAnimating)},[a,n.dropAnimating,n.userCancel]),p=u(()=>{o.current&&a(n.resting)},[a,n.resting]);return d(()=>({dragging:l,dropping:c,resting:p}),[l,c,p])}(t,a),y=u(e=>{Br(p).dispatch(e)},[]),x=d(()=>(0,s.bindActionCreators)({publishWhileDragging:bt,updateDroppableScroll:yt,updateDroppableIsEnabled:xt,updateDroppableIsCombineEnabled:wt,collectionStarting:vt},y),[y]),w=function(){const e=d(jn,[]);return(0,r.useEffect)(()=>function(){i().version.startsWith("16")||i().version.startsWith("17")?requestAnimationFrame(e.clean):e.clean()},[e]),e}(),I=d(()=>cn(w,x),[w,x]),D=d(()=>Rn({scrollWindow:un,scrollDroppable:I.scrollDroppable,getAutoScrollerOptions:h,...(0,s.bindActionCreators)({move:It},y)}),[I.scrollDroppable,y,h]),E=function(e){const t=(0,r.useRef)({}),n=(0,r.useRef)(null),i=(0,r.useRef)(null),o=(0,r.useRef)(!1),a=u(function(e,n){const r={id:e,focus:n};return t.current[e]=r,function(){const n=t.current;n[e]!==r&&delete n[e]}},[]),s=u(function(t){const n=Fn(e,t);n&&n!==document.activeElement&&n.focus()},[e]),l=u(function(e,t){n.current===e&&(n.current=t)},[]),c=u(function(){i.current||o.current&&(i.current=requestAnimationFrame(()=>{i.current=null;const e=n.current;e&&s(e)}))},[s]),p=u(function(e){n.current=null;const t=document.activeElement;t&&t.getAttribute(On.draggableId)===e&&(n.current=e)},[]);return Mn(()=>(o.current=!0,function(){o.current=!1;const e=i.current;e&&cancelAnimationFrame(e)}),[]),d(()=>({register:a,tryRecordFocus:p,tryRestoreFocusRecorded:c,tryShiftRecord:l}),[a,p,c,l])}(t),S=d(()=>nn({announce:m,autoScroller:D,dimensionMarshal:I,focusMarshal:E,getResponders:g,styleMarshal:v}),[m,D,I,E,g,v]);p.current=S;const R=u(()=>{const e=Br(p);"IDLE"!==e.getState().phase&&e.dispatch(Rt())},[]),O=u(()=>{const e=Br(p).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging},[]);n(d(()=>({isDragging:O,tryAbort:R}),[O,R]));const P=u(e=>dn(Br(p).getState(),e),[]),N=u(()=>Xe(Br(p).getState()),[]),B=d(()=>({marshal:I,focus:E,contextId:t,canLift:P,isMovementAllowed:N,dragHandleUsageInstructionsId:b,registry:w}),[t,I,b,E,P,N,w]);return Or({contextId:t,store:S,registry:w,customSensors:o||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,r.useEffect)(()=>R,[R]),i().createElement(Jn.Provider,{value:B},i().createElement(l.Provider,{context:Hn,store:S},e.children))}let Lr=0;var Mr="useId"in i()?function(){return i().useId()}:function(){return d(()=>""+Lr++,[])};function _r(e){const t=Mr(),n=e.dragHandleUsageInstructions||j.dragHandleUsageInstructions;return i().createElement(_,null,r=>i().createElement(Tr,{nonce:e.nonce,contextId:t,setCallbacks:r,dragHandleUsageInstructions:n,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children))}const Gr=5e3,zr=4500,Wr=(e,t)=>t?_t.drop(t.duration):e?_t.snap:_t.fluid,kr=(e,t)=>{if(e)return t?Bt.drop:Bt.combining};function Fr(e){return"DRAGGING"===e.type?function(e){const t=e.dimension.client,{offset:n,combineWith:r,dropping:i}=e,o=Boolean(r),a=(e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode)(e),s=Boolean(i),l=s?Wt(n,o):zt(n);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:Wr(a,i),transform:l,opacity:kr(o,s),zIndex:s?zr:Gr,pointerEvents:"none"}}(e):{transform:zt((t=e).offset),transition:t.shouldAnimateDisplacement?void 0:"none"};var t}function jr(e){const t=Xn("draggable"),{descriptor:n,registry:i,getDraggableRef:o,canDragInteractiveElements:a,shouldRespectForcePress:s,isEnabled:l}=e,c=d(()=>({canDragInteractiveElements:a,shouldRespectForcePress:s,isEnabled:l}),[a,l,s]),p=u(e=>{const t=o();return t||M(!1),function(e,t,n=H){const r=window.getComputedStyle(t),i=t.getBoundingClientRect(),o=w(i,r),a=x(o,n);return{descriptor:e,placeholder:{client:o,tagName:t.tagName.toLowerCase(),display:r.display},displaceBy:{x:o.marginBox.width,y:o.marginBox.height},client:o,page:a}}(n,t,e)},[n,o]),f=d(()=>({uniqueId:t,descriptor:n,options:c,getDimension:p}),[n,p,c,t]),g=(0,r.useRef)(f),h=(0,r.useRef)(!0);Mn(()=>(i.draggable.register(g.current),()=>i.draggable.unregister(g.current)),[i.draggable]),Mn(()=>{if(h.current)return void(h.current=!1);const e=g.current;g.current=f,i.draggable.update(f,e)},[f,i.draggable])}var Hr=i().createContext(null);function Ur(e){const t=(0,r.useContext)(e);return t||M(!1),t}function $r(e){e.preventDefault()}var Vr=e=>{const t=(0,r.useRef)(null),n=u((e=null)=>{t.current=e},[]),a=u(()=>t.current,[]),{contextId:s,dragHandleUsageInstructionsId:l,registry:c}=Ur(Jn),{type:p,droppableId:f}=Ur(Hr),g=d(()=>({id:e.draggableId,index:e.index,type:p,droppableId:f}),[e.draggableId,e.index,p,f]),{children:h,draggableId:m,isEnabled:b,shouldRespectForcePress:v,canDragInteractiveElements:y,isClone:x,mapped:w,dropAnimationFinished:I}=e;if(Qn(),Kn(),!x){jr(d(()=>({descriptor:g,registry:c,getDraggableRef:a,canDragInteractiveElements:y,shouldRespectForcePress:v,isEnabled:b}),[g,c,a,y,v,b]))}const D=d(()=>b?{tabIndex:0,role:"button","aria-describedby":l,"data-rfd-drag-handle-draggable-id":m,"data-rfd-drag-handle-context-id":s,draggable:!1,onDragStart:$r}:null,[s,l,m,b]),E=u(e=>{"DRAGGING"===w.type&&w.dropping&&"transform"===e.propertyName&&(i().version.startsWith("16")||i().version.startsWith("17")?I():(0,o.flushSync)(I))},[I,w]),S=d(()=>{const e=Fr(w),t="DRAGGING"===w.type&&w.dropping?E:void 0;return{innerRef:n,draggableProps:{"data-rfd-draggable-context-id":s,"data-rfd-draggable-id":m,style:e,onTransitionEnd:t},dragHandleProps:D}},[s,D,m,w,E,n]),A=d(()=>({draggableId:g.id,type:g.type,source:{index:g.index,droppableId:g.droppableId}}),[g.droppableId,g.id,g.index,g.type]);return i().createElement(i().Fragment,null,h(S,w.snapshot,A))},qr=(e,t)=>e===t,Yr=e=>{const{combine:t,destination:n}=e;return n?n.droppableId:t?t.droppableId:null};function Xr(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}const Jr={mapped:{type:"SECONDARY",offset:H,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:Xr(null)}};const Kr={dropAnimationFinished:Pt},Qr=(0,l.connect)(()=>{const e=function(){const e=A((e,t)=>({x:e,y:t})),t=A((e,t,n=null,r=null,i=null)=>({isDragging:!0,isClone:t,isDropAnimating:Boolean(i),dropAnimation:i,mode:e,draggingOver:n,combineWith:r,combineTargetFor:null})),n=A((e,n,r,i,o=null,a=null,s=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:o,combineWith:a,mode:n,offset:e,dimension:r,forceShouldAnimate:s,snapshot:t(n,i,o,a,null)}}));return(r,i)=>{if(er(r)){if(r.critical.draggable.id!==i.draggableId)return null;const t=r.current.client.offset,a=r.dimensions.draggables[i.draggableId],s=qe(r.impact),l=(o=r.impact).at&&"COMBINE"===o.at.type?o.at.combine.draggableId:null,c=r.forceShouldAnimate;return n(e(t.x,t.y),r.movementMode,a,i.isClone,s,l,c)}var o;if("DROP_ANIMATING"===r.phase){const e=r.completed;if(e.result.draggableId!==i.draggableId)return null;const n=i.isClone,o=r.dimensions.draggables[i.draggableId],a=e.result,s=a.mode,l=Yr(a),c=(e=>e.combine?e.combine.draggableId:null)(a),d={duration:r.dropDuration,curve:Nt,moveTo:r.newHomeClientOffset,opacity:c?Bt.drop:null,scale:c?Tt.drop:null};return{mapped:{type:"DRAGGING",offset:r.newHomeClientOffset,dimension:o,dropping:d,draggingOver:l,combineWith:c,mode:s,forceShouldAnimate:null,snapshot:t(s,n,l,c,d)}}}return null}}(),t=function(){const e=A((e,t)=>({x:e,y:t})),t=A(Xr),n=A((e,n=null,r)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:n,shouldAnimateDisplacement:r,snapshot:t(n)}})),r=e=>e?n(H,e,!0):null,i=(t,i,o,a)=>{const s=o.displaced.visible[t],l=Boolean(a.inVirtualList&&a.effected[t]),c=ce(o),d=c&&c.draggableId===t?i:null;if(!s){if(!l)return r(d);if(o.displaced.invisible[t])return null;const i=q(a.displacedBy.point),s=e(i.x,i.y);return n(s,d,!0)}if(l)return r(d);const u=o.displacedBy.point,p=e(u.x,u.y);return n(p,d,s.shouldAnimate)};return(e,t)=>{if(er(e))return e.critical.draggable.id===t.draggableId?null:i(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){const n=e.completed;return n.result.draggableId===t.draggableId?null:i(t.draggableId,n.result.draggableId,n.impact,n.afterCritical)}return null}}();return(n,r)=>e(n,r)||t(n,r)||Jr},Kr,null,{context:Hn,areStatePropsEqual:qr})(Vr);var Zr=Qr;function ei(e){return Ur(Hr).isUsingCloneFor!==e.draggableId||e.isClone?i().createElement(Zr,e):null}function ti(e){const t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,n=Boolean(e.disableInteractiveElementBlocking),r=Boolean(e.shouldRespectForcePress);return i().createElement(ei,C({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:n,shouldRespectForcePress:r}))}const ni=e=>t=>e===t,ri=ni("scroll"),ii=ni("auto"),oi=(ni("visible"),(e,t)=>t(e.overflowX)||t(e.overflowY)),ai=e=>{const t=window.getComputedStyle(e),n={overflowX:t.overflowX,overflowY:t.overflowY};return oi(n,ri)||oi(n,ii)},si=e=>null==e||e===document.body||e===document.documentElement?null:ai(e)?e:si(e.parentElement);var li=e=>({x:e.scrollLeft,y:e.scrollTop});const ci=e=>{if(!e)return!1;return"fixed"===window.getComputedStyle(e).position||ci(e.parentElement)};var di=({ref:e,descriptor:t,env:n,windowScroll:r,direction:i,isDropDisabled:o,isCombineEnabled:a,shouldClipSubject:s})=>{const l=n.closestScrollable,c=((e,t)=>{const n=I(e);if(!t)return n;if(e!==t)return n;const r=n.paddingBox.top-t.scrollTop,i=n.paddingBox.left-t.scrollLeft,o=r+t.scrollHeight,a=i+t.scrollWidth,s=g({top:r,right:a,bottom:o,left:i},n.border);return b({borderBox:s,margin:n.margin,border:n.border,padding:n.padding})})(e,l),d=x(c,r),u=(()=>{if(!l)return null;const e=I(l),t={scrollHeight:l.scrollHeight,scrollWidth:l.scrollWidth};return{client:e,page:x(e,r),scroll:li(l),scrollSize:t,shouldClipSubject:s}})(),p=(({descriptor:e,isEnabled:t,isCombineEnabled:n,isFixedOnPage:r,direction:i,client:o,page:a,closest:s})=>{const l=(()=>{if(!s)return null;const{scrollSize:e,client:t}=s,n=rn({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:s.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:s.shouldClipSubject,scroll:{initial:s.scroll,current:s.scroll,max:n,diff:{value:H,displacement:H}}}})(),c="vertical"===i?ve:ye;return{descriptor:e,isCombineEnabled:n,isFixedOnPage:r,axis:c,isEnabled:t,client:o,page:a,frame:l,subject:te({page:a,withPlaceholder:null,axis:c,frame:l})}})({descriptor:t,isEnabled:!o,isCombineEnabled:a,isFixedOnPage:n.isFixedOnPage,direction:i,client:c,page:d,closest:u});return p};const ui={passive:!1},pi={passive:!0};var fi=e=>e.shouldPublishImmediately?ui:pi;const gi=e=>e&&e.env.closestScrollable||null;function hi(e){const t=(0,r.useRef)(null),n=Ur(Jn),i=Xn("droppable"),{registry:o,marshal:a}=n,s=Zn(e),l=d(()=>({id:e.droppableId,type:e.type,mode:e.mode}),[e.droppableId,e.mode,e.type]),c=(0,r.useRef)(l),p=d(()=>A((e,n)=>{t.current||M(!1);const r={x:e,y:n};a.updateDroppableScroll(l.id,r)}),[l.id,a]),f=u(()=>{const e=t.current;return e&&e.env.closestScrollable?li(e.env.closestScrollable):H},[]),g=u(()=>{const e=f();p(e.x,e.y)},[f,p]),h=d(()=>R(g),[g]),m=u(()=>{const e=t.current,n=gi(e);e&&n||M(!1);e.scrollOptions.shouldPublishImmediately?g():h()},[h,g]),b=u((e,r)=>{t.current&&M(!1);const i=s.current,o=i.getDroppableRef();o||M(!1);const a=(e=>({closestScrollable:si(e),isFixedOnPage:ci(e)}))(o),c={ref:o,descriptor:l,env:a,scrollOptions:r};t.current=c;const d=di({ref:o,descriptor:l,env:a,windowScroll:e,direction:i.direction,isDropDisabled:i.isDropDisabled,isCombineEnabled:i.isCombineEnabled,shouldClipSubject:!i.ignoreContainerClipping}),u=a.closestScrollable;return u&&(u.setAttribute(Bn.contextId,n.contextId),u.addEventListener("scroll",m,fi(c.scrollOptions))),d},[n.contextId,l,m,s]),v=u(()=>{const e=t.current,n=gi(e);return e&&n||M(!1),li(n)},[]),y=u(()=>{const e=t.current;e||M(!1);const n=gi(e);t.current=null,n&&(h.cancel(),n.removeAttribute(Bn.contextId),n.removeEventListener("scroll",m,fi(e.scrollOptions)))},[m,h]),x=u(e=>{const n=t.current;n||M(!1);const r=gi(n);r||M(!1),r.scrollTop+=e.y,r.scrollLeft+=e.x},[]),w=d(()=>({getDimensionAndWatchScroll:b,getScrollWhileDragging:v,dragStopped:y,scroll:x}),[y,b,v,x]),I=d(()=>({uniqueId:i,descriptor:l,callbacks:w}),[w,l,i]);Mn(()=>(c.current=I.descriptor,o.droppable.register(I),()=>{t.current&&y(),o.droppable.unregister(I)}),[w,l,y,I,a,o.droppable]),Mn(()=>{t.current&&a.updateDroppableIsEnabled(c.current.id,!e.isDropDisabled)},[e.isDropDisabled,a]),Mn(()=>{t.current&&a.updateDroppableIsCombineEnabled(c.current.id,e.isCombineEnabled)},[e.isCombineEnabled,a])}function mi(){}const bi={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},vi=({isAnimatingOpenOnMount:e,placeholder:t,animate:n})=>{const r=(({isAnimatingOpenOnMount:e,placeholder:t,animate:n})=>e||"close"===n?bi:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin})({isAnimatingOpenOnMount:e,placeholder:t,animate:n});return{display:t.display,boxSizing:"border-box",width:r.width,height:r.height,marginTop:r.margin.top,marginRight:r.margin.right,marginBottom:r.margin.bottom,marginLeft:r.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==n?_t.placeholder:null}};var yi=i().memo(e=>{const t=(0,r.useRef)(null),n=u(()=>{t.current&&(clearTimeout(t.current),t.current=null)},[]),{animate:o,onTransitionEnd:a,onClose:s,contextId:l}=e,[c,d]=(0,r.useState)("open"===e.animate);(0,r.useEffect)(()=>c?"open"!==o?(n(),d(!1),mi):t.current?mi:(t.current=setTimeout(()=>{t.current=null,d(!1)}),n):mi,[o,c,n]);const p=u(e=>{"height"===e.propertyName&&(a(),"close"===o&&s())},[o,s,a]),f=vi({isAnimatingOpenOnMount:c,animate:e.animate,placeholder:e.placeholder});return i().createElement(e.placeholder.tagName,{style:f,"data-rfd-placeholder-context-id":l,onTransitionEnd:p,ref:e.innerRef})});class xi extends i().PureComponent{constructor(...e){super(...e),this.state={isVisible:Boolean(this.props.on),data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:Boolean(e.on),data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;const e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}var wi=e=>{const t=(0,r.useContext)(Jn);t||M(!1);const{contextId:n,isMovementAllowed:o}=t,s=(0,r.useRef)(null),l=(0,r.useRef)(null),{children:c,droppableId:p,type:f,mode:g,direction:h,ignoreContainerClipping:m,isDropDisabled:b,isCombineEnabled:v,snapshot:y,useClone:x,updateViewportMaxScroll:w,getContainerForClone:I}=e,D=u(()=>s.current,[]),E=u((e=null)=>{s.current=e},[]),S=(u(()=>l.current,[]),u((e=null)=>{l.current=e},[]));Qn();const A=u(()=>{o()&&w({maxScroll:an()})},[o,w]);hi({droppableId:p,type:f,mode:g,direction:h,isDropDisabled:b,isCombineEnabled:v,ignoreContainerClipping:m,getDroppableRef:D});const R=d(()=>i().createElement(xi,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},({onClose:e,data:t,animate:r})=>i().createElement(yi,{placeholder:t,onClose:e,innerRef:S,animate:r,contextId:n,onTransitionEnd:A})),[n,A,e.placeholder,e.shouldAnimatePlaceholder,S]),C=d(()=>({innerRef:E,placeholder:R,droppableProps:{"data-rfd-droppable-id":p,"data-rfd-droppable-context-id":n}}),[n,p,R,E]),O=x?x.dragging.draggableId:null,P=d(()=>({droppableId:p,type:f,isUsingCloneFor:O}),[p,O,f]);return i().createElement(Hr.Provider,{value:P},c(C,y),function(){if(!x)return null;const{dragging:e,render:t}=x,n=i().createElement(ei,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(n,r)=>t(n,r,e));return a().createPortal(n,I())}())};const Ii={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||M(!1),document.body}},Di=e=>{let t,n={...e};for(t in Ii)void 0===e[t]&&(n={...n,[t]:Ii[t]});return n},Ei=(e,t)=>e===t.droppable.type,Si=(e,t)=>t.draggables[e.draggable.id],Ai={updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})};var Ri=(0,l.connect)(()=>{const e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},n=A(e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}})),r=A((r,i,o,a,s,l)=>{const c=s.descriptor.id;if(s.descriptor.droppableId===r){const e=l?{render:l,dragging:n(s.descriptor)}:null,t={isDraggingOver:o,draggingOverWith:o?c:null,draggingFromThisWith:c,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!1,snapshot:t,useClone:e}}if(!i)return t;if(!a)return e;const d={isDraggingOver:o,draggingOverWith:c,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:s.placeholder,shouldAnimatePlaceholder:!0,snapshot:d,useClone:null}});return(n,i)=>{const o=Di(i),a=o.droppableId,s=o.type,l=!o.isDropDisabled,c=o.renderClone;if(er(n)){const e=n.critical;if(!Ei(s,e))return t;const i=Si(e,n.dimensions),o=qe(n.impact)===a;return r(a,l,o,o,i,c)}if("DROP_ANIMATING"===n.phase){const e=n.completed;if(!Ei(s,e.critical))return t;const i=Si(e.critical,n.dimensions);return r(a,l,Yr(e.result)===a,qe(e.impact)===a,i,c)}if("IDLE"===n.phase&&n.completed&&!n.shouldFlush){const r=n.completed;if(!Ei(s,r.critical))return t;const i=qe(r.impact)===a,o=Boolean(r.impact.at&&"COMBINE"===r.impact.at.type),l=r.critical.droppable.id===a;return i?o?e:t:l?e:t}return t}},Ai,(e,t,n)=>({...Di(n),...e,...t}),{context:Hn,areStatePropsEqual:qr})(wi)},3929:(e,t,n)=>{"use strict";n.d(t,{c:()=>S});var r,i=n(2540),o=n(5959),a=n(8398),s=function(){return s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},s.apply(this,arguments)},l={width:"100%",height:"10px",top:"0px",left:"0px",cursor:"row-resize"},c={width:"10px",height:"100%",top:"0px",left:"0px",cursor:"col-resize"},d={width:"20px",height:"20px",position:"absolute",zIndex:1},u={top:s(s({},l),{top:"-5px"}),right:s(s({},c),{left:void 0,right:"-5px"}),bottom:s(s({},l),{top:void 0,bottom:"-5px"}),left:s(s({},c),{left:"-5px"}),topRight:s(s({},d),{right:"-10px",top:"-10px",cursor:"ne-resize"}),bottomRight:s(s({},d),{right:"-10px",bottom:"-10px",cursor:"se-resize"}),bottomLeft:s(s({},d),{left:"-10px",bottom:"-10px",cursor:"sw-resize"}),topLeft:s(s({},d),{left:"-10px",top:"-10px",cursor:"nw-resize"})},p=(0,o.memo)(function(e){var t=e.onResizeStart,n=e.direction,r=e.children,a=e.replaceStyles,l=e.className,c=(0,o.useCallback)(function(e){t(e,n)},[t,n]),d=(0,o.useCallback)(function(e){t(e,n)},[t,n]),p=(0,o.useMemo)(function(){return s(s({position:"absolute",userSelect:"none"},u[n]),null!=a?a:{})},[a,n]);return(0,i.jsx)("div",{className:l||void 0,style:p,onMouseDown:c,onTouchStart:d,children:r})}),f=(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),g=function(){return g=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},g.apply(this,arguments)},h={width:"auto",height:"auto"},m=function(e,t,n){return Math.max(Math.min(e,n),t)},b=function(e,t,n){var r=Math.round(e/t);return r*t+n*(r-1)},v=function(e,t){return new RegExp(e,"i").test(t)},y=function(e){return Boolean(e.touches&&e.touches.length)},x=function(e,t,n){void 0===n&&(n=0);var r=t.reduce(function(n,r,i){return Math.abs(r-e)<Math.abs(t[n]-e)?i:n},0),i=Math.abs(t[r]-e);return 0===n||i<n?t[r]:e},w=function(e){return"auto"===(e=e.toString())||e.endsWith("px")||e.endsWith("%")||e.endsWith("vh")||e.endsWith("vw")||e.endsWith("vmax")||e.endsWith("vmin")?e:"".concat(e,"px")},I=function(e,t,n,r){if(e&&"string"==typeof e){if(e.endsWith("px"))return Number(e.replace("px",""));if(e.endsWith("%"))return t*(Number(e.replace("%",""))/100);if(e.endsWith("vw"))return n*(Number(e.replace("vw",""))/100);if(e.endsWith("vh"))return r*(Number(e.replace("vh",""))/100)}return e},D=["as","ref","style","className","grid","gridGap","snap","bounds","boundsByDirection","size","defaultSize","minWidth","minHeight","maxWidth","maxHeight","lockAspectRatio","lockAspectRatioExtraWidth","lockAspectRatioExtraHeight","enable","handleStyles","handleClasses","handleWrapperStyle","handleWrapperClass","children","onResizeStart","onResize","onResizeStop","handleComponent","scale","resizeRatio","snapGap"],E="__resizable_base__",S=function(e){function t(t){var n,r,i,o,a=e.call(this,t)||this;return a.ratio=1,a.resizable=null,a.parentLeft=0,a.parentTop=0,a.resizableLeft=0,a.resizableRight=0,a.resizableTop=0,a.resizableBottom=0,a.targetLeft=0,a.targetTop=0,a.delta={width:0,height:0},a.appendBase=function(){if(!a.resizable||!a.window)return null;var e=a.parentNode;if(!e)return null;var t=a.window.document.createElement("div");return t.style.width="100%",t.style.height="100%",t.style.position="absolute",t.style.transform="scale(0, 0)",t.style.left="0",t.style.flex="0 0 100%",t.classList?t.classList.add(E):t.className+=E,e.appendChild(t),t},a.removeBase=function(e){var t=a.parentNode;t&&t.removeChild(e)},a.state={isResizing:!1,width:null!==(r=null===(n=a.propsSize)||void 0===n?void 0:n.width)&&void 0!==r?r:"auto",height:null!==(o=null===(i=a.propsSize)||void 0===i?void 0:i.height)&&void 0!==o?o:"auto",direction:"right",original:{x:0,y:0,width:0,height:0},backgroundStyle:{height:"100%",width:"100%",backgroundColor:"rgba(0,0,0,0)",cursor:"auto",opacity:0,position:"fixed",zIndex:9999,top:"0",left:"0",bottom:"0",right:"0"},flexBasis:void 0},a.onResizeStart=a.onResizeStart.bind(a),a.onMouseMove=a.onMouseMove.bind(a),a.onMouseUp=a.onMouseUp.bind(a),a}return f(t,e),Object.defineProperty(t.prototype,"parentNode",{get:function(){return this.resizable?this.resizable.parentNode:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"window",{get:function(){return this.resizable&&this.resizable.ownerDocument?this.resizable.ownerDocument.defaultView:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"propsSize",{get:function(){return this.props.size||this.props.defaultSize||h},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"size",{get:function(){var e=0,t=0;if(this.resizable&&this.window){var n=this.resizable.offsetWidth,r=this.resizable.offsetHeight,i=this.resizable.style.position;"relative"!==i&&(this.resizable.style.position="relative"),e="auto"!==this.resizable.style.width?this.resizable.offsetWidth:n,t="auto"!==this.resizable.style.height?this.resizable.offsetHeight:r,this.resizable.style.position=i}return{width:e,height:t}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"sizeStyle",{get:function(){var e=this,t=this.props.size,n=function(t){var n;if(void 0===e.state[t]||"auto"===e.state[t])return"auto";if(e.propsSize&&e.propsSize[t]&&(null===(n=e.propsSize[t])||void 0===n?void 0:n.toString().endsWith("%"))){if(e.state[t].toString().endsWith("%"))return e.state[t].toString();var r=e.getParentSize(),i=Number(e.state[t].toString().replace("px",""))/r[t]*100;return"".concat(i,"%")}return w(e.state[t])};return{width:t&&void 0!==t.width&&!this.state.isResizing?w(t.width):n("width"),height:t&&void 0!==t.height&&!this.state.isResizing?w(t.height):n("height")}},enumerable:!1,configurable:!0}),t.prototype.getParentSize=function(){if(!this.parentNode)return this.window?{width:this.window.innerWidth,height:this.window.innerHeight}:{width:0,height:0};var e=this.appendBase();if(!e)return{width:0,height:0};var t=!1,n=this.parentNode.style.flexWrap;"wrap"!==n&&(t=!0,this.parentNode.style.flexWrap="wrap"),e.style.position="relative",e.style.minWidth="100%",e.style.minHeight="100%";var r={width:e.offsetWidth,height:e.offsetHeight};return t&&(this.parentNode.style.flexWrap=n),this.removeBase(e),r},t.prototype.bindEvents=function(){this.window&&(this.window.addEventListener("mouseup",this.onMouseUp),this.window.addEventListener("mousemove",this.onMouseMove),this.window.addEventListener("mouseleave",this.onMouseUp),this.window.addEventListener("touchmove",this.onMouseMove,{capture:!0,passive:!1}),this.window.addEventListener("touchend",this.onMouseUp))},t.prototype.unbindEvents=function(){this.window&&(this.window.removeEventListener("mouseup",this.onMouseUp),this.window.removeEventListener("mousemove",this.onMouseMove),this.window.removeEventListener("mouseleave",this.onMouseUp),this.window.removeEventListener("touchmove",this.onMouseMove,!0),this.window.removeEventListener("touchend",this.onMouseUp))},t.prototype.componentDidMount=function(){if(this.resizable&&this.window){var e=this.window.getComputedStyle(this.resizable);this.setState({width:this.state.width||this.size.width,height:this.state.height||this.size.height,flexBasis:"auto"!==e.flexBasis?e.flexBasis:void 0})}},t.prototype.componentWillUnmount=function(){this.window&&this.unbindEvents()},t.prototype.createSizeForCssProperty=function(e,t){var n=this.propsSize&&this.propsSize[t];return"auto"!==this.state[t]||this.state.original[t]!==e||void 0!==n&&"auto"!==n?e:"auto"},t.prototype.calculateNewMaxFromBoundary=function(e,t){var n,r,i=this.props.boundsByDirection,o=this.state.direction,a=i&&v("left",o),s=i&&v("top",o);if("parent"===this.props.bounds){var l=this.parentNode;l&&(n=a?this.resizableRight-this.parentLeft:l.offsetWidth+(this.parentLeft-this.resizableLeft),r=s?this.resizableBottom-this.parentTop:l.offsetHeight+(this.parentTop-this.resizableTop))}else"window"===this.props.bounds?this.window&&(n=a?this.resizableRight:this.window.innerWidth-this.resizableLeft,r=s?this.resizableBottom:this.window.innerHeight-this.resizableTop):this.props.bounds&&(n=a?this.resizableRight-this.targetLeft:this.props.bounds.offsetWidth+(this.targetLeft-this.resizableLeft),r=s?this.resizableBottom-this.targetTop:this.props.bounds.offsetHeight+(this.targetTop-this.resizableTop));return n&&Number.isFinite(n)&&(e=e&&e<n?e:n),r&&Number.isFinite(r)&&(t=t&&t<r?t:r),{maxWidth:e,maxHeight:t}},t.prototype.calculateNewSizeFromDirection=function(e,t){var n,r=this.props.scale||1,i=(n=this.props.resizeRatio||1,Array.isArray(n)?n:[n,n]),o=i[0],a=i[1],s=this.state,l=s.direction,c=s.original,d=this.props,u=d.lockAspectRatio,p=d.lockAspectRatioExtraHeight,f=d.lockAspectRatioExtraWidth,g=c.width,h=c.height,m=p||0,b=f||0;return v("right",l)&&(g=c.width+(e-c.x)*o/r,u&&(h=(g-b)/this.ratio+m)),v("left",l)&&(g=c.width-(e-c.x)*o/r,u&&(h=(g-b)/this.ratio+m)),v("bottom",l)&&(h=c.height+(t-c.y)*a/r,u&&(g=(h-m)*this.ratio+b)),v("top",l)&&(h=c.height-(t-c.y)*a/r,u&&(g=(h-m)*this.ratio+b)),{newWidth:g,newHeight:h}},t.prototype.calculateNewSizeFromAspectRatio=function(e,t,n,r){var i=this.props,o=i.lockAspectRatio,a=i.lockAspectRatioExtraHeight,s=i.lockAspectRatioExtraWidth,l=void 0===r.width?10:r.width,c=void 0===n.width||n.width<0?e:n.width,d=void 0===r.height?10:r.height,u=void 0===n.height||n.height<0?t:n.height,p=a||0,f=s||0;if(o){var g=(d-p)*this.ratio+f,h=(u-p)*this.ratio+f,b=(l-f)/this.ratio+p,v=(c-f)/this.ratio+p,y=Math.max(l,g),x=Math.min(c,h),w=Math.max(d,b),I=Math.min(u,v);e=m(e,y,x),t=m(t,w,I)}else e=m(e,l,c),t=m(t,d,u);return{newWidth:e,newHeight:t}},t.prototype.setBoundingClientRect=function(){var e=1/(this.props.scale||1);if("parent"===this.props.bounds){var t=this.parentNode;if(t){var n=t.getBoundingClientRect();this.parentLeft=n.left*e,this.parentTop=n.top*e}}if(this.props.bounds&&"string"!=typeof this.props.bounds){var r=this.props.bounds.getBoundingClientRect();this.targetLeft=r.left*e,this.targetTop=r.top*e}if(this.resizable){var i=this.resizable.getBoundingClientRect(),o=i.left,a=i.top,s=i.right,l=i.bottom;this.resizableLeft=o*e,this.resizableRight=s*e,this.resizableTop=a*e,this.resizableBottom=l*e}},t.prototype.onResizeStart=function(e,t){if(this.resizable&&this.window){var n,r=0,i=0;if(e.nativeEvent&&function(e){return Boolean((e.clientX||0===e.clientX)&&(e.clientY||0===e.clientY))}(e.nativeEvent)?(r=e.nativeEvent.clientX,i=e.nativeEvent.clientY):e.nativeEvent&&y(e.nativeEvent)&&(r=e.nativeEvent.touches[0].clientX,i=e.nativeEvent.touches[0].clientY),this.props.onResizeStart)if(this.resizable)if(!1===this.props.onResizeStart(e,t,this.resizable))return;this.props.size&&(void 0!==this.props.size.height&&this.props.size.height!==this.state.height&&this.setState({height:this.props.size.height}),void 0!==this.props.size.width&&this.props.size.width!==this.state.width&&this.setState({width:this.props.size.width})),this.ratio="number"==typeof this.props.lockAspectRatio?this.props.lockAspectRatio:this.size.width/this.size.height;var o=this.window.getComputedStyle(this.resizable);if("auto"!==o.flexBasis){var a=this.parentNode;if(a){var s=this.window.getComputedStyle(a).flexDirection;this.flexDir=s.startsWith("row")?"row":"column",n=o.flexBasis}}this.setBoundingClientRect(),this.bindEvents();var l={original:{x:r,y:i,width:this.size.width,height:this.size.height},isResizing:!0,backgroundStyle:g(g({},this.state.backgroundStyle),{cursor:this.window.getComputedStyle(e.target).cursor||"auto"}),direction:t,flexBasis:n};this.setState(l)}},t.prototype.onMouseMove=function(e){var t=this;if(this.state.isResizing&&this.resizable&&this.window){if(this.window.TouchEvent&&y(e))try{e.preventDefault(),e.stopPropagation()}catch(e){}var n=this.props,r=n.maxWidth,i=n.maxHeight,o=n.minWidth,s=n.minHeight,l=y(e)?e.touches[0].clientX:e.clientX,c=y(e)?e.touches[0].clientY:e.clientY,d=this.state,u=d.direction,p=d.original,f=d.width,g=d.height,h=this.getParentSize(),m=function(e,t,n,r,i,o,a){return r=I(r,e.width,t,n),i=I(i,e.height,t,n),o=I(o,e.width,t,n),a=I(a,e.height,t,n),{maxWidth:void 0===r?void 0:Number(r),maxHeight:void 0===i?void 0:Number(i),minWidth:void 0===o?void 0:Number(o),minHeight:void 0===a?void 0:Number(a)}}(h,this.window.innerWidth,this.window.innerHeight,r,i,o,s);r=m.maxWidth,i=m.maxHeight,o=m.minWidth,s=m.minHeight;var v=this.calculateNewSizeFromDirection(l,c),w=v.newHeight,D=v.newWidth,E=this.calculateNewMaxFromBoundary(r,i);this.props.snap&&this.props.snap.x&&(D=x(D,this.props.snap.x,this.props.snapGap)),this.props.snap&&this.props.snap.y&&(w=x(w,this.props.snap.y,this.props.snapGap));var S=this.calculateNewSizeFromAspectRatio(D,w,{width:E.maxWidth,height:E.maxHeight},{width:o,height:s});if(D=S.newWidth,w=S.newHeight,this.props.grid){var A=b(D,this.props.grid[0],this.props.gridGap?this.props.gridGap[0]:0),R=b(w,this.props.grid[1],this.props.gridGap?this.props.gridGap[1]:0),C=this.props.snapGap||0;D=0===C||Math.abs(A-D)<=C?A:D,w=0===C||Math.abs(R-w)<=C?R:w}var O={width:D-p.width,height:w-p.height};if(this.delta=O,f&&"string"==typeof f)if(f.endsWith("%")){var P=D/h.width*100;D="".concat(P,"%")}else if(f.endsWith("vw")){var N=D/this.window.innerWidth*100;D="".concat(N,"vw")}else if(f.endsWith("vh")){var B=D/this.window.innerHeight*100;D="".concat(B,"vh")}if(g&&"string"==typeof g)if(g.endsWith("%")){P=w/h.height*100;w="".concat(P,"%")}else if(g.endsWith("vw")){N=w/this.window.innerWidth*100;w="".concat(N,"vw")}else if(g.endsWith("vh")){B=w/this.window.innerHeight*100;w="".concat(B,"vh")}var T={width:this.createSizeForCssProperty(D,"width"),height:this.createSizeForCssProperty(w,"height")};"row"===this.flexDir?T.flexBasis=T.width:"column"===this.flexDir&&(T.flexBasis=T.height);var L=this.state.width!==T.width,M=this.state.height!==T.height,_=this.state.flexBasis!==T.flexBasis,G=L||M||_;G&&(0,a.flushSync)(function(){t.setState(T)}),this.props.onResize&&G&&this.props.onResize(e,u,this.resizable,O)}},t.prototype.onMouseUp=function(e){var t,n,r=this.state,i=r.isResizing,o=r.direction;r.original;i&&this.resizable&&(this.props.onResizeStop&&this.props.onResizeStop(e,o,this.resizable,this.delta),this.props.size&&this.setState({width:null!==(t=this.props.size.width)&&void 0!==t?t:"auto",height:null!==(n=this.props.size.height)&&void 0!==n?n:"auto"}),this.unbindEvents(),this.setState({isResizing:!1,backgroundStyle:g(g({},this.state.backgroundStyle),{cursor:"auto"})}))},t.prototype.updateSize=function(e){var t,n;this.setState({width:null!==(t=e.width)&&void 0!==t?t:"auto",height:null!==(n=e.height)&&void 0!==n?n:"auto"})},t.prototype.renderResizer=function(){var e=this,t=this.props,n=t.enable,r=t.handleStyles,o=t.handleClasses,a=t.handleWrapperStyle,s=t.handleWrapperClass,l=t.handleComponent;if(!n)return null;var c=Object.keys(n).map(function(t){return!1!==n[t]?(0,i.jsx)(p,{direction:t,onResizeStart:e.onResizeStart,replaceStyles:r&&r[t],className:o&&o[t],children:l&&l[t]?l[t]:null},t):null});return(0,i.jsx)("div",{className:s,style:a,children:c})},t.prototype.render=function(){var e=this,t=Object.keys(this.props).reduce(function(t,n){return-1!==D.indexOf(n)||(t[n]=e.props[n]),t},{}),n=g(g(g({position:"relative",userSelect:this.state.isResizing?"none":"auto"},this.props.style),this.sizeStyle),{maxWidth:this.props.maxWidth,maxHeight:this.props.maxHeight,minWidth:this.props.minWidth,minHeight:this.props.minHeight,boxSizing:"border-box",flexShrink:0});this.state.flexBasis&&(n.flexBasis=this.state.flexBasis);var r=this.props.as||"div";return(0,i.jsxs)(r,g({style:n,className:this.props.className},t,{ref:function(t){t&&(e.resizable=t)},children:[this.state.isResizing&&(0,i.jsx)("div",{style:this.state.backgroundStyle}),this.props.children,this.renderResizer()]}))},t.defaultProps={as:"div",onResizeStart:function(){},onResize:function(){},onResizeStop:function(){},enable:{top:!0,right:!0,bottom:!0,left:!0,topRight:!0,bottomRight:!0,bottomLeft:!0,topLeft:!0},style:{},grid:[1,1],gridGap:[0,0],lockAspectRatio:!1,lockAspectRatioExtraWidth:0,lockAspectRatioExtraHeight:0,scale:1,resizeRatio:1,snapGap:0},t}(o.PureComponent)},3367:function(e,t,n){var r;r=function(e){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.i=function(e){return e},n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=10)}([function(e,t){var n,r,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(n===setTimeout)return setTimeout(e,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(e){n=o}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var l,c=[],d=!1,u=-1;function p(){d&&l&&(d=!1,l.length?c=l.concat(c):u=-1,c.length&&f())}function f(){if(!d){var e=s(p);d=!0;for(var t=c.length;t;){for(l=c,c=[];++u<t;)l&&l[u].run();u=-1,t=c.length}l=null,d=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{return r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function g(e,t){this.fun=e,this.array=t}function h(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];c.push(new g(e,t)),1!==c.length||d||s(f)},g.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,n){"use strict";function r(e){return function(){return e}}var i=function(){};i.thatReturns=r,i.thatReturnsFalse=r(!1),i.thatReturnsTrue=r(!0),i.thatReturnsNull=r(null),i.thatReturnsThis=function(){return this},i.thatReturnsArgument=function(e){return e},e.exports=i},function(e,t,n){"use strict";(function(t){var n=function(e){};"production"!==t.env.NODE_ENV&&(n=function(e){if(void 0===e)throw new Error("invariant requires an error message argument")}),e.exports=function(e,t,r,i,o,a,s,l){if(n(t),!e){var c;if(void 0===t)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var d=[r,i,o,a,s,l],u=0;(c=new Error(t.replace(/%s/g,function(){return d[u++]}))).name="Invariant Violation"}throw c.framesToPop=1,c}}}).call(t,n(0))},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(t,n){t.exports=e},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,i=n(4),o=((r=i)&&r.__esModule?r:{default:r}).default.createContext({registerPane:function(){},unregisterPane:function(){}});t.default=o,e.exports=t.default},function(e,t,n){"use strict";(function(t){var r,i=n(1);"production"!==t.env.NODE_ENV&&(r=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=0,o="Warning: "+e.replace(/%s/g,function(){return n[i++]});"undefined"!=typeof console&&console.error(o);try{throw new Error(o)}catch(e){}},i=function(e,t){if(void 0===t)throw new Error("`warning(condition, format, ...args)` requires a warning message argument");if(0!==t.indexOf("Failed Composite propType: ")&&!e){for(var n=arguments.length,i=Array(n>2?n-2:0),o=2;o<n;o++)i[o-2]=arguments[o];r.apply(void 0,[t].concat(i))}}),e.exports=i}).call(t,n(0))},function(e,t,n){(function(t){if("production"!==t.env.NODE_ENV){var r="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103;e.exports=n(13)(function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},!0)}else e.exports=n(12)()}).call(t,n(0))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(4),o=l(i),a=l(n(7)),s=l(n(5));function l(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var d=function(e){function t(){var e,n,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return n=r=c(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(o))),r.panes={},r.registerPane=function(e,t){t.forEach(function(t){r.panes[t]||(r.panes[t]=[]),r.findPane(e,t)||(r.panes[t].length>0&&r.syncScrollPosition(r.panes[t][0],e),r.panes[t].push(e))}),r.addEvents(e,t)},r.unregisterPane=function(e,t){t.forEach(function(t){r.findPane(e,t)&&(r.removeEvents(e),r.panes[t].splice(r.panes[t].indexOf(e),1))})},r.addEvents=function(e,t){e.onscroll=r.handlePaneScroll.bind(r,e,t)},r.removeEvents=function(e){e.onscroll=null},r.findPane=function(e,t){return!!r.panes[t]&&r.panes[t].find(function(t){return t===e})},r.handlePaneScroll=function(e,t){r.props.enabled&&window.requestAnimationFrame(function(){r.syncScrollPositions(e,t)})},r.syncScrollPositions=function(e,t){t.forEach(function(t){r.panes[t].forEach(function(n){e!==n&&(r.removeEvents(n,t),r.syncScrollPosition(e,n),window.requestAnimationFrame(function(){var e=Object.keys(r.panes).filter(function(e){return r.panes[e].includes(n)});r.addEvents(n,e)}))})}),r.props.onSync&&r.props.onSync(e)},c(r,n)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"getContextValue",value:function(){return{registerPane:this.registerPane,unregisterPane:this.unregisterPane}}},{key:"syncScrollPosition",value:function(e,t){var n=e.scrollTop,r=e.scrollHeight,i=e.clientHeight,o=e.scrollLeft,a=e.scrollWidth,s=e.clientWidth,l=r-i,c=a-s,d=this.props,u=d.proportional,p=d.vertical,f=d.horizontal,g=t.scrollHeight-i,h=t.scrollWidth-s;p&&l>0&&(t.scrollTop=u?g*n/l:n),f&&c>0&&(t.scrollLeft=u?h*o/c:o)}},{key:"render",value:function(){return o.default.createElement(s.default.Provider,{value:this.getContextValue()},o.default.Children.only(this.props.children))}}]),t}(i.Component);d.propTypes={onSync:a.default.func,children:a.default.element.isRequired,proportional:a.default.bool,vertical:a.default.bool,horizontal:a.default.bool,enabled:a.default.bool},d.defaultProps={proportional:!0,vertical:!0,horizontal:!0,enabled:!0},t.default=d,e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(4),o=s(n(7)),a=s(n(5));function s(e){return e&&e.__esModule?e:{default:e}}var l=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.toArray=function(e){return[].concat(e)},n.updateNode=function(){n.props.attachTo?n.node=n.props.attachTo.current:n.node=n.childRef.current},n.childRef=e.innerRef?e.innerRef:(0,i.createRef)(),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"componentDidMount",value:function(){this.props.enabled&&(this.updateNode(),this.node&&this.context.registerPane(this.node,this.toArray(this.props.group)))}},{key:"componentDidUpdate",value:function(e){this.props.attachTo!==e.attachTo&&(this.node&&this.context.unregisterPane(this.node,this.toArray(e.group)),this.updateNode(),this.node&&this.context.registerPane(this.node,this.toArray(e.group))),this.node&&this.props.enabled!==e.enabled&&(this.props.enabled?this.context.registerPane(this.node,this.toArray(e.group)):this.context.unregisterPane(this.node,this.toArray(e.group))),this.node&&this.props.enabled&&this.props.group!==e.group&&(this.context.unregisterPane(this.node,this.toArray(e.group)),this.context.registerPane(this.node,this.toArray(this.props.group)))}},{key:"componentWillUnmount",value:function(){this.node&&this.props.enabled&&this.context.unregisterPane(this.node,this.toArray(this.props.group))}},{key:"render",value:function(){return this.props.attachTo?this.props.children:(0,i.cloneElement)(i.Children.only(this.props.children),{ref:this.childRef})}}]),t}(i.Component);l.contextType=a.default,l.propTypes={children:o.default.node.isRequired,attachTo:o.default.oneOfType([o.default.func,o.default.shape({current:o.default.any})]),group:o.default.oneOfType([o.default.string,o.default.arrayOf(o.default.string)]),enabled:o.default.bool,innerRef:o.default.oneOfType([o.default.func,o.default.shape({current:o.default.any})])},l.defaultProps={group:"default",enabled:!0},t.default=l,e.exports=t.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(8);Object.defineProperty(t,"ScrollSync",{enumerable:!0,get:function(){return o(r).default}});var i=n(9);function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"ScrollSyncPane",{enumerable:!0,get:function(){return o(i).default}})},function(e,t,n){"use strict";(function(t){if("production"!==t.env.NODE_ENV)var r=n(2),i=n(6),o=n(3),a={};e.exports=function(e,n,s,l,c){if("production"!==t.env.NODE_ENV)for(var d in e)if(e.hasOwnProperty(d)){var u;try{r("function"==typeof e[d],"%s: %s type `%s` is invalid; it must be a function, usually from React.PropTypes.",l||"React class",s,d),u=e[d](n,d,l,s,null,o)}catch(e){u=e}if(i(!u||u instanceof Error,"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",l||"React class",s,d,typeof u),u instanceof Error&&!(u.message in a)){a[u.message]=!0;var p=c?c():"";i(!1,"Failed %s type: %s%s",s,u.message,null!=p?p:"")}}}}).call(t,n(0))},function(e,t,n){"use strict";var r=n(1),i=n(2),o=n(3);e.exports=function(){function e(e,t,n,r,a,s){s!==o&&i(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t};return n.checkPropTypes=r,n.PropTypes=n,n}},function(e,t,n){"use strict";(function(t){var r=n(1),i=n(2),o=n(6),a=n(3),s=n(11);e.exports=function(e,n){var l="function"==typeof Symbol&&Symbol.iterator,c="<<anonymous>>",d={array:g("array"),bool:g("boolean"),func:g("function"),number:g("number"),object:g("object"),string:g("string"),symbol:g("symbol"),any:f(r.thatReturnsNull),arrayOf:function(e){return f(function(t,n,r,i,o){if("function"!=typeof e)return new p("Property `"+o+"` of component `"+r+"` has invalid PropType notation inside arrayOf.");var s=t[n];if(!Array.isArray(s))return new p("Invalid "+i+" `"+o+"` of type `"+m(s)+"` supplied to `"+r+"`, expected an array.");for(var l=0;l<s.length;l++){var c=e(s,l,r,i,o+"["+l+"]",a);if(c instanceof Error)return c}return null})},element:f(function(t,n,r,i,o){var a=t[n];return e(a)?null:new p("Invalid "+i+" `"+o+"` of type `"+m(a)+"` supplied to `"+r+"`, expected a single ReactElement.")}),instanceOf:function(e){return f(function(t,n,r,i,o){if(!(t[n]instanceof e)){var a=e.name||c;return new p("Invalid "+i+" `"+o+"` of type `"+((s=t[n]).constructor&&s.constructor.name?s.constructor.name:c)+"` supplied to `"+r+"`, expected instance of `"+a+"`.")}var s;return null})},node:f(function(e,t,n,r,i){return h(e[t])?null:new p("Invalid "+r+" `"+i+"` supplied to `"+n+"`, expected a ReactNode.")}),objectOf:function(e){return f(function(t,n,r,i,o){if("function"!=typeof e)return new p("Property `"+o+"` of component `"+r+"` has invalid PropType notation inside objectOf.");var s=t[n],l=m(s);if("object"!==l)return new p("Invalid "+i+" `"+o+"` of type `"+l+"` supplied to `"+r+"`, expected an object.");for(var c in s)if(s.hasOwnProperty(c)){var d=e(s,c,r,i,o+"."+c,a);if(d instanceof Error)return d}return null})},oneOf:function(e){return Array.isArray(e)?f(function(t,n,r,i,o){for(var a=t[n],s=0;s<e.length;s++)if(u(a,e[s]))return null;return new p("Invalid "+i+" `"+o+"` of value `"+a+"` supplied to `"+r+"`, expected one of "+JSON.stringify(e)+".")}):("production"!==t.env.NODE_ENV&&o(!1,"Invalid argument supplied to oneOf, expected an instance of array."),r.thatReturnsNull)},oneOfType:function(e){if(!Array.isArray(e))return"production"!==t.env.NODE_ENV&&o(!1,"Invalid argument supplied to oneOfType, expected an instance of array."),r.thatReturnsNull;for(var n=0;n<e.length;n++){var i=e[n];if("function"!=typeof i)return o(!1,"Invalid argument supplid to oneOfType. Expected an array of check functions, but received %s at index %s.",v(i),n),r.thatReturnsNull}return f(function(t,n,r,i,o){for(var s=0;s<e.length;s++)if(null==(0,e[s])(t,n,r,i,o,a))return null;return new p("Invalid "+i+" `"+o+"` supplied to `"+r+"`.")})},shape:function(e){return f(function(t,n,r,i,o){var s=t[n],l=m(s);if("object"!==l)return new p("Invalid "+i+" `"+o+"` of type `"+l+"` supplied to `"+r+"`, expected `object`.");for(var c in e){var d=e[c];if(d){var u=d(s,c,r,i,o+"."+c,a);if(u)return u}}return null})}};function u(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function p(e){this.message=e,this.stack=""}function f(e){if("production"!==t.env.NODE_ENV)var r={},s=0;function l(l,d,u,f,g,h,m){if(f=f||c,h=h||u,m!==a)if(n)i(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");else if("production"!==t.env.NODE_ENV&&"undefined"!=typeof console){var b=f+":"+u;!r[b]&&s<3&&(o(!1,"You are manually calling a React.PropTypes validation function for the `%s` prop on `%s`. This is deprecated and will throw in the standalone `prop-types` package. You may be seeing this warning due to a third-party PropTypes library. See https://fb.me/react-warning-dont-call-proptypes for details.",h,f),r[b]=!0,s++)}return null==d[u]?l?null===d[u]?new p("The "+g+" `"+h+"` is marked as required in `"+f+"`, but its value is `null`."):new p("The "+g+" `"+h+"` is marked as required in `"+f+"`, but its value is `undefined`."):null:e(d,u,f,g,h)}var d=l.bind(null,!1);return d.isRequired=l.bind(null,!0),d}function g(e){return f(function(t,n,r,i,o,a){var s=t[n];return m(s)!==e?new p("Invalid "+i+" `"+o+"` of type `"+b(s)+"` supplied to `"+r+"`, expected `"+e+"`."):null})}function h(t){switch(typeof t){case"number":case"string":case"undefined":return!0;case"boolean":return!t;case"object":if(Array.isArray(t))return t.every(h);if(null===t||e(t))return!0;var n=function(e){var t=e&&(l&&e[l]||e["@@iterator"]);if("function"==typeof t)return t}(t);if(!n)return!1;var r,i=n.call(t);if(n!==t.entries){for(;!(r=i.next()).done;)if(!h(r.value))return!1}else for(;!(r=i.next()).done;){var o=r.value;if(o&&!h(o[1]))return!1}return!0;default:return!1}}function m(e){var t=typeof e;return Array.isArray(e)?"array":e instanceof RegExp?"object":function(e,t){return"symbol"===e||"Symbol"===t["@@toStringTag"]||"function"==typeof Symbol&&t instanceof Symbol}(t,e)?"symbol":t}function b(e){if(null==e)return""+e;var t=m(e);if("object"===t){if(e instanceof Date)return"date";if(e instanceof RegExp)return"regexp"}return t}function v(e){var t=b(e);switch(t){case"array":case"object":return"an "+t;case"boolean":case"date":case"regexp":return"a "+t;default:return t}}return p.prototype=Error.prototype,d.checkPropTypes=s,d.PropTypes=d,d}}).call(t,n(0))}])},e.exports=r(n(5959))},5540:(e,t,n)=>{"use strict";n.d(t,{w:()=>i});var r=n(5959);function i(e){const{ref:t,box:n,onResize:i}=e;(0,r.useEffect)(()=>{let e=null==t?void 0:t.current;if(e){if(void 0===window.ResizeObserver)return window.addEventListener("resize",i,!1),()=>{window.removeEventListener("resize",i,!1)};{const t=new window.ResizeObserver(e=>{e.length&&i()});return t.observe(e,{box:n}),()=>{e&&t.unobserve(e)}}}},[i,t,n])}}}]);
//# sourceMappingURL=864.js.map?_cache=c7042e4fc7e1fc7aad94