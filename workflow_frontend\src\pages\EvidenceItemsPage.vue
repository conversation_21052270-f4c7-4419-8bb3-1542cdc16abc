<template>
  <q-page style="padding: 10px;">
    <div class="q-pa-sm">
      <q-breadcrumbs separator="/" style="color: grey; font-size: 13px;">
        <q-breadcrumbs-el label="Fälle" to="/cases" icon="home" :exact="true" clickable style="color: grey;"/>
        <q-breadcrumbs-el :label="caseName" icon="folder_open"/>
      </q-breadcrumbs>
    </div>

    <div style="padding: 30px;">
      <div class="row"
           style="justify-content: space-between; align-items: center; padding-bottom: 20px; padding-top: 20px;">
        <div style="font-size: 30px; font-weight: 500;">{{ caseName }}</div>

        <div style="font-size: 22px; font-weight: 500;">
          Fortschritt: {{ caseProgress }} %
          <CaseProgressIndicator :progress-value="caseProgress" size="50px"/>
        </div>
      </div>

      <q-card class="q-pa-md" style="margin-bottom: 15px;">
        <div class="row" style="font-size: 14px;">
          <span style="font-weight: 500;">Auftraggeber: <span
              style="font-weight: 400; margin-left: 5px;">{{ caseClient }}</span></span>
          <span style="font-weight: 500; margin-left: 40px;"> Aktenzeichen: <span
              style="font-weight: 400; margin-left: 5px;"> {{ caseReferenceNumber }}</span></span>
        </div>
      </q-card>


      <q-card class="q-pa-md">

        <!-- Button for entering a new evidence item -->
        <div class="row tableTopBtnRow no-wrap">
          <q-btn class="tableTopBtn" icon="add" label="Asservat hinzufügen" @click="showAddEvidenceDialog = true"/>
          <q-btn class="tableTopBtn" icon="refresh" label="Aktualisieren" @click="fetchAllDataFromAPI"/>
        </div>

        <!-- Popup Dialog for Adding Evidence Item -->
        <q-dialog v-model="showAddEvidenceDialog">
          <q-card style="width: 500px">
            <q-card-section>
              <div class="text-h6">Neues Asservat hinzufügen</div>
            </q-card-section>

            <q-card-section>
              <q-input v-model="newEvidenceItemName" label="Asservatname" filled/>
            </q-card-section>

            <q-card-actions align="right">
              <q-btn flat label="Abbrechen" color="grey-8" @click="showAddEvidenceDialog = false"/>
              <q-btn flat label="Hinzufügen" color="primary" @click="addEvidenceItem"/>
            </q-card-actions>
          </q-card>
        </q-dialog>

        <q-table
            :rows="evidenceItemsList"
            :columns="evidenceItemColumns"
            row-key="id"
            no-data-label="Keine Asservate gefunden."
            :rows-per-page-options="[0, 10, 25, 50, 100]"
        >
          <template v-slot:body-cell-evidence_item="props">
            <q-td :props="props">
              {{ props.row.evidence_item_name }}
              <span v-if="props.row.device_size"> [{{ formatDeviceSize(props.row.device_size) }}]</span>
              <q-tooltip anchor="center start" self="bottom left">Asservat ID {{ props.row.id }}</q-tooltip>
            </q-td>
          </template>
          <template v-slot:body-cell-step_0="props">
            <StepTableCell v-if="props.row.step_0.length !== 0" :processes="props.row.step_0" :table-props="props"/>
            <!--          show button to finished process only if there is already an image station process-->
            <q-td v-else-if="props.row.step_1.length !== 0" :props="props">
              <AddProcessBtn btn-label="Als fertig markieren"
                             :evidence-item-id="props.row.id"
                             :step-id="0" :process-status="2"/>
            </q-td>
            <q-td v-else></q-td>
          </template>
          <template v-slot:body-cell-step_1="props">
            <StepTableCell :processes="props.row.step_1" :table-props="props"/>
          </template>
          <template v-slot:body-cell-step_2="props">
            <StepTableCell :processes="props.row.step_2" :table-props="props"/>
          </template>
          <template v-slot:body-cell-step_3="props">
            <StepTableCell :processes="props.row.step_3" :table-props="props"/>
          </template>
          <template v-slot:body-cell-step_4="props">
            <StepTableCell v-if="props.row.step_4.length !== 0" :processes="props.row.step_4" :table-props="props"/>
            <q-td v-else :props="props">
              <AddProcessBtn btn-label="Als fertig markieren"
                             :evidence-item-id="props.row.id"
                             :step-id="4" :process-status="2"/>
            </q-td>
          </template>

          <!-- New Action Column -->
          <template v-slot:body-cell-actions="props">
            <q-td class="text-center actions-cell">
              <div class="action-btn-wrapper">
                <q-btn
                    dense
                    flat
                    round
                    :color="props.row.pinned ? 'red' : 'grey-8'"
                    :icon="props.row.pinned ? 'push_pin' : 'push_pin'"
                    @click="togglePin(props.row)"
                >
                  <q-tooltip>{{ props.row.pinned ? 'Anpinnen' : 'Pin entfernen' }}</q-tooltip>
                </q-btn>
              </div>
            </q-td>
          </template>
        </q-table>

      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import {ref, onMounted, onBeforeMount, computed} from 'vue';
import {Notify} from 'quasar';
import {useRoute} from "vue-router";
import StepTableCell from "components/StepTableCell.vue";
import AddProcessBtn from "components/AddProcessBtn.vue";
import CaseProgressIndicator from "components/CaseProgressIndicator.vue";
import {formatDeviceSize} from "src/utils/processes_utils.js";

const forensicStepsList = ref([]);
const evidenceItemColumns = ref([]);

const caseId = ref(-1);
const caseName = ref('');
const caseClient = ref('');
const caseReferenceNumber = ref('');
const caseProgress = ref(0);
const evidenceItemsList = ref([]);
const showAddEvidenceDialog = ref(false);
const newEvidenceItemName = ref('');

const workflowApiUrl = computed(() => {
  let url = new URL(window.location.toString());
  url.port = '8000';
  url.hash = ''
  url.pathname = '/'
  // remove ending '/' (appending path segments later would fail otherwise)
  return url.toString().replace(/\/$/, '');
});

async function fetchCaseProperties() {
  try {
    // fetch evidence items
    const caseUrl = `${workflowApiUrl.value}/cases/${caseId.value}`;
    console.log("GET " + caseUrl);
    const responseCase = await fetch(caseUrl, {method: 'GET'});
    if (!responseCase.ok) throw new Error(`Failed to fetch case with id ${caseId.value}.`);

    const caseData = await responseCase.json();
    caseName.value = caseData.case_name
    caseClient.value = caseData.client
    caseReferenceNumber.value = caseData.reference_number

  } catch (err) {
    console.log(err)
    Notify.create({message: `Fehler beim Abrufen von Fall ${caseId.value}`, type: 'negative', timeout: 10000});
  }
}

async function fetchEvidenceItemsAndProcesses() {
  try {
    // fetch evidence items
    const evidenceItemsUrl = `${workflowApiUrl.value}/evidence-items?case_id=${caseId.value}`;
    console.log("GET " + evidenceItemsUrl);
    const responseEvidenceItems = await fetch(evidenceItemsUrl, {method: 'GET'});
    if (!responseEvidenceItems.ok) throw new Error('Failed to fetch evidence items');

    const evidenceItemsResponse = await responseEvidenceItems.json();

    // collect evidence item ids for fetching corresponding processes
    const evidenceItemsByIds = {};
    evidenceItemsResponse.forEach(item => {
      evidenceItemsByIds[item.id] = item;
      // init empty processes list for each step
      forensicStepsList.value.forEach(step => {
        const step_key = "step_" + step["id"].toString()
        evidenceItemsByIds[item.id][step_key] = []
      })
    })
    if (Object.keys(evidenceItemsByIds).length === 0) {
      evidenceItemsList.value = [];
      return;
    }

    // fetch processes
    const processesUrl = `${workflowApiUrl.value}/processes?evidence_item_ids=${Object.keys(evidenceItemsByIds).join('&evidence_item_ids=')}`
    console.log("GET " + processesUrl)
    const responseProcesses = await fetch(processesUrl, {method: 'GET'});
    if (responseProcesses.status === 404) {
      evidenceItemsList.value = [];
      return;
    } else if (!responseProcesses.ok) throw new Error('Failed to fetch processes.');

    const processes = await responseProcesses.json();
    // Add processes to corresponding evidence items
    processes.forEach(process => {
      const step_key = "step_" + process.step_id.toString()
      evidenceItemsByIds[process.evidence_item_id][step_key].push(process);
    });
    evidenceItemsList.value = Object.values(evidenceItemsByIds)

  } catch (err) {
    console.log(err)
    Notify.create({message: 'Fehler beim Abrufen der Asservate und Prozesse', type: 'negative', timeout: 10000});
  }
}

async function fetchForensicSteps() {
  try {
    // fetch evidence items
    const forensicStepsUrl = `${workflowApiUrl.value}/forensic-steps`;
    console.log("GET " + forensicStepsUrl);
    const responseForensicSteps = await fetch(forensicStepsUrl, {method: 'GET'});
    if (!responseForensicSteps.ok) throw new Error('Failed to fetch forensic steps.');

    forensicStepsList.value = await responseForensicSteps.json();

  } catch (err) {
    console.log(err)
    Notify.create({message: 'Fehler beim Abrufen der Schritte des ITF Workflows', type: 'negative', timeout: 10000});
  }
}

async function addEvidenceItem() {
  if (newEvidenceItemName.value.trim() === '') {
    Notify.create({message: 'Asservatname darf nicht leer sein.', type: 'warning'});
    return;
  }

  try {
    console.log("POST " + `${workflowApiUrl.value}/evidence-items`)
    const response = await fetch(`${workflowApiUrl.value}/evidence-items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        case_id: caseId.value,
        evidence_item_name: newEvidenceItemName.value,
      }),
    });

    if (!response.ok) throw new Error('Failed to add evidence item');

    Notify.create({message: 'Asservat erfolgreich hinzugefügt.', type: 'positive'});
    await fetchEvidenceItemsAndProcesses();
    showAddEvidenceDialog.value = false;
    newEvidenceItemName.value = '';

    await fetchAllDataFromAPI();
  } catch (err) {
    console.error(err);
    Notify.create({message: 'Fehler beim Hinzufügen des Asservats', type: 'negative'});
  }
}

const togglePin = (evidenceItem) => {
  evidenceItem.pinned = !evidenceItem.pinned;
  sortEvidenceItems();
  Notify.create({
    message: evidenceItem.pinned
        ? `Pinned evidence: ${evidenceItem.evidence_item_name}`
        : `Unpinned evidence: ${evidenceItem.evidence_item_name}`,
    type: evidenceItem.pinned ? 'positive' : 'info'
  });
};

const sortEvidenceItems = () => {
  evidenceItemsList.value = [...evidenceItemsList.value].sort((a, b) => {
    if (a.pinned === b.pinned) return 0;
    return a.pinned ? -1 : 1;
  });
};

const fetchCaseProgress = async function () {
  try {
    // fetch evidence items of this case
    const progressUrl = `${workflowApiUrl.value}/cases/${caseId.value}/progress`;
    console.log("GET " + progressUrl);
    const responseProgress = await fetch(progressUrl, {method: 'GET'});
    if (responseProgress.status === 404) return 0;
    else if (!responseProgress.ok) throw new Error(`Failed to fetch progress for case ${caseId}.`);

    const progressPercent = (await responseProgress.json())['progress_int_percent'];
    caseProgress.value = progressPercent;

  } catch (err) {
    console.log(err)
    Notify.create({message: 'Fehler beim Abrufen des Fortschritts von Fall ' + caseId.value, type: 'negative', timeout: 10000});
  }
}

onBeforeMount(async () => {
  // fetch step names (used as column headers)
  await fetchForensicSteps();
  // reset column header and add column "id" and "Asservatname"
  evidenceItemColumns.value = [];
  evidenceItemColumns.value.push({
    name: 'evidence_item', label: 'Asservat', field: 'evidence_item_name', align: 'left', sortable: true
  });
  // add column for each step
  forensicStepsList.value.forEach(step => {
        const column = {
          name: 'step_' + step["id"],
          label: step["display_name"],
          field: "step_" + step["id"],
          align: 'left',
          sortable: true,
          style: "width: 10%",
          headerStyle: "width: 10%; white-space: normal;"
        };
        evidenceItemColumns.value.push(column)
      }
  );
  //   add column "actions"
  // evidenceItemColumns.value.push({ name: 'actions', label: 'Aktionen', align: 'center', sortable: false});
})

const fetchAllDataFromAPI = async function () {
  // fetch evidence items
  if (caseId.value === -1) caseId.value = useRoute().query.caseId;
  await fetchCaseProperties();
  await fetchCaseProgress();
  await fetchEvidenceItemsAndProcesses();
};

onMounted(fetchAllDataFromAPI);
</script>

<style scoped>

.q-btn {
  text-transform: none;
}

.q-table {
  margin-top: 20px;
  width: 100%;
}

.tableTopBtnRow {
  margin: 0px 0px 15px 0px;

  .q-btn {
    float: right;
    width: auto;
    height: 38px;
    background-color: #be1717;
    color: white;
    margin-right: 5px;
  }
}

.actions-cell .q-btn {
  margin-right: 8px;
}

.action-btn-wrapper {
  display: flex;
  justify-content: center;
}
</style>
