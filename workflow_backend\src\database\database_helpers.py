import datetime
import logging

from fastapi import HTT<PERSON>Exception
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from workflow_backend.src.Define import ProcessStatus
from workflow_backend.src.database.database import engine
from workflow_backend.src.models.process_data_models import XwaysDuResultData
from workflow_backend.src.models.processes_models import AddProcessPayload, UpdateProcessPayload


def add_new_process_to_database(payload: AddProcessPayload):
    # automatically add start or end timestamp if applicable
    started_running_at = None
    ended_at = None
    if payload.status == ProcessStatus.RUNNING:
        started_running_at = datetime.datetime.now()
    if payload.status in [ProcessStatus.FINISHED, ProcessStatus.FAILED]:
        ended_at = datetime.datetime.now()

    if isinstance(payload.result_data, XwaysDuResultData):
        result_data_json = payload.result_data.model_dump_json()
    else:
        result_data_json = payload.result_data
    # TODO: include step_status field
    create_query = """INSERT INTO processes (evidence_item_id, step_id, status, updated_at, input_data, result_data, prioritize, created_at, started_running_at, ended_at) 
                        VALUES (:evidence_item_id, :step_id, :status, now(), :input_data, :result_data, :prioritize, NOW(), :started_running_at, :ended_at)
                        RETURNING *;"""
    create_result = run_query(create_query,
                              {"evidence_item_id":   payload.evidence_item_id,
                               'step_id':            payload.step_id,
                               'status':             payload.status,
                               'input_data':         payload.input_data,
                               'result_data':        result_data_json,
                               'prioritize':         payload.prioritize,
                               'started_running_at': started_running_at,
                               'ended_at':           ended_at})
    logging.log(logging.INFO, f"Created new process: {create_result}")

    return create_result


def query_processes_table(step_id, evidence_item_id, status):
    query = """SELECT * FROM processes 
               WHERE step_id = :step_id and evidence_item_id = :evidence_item_id and status = :status"""
    steps_result = run_query(query, {"step_id": step_id,
                                     "evidence_item_id": evidence_item_id,
                                     "status": status})
    return steps_result


def update_process_in_database(process_id: int, payload: UpdateProcessPayload):
    started_running_at = None
    ended_at = None

    # select process with old values to check if process start or end timestamp can be set automatically
    result = run_query("SELECT * FROM processes WHERE id = :process_id;",
                       {"process_id": process_id})
    # if process with process_id does not exit, return None
    if result is None or len(result) == 0:
        return None
    # if process status changed
    if payload.status is not None and result[0]["status"] != payload.status:
        # set process start timestamp automatically if new status is FINISHED
        if payload.status == ProcessStatus.RUNNING:
            started_running_at = datetime.datetime.now()
        # set process end timestamp automatically if new status is FINISHED or FAILED
        if payload.status in [ProcessStatus.FINISHED, ProcessStatus.FAILED]:
            ended_at = datetime.datetime.now()

    if isinstance(payload.result_data, XwaysDuResultData):
        result_data_json = payload.result_data.model_dump_json()
    else:
        result_data_json = payload.result_data

    # update process
    # TODO: include step_status field
    update_query = """
        UPDATE processes
        SET updated_at = now() {0}{1}{2}{3}{4}
        WHERE id = :process_id
        RETURNING *;
    """.format(
        ", status = :status" if payload.status is not None else "",
        ", result_data = :data" if result_data_json is not None else "",
               ", started_running_at = :started_running_at" if started_running_at is not None else "",
               ", ended_at = :ended_at" if ended_at is not None else "",
               ", evidence_item_id = :evidence_item_id" if payload.evidence_item_id is not None else "",)
    updated_process = run_query(update_query, {
        "process_id": process_id,
        "status": payload.status,
        "data": result_data_json,
        "started_running_at": started_running_at,
        "ended_at": ended_at,
        "evidence_item_id": payload.evidence_item_id
    })
    return updated_process


def run_query(query, param_dict=None):
    try:
        with engine.connect() as connection:
            if not query.startswith("SELECT") and not query.startswith("with processes_of_this_case"):
                logging.log(logging.DEBUG, f"Run query: {query.replace("\n", "")} - with params: {str(param_dict)}")
            query = text(query)
            result = connection.execute(query, param_dict)
            rows = [dict(row._mapping) for row in result]
            connection.commit()
            return rows
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_unassigned_processes_from_database(date_threshold: datetime.datetime | None):
    query = """
            SELECT processes.id AS process_id,
                   processes.evidence_item_id,
                   processes.step_id,
                   processes.status,
                   processes.updated_at,
                   processes.input_data,
                   processes.result_data,
                   processes.prioritize,
                   processes.created_at,
                   processes.started_running_at,
                   processes.ended_at,
                   evidence_items.id AS evidence_item_id,
                   evidence_items.evidence_item_name,
                   cases.id AS case_id,
                   cases.case_name
            FROM processes
                     LEFT JOIN evidence_items ON processes.evidence_item_id = evidence_items.id
                     LEFT JOIN cases ON evidence_items.case_id = cases.id
            WHERE (processes.evidence_item_id IS NULL OR evidence_items.case_id IS NULL)
                AND processes.updated_at >= :date_threshold
            ORDER BY processes.updated_at DESC; \
            """
    if date_threshold is None:
        date_threshold = datetime.date(1970,1,1)
    result = run_query(query, {"date_threshold": date_threshold})
    return result
