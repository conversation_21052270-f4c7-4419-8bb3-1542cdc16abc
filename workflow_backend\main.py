import os
import sys

from fastapi.middleware.cors import CORSMiddleware
from fastapi_offline import FastAPIOffline


sys.path.append(os.path.abspath(os.path.dirname(__file__)))

from src.routers.forensic_steps_endpoints import forensic_steps_router
from src.routers.processes_endpoints import processes_router
from src.routers.case_endpoints import case_router
from src.routers.evidence_items_endpoints import evidence_items_router
from src.routers.process_logs_endpoints import process_logs_router


app = FastAPIOffline()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(case_router)
app.include_router(evidence_items_router)
app.include_router(processes_router)
app.include_router(forensic_steps_router)
app.include_router(process_logs_router)

