{"version": 3, "file": "70.js", "mappings": "kKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,4CACxB,iBAAkB,8BAClB,yBAA0B,4CAE3B,yBAA0B,CACzB,sBAAuB,yCACvB,mBAAoB,oCAErB,gBAAiB,CAChBC,QAAS,8EACTC,SAAU,iCACVC,MAAO,cAER,wBAAyB,CACxB,wBAAyB,iBACzB,sBAAuB,mBACvB,sBAAuB,kBAExB,iBAAkB,CACjB,iBAAkB,oBAClB,cAAe,gBACf,uBAAwB,yBAEzB,iBAAkB,CACjB,eAAgB,gBAChB,aAAc,mBAEf,oCAAqC,CACpC,eAAgB,gBAChB,iBAAkB,8CAEnBC,SAAU,CACT,gCAAiC,wCAElC,YAAa,CACZD,MAAO,CACNA,MAAO,WAGT,2BAA4B,CAC3BE,QAAS,UAEV,qBAAsB,CACrB,uBAAwB,+BACxB,sCAAuC,yCAExC,yBAA0B,CACzB,2DAA4D,+GAC5D,kBAAmB,iDAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,UAEnB,oBAAqB,CACpB,uBAAwB,qBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,2BAEpC,wBAAyB,CACxB,wBAAyB,cACzB,mBAAoB,eAErB,yBAA0B,CACzB,2BAA4B,kBAC5B,aAAc,CACb,2BAA4B,gBAE7B,qBAAsB,eACtB,sBAAuB,kBACvB,eAAgB,CACf,2BAA4B,gBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,eAGb,8CAA+C,CAC9C,mBAAoB,UACpBC,QAAS,8EACT,gDAAiD,iEAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,6BACzB,uBAAwB,gCACxB,gCAAiC,yCACjC,oDAAqD,+FACrD,0BAA2B,uDAC3B,uBAAwB,wBACxB,mBAAoB,yBACpB,mDAAoD,+DACpD,uBAAwB,+BACxB,kDAAmD,uIACnD,iCAAkC,wFAClC,oCAAqC,uCAIxC,6BAA8B,CAC7B,+BAAgC,uBAChC,6BAA8B,uBAE/B,oBAAqB,CACpB,2BAA4B,eAE7B,8BAA+B,CAC9B,kBAAmB,4BAEpB,2BAA4B,CAC3BC,MAAO,UAER,yBAA0B,CACzB,mBAAoB,sBAErB,4BAA6B,CAC5B,6CAA8C,sEAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,YAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,UAGR,uBAAwB,CACvB,0BAA2B,eAE5B,wBAAyB,CACxB,2BAA4B,iB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/tr-TR/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Filtreyi {{keyLabel}} anahtar<PERSON>yla düzenle\",\n\t\t\t\"managed-filter\": \"{{origin}} y<PERSON><PERSON><PERSON><PERSON> filtre\",\n\t\t\t\"remove-filter-with-key\": \"Filtreyi {{keyLabel}} anahtar<PERSON>yla kaldır\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Filtre değerini kaldır - {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"<PERSON><PERSON> değ<PERSON> kullan: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Buraya bir bağlantı aracılığıyla ulaştıysanız uygulamada bir hata olabilir.\",\n\t\t\tsubTitle: \"URL hiçbir sayfayla eşleşmedi.\",\n\t\t\ttitle: \"Bulunamadı\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"<PERSON>hney<PERSON> daralt\",\n\t\t\t\"expand-button-label\": \"Sahneyi genişlet\",\n\t\t\t\"remove-button-label\": \"Sahneyi kaldır\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Nesne ayrıntıları\",\n\t\t\t\"scene-graph\": \"Sahne grafiği\",\n\t\t\t\"title-scene-debugger\": \"Sahne hata ayıklayıcı\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Satırı daralt\",\n\t\t\t\"expand-row\": \"Satırı genişlet\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Karşılaştırma\",\n\t\t\t\"button-tooltip\": \"Zaman dilimi karşılaştırmasını etkinleştir\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Bölme yeniden boyutlandırma widget'ı\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Başlık\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Keşfet\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Eklenti paneli yükleniyor...\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"Panel eklentisinde panel bileşeni yok\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Tek bir panelde çok fazla seri işlenmesi, performansı etkileyebilir ve verilerin okunmasını zorlaştırabilir.\",\n\t\t\t\"warning-message\": \"Sadece {{seriesLimit}} serileri gösteriliyor\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Kaldır\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Sorguyu iptal et\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Filtre işlecini düzenle\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Filtre ekle\",\n\t\t\t\"title-add-filter\": \"Filtre ekle\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Filtreyi kaldır\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Etiket seçin\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Etiket seçin\",\n\t\t\t\"title-remove-filter\": \"Filtreyi kaldır\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Değer seçin\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"varsayılan\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"temizle\",\n\t\t\ttooltip: \"Bu panoda varsayılan olarak uygulanır. Düzenlenirse diğer panolara taşınır.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Bu pano tarafından ayarlanmış groupby kümesini geri yükleyin.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Virgülle ayrılmış değerler\",\n\t\t\t\t\t\"double-quoted-values\": \"Çift tırnak içindeki değerler\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Tarihi farklı şekillerde biçimlendirin\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Çok değerli değişkenleri glob söz dizimi kullanarak biçimlendirin (örneğin {value1,value2}).\",\n\t\t\t\t\t\"html-escaping-of-values\": \"Değerlerin HTML kaçış karakteriyle yazılması gerekir\",\n\t\t\t\t\t\"json-stringify-value\": \"JSON stringify değeri\",\n\t\t\t\t\t\"keep-value-as-is\": \"Değeri olduğu gibi tut\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Birden fazla değer, değişken=değer biçiminde biçimlendirilir\",\n\t\t\t\t\t\"single-quoted-values\": \"Tek tırnak içindeki değerler\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"URL'ye uygun hâle getirmek için değerlerin kaçış karakteriyle yazılmasında kullanılır; URI söz dizimindeki karakterleri dikkate alır\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"URL'ye uygun hâle getirmek için değerlerin kaçış karakteriyle yazılmasında kullanılır\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Değerler \\\"|\\\" karakteriyle ayrılır\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Seçiciye göre grupla\",\n\t\t\t\"placeholder-group-by-label\": \"Etikete göre grupla\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Değer seçin\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Seçenekler yükleniyor...\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Uygula\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Seçenek bulunamadı\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Etiketler alınırken bir hata oluştu. Yeniden denemek için tıklayın\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Merhaba\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Metin\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Değer girin\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Değer seçin\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}