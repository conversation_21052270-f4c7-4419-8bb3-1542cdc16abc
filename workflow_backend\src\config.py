import os
from dotenv import load_dotenv

load_dotenv(override=True)


DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")
DB_HOST = os.getenv("DB_HOST", "workflow_database")
DB_PORT = os.getenv("DB_PORT", "5432")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"


MAX_FOCUS_INSTANCES = int(os.getenv("MAX_FOCUS_INSTANCES", 3)) # default is 3