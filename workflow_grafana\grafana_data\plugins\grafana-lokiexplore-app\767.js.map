{"version": 3, "file": "767.js?_cache=ca8114e32d5cb06e2cdd", "mappings": "+JAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,oCACxB,iBAAkB,4BAClB,yBAA0B,uCAE3B,yBAA0B,CACzB,sBAAuB,sCACvB,mBAAoB,mCAErB,gBAAiB,CAChBC,QAAS,yFACTC,SAAU,iCACVC,MAAO,aAER,wBAAyB,CACxB,wBAAyB,iBACzB,sBAAuB,eACvB,sBAAuB,gBAExB,iBAAkB,CACjB,iBAAkB,iBAClB,cAAe,cACf,uBAAwB,kBAEzB,iBAAkB,CACjB,eAAgB,eAChB,aAAc,cAEf,oCAAqC,CACpC,eAAgB,aAChB,iBAAkB,gCAEnBC,SAAU,CACT,gCAAiC,sBAElC,YAAa,CACZD,MAAO,CACNA,MAAO,UAGT,2BAA4B,CAC3BE,QAAS,WAEV,qBAAsB,CACrB,uBAAwB,0BACxB,sCAAuC,uCAExC,yBAA0B,CACzB,2DAA4D,mGAC5D,kBAAmB,wCAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,UAEnB,oBAAqB,CACpB,uBAAwB,iBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,wBAEpC,wBAAyB,CACxB,wBAAyB,aACzB,mBAAoB,cAErB,yBAA0B,CACzB,2BAA4B,gBAC5B,aAAc,CACb,2BAA4B,gBAE7B,qBAAsB,eACtB,sBAAuB,gBACvB,eAAgB,CACf,2BAA4B,iBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,YAGb,8CAA+C,CAC9C,mBAAoB,QACpBC,QAAS,wFACT,gDAAiD,0CAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,yBACzB,uBAAwB,uBACxB,gCAAiC,gCACjC,oDAAqD,2EACrD,0BAA2B,0BAC3B,uBAAwB,uBACxB,mBAAoB,mBACpB,mDAAoD,oDACpD,uBAAwB,uBACxB,kDAAmD,oEACnD,iCAAkC,iCAClC,oCAAqC,yCAIxC,6BAA8B,CAC7B,+BAAgC,oBAChC,6BAA8B,kBAE/B,oBAAqB,CACpB,2BAA4B,gBAE7B,8BAA+B,CAC9B,kBAAmB,sBAEpB,2BAA4B,CAC3BC,MAAO,SAER,yBAA0B,CACzB,mBAAoB,oBAErB,4BAA6B,CAC5B,6CAA8C,yDAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,UAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,SAGR,uBAAwB,CACvB,0BAA2B,eAE5B,wBAAyB,CACxB,2BAA4B,kB", "sources": ["webpack://grafana-lokiexplore-app/../node_modules/@grafana/scenes/dist/esm/locales/en-US/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Edit filter with key {{keyLabel}}\",\n\t\t\t\"managed-filter\": \"{{origin}} managed filter\",\n\t\t\t\"remove-filter-with-key\": \"Remove filter with key {{keyLabel}}\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Remove filter value - {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Use custom value: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"If you found your way here using a link then there might be a bug in this application.\",\n\t\t\tsubTitle: \"The url did not match any page\",\n\t\t\ttitle: \"Not found\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"Collapse scene\",\n\t\t\t\"expand-button-label\": \"Expand scene\",\n\t\t\t\"remove-button-label\": \"Remove scene\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Object details\",\n\t\t\t\"scene-graph\": \"Scene graph\",\n\t\t\t\"title-scene-debugger\": \"Scene debugger\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Collapse row\",\n\t\t\t\"expand-row\": \"Expand row\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Comparison\",\n\t\t\t\"button-tooltip\": \"Enable time frame comparison\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Pane resize widget\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Title\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Explore\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Loading plugin panel...\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"Panel plugin has no panel component\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Rendering too many series in a single panel may impact performance and make data harder to read.\",\n\t\t\t\"warning-message\": \"Showing only {{seriesLimit}} series\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Remove\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Cancel query\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Edit filter operator\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Add filter\",\n\t\t\t\"title-add-filter\": \"Add filter\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Remove filter\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Select label\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Select label\",\n\t\t\t\"title-remove-filter\": \"Remove filter\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Select value\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"default\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"clear\",\n\t\t\ttooltip: \"Applied by default in this dashboard. If edited, it carries over to other dashboards.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Restore groupby set by this dashboard.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Comma-separated values\",\n\t\t\t\t\t\"double-quoted-values\": \"Double quoted values\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Format date in different ways\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Format multi-valued variables using glob syntax, example {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"HTML escaping of values\",\n\t\t\t\t\t\"json-stringify-value\": \"JSON stringify value\",\n\t\t\t\t\t\"keep-value-as-is\": \"Keep value as is\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Multiple values are formatted like variable=value\",\n\t\t\t\t\t\"single-quoted-values\": \"Single quoted values\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Useful for URL escaping values, taking into URI syntax characters\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Useful for URL escaping values\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Values are separated by | character\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Group by selector\",\n\t\t\t\"placeholder-group-by-label\": \"Group by label\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Select value\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Loading options...\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Apply\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"No options found\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"An error has occurred fetching labels. Click to retry\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Hello\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Text\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Enter value\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Select value\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}