import json
import logging
from pathlib import Path

from .Define import NEXT_STEP_AFTER, ProcessStatus, Step
from workflow_backend.src.database.database_helpers import run_query, query_processes_table, add_new_process_to_database
from workflow_backend.src.models.processes_models import AddProcessPayload


def queue_next_step_if_requirements_fulfilled(evidence_item_id, finished_step_id):
    if evidence_item_id is None:
        logging.warning("No evidence item id given. Requirements for the step cannot be checked if the process is not assigned to an evidence item.")
        return

    # if there is a step that should automatically be queued next
    if finished_step_id in NEXT_STEP_AFTER:
        next_step_id = NEXT_STEP_AFTER[finished_step_id]

        # Check if the requirements for next step are fulfilled
        if is_step_ready_to_be_queued(next_step_id, evidence_item_id):
            queue_new_process(next_step_id, evidence_item_id)


def is_step_ready_to_be_queued(step_id, evidence_item_id) -> bool:
    logging.log(logging.DEBUG, f"Checking if requirements for step {step_id} are fulfilled...")

    # ensure that this step is not already queued, running, finished or canceled for this evidence item
    logging.log(logging.INFO,f"Checking that there is no other process already queued, running, finished, canceled or assigned for step {step_id} and evidence item {evidence_item_id} ...")
    query3 = f"""SELECT id FROM processes 
                 WHERE step_id = :step_id and evidence_item_id = :evidence_item_id 
                 and status in ({ProcessStatus.QUEUED},{ProcessStatus.RUNNING},{ProcessStatus.FINISHED},{ProcessStatus.CANCELED},{ProcessStatus.ASSIGNED})"""
    result = run_query(query3, {"step_id": step_id, "evidence_item_id": evidence_item_id})

    # If there is no process for this step and evidence_item_id, yet
    if len(result) == 0:
        logging.log(logging.DEBUG,"There is no process already queued, running, finished, canceled or assigned for this step and evidence item.")

        # check other requirements
        requirements_fulfilled = False
        if step_id == Step.XWAYS_DU: # for XWaysDÜ
            requirements_fulfilled = is_xwaysdu_requirements_fulfilled(evidence_item_id)
        elif step_id == Step.FOCUS_AI: # for FOCUS.AI
            requirements_fulfilled = is_focusai_requirements_fulfilled(evidence_item_id)
        return requirements_fulfilled
    else:
        logging.log(logging.WARNING,"Process for this step and this evidence item already exists:" + str(result))
    return False


def is_xwaysdu_requirements_fulfilled(evidence_item_id):
    # Is Image Station finished for this evidence_item?
    finished_imagestation_processes = query_processes_table(Step.IMAGE_STATION, evidence_item_id, ProcessStatus.FINISHED)
    imagestation_step_finished = len(finished_imagestation_processes) > 0
    if imagestation_step_finished:
        logging.log(logging.INFO, f"ImageStation step finished for evidence item {evidence_item_id}. XwaysDÜ can be run.")
    else:
        logging.log(logging.INFO, f"XwaysDÜ requires ImageStation step to be run for {evidence_item_id}, first.")

    return imagestation_step_finished


def is_focusai_requirements_fulfilled(evidence_item_id):
    # Is Image Station for this evidence_item?
    finished_imagestation_processes = query_processes_table(Step.IMAGE_STATION, evidence_item_id, ProcessStatus.FINISHED)
    imagestation_step_finished = len(finished_imagestation_processes) > 0
    if imagestation_step_finished:
        logging.log(logging.INFO, f"ImageStation step finished for evidence item {evidence_item_id}. FOCUS.AI can be run.")
    else:
        logging.log(logging.INFO, f"FOCUS.AI requires ImageStation step to be run for {evidence_item_id}, first.")

    return imagestation_step_finished


def queue_new_process(step_id, evidence_item_id):
    """
    Queue a new process for the step and the evidence item. Automatically determine the input data from previous processes.

    :param step_id: The id of the step
    :param evidence_item_id: The id of the evidence item
    :return: Boolean whether queuing was successful
    """

    input_data_json = ""
    queueing_failed = False

    # collect step-specific input data
    if step_id == Step.XWAYS_DU: # collect input data for XWAYS DÜ process
        logging.log(logging.INFO, f"Queuing new process for step 'XwaysDÜ' and evidence item {evidence_item_id}.")
        first_image_file_path = get_first_image_file_path_from_image_station_process(evidence_item_id)

        if first_image_file_path is None:
            queueing_failed = True
            logging.log(logging.ERROR,
                        f"Failed to queue new process for step {step_id} and evidence item {evidence_item_id}: first_image_file_path is None.")

        else:
            input_dict = {"first_image_file_path": first_image_file_path}
            input_data_json = json.dumps(input_dict, ensure_ascii=False)

    elif step_id == Step.FOCUS_AI: # collect input data for FOCUS.AI process
        logging.log(logging.INFO, f"Queuing new process for step 'FOCUS.AI' and evidence item {evidence_item_id}.")
        # get first image file path
        first_image_file_path = get_first_image_file_path_from_image_station_process(evidence_item_id)

        if first_image_file_path is not None:
            # get triage file path
            triage_file_path = get_triage_file_path(first_image_file_path)
            # get evidence item name
            query = "SELECT * FROM evidence_items WHERE id = :evidence_item_id;"
            evidence_item_result = run_query(query, {"evidence_item_id": evidence_item_id})

            if evidence_item_result is not None and len(evidence_item_result) == 1:
                input_dict = {"first_image_file_path": first_image_file_path,
                              "triage_file_path": triage_file_path,
                              "evidence_item_name": evidence_item_result[0]["evidence_item_name"]}
                input_data_json = json.dumps(input_dict, ensure_ascii=False)
            else:
                queueing_failed = True
                logging.log(logging.ERROR,
                            f"Failed to queue new process for step {step_id} and evidence item {evidence_item_id}: evidence_item_result is None or len(evidence_item_result) not 1.")
        else:
            queueing_failed = True
            logging.log(logging.ERROR, f"Failed to queue new process for step {step_id} and evidence item {evidence_item_id}: first_image_file_path is None.")
    else:
        logging.log(logging.INFO, f"Queuing new process for step {step_id} and evidence item {evidence_item_id}.")

    if queueing_failed == False:
        add_new_process_to_database(
            AddProcessPayload(
                evidence_item_id=evidence_item_id,
                step_id=step_id,
                status=ProcessStatus.QUEUED,
                input_data=input_data_json,
                result_data=""
            )
        )

    return not queueing_failed


def get_first_image_file_path_from_image_station_process(evidence_item_id):
    """
    Query the database for the image station process containing the needed result data.
    Construct the first image file path by combining image_folder_path, image_name and image_file_extension
    :param evidence_item_id: The id of the evidence item to query the image station process for
    :return: The path to the first image file if successful, None otherwise.
    """
    try:
        # get corresponding finished image station process
        finished_image_station_process = query_processes_table(Step.IMAGE_STATION, evidence_item_id, ProcessStatus.FINISHED)
        # convert from list to dict (get first element in list)
        finished_image_station_process = finished_image_station_process[0]
        # if finished image station process contains result data
        if finished_image_station_process["result_data"] is not None and finished_image_station_process["result_data"] != "":
            # load result data from image station process
            image_station_result_data = json.loads(finished_image_station_process["result_data"])

            # if image station result contains all necessary fields, to queue next process
            if ("image_folder_path" in image_station_result_data and "image_file_extension" in image_station_result_data
                    and "image_name" in image_station_result_data):

                first_image_file = image_station_result_data["image_name"] + image_station_result_data[
                    "image_file_extension"]
                first_image_file_path = Path(image_station_result_data["image_folder_path"].removesuffix('\n'), first_image_file)

                logging.info(f"Constructed first image file path: {first_image_file_path}")
                return str(first_image_file_path)
            else:
                logging.error(f"Did not find 'image_folder_path', 'image_file_extension' and/or 'image_name' in result_data of process {finished_image_station_process["id"]}.")
                return None
        else:
            logging.warning(f"Finished image station process (id {finished_image_station_process["id"]}) does not contain result_data.")
            return None
    except Exception as e:
        logging.error(f"Error getting first_image_file_path from image station process: {e}")
        return None


def get_triage_file_path(first_image_file_path):
    """
    Create the triage file path from the first_image_file_path.
    :param first_image_file_path:
    :return: The path of the triage file
    """
    # example: "path/to/LKA Börlin/AZ 666-666_Müller_Fried/Müller_Fried_Asservat_01/Müller_Fried_Asservat_01.E01"
    #           would return "path/to/LKA Börlin/AZ 666-666_Müller_Fried/AZ 666-666_Müller_Fried.triage"
    folder_path = Path(first_image_file_path).parent.parent
    file_name = Path(folder_path).stem + ".triage"
    return str(folder_path / file_name)
