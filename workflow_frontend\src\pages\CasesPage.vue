<template>

  <q-page>
    <div class="row"
         style="justify-content: space-between; align-items: center; padding-bottom: 30px; padding-top: 30px;">
      <div style="font-size: 30px; font-weight: 500;">Fallübersicht</div>
    </div>
    <q-card class="q-pa-md">
      <div class="row tableTopBtnRow no-wrap">
        <q-btn class="tableTopBtn" icon="add" label="Fall hinzufügen" @click="showAddCaseDialog = true"/>
        <q-btn class="tableTopBtn" icon="refresh" label="Aktualisieren" @click="fetchCases" />
      </div>

      <q-table
        :rows="filteredCases"
        row-key="id"
        :columns="caseColumns"
        no-data-label="Keine Fälle gefunden."
        @row-click="goToEvidenceItemsPage"
        dense flat bordered
        :rows-per-page-options="[0, 15, 50, 100]"
      >
       <!-- Custom Header Slot -->
      <template v-slot:header="props">
        <q-tr :props="props">
          <q-th v-for="col in props.cols" :key="col.name" :props="props" style="font-size: 13px">
            {{ col.label }}
          </q-th>
        </q-tr>
        <q-tr>
          <q-th v-for="col in props.cols" :key="col.name">
            <q-input v-if="col.name in filterDict"
                     class="tableHeaderSearchInput" debounce="300" borderless
                     v-model="filterDict[col.name]" placeholder="Suchen"
                     @update:model-value="filterCases">
              <template v-slot:append>
                <q-icon name="search"/>
              </template>
            </q-input>
          </q-th>
        </q-tr>
      </template>

        <template v-slot:body-cell-case_progress="props">
          <q-td style="text-align: right;" auto-width>
            <CaseProgressIndicator :progress-value="props.row.case_progress" size="30px"/>
            <q-tooltip>{{ props.row.case_progress }} %</q-tooltip>
          </q-td>
        </template>

        <template v-slot:body-cell-case_name="props">
          <q-td>
            <div>
              {{ props.row.case_name }}
              <q-tooltip>Fall ID {{ props.row.id }}</q-tooltip>
            </div>
          </q-td>
        </template>

        <template v-slot:body-cell-my_actions="props">
          <q-td class="text-center actions-cell">
            <div>
<!--              <q-btn-->
<!--                dense-->
<!--                flat-->
<!--                round-->
<!--                :color="props.row.pinned ? 'red' : 'grey-8'"-->
<!--                :icon="props.row.pinned ? 'push_pin' : 'push_pin'"-->
<!--                @click="togglePin(props.row)"-->
<!--              >-->
<!--                <q-tooltip>{{ props.row.pinned ? 'Anpinnen' : 'Pin entfernen' }}</q-tooltip>-->
<!--              </q-btn>-->

<!--               Archive Button-->
              <q-btn
                dense
                flat
                round
                color="grey-8"
                icon="archive"
                @click="deleteCase(props.row.id)"
              >
                <q-tooltip>Fall archivieren</q-tooltip>
              </q-btn>
            </div>
          </q-td>
        </template>
      </q-table>
    </q-card>

    <!-- Popup Dialog for Adding a New Case -->
    <q-dialog v-model="showAddCaseDialog">
      <q-card style="min-width: 400px">
        <q-card-section>
          <div class="text-h6">Neuen Fall anlegen</div>
        </q-card-section>

        <q-card-section>
          <q-input class="q-pa-xs" v-model="caseToAdd['name']" label="Fallname" outlined />
          <q-input class="q-pa-xs" v-model="caseToAdd['client']" label="Auftraggeber" outlined />
          <q-input class="q-pa-xs" v-model="caseToAdd['reference_number']" label="Aktenzeichen" outlined />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Abbrechen" color="grey-8" @click="showAddCaseDialog = false" />
          <q-btn flat label="Hinzufügen" color="primary" @click="addCase" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue';
import {Notify} from 'quasar';
import {useRouter} from "vue-router";
import CaseProgressIndicator from "src/components/CaseProgressIndicator.vue";

const router = useRouter();
const workflowApiUrl = computed(() => {
  let url = new URL(window.location.toString());
  url.port = '8000';
  url.hash = ''
  url.pathname = '/'
  // remove ending '/' (appending path segments later would fail otherwise)
  return url.toString().replace(/\/$/, '');
});

const cases = ref([]);
const filteredCases = ref([]);

const showAddCaseDialog = ref(false);
const caseToAdd = ref({});

const caseColumns = [
  {name: 'case_progress', label: 'Fortschritt', field: 'case_progress', align: 'center', sortable: true,
    style: "width: 5%", headerStyle: "width: 5%;"},
  {name: 'case_name', label: 'Fallname', field: 'case_name', align: 'left', sortable: true, style: "width: 30%", headerStyle: "width: 30%;"},
  {name: 'count_evidence_items', label: 'Anzahl Asservate', field: 'count_evidence_items', align: 'left', sortable: true,
    style: "width: 40px", headerStyle: "width: 40px;"},
  {name: 'client', label: 'Auftraggeber', field: 'client', align: 'left', sortable: true},
  {name: 'reference_number', label: 'Aktenzeichen', field: 'reference_number', align: 'left', sortable: true},
  {name: 'intake_at', label: 'Aufnahmezeitpunkt', field: 'intake_at', align: 'left', sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
  // {name: 'my_actions', label: 'Aktionen', align: 'center', sortable: false, headerStyle: "max-width: 70px; "}
];
const filterDict = ref({
  'case_name': "",
  'count_evidence_items': "",
  'client': "",
  'reference_number': "",
});

const fetchCases = async () => {
  try {
    console.log("GET " + workflowApiUrl.value + '/cases');
    const response = await fetch(workflowApiUrl.value + '/cases', {method: 'GET'});
    if (!response.ok) throw new Error('Failed to fetch cases');

    const data = await response.json();
    cases.value = data.map((item) => ({...item, pinned: false}));
    filteredCases.value = [...cases.value];

    // fetch count of evidence items and progress for each case
    for (const caseItem of cases.value) {
      const evidenceItems = await fetchEvidenceItemsForCase(caseItem.id);
      const countEvidenceItems = evidenceItems.length
      caseItem["count_evidence_items"] = countEvidenceItems;
      const case_progress = await fetchCaseProgress(caseItem.id);
      caseItem["case_progress"] = case_progress;
    }
  } catch (err) {
    console.log(err)
    Notify.create({message: 'Fehler beim Abrufen aller Fälle', type: 'negative', timeout: 10000});
  }
};

async function fetchEvidenceItemsForCase(case_id) {
  try {
    // fetch evidence items
    const evidenceItemsUrl = `${workflowApiUrl.value}/evidence-items?case_id=${case_id}`;
    console.log("GET " + evidenceItemsUrl);
    const responseEvidenceItems = await fetch(evidenceItemsUrl, {method: 'GET'});
    if (!responseEvidenceItems.ok) throw new Error(`Failed to fetch evidence items for case ${case_id}.`);

    const data = await responseEvidenceItems.json();
    return data;

  } catch (err) {
    console.log(err)
    Notify.create({message: 'Fehler beim Abrufen der Anzahl Asservate für Fall ' + case_id, type: 'negative', timeout: 10000});
  }
}

const filterCases = () => {
  filteredCases.value = cases.value.filter((caseItem) =>
      Object.keys(filterDict.value).every(key => {
        return caseItem[key].toString().toLowerCase().includes(filterDict.value[key].toLowerCase());
      })
  );
}

const togglePin = (caseData) => {
  caseData.pinned = !caseData.pinned;
  sortCases();
  Notify.create({
    message: caseData.pinned
      ? `Pinned case: ${caseData.case_name}`
      : `Unpinned case: ${caseData.case_name}`,
    type: caseData.pinned ? 'positive' : 'info'
  });
};

const sortCases = () => {
  filteredCases.value = [...cases.value].sort((a, b) => {
    if (a.pinned === b.pinned) return 0;
    return a.pinned ? -1 : 1;
  });
};

const addCase = async () => {
  if (caseToAdd.value['name'].trim() === '') {
    Notify.create({ message: 'Fallname darf nicht leer sein.', type: 'warning' });
    return;
  }
  if (caseToAdd.value['client'].trim() === '') {
    Notify.create({ message: 'Auftraggeber darf nicht leer sein.', type: 'warning' });
    return;
  }
  if (caseToAdd.value['reference_number'].trim() === '') {
    Notify.create({ message: 'Aktenzeichen darf nicht leer sein.', type: 'warning' });
    return;
  }

  try {
    const data = JSON.stringify({
      case_name: caseToAdd.value['name'],
      client: caseToAdd.value['client'],
      reference_number: caseToAdd.value['reference_number'],
      intake_time_in_ms: Date.now()
    })
    console.log("POST " + workflowApiUrl.value + '/cases ' + data);
    const response = await fetch(workflowApiUrl.value + '/cases', {
      method: 'POST',
      headers: {'Content-Type': 'application/json'},
      body: data
    });

    if (!response.ok) throw new Error('Failed to add case');
    else showAddCaseDialog.value = false;

    Notify.create({ message: 'Neuer Fall hinzugefügt!', type: 'positive' });
  } catch (err) {
    console.error(err);
    Notify.create({ message: 'Fehler beim Fall Hinzufügen', type: 'negative' });
    return;
  }
  await fetchCases();
};

const deleteCase = async (caseId) => {
  // TODO: add confirm dialog
  try {
    console.log("DELETE " + `${workflowApiUrl.value}/cases/${caseId}`);
    const response = await fetch(`${workflowApiUrl.value}/cases/${caseId}`, {
      method: 'DELETE'
    });

    if (!response.ok) throw new Error('Failed to delete case');

    cases.value = cases.value.filter((item) => item.id !== caseId);
    filteredRows.value = [...cases.value];

    Notify.create({ message: 'Fall gelöscht!', type: 'negative' });
  } catch (err) {
    console.error(err);
    Notify.create({ message: 'Fehler beim Löschen von Fall ' + caseId, type: 'negative' });
  }
};

const goToEvidenceItemsPage = function (event, row, index) {
  router.push({ path: 'evidence-items/', query: { caseId: row.id } })
}

const fetchCaseProgress = async function (caseId){
   try {
    // fetch evidence items of this case
    const progressUrl = `${workflowApiUrl.value}/cases/${caseId}/progress`;
    console.log("GET " + progressUrl);
    const responseProgress = await fetch(progressUrl, {method: 'GET'});
    if (responseProgress.status === 404) return 0;
    else if (!responseProgress.ok) throw new Error(`Failed to fetch progress for case ${caseId}.`);

    const progressPercent = (await responseProgress.json())['progress_int_percent'];
    return progressPercent;

  } catch (err) {
    console.log(err)
    Notify.create({message: 'Fehler beim Abrufen des Fortschritts von Fall ' + caseId, type: 'negative', timeout: 10000});
  }
}

onMounted(fetchCases);
</script>

<style scoped>
.q-table {
  width: 100%;
}

.q-btn {
  text-transform: none;
}

.actions-cell .q-btn {
  margin-right: 8px;
}

.action-btn-wrapper {
  display: flex;
  justify-content: center;
}

.tableTopBtnRow {
  margin: 0px 0px 15px 0px;

  .q-btn {
    float: right;
    width: auto;
    height: 38px;
    background-color: #be1717;
    color: white;
    margin-right: 5px;
  }
}

.tableHeaderSearchInput :deep(.q-field__control), :deep(.q-field__append) {
  height: 22px;
  padding: 0px; /* Reduce padding */
  font-size: 13px;
}

</style>
