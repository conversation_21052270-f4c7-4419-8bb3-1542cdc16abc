/* [create-plugin] version: 5.25.7 */
define(["react-router","rxjs","module","@grafana/ui","lodash","react","@emotion/css","@grafana/data","react-dom","@grafana/runtime"],(e,t,i,n,r,s,o,a,l,u)=>(()=>{var c,d,h,f,p={519:(e,t,i)=>{"use strict";i.d(t,{q:()=>r});const n=new RegExp("([a-zA-Z_]\\w*)(>|<|!~|=~|!=|=)(.+)");function r(e){const[,t,i,r]=e.match(n)||[,"","",""];return{key:t.trim(),value:r.replace(/['" ]/g,""),operator:i.trim()}}},782:(e,t,i)=>{"use strict";i.d(t,{U1:()=>k});var n=i(7203);class r{constructor(e,t,i,n,r,s,o,a,l,u=0,c){this.p=e,this.stack=t,this.state=i,this.reducePos=n,this.pos=r,this.score=s,this.buffer=o,this.bufferBase=a,this.curContext=l,this.lookAhead=u,this.parent=c}toString(){return`[${this.stack.filter((e,t)=>t%3==0).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(e,t,i=0){let n=e.parser.context;return new r(e,[],t,i,i,0,[],0,n?new s(n,n.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(e,t){this.stack.push(this.state,t,this.bufferBase+this.buffer.length),this.state=e}reduce(e){var t;let i=e>>19,n=65535&e,{parser:r}=this.p,s=this.reducePos<this.pos-25;s&&this.setLookAhead(this.pos);let o=r.dynamicPrecedence(n);if(o&&(this.score+=o),0==i)return this.pushState(r.getGoto(this.state,n,!0),this.reducePos),n<r.minRepeatTerm&&this.storeNode(n,this.reducePos,this.reducePos,s?8:4,!0),void this.reduceContext(n,this.reducePos);let a=this.stack.length-3*(i-1)-(262144&e?6:0),l=a?this.stack[a-2]:this.p.ranges[0].from,u=this.reducePos-l;u>=2e3&&!(null===(t=this.p.parser.nodeSet.types[n])||void 0===t?void 0:t.isAnonymous)&&(l==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=u):this.p.lastBigReductionSize<u&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=l,this.p.lastBigReductionSize=u));let c=a?this.stack[a-1]:0,d=this.bufferBase+this.buffer.length-c;if(n<r.minRepeatTerm||131072&e){let e=r.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(n,l,e,d+4,!0)}if(262144&e)this.state=this.stack[a];else{let e=this.stack[a-3];this.state=r.getGoto(e,n,!0)}for(;this.stack.length>a;)this.stack.pop();this.reduceContext(n,l)}storeNode(e,t,i,n=4,r=!1){if(0==e&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let e=this,n=this.buffer.length;if(0==n&&e.parent&&(n=e.bufferBase-e.parent.bufferBase,e=e.parent),n>0&&0==e.buffer[n-4]&&e.buffer[n-1]>-1){if(t==i)return;if(e.buffer[n-2]>=t)return void(e.buffer[n-2]=i)}}if(r&&this.pos!=i){let r=this.buffer.length;if(r>0&&0!=this.buffer[r-4]){let e=!1;for(let t=r;t>0&&this.buffer[t-2]>i;t-=4)if(this.buffer[t-1]>=0){e=!0;break}if(e)for(;r>0&&this.buffer[r-2]>i;)this.buffer[r]=this.buffer[r-4],this.buffer[r+1]=this.buffer[r-3],this.buffer[r+2]=this.buffer[r-2],this.buffer[r+3]=this.buffer[r-1],r-=4,n>4&&(n-=4)}this.buffer[r]=e,this.buffer[r+1]=t,this.buffer[r+2]=i,this.buffer[r+3]=n}else this.buffer.push(e,t,i,n)}shift(e,t,i,n){if(131072&e)this.pushState(65535&e,this.pos);else if(262144&e)this.pos=n,this.shiftContext(t,i),t<=this.p.parser.maxNode&&this.buffer.push(t,i,n,4);else{let r=e,{parser:s}=this.p;(n>this.pos||t<=s.maxNode)&&(this.pos=n,s.stateFlag(r,1)||(this.reducePos=n)),this.pushState(r,i),this.shiftContext(t,i),t<=s.maxNode&&this.buffer.push(t,i,n,4)}}apply(e,t,i,n){65536&e?this.reduce(e):this.shift(e,t,i,n)}useNode(e,t){let i=this.p.reused.length-1;(i<0||this.p.reused[i]!=e)&&(this.p.reused.push(e),i++);let n=this.pos;this.reducePos=this.pos=n+e.length,this.pushState(t,n),this.buffer.push(i,n,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,e,this,this.p.stream.reset(this.pos-e.length)))}split(){let e=this,t=e.buffer.length;for(;t>0&&e.buffer[t-2]>e.reducePos;)t-=4;let i=e.buffer.slice(t),n=e.bufferBase+t;for(;e&&n==e.bufferBase;)e=e.parent;return new r(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,i,n,this.curContext,this.lookAhead,e)}recoverByDelete(e,t){let i=e<=this.p.parser.maxNode;i&&this.storeNode(e,this.pos,t,4),this.storeNode(0,this.pos,t,i?8:4),this.pos=this.reducePos=t,this.score-=190}canShift(e){for(let t=new o(this);;){let i=this.p.parser.stateSlot(t.state,4)||this.p.parser.hasAction(t.state,e);if(0==i)return!1;if(!(65536&i))return!0;t.reduce(i)}}recoverByInsert(e){if(this.stack.length>=300)return[];let t=this.p.parser.nextStates(this.state);if(t.length>8||this.stack.length>=120){let i=[];for(let n,r=0;r<t.length;r+=2)(n=t[r+1])!=this.state&&this.p.parser.hasAction(n,e)&&i.push(t[r],n);if(this.stack.length<120)for(let e=0;i.length<8&&e<t.length;e+=2){let n=t[e+1];i.some((e,t)=>1&t&&e==n)||i.push(t[e],n)}t=i}let i=[];for(let e=0;e<t.length&&i.length<4;e+=2){let n=t[e+1];if(n==this.state)continue;let r=this.split();r.pushState(n,this.pos),r.storeNode(0,r.pos,r.pos,4,!0),r.shiftContext(t[e],this.pos),r.reducePos=this.pos,r.score-=200,i.push(r)}return i}forceReduce(){let{parser:e}=this.p,t=e.stateSlot(this.state,5);if(!(65536&t))return!1;if(!e.validAction(this.state,t)){let i=t>>19,n=65535&t,r=this.stack.length-3*i;if(r<0||e.getGoto(this.stack[r],n,!1)<0){let e=this.findForcedReduction();if(null==e)return!1;t=e}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(t),!0}findForcedReduction(){let{parser:e}=this.p,t=[],i=(n,r)=>{if(!t.includes(n))return t.push(n),e.allActions(n,t=>{if(393216&t);else if(65536&t){let i=(t>>19)-r;if(i>1){let n=65535&t,r=this.stack.length-3*i;if(r>=0&&e.getGoto(this.stack[r],n,!1)>=0)return i<<19|65536|n}}else{let e=i(t,r+1);if(null!=e)return e}})};return i(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(3!=this.stack.length)return!1;let{parser:e}=this.p;return 65535==e.data[e.stateSlot(this.state,1)]&&!e.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(e){if(this.state!=e.state||this.stack.length!=e.stack.length)return!1;for(let t=0;t<this.stack.length;t+=3)if(this.stack[t]!=e.stack[t])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(e){return this.p.parser.dialect.flags[e]}shiftContext(e,t){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,e,this,this.p.stream.reset(t)))}reduceContext(e,t){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,e,this,this.p.stream.reset(t)))}emitContext(){let e=this.buffer.length-1;(e<0||-3!=this.buffer[e])&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let e=this.buffer.length-1;(e<0||-4!=this.buffer[e])&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(e){if(e!=this.curContext.context){let t=new s(this.curContext.tracker,e);t.hash!=this.curContext.hash&&this.emitContext(),this.curContext=t}}setLookAhead(e){e>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=e)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class s{constructor(e,t){this.tracker=e,this.context=t,this.hash=e.strict?e.hash(t):0}}class o{constructor(e){this.start=e,this.state=e.state,this.stack=e.stack,this.base=this.stack.length}reduce(e){let t=65535&e,i=e>>19;0==i?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=3*(i-1);let n=this.start.p.parser.getGoto(this.stack[this.base-3],t,!0);this.state=n}}class a{constructor(e,t,i){this.stack=e,this.pos=t,this.index=i,this.buffer=e.buffer,0==this.index&&this.maybeNext()}static create(e,t=e.bufferBase+e.buffer.length){return new a(e,t,t-e.bufferBase)}maybeNext(){let e=this.stack.parent;null!=e&&(this.index=this.stack.bufferBase-e.bufferBase,this.stack=e,this.buffer=e.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,0==this.index&&this.maybeNext()}fork(){return new a(this.stack,this.pos,this.index)}}function l(e,t=Uint16Array){if("string"!=typeof e)return e;let i=null;for(let n=0,r=0;n<e.length;){let s=0;for(;;){let t=e.charCodeAt(n++),i=!1;if(126==t){s=65535;break}t>=92&&t--,t>=34&&t--;let r=t-32;if(r>=46&&(r-=46,i=!0),s+=r,i)break;s*=46}i?i[r++]=s:i=new t(s)}return i}class u{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const c=new u;class d{constructor(e,t){this.input=e,this.ranges=t,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=c,this.rangeIndex=0,this.pos=this.chunkPos=t[0].from,this.range=t[0],this.end=t[t.length-1].to,this.readNext()}resolveOffset(e,t){let i=this.range,n=this.rangeIndex,r=this.pos+e;for(;r<i.from;){if(!n)return null;let e=this.ranges[--n];r-=i.from-e.to,i=e}for(;t<0?r>i.to:r>=i.to;){if(n==this.ranges.length-1)return null;let e=this.ranges[++n];r+=e.from-i.to,i=e}return r}clipPos(e){if(e>=this.range.from&&e<this.range.to)return e;for(let t of this.ranges)if(t.to>e)return Math.max(e,t.from);return this.end}peek(e){let t,i,n=this.chunkOff+e;if(n>=0&&n<this.chunk.length)t=this.pos+e,i=this.chunk.charCodeAt(n);else{let n=this.resolveOffset(e,1);if(null==n)return-1;if(t=n,t>=this.chunk2Pos&&t<this.chunk2Pos+this.chunk2.length)i=this.chunk2.charCodeAt(t-this.chunk2Pos);else{let e=this.rangeIndex,n=this.range;for(;n.to<=t;)n=this.ranges[++e];this.chunk2=this.input.chunk(this.chunk2Pos=t),t+this.chunk2.length>n.to&&(this.chunk2=this.chunk2.slice(0,n.to-t)),i=this.chunk2.charCodeAt(0)}}return t>=this.token.lookAhead&&(this.token.lookAhead=t+1),i}acceptToken(e,t=0){let i=t?this.resolveOffset(t,-1):this.pos;if(null==i||i<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=e,this.token.end=i}acceptTokenTo(e,t){this.token.value=e,this.token.end=t}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:e,chunkPos:t}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=e,this.chunk2Pos=t,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let e=this.input.chunk(this.pos),t=this.pos+e.length;this.chunk=t>this.range.to?e.slice(0,this.range.to-this.pos):e,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(e=1){for(this.chunkOff+=e;this.pos+e>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();e-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=e,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(e,t){if(t?(this.token=t,t.start=e,t.lookAhead=e+1,t.value=t.extended=-1):this.token=c,this.pos!=e){if(this.pos=e,e==this.end)return this.setDone(),this;for(;e<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;e>=this.range.to;)this.range=this.ranges[++this.rangeIndex];e>=this.chunkPos&&e<this.chunkPos+this.chunk.length?this.chunkOff=e-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(e,t){if(e>=this.chunkPos&&t<=this.chunkPos+this.chunk.length)return this.chunk.slice(e-this.chunkPos,t-this.chunkPos);if(e>=this.chunk2Pos&&t<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(e-this.chunk2Pos,t-this.chunk2Pos);if(e>=this.range.from&&t<=this.range.to)return this.input.read(e,t);let i="";for(let n of this.ranges){if(n.from>=t)break;n.to>e&&(i+=this.input.read(Math.max(n.from,e),Math.min(n.to,t)))}return i}}class h{constructor(e,t){this.data=e,this.id=t}token(e,t){let{parser:i}=t.p;f(this.data,e,t,this.id,i.data,i.tokenPrecTable)}}h.prototype.contextual=h.prototype.fallback=h.prototype.extend=!1;h.prototype.fallback=h.prototype.extend=!1;function f(e,t,i,n,r,s){let o=0,a=1<<n,{dialect:l}=i.p.parser;e:for(;0!=(a&e[o]);){let i=e[o+1];for(let n=o+3;n<i;n+=2)if((e[n+1]&a)>0){let i=e[n];if(l.allows(i)&&(-1==t.token.value||t.token.value==i||g(i,t.token.value,r,s))){t.acceptToken(i);break}}let n=t.next,u=0,c=e[o+2];if(!(t.next<0&&c>u&&65535==e[i+3*c-3])){for(;u<c;){let r=u+c>>1,s=i+r+(r<<1),a=e[s],l=e[s+1]||65536;if(n<a)c=r;else{if(!(n>=l)){o=e[s+2],t.advance();continue e}u=r+1}}break}o=e[i+3*c-1]}}function p(e,t,i){for(let n,r=t;65535!=(n=e[r]);r++)if(n==i)return r-t;return-1}function g(e,t,i,n){let r=p(i,n,t);return r<0||p(i,n,e)<r}const m="undefined"!=typeof process&&process.env&&/\bparse\b/.test(process.env.LOG);let v=null;function b(e,t,i){let r=e.cursor(n.Qj.IncludeAnonymous);for(r.moveTo(t);;)if(!(i<0?r.childBefore(t):r.childAfter(t)))for(;;){if((i<0?r.to<t:r.from>t)&&!r.type.isError)return i<0?Math.max(0,Math.min(r.to-1,t-25)):Math.min(e.length,Math.max(r.from+1,t+25));if(i<0?r.prevSibling():r.nextSibling())break;if(!r.parent())return i<0?0:e.length}}class O{constructor(e,t){this.fragments=e,this.nodeSet=t,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let e=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(e){for(this.safeFrom=e.openStart?b(e.tree,e.from+e.offset,1)-e.offset:e.from,this.safeTo=e.openEnd?b(e.tree,e.to+e.offset,-1)-e.offset:e.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(e.tree),this.start.push(-e.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(e){if(e<this.nextStart)return null;for(;this.fragment&&this.safeTo<=e;)this.nextFragment();if(!this.fragment)return null;for(;;){let t=this.trees.length-1;if(t<0)return this.nextFragment(),null;let i=this.trees[t],r=this.index[t];if(r==i.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let s=i.children[r],o=this.start[t]+i.positions[r];if(o>e)return this.nextStart=o,null;if(s instanceof n.PH){if(o==e){if(o<this.safeFrom)return null;let e=o+s.length;if(e<=this.safeTo){let t=s.prop(n.uY.lookAhead);if(!t||e+t<this.fragment.to)return s}}this.index[t]++,o+s.length>=Math.max(this.safeFrom,e)&&(this.trees.push(s),this.start.push(o),this.index.push(0))}else this.index[t]++,this.nextStart=o+s.length}}}class w{constructor(e,t){this.stream=t,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=e.tokenizers.map(e=>new u)}getActions(e){let t=0,i=null,{parser:n}=e.p,{tokenizers:r}=n,s=n.stateSlot(e.state,3),o=e.curContext?e.curContext.hash:0,a=0;for(let n=0;n<r.length;n++){if(!(1<<n&s))continue;let l=r[n],u=this.tokens[n];if((!i||l.fallback)&&((l.contextual||u.start!=e.pos||u.mask!=s||u.context!=o)&&(this.updateCachedToken(u,l,e),u.mask=s,u.context=o),u.lookAhead>u.end+25&&(a=Math.max(u.lookAhead,a)),0!=u.value)){let n=t;if(u.extended>-1&&(t=this.addActions(e,u.extended,u.end,t)),t=this.addActions(e,u.value,u.end,t),!l.extend&&(i=u,t>n))break}}for(;this.actions.length>t;)this.actions.pop();return a&&e.setLookAhead(a),i||e.pos!=this.stream.end||(i=new u,i.value=e.p.parser.eofTerm,i.start=i.end=e.pos,t=this.addActions(e,i.value,i.end,t)),this.mainToken=i,this.actions}getMainToken(e){if(this.mainToken)return this.mainToken;let t=new u,{pos:i,p:n}=e;return t.start=i,t.end=Math.min(i+1,n.stream.end),t.value=i==n.stream.end?n.parser.eofTerm:0,t}updateCachedToken(e,t,i){let n=this.stream.clipPos(i.pos);if(t.token(this.stream.reset(n,e),i),e.value>-1){let{parser:t}=i.p;for(let n=0;n<t.specialized.length;n++)if(t.specialized[n]==e.value){let r=t.specializers[n](this.stream.read(e.start,e.end),i);if(r>=0&&i.p.parser.dialect.allows(r>>1)){1&r?e.extended=r>>1:e.value=r>>1;break}}}else e.value=0,e.end=this.stream.clipPos(n+1)}putAction(e,t,i,n){for(let t=0;t<n;t+=3)if(this.actions[t]==e)return n;return this.actions[n++]=e,this.actions[n++]=t,this.actions[n++]=i,n}addActions(e,t,i,n){let{state:r}=e,{parser:s}=e.p,{data:o}=s;for(let e=0;e<2;e++)for(let a=s.stateSlot(r,e?2:1);;a+=3){if(65535==o[a]){if(1!=o[a+1]){0==n&&2==o[a+1]&&(n=this.putAction(S(o,a+2),t,i,n));break}a=S(o,a+2)}o[a]==t&&(n=this.putAction(S(o,a+1),t,i,n))}return n}}class y{constructor(e,t,i,n){this.parser=e,this.input=t,this.ranges=n,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new d(t,n),this.tokens=new w(e,this.stream),this.topTerm=e.top[1];let{from:s}=n[0];this.stacks=[r.start(this,e.top[0],s)],this.fragments=i.length&&this.stream.end-s>4*e.bufferLength?new O(i,e.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let e,t,i=this.stacks,n=this.minStackPos,r=this.stacks=[];if(this.bigReductionCount>300&&1==i.length){let[e]=i;for(;e.forceReduce()&&e.stack.length&&e.stack[e.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let s=0;s<i.length;s++){let o=i[s];for(;;){if(this.tokens.mainToken=null,o.pos>n)r.push(o);else{if(this.advanceStack(o,r,i))continue;{e||(e=[],t=[]),e.push(o);let i=this.tokens.getMainToken(o);t.push(i.value,i.end)}}break}}if(!r.length){let t=e&&function(e){let t=null;for(let i of e){let e=i.p.stoppedAt;(i.pos==i.p.stream.end||null!=e&&i.pos>e)&&i.p.parser.stateFlag(i.state,2)&&(!t||t.score<i.score)&&(t=i)}return t}(e);if(t)return this.stackToTree(t);if(this.parser.strict)throw new SyntaxError("No parse at "+n);this.recovering||(this.recovering=5)}if(this.recovering&&e){let i=null!=this.stoppedAt&&e[0].pos>this.stoppedAt?e[0]:this.runRecovery(e,t,r);if(i)return this.stackToTree(i.forceAll())}if(this.recovering){let e=1==this.recovering?1:3*this.recovering;if(r.length>e)for(r.sort((e,t)=>t.score-e.score);r.length>e;)r.pop();r.some(e=>e.reducePos>n)&&this.recovering--}else if(r.length>1){e:for(let e=0;e<r.length-1;e++){let t=r[e];for(let i=e+1;i<r.length;i++){let n=r[i];if(t.sameState(n)||t.buffer.length>500&&n.buffer.length>500){if(!((t.score-n.score||t.buffer.length-n.buffer.length)>0)){r.splice(e--,1);continue e}r.splice(i--,1)}}}r.length>12&&r.splice(12,r.length-12)}this.minStackPos=r[0].pos;for(let e=1;e<r.length;e++)r[e].pos<this.minStackPos&&(this.minStackPos=r[e].pos);return null}stopAt(e){if(null!=this.stoppedAt&&this.stoppedAt<e)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=e}advanceStack(e,t,i){let r=e.pos,{parser:s}=this;m&&this.stackID(e);if(null!=this.stoppedAt&&r>this.stoppedAt)return e.forceReduce()?e:null;if(this.fragments){let t=e.curContext&&e.curContext.tracker.strict,i=t?e.curContext.hash:0;for(let o=this.fragments.nodeAt(r);o;){let r=this.parser.nodeSet.types[o.type.id]==o.type?s.getGoto(e.state,o.type.id):-1;if(r>-1&&o.length&&(!t||(o.prop(n.uY.contextHash)||0)==i))return e.useNode(o,r),!0;if(!(o instanceof n.PH)||0==o.children.length||o.positions[0]>0)break;let a=o.children[0];if(!(a instanceof n.PH&&0==o.positions[0]))break;o=a}}let o=s.stateSlot(e.state,4);if(o>0)return e.reduce(o),!0;if(e.stack.length>=8400)for(;e.stack.length>6e3&&e.forceReduce(););let a=this.tokens.getActions(e);for(let n=0;n<a.length;){let s=a[n++],o=a[n++],l=a[n++],u=n==a.length||!i,c=u?e:e.split(),d=this.tokens.mainToken;if(c.apply(s,o,d?d.start:c.pos,l),u)return!0;c.pos>r?t.push(c):i.push(c)}return!1}advanceFully(e,t){let i=e.pos;for(;;){if(!this.advanceStack(e,null,null))return!1;if(e.pos>i)return $(e,t),!0}}runRecovery(e,t,i){let n=null,r=!1;for(let s=0;s<e.length;s++){let o=e[s],a=t[s<<1],l=t[1+(s<<1)],u=m?this.stackID(o)+" -> ":"";if(o.deadEnd){if(r)continue;if(r=!0,o.restart(),this.advanceFully(o,i))continue}let c=o.split(),d=u;for(let e=0;c.forceReduce()&&e<10;e++){if(this.advanceFully(c,i))break;m&&(d=this.stackID(c)+" -> ")}for(let e of o.recoverByInsert(a))this.advanceFully(e,i);this.stream.end>o.pos?(l==o.pos&&(l++,a=0),o.recoverByDelete(a,l),$(o,i)):(!n||n.score<o.score)&&(n=o)}return n}stackToTree(e){return e.close(),n.PH.build({buffer:a.create(e),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:e.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(e){let t=(v||(v=new WeakMap)).get(e);return t||v.set(e,t=String.fromCodePoint(this.nextStackID++)),t+e}}function $(e,t){for(let i=0;i<t.length;i++){let n=t[i];if(n.pos==e.pos&&n.sameState(e))return void(t[i].score<e.score&&(t[i]=e))}t.push(e)}class x{constructor(e,t,i){this.source=e,this.flags=t,this.disabled=i}allows(e){return!this.disabled||0==this.disabled[e]}}class k extends n.iX{constructor(e){if(super(),this.wrappers=[],14!=e.version)throw new RangeError(`Parser version (${e.version}) doesn't match runtime version (14)`);let t=e.nodeNames.split(" ");this.minRepeatTerm=t.length;for(let i=0;i<e.repeatNodeCount;i++)t.push("");let i=Object.keys(e.topRules).map(t=>e.topRules[t][1]),r=[];for(let e=0;e<t.length;e++)r.push([]);function s(e,t,i){r[e].push([t,t.deserialize(String(i))])}if(e.nodeProps)for(let t of e.nodeProps){let e=t[0];"string"==typeof e&&(e=n.uY[e]);for(let i=1;i<t.length;){let n=t[i++];if(n>=0)s(n,e,t[i++]);else{let r=t[i+-n];for(let o=-n;o>0;o--)s(t[i++],e,r);i++}}}this.nodeSet=new n.fI(t.map((t,s)=>n.Z6.define({name:s>=this.minRepeatTerm?void 0:t,id:s,props:r[s],top:i.indexOf(s)>-1,error:0==s,skipped:e.skippedNodes&&e.skippedNodes.indexOf(s)>-1}))),e.propSources&&(this.nodeSet=this.nodeSet.extend(...e.propSources)),this.strict=!1,this.bufferLength=n.cF;let o=l(e.tokenData);this.context=e.context,this.specializerSpecs=e.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let e=0;e<this.specializerSpecs.length;e++)this.specialized[e]=this.specializerSpecs[e].term;this.specializers=this.specializerSpecs.map(P),this.states=l(e.states,Uint32Array),this.data=l(e.stateData),this.goto=l(e.goto),this.maxTerm=e.maxTerm,this.tokenizers=e.tokenizers.map(e=>"number"==typeof e?new h(o,e):e),this.topRules=e.topRules,this.dialects=e.dialects||{},this.dynamicPrecedences=e.dynamicPrecedences||null,this.tokenPrecTable=e.tokenPrec,this.termNames=e.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(e,t,i){let n=new y(this,e,t,i);for(let r of this.wrappers)n=r(n,e,t,i);return n}getGoto(e,t,i=!1){let n=this.goto;if(t>=n[0])return-1;for(let r=n[t+1];;){let t=n[r++],s=1&t,o=n[r++];if(s&&i)return o;for(let i=r+(t>>1);r<i;r++)if(n[r]==e)return o;if(s)return-1}}hasAction(e,t){let i=this.data;for(let n=0;n<2;n++)for(let r,s=this.stateSlot(e,n?2:1);;s+=3){if(65535==(r=i[s])){if(1!=i[s+1]){if(2==i[s+1])return S(i,s+2);break}r=i[s=S(i,s+2)]}if(r==t||0==r)return S(i,s+1)}return 0}stateSlot(e,t){return this.states[6*e+t]}stateFlag(e,t){return(this.stateSlot(e,0)&t)>0}validAction(e,t){return!!this.allActions(e,e=>e==t||null)}allActions(e,t){let i=this.stateSlot(e,4),n=i?t(i):void 0;for(let i=this.stateSlot(e,1);null==n;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=S(this.data,i+2)}n=t(S(this.data,i+1))}return n}nextStates(e){let t=[];for(let i=this.stateSlot(e,1);;i+=3){if(65535==this.data[i]){if(1!=this.data[i+1])break;i=S(this.data,i+2)}if(!(1&this.data[i+2])){let e=this.data[i+1];t.some((t,i)=>1&i&&t==e)||t.push(this.data[i],e)}}return t}configure(e){let t=Object.assign(Object.create(k.prototype),this);if(e.props&&(t.nodeSet=this.nodeSet.extend(...e.props)),e.top){let i=this.topRules[e.top];if(!i)throw new RangeError(`Invalid top rule name ${e.top}`);t.top=i}return e.tokenizers&&(t.tokenizers=this.tokenizers.map(t=>{let i=e.tokenizers.find(e=>e.from==t);return i?i.to:t})),e.specializers&&(t.specializers=this.specializers.slice(),t.specializerSpecs=this.specializerSpecs.map((i,n)=>{let r=e.specializers.find(e=>e.from==i.external);if(!r)return i;let s=Object.assign(Object.assign({},i),{external:r.to});return t.specializers[n]=P(s),s})),e.contextTracker&&(t.context=e.contextTracker),e.dialect&&(t.dialect=this.parseDialect(e.dialect)),null!=e.strict&&(t.strict=e.strict),e.wrap&&(t.wrappers=t.wrappers.concat(e.wrap)),null!=e.bufferLength&&(t.bufferLength=e.bufferLength),t}hasWrappers(){return this.wrappers.length>0}getName(e){return this.termNames?this.termNames[e]:String(e<=this.maxNode&&this.nodeSet.types[e].name||e)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(e){let t=this.dynamicPrecedences;return null==t?0:t[e]||0}parseDialect(e){let t=Object.keys(this.dialects),i=t.map(()=>!1);if(e)for(let n of e.split(" ")){let e=t.indexOf(n);e>=0&&(i[e]=!0)}let n=null;for(let e=0;e<t.length;e++)if(!i[e])for(let i,r=this.dialects[t[e]];65535!=(i=this.data[r++]);)(n||(n=new Uint8Array(this.maxTerm+1)))[i]=1;return new x(e,i,n)}static deserialize(e){return new k(e)}}function S(e,t){return e[t]|e[t+1]<<16}function P(e){if(e.external){let t=e.extend?1:0;return(i,n)=>e.external(i,n)<<1|t}return e.get}},1159:t=>{"use strict";t.exports=e},1160:(e,t,i)=>{"use strict";i.d(t,{n1:()=>$r,Js:()=>kr});class n{constructor(){this.subscribers=[]}subscribe(e){return this.subscribers.push(e),{unsubscribe:()=>this.unsubscribe(e)}}unsubscribe(e){this.subscribers=this.subscribers.filter(t=>t!==e)}notify(e){this.subscribers.forEach(t=>t(e))}first(){const e=new n,t=t=>{e.notify(t),i.unsubscribe()},i=this.subscribe(t),r=e.unsubscribe.bind(e);return this.withUnsubscribeOverride(e,r,t)}takeWhile(e){const t=new n,i=n=>{e(n)?t.notify(n):t.unsubscribe(i)};this.subscribe(i);const r=t.unsubscribe.bind(t);return this.withUnsubscribeOverride(t,r,i)}filter(e){const t=new n,i=i=>{e(i)&&t.notify(i)};this.subscribe(i);const r=t.unsubscribe.bind(t);return this.withUnsubscribeOverride(t,r,i)}merge(...e){const t=new n,i=[];e.forEach(e=>{const n=e.subscribe(e=>{t.notify(e)});i.push(n)});const r=t.unsubscribeAll.bind(t);return t.unsubscribe=()=>{i.forEach(e=>e.unsubscribe()),r()},t}withUnsubscribeOverride(e,t,i){return e.unsubscribe=e=>{t(e),this.unsubscribe(i)},e}unsubscribeAll(){this.subscribers=[]}}var r;!function(e){e.EXCEPTION="exception",e.LOG="log",e.MEASUREMENT="measurement",e.TRACE="trace",e.EVENT="event"}(r||(r={}));const s={[r.EXCEPTION]:"exceptions",[r.LOG]:"logs",[r.MEASUREMENT]:"measurements",[r.TRACE]:"traces",[r.EVENT]:"events"};function o(e,t){return typeof e===t}function a(e,t){return Object.prototype.toString.call(e)===`[object ${t}]`}function l(e,t){try{return e instanceof t}catch(e){return!1}}const u=e=>o(e,"null"),c=e=>o(e,"string"),d=e=>o(e,"number")&&!isNaN(e)||o(e,"bigint"),h=e=>o(e,"boolean"),f=e=>!u(e)&&o(e,"object"),p=e=>o(e,"function"),g=e=>a(e,"Array"),m="undefined"!=typeof Event,v="undefined"!=typeof Error,b=e=>v&&l(e,Error);function O(e){return null==e||(g(e)||c(e)?0===e.length:!!f(e)&&0===Object.keys(e).length)}function w(e={}){return JSON.stringify(null!=e?e:{},function(){const e=new WeakSet;return function(t,i){if(f(i)&&null!==i){if(e.has(i))return null;e.add(i)}return i}}())}function y(e={}){const t={};for(const[i,n]of Object.entries(e))t[i]=f(n)&&null!==n?w(n):String(n);return t}function $(){return Date.now()}function x(){return(new Date).toISOString()}function k(e){return new Date(e).toISOString()}function S(e,t){if(e===t)return!0;if(o(e,"number")&&isNaN(e))return o(t,"number")&&isNaN(t);const i=g(e),n=g(t);if(i!==n)return!1;if(i&&n){const i=e.length;if(i!==t.length)return!1;for(let n=i;0!==n--;)if(!S(e[n],t[n]))return!1;return!0}const r=f(e),s=f(t);if(r!==s)return!1;if(e&&t&&r&&s){const i=Object.keys(e),n=Object.keys(t);if(i.length!==n.length)return!1;for(let e of i)if(!n.includes(e))return!1;for(let n of i)if(!S(e[n],t[n]))return!1;return!0}return!1}const P="user-action-start",T="user-action-end",E="user-action-cancel",z="user-action-halt";const Q="Error",A=e=>e.map(e=>f(e)?w(e):String(e)).join(" ");let C;function I({internalLogger:e,config:t,metas:i,transports:n,tracesApi:s,actionBuffer:o,getMessage:a}){var l;e.debug("Initializing exceptions API");let d=null;C=null!==(l=t.parseStacktrace)&&void 0!==l?l:C;const h=t=>{e.debug("Changing stacktrace parser"),C=null!=t?t:C},{ignoreErrors:p=[],preserveOriginalError:m}=t;return h(t.parseStacktrace),{changeStacktraceParser:h,getStacktraceParser:()=>C,pushError:(l,{skipDedupe:h,stackFrames:v,type:$,context:T,spanContext:E,timestampOverwriteMs:z,originalError:A}={})=>{if(!function(e,t){const{message:i,name:n,stack:r}=t;return s=e,o=i+" "+n+" "+r,s.some(e=>c(e)?o.includes(e):!!o.match(e));var s,o}(p,null!=A?A:l))try{const c=y(Object.assign(Object.assign({},function(e){let t=e.cause;b(t)?t=e.cause.toString():null!==t&&(f(e.cause)||g(e.cause))?t=w(e.cause):null!=t&&(t=e.cause.toString());return null==t?{}:{cause:t}}(null!=A?A:l)),null!=T?T:{})),p={meta:i.value,payload:Object.assign(Object.assign({type:$||l.name||Q,value:l.message,timestamp:z?k(z):x(),trace:E?{trace_id:E.traceId,span_id:E.spanId}:s.getTraceContext()},O(c)?{}:{context:c}),m?{originalError:A}:{}),type:r.EXCEPTION};(null==(v=null!=v?v:l.stack?null==C?void 0:C(l).frames:void 0)?void 0:v.length)&&(p.payload.stacktrace={frames:v});const I={type:p.payload.type,value:p.payload.value,stackTrace:p.payload.stacktrace,context:p.payload.context};if(!h&&t.dedupe&&!u(d)&&S(I,d))return void e.debug("Skipping error push because it is the same as the last one\n",p.payload);d=I,e.debug("Pushing exception\n",p);const X=a();X&&X.type===P?o.addItem(p):n.execute(p)}catch(t){e.error("Error pushing event",t)}}}}var X=i(5438);const U=e=>e.map(e=>{try{return String(e)}catch(e){return""}}).join(" ");class _{constructor(){this.buffer=[]}addItem(e){this.buffer.push(e)}flushBuffer(e){if(p(e))for(const t of this.buffer)e(t);this.buffer.length=0}size(){return this.buffer.length}}function R({apiMessageBus:e,transports:t,config:i}){const n=new _,s=i.trackUserActionsExcludeItem;let o;e.subscribe(e=>{if(P!==e.type&&z!==e.type){if(e.type===T){const{id:i,name:a}=e;return n.flushBuffer(e=>{if(function(e,t){return(null==t?void 0:t(e))||e.type===r.MEASUREMENT&&"web-vitals"===e.payload.type}(e,s))return void t.execute(e);const n=Object.assign(Object.assign({},e),{payload:Object.assign(Object.assign({},e.payload),{action:{parentId:i,name:a}})});t.execute(n)}),void(o=void 0)}e.type===E&&(o=void 0,n.flushBuffer(e=>{t.execute(e)}))}else o=e});return{actionBuffer:n,getMessage:()=>o}}const L=new n;function N(e,t,i,n,s){t.debug("Initializing API");const{actionBuffer:o,getMessage:a}=R({apiMessageBus:L,transports:s,config:i}),l=function(e,t,i,n,s){let o;return t.debug("Initializing traces API"),{getOTEL:()=>o,getTraceContext:()=>{const e=null==o?void 0:o.trace.getSpanContext(o.context.active());return e?{trace_id:e.traceId,span_id:e.spanId}:void 0},initOTEL:(e,i)=>{t.debug("Initializing OpenTelemetry"),o={trace:e,context:i}},isOTELInitialized:()=>!!o,pushTraces:e=>{try{const i={type:r.TRACE,payload:e,meta:n.value};t.debug("Pushing trace\n",i),s.execute(i)}catch(e){t.error("Error pushing trace\n",e)}}}}(0,t,0,n,s),d={unpatchedConsole:e,internalLogger:t,config:i,metas:n,transports:s,tracesApi:l,actionBuffer:o,getMessage:a};return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},l),I(d)),function({internalLogger:e,metas:t}){let i,n,r,s;e.debug("Initializing meta API");const o=e=>{n&&t.remove(n),n={user:e},t.add(n)},a=(e,n)=>{var r;const s=null==n?void 0:n.overrides,o=s?{overrides:Object.assign(Object.assign({},null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.overrides),s)}:{};i&&t.remove(i),i={session:Object.assign(Object.assign({},O(e)?void 0:e),o)},t.add(i)},l=()=>t.value.session,u=()=>t.value.page;return{setUser:o,resetUser:o,setSession:a,resetSession:a,getSession:l,setView:(e,i)=>{var n;if((null==i?void 0:i.overrides)&&a(l(),{overrides:i.overrides}),(null===(n=null==r?void 0:r.view)||void 0===n?void 0:n.name)===(null==e?void 0:e.name))return;const s=r;r={view:e},t.add(r),s&&t.remove(s)},getView:()=>t.value.view,setPage:e=>{var i;const n=c(e)?Object.assign(Object.assign({},null!==(i=null==s?void 0:s.page)&&void 0!==i?i:u()),{id:e}):e;s&&t.remove(s),s={page:n},t.add(s)},getPage:u}}(d)),function({internalLogger:e,config:t,metas:i,transports:n,tracesApi:s,actionBuffer:o,getMessage:a}){var l;e.debug("Initializing logs API");let c=null;const d=null!==(l=t.logArgsSerializer)&&void 0!==l?l:U;return{pushLog:(l,{context:h,level:f,skipDedupe:p,spanContext:g,timestampOverwriteMs:m}={})=>{try{const v=y(h),b={type:r.LOG,payload:{message:d(l),level:null!=f?f:X.Ic,context:O(v)?void 0:v,timestamp:m?k(m):x(),trace:g?{trace_id:g.traceId,span_id:g.spanId}:s.getTraceContext()},meta:i.value},w={message:b.payload.message,level:b.payload.level,context:b.payload.context};if(!p&&t.dedupe&&!u(c)&&S(w,c))return void e.debug("Skipping log push because it is the same as the last one\n",b.payload);c=w,e.debug("Pushing log\n",b);const $=a();$&&$.type===P?o.addItem(b):n.execute(b)}catch(t){e.error("Error pushing log\n",t)}}}}(d)),function({internalLogger:e,config:t,metas:i,transports:n,tracesApi:s,actionBuffer:o,getMessage:a}){e.debug("Initializing measurements API");let l=null;return{pushMeasurement:(c,{skipDedupe:d,context:h,spanContext:f,timestampOverwriteMs:p}={})=>{try{const g=y(h),m={type:r.MEASUREMENT,payload:Object.assign(Object.assign({},c),{trace:f?{trace_id:f.traceId,span_id:f.spanId}:s.getTraceContext(),timestamp:p?k(p):x(),context:O(g)?void 0:g}),meta:i.value},v={type:m.payload.type,values:m.payload.values,context:m.payload.context};if(!d&&t.dedupe&&!u(l)&&S(v,l))return void e.debug("Skipping measurement push because it is the same as the last one\n",m.payload);l=v,e.debug("Pushing measurement\n",m);const b=a();b&&b.type===P?o.addItem(m):n.execute(m)}catch(t){e.error("Error pushing measurement\n",t)}}}}(d)),function({internalLogger:e,config:t,metas:i,transports:n,tracesApi:s,actionBuffer:o,getMessage:a}){let l=null;return{pushEvent:(c,d,h,{skipDedupe:f,spanContext:p,timestampOverwriteMs:g,customPayloadTransformer:m=e=>e}={})=>{try{const v=y(d),b={meta:i.value,payload:m({name:c,domain:null!=h?h:t.eventDomain,attributes:O(v)?void 0:v,timestamp:g?k(g):x(),trace:p?{trace_id:p.traceId,span_id:p.spanId}:s.getTraceContext()}),type:r.EVENT},w={name:b.payload.name,attributes:b.payload.attributes,domain:b.payload.domain};if(!f&&t.dedupe&&!u(l)&&S(w,l))return void e.debug("Skipping event push because it is the same as the last one\n",b.payload);l=w,e.debug("Pushing event\n",b);const $=a();$&&$.type===P?o.addItem(b):n.execute(b)}catch(t){e.error("Error pushing event",t)}}}}(d))}function D(){}var j;!function(e){e[e.OFF=0]="OFF",e[e.ERROR=1]="ERROR",e[e.WARN=2]="WARN",e[e.INFO=3]="INFO",e[e.VERBOSE=4]="VERBOSE"}(j||(j={}));const M={debug:D,error:D,info:D,prefix:"Faro",warn:D},q=j.ERROR,F=Object.assign({},console);function B(e=F,t=q){const i=M;return t>j.OFF&&(i.error=t>=j.ERROR?function(...t){e.error(`${i.prefix}\n`,...t)}:D,i.warn=t>=j.WARN?function(...t){e.warn(`${i.prefix}\n`,...t)}:D,i.info=t>=j.INFO?function(...t){e.info(`${i.prefix}\n`,...t)}:D,i.debug=t>=j.VERBOSE?function(...t){e.debug(`${i.prefix}\n`,...t)}:D),i}let V=M;function W(e,t){return V=B(e,t.internalLoggerLevel),V}const Y="undefined"!=typeof globalThis?globalThis:void 0!==i.g?i.g:"undefined"!=typeof self?self:void 0;const Z="1.19.0";const G="_faroInternal";let H={};function K(e,t,i,n,r,s,o){return t.debug("Initializing Faro"),H={api:s,config:i,instrumentations:o,internalLogger:t,metas:n,pause:r.pause,transports:r,unpatchedConsole:e,unpause:r.unpause},function(e){e.config.isolate?e.internalLogger.debug("Skipping registering internal Faro instance on global object"):(e.internalLogger.debug("Registering internal Faro instance on global object"),Object.defineProperty(Y,G,{configurable:!1,enumerable:!1,writable:!1,value:e}))}(H),function(e){if(e.config.preventGlobalExposure)e.internalLogger.debug("Skipping registering public Faro instance in the global scope");else{if(e.internalLogger.debug(`Registering public faro reference in the global scope using "${e.config.globalObjectKey}" key`),e.config.globalObjectKey in Y)return void e.internalLogger.warn(`Skipping global registration due to key "${e.config.globalObjectKey}" being used already. Please set "globalObjectKey" to something else or set "preventGlobalExposure" to "true"`);Object.defineProperty(Y,e.config.globalObjectKey,{configurable:!1,writable:!1,value:e})}}(H),H}class J{constructor(e,t){var i,n;this.signalBuffer=[],this.itemLimit=null!==(i=null==t?void 0:t.itemLimit)&&void 0!==i?i:50,this.sendTimeout=null!==(n=null==t?void 0:t.sendTimeout)&&void 0!==n?n:250,this.paused=(null==t?void 0:t.paused)||!1,this.sendFn=e,this.flushInterval=-1,this.paused||this.start(),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&this.flush()})}addItem(e){this.paused||(this.signalBuffer.push(e),this.signalBuffer.length>=this.itemLimit&&this.flush())}start(){this.paused=!1,this.sendTimeout>0&&(this.flushInterval=window.setInterval(()=>this.flush(),this.sendTimeout))}pause(){this.paused=!0,clearInterval(this.flushInterval)}groupItems(e){const t=new Map;return e.forEach(e=>{const i=JSON.stringify(e.meta);let n=t.get(i);n=void 0===n?[e]:[...n,e],t.set(i,n)}),Array.from(t.values())}flush(){if(this.paused||0===this.signalBuffer.length)return;this.groupItems(this.signalBuffer).forEach(this.sendFn),this.signalBuffer=[]}}function ee(e,t,i,n){var r;t.debug("Initializing transports");const s=[];let o=i.paused,a=[];const l=e=>{let t=e;for(const e of a){const n=t.map(e).filter(Boolean);if(0===n.length)return[];t=te(n,i)}return t},u=e=>{const i=l(e);if(0!==i.length)for(const e of s)t.debug(`Transporting item using ${e.name}\n`,i),e.isBatched()&&e.send(i)};let c;(null===(r=i.batching)||void 0===r?void 0:r.enabled)&&(c=new J(u,{sendTimeout:i.batching.sendTimeout,itemLimit:i.batching.itemLimit,paused:o}));return{add:(...r)=>{t.debug("Adding transports"),r.forEach(r=>{t.debug(`Adding "${r.name}" transport`);s.some(e=>e===r)?t.warn(`Transport ${r.name} is already added`):(r.unpatchedConsole=e,r.internalLogger=t,r.config=i,r.metas=n,s.push(r))})},addBeforeSendHooks:(...e)=>{t.debug("Adding beforeSendHooks\n",a),e.forEach(e=>{e&&a.push(e)})},getBeforeSendHooks:()=>[...a],execute:e=>{var n;o||((null===(n=i.batching)||void 0===n?void 0:n.enabled)&&(null==c||c.addItem(e)),(e=>{var n,r;if((null===(n=i.batching)||void 0===n?void 0:n.enabled)&&s.every(e=>e.isBatched()))return;const[o]=l([e]);if(void 0!==o)for(const e of s)t.debug(`Transporting item using ${e.name}\n`,o),e.isBatched()?(null===(r=i.batching)||void 0===r?void 0:r.enabled)||e.send([o]):e.send(o)})(e))},isPaused:()=>o,pause:()=>{t.debug("Pausing transports"),null==c||c.pause(),o=!0},remove:(...e)=>{t.debug("Removing transports"),e.forEach(e=>{t.debug(`Removing "${e.name}" transport`);const i=s.indexOf(e);-1!==i?s.splice(i,1):t.warn(`Transport "${e.name}" is not added`)})},removeBeforeSendHooks:(...e)=>{a.filter(t=>!e.includes(t))},get transports(){return[...s]},unpause:()=>{t.debug("Unpausing transports"),null==c||c.start(),o=!1}}}function te(e,t){if(t.preserveOriginalError)for(const t of e)t.type===r.EXCEPTION&&delete t.payload.originalError;return e}let ie=F;function ne(e){var t;return ie=null!==(t=e.unpatchedConsole)&&void 0!==t?t:ie,ie}function re(e){const t=ne(e),i=W(t,e);if(G in Y&&!e.isolate)return void i.error('Faro is already registered. Either add instrumentations, transports etc. to the global faro instance or use the "isolate" property');i.debug("Initializing");const n=function(e,t){let i=[],n=[];const r=()=>i.reduce((e,t)=>Object.assign(e,p(t)?t():t),{}),s=()=>{if(n.length){const e=r();n.forEach(t=>t(e))}};return{add:(...e)=>{t.debug("Adding metas\n",e),i.push(...e),s()},remove:(...e)=>{t.debug("Removing metas\n",e),i=i.filter(t=>!e.includes(t)),s()},addListener:e=>{t.debug("Adding metas listener\n",e),n.push(e)},removeListener:e=>{t.debug("Removing metas listener\n",e),n=n.filter(t=>t!==e)},get value(){return r()}}}(0,i),r=ee(t,i,e,n),s=N(t,i,e,n,r),o=function(e,t,i,n,r,s){t.debug("Initializing instrumentations");const o=[];return{add:(...a)=>{t.debug("Adding instrumentations"),a.forEach(a=>{t.debug(`Adding "${a.name}" instrumentation`),o.some(e=>e.name===a.name)?t.warn(`Instrumentation ${a.name} is already added`):(a.unpatchedConsole=e,a.internalLogger=t,a.config=i,a.metas=n,a.transports=r,a.api=s,o.push(a),a.initialize())})},get instrumentations(){return[...o]},remove:(...e)=>{t.debug("Removing instrumentations"),e.forEach(e=>{var i,n;t.debug(`Removing "${e.name}" instrumentation`);const r=o.reduce((t,i,n)=>null===t&&i.name===e.name?n:null,null);null!==r?(null===(n=(i=o[r]).destroy)||void 0===n||n.call(i),o.splice(r,1)):t.warn(`Instrumentation "${e.name}" is not added`)})}}}(t,i,e,n,r,s),a=K(t,i,e,n,r,s,o);return function(e){var t,i;const n={sdk:{version:Z},app:{bundleId:e.config.app.name&&(r=e.config.app.name,null==Y?void 0:Y[`__faroBundleId_${r}`])}};var r;const s=null===(t=e.config.sessionTracking)||void 0===t?void 0:t.session;s&&e.api.setSession(s),e.config.app&&(n.app=Object.assign(Object.assign({},e.config.app),n.app)),e.config.user&&(n.user=e.config.user),e.config.view&&(n.view=e.config.view),e.metas.add(n,...null!==(i=e.config.metas)&&void 0!==i?i:[])}(a),function(e){e.transports.add(...e.config.transports),e.transports.addBeforeSendHooks(e.config.beforeSend)}(a),function(e){e.instrumentations.add(...e.config.instrumentations)}(a),a}const se="faro",oe={enabled:!0,sendTimeout:250,itemLimit:50},ae="browser",le="\n",ue="eval",ce="?",de="@",he=/^\s*at (?:(.*?) ?\((?:address at )?)?((?:file|https?|blob|chrome-extension|address|native|eval|webpack|<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,fe=/\((\S*)(?::(\d+))(?::(\d+))\)/,pe="eval",ge="address at ",me=ge.length,ve=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:file|https?|blob|chrome|webpack|resource|moz-extension|safari-extension|safari-web-extension|capacitor)?:\/.*?|\[native code]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,be=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Oe=" > eval",we="safari-extension",ye="safari-web-extension",$e=/Minified React error #\d+;/i;function xe(e,t,i,n){const r={filename:e||document.location.href,function:t||ce};return void 0!==i&&(r.lineno=i),void 0!==n&&(r.colno=n),r}function ke(e,t){const i=null==e?void 0:e.includes(we),n=!i&&(null==e?void 0:e.includes(ye));return i||n?[(null==e?void 0:e.includes(de))?e.split(de)[0]:e,i?`${we}:${t}`:`${ye}:${t}`]:[e,t]}function Se(e){let t=[];e.stacktrace?t=e.stacktrace.split(le).filter((e,t)=>t%2==0):e.stack&&(t=e.stack.split(le));const i=t.reduce((t,i,n)=>{let r,s,o,a,l;if(r=he.exec(i)){if(s=r[1],o=r[2],a=r[3],l=r[4],null==o?void 0:o.startsWith(pe)){const e=fe.exec(o);e&&(o=e[1],a=e[2],l=e[3])}o=(null==o?void 0:o.startsWith(ge))?o.substring(me):o,[s,o]=ke(s,o)}else if(r=ve.exec(i)){if(s=r[1],o=r[3],a=r[4],l=r[5],o&&o.includes(Oe)){const e=be.exec(o);e&&(s=s||ue,o=e[1],a=e[2])}else 0===n&&!l&&d(e.columnNumber)&&(l=String(e.columnNumber+1));[s,o]=ke(s,o)}return(o||s)&&t.push(xe(o,s,a?Number(a):void 0,l?Number(l):void 0)),t},[]);return $e.test(e.message)?i.slice(1):i}function Pe(e){return{frames:Se(e)}}const Te="com.grafana.faro.session",Ee=9e5,ze={enabled:!0,persistent:!1,maxSessionPersistenceTime:Ee},Qe="http-request-start",Ae="http-request-end",Ce="data-faro-user-action-name",Ie="faroApiCall";var Xe=i(6660);const Ue="unknown",_e=()=>{const e=new Xe.UAParser,{name:t,version:i}=e.getBrowser(),{name:n,version:r}=e.getOS(),s=e.getUA(),o=navigator.language,a=navigator.userAgent.includes("Mobi"),l=function(){if(!t||!i)return;if("userAgentData"in navigator&&navigator.userAgentData)return navigator.userAgentData.brands;return}();return{browser:{name:null!=t?t:Ue,version:null!=i?i:Ue,os:`${null!=n?n:Ue} ${null!=r?r:Ue}`,userAgent:null!=s?s:Ue,language:null!=o?o:Ue,mobile:a,brands:null!=l?l:Ue,viewportWidth:`${window.innerWidth}`,viewportHeight:`${window.innerHeight}`}}},Re=()=>{const e=window.k6;return{k6:Object.assign({isK6Browser:!0},(null==e?void 0:e.testRunId)&&{testRunId:null==e?void 0:e.testRunId})}};let Le,Ne;function De({generatePageId:e,initialPageMeta:t}={}){return()=>{const i=location.href;return p(e)&&Le!==i&&(Le=i,Ne=e(location)),{page:Object.assign(Object.assign({url:i},Ne?{id:Ne}:{}),t)}}}class je{constructor(){this.unpatchedConsole=F,this.internalLogger=M,this.config={},this.metas={}}logDebug(...e){this.internalLogger.debug(`${this.name}\n`,...e)}logInfo(...e){this.internalLogger.info(`${this.name}\n`,...e)}logWarn(...e){this.internalLogger.warn(`${this.name}\n`,...e)}logError(...e){this.internalLogger.error(`${this.name}\n`,...e)}}class Me extends je{isBatched(){return!1}getIgnoreUrls(){return[]}}function qe(e,t){var i,n;if(void 0===t)return e;if(void 0===e)return{resourceSpans:t};const r=null===(i=e.resourceSpans)||void 0===i?void 0:i[0];if(void 0===r)return e;const s=(null==r?void 0:r.scopeSpans)||[],o=(null===(n=null==t?void 0:t[0])||void 0===n?void 0:n.scopeSpans)||[];return Object.assign(Object.assign({},e),{resourceSpans:[Object.assign(Object.assign({},r),{scopeSpans:[...s,...o]})]})}function Fe(e,t){let i,n=!1;const r=()=>{null!=i?(e(...i),i=null,setTimeout(r,t)):n=!1};return(...s)=>{n?i=s:(e(...s),n=!0,setTimeout(r,t))}}const Be="sessionStorage",Ve="localStorage";function We(e){var t;try{let t;t=window[e];const i="__faro_storage_test__";return t.setItem(i,i),t.removeItem(i),!0}catch(i){return null===(t=H.internalLogger)||void 0===t||t.info(`Web storage of type ${e} is not available. Reason: ${i}`),!1}}function Ye(e,t){return Je(t)?window[t].getItem(e):null}function Ze(e,t,i){if(Je(i))try{window[i].setItem(e,t)}catch(e){}}function Ge(e,t){Je(t)&&window[t].removeItem(e)}const He=We(Ve),Ke=We(Be);function Je(e){return e===Ve?He:e===Be&&Ke}const et="abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ0123456789";function tt(e=10){return Array.from(Array(e)).map(()=>et[Math.floor(59*Math.random())]).join("")}const it="session_start",nt="session_resume",rt="service_name_override";function st(){var e,t,i;const n=H.config.sessionTracking;let r=null!==(i=null!==(t=null===(e=null==n?void 0:n.sampler)||void 0===e?void 0:e.call(n,{metas:H.metas.value}))&&void 0!==t?t:null==n?void 0:n.samplingRate)&&void 0!==i?i:1;if("number"!=typeof r){r=0}return Math.random()<r}function ot({sessionId:e,started:t,lastActivity:i,isSampled:n=!0}={}){var r,s;const o=$(),a=null===(s=null===(r=H.config)||void 0===r?void 0:r.sessionTracking)||void 0===s?void 0:s.generateSessionId;return null==e&&(e="function"==typeof a?a():tt()),{sessionId:e,lastActivity:null!=i?i:o,started:null!=t?t:o,isSampled:n}}function at(e){if(null==e)return!1;const t=$();if(!(t-e.started<144e5))return!1;return t-e.lastActivity<Ee}function lt({fetchUserSession:e,storeUserSession:t}){return function({forceSessionExtend:i}={forceSessionExtend:!1}){var n,r,s;if(!e||!t)return;const o=H.config.sessionTracking,a=null==o?void 0:o.persistent;if(a&&!He||!a&&!Ke)return;const l=e();if(!1===i&&at(l))t(Object.assign(Object.assign({},l),{lastActivity:$()}));else{let e=ut(ot({isSampled:st()}),l);t(e),null===(n=H.api)||void 0===n||n.setSession(e.sessionMeta),null===(r=null==o?void 0:o.onSessionChange)||void 0===r||r.call(o,null!==(s=null==l?void 0:l.sessionMeta)&&void 0!==s?s:null,e.sessionMeta)}}}function ut(e,t){var i,n,r,s,o,a,l;const u=Object.assign(Object.assign({},e),{sessionMeta:{id:e.sessionId,attributes:Object.assign(Object.assign(Object.assign({},null===(n=null===(i=H.config.sessionTracking)||void 0===i?void 0:i.session)||void 0===n?void 0:n.attributes),null!==(s=null===(r=H.metas.value.session)||void 0===r?void 0:r.attributes)&&void 0!==s?s:{}),{isSampled:e.isSampled.toString()})}}),c=null!==(a=null===(o=H.metas.value.session)||void 0===o?void 0:o.overrides)&&void 0!==a?a:null===(l=null==t?void 0:t.sessionMeta)||void 0===l?void 0:l.overrides;O(c)||(u.sessionMeta.overrides=c);const d=null==t?void 0:t.sessionId;return null!=d&&(u.sessionMeta.attributes.previousSession=d),u}function ct({fetchUserSession:e,storeUserSession:t}){return function(i){const n=i.session,r=e();let s=null==n?void 0:n.id;const o=null==n?void 0:n.attributes,a=null==n?void 0:n.overrides,l=null==r?void 0:r.sessionMeta,u=null==l?void 0:l.overrides,c=!!a&&!S(a,u),d=!!o&&!S(o,null==l?void 0:l.attributes);if(!!n&&s!==(null==r?void 0:r.sessionId)||d||c){const e=ut(ot({sessionId:s,isSampled:st()}),r);t(e),function(e,t={},i={}){var n,r,s;if(!e)return;const o=t.serviceName,a=null!==(s=null!==(n=i.serviceName)&&void 0!==n?n:null===(r=H.metas.value.app)||void 0===r?void 0:r.name)&&void 0!==s?s:"";o&&o!==a&&H.api.pushEvent(rt,{serviceName:o,previousServiceName:a})}(c,a,u),H.api.setSession(e.sessionMeta)}}}class dt{constructor(){this.updateSession=Fe(()=>this.updateUserSession(),1e3),this.updateUserSession=lt({fetchUserSession:dt.fetchUserSession,storeUserSession:dt.storeUserSession}),this.init()}static removeUserSession(){Ge(Te,dt.storageTypeLocal)}static storeUserSession(e){Ze(Te,w(e),dt.storageTypeLocal)}static fetchUserSession(){const e=Ye(Te,dt.storageTypeLocal);return e?JSON.parse(e):null}init(){document.addEventListener("visibilitychange",()=>{"visible"===document.visibilityState&&this.updateSession()}),H.metas.addListener(ct({fetchUserSession:dt.fetchUserSession,storeUserSession:dt.storeUserSession}))}}dt.storageTypeLocal=Ve;class ht{constructor(){this.updateSession=Fe(()=>this.updateUserSession(),1e3),this.updateUserSession=lt({fetchUserSession:ht.fetchUserSession,storeUserSession:ht.storeUserSession}),this.init()}static removeUserSession(){Ge(Te,ht.storageTypeSession)}static storeUserSession(e){Ze(Te,w(e),ht.storageTypeSession)}static fetchUserSession(){const e=Ye(Te,ht.storageTypeSession);return e?JSON.parse(e):null}init(){document.addEventListener("visibilitychange",()=>{"visible"===document.visibilityState&&this.updateSession()}),H.metas.addListener(ct({fetchUserSession:ht.fetchUserSession,storeUserSession:ht.storeUserSession}))}}function ft(e){return(null==e?void 0:e.persistent)?dt:ht}ht.storageTypeSession=Be;var pt=function(e,t,i,n){return new(i||(i=Promise))(function(r,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})},gt=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(i[n[r]]=e[n[r]])}return i};class mt extends Me{constructor(e){var t,i,n,r;super(),this.options=e,this.name="@grafana/faro-web-sdk:transport-fetch",this.version=Z,this.disabledUntil=new Date,this.rateLimitBackoffMs=null!==(t=e.defaultRateLimitBackoffMs)&&void 0!==t?t:5e3,this.getNow=null!==(i=e.getNow)&&void 0!==i?i:()=>Date.now(),this.promiseBuffer=function(e){const{size:t,concurrency:i}=e,n=[];let r=0;const s=()=>{if(r<i&&n.length){const{producer:e,resolve:t,reject:i}=n.shift();r++,e().then(e=>{r--,s(),t(e)},e=>{r--,s(),i(e)})}};return{add:e=>{if(n.length+r>=t)throw new Error("Task buffer full");return new Promise((t,i)=>{n.push({producer:e,resolve:t,reject:i}),s()})}}}({size:null!==(n=e.bufferSize)&&void 0!==n?n:30,concurrency:null!==(r=e.concurrency)&&void 0!==r?r:5})}send(e){return pt(this,void 0,void 0,function*(){try{if(this.disabledUntil>new Date(this.getNow()))return this.logWarn(`Dropping transport item due to too many requests. Backoff until ${this.disabledUntil}`),Promise.resolve();yield this.promiseBuffer.add(()=>{const t=JSON.stringify(function(e){let t={meta:{}};return void 0!==e[0]&&(t.meta=e[0].meta),e.forEach(e=>{switch(e.type){case r.LOG:case r.EVENT:case r.EXCEPTION:case r.MEASUREMENT:const i=s[e.type],n=t[i];t=Object.assign(Object.assign({},t),{[i]:void 0===n?[e.payload]:[...n,e.payload]});break;case r.TRACE:t=Object.assign(Object.assign({},t),{traces:qe(t.traces,e.payload.resourceSpans)})}}),t}(e)),{url:i,requestOptions:n,apiKey:o}=this.options,a=null!=n?n:{},{headers:l}=a,u=gt(a,["headers"]);let c;const d=this.metas.value.session;return null!=d&&(c=d.id),fetch(i,Object.assign({method:"POST",headers:Object.assign(Object.assign(Object.assign({"Content-Type":"application/json"},null!=l?l:{}),o?{"x-api-key":o}:{}),c?{"x-faro-session-id":c}:{}),body:t,keepalive:t.length<=6e4},null!=u?u:{})).then(e=>pt(this,void 0,void 0,function*(){if(202===e.status){"invalid"===e.headers.get("X-Faro-Session-Status")&&this.extendFaroSession(this.config,this.logDebug)}return 429===e.status&&(this.disabledUntil=this.getRetryAfterDate(e),this.logWarn(`Too many requests, backing off until ${this.disabledUntil}`)),e.text().catch(D),e})).catch(e=>{this.logError("Failed sending payload to the receiver\n",JSON.parse(t),e)})})}catch(e){this.logError(e)}})}getIgnoreUrls(){var e;return[this.options.url].concat(null!==(e=this.config.ignoreUrls)&&void 0!==e?e:[])}isBatched(){return!0}getRetryAfterDate(e){const t=this.getNow(),i=e.headers.get("Retry-After");if(i){const e=Number(i);if(!isNaN(e))return new Date(1e3*e+t);const n=Date.parse(i);if(!isNaN(n))return new Date(n)}return new Date(t+this.rateLimitBackoffMs)}extendFaroSession(e,t){const i="Session expired",n=e.sessionTracking;if(null==n?void 0:n.enabled){const{fetchUserSession:e,storeUserSession:r}=ft(n);lt({fetchUserSession:e,storeUserSession:r})({forceSessionExtend:!0}),t(`${i} created new session.`)}else t(`${i}.`)}}class vt extends je{constructor(){super(...arguments),this.api={},this.transports={}}}function bt(e=""){return H.transports.transports.flatMap(e=>e.getIgnoreUrls()).some(t=>e&&null!=e.match(t))}const Ot="fetch",wt="xhr";function yt(){const e=new n;function t(t){e.notify({type:Qe,request:t})}function i(t){e.notify({type:Ae,request:t})}return function({onRequestEnd:e,onRequestStart:t}){const i=window.fetch;window.fetch=function(){var n,r;const s=null!==(n=c(o=arguments[0])?o:o instanceof URL?o.href:!O(o)&&p(null==o?void 0:o.toString)?o.toString():void 0)&&void 0!==n?n:"";var o;const a=bt(s),l=(null!==(r=arguments[1])&&void 0!==r?r:{}).method,u=tt();return a||t({url:s,method:l,requestId:u,apiType:Ot}),i.apply(this,arguments).then(t=>(a||e({url:s,method:l,requestId:u,apiType:Ot}),t)).catch(t=>{throw a||e({url:s,method:l,requestId:u,apiType:Ot}),t})}}({onRequestStart:t,onRequestEnd:i}),function({onRequestStart:e,onRequestEnd:t}){const i=XMLHttpRequest.prototype.open;XMLHttpRequest.prototype.open=function(){const n=arguments[1],r=bt(n),s=arguments[0],o=tt();this.addEventListener("loadstart",function(){r||e({url:n,method:s,requestId:o,apiType:wt})}),this.addEventListener("load",function(){r||t({url:n,method:s,requestId:o,apiType:wt})}),this.addEventListener("error",function(){r||t({url:n,method:s,requestId:o,apiType:wt})}),this.addEventListener("abort",function(){r||t({url:n,method:s,requestId:o,apiType:wt})}),i.apply(this,arguments)}}({onRequestStart:t,onRequestEnd:i}),e}const $t="com.grafana.faro.lastNavigationId",xt="resource",kt=/^00-[a-f0-9]{32}-[a-f0-9]{16}-[0-9]{1,2}$/;function St(e=[]){for(const t of e)if("traceparent"===t.name){if(!kt.test(t.description))continue;const[,e,i]=t.description.split("-");if(null!=e&&null!=i)return{traceId:e,spanId:i};break}}function Pt(e,t={}){for(const[i,n]of Object.entries(t)){const t=e[i];return null!=t&&(g(n)?n.includes(t):t===n)}return!0}function Tt(e){const{connectEnd:t,connectStart:i,decodedBodySize:n,domainLookupEnd:r,domainLookupStart:s,duration:o,encodedBodySize:a,fetchStart:l,initiatorType:u,name:c,nextHopProtocol:d,redirectEnd:h,redirectStart:f,renderBlockingStatus:p,requestStart:g,responseEnd:m,responseStart:v,responseStatus:b,secureConnectionStart:O,transferSize:w,workerStart:y}=e;return{name:c,duration:zt(o),tcpHandshakeTime:zt(t-i),dnsLookupTime:zt(r-s),tlsNegotiationTime:zt(t-O),responseStatus:zt(b),redirectTime:zt(h-f),requestTime:zt(v-g),responseTime:zt(m-v),fetchTime:zt(m-l),serviceWorkerTime:zt(l-y),decodedBodySize:zt(n),encodedBodySize:zt(a),cacheHitStatus:function(){let e="fullLoad";0===w?n>0&&(e="cache"):null!=b?304===b&&(e="conditionalFetch"):a>0&&w<a&&(e="conditionalFetch");return e}(),renderBlockingStatus:zt(p),protocol:d,initiatorType:u,visibilityState:document.visibilityState,ttfb:zt(v-g),transferSize:zt(w)}}function Et(e){const{activationStart:t,domComplete:i,domContentLoadedEventEnd:n,domContentLoadedEventStart:r,domInteractive:s,fetchStart:o,loadEventEnd:a,loadEventStart:l,responseStart:u,type:c}=e,d=function(){var e;if(null!=(null===(e=performance.timing)||void 0===e?void 0:e.domLoading))return performance.timing.domLoading-performance.timeOrigin;return null}();return Object.assign(Object.assign({},Tt(e)),{pageLoadTime:zt(i-o),documentParsingTime:zt(d?s-d:null),domProcessingTime:zt(i-s),domContentLoadHandlerTime:zt(n-r),onLoadTime:zt(a-l),ttfb:zt(Math.max(u-(null!=t?t:0),0)),type:c})}function zt(e){return null==e?Ue:"number"==typeof e?Math.round(e>0?e:0).toString():e.toString()}const Qt={initiatorType:["xmlhttprequest","fetch"]};var At=function(e,t,i,n){return new(i||(i=Promise))(function(r,s){function o(e){try{l(n.next(e))}catch(e){s(e)}}function a(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof i?t:new i(function(e){e(t)})).then(o,a)}l((n=n.apply(e,t||[])).next())})};const Ct=new n;class It extends vt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-performance",this.version=Z}initialize(){"PerformanceObserver"in window?function(e){if("complete"===document.readyState)e();else{const t=()=>{"complete"===document.readyState&&(e(),document.removeEventListener("readystatechange",t))};document.addEventListener("readystatechange",t)}}(()=>At(this,void 0,void 0,function*(){const e=this.api.pushEvent,{faroNavigationId:t}=yield function(e){let t;const i=new Promise(e=>{t=e});return new PerformanceObserver(i=>{var n;const[r]=i.getEntries();if(null==r||bt(r.name))return;const s=r.toJSON();let o=St(null==s?void 0:s.serverTiming);const a=null!==(n=Ye($t,Be))&&void 0!==n?n:Ue,l=Object.assign(Object.assign({},Et(s)),{faroNavigationId:tt(),faroPreviousNavigationId:a});Ze($t,l.faroNavigationId,Be),e("faro.performance.navigation",l,void 0,{spanContext:o,timestampOverwriteMs:performance.timeOrigin+s.startTime}),t(l)}).observe({type:"navigation",buffered:!0}),i}(e);null!=t&&function(e,t,i){const n=H.config.trackResources;new PerformanceObserver(r=>{const s=r.getEntries();for(const r of s){if(bt(r.name))return;const s=r.toJSON();let o=St(null==s?void 0:s.serverTiming);if(null==n&&Pt(s,Qt)||n){const n=Object.assign(Object.assign({},Tt(s)),{faroNavigationId:e,faroResourceId:tt()});H.config.trackUserActionsPreview&&(null==i||i.notify({type:xt})),t("faro.performance.resource",n,void 0,{spanContext:o,timestampOverwriteMs:performance.timeOrigin+s.startTime})}}}).observe({type:xt,buffered:!0})}(t,e,Ct)})):this.logDebug("performance observer not supported. Disable performance instrumentation.")}}function Xt(e){const{api:t,config:i}=e,r=yt(),s=function(){const e=new n;return new MutationObserver((t,i)=>{e.notify({type:"dom-mutation"})}).observe(document,{attributes:!0,childList:!0,subtree:!0,characterData:!0}),e}(),o=function(){const e=new n;return Ct.subscribe(t=>{t.type===xt&&e.notify({type:"resource-entry"})}),e}();let a,l=!1;return function(e){var u;let c;const d=(h=e).type===Ie&&"string"==typeof h.name;var h;if(c=d?e.name:function(e,t){const i=function(e){const t=e.split("data-")[1],i=null==t?void 0:t.replace(/-(.)/g,(e,t)=>t.toUpperCase());return null==i?void 0:i.replace(/-/g,"")}(t),n=e.dataset;for(const e in n)if(e===i)return n[e];return}(e.target,null!==(u=i.trackUserActionsDataAttributeName)&&void 0!==u?u:"faroUserActionName"),l||null==c)return;l=!0;const f=$();let p;const g=tt();L.notify({type:P,name:c,startTime:f,parentId:g}),a=_t(a,()=>{p=$(),l=!1,function(e,t){L.notify({type:E,name:e,parentId:t})}(c,g)},100);const m=new Map;let v,b=!1;const O=(new n).merge(r,s,o).takeWhile(()=>l).filter(e=>!!(!b||Lt(e)&&m.has(e.request.requestId))).subscribe(i=>{(function(e){return e.type===Qe})(i)&&m.set(i.request.requestId,i.request),Lt(i)&&m.delete(i.request.requestId),a=_t(a,()=>{p=$();const i=Object.assign({api:t,userActionName:c,startTime:f,endTime:p,actionId:g,event:e},d?{attributes:e.attributes}:{}),n=m.size>0;b&&!n&&(clearTimeout(v),b=!1),n?(b=!0,L.notify({type:z,name:c,parentId:g,reason:"pending-requests",haltTime:$()}),v=_t(void 0,()=>{Rt(O),Ut(i),l=!1,b=!1},1e4)):(Rt(O),Ut(i),l=!1,b=!1)},100)})}}function Ut(e){const{api:t,userActionName:i,startTime:n,endTime:r,actionId:s,event:o,attributes:a}=e,l=r-n,u=o.type;L.notify({type:T,name:i,id:s,startTime:n,endTime:r,duration:l,eventType:u}),t.pushEvent(i,Object.assign({userActionStartTime:n.toString(),userActionEndTime:r.toString(),userActionDuration:l.toString(),userActionTrigger:u},y(a)),void 0,{timestampOverwriteMs:n,customPayloadTransformer:e=>(e.action={id:s,name:i},e)})}function _t(e,t,i){return e&&clearTimeout(e),e=setTimeout(()=>{t()},i)}function Rt(e){null==e||e.unsubscribe(),e=void 0}function Lt(e){return e.type===Ae}let Nt;class Dt extends vt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-user-action",this.version=Z}initialize(){Nt=Xt(H),window.addEventListener("pointerdown",Nt),window.addEventListener("keydown",Nt)}}const jt=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function Mt(e){let t,i,n,r,s=[];if((e=>a(e,"ErrorEvent"))(e)&&e.error)t=e.error.message,i=e.error.name,s=Se(e.error);else if((n=(e=>a(e,"DOMError"))(e))||(e=>a(e,"DOMException"))(e)){const{name:r,message:s}=e;i=null!=r?r:n?"DOMError":"DOMException",t=s?`${i}: ${s}`:i}else b(e)?(t=e.message,s=Se(e)):(f(e)||(r=(e=>m&&l(e,Event))(e)))&&(i=r?e.constructor.name:void 0,t=`Non-Error exception captured with keys: ${Object.keys(e)}`);return[t,i,s]}function qt(e){const[t,i,n,r,s]=e;let o,a,l=[];const u=c(t),d=xe(i,"?",n,r);return s||!u?([o,a,l]=Mt(null!=s?s:t),0===l.length&&(l=[d])):u&&([o,a]=function(e){var t,i;const n=e.match(jt),r=null!==(t=null==n?void 0:n[1])&&void 0!==t?t:Q;return[null!==(i=null==n?void 0:n[2])&&void 0!==i?i:e,r]}(t),l=[d]),{value:o,type:a,stackFrames:l}}function Ft(e,t){return b(e[0])?qt(e):{value:t(e)}}function Bt(e){window.addEventListener("unhandledrejection",t=>{var i,n;let r,s,o=t;o.reason?o=t.reason:(null===(i=t.detail)||void 0===i?void 0:i.reason)&&(o=null===(n=t.detail)||void 0===n?void 0:n.reason);let a=[];(e=>!f(e)&&!p(e))(o)?(r=`Non-Error promise rejection captured with value: ${String(o)}`,s="UnhandledRejection"):[r,s,a]=Mt(o),r&&e.pushError(new Error(r),{type:s,stackFrames:a})})}class Vt extends vt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-errors",this.version=Z}initialize(){this.logDebug("Initializing"),function(e){const t=window.onerror;window.onerror=(...i)=>{try{const{value:t,type:n,stackFrames:r}=qt(i),s=i[4];if(t){const i={type:n,stackFrames:r};null!=s&&(i.originalError=s),e.pushError(new Error(t),i)}}finally{null==t||t.apply(window,i)}}}(this.api),Bt(this.api)}}var Wt,Yt,Zt,Gt,Ht,Kt=-1,Jt=function(e){addEventListener("pageshow",function(t){t.persisted&&(Kt=t.timeStamp,e(t))},!0)},ei=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},ti=function(){var e=ei();return e&&e.activationStart||0},ii=function(e,t){var i=ei(),n="navigate";return Kt>=0?n="back-forward-cache":i&&(document.prerendering||ti()>0?n="prerender":document.wasDiscarded?n="restore":i.type&&(n=i.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:n}},ni=function(e,t,i){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var n=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})});return n.observe(Object.assign({type:e,buffered:!0},i||{})),n}}catch(e){}},ri=function(e,t,i,n){var r,s;return function(o){t.value>=0&&(o||n)&&((s=t.value-(r||0))||void 0===r)&&(r=t.value,t.delta=s,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,i),e(t))}},si=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},oi=function(e){document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&e()})},ai=function(e){var t=!1;return function(){t||(e(),t=!0)}},li=-1,ui=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},ci=function(e){"hidden"===document.visibilityState&&li>-1&&(li="visibilitychange"===e.type?e.timeStamp:0,hi())},di=function(){addEventListener("visibilitychange",ci,!0),addEventListener("prerenderingchange",ci,!0)},hi=function(){removeEventListener("visibilitychange",ci,!0),removeEventListener("prerenderingchange",ci,!0)},fi=function(){return li<0&&(li=ui(),di(),Jt(function(){setTimeout(function(){li=ui(),di()},0)})),{get firstHiddenTime(){return li}}},pi=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},gi=[1800,3e3],mi=function(e,t){t=t||{},pi(function(){var i,n=fi(),r=ii("FCP"),s=ni("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<n.firstHiddenTime&&(r.value=Math.max(e.startTime-ti(),0),r.entries.push(e),i(!0)))})});s&&(i=ri(e,r,gi,t.reportAllChanges),Jt(function(n){r=ii("FCP"),i=ri(e,r,gi,t.reportAllChanges),si(function(){r.value=performance.now()-n.timeStamp,i(!0)})}))})},vi=[.1,.25],bi=0,Oi=1/0,wi=0,yi=function(e){e.forEach(function(e){e.interactionId&&(Oi=Math.min(Oi,e.interactionId),wi=Math.max(wi,e.interactionId),bi=wi?(wi-Oi)/7+1:0)})},$i=function(){return Wt?bi:performance.interactionCount||0},xi=function(){"interactionCount"in performance||Wt||(Wt=ni("event",yi,{type:"event",buffered:!0,durationThreshold:0}))},ki=[],Si=new Map,Pi=0,Ti=[],Ei=function(e){if(Ti.forEach(function(t){return t(e)}),e.interactionId||"first-input"===e.entryType){var t=ki[ki.length-1],i=Si.get(e.interactionId);if(i||ki.length<10||e.duration>t.latency){if(i)e.duration>i.latency?(i.entries=[e],i.latency=e.duration):e.duration===i.latency&&e.startTime===i.entries[0].startTime&&i.entries.push(e);else{var n={id:e.interactionId,latency:e.duration,entries:[e]};Si.set(n.id,n),ki.push(n)}ki.sort(function(e,t){return t.latency-e.latency}),ki.length>10&&ki.splice(10).forEach(function(e){return Si.delete(e.id)})}}},zi=function(e){var t=self.requestIdleCallback||self.setTimeout,i=-1;return e=ai(e),"hidden"===document.visibilityState?e():(i=t(e),oi(e)),i},Qi=[200,500],Ai=[2500,4e3],Ci={},Ii=[800,1800],Xi=function e(t){document.prerendering?pi(function(){return e(t)}):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},Ui={passive:!0,capture:!0},_i=new Date,Ri=function(e,t){Yt||(Yt=t,Zt=e,Gt=new Date,Di(removeEventListener),Li())},Li=function(){if(Zt>=0&&Zt<Gt-_i){var e={entryType:"first-input",name:Yt.type,target:Yt.target,cancelable:Yt.cancelable,startTime:Yt.timeStamp,processingStart:Yt.timeStamp+Zt};Ht.forEach(function(t){t(e)}),Ht=[]}},Ni=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var i=function(){Ri(e,t),r()},n=function(){r()},r=function(){removeEventListener("pointerup",i,Ui),removeEventListener("pointercancel",n,Ui)};addEventListener("pointerup",i,Ui),addEventListener("pointercancel",n,Ui)}(t,e):Ri(t,e)}},Di=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,Ni,Ui)})},ji=[100,300];class Mi{constructor(e,t){this.pushMeasurement=e,this.webVitalConfig=t}initialize(){Object.entries(Mi.mapping).forEach(([e,t])=>{var i;t(t=>{this.pushMeasurement({type:"web-vitals",values:{[e]:t.value}})},{reportAllChanges:null===(i=this.webVitalConfig)||void 0===i?void 0:i.reportAllChanges})})}}Mi.mapping={cls:function(e,t){t=t||{},mi(ai(function(){var i,n=ii("CLS",0),r=0,s=[],o=function(e){e.forEach(function(e){if(!e.hadRecentInput){var t=s[0],i=s[s.length-1];r&&e.startTime-i.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,s.push(e)):(r=e.value,s=[e])}}),r>n.value&&(n.value=r,n.entries=s,i())},a=ni("layout-shift",o);a&&(i=ri(e,n,vi,t.reportAllChanges),oi(function(){o(a.takeRecords()),i(!0)}),Jt(function(){r=0,n=ii("CLS",0),i=ri(e,n,vi,t.reportAllChanges),si(function(){return i()})}),setTimeout(i,0))}))},fcp:mi,fid:function(e,t){t=t||{},pi(function(){var i,n=fi(),r=ii("FID"),s=function(e){e.startTime<n.firstHiddenTime&&(r.value=e.processingStart-e.startTime,r.entries.push(e),i(!0))},o=function(e){e.forEach(s)},a=ni("first-input",o);i=ri(e,r,ji,t.reportAllChanges),a&&(oi(ai(function(){o(a.takeRecords()),a.disconnect()})),Jt(function(){var n;r=ii("FID"),i=ri(e,r,ji,t.reportAllChanges),Ht=[],Zt=-1,Yt=null,Di(addEventListener),n=s,Ht.push(n),Li()}))})},inp:function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},pi(function(){var i;xi();var n,r=ii("INP"),s=function(e){zi(function(){e.forEach(Ei);var t=function(){var e=Math.min(ki.length-1,Math.floor(($i()-Pi)/50));return ki[e]}();t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,n())})},o=ni("event",s,{durationThreshold:null!==(i=t.durationThreshold)&&void 0!==i?i:40});n=ri(e,r,Qi,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),oi(function(){s(o.takeRecords()),n(!0)}),Jt(function(){Pi=$i(),ki.length=0,Si.clear(),r=ii("INP"),n=ri(e,r,Qi,t.reportAllChanges)}))}))},lcp:function(e,t){t=t||{},pi(function(){var i,n=fi(),r=ii("LCP"),s=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach(function(e){e.startTime<n.firstHiddenTime&&(r.value=Math.max(e.startTime-ti(),0),r.entries=[e],i())})},o=ni("largest-contentful-paint",s);if(o){i=ri(e,r,Ai,t.reportAllChanges);var a=ai(function(){Ci[r.id]||(s(o.takeRecords()),o.disconnect(),Ci[r.id]=!0,i(!0))});["keydown","click"].forEach(function(e){addEventListener(e,function(){return zi(a)},{once:!0,capture:!0})}),oi(a),Jt(function(n){r=ii("LCP"),i=ri(e,r,Ai,t.reportAllChanges),si(function(){r.value=performance.now()-n.timeStamp,Ci[r.id]=!0,i(!0)})})}})},ttfb:function(e,t){t=t||{};var i=ii("TTFB"),n=ri(e,i,Ii,t.reportAllChanges);Xi(function(){var r=ei();r&&(i.value=Math.max(r.responseStart-ti(),0),i.entries=[r],n(!0),Jt(function(){i=ii("TTFB",0),(n=ri(e,i,Ii,t.reportAllChanges))(!0)}))})}};var qi,Fi,Bi=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},Vi=function(e){if("loading"===document.readyState)return"loading";var t=Bi();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},Wi=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},Yi=function(e,t){var i="";try{for(;e&&9!==e.nodeType;){var n=e,r=n.id?"#"+n.id:Wi(n)+(n.classList&&n.classList.value&&n.classList.value.trim()&&n.classList.value.trim().length?"."+n.classList.value.trim().replace(/\s+/g,"."):"");if(i.length+r.length>(t||100)-1)return i||r;if(i=i?r+">"+i:r,n.id)break;e=n.parentNode}}catch(e){}return i},Zi=-1,Gi=function(){return Zi},Hi=function(e){addEventListener("pageshow",function(t){t.persisted&&(Zi=t.timeStamp,e(t))},!0)},Ki=function(){var e=Bi();return e&&e.activationStart||0},Ji=function(e,t){var i=Bi(),n="navigate";return Gi()>=0?n="back-forward-cache":i&&(document.prerendering||Ki()>0?n="prerender":document.wasDiscarded?n="restore":i.type&&(n=i.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:n}},en=function(e,t,i){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var n=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})});return n.observe(Object.assign({type:e,buffered:!0},i||{})),n}}catch(e){}},tn=function(e,t,i,n){var r,s;return function(o){t.value>=0&&(o||n)&&((s=t.value-(r||0))||void 0===r)&&(r=t.value,t.delta=s,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,i),e(t))}},nn=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},rn=function(e){document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&e()})},sn=function(e){var t=!1;return function(){t||(e(),t=!0)}},on=-1,an=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},ln=function(e){"hidden"===document.visibilityState&&on>-1&&(on="visibilitychange"===e.type?e.timeStamp:0,cn())},un=function(){addEventListener("visibilitychange",ln,!0),addEventListener("prerenderingchange",ln,!0)},cn=function(){removeEventListener("visibilitychange",ln,!0),removeEventListener("prerenderingchange",ln,!0)},dn=function(){return on<0&&(on=an(),un(),Hi(function(){setTimeout(function(){on=an(),un()},0)})),{get firstHiddenTime(){return on}}},hn=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},fn=[1800,3e3],pn=function(e,t){t=t||{},hn(function(){var i,n=dn(),r=Ji("FCP"),s=en("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<n.firstHiddenTime&&(r.value=Math.max(e.startTime-Ki(),0),r.entries.push(e),i(!0)))})});s&&(i=tn(e,r,fn,t.reportAllChanges),Hi(function(n){r=Ji("FCP"),i=tn(e,r,fn,t.reportAllChanges),nn(function(){r.value=performance.now()-n.timeStamp,i(!0)})}))})},gn=[.1,.25],mn=0,vn=1/0,bn=0,On=function(e){e.forEach(function(e){e.interactionId&&(vn=Math.min(vn,e.interactionId),bn=Math.max(bn,e.interactionId),mn=bn?(bn-vn)/7+1:0)})},wn=function(){return qi?mn:performance.interactionCount||0},yn=[],$n=new Map,xn=0,kn=[],Sn=function(e){if(kn.forEach(function(t){return t(e)}),e.interactionId||"first-input"===e.entryType){var t=yn[yn.length-1],i=$n.get(e.interactionId);if(i||yn.length<10||e.duration>t.latency){if(i)e.duration>i.latency?(i.entries=[e],i.latency=e.duration):e.duration===i.latency&&e.startTime===i.entries[0].startTime&&i.entries.push(e);else{var n={id:e.interactionId,latency:e.duration,entries:[e]};$n.set(n.id,n),yn.push(n)}yn.sort(function(e,t){return t.latency-e.latency}),yn.length>10&&yn.splice(10).forEach(function(e){return $n.delete(e.id)})}}},Pn=function(e){var t=self.requestIdleCallback||self.setTimeout,i=-1;return e=sn(e),"hidden"===document.visibilityState?e():(i=t(e),rn(e)),i},Tn=[200,500],En=function(e,t){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(t=t||{},hn(function(){var i;"interactionCount"in performance||qi||(qi=en("event",On,{type:"event",buffered:!0,durationThreshold:0}));var n,r=Ji("INP"),s=function(e){Pn(function(){e.forEach(Sn);var t=function(){var e=Math.min(yn.length-1,Math.floor((wn()-xn)/50));return yn[e]}();t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,n())})},o=en("event",s,{durationThreshold:null!==(i=t.durationThreshold)&&void 0!==i?i:40});n=tn(e,r,Tn,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),rn(function(){s(o.takeRecords()),n(!0)}),Hi(function(){xn=wn(),yn.length=0,$n.clear(),r=Ji("INP"),n=tn(e,r,Tn,t.reportAllChanges)}))}))},zn=[],Qn=[],An=0,Cn=new WeakMap,In=new Map,Xn=-1,Un=function(e){zn=zn.concat(e),_n()},_n=function(){Xn<0&&(Xn=Pn(Rn))},Rn=function(){In.size>10&&In.forEach(function(e,t){$n.has(t)||In.delete(t)});var e=yn.map(function(e){return Cn.get(e.entries[0])}),t=Qn.length-50;Qn=Qn.filter(function(i,n){return n>=t||e.includes(i)});for(var i=new Set,n=0;n<Qn.length;n++){var r=Qn[n];Mn(r.startTime,r.processingEnd).forEach(function(e){i.add(e)})}var s=zn.length-1-50;zn=zn.filter(function(e,t){return e.startTime>An&&t>s||i.has(e)}),Xn=-1};kn.push(function(e){e.interactionId&&e.target&&!In.has(e.interactionId)&&In.set(e.interactionId,e.target)},function(e){var t,i=e.startTime+e.duration;An=Math.max(An,e.processingEnd);for(var n=Qn.length-1;n>=0;n--){var r=Qn[n];if(Math.abs(i-r.renderTime)<=8){(t=r).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:i,entries:[e]},Qn.push(t)),(e.interactionId||"first-input"===e.entryType)&&Cn.set(e,t),_n()});var Ln,Nn,Dn,jn,Mn=function(e,t){for(var i,n=[],r=0;i=zn[r];r++)if(!(i.startTime+i.duration<e)){if(i.startTime>t)break;n.push(i)}return n},qn=[2500,4e3],Fn={},Bn=[800,1800],Vn=function e(t){document.prerendering?hn(function(){return e(t)}):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},Wn=function(e,t){t=t||{};var i=Ji("TTFB"),n=tn(e,i,Bn,t.reportAllChanges);Vn(function(){var r=Bi();r&&(i.value=Math.max(r.responseStart-Ki(),0),i.entries=[r],n(!0),Hi(function(){i=Ji("TTFB",0),(n=tn(e,i,Bn,t.reportAllChanges))(!0)}))})},Yn={passive:!0,capture:!0},Zn=new Date,Gn=function(e,t){Ln||(Ln=t,Nn=e,Dn=new Date,Jn(removeEventListener),Hn())},Hn=function(){if(Nn>=0&&Nn<Dn-Zn){var e={entryType:"first-input",name:Ln.type,target:Ln.target,cancelable:Ln.cancelable,startTime:Ln.timeStamp,processingStart:Ln.timeStamp+Nn};jn.forEach(function(t){t(e)}),jn=[]}},Kn=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var i=function(){Gn(e,t),r()},n=function(){r()},r=function(){removeEventListener("pointerup",i,Yn),removeEventListener("pointercancel",n,Yn)};addEventListener("pointerup",i,Yn),addEventListener("pointercancel",n,Yn)}(t,e):Gn(t,e)}},Jn=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,Kn,Yn)})},er=[100,300],tr=function(e,t){!function(e,t){t=t||{},hn(function(){var i,n=dn(),r=Ji("FID"),s=function(e){e.startTime<n.firstHiddenTime&&(r.value=e.processingStart-e.startTime,r.entries.push(e),i(!0))},o=function(e){e.forEach(s)},a=en("first-input",o);i=tn(e,r,er,t.reportAllChanges),a&&(rn(sn(function(){o(a.takeRecords()),a.disconnect()})),Hi(function(){var n;r=Ji("FID"),i=tn(e,r,er,t.reportAllChanges),jn=[],Nn=-1,Ln=null,Jn(addEventListener),n=s,jn.push(n),Hn()}))})}(function(t){var i=function(e){var t=e.entries[0],i={eventTarget:Yi(t.target),eventType:t.name,eventTime:t.startTime,eventEntry:t,loadState:Vi(t.startTime)};return Object.assign(e,{attribution:i})}(t);e(i)},t)};const ir="load_state",nr="time_to_first_byte";class rr{constructor(e,t){this.corePushMeasurement=e,this.webVitalConfig=t}initialize(){this.measureCLS(),this.measureFCP(),this.measureFID(),this.measureINP(),this.measureLCP(),this.measureTTFB()}measureCLS(){var e;!function(e,t){!function(e,t){t=t||{},pn(sn(function(){var i,n=Ji("CLS",0),r=0,s=[],o=function(e){e.forEach(function(e){if(!e.hadRecentInput){var t=s[0],i=s[s.length-1];r&&e.startTime-i.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,s.push(e)):(r=e.value,s=[e])}}),r>n.value&&(n.value=r,n.entries=s,i())},a=en("layout-shift",o);a&&(i=tn(e,n,gn,t.reportAllChanges),rn(function(){o(a.takeRecords()),i(!0)}),Hi(function(){r=0,n=Ji("CLS",0),i=tn(e,n,gn,t.reportAllChanges),nn(function(){return i()})}),setTimeout(i,0))}))}(function(t){var i=function(e){var t,i={};if(e.entries.length){var n=e.entries.reduce(function(e,t){return e&&e.value>t.value?e:t});if(n&&n.sources&&n.sources.length){var r=(t=n.sources).find(function(e){return e.node&&1===e.node.nodeType})||t[0];r&&(i={largestShiftTarget:Yi(r.node),largestShiftTime:n.startTime,largestShiftValue:n.value,largestShiftSource:r,largestShiftEntry:n,loadState:Vi(n.startTime)})}}return Object.assign(e,{attribution:i})}(t);e(i)},t)}(e=>{const{loadState:t,largestShiftValue:i,largestShiftTime:n,largestShiftTarget:r}=e.attribution,s=this.buildInitialValues(e);this.addIfPresent(s,"largest_shift_value",i),this.addIfPresent(s,"largest_shift_time",n);const o=this.buildInitialContext(e);this.addIfPresent(o,ir,t),this.addIfPresent(o,"largest_shift_target",r),this.pushMeasurement(s,o)},{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureFCP(){var e;!function(e,t){pn(function(t){var i=function(e){var t={timeToFirstByte:0,firstByteToFCP:e.value,loadState:Vi(Gi())};if(e.entries.length){var i=Bi(),n=e.entries[e.entries.length-1];if(i){var r=i.activationStart||0,s=Math.max(0,i.responseStart-r);t={timeToFirstByte:s,firstByteToFCP:e.value-s,loadState:Vi(e.entries[0].startTime),navigationEntry:i,fcpEntry:n}}}return Object.assign(e,{attribution:t})}(t);e(i)},t)}(e=>{const{firstByteToFCP:t,timeToFirstByte:i,loadState:n}=e.attribution,r=this.buildInitialValues(e);this.addIfPresent(r,"first_byte_to_fcp",t),this.addIfPresent(r,nr,i);const s=this.buildInitialContext(e);this.addIfPresent(s,ir,n),this.pushMeasurement(r,s)},{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureFID(){var e;tr(e=>{const{eventTime:t,eventTarget:i,eventType:n,loadState:r}=e.attribution,s=this.buildInitialValues(e);this.addIfPresent(s,"event_time",t);const o=this.buildInitialContext(e);this.addIfPresent(o,"event_target",i),this.addIfPresent(o,"event_type",n),this.addIfPresent(o,ir,r),this.pushMeasurement(s,o)},{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureINP(){var e;!function(e,t){Fi||(Fi=en("long-animation-frame",Un)),En(function(t){var i=function(e){var t=e.entries[0],i=Cn.get(t),n=t.processingStart,r=i.processingEnd,s=i.entries.sort(function(e,t){return e.processingStart-t.processingStart}),o=Mn(t.startTime,r),a=e.entries.find(function(e){return e.target}),l=a&&a.target||In.get(t.interactionId),u=[t.startTime+t.duration,r].concat(o.map(function(e){return e.startTime+e.duration})),c=Math.max.apply(Math,u),d={interactionTarget:Yi(l),interactionTargetElement:l,interactionType:t.name.startsWith("key")?"keyboard":"pointer",interactionTime:t.startTime,nextPaintTime:c,processedEventEntries:s,longAnimationFrameEntries:o,inputDelay:n-t.startTime,processingDuration:r-n,presentationDelay:Math.max(c-r,0),loadState:Vi(t.startTime)};return Object.assign(e,{attribution:d})}(t);e(i)},t)}(e=>{const{interactionTime:t,presentationDelay:i,inputDelay:n,processingDuration:r,nextPaintTime:s,loadState:o,interactionTarget:a,interactionType:l}=e.attribution,u=this.buildInitialValues(e);this.addIfPresent(u,"interaction_time",t),this.addIfPresent(u,"presentation_delay",i),this.addIfPresent(u,"input_delay",n),this.addIfPresent(u,"processing_duration",r),this.addIfPresent(u,"next_paint_time",s);const c=this.buildInitialContext(e);this.addIfPresent(c,ir,o),this.addIfPresent(c,"interaction_target",a),this.addIfPresent(c,"interaction_type",l),this.pushMeasurement(u,c)},{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureLCP(){var e;!function(e,t){!function(e,t){t=t||{},hn(function(){var i,n=dn(),r=Ji("LCP"),s=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach(function(e){e.startTime<n.firstHiddenTime&&(r.value=Math.max(e.startTime-Ki(),0),r.entries=[e],i())})},o=en("largest-contentful-paint",s);if(o){i=tn(e,r,qn,t.reportAllChanges);var a=sn(function(){Fn[r.id]||(s(o.takeRecords()),o.disconnect(),Fn[r.id]=!0,i(!0))});["keydown","click"].forEach(function(e){addEventListener(e,function(){return Pn(a)},{once:!0,capture:!0})}),rn(a),Hi(function(n){r=Ji("LCP"),i=tn(e,r,qn,t.reportAllChanges),nn(function(){r.value=performance.now()-n.timeStamp,Fn[r.id]=!0,i(!0)})})}})}(function(t){var i=function(e){var t={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:e.value};if(e.entries.length){var i=Bi();if(i){var n=i.activationStart||0,r=e.entries[e.entries.length-1],s=r.url&&performance.getEntriesByType("resource").filter(function(e){return e.name===r.url})[0],o=Math.max(0,i.responseStart-n),a=Math.max(o,s?(s.requestStart||s.startTime)-n:0),l=Math.max(a,s?s.responseEnd-n:0),u=Math.max(l,r.startTime-n);t={element:Yi(r.element),timeToFirstByte:o,resourceLoadDelay:a-o,resourceLoadDuration:l-a,elementRenderDelay:u-l,navigationEntry:i,lcpEntry:r},r.url&&(t.url=r.url),s&&(t.lcpResourceEntry=s)}}return Object.assign(e,{attribution:t})}(t);e(i)},t)}(e=>{const{elementRenderDelay:t,resourceLoadDelay:i,resourceLoadDuration:n,timeToFirstByte:r,element:s}=e.attribution,o=this.buildInitialValues(e);this.addIfPresent(o,"element_render_delay",t),this.addIfPresent(o,"resource_load_delay",i),this.addIfPresent(o,"resource_load_duration",n),this.addIfPresent(o,nr,r);const a=this.buildInitialContext(e);this.addIfPresent(a,"element",s),this.pushMeasurement(o,a)},{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}measureTTFB(){var e;!function(e,t){Wn(function(t){var i=function(e){var t={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(e.entries.length){var i=e.entries[0],n=i.activationStart||0,r=Math.max((i.workerStart||i.fetchStart)-n,0),s=Math.max(i.domainLookupStart-n,0),o=Math.max(i.connectStart-n,0),a=Math.max(i.connectEnd-n,0);t={waitingDuration:r,cacheDuration:s-r,dnsDuration:o-s,connectionDuration:a-o,requestDuration:e.value-a,navigationEntry:i}}return Object.assign(e,{attribution:t})}(t);e(i)},t)}(e=>{const{dnsDuration:t,connectionDuration:i,requestDuration:n,waitingDuration:r,cacheDuration:s}=e.attribution,o=this.buildInitialValues(e);this.addIfPresent(o,"dns_duration",t),this.addIfPresent(o,"connection_duration",i),this.addIfPresent(o,"request_duration",n),this.addIfPresent(o,"waiting_duration",r),this.addIfPresent(o,"cache_duration",s);const a=this.buildInitialContext(e);this.pushMeasurement(o,a)},{reportAllChanges:null===(e=this.webVitalConfig)||void 0===e?void 0:e.reportAllChanges})}buildInitialValues(e){const t=e.name.toLowerCase();return{[t]:e.value,delta:e.delta}}buildInitialContext(e){var t;const i=null!==(t=Ye($t,Be))&&void 0!==t?t:Ue;return{id:e.id,rating:e.rating,navigation_type:e.navigationType,navigation_entry_id:i}}pushMeasurement(e,t){this.corePushMeasurement({type:"web-vitals",values:e},{context:t})}addIfPresent(e,t,i){i&&(e[t]=i)}}class sr extends vt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-web-vitals",this.version=Z}initialize(){this.logDebug("Initializing");this.intializeWebVitalsInstrumentation().initialize()}intializeWebVitalsInstrumentation(){var e,t,i;return!1===(null===(e=this.config)||void 0===e?void 0:e.trackWebVitalsAttribution)||!1===(null===(i=null===(t=this.config)||void 0===t?void 0:t.webVitalsInstrumentation)||void 0===i?void 0:i.trackAttribution)?new Mi(this.api.pushMeasurement,this.config.webVitalsInstrumentation):new rr(this.api.pushMeasurement,this.config.webVitalsInstrumentation)}}class or extends vt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-session",this.version=Z}sendSessionStartEvent(e){var t,i;const n=e.session;if(n&&n.id!==(null===(t=this.notifiedSession)||void 0===t?void 0:t.id)){if(this.notifiedSession&&this.notifiedSession.id===(null===(i=n.attributes)||void 0===i?void 0:i.previousSession))return this.api.pushEvent("session_extend",{},void 0,{skipDedupe:!0}),void(this.notifiedSession=n);this.notifiedSession=n,this.api.pushEvent(it,{},void 0,{skipDedupe:!0})}}createInitialSession(e,t){var i,n,r,s,o,a;let l,u,c=e.fetchUserSession();if(t.persistent&&t.maxSessionPersistenceTime&&c){const e=$();c.lastActivity<e-t.maxSessionPersistenceTime&&(dt.removeUserSession(),c=null)}if(at(c)){const e=null==c?void 0:c.sessionId;u=ot({sessionId:e,isSampled:c.isSampled||!1,started:null==c?void 0:c.started});const r=null==c?void 0:c.sessionMeta,s=Object.assign(Object.assign({},null===(i=t.session)||void 0===i?void 0:i.overrides),null==r?void 0:r.overrides);u.sessionMeta=Object.assign(Object.assign({},t.session),{id:e,attributes:Object.assign(Object.assign(Object.assign({},null===(n=t.session)||void 0===n?void 0:n.attributes),null==r?void 0:r.attributes),{isSampled:u.isSampled.toString()}),overrides:s}),l=nt}else{const e=null!==(s=null===(r=t.session)||void 0===r?void 0:r.id)&&void 0!==s?s:function(e){var t,i,n,r;return{id:null!==(r=null===(n=null===(i=null===(t=H.config)||void 0===t?void 0:t.sessionTracking)||void 0===i?void 0:i.generateSessionId)||void 0===n?void 0:n.call(i))&&void 0!==r?r:tt(),attributes:e}}().id;u=ot({sessionId:e,isSampled:st()});const i=null===(o=t.session)||void 0===o?void 0:o.overrides;u.sessionMeta=Object.assign({id:e,attributes:Object.assign({isSampled:u.isSampled.toString()},null===(a=t.session)||void 0===a?void 0:a.attributes)},i?{overrides:i}:{}),l=it}return{initialSession:u,lifecycleType:l}}registerBeforeSendHook(e){var t;const{updateSession:i}=new e;null===(t=this.transports)||void 0===t||t.addBeforeSendHooks(e=>{var t,n,r;i();const s=null===(t=e.meta.session)||void 0===t?void 0:t.attributes;if(s&&"true"===(null==s?void 0:s.isSampled)){let t=JSON.parse(JSON.stringify(e));const i=null===(n=t.meta.session)||void 0===n?void 0:n.attributes;return null==i||delete i.isSampled,0===Object.keys(null!=i?i:{}).length&&(null===(r=t.meta.session)||void 0===r||delete r.attributes),t}return null})}initialize(){this.logDebug("init session instrumentation");const e=this.config.sessionTracking;if(null==e?void 0:e.enabled){const t=ft(e);this.registerBeforeSendHook(t);const{initialSession:i,lifecycleType:n}=this.createInitialSession(t,e);t.storeUserSession(i);const r=i.sessionMeta;this.notifiedSession=r,this.api.setSession(r),n===it&&this.api.pushEvent(it,{},void 0,{skipDedupe:!0}),n===nt&&this.api.pushEvent(nt,{},void 0,{skipDedupe:!0})}this.metas.addListener(this.sendSessionStartEvent.bind(this))}}class ar extends vt{constructor(){super(...arguments),this.name="@grafana/faro-web-sdk:instrumentation-view",this.version=Z}sendViewChangedEvent(e){var t,i,n,r;const s=e.view;s&&s.name!==(null===(t=this.notifiedView)||void 0===t?void 0:t.name)&&(this.api.pushEvent("view_changed",{fromView:null!==(n=null===(i=this.notifiedView)||void 0===i?void 0:i.name)&&void 0!==n?n:Ue,toView:null!==(r=s.name)&&void 0!==r?r:Ue},void 0,{skipDedupe:!0}),this.notifiedView=s)}initialize(){this.metas.addListener(this.sendViewChangedEvent.bind(this))}}class lr extends vt{constructor(){super(),this.name="@grafana/faro-web-sdk:instrumentation-csp",this.version=Z}initialize(){document.addEventListener("securitypolicyviolation",this.securitypolicyviolationHandler.bind(this))}destroy(){document.removeEventListener("securitypolicyviolation",this.securitypolicyviolationHandler)}securitypolicyviolationHandler(e){this.api.pushEvent("securitypolicyviolation",y(e))}}class ur extends vt{constructor(e={}){super(),this.options=e,this.name="@grafana/faro-web-sdk:instrumentation-console",this.version=Z,this.errorSerializer=U}initialize(){var e,t,i,n;this.options=Object.assign(Object.assign({},this.options),this.config.consoleInstrumentation);const r=(null===(e=this.options)||void 0===e?void 0:e.serializeErrors)||!!(null===(t=this.options)||void 0===t?void 0:t.errorSerializer);this.errorSerializer=r?null!==(n=null===(i=this.options)||void 0===i?void 0:i.errorSerializer)&&void 0!==n?n:A:U,X.HT.filter(e=>{var t,i;return!(null!==(i=null===(t=this.options)||void 0===t?void 0:t.disabledLevels)&&void 0!==i?i:ur.defaultDisabledLevels).includes(e)}).forEach(e=>{console[e]=(...t)=>{var i,n;try{if(e!==X.$b.ERROR||(null===(i=this.options)||void 0===i?void 0:i.consoleErrorAsLog))if(e===X.$b.ERROR&&(null===(n=this.options)||void 0===n?void 0:n.consoleErrorAsLog)){const{value:i,type:n,stackFrames:r}=Ft(t,this.errorSerializer);this.api.pushLog(i?[ur.consoleErrorPrefix+i]:t,{level:e,context:{value:null!=i?i:"",type:null!=n?n:"",stackFrames:(null==r?void 0:r.length)?A(r):""}})}else this.api.pushLog(t,{level:e});else{const{value:e,type:i,stackFrames:n}=Ft(t,this.errorSerializer);if(e&&!i&&!n)return void this.api.pushError(new Error(ur.consoleErrorPrefix+e));this.api.pushError(new Error(ur.consoleErrorPrefix+e),{type:i,stackFrames:n})}}catch(e){this.logError(e)}finally{this.unpatchedConsole[e](...t)}}})}}function cr(e={}){const t=[new Dt,new Vt,new sr,new or,new ar];return!1!==e.enablePerformanceInstrumentation&&t.unshift(new It),!1!==e.enableContentSecurityPolicyInstrumentation&&t.push(new lr),!1!==e.captureConsole&&t.push(new ur({disabledLevels:e.captureConsoleDisabledLevels})),t}ur.defaultDisabledLevels=[X.$b.DEBUG,X.$b.TRACE,X.$b.LOG],ur.consoleErrorPrefix="console.error: ";var dr=function(e,t){var i={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(i[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(i[n[r]]=e[n[r]])}return i};function hr(e,{trackUserActionsPreview:t}){return e.filter(e=>!("@grafana/faro-web-sdk:instrumentation-user-action"===e.name&&!t))}function fr(e){var t,i;const{page:n,generatePageId:r}=null!==(t=null==e?void 0:e.pageTracking)&&void 0!==t?t:{},s=[_e,De({generatePageId:r,initialPageMeta:n}),...null!==(i=e.metas)&&void 0!==i?i:[]];return f(window.k6)?[...s,Re]:s}function pr({trackGeolocation:e,sessionTracking:t}){var i;const n={};return h(e)&&(n.geoLocationTrackingEnabled=e),O(n)?{}:{session:Object.assign(Object.assign({},null!==(i=null==t?void 0:t.session)&&void 0!==i?i:{}),{overrides:n})}}function gr(e){const t=function(e){var t;const i=[],n=B(e.unpatchedConsole,e.internalLoggerLevel);e.transports?((e.url||e.apiKey)&&n.error('if "transports" is defined, "url" and "apiKey" should not be defined'),i.push(...e.transports)):e.url?i.push(new mt({url:e.url,apiKey:e.apiKey})):n.error('either "url" or "transports" must be defined');const{dedupe:r=!0,eventDomain:s=ae,globalObjectKey:o=se,instrumentations:a=cr(),internalLoggerLevel:l=q,isolate:u=!1,logArgsSerializer:c=U,metas:d=fr(e),paused:h=!1,preventGlobalExposure:f=!1,unpatchedConsole:p=F,trackUserActionsPreview:g=!1,trackUserActionsDataAttributeName:m=Ce,url:v}=e,b=dr(e,["dedupe","eventDomain","globalObjectKey","instrumentations","internalLoggerLevel","isolate","logArgsSerializer","metas","paused","preventGlobalExposure","unpatchedConsole","trackUserActionsPreview","trackUserActionsDataAttributeName","url"]);return Object.assign(Object.assign({},b),{batching:Object.assign(Object.assign({},oe),e.batching),dedupe:r,globalObjectKey:o,instrumentations:hr(a,e),internalLoggerLevel:l,isolate:u,logArgsSerializer:c,metas:d,parseStacktrace:Pe,paused:h,preventGlobalExposure:f,transports:i,unpatchedConsole:p,eventDomain:s,ignoreUrls:[...null!==(t=e.ignoreUrls)&&void 0!==t?t:[],...v?[v]:[],/\/collect(?:\/[\w]*)?$/],sessionTracking:Object.assign(Object.assign(Object.assign({},ze),e.sessionTracking),pr({trackGeolocation:e.trackGeolocation,sessionTracking:e.sessionTracking})),trackUserActionsPreview:g,trackUserActionsDataAttributeName:m})}(e);if(t)return re(t)}var mr=i(8531),vr=i(6159);const br=new Map([["dev",{environment:"dev",appName:"grafana-metricsdrilldown-app-dev",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/8c57b32175ba39d35dfaccee7cd793c7"}],["ops",{environment:"ops",appName:"grafana-metricsdrilldown-app-ops",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/d65ab91eb9c5e8c51b474d9313ba28f4"}],["prod",{environment:"prod",appName:"grafana-metricsdrilldown-app-prod",faroUrl:"https://faro-collector-ops-eu-south-0.grafana-ops.net/collect/0f4f1bbc97c9e2db4fa85ef75a559885"}]]);var Or=i(4137),wr=i(5176);let yr=null;const $r=()=>yr,xr=e=>yr=e;function kr(){if($r())return;const e=function(){const e=(0,vr.u)();if(e&&br.has(e))return br.get(e)}();if(!e)return;const{environment:t,faroUrl:i,appName:n}=e,{apps:r,bootData:s}=mr.config,o=r[Or.s_].version,a=s.user.email;xr(gr({url:i,app:{name:n,release:o,version:wr.t,environment:t},user:{email:a},instrumentations:[...cr({captureConsole:!1})],isolate:!0,beforeSend:e=>{var t,i;return(null!==(i=null===(t=e.meta.page)||void 0===t?void 0:t.url)&&void 0!==i?i:"").includes(Or.Gy)?e:null}}))}},1269:e=>{"use strict";e.exports=t},1308:e=>{"use strict";e.exports=i},2007:e=>{"use strict";e.exports=n},2445:(e,t,i)=>{"use strict";i.d(t,{v:()=>f});var n=i(5438),r=i(1160),s=i(6159);function o(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function a(e,t,i){if(!t.has(e))throw new TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function l(e,t,i){return function(e,t,i){if(t.set)t.set.call(e,i);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=i}}(e,a(e,t,"set"),i),i}function u(e,t,i){if(!t.has(e))throw new TypeError("attempted to get private field on non-instance");return i}var c=new WeakMap,d=new WeakSet;function h(e,t){var i;"prod"!==function(e,t){return t.get?t.get.call(e):t.value}(i=this,a(i,c,"get"))&&console[e](...t)}const f=new class{trace(){var e;u(this,d,h).call(this,"trace",[]),null===(e=(0,r.n1)())||void 0===e||e.api.pushLog([],{level:n.$b.TRACE})}debug(...e){var t;u(this,d,h).call(this,"debug",e),null===(t=(0,r.n1)())||void 0===t||t.api.pushLog(e,{level:n.$b.DEBUG})}info(...e){var t;u(this,d,h).call(this,"info",e),null===(t=(0,r.n1)())||void 0===t||t.api.pushLog(e,{level:n.$b.INFO})}log(...e){var t;u(this,d,h).call(this,"log",e),null===(t=(0,r.n1)())||void 0===t||t.api.pushLog(e,{level:n.$b.LOG})}warn(...e){var t;u(this,d,h).call(this,"warn",e),null===(t=(0,r.n1)())||void 0===t||t.api.pushLog(e,{level:n.$b.WARN})}error(e,t){var i;u(this,d,h).call(this,"error",[e]),t&&u(this,d,h).call(this,"error",["Error context",t]),null===(i=(0,r.n1)())||void 0===i||i.api.pushError(e,{context:t})}constructor(){var e,t;o(e=this,t=d),t.add(e),function(e,t,i){o(e,t),t.set(e,i)}(this,c,{writable:!0,value:void 0}),l(this,c,(0,s.u)())}}},2533:e=>{"use strict";e.exports=JSON.parse('{"id":"grafana-metricsdrilldown-app"}')},3241:e=>{"use strict";e.exports=r},4137:(e,t,i)=>{"use strict";i.d(t,{Gy:()=>s,bw:()=>o,s_:()=>r});var n=i(2533);const r=n.id,s=`/a/${n.id}`,o={Trail:"trail",Drilldown:"drilldown"}},5176:(e,t,i)=>{"use strict";i.d(t,{t:()=>n});const n="3fd2be0bb7a01e6027fb74c9bcbc0723ef7e6b93"},5438:(e,t,i)=>{"use strict";var n;i.d(t,{$b:()=>n,HT:()=>s,Ic:()=>r}),function(e){e.TRACE="trace",e.DEBUG="debug",e.INFO="info",e.LOG="log",e.WARN="warn",e.ERROR="error"}(n||(n={}));const r=n.LOG,s=[n.TRACE,n.DEBUG,n.INFO,n.LOG,n.WARN,n.ERROR]},5959:e=>{"use strict";e.exports=s},6089:e=>{"use strict";e.exports=o},6159:(e,t,i)=>{"use strict";i.d(t,{u:()=>r});const n=[{regExp:/localhost/,environment:"local"},{regExp:/grafana-dev\.net/,environment:"dev"},{regExp:/grafana-ops\.net/,environment:"ops"},{regExp:/grafana\.net/,environment:"prod"}];function r(){var e,t;if(!(null===(t=window)||void 0===t||null===(e=t.location)||void 0===e?void 0:e.host))return null;const i=n.find(({regExp:e})=>e.test(window.location.host));return i?i.environment:null}},6660:function(e,t,i){var n;!function(r,s){"use strict";var o="function",a="undefined",l="object",u="string",c="major",d="model",h="name",f="type",p="vendor",g="version",m="architecture",v="console",b="mobile",O="tablet",w="smarttv",y="wearable",$="embedded",x="Amazon",k="Apple",S="ASUS",P="BlackBerry",T="Browser",E="Chrome",z="Firefox",Q="Google",A="Huawei",C="LG",I="Microsoft",X="Motorola",U="Opera",_="Samsung",R="Sharp",L="Sony",N="Xiaomi",D="Zebra",j="Facebook",M="Chromium OS",q="Mac OS",F=" Browser",B=function(e){for(var t={},i=0;i<e.length;i++)t[e[i].toUpperCase()]=e[i];return t},V=function(e,t){return typeof e===u&&-1!==W(t).indexOf(W(e))},W=function(e){return e.toLowerCase()},Y=function(e,t){if(typeof e===u)return e=e.replace(/^\s\s*/,""),typeof t===a?e:e.substring(0,500)},Z=function(e,t){for(var i,n,r,a,u,c,d=0;d<t.length&&!u;){var h=t[d],f=t[d+1];for(i=n=0;i<h.length&&!u&&h[i];)if(u=h[i++].exec(e))for(r=0;r<f.length;r++)c=u[++n],typeof(a=f[r])===l&&a.length>0?2===a.length?typeof a[1]==o?this[a[0]]=a[1].call(this,c):this[a[0]]=a[1]:3===a.length?typeof a[1]!==o||a[1].exec&&a[1].test?this[a[0]]=c?c.replace(a[1],a[2]):s:this[a[0]]=c?a[1].call(this,c,a[2]):s:4===a.length&&(this[a[0]]=c?a[3].call(this,c.replace(a[1],a[2])):s):this[a]=c||s;d+=2}},G=function(e,t){for(var i in t)if(typeof t[i]===l&&t[i].length>0){for(var n=0;n<t[i].length;n++)if(V(t[i][n],e))return"?"===i?s:i}else if(V(t[i],e))return"?"===i?s:i;return t.hasOwnProperty("*")?t["*"]:e},H={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},K={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[h,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[h,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[h,g],[/opios[\/ ]+([\w\.]+)/i],[g,[h,U+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[g,[h,U+" GX"]],[/\bopr\/([\w\.]+)/i],[g,[h,U]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[g,[h,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[g,[h,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[h,g],[/quark(?:pc)?\/([-\w\.]+)/i],[g,[h,"Quark"]],[/\bddg\/([\w\.]+)/i],[g,[h,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[h,"UC"+T]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[g,[h,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[h,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[h,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[h,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[g,[h,"Smart Lenovo "+T]],[/(avast|avg)\/([\w\.]+)/i],[[h,/(.+)/,"$1 Secure "+T],g],[/\bfocus\/([\w\.]+)/i],[g,[h,z+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[h,U+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[h,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[h,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[h,U+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[h,"MIUI"+F]],[/fxios\/([\w\.-]+)/i],[g,[h,z]],[/\bqihoobrowser\/?([\w\.]*)/i],[g,[h,"360"]],[/\b(qq)\/([\w\.]+)/i],[[h,/(.+)/,"$1Browser"],g],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[h,/(.+)/,"$1"+F],g],[/samsungbrowser\/([\w\.]+)/i],[g,[h,_+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[g,[h,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[h,"Sogou Mobile"],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[h,g],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[h],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[g,h],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[h,j],g],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[h,g],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[h,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[h,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[h,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[h,E+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[h,"Android "+T]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[h,g],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[g,[h,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[g,h],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[h,[g,G,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[h,g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[h,"Netscape"],g],[/(wolvic|librewolf)\/([\w\.]+)/i],[h,g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[h,z+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[h,[g,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[h,[g,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[m,"amd64"]],[/(ia32(?=;))/i],[[m,W]],[/((?:i[346]|x)86)[;\)]/i],[[m,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[m,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[m,"armhf"]],[/windows (ce|mobile); ppc;/i],[[m,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[m,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[m,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[m,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[d,[p,_],[f,O]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[d,[p,_],[f,b]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[d,[p,k],[f,b]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[d,[p,k],[f,O]],[/(macintosh);/i],[d,[p,k]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[d,[p,R],[f,b]],[/(?:honor)([-\w ]+)[;\)]/i],[d,[p,"Honor"],[f,b]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[d,[p,A],[f,O]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[d,[p,A],[f,b]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[d,/_/g," "],[p,N],[f,b]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[d,/_/g," "],[p,N],[f,O]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[d,[p,"OPPO"],[f,b]],[/\b(opd2\d{3}a?) bui/i],[d,[p,"OPPO"],[f,O]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[d,[p,"Vivo"],[f,b]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[d,[p,"Realme"],[f,b]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[d,[p,X],[f,b]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[d,[p,X],[f,O]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[d,[p,C],[f,O]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[d,[p,C],[f,b]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[d,[p,"Lenovo"],[f,O]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[d,/_/g," "],[p,"Nokia"],[f,b]],[/(pixel c)\b/i],[d,[p,Q],[f,O]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[d,[p,Q],[f,b]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[d,[p,L],[f,b]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[d,"Xperia Tablet"],[p,L],[f,O]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[d,[p,"OnePlus"],[f,b]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[d,[p,x],[f,O]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[d,/(.+)/g,"Fire Phone $1"],[p,x],[f,b]],[/(playbook);[-\w\),; ]+(rim)/i],[d,p,[f,O]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[d,[p,P],[f,b]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[d,[p,S],[f,O]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[d,[p,S],[f,b]],[/(nexus 9)/i],[d,[p,"HTC"],[f,O]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[p,[d,/_/g," "],[f,b]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[d,[p,"TCL"],[f,O]],[/(itel) ((\w+))/i],[[p,W],d,[f,G,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[d,[p,"Acer"],[f,O]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[d,[p,"Meizu"],[f,b]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[d,[p,"Ulefone"],[f,b]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[d,[p,"Energizer"],[f,b]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[d,[p,"Cat"],[f,b]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[d,[p,"Smartfren"],[f,b]],[/droid.+; (a(?:015|06[35]|142p?))/i],[d,[p,"Nothing"],[f,b]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[p,d,[f,b]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[p,d,[f,O]],[/(surface duo)/i],[d,[p,I],[f,O]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[d,[p,"Fairphone"],[f,b]],[/(u304aa)/i],[d,[p,"AT&T"],[f,b]],[/\bsie-(\w*)/i],[d,[p,"Siemens"],[f,b]],[/\b(rct\w+) b/i],[d,[p,"RCA"],[f,O]],[/\b(venue[\d ]{2,7}) b/i],[d,[p,"Dell"],[f,O]],[/\b(q(?:mv|ta)\w+) b/i],[d,[p,"Verizon"],[f,O]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[d,[p,"Barnes & Noble"],[f,O]],[/\b(tm\d{3}\w+) b/i],[d,[p,"NuVision"],[f,O]],[/\b(k88) b/i],[d,[p,"ZTE"],[f,O]],[/\b(nx\d{3}j) b/i],[d,[p,"ZTE"],[f,b]],[/\b(gen\d{3}) b.+49h/i],[d,[p,"Swiss"],[f,b]],[/\b(zur\d{3}) b/i],[d,[p,"Swiss"],[f,O]],[/\b((zeki)?tb.*\b) b/i],[d,[p,"Zeki"],[f,O]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[p,"Dragon Touch"],d,[f,O]],[/\b(ns-?\w{0,9}) b/i],[d,[p,"Insignia"],[f,O]],[/\b((nxa|next)-?\w{0,9}) b/i],[d,[p,"NextBook"],[f,O]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[p,"Voice"],d,[f,b]],[/\b(lvtel\-)?(v1[12]) b/i],[[p,"LvTel"],d,[f,b]],[/\b(ph-1) /i],[d,[p,"Essential"],[f,b]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[d,[p,"Envizen"],[f,O]],[/\b(trio[-\w\. ]+) b/i],[d,[p,"MachSpeed"],[f,O]],[/\btu_(1491) b/i],[d,[p,"Rotor"],[f,O]],[/(shield[\w ]+) b/i],[d,[p,"Nvidia"],[f,O]],[/(sprint) (\w+)/i],[p,d,[f,b]],[/(kin\.[onetw]{3})/i],[[d,/\./g," "],[p,I],[f,b]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[d,[p,D],[f,O]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[d,[p,D],[f,b]],[/smart-tv.+(samsung)/i],[p,[f,w]],[/hbbtv.+maple;(\d+)/i],[[d,/^/,"SmartTV"],[p,_],[f,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[p,C],[f,w]],[/(apple) ?tv/i],[p,[d,k+" TV"],[f,w]],[/crkey/i],[[d,E+"cast"],[p,Q],[f,w]],[/droid.+aft(\w+)( bui|\))/i],[d,[p,x],[f,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[d,[p,R],[f,w]],[/(bravia[\w ]+)( bui|\))/i],[d,[p,L],[f,w]],[/(mitv-\w{5}) bui/i],[d,[p,N],[f,w]],[/Hbbtv.*(technisat) (.*);/i],[p,d,[f,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[p,Y],[d,Y],[f,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[p,d,[f,v]],[/droid.+; (shield) bui/i],[d,[p,"Nvidia"],[f,v]],[/(playstation [345portablevi]+)/i],[d,[p,L],[f,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[d,[p,I],[f,v]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[d,[p,_],[f,y]],[/((pebble))app/i],[p,d,[f,y]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[d,[p,k],[f,y]],[/droid.+; (glass) \d/i],[d,[p,Q],[f,y]],[/droid.+; (wt63?0{2,3})\)/i],[d,[p,D],[f,y]],[/droid.+; (glass) \d/i],[d,[p,Q],[f,y]],[/(pico) (4|neo3(?: link|pro)?)/i],[p,d,[f,y]],[/; (quest( \d| pro)?)/i],[d,[p,j],[f,y]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[p,[f,$]],[/(aeobc)\b/i],[d,[p,x],[f,$]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[d,[f,b]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[d,[f,O]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,O]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[f,b]],[/(android[-\w\. ]{0,9});.+buil/i],[d,[p,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[h,"EdgeHTML"]],[/(arkweb)\/([\w\.]+)/i],[h,g],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[h,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[h,g],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,h]],os:[[/microsoft (windows) (vista|xp)/i],[h,g],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[h,[g,G,H]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,G,H],[h,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[h,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[h,q],[g,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,h],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[h,g],[/\(bb(10);/i],[g,[h,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[g,[h,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[h,z+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[h,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[h,"watchOS"]],[/crkey\/([\d\.]+)/i],[g,[h,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[h,M],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[h,g],[/(sunos) ?([\w\.\d]*)/i],[[h,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[h,g]]},J=function(e,t){if(typeof e===l&&(t=e,e=s),!(this instanceof J))return new J(e,t).getResult();var i=typeof r!==a&&r.navigator?r.navigator:s,n=e||(i&&i.userAgent?i.userAgent:""),v=i&&i.userAgentData?i.userAgentData:s,w=t?function(e,t){var i={};for(var n in e)t[n]&&t[n].length%2==0?i[n]=t[n].concat(e[n]):i[n]=e[n];return i}(K,t):K,y=i&&i.userAgent==n;return this.getBrowser=function(){var e,t={};return t[h]=s,t[g]=s,Z.call(t,n,w.browser),t[c]=typeof(e=t[g])===u?e.replace(/[^\d\.]/g,"").split(".")[0]:s,y&&i&&i.brave&&typeof i.brave.isBrave==o&&(t[h]="Brave"),t},this.getCPU=function(){var e={};return e[m]=s,Z.call(e,n,w.cpu),e},this.getDevice=function(){var e={};return e[p]=s,e[d]=s,e[f]=s,Z.call(e,n,w.device),y&&!e[f]&&v&&v.mobile&&(e[f]=b),y&&"Macintosh"==e[d]&&i&&typeof i.standalone!==a&&i.maxTouchPoints&&i.maxTouchPoints>2&&(e[d]="iPad",e[f]=O),e},this.getEngine=function(){var e={};return e[h]=s,e[g]=s,Z.call(e,n,w.engine),e},this.getOS=function(){var e={};return e[h]=s,e[g]=s,Z.call(e,n,w.os),y&&!e[h]&&v&&v.platform&&"Unknown"!=v.platform&&(e[h]=v.platform.replace(/chrome os/i,M).replace(/macos/i,q)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===u&&e.length>500?Y(e,500):e,this},this.setUA(n),this};J.VERSION="1.0.40",J.BROWSER=B([h,g,c]),J.CPU=B([m]),J.DEVICE=B([d,p,f,v,b,w,O,y,$]),J.ENGINE=J.OS=B([h,g]),typeof t!==a?(e.exports&&(t=e.exports=J),t.UAParser=J):i.amdO?(n=function(){return J}.call(t,i,t,e))===s||(e.exports=n):typeof r!==a&&(r.UAParser=J);var ee=typeof r!==a&&(r.jQuery||r.Zepto);if(ee&&!ee.ua){var te=new J;ee.ua=te.getResult(),ee.ua.get=function(){return te.getUA()},ee.ua.set=function(e){te.setUA(e);var t=te.getResult();for(var i in t)ee.ua[i]=t[i]}}}("object"==typeof window?window:this)},7203:(e,t,i)=>{"use strict";i.d(t,{PH:()=>p,Qj:()=>f,Z6:()=>u,cF:()=>n,fI:()=>c,iX:()=>C,uY:()=>o});const n=1024;let r=0;class s{constructor(e,t){this.from=e,this.to=t}}class o{constructor(e={}){this.id=r++,this.perNode=!!e.perNode,this.deserialize=e.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(e){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return"function"!=typeof e&&(e=u.match(e)),t=>{let i=e(t);return void 0===i?null:[this,i]}}}o.closedBy=new o({deserialize:e=>e.split(" ")}),o.openedBy=new o({deserialize:e=>e.split(" ")}),o.group=new o({deserialize:e=>e.split(" ")}),o.isolate=new o({deserialize:e=>{if(e&&"rtl"!=e&&"ltr"!=e&&"auto"!=e)throw new RangeError("Invalid value for isolate: "+e);return e||"auto"}}),o.contextHash=new o({perNode:!0}),o.lookAhead=new o({perNode:!0}),o.mounted=new o({perNode:!0});class a{constructor(e,t,i){this.tree=e,this.overlay=t,this.parser=i}static get(e){return e&&e.props&&e.props[o.mounted.id]}}const l=Object.create(null);class u{constructor(e,t,i,n=0){this.name=e,this.props=t,this.id=i,this.flags=n}static define(e){let t=e.props&&e.props.length?Object.create(null):l,i=(e.top?1:0)|(e.skipped?2:0)|(e.error?4:0)|(null==e.name?8:0),n=new u(e.name||"",t,e.id,i);if(e.props)for(let i of e.props)if(Array.isArray(i)||(i=i(n)),i){if(i[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");t[i[0].id]=i[1]}return n}prop(e){return this.props[e.id]}get isTop(){return(1&this.flags)>0}get isSkipped(){return(2&this.flags)>0}get isError(){return(4&this.flags)>0}get isAnonymous(){return(8&this.flags)>0}is(e){if("string"==typeof e){if(this.name==e)return!0;let t=this.prop(o.group);return!!t&&t.indexOf(e)>-1}return this.id==e}static match(e){let t=Object.create(null);for(let i in e)for(let n of i.split(" "))t[n]=e[i];return e=>{for(let i=e.prop(o.group),n=-1;n<(i?i.length:0);n++){let r=t[n<0?e.name:i[n]];if(r)return r}}}}u.none=new u("",Object.create(null),0,8);class c{constructor(e){this.types=e;for(let t=0;t<e.length;t++)if(e[t].id!=t)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...e){let t=[];for(let i of this.types){let n=null;for(let t of e){let e=t(i);e&&(n||(n=Object.assign({},i.props)),n[e[0].id]=e[1])}t.push(n?new u(i.name,n,i.id,i.flags):i)}return new c(t)}}const d=new WeakMap,h=new WeakMap;var f;!function(e){e[e.ExcludeBuffers=1]="ExcludeBuffers",e[e.IncludeAnonymous=2]="IncludeAnonymous",e[e.IgnoreMounts=4]="IgnoreMounts",e[e.IgnoreOverlays=8]="IgnoreOverlays"}(f||(f={}));class p{constructor(e,t,i,n,r){if(this.type=e,this.children=t,this.positions=i,this.length=n,this.props=null,r&&r.length){this.props=Object.create(null);for(let[e,t]of r)this.props["number"==typeof e?e:e.id]=t}}toString(){let e=a.get(this);if(e&&!e.overlay)return e.tree.toString();let t="";for(let e of this.children){let i=e.toString();i&&(t&&(t+=","),t+=i)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(t.length?"("+t+")":""):t}cursor(e=0){return new T(this.topNode,e)}cursorAt(e,t=0,i=0){let n=d.get(this)||this.topNode,r=new T(n);return r.moveTo(e,t),d.set(this,r._tree),r}get topNode(){return new w(this,0,0,null)}resolve(e,t=0){let i=b(d.get(this)||this.topNode,e,t,!1);return d.set(this,i),i}resolveInner(e,t=0){let i=b(h.get(this)||this.topNode,e,t,!0);return h.set(this,i),i}resolveStack(e,t=0){return function(e,t,i){let n=e.resolveInner(t,i),r=null;for(let e=n instanceof w?n:n.context.parent;e;e=e.parent)if(e.index<0){let s=e.parent;(r||(r=[n])).push(s.resolve(t,i)),e=s}else{let s=a.get(e.tree);if(s&&s.overlay&&s.overlay[0].from<=t&&s.overlay[s.overlay.length-1].to>=t){let o=new w(s.tree,s.overlay[0].from+e.from,-1,e);(r||(r=[n])).push(b(o,t,i,!1))}}return r?S(r):n}(this,e,t)}iterate(e){let{enter:t,leave:i,from:n=0,to:r=this.length}=e,s=e.mode||0,o=(s&f.IncludeAnonymous)>0;for(let e=this.cursor(s|f.IncludeAnonymous);;){let s=!1;if(e.from<=r&&e.to>=n&&(!o&&e.type.isAnonymous||!1!==t(e))){if(e.firstChild())continue;s=!0}for(;s&&i&&(o||!e.type.isAnonymous)&&i(e),!e.nextSibling();){if(!e.parent())return;s=!0}}}prop(e){return e.perNode?this.props?this.props[e.id]:void 0:this.type.prop(e)}get propValues(){let e=[];if(this.props)for(let t in this.props)e.push([+t,this.props[t]]);return e}balance(e={}){return this.children.length<=8?this:A(u.none,this.children,this.positions,0,this.children.length,0,this.length,(e,t,i)=>new p(this.type,e,t,i,this.propValues),e.makeTree||((e,t,i)=>new p(u.none,e,t,i)))}static build(e){return function(e){var t;let{buffer:i,nodeSet:r,maxBufferLength:s=n,reused:a=[],minRepeatType:l=r.types.length}=e,u=Array.isArray(i)?new g(i,i.length):i,c=r.types,d=0,h=0;function f(e,t,i,n,o,p){let{id:g,start:x,end:k,size:S}=u,P=h,T=d;for(;S<0;){if(u.next(),-1==S){let t=a[g];return i.push(t),void n.push(x-e)}if(-3==S)return void(d=g);if(-4==S)return void(h=g);throw new RangeError(`Unrecognized record size: ${S}`)}let E,z,Q=c[g],C=x-e;if(k-x<=s&&(z=y(u.pos-t,o))){let t=new Uint16Array(z.size-z.skip),i=u.pos-z.size,n=t.length;for(;u.pos>i;)n=$(z.start,t,n);E=new m(t,k-z.start,r),C=z.start-e}else{let e=u.pos-S;u.next();let t=[],i=[],n=g>=l?g:-1,r=0,o=k;for(;u.pos>e;)n>=0&&u.id==n&&u.size>=0?(u.end<=o-s&&(O(t,i,x,r,u.end,o,n,P,T),r=t.length,o=u.end),u.next()):p>2500?v(x,e,t,i):f(x,e,t,i,n,p+1);if(n>=0&&r>0&&r<t.length&&O(t,i,x,r,x,o,n,P,T),t.reverse(),i.reverse(),n>-1&&r>0){let e=b(Q,T);E=A(Q,t,i,0,t.length,0,k-x,e,e)}else E=w(Q,t,i,k-x,P-k,T)}i.push(E),n.push(C)}function v(e,t,i,n){let o=[],a=0,l=-1;for(;u.pos>t;){let{id:e,start:t,end:i,size:n}=u;if(n>4)u.next();else{if(l>-1&&t<l)break;l<0&&(l=i-s),o.push(e,t,i),a++,u.next()}}if(a){let t=new Uint16Array(4*a),s=o[o.length-2];for(let e=o.length-3,i=0;e>=0;e-=3)t[i++]=o[e],t[i++]=o[e+1]-s,t[i++]=o[e+2]-s,t[i++]=i;i.push(new m(t,o[2]-s,r)),n.push(s-e)}}function b(e,t){return(i,n,r)=>{let s,a,l=0,u=i.length-1;if(u>=0&&(s=i[u])instanceof p){if(!u&&s.type==e&&s.length==r)return s;(a=s.prop(o.lookAhead))&&(l=n[u]+s.length+a)}return w(e,i,n,r,l,t)}}function O(e,t,i,n,s,o,a,l,u){let c=[],d=[];for(;e.length>n;)c.push(e.pop()),d.push(t.pop()+i-s);e.push(w(r.types[a],c,d,o-s,l-o,u)),t.push(s-i)}function w(e,t,i,n,r,s,a){if(s){let e=[o.contextHash,s];a=a?[e].concat(a):[e]}if(r>25){let e=[o.lookAhead,r];a=a?[e].concat(a):[e]}return new p(e,t,i,n,a)}function y(e,t){let i=u.fork(),n=0,r=0,o=0,a=i.end-s,c={size:0,start:0,skip:0};e:for(let s=i.pos-e;i.pos>s;){let e=i.size;if(i.id==t&&e>=0){c.size=n,c.start=r,c.skip=o,o+=4,n+=4,i.next();continue}let u=i.pos-e;if(e<0||u<s||i.start<a)break;let d=i.id>=l?4:0,h=i.start;for(i.next();i.pos>u;){if(i.size<0){if(-3!=i.size)break e;d+=4}else i.id>=l&&(d+=4);i.next()}r=h,n+=e,o+=d}return(t<0||n==e)&&(c.size=n,c.start=r,c.skip=o),c.size>4?c:void 0}function $(e,t,i){let{id:n,start:r,end:s,size:o}=u;if(u.next(),o>=0&&n<l){let a=i;if(o>4){let n=u.pos-(o-4);for(;u.pos>n;)i=$(e,t,i)}t[--i]=a,t[--i]=s-e,t[--i]=r-e,t[--i]=n}else-3==o?d=n:-4==o&&(h=n);return i}let x=[],k=[];for(;u.pos>0;)f(e.start||0,e.bufferStart||0,x,k,-1,0);let S=null!==(t=e.length)&&void 0!==t?t:x.length?k[0]+x[0].length:0;return new p(c[e.topID],x.reverse(),k.reverse(),S)}(e)}}p.empty=new p(u.none,[],[],0);class g{constructor(e,t){this.buffer=e,this.index=t}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new g(this.buffer,this.index)}}class m{constructor(e,t,i){this.buffer=e,this.length=t,this.set=i}get type(){return u.none}toString(){let e=[];for(let t=0;t<this.buffer.length;)e.push(this.childString(t)),t=this.buffer[t+3];return e.join(",")}childString(e){let t=this.buffer[e],i=this.buffer[e+3],n=this.set.types[t],r=n.name;if(/\W/.test(r)&&!n.isError&&(r=JSON.stringify(r)),i==(e+=4))return r;let s=[];for(;e<i;)s.push(this.childString(e)),e=this.buffer[e+3];return r+"("+s.join(",")+")"}findChild(e,t,i,n,r){let{buffer:s}=this,o=-1;for(let a=e;a!=t&&!(v(r,n,s[a+1],s[a+2])&&(o=a,i>0));a=s[a+3]);return o}slice(e,t,i){let n=this.buffer,r=new Uint16Array(t-e),s=0;for(let o=e,a=0;o<t;){r[a++]=n[o++],r[a++]=n[o++]-i;let t=r[a++]=n[o++]-i;r[a++]=n[o++]-e,s=Math.max(s,t)}return new m(r,s,this.set)}}function v(e,t,i,n){switch(e){case-2:return i<t;case-1:return n>=t&&i<t;case 0:return i<t&&n>t;case 1:return i<=t&&n>t;case 2:return n>t;case 4:return!0}}function b(e,t,i,n){for(var r;e.from==e.to||(i<1?e.from>=t:e.from>t)||(i>-1?e.to<=t:e.to<t);){let t=!n&&e instanceof w&&e.index<0?null:e.parent;if(!t)return e;e=t}let s=n?0:f.IgnoreOverlays;if(n)for(let n=e,o=n.parent;o;n=o,o=n.parent)n instanceof w&&n.index<0&&(null===(r=o.enter(t,i,s))||void 0===r?void 0:r.from)!=n.from&&(e=o);for(;;){let n=e.enter(t,i,s);if(!n)return e;e=n}}class O{cursor(e=0){return new T(this,e)}getChild(e,t=null,i=null){let n=y(this,e,t,i);return n.length?n[0]:null}getChildren(e,t=null,i=null){return y(this,e,t,i)}resolve(e,t=0){return b(this,e,t,!1)}resolveInner(e,t=0){return b(this,e,t,!0)}matchContext(e){return $(this.parent,e)}enterUnfinishedNodesBefore(e){let t=this.childBefore(e),i=this;for(;t;){let e=t.lastChild;if(!e||e.to!=t.to)break;e.type.isError&&e.from==e.to?(i=t,t=e.prevSibling):t=e}return i}get node(){return this}get next(){return this.parent}}class w extends O{constructor(e,t,i,n){super(),this._tree=e,this.from=t,this.index=i,this._parent=n}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(e,t,i,n,r=0){for(let s=this;;){for(let{children:o,positions:l}=s._tree,u=t>0?o.length:-1;e!=u;e+=t){let u=o[e],c=l[e]+s.from;if(v(n,i,c,c+u.length))if(u instanceof m){if(r&f.ExcludeBuffers)continue;let o=u.findChild(0,u.buffer.length,t,i-c,n);if(o>-1)return new k(new x(s,u,e,c),null,o)}else if(r&f.IncludeAnonymous||!u.type.isAnonymous||E(u)){let o;if(!(r&f.IgnoreMounts)&&(o=a.get(u))&&!o.overlay)return new w(o.tree,c,e,s);let l=new w(u,c,e,s);return r&f.IncludeAnonymous||!l.type.isAnonymous?l:l.nextChild(t<0?u.children.length-1:0,t,i,n)}}if(r&f.IncludeAnonymous||!s.type.isAnonymous)return null;if(e=s.index>=0?s.index+t:t<0?-1:s._parent._tree.children.length,s=s._parent,!s)return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(e){return this.nextChild(0,1,e,2)}childBefore(e){return this.nextChild(this._tree.children.length-1,-1,e,-2)}enter(e,t,i=0){let n;if(!(i&f.IgnoreOverlays)&&(n=a.get(this._tree))&&n.overlay){let i=e-this.from;for(let{from:e,to:r}of n.overlay)if((t>0?e<=i:e<i)&&(t<0?r>=i:r>i))return new w(n.tree,n.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,e,t,i)}nextSignificantParent(){let e=this;for(;e.type.isAnonymous&&e._parent;)e=e._parent;return e}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function y(e,t,i,n){let r=e.cursor(),s=[];if(!r.firstChild())return s;if(null!=i)for(let e=!1;!e;)if(e=r.type.is(i),!r.nextSibling())return s;for(;;){if(null!=n&&r.type.is(n))return s;if(r.type.is(t)&&s.push(r.node),!r.nextSibling())return null==n?s:[]}}function $(e,t,i=t.length-1){for(let n=e;i>=0;n=n.parent){if(!n)return!1;if(!n.type.isAnonymous){if(t[i]&&t[i]!=n.name)return!1;i--}}return!0}class x{constructor(e,t,i,n){this.parent=e,this.buffer=t,this.index=i,this.start=n}}class k extends O{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(e,t,i){super(),this.context=e,this._parent=t,this.index=i,this.type=e.buffer.set.types[e.buffer.buffer[i]]}child(e,t,i){let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],e,t-this.context.start,i);return r<0?null:new k(this.context,this,r)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(e){return this.child(1,e,2)}childBefore(e){return this.child(-1,e,-2)}enter(e,t,i=0){if(i&f.ExcludeBuffers)return null;let{buffer:n}=this.context,r=n.findChild(this.index+4,n.buffer[this.index+3],t>0?1:-1,e-this.context.start,t);return r<0?null:new k(this.context,this,r)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(e){return this._parent?null:this.context.parent.nextChild(this.context.index+e,e,0,4)}get nextSibling(){let{buffer:e}=this.context,t=e.buffer[this.index+3];return t<(this._parent?e.buffer[this._parent.index+3]:e.buffer.length)?new k(this.context,this._parent,t):this.externalSibling(1)}get prevSibling(){let{buffer:e}=this.context,t=this._parent?this._parent.index+4:0;return this.index==t?this.externalSibling(-1):new k(this.context,this._parent,e.findChild(t,this.index,-1,0,4))}get tree(){return null}toTree(){let e=[],t=[],{buffer:i}=this.context,n=this.index+4,r=i.buffer[this.index+3];if(r>n){let s=i.buffer[this.index+1];e.push(i.slice(n,r,s)),t.push(0)}return new p(this.type,e,t,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function S(e){if(!e.length)return null;let t=0,i=e[0];for(let n=1;n<e.length;n++){let r=e[n];(r.from>i.from||r.to<i.to)&&(i=r,t=n)}let n=i instanceof w&&i.index<0?null:i.parent,r=e.slice();return n?r[t]=n:r.splice(t,1),new P(r,i)}class P{constructor(e,t){this.heads=e,this.node=t}get next(){return S(this.heads)}}class T{get name(){return this.type.name}constructor(e,t=0){if(this.mode=t,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,e instanceof w)this.yieldNode(e);else{this._tree=e.context.parent,this.buffer=e.context;for(let t=e._parent;t;t=t._parent)this.stack.unshift(t.index);this.bufferNode=e,this.yieldBuf(e.index)}}yieldNode(e){return!!e&&(this._tree=e,this.type=e.type,this.from=e.from,this.to=e.to,!0)}yieldBuf(e,t){this.index=e;let{start:i,buffer:n}=this.buffer;return this.type=t||n.set.types[n.buffer[e]],this.from=i+n.buffer[e+1],this.to=i+n.buffer[e+2],!0}yield(e){return!!e&&(e instanceof w?(this.buffer=null,this.yieldNode(e)):(this.buffer=e.context,this.yieldBuf(e.index,e.type)))}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(e,t,i){if(!this.buffer)return this.yield(this._tree.nextChild(e<0?this._tree._tree.children.length-1:0,e,t,i,this.mode));let{buffer:n}=this.buffer,r=n.findChild(this.index+4,n.buffer[this.index+3],e,t-this.buffer.start,i);return!(r<0)&&(this.stack.push(this.index),this.yieldBuf(r))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(e){return this.enterChild(1,e,2)}childBefore(e){return this.enterChild(-1,e,-2)}enter(e,t,i=this.mode){return this.buffer?!(i&f.ExcludeBuffers)&&this.enterChild(1,e,t):this.yield(this._tree.enter(e,t,i))}parent(){if(!this.buffer)return this.yieldNode(this.mode&f.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let e=this.mode&f.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(e)}sibling(e){if(!this.buffer)return!!this._tree._parent&&this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+e,e,0,4,this.mode));let{buffer:t}=this.buffer,i=this.stack.length-1;if(e<0){let e=i<0?0:this.stack[i]+4;if(this.index!=e)return this.yieldBuf(t.findChild(e,this.index,-1,0,4))}else{let e=t.buffer[this.index+3];if(e<(i<0?t.buffer.length:t.buffer[this.stack[i]+3]))return this.yieldBuf(e)}return i<0&&this.yield(this.buffer.parent.nextChild(this.buffer.index+e,e,0,4,this.mode))}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(e){let t,i,{buffer:n}=this;if(n){if(e>0){if(this.index<n.buffer.buffer.length)return!1}else for(let e=0;e<this.index;e++)if(n.buffer.buffer[e+3]<this.index)return!1;({index:t,parent:i}=n)}else({index:t,_parent:i}=this._tree);for(;i;({index:t,_parent:i}=i))if(t>-1)for(let n=t+e,r=e<0?-1:i._tree.children.length;n!=r;n+=e){let e=i._tree.children[n];if(this.mode&f.IncludeAnonymous||e instanceof m||!e.type.isAnonymous||E(e))return!1}return!0}move(e,t){if(t&&this.enterChild(e,0,4))return!0;for(;;){if(this.sibling(e))return!0;if(this.atLastNode(e)||!this.parent())return!1}}next(e=!0){return this.move(1,e)}prev(e=!0){return this.move(-1,e)}moveTo(e,t=0){for(;(this.from==this.to||(t<1?this.from>=e:this.from>e)||(t>-1?this.to<=e:this.to<e))&&this.parent(););for(;this.enterChild(1,e,t););return this}get node(){if(!this.buffer)return this._tree;let e=this.bufferNode,t=null,i=0;if(e&&e.context==this.buffer)e:for(let n=this.index,r=this.stack.length;r>=0;){for(let s=e;s;s=s._parent)if(s.index==n){if(n==this.index)return s;t=s,i=r+1;break e}n=this.stack[--r]}for(let e=i;e<this.stack.length;e++)t=new k(this.buffer,t,this.stack[e]);return this.bufferNode=new k(this.buffer,t,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(e,t){for(let i=0;;){let n=!1;if(this.type.isAnonymous||!1!==e(this)){if(this.firstChild()){i++;continue}this.type.isAnonymous||(n=!0)}for(;;){if(n&&t&&t(this),n=this.type.isAnonymous,!i)return;if(this.nextSibling())break;this.parent(),i--,n=!0}}}matchContext(e){if(!this.buffer)return $(this.node.parent,e);let{buffer:t}=this.buffer,{types:i}=t.set;for(let n=e.length-1,r=this.stack.length-1;n>=0;r--){if(r<0)return $(this._tree,e,n);let s=i[t.buffer[this.stack[r]]];if(!s.isAnonymous){if(e[n]&&e[n]!=s.name)return!1;n--}}return!0}}function E(e){return e.children.some(e=>e instanceof m||!e.type.isAnonymous||E(e))}const z=new WeakMap;function Q(e,t){if(!e.isAnonymous||t instanceof m||t.type!=e)return 1;let i=z.get(t);if(null==i){i=1;for(let n of t.children){if(n.type!=e||!(n instanceof p)){i=1;break}i+=Q(e,n)}z.set(t,i)}return i}function A(e,t,i,n,r,s,o,a,l){let u=0;for(let i=n;i<r;i++)u+=Q(e,t[i]);let c=Math.ceil(1.5*u/8),d=[],h=[];return function t(i,n,r,o,a){for(let u=r;u<o;){let r=u,f=n[u],p=Q(e,i[u]);for(u++;u<o;u++){let t=Q(e,i[u]);if(p+t>=c)break;p+=t}if(u==r+1){if(p>c){let e=i[r];t(e.children,e.positions,0,e.children.length,n[r]+a);continue}d.push(i[r])}else{let t=n[u-1]+i[u-1].length-f;d.push(A(e,i,n,r,u,f,t,null,l))}h.push(f+a-s)}}(t,i,n,r,0),(a||l)(d,h,o)}class C{startParse(e,t,i){return"string"==typeof e&&(e=new I(e)),i=i?i.length?i.map(e=>new s(e.from,e.to)):[new s(0,0)]:[new s(0,e.length)],this.createParse(e,t||[],i)}parse(e,t,i){let n=this.startParse(e,t,i);for(;;){let e=n.advance();if(e)return e}}}class I{constructor(e){this.string=e}get length(){return this.string.length}chunk(e){return this.string.slice(e)}get lineChunks(){return!1}read(e,t){return this.string.slice(e,t)}}new o({perNode:!0})},7781:e=>{"use strict";e.exports=a},7818:(e,t,i)=>{"use strict";i.d(t,{$9:()=>d,Ge:()=>c,Rk:()=>m,Wf:()=>h});var n=i(7781),r=i(8531),s=i(9851),o=i(519),a=i(4137),l=i(2445);const u="Grafana Metrics Drilldown",c=[{title:`Open in ${u}`,description:`Open current query in the ${u} view`,category:"metrics-drilldown",icon:"gf-prometheus",path:m(a.bw.Drilldown),targets:[n.PluginExtensionPoints.DashboardPanelMenu,n.PluginExtensionPoints.ExploreToolbarAction,"grafana/alerting/alertingrule/queryeditor","grafana-assistant-app/navigateToDrilldown/v1"],configure:function(e){var t;if(void 0===e)return;if("pluginId"in e&&"timeseries"!==e.pluginId)return;const i=e.targets.filter(b);if(!i.length)return;const n=i[0],s=(0,r.getTemplateSrv)(),o=s.replace(null==n||null===(t=n.datasource)||void 0===t?void 0:t.uid,e.scopedVars);if(!n.expr)return{path:m(a.bw.Drilldown)};const u=s.replace(n.expr,e.scopedVars,O);try{const{metric:t,labels:i,hasErrors:n,errors:r}=d(u);n&&l.v.warn(`PromQL query has parsing errors: ${r.join(", ")}`);const s="timeRange"in e&&"object"==typeof e.timeRange&&null!=e.timeRange&&"from"in e.timeRange&&"to"in e.timeRange?e.timeRange:void 0,c=g(p(o,i,t,null==s?void 0:s.from,null==s?void 0:s.to));return{path:m(a.bw.Drilldown,c)}}catch(e){return l.v.error(new Error(`[Metrics Drilldown] Error parsing PromQL query: ${e}`)),{path:m(a.bw.Drilldown)}}}},{targets:["grafana-metricsdrilldown-app/grafana-assistant-app/navigateToDrilldown/v0-alpha"],title:"Navigate to metrics drilldown",description:"Build a url path to the metrics drilldown",path:m(a.bw.Drilldown),configure:e=>{if(void 0===e)return;const{navigateToMetrics:t,datasource_uid:i,label_filters:n,metric:r,start:s,end:l}=e,u=function(e){if(!e)return[];return e.map(e=>{const t=(0,o.q)(e);return{label:t.key,op:t.operator,value:t.value}})}(n),c=p(i,u,r,s,l),d=t?g(c):void 0;return{path:m(a.bw.Drilldown,d)}}}];function d(e){const t=s.K3.parse(e);let i="";const n=[];let r=!1;const o=[];return t.iterate({enter:t=>{var s;if(t.type.isError||"⚠"===t.name){r=!0;const i=e.slice(t.from,t.to),n=i?`Parse error at position ${t.from}-${t.to}: "${i}"`:`Parse error at position ${t.from}`;o.push(n)}i||"Identifier"!==t.name||"VectorSelector"!==(null===(s=t.node.parent)||void 0===s?void 0:s.type.name)||(i=e.slice(t.from,t.to));const a=h(t,e);a&&n.push(a)}}),{metric:i,labels:n,hasErrors:r,errors:o}}function h(e,t){if("UnquotedLabelMatcher"!==e.name)return null;let i="",n="",r="";for(let s=e.node.firstChild;s;s=s.nextSibling)"LabelName"===s.type.name?i=t.slice(s.from,s.to):"MatchOp"===s.type.name?n=t.slice(s.from,s.to):"StringLiteral"===s.type.name&&(r=t.slice(s.from+1,s.to-1));return i&&n?{label:i,op:n,value:r}:null}function f(e){return[v.Filters,`${e.label}|${e.op}|${e.value}`]}function p(e,t,i,n,r){return{datasource_uid:e,label_filters:null!=t?t:[],metric:i,start:n,end:r}}function g(e){const{metric:t,start:i,end:n,datasource_uid:r,label_filters:s}=e,o=null!=s?s:[];return function(e,t){const i=new URLSearchParams(null==t?void 0:t.toString());return e.forEach(([e,t])=>{t&&i.append(e,t)}),i}([[v.Metric,t],[v.TimeRangeFrom,i],[v.TimeRangeTo,n],[v.DatasourceId,r],...o.map(f)])}function m(e,t){const i=t?`?${t.toString()}`:"";return`${a.Gy}/${e}${i}`}const v={TimeRangeFrom:"from",TimeRangeTo:"to",Metric:"metric",DatasourceId:"var-ds",Filters:"var-filters"};function b(e){const{datasource:t}=e;return"prometheus"===(null==t?void 0:t.type)}function O(e=[],t){if(!t.multi&&!t.includeAll)return function(e){if("string"!=typeof e)return e;if(r.config.featureToggles.prometheusSpecialCharsInLabelValues)return/^\w+(=|!=|=~|!~)".*"$/.test(e)?e:e.replace(/\\/g,"\\\\").replace(/"/g,'\\"');return e.replace(/\\/g,"\\\\").replace(/'/g,"\\\\'")}(e);if("string"==typeof e)return w(e);const i=e.map(e=>w(e));return 1===i.length?i[0]:"("+i.join("|")+")"}function w(e){return"string"!=typeof e?e:r.config.featureToggles.prometheusSpecialCharsInLabelValues?e.replace(/\\/g,"\\\\\\\\").replace(/"/g,'\\\\\\"').replace(/[$^*{}\[\]\'+?.()|]/g,"\\\\$&"):e.replace(/\\/g,"\\\\\\\\").replace(/[$^*{}\[\]+?.()|]/g,"\\\\$&")}},8398:e=>{"use strict";e.exports=l},8531:e=>{"use strict";e.exports=u},9851:(e,t,i)=>{"use strict";i.d(t,{K3:()=>I});var n=i(782),r=i(7203);let s=0;class o{constructor(e,t,i,n){this.name=e,this.set=t,this.base=i,this.modified=n,this.id=s++}toString(){let{name:e}=this;for(let t of this.modified)t.name&&(e=`${t.name}(${e})`);return e}static define(e,t){let i="string"==typeof e?e:"?";if(e instanceof o&&(t=e),null==t?void 0:t.base)throw new Error("Can not derive from a modified tag");let n=new o(i,[],null,[]);if(n.set.push(n),t)for(let e of t.set)n.set.push(e);return n}static defineModifier(e){let t=new l(e);return e=>e.modified.indexOf(t)>-1?e:l.get(e.base||e,e.modified.concat(t).sort((e,t)=>e.id-t.id))}}let a=0;class l{constructor(e){this.name=e,this.instances=[],this.id=a++}static get(e,t){if(!t.length)return e;let i=t[0].instances.find(i=>{return i.base==e&&(n=t,r=i.modified,n.length==r.length&&n.every((e,t)=>e==r[t]));var n,r});if(i)return i;let n=[],r=new o(e.name,n,e,t);for(let e of t)e.instances.push(r);let s=function(e){let t=[[]];for(let i=0;i<e.length;i++)for(let n=0,r=t.length;n<r;n++)t.push(t[n].concat(e[i]));return t.sort((e,t)=>t.length-e.length)}(t);for(let t of e.set)if(!t.modified.length)for(let e of s)n.push(l.get(t,e));return r}}const u=new r.uY;class c{constructor(e,t,i,n){this.tags=e,this.mode=t,this.context=i,this.next=n}get opaque(){return 0==this.mode}get inherit(){return 1==this.mode}sort(e){return!e||e.depth<this.depth?(this.next=e,this):(e.next=this.sort(e.next),e)}get depth(){return this.context?this.context.length:0}}c.empty=new c([],2,null);const d=o.define,h=d(),f=d(),p=d(f),g=d(f),m=d(),v=d(m),b=d(m),O=d(),w=d(O),y=d(),$=d(),x=d(),k=d(x),S=d(),P={comment:h,lineComment:d(h),blockComment:d(h),docComment:d(h),name:f,variableName:d(f),typeName:p,tagName:d(p),propertyName:g,attributeName:d(g),className:d(f),labelName:d(f),namespace:d(f),macroName:d(f),literal:m,string:v,docString:d(v),character:d(v),attributeValue:d(v),number:b,integer:d(b),float:d(b),bool:d(m),regexp:d(m),escape:d(m),color:d(m),url:d(m),keyword:y,self:d(y),null:d(y),atom:d(y),unit:d(y),modifier:d(y),operatorKeyword:d(y),controlKeyword:d(y),definitionKeyword:d(y),moduleKeyword:d(y),operator:$,derefOperator:d($),arithmeticOperator:d($),logicOperator:d($),bitwiseOperator:d($),compareOperator:d($),updateOperator:d($),definitionOperator:d($),typeOperator:d($),controlOperator:d($),punctuation:x,separator:d(x),bracket:k,angleBracket:d(k),squareBracket:d(k),paren:d(k),brace:d(k),content:O,heading:w,heading1:d(w),heading2:d(w),heading3:d(w),heading4:d(w),heading5:d(w),heading6:d(w),contentSeparator:d(O),list:d(O),quote:d(O),emphasis:d(O),strong:d(O),link:d(O),monospace:d(O),strikethrough:d(O),inserted:d(),deleted:d(),changed:d(),invalid:d(),meta:S,documentMeta:d(S),annotation:d(S),processingInstruction:d(S),definition:o.defineModifier("definition"),constant:o.defineModifier("constant"),function:o.defineModifier("function"),standard:o.defineModifier("standard"),local:o.defineModifier("local"),special:o.defineModifier("special")};for(let e in P){let t=P[e];t instanceof o&&(t.name=e)}!function(e,t){let i=Object.create(null);for(let t of e)if(Array.isArray(t.tag))for(let e of t.tag)i[e.id]=t.class;else i[t.tag.id]=t.class;let{scope:n,all:r=null}=t||{}}([{tag:P.link,class:"tok-link"},{tag:P.heading,class:"tok-heading"},{tag:P.emphasis,class:"tok-emphasis"},{tag:P.strong,class:"tok-strong"},{tag:P.keyword,class:"tok-keyword"},{tag:P.atom,class:"tok-atom"},{tag:P.bool,class:"tok-bool"},{tag:P.url,class:"tok-url"},{tag:P.labelName,class:"tok-labelName"},{tag:P.inserted,class:"tok-inserted"},{tag:P.deleted,class:"tok-deleted"},{tag:P.literal,class:"tok-literal"},{tag:P.string,class:"tok-string"},{tag:P.number,class:"tok-number"},{tag:[P.regexp,P.escape,P.special(P.string)],class:"tok-string2"},{tag:P.variableName,class:"tok-variableName"},{tag:P.local(P.variableName),class:"tok-variableName tok-local"},{tag:P.definition(P.variableName),class:"tok-variableName tok-definition"},{tag:P.special(P.variableName),class:"tok-variableName2"},{tag:P.definition(P.propertyName),class:"tok-propertyName tok-definition"},{tag:P.typeName,class:"tok-typeName"},{tag:P.namespace,class:"tok-namespace"},{tag:P.className,class:"tok-className"},{tag:P.macroName,class:"tok-macroName"},{tag:P.propertyName,class:"tok-propertyName"},{tag:P.operator,class:"tok-operator"},{tag:P.comment,class:"tok-comment"},{tag:P.meta,class:"tok-meta"},{tag:P.invalid,class:"tok-invalid"},{tag:P.punctuation,class:"tok-punctuation"}]);const T={inf:161,nan:162,bool:1,ignoring:2,on:3,group_left:4,group_right:5,offset:6},E=(e,t)=>T[e.toLowerCase()]||-1,z={avg:8,atan2:7,bottomk:9,count:10,count_values:11,group:12,max:13,min:14,quantile:15,limitk:16,limit_ratio:17,stddev:18,stdvar:19,sum:20,topk:21,by:22,without:23,and:24,or:25,unless:26,start:27,end:28},Q=(e,t)=>z[e.toLowerCase()]||-1,A=function(e){let t=Object.create(null);for(let i in e){let n=e[i];Array.isArray(n)||(n=[n]);for(let e of i.split(" "))if(e){let i=[],r=2,s=e;for(let t=0;;){if("..."==s&&t>0&&t+3==e.length){r=1;break}let n=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(s);if(!n)throw new RangeError("Invalid path: "+e);if(i.push("*"==n[0]?"":'"'==n[0][0]?JSON.parse(n[0]):n[0]),t+=n[0].length,t==e.length)break;let o=e[t++];if(t==e.length&&"!"==o){r=0;break}if("/"!=o)throw new RangeError("Invalid path: "+e);s=e.slice(t)}let o=i.length-1,a=i[o];if(!a)throw new RangeError("Invalid path: "+e);let l=new c(n,r,o>0?i.slice(0,o):null);t[a]=l.sort(t[a])}}return u.add(t)}({LineComment:P.comment,LabelName:P.labelName,StringLiteral:P.string,NumberDurationLiteral:P.number,NumberDurationLiteralInDurationContext:P.number,Identifier:P.variableName,"Abs Absent AbsentOverTime Acos Acosh Asin Asinh Atan Atanh AvgOverTime Ceil Changes Clamp ClampMax ClampMin Cos Cosh CountOverTime DaysInMonth DayOfMonth DayOfWeek DayOfYear Deg Delta Deriv Exp Floor HistogramAvg HistogramCount HistogramFraction HistogramQuantile HistogramSum DoubleExponentialSmoothing Hour Idelta Increase Irate LabelReplace LabelJoin LastOverTime Ln Log10 Log2 MaxOverTime MinOverTime Minute Month Pi PredictLinear PresentOverTime QuantileOverTime Rad Rate Resets Round Scalar Sgn Sin Sinh Sort SortDesc SortByLabel SortByLabelDesc Sqrt StddevOverTime StdvarOverTime SumOverTime Tan Tanh Time Timestamp Vector Year":P.function(P.variableName),"Avg Bottomk Count Count_values Group LimitK LimitRatio Max Min Quantile Stddev Stdvar Sum Topk":P.operatorKeyword,"By Without Bool On Ignoring GroupLeft GroupRight Offset Start End":P.modifier,"And Unless Or":P.logicOperator,"Sub Add Mul Mod Div Atan2 Eql Neq Lte Lss Gte Gtr EqlRegex EqlSingle NeqRegex Pow At":P.operator,UnaryOp:P.arithmeticOperator,"( )":P.paren,"[ ]":P.squareBracket,"{ }":P.brace,"⚠":P.invalid}),C={__proto__:null,absent_over_time:341,absent:343,abs:345,acos:347,acosh:349,asin:351,asinh:353,atan:355,atanh:357,avg_over_time:359,ceil:361,changes:363,clamp:365,clamp_max:367,clamp_min:369,cos:371,cosh:373,count_over_time:375,days_in_month:377,day_of_month:379,day_of_week:381,day_of_year:383,deg:385,delta:387,deriv:389,exp:391,floor:393,histogram_count:395,histogram_fraction:397,histogram_quantile:399,histogram_stddev:401,histogram_stdvar:403,histogram_sum:405,histogram_avg:407,double_exponential_smoothing:409,hour:411,idelta:413,increase:415,info:417,irate:419,label_replace:421,label_join:423,last_over_time:425,ln:427,log10:429,log2:431,mad_over_time:433,max_over_time:435,min_over_time:437,ts_of_max_over_time:439,ts_of_min_over_time:441,ts_of_last_over_time:443,minute:445,month:447,pi:449,predict_linear:451,present_over_time:453,quantile_over_time:455,rad:457,rate:459,resets:461,round:463,scalar:465,sgn:467,sin:469,sinh:471,sort:473,sort_desc:475,sort_by_label:477,sort_by_label_desc:479,sqrt:481,stddev_over_time:483,stdvar_over_time:485,sum_over_time:487,tan:489,tanh:491,timestamp:493,time:495,vector:497,year:499},I=n.U1.deserialize({version:14,states:"9tOYQPOOO'}QPOOOOQO'#C|'#C|O(SQPO'#C{Q(_QQOOOOQO'#De'#DeO(YQPO'#DdOOQO'#F]'#F]O)lQPO'#FbOYQPO'#F_OYQPO'#FaO0jQSO'#FdO0uQQO'#FcOOQO'#Fc'#FcOOQO'#Fw'#FwOOQO'#Df'#DfOOQO'#Dh'#DhOOQO'#Di'#DiOOQO'#Dj'#DjOOQO'#Dk'#DkOOQO'#Dl'#DlOOQO'#Dm'#DmOOQO'#Dn'#DnOOQO'#Do'#DoOOQO'#Dp'#DpOOQO'#Dq'#DqOOQO'#Dr'#DrOOQO'#Ds'#DsOOQO'#Dt'#DtOOQO'#Du'#DuOOQO'#Dv'#DvOOQO'#Dw'#DwOOQO'#Dx'#DxOOQO'#Dy'#DyOOQO'#Dz'#DzOOQO'#D{'#D{OOQO'#D|'#D|OOQO'#D}'#D}OOQO'#EO'#EOOOQO'#EP'#EPOOQO'#EQ'#EQOOQO'#ER'#EROOQO'#ES'#ESOOQO'#ET'#ETOOQO'#EU'#EUOOQO'#EV'#EVOOQO'#EW'#EWOOQO'#EX'#EXOOQO'#EY'#EYOOQO'#EZ'#EZOOQO'#E['#E[OOQO'#E]'#E]OOQO'#E^'#E^OOQO'#E_'#E_OOQO'#E`'#E`OOQO'#Ea'#EaOOQO'#Eb'#EbOOQO'#Ec'#EcOOQO'#Ed'#EdOOQO'#Ee'#EeOOQO'#Ef'#EfOOQO'#Eg'#EgOOQO'#Eh'#EhOOQO'#Ei'#EiOOQO'#Ej'#EjOOQO'#Ek'#EkOOQO'#El'#ElOOQO'#Em'#EmOOQO'#En'#EnOOQO'#Eo'#EoOOQO'#Ep'#EpOOQO'#Eq'#EqOOQO'#Er'#ErOOQO'#Es'#EsOOQO'#Et'#EtOOQO'#Eu'#EuOOQO'#Ev'#EvOOQO'#Ew'#EwOOQO'#Ex'#ExOOQO'#Ey'#EyOOQO'#Ez'#EzOOQO'#E{'#E{OOQO'#E|'#E|OOQO'#E}'#E}OOQO'#FO'#FOOOQO'#FP'#FPOOQO'#FQ'#FQOOQO'#FR'#FROOQO'#FS'#FSOOQO'#FT'#FTOOQO'#FU'#FUOOQO'#FV'#FVOOQO'#FW'#FWOOQO'#FX'#FXOOQO'#FY'#FYQOQPOOO2`QPO'#C}O2eQPO'#DSO(YQPO,59gO2lQQO,59gO4YQPO,59oO4YQPO,59oO4YQPO,59oO4YQPO,59oO4YQPO,59oO;WQPO,5;uO;WQPO,5;xO;iQPO,5<VOOQO,5:O,5:OOOQO,5;w,5;wO<QQQO,5;yO<XQQO,5;{OOQO'#DQ'#DQO=oQPO'#FeO=}QPO,5<OOOQO,5<O,5<OO>ZQPO,5<OOOQO,5;},5;}O>cQSO'#DOOOQO,59i,59iOOQO,59n,59nO>nQQO,59nOOQO1G/R1G/ROOQO'#DV'#DVO2`QPO'#DWOOQO'#F{'#F{O>xQPO'#F{OYQPO1G/ZOYQPO1G/ZOYQPO1G/ZOYQPO1G/ZOYQPO1G/ZOOQO'#F['#F[OEsQPO'#F[OFOQSO1G1aOOQO1G1d1G1dOFWQPO'#F]OOQO'#Fm'#FmOOQO1G1q1G1qOFcQPO1G1qOOQO1G1e1G1eOOQO'#Ff'#FfOFhQPO,5<POFmQPO,5<UOFrQSO1G1jOF}QPO1G1jOOQO1G1j1G1jOOQO,59j,59jOGVQPO,59jOYQPO'#FpOG_QPO1G/YOOQO1G/Y1G/YOGgQPO,59rOOQO,5<g,5<gO! lQQO7+$uO! |QQO7+$uO!#bQQO7+$uO!#xQQO7+$uO!%aQQO7+$uOOQO,5;v,5;vOOQO7+&{7+&{O!%zQPO7+'QO!&RQPO7+']OOQO1G1k1G1kOOQO1G1p1G1pO!&WQPO,5<]OOQO,5<],5<]OOQO7+'U7+'UO!&lQSO7+'UOOQO-E9o-E9oO!&wQSO1G/UO!'SQPO1G/UOOQO1G/U1G/UO!'[QQO,5<[OOQO-E9n-E9nOOQO7+$t7+$tO!'fQPO1G/^OOQO<<Jl<<JlO!.ZQPO<<JlOOQO<<Jw<<JwOOQO<<Jp<<JpP!.`QSO'#FqOOQO,5<Z,5<ZOOQO7+$p7+$pO!.hQSO7+$pOOQO-E9m-E9mOOQO7+$x7+$xOOQOAN@WAN@WOOQO<<H[<<H[P!.sQSO'#Fo",stateData:"!.{~O$jOSmOS~OWQOXQOYQOZQO[QO]QO^QO_QO`QOaQObQOcQOdQOeQOu^O!Z[O$gVO$hVO$lXO$p_O$q`O$raO$sbO$tcO$udO$veO$wfO$xgO$yhO$ziO${jO$|kO$}lO%OmO%PnO%QoO%RpO%SqO%TrO%UsO%VtO%WuO%XvO%YwO%ZxO%[yO%]zO%^{O%_|O%`}O%a!OO%b!PO%c!QO%d!RO%e!SO%f!TO%g!UO%h!VO%i!WO%j!XO%k!YO%l!ZO%m![O%n!]O%o!^O%p!_O%q!`O%r!aO%s!bO%t!cO%u!dO%v!eO%w!fO%x!gO%y!hO%z!iO%{!jO%|!kO%}!lO&O!mO&P!nO&Q!oO&R!pO&S!qO&T!rO&U!sO&V!tO&W!uO&X!vO&Y!wO&Z!xO&[!yO&]!zO&^!{O&_!|O&`!}O&a#OO&b#PO&c#QO&eWO&fWO&gVO&jZO~O!Z#RO~Of#SOg#SO$l#TO~OU#^OV#WOh#ZOi#[Oj#ZOx#WO{#WO|#WO}#WO!O#XO!P#XO!Q#YO!R#YO!S#YO!T#YO!U#YO!V#YO$`#_O&d#]O~O$g#aO$h#aO&g#aOW$UXX$UXY$UXZ$UX[$UX]$UX^$UX_$UX`$UXa$UXb$UXc$UXd$UXe$UXu$UX!Z$UX$g$UX$h$UX$l$UX$p$UX$q$UX$r$UX$s$UX$t$UX$u$UX$v$UX$w$UX$x$UX$y$UX$z$UX${$UX$|$UX$}$UX%O$UX%P$UX%Q$UX%R$UX%S$UX%T$UX%U$UX%V$UX%W$UX%X$UX%Y$UX%Z$UX%[$UX%]$UX%^$UX%_$UX%`$UX%a$UX%b$UX%c$UX%d$UX%e$UX%f$UX%g$UX%h$UX%i$UX%j$UX%k$UX%l$UX%m$UX%n$UX%o$UX%p$UX%q$UX%r$UX%s$UX%t$UX%u$UX%v$UX%w$UX%x$UX%y$UX%z$UX%{$UX%|$UX%}$UX&O$UX&P$UX&Q$UX&R$UX&S$UX&T$UX&U$UX&V$UX&W$UX&X$UX&Y$UX&Z$UX&[$UX&]$UX&^$UX&_$UX&`$UX&a$UX&b$UX&c$UX&e$UX&f$UX&g$UX&j$UX~Os#eOu#dO&k#gO~O&jZOU$VXV$VXh$VXi$VXj$VXx$VX{$VX|$VX}$VX!O$VX!P$VX!Q$VX!R$VX!S$VX!T$VX!U$VX!V$VX$`$VX$f$VX&d$VX$n$VX$m$VX~O$l#jO~O$n#lO~PYOf#SOg#SOUoaVoahoaioajoaxoa{oa|oa}oa!Ooa!Poa!Qoa!Roa!Soa!Toa!Uoa!Voa$`oa$foa&doa$noa$moa~OP#oOQ#pOR#pOW$oPX$oPY$oPZ$oP[$oP]$oP^$oP_$oP`$oPa$oPb$oPc$oPd$oPe$oPu$oP!Z$oP$g$oP$h$oP$l$oP$p$oP$q$oP$r$oP$s$oP$t$oP$u$oP$v$oP$w$oP$x$oP$y$oP$z$oP${$oP$|$oP$}$oP%O$oP%P$oP%Q$oP%R$oP%S$oP%T$oP%U$oP%V$oP%W$oP%X$oP%Y$oP%Z$oP%[$oP%]$oP%^$oP%_$oP%`$oP%a$oP%b$oP%c$oP%d$oP%e$oP%f$oP%g$oP%h$oP%i$oP%j$oP%k$oP%l$oP%m$oP%n$oP%o$oP%p$oP%q$oP%r$oP%s$oP%t$oP%u$oP%v$oP%w$oP%x$oP%y$oP%z$oP%{$oP%|$oP%}$oP&O$oP&P$oP&Q$oP&R$oP&S$oP&T$oP&U$oP&V$oP&W$oP&X$oP&Y$oP&Z$oP&[$oP&]$oP&^$oP&_$oP&`$oP&a$oP&b$oP&c$oP&e$oP&f$oP&g$oP&j$oP~O$g#xO$h#xO&e#yO&f#yO&g#xO~Ok#}Ol#}O$gVO$hVO&e#|O&f#|O&gVO~O$n$QO~P(_Ox#WOU$TaV$Tah$Tai$Taj$Ta{$Ta|$Ta}$Ta!O$Ta!P$Ta!Q$Ta!R$Ta!S$Ta!T$Ta!U$Ta!V$Ta$`$Ta$f$Ta&d$Ta$n$Ta$m$Ta~O!V$RO$Z$RO$[$RO$]$RO~O!V$RO$Z$RO$[$RO$]$RO$m$UO&k$WO~Os$YOu#dO$n$XO~O$m$ZO$n$]O~P(_OQ#pOR#pOW$oXX$oXY$oXZ$oX[$oX]$oX^$oX_$oX`$oXa$oXb$oXc$oXd$oXe$oXu$oX!Z$oX$g$oX$h$oX$l$oX$p$oX$q$oX$r$oX$s$oX$t$oX$u$oX$v$oX$w$oX$x$oX$y$oX$z$oX${$oX$|$oX$}$oX%O$oX%P$oX%Q$oX%R$oX%S$oX%T$oX%U$oX%V$oX%W$oX%X$oX%Y$oX%Z$oX%[$oX%]$oX%^$oX%_$oX%`$oX%a$oX%b$oX%c$oX%d$oX%e$oX%f$oX%g$oX%h$oX%i$oX%j$oX%k$oX%l$oX%m$oX%n$oX%o$oX%p$oX%q$oX%r$oX%s$oX%t$oX%u$oX%v$oX%w$oX%x$oX%y$oX%z$oX%{$oX%|$oX%}$oX&O$oX&P$oX&Q$oX&R$oX&S$oX&T$oX&U$oX&V$oX&W$oX&X$oX&Y$oX&Z$oX&[$oX&]$oX&^$oX&_$oX&`$oX&a$oX&b$oX&c$oX&e$oX&f$oX&g$oX&j$oX~O$g$eO$h$eO&g$eO~O&h$fO&i$gO~O$g#aO$h#aO&g#aO~O$l$hO~Ou$iO~Ou$jO~Os#eOu#dO&k$mO~O$m$nO&k$mO~O$m$pO$n$rO~O$m$ZO$n$uO~OS$vOT$vOWzaXzaYzaZza[za]za^za_za`zaazabzaczadzaezauza!Zza$gza$hza$lza$pza$qza$rza$sza$tza$uza$vza$wza$xza$yza$zza${za$|za$}za%Oza%Pza%Qza%Rza%Sza%Tza%Uza%Vza%Wza%Xza%Yza%Zza%[za%]za%^za%_za%`za%aza%bza%cza%dza%eza%fza%gza%hza%iza%jza%kza%lza%mza%nza%oza%pza%qza%rza%sza%tza%uza%vza%wza%xza%yza%zza%{za%|za%}za&Oza&Pza&Qza&Rza&Sza&Tza&Uza&Vza&Wza&Xza&Yza&Zza&[za&]za&^za&_za&`za&aza&bza&cza&eza&fza&gza&jza~Ox#WOUwqhwqiwqjwq!Owq!Pwq!Qwq!Rwq!Swq!Twq!Uwq!Vwq$`wq$fwq&dwq$nwq$mwq~OVwq{wq|wq}wq~PNbOV#WO{#WO|#WO}#WO~PNbOV#WOx#WO{#WO|#WO}#WO!O#XO!P#XOUwqhwqiwqjwq$`wq$fwq&dwq$nwq$mwq~O!Qwq!Rwq!Swq!Twq!Uwq!Vwq~P!!^O!Q#YO!R#YO!S#YO!T#YO!U#YO!V#YO~P!!^OV#WOh#ZOj#ZOx#WO{#WO|#WO}#WO!O#XO!P#XO!Q#YO!R#YO!S#YO!T#YO!U#YO!V#YO~OUwqiwq$`wq$fwq&dwq$nwq$mwq~P!$`O&h$wO~P;WO$n$yO~O!V$RO$Z$RO$[$RO$]$RO$m$ea&k$ea~Os#eOu#dO&k$zO~Os$|Ou#dO$n$}O~O$m%OO$n$}O~O$m$da$n$da~P(_O$l#jOWziXziYziZzi[zi]zi^zi_zi`ziazibziczidzieziuzi!Zzi$gzi$hzi$pzi$qzi$rzi$szi$tzi$uzi$vzi$wzi$xzi$yzi$zzi${zi$|zi$}zi%Ozi%Pzi%Qzi%Rzi%Szi%Tzi%Uzi%Vzi%Wzi%Xzi%Yzi%Zzi%[zi%]zi%^zi%_zi%`zi%azi%bzi%czi%dzi%ezi%fzi%gzi%hzi%izi%jzi%kzi%lzi%mzi%nzi%ozi%pzi%qzi%rzi%szi%tzi%uzi%vzi%wzi%xzi%yzi%zzi%{zi%|zi%}zi&Ozi&Pzi&Qzi&Rzi&Szi&Tzi&Uzi&Vzi&Wzi&Xzi&Yzi&Zzi&[zi&]zi&^zi&_zi&`zi&azi&bzi&czi&ezi&fzi&gzi&jzi~O&h%RO~Os#eOu#dO~Os$|Ou#dO$n%SO~Os$|Ou#dO~O",goto:")y$pPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP$q$}%Z%aP%jP%z$qP&T&[PPPPPPPPPPP$q&f&rP&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r&r$q'O'X$q$q$q$q'h$q't(T(]PPP(T$qP(dP(g(m(sPPPPP(zPPP)je^OXY#T#s#t#u#v#w$ZeROXY#T#s#t#u#v#w$ZQ#URR#n#VQ#k#SQ$^#pR%Q$vQ#fZQ$Y#jU$k$U$n${V$|$p%O%TQ#VRQ#`UR#n#UZ#r#W#X#Y#Z#[Y#q#W#X#Y#Z#[R$_#reUOXY#T#s#t#u#v#w$ZeTOXY#T#s#t#u#v#w$ZQ#z#]Q#{#^R$x$gd^OXY#T#s#t#u#v#w$ZR$O#_eYOXY#T#s#t#u#v#w$Zd]OXY#T#s#t#u#v#w$ZR#i[Q#hZV$l$U$n${Q$S#eT$T#f$kR$P#_Q$q$YR%P$qQ$[#mR$t$[S$V#f#hR$o$VQSOQ#bXQ#cYQ#m#TQ$`#sQ$a#tQ$b#uQ$c#vQ$d#wR$s$ZQ#s#WQ#t#XQ#u#YQ#v#ZR#w#[",nodeNames:"⚠ Bool Ignoring On GroupLeft GroupRight Offset Atan2 Avg Bottomk Count CountValues Group Max Min Quantile LimitK LimitRatio Stddev Stdvar Sum Topk By Without And Or Unless Start End LineComment PromQL AggregateExpr AggregateOp AggregateModifier GroupingLabels LabelName QuotedLabelName StringLiteral FunctionCallBody BinaryExpr Pow BoolModifier MatchingModifierClause Mul Div Mod Add Sub Eql Gte Gtr Lte Lss Neq FunctionCall FunctionIdentifier AbsentOverTime Identifier Absent Abs Acos Acosh Asin Asinh Atan Atanh AvgOverTime Ceil Changes Clamp ClampMax ClampMin Cos Cosh CountOverTime DaysInMonth DayOfMonth DayOfWeek DayOfYear Deg Delta Deriv Exp Floor HistogramCount HistogramFraction HistogramQuantile HistogramStdDev HistogramStdVar HistogramSum HistogramAvg DoubleExponentialSmoothing Hour Idelta Increase Info Irate LabelReplace LabelJoin LastOverTime Ln Log10 Log2 MadOverTime MaxOverTime MinOverTime TsOfMaxOverTime TsOfMinOverTime TsOfLastOverTime Minute Month Pi PredictLinear PresentOverTime QuantileOverTime Rad Rate Resets Round Scalar Sgn Sin Sinh Sort SortDesc SortByLabel SortByLabelDesc Sqrt StddevOverTime StdvarOverTime SumOverTime Tan Tanh Timestamp Time Vector Year MatrixSelector NumberDurationLiteralInDurationContext NumberDurationLiteral OffsetExpr ParenExpr SubqueryExpr UnaryExpr UnaryOp VectorSelector LabelMatchers UnquotedLabelMatcher MatchOp EqlSingle EqlRegex NeqRegex QuotedLabelMatcher StepInvariantExpr At AtModifierPreprocessors MetricName",maxTerm:257,nodeProps:[["group",-12,31,37,39,54,137,139,140,141,142,143,145,153,"Expr"]],propSources:[A],skippedNodes:[0,29],repeatNodeCount:3,tokenData:"4k~RwX^#lpq#lqr$ars$tst&luv'Twx'Yxy({yz)Qz{)V{|)[|})c}!O)h!O!P)o!P!Q*u!Q!R*z!R![+u![!]1O!^!_1z!_!`2X!`!a2n!b!c2{!c!}3Q!}#O3h#P#Q3m#Q#R3r#R#S3Q#S#T3w#T#o3Q#o#p4a#q#r4f#y#z#l$f$g#l#BY#BZ#l$IS$I_#l$I|$JO#l$JT$JU#l$KV$KW#l&FU&FV#l~#qY$j~X^#lpq#l#y#z#l$f$g#l#BY#BZ#l$IS$I_#l$I|$JO#l$JT$JU#l$KV$KW#l&FU&FV#l~$dQ!_!`$j#r#s$o~$oO!V~~$tO$]~~$yWu~OY$tZr$trs%cs#O$t#O#P%h#P;'S$t;'S;=`&f<%lO$t~%hOu~~%kRO;'S$t;'S;=`%t;=`O$t~%yXu~OY$tZr$trs%cs#O$t#O#P%h#P;'S$t;'S;=`&f;=`<%l$t<%lO$t~&iP;=`<%l$t~&qSm~OY&lZ;'S&l;'S;=`&}<%lO&l~'QP;=`<%l&l~'YO}~~'_Wu~OY'YZw'Ywx%cx#O'Y#O#P'w#P;'S'Y;'S;=`(u<%lO'Y~'zRO;'S'Y;'S;=`(T;=`O'Y~(YXu~OY'YZw'Ywx%cx#O'Y#O#P'w#P;'S'Y;'S;=`(u;=`<%l'Y<%lO'Y~(xP;=`<%l'Y~)QO$l~~)VO$n~~)[O{~R)cO&fP!OQ~)hO$m~R)oO&eP!PQ~)rP!Q![)u~)zS&g~!Q![)u!g!h*W#R#S)o#X#Y*W~*ZR{|*d}!O*d!Q![*j~*gP!Q![*j~*oQ&g~!Q![*j#R#S*d~*zO|~~+P[&g~!O!P)o!Q![+u!g!h*W#R#S,m#W#X-X#X#Y*W#[#]-p#a#b.U#g#h.x#k#l/Z#l#m0d#m#n/u~+zZ&g~!O!P)o!Q![+u!g!h*W#R#S,m#W#X-X#X#Y*W#[#]-p#a#b.U#g#h.x#k#l/Z#m#n/u~,pP!Q![,s~,xT&g~!O!P)o!Q![,s!g!h*W#R#S,m#X#Y*W~-^P&g~!Q![-a~-dS!Q![-a#[#]-p#a#b.U#g#h.x~-uP&g~!Q![-x~-{R!Q![-x#a#b.U#g#h.x~.ZQ&g~!Q![.a#g#h.s~.dR!Q![.a#a#b.m#g#h.x~.pP#g#h.s~.xO&g~~.}P&g~!Q![/Q~/TQ!Q![/Q#a#b.m~/`P&g~!Q![/c~/fT!Q![/c#W#X-X#[#]-p#a#b.U#g#h.x~/zP&g~!Q![/}~0QU!Q![/}#W#X-X#[#]-p#a#b.U#g#h.x#k#l/Z~0gR!Q![0p!c!i0p#T#Z0p~0uR&g~!Q![0p!c!i0p#T#Z0pV1VT&iS!ZR!Q![1f![!]1f!c!}1f#R#S1f#T#o1fR1kT!ZR!Q![1f![!]1f!c!}1f#R#S1f#T#o1f~2PP!U~!_!`2S~2XO!T~~2^Q$ZP!_!`2d#r#s2iQ2iO!QQ~2nO$[~~2sP!S~!_!`2v~2{O!R~~3QO$`~V3XT!ZRsS!Q![3Q![!]1f!c!}3Q#R#S3Q#T#o3Q~3mO&d~~3rO&h~~3wOx~~3zTO#S3w#S#T%c#T;'S3w;'S;=`4Z<%lO3w~4^P;=`<%l3w~4fO&j~~4kO&k~",tokenizers:[0,1,2],topRules:{PromQL:[0,30],MetricName:[1,156]},specialized:[{term:57,get:(e,t)=>E(e)<<1,external:E},{term:57,get:(e,t)=>Q(e)<<1|1,external:Q,extend:!0},{term:57,get:e=>C[e]||-1}],tokenPrec:0})}},g={};function m(e){var t=g[e];if(void 0!==t)return t.exports;var i=g[e]={id:e,loaded:!1,exports:{}};return p[e].call(i.exports,i,i.exports,m),i.loaded=!0,i.exports}m.m=p,m.amdO={},m.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return m.d(t,{a:t}),t},d=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,m.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var i=Object.create(null);m.r(i);var n={};c=c||[null,d({}),d([]),d(d)];for(var r=2&t&&e;("object"==typeof r||"function"==typeof r)&&!~c.indexOf(r);r=d(r))Object.getOwnPropertyNames(r).forEach(t=>n[t]=()=>e[t]);return n.default=()=>e,m.d(i,n),i},m.d=(e,t)=>{for(var i in t)m.o(t,i)&&!m.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},m.f={},m.e=e=>Promise.all(Object.keys(m.f).reduce((t,i)=>(m.f[i](e,t),t),[])),m.u=e=>e+".js?_cache="+{78:"b47d06db0be36dfd8cc3",138:"e22db2b683184a3f68f2",351:"eca2ae683cc3d0cb9596",452:"2b21d327e72e37b1c8bb",619:"d2184616561dc86728cb",767:"d134cf551dad48bfaf7f",792:"79c48dbde1c722d712fa",836:"6db109af7f73f8074619",944:"b4c7bab5d72bcf4cb8cf"}[e],m.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),m.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),h={},f="grafana-metricsdrilldown-app:",m.l=(e,t,i,n)=>{if(h[e])h[e].push(t);else{var r,s;if(void 0!==i)for(var o=document.getElementsByTagName("script"),a=0;a<o.length;a++){var l=o[a];if(l.getAttribute("src")==e||l.getAttribute("data-webpack")==f+i){r=l;break}}r||(s=!0,(r=document.createElement("script")).charset="utf-8",r.timeout=120,m.nc&&r.setAttribute("nonce",m.nc),r.setAttribute("data-webpack",f+i),r.src=e,0!==r.src.indexOf(window.location.origin+"/")&&(r.crossOrigin="anonymous"),r.integrity=m.sriHashes[n],r.crossOrigin="anonymous"),h[e]=[t];var u=(t,i)=>{r.onerror=r.onload=null,clearTimeout(c);var n=h[e];if(delete h[e],r.parentNode&&r.parentNode.removeChild(r),n&&n.forEach(e=>e(i)),t)return t(i)},c=setTimeout(u.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=u.bind(null,r.onerror),r.onload=u.bind(null,r.onload),s&&document.head.appendChild(r)}},m.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},m.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),m.p="public/plugins/grafana-metricsdrilldown-app/",m.sriHashes={78:"sha256-CCc6Xp9SgOVlvDqSkxbIrUREQ0bDfUYfBuxNWC/mDzw=",138:"sha256-V7gPM30hNdhf8/K618q8te8ou1DUiq752tkK6ET2ork=",351:"sha256-Z08NkGCe1gWkz/2heTpjjN/l1uiUIPTNLSC0Hs5im64=",452:"sha256-pwlVYYLoNyru74MFMDfkcFZHjwq5kQmSaW4UxnWvo4A=",619:"sha256-PRHpZcZFpI10E2alMINDGDZjgdXwQj6+D7Fih3/YbR0=",767:"sha256-Q5bkSJ3/0ztTVIaPo4ut3TvpYazZRbG5l5x0Yche4ug=",792:"sha256-n9glSvUoslqv4tg53t94pHnbLrTU+AZ5rQm88Td1s2I=",836:"sha256-2HdxVOoHyajdkNlSWbtPt59UkpWmKcGVWPTJeBodCi8=",944:"sha256-OVZI3gSJuxoudok5S/svrimmNB8Q45bCdne9r4Izhiw="},(()=>{m.b=document.baseURI||self.location.href;var e={231:0};m.f.j=(t,i)=>{var n=m.o(e,t)?e[t]:void 0;if(0!==n)if(n)i.push(n[2]);else{var r=new Promise((i,r)=>n=e[t]=[i,r]);i.push(n[2]=r);var s=m.p+m.u(t),o=new Error;m.l(s,i=>{if(m.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var r=i&&("load"===i.type?"missing":i.type),s=i&&i.target&&i.target.src;o.message="Loading chunk "+t+" failed.\n("+r+": "+s+")",o.name="ChunkLoadError",o.type=r,o.request=s,n[1](o)}},"chunk-"+t,t)}};var t=(t,i)=>{var n,r,[s,o,a]=i,l=0;if(s.some(t=>0!==e[t])){for(n in o)m.o(o,n)&&(m.m[n]=o[n]);if(a)a(m)}for(t&&t(i);l<s.length;l++)r=s[l],m.o(e,r)&&e[r]&&e[r][0](),e[r]=0},i=self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))})();var v={};return(()=>{"use strict";m.r(v),m.d(v,{plugin:()=>h});var e=m(1308),t=m.n(e);m.p=t()&&t().uri?t().uri.slice(0,t().uri.lastIndexOf("/")+1):"public/plugins/grafana-metricsdrilldown-app/";var i=m(7781),n=m(2007),r=m(5959),s=m.n(r);const o=(0,r.lazy)(()=>Promise.all([m.e(944),m.e(138),m.e(836),m.e(452)]).then(m.bind(m,7452)));const a=[{id:`${m(2533).id}/label-breakdown-component/v1`,title:"Label Breakdown",description:"A metrics label breakdown view from the Metrics Drilldown app.",component:e=>s().createElement(r.Suspense,{fallback:s().createElement("div",null,"Loading...")},s().createElement(o,e))}];var l=m(7818),u=m(2445);function c(e,t,i,n,r,s,o){try{var a=e[s](o),l=a.value}catch(e){return void i(e)}a.done?t(l):Promise.resolve(l).then(n,r)}const d=(0,r.lazy)(()=>{return(e=function*(){const{wasmSupported:e}=yield Promise.all([m.e(944),m.e(619)]).then(m.bind(m,619)),{default:t}=yield m.e(944).then(m.bind(m,6944));if(e())try{yield t()}catch(e){u.v.error(e,{message:"Error while initializing outlier detection"})}else u.v.warn("WASM not supported, outlier detection will not work");return Promise.all([m.e(944),m.e(138),m.e(792),m.e(836),m.e(351)]).then(m.bind(m,5351))},function(){var t=this,i=arguments;return new Promise(function(n,r){var s=e.apply(t,i);function o(e){c(s,n,r,o,a,"next",e)}function a(e){c(s,n,r,o,a,"throw",e)}o(void 0)})})();var e}),h=(new i.AppPlugin).setRootPage(e=>s().createElement(r.Suspense,{fallback:s().createElement(n.LoadingPlaceholder,{text:""})},s().createElement(d,e)));for(const e of l.Ge)h.addLink(e);for(const e of a)h.exposeComponent(e)})(),v})());
//# sourceMappingURL=module.js.map