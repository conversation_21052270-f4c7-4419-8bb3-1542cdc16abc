import { defineConfig } from '#q-app/wrappers'
import {config} from "dotenv";

export default defineConfig((ctx) => { // can be async too
  const my_env_file = process.env.NODE_ENV === 'development' ? '../.env' : './.env';
  config({ path: my_env_file });

  return {
    css: ['app.scss'],
    extras: [
      'roboto-font',
      'material-icons',
    ],
    build: {
      target: {
        browser: ['es2019', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
        node: 'node20'
      },
      vueRouterMode: 'hash',
      vitePlugins: [
        //check
      ],
      env: {
        WORKFLOW_API_URL: process.env.WORKFLOW_API_URL,
        LAGER_DB_URL: process.env.LAGER_DB_URL,
        IMAGE_STATION_URL: process.env.IMAGE_STATION_URL
      }
    },
    devServer: {
      open: true, // opens browser window automatically
      hot: true
    },
    framework: {
      config: {},
      plugins: [
        'Notify'  // Add Notify plugin here
      ],
      lang: 'de'
    },
    ssr: {
      prodPort: 3001,
      middlewares: ['render']
    },
    pwa: {
      workboxMode: 'generateSW',
      injectPwaMetaTags: true,
      swFilename: 'sw.js',
      manifestFilename: 'manifest.json'
    },
    capacitor: {
      hideSplashscreen: true
    },
    electron: {
      inspectPort: 5858,
      bundler: 'packager',
      builder: {
        appId: 'itf-workflow-app'
      }
    },
    bex: {
      contentScripts: ['my-content-script']
    }
  };
});
