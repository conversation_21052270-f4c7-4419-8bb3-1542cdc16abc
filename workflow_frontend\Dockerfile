# development stage
FROM node:22-alpine as dev-stage
# make the 'app' folder the current working directory
WORKDIR /app
# copy both 'package.json' and 'package-lock.json' (if available)
COPY package*.json ./
# copy .env file
COPY --from=main_workflow_dir .env .env
# install project dependencies
RUN npm install
# Install Quasar CLI globally
RUN npm install -g @quasar/cli
# copy project files and folders to the current working directory (i.e. 'app' folder)
COPY . .

# build stage
FROM dev-stage as build-stage
# build app for production
RUN npm run build

# production stage
FROM httpd:2.4 as production-stage
# Copy your quasar app's built files from build-stage to Apache's default document root
COPY --from=build-stage /app/dist/spa /usr/local/apache2/htdocs/
## Expose the port 80
EXPOSE 80
