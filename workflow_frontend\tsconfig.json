{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "Node", "strict": true, "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist"]}