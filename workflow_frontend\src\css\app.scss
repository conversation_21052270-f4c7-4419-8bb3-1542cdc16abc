/* General Table Styling */
.q-table {
  width: 100%;
  border-collapse: collapse; /* Ensure borders don't double up */
}

/* Header Styling */
.q-table thead th {
  background-color: #f5f5f5; /* Light grey background for headers */
  font-weight: bold;         /* Bold text for headers */
  padding: 10px;             /* Padding for header cells */
  border-bottom: 2px solid #d3d3d3; /* Solid bottom border for header */
}

/* Table Cell Styling */
.q-table th,
.q-table td {
  padding: 10px 14px;              /* Padding for all cells */
  border-right: 1px solid #d3d3d3; /* Solid vertical border for columns */
  border-bottom: 2px dashed #b0b0b0; /* Dashed horizontal border for rows */
}

/* Remove right border for the last cell in each row */
.q-table td:last-child,
.q-table th:last-child {
  border-right: none;
}

/* Add solid bottom border for the last row */
.q-table tbody tr:last-child td {
  border-bottom: 2px solid #d3d3d3;
}

/* Center the button inside the Actions column */
.action-btn-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px; /* Add spacing between buttons */
}

/* Hover Effect for Rows */
.q-table tbody tr:hover {
  background-color: #f0f0f0;
}

/* Page Padding */
.q-page {
  padding: 40px;
}

.myTopDrawer {
  justify-content: space-between;
  align-items: center;
  padding-bottom: 30px;
  padding-top: 30px;
  background-color: #333;
  height: 150px;
  color: white;
}