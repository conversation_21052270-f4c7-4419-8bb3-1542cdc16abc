<script setup>
import workflow_overview_img from '../assets/workflow_overview.jpg';
</script>

<template>
  <q-page>
    <div class="helpPageContainer">
      <div class="text-h3" style="padding: 30px 0px;">Hilfe</div>
       <section class="Overview">
      Die kontinuierlich zunehmende Flut von Datenträgern in Strafverfahren erhöht auch den Umfang forensischer Untersuchungen.
      Eine qualitativ hochwertige Analyse großer Datenmengen nimmt viel Zeit in Anspruch.
      Zusätzlich steigt die Herausforderung einen Überblick über alle Untersuchungsgegenstände und laufenden Analysen zu behalten.
      <br> Die Automatisierung von Aufgaben kann hier Ressourcen freilegen und Fachkräfte von Routineaufgaben entlasten.
         Mit unserer <strong>IT-Forensischen Workflow Automation Software</strong> können sich Analy<PERSON> wieder mehr auf die eigentliche IT-forensische Analyse konzentrieren.
      Sie bringt Automatisierungen von Aufgaben zusammen und sorgt dafür, dass diese ausgeführt werden. Dazu sammelt sie
      fortwährend Informationen über jeden Untersuchungsgegenstand und kennt den aktuellen Fortschritt eines Asservats in jedem Schritt des IT-Forensischen Workflows.
      Die <strong>IT-Forensischen Workflow Automation Software</strong> stellt damit einen Ort dar, an welchem Fälle und Asservate übersichtlich verwaltet und
      Untersuchungsfortschritte überwacht werden können.
       </section>
      <img :src="workflow_overview_img" alt="Overview Image" class="overview-img" />

      <section class="Overview">
        <div class="text-h4 q-py-lg">Stationen / Schritte</div>
        Ein Asservat durchläuft die Stationen von links nach rechts.
        <ul>
          <li><strong>LagerDB:</strong> Der zugehörige Fall wird in der LagerDB aufgenommen und die Lagerposition der
            Asservate hinterlegt.
          </li>
          <li><strong>ImageStation:</strong> Die ImageStation erstellt ein digitales Abbild des Speichermediums
            (Asservat)
            werden erstellt, die Datenintegrität wird durch automatische Hash-Prüfung sichergestellt.
          </li>
          <li><strong>  FOCUS.AI (DigiFors Triage-Tool):</strong> Triage der Asservate, um Aufwandsschätzung und
            Priorisierung zu unterstützen. <span style="font-style: italic">[temporär deaktiviert]</span>
          </li>
          <li><strong>InvestiGator:</strong> Automatisierte Verarbeitung und Voranalyse der Daten im IT-Forensik
            Werkzeug X-Ways.
          </li>
          <li><strong>Detailanalyse durch IT-Forensiker:</strong> Durchführung einer detaillierte Untersuchung der
            aufbereiteten Daten durch IT-Forensische Experten.
          </li>
          <li><strong>IT-Forensik Tools und Gutachten:</strong> Erstellung von Berichten und Gerichts-feste Gutachten.
            Dabei unterstützt IT-forensische Software aus dem DigiFors Toolkit.
          </li>
        </ul>
        <div class="text-h4 q-py-lg" style="margin-top: 10px">Fortschrittsanzeige von Prozessen</div>
        <div>
          Ein <strong>Prozess</strong> stellt einen Job / eine Aufgabe im IT-Forensischen Workflow dar.
          Es gibt Jobs, welche eine Software durchführen soll, und es gibt Aufgaben, welche von einem echten Menschen
          bearbeitet werden (müssen).
          Zum Beispiel: Imaging eines Datenträgers durch die ImageStation Software oder Gutachten-Erstellung durch einen
          IT-Forensischen Analysten
        </div>

        <div style="padding-top: 2em">
          Folgende <strong>Fortschrittsanzeigen</strong> sind für einen Prozess möglich:
          <ul>
            <li>
              <q-badge color="light-blue-14">in der Warteschlange</q-badge>:
              Der Job wurde in die Datenbank aufgenommen und wartet nun auf Bearbeitung.
            </li>
            <li>
              <q-badge color="light-blue-14">zugewiesen</q-badge>:
              Der Job wurde einer Software-Instanz (z. B. InvestiGator oder FOCUS.AI)
              als nächste Aufgabe zugeteilt und wird demnächst von dieser bearbeitet.
            </li>
            <li>
              <q-badge color="amber-7">Verarbeitung läuft</q-badge>:
              Die entsprechende Software bearbeitet die Aufgabe
              (z. B. ImageStation erzeugt ein Image eines Datenträgers oder FOCUS.AI scannt ein EWF-Image).
            </li>
            <li>
              <q-badge color="light-green-9">fertig</q-badge>:
              Die Aufgabe wurde erfolgreich bearbeitet. Der Job ist fertig.
            </li>
            <li>
              <q-badge color="red-10">fehlgeschlagen</q-badge>:
              Bei der Bearbeitung der Aufgabe ist ein Fehler ist aufgetreten. Der Job wurde nicht erfolgreich
              durchgeführt.
            </li>
            <li>
              <q-badge color="grey-8">gestoppt</q-badge>:
              Die Aufgabe wurde manuell (zum Beispiel über die Workflow-Oberfläche) abgebrochen und wird daher nicht bearbeitet.
            </li>
          </ul>
        </div>
      </section>
    </div>
  </q-page>
</template>

<style scoped>
.helpPageContainer {
  display: flex;
  flex-direction: column;
  margin: auto;
}

.overview-img {
  width: 100%;
  padding: 30px 0px;
}

.Overview {
  background-color: #f2f7ff;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-size: 1rem;
  color: #333;
}

.Overview h3 {
  margin-bottom: 15px;
  color: #2c3e50;
}

.Overview ul {
  padding-left: 20px;
}

.Overview li {
  margin-bottom: 10px;
}

</style>