{"version": 3, "file": "619.js?_cache=d2184616561dc86728cb", "mappings": "+PAEO,SAASA,EAA2BC,G,IAC1BA,EAAf,MAAMC,EAAwB,QAAfD,EAAAA,EAAME,OAAO,UAAbF,IAAAA,OAAAA,EAAAA,EAAiBC,OAEhC,IAAKA,EACH,OAAO,KAGT,MAAME,EAAOC,OAAOD,KAAKF,GACzB,OAAoB,IAAhBE,EAAKE,OACA,KAGFJ,EAAOE,EAAK,GACrB,CCMA,MAAMG,EAAmB,CAACC,EAAqBC,EAAiC,SAC9E,MAAMC,EACU,QAAdD,EAAsB,CAACE,EAAGC,KAAMC,EAAAA,EAAAA,GAAcF,EAAGC,GAAK,CAACD,EAAGC,KAAMC,EAAAA,EAAAA,GAAcD,EAAGD,GAEnF,OAAOH,EAAOM,KAAK,CAACH,EAAGC,KACrB,MAAMG,EAASf,EAA2BW,GAC1C,IAAKI,EACH,OAAO,EAGT,MAAMC,EAAShB,EAA2BY,GAC1C,OAAKI,EAIEN,EAAUK,EAAQC,GAHhB,KAQPC,EAAqB,CAACT,EAAqBU,EAAgBT,EAAiC,SAChG,MAAMU,EAAeC,EAAAA,cAAcC,IAAIH,GAEjCI,EAAcd,EAAOe,IAAKC,I,IAShBL,EARd,MAAMM,EAAQD,EAAUrB,OAAO,GAC/B,IAAKsB,EACH,MAAO,CACLC,MAAO,EACPF,a,IAIUL,E,IAELO,EADT,MAAO,CACLA,MAAoB,QAAbA,GAFwC,QAAnCP,EAAmB,QAAnBA,EAAAA,EAAaQ,cAAbR,IAAAA,OAAAA,EAAAA,EAAAA,KAAAA,EAAsBM,GAAO,GAAM,UAAnCN,IAAAA,EAAAA,GAA4CS,EAAAA,EAAAA,iBAAgBH,GAAO,GAAM,IAExEP,UAANQ,IAAAA,EAAAA,EAAiB,EACxBF,eAMJ,OAFAF,EAAYR,KAAmB,QAAdL,EAAsB,CAACE,EAAGC,IAAMD,EAAEe,MAAQd,EAAEc,MAAQ,CAACf,EAAGC,IAAMA,EAAEc,MAAQf,EAAEe,OAEpFJ,EAAYC,IAAI,EAAGC,eAAgBA,IAqBtCK,EAAerB,IAEnB,MAAMsB,GAASC,EAAAA,EAAAA,qBAAoB,CAAEC,OAAQxB,IAC7C,IAAKsB,EACH,MAAM,IAAIG,MAAM,gDAIlB,MACMC,EADeJ,EAAO3B,OAAOgC,OAAQC,GAAMA,EAAEC,OAASC,EAAAA,UAAUC,QAC1ChB,IAAKf,GAAW,IAAIgC,aAAahC,EAAOiC,SAEpE,OAAOC,EAAAA,gBAAgBC,OAAO,CAAEC,YAAa,KAAOC,OAAOX,IAGvDY,EAAwB,CAACC,EAAyBC,IAClDD,EAASE,cAAcD,GAAOE,WACxBH,EAASE,cAAcD,GAAOG,iBAAiB7C,OAElD,EAGI8C,GAAaC,EAAAA,EAAAA,SACxB,CAACC,EAAyBpC,EAA4BT,EAAiC,SACrF,IAAK6C,EAAWhD,OACd,MAAO,GAGT,MAAME,EAAS,IAAI8C,GAGnB,GAAe,iBAAXpC,EACF,OAAOX,EAAiBC,EAAQ,OAGlC,GAAe,0BAAXU,EACF,OAAOX,EAAiBC,EAAQ,QAIlC,GAAe,aAAXU,EACF,IACE,MA1De,EAACV,EAAqBC,EAA4B,SACvE,IAAK8C,IACH,MAAM,IAAItB,MAAM,sBAGlB,MAAMc,EAAWlB,EAAYrB,GAEvBc,EAAcd,EAAOe,IAAI,CAACC,EAAWwB,KAAW,CACpDtB,MAAOoB,EAAsBC,EAAUC,GACvCxB,UAAWA,KAKb,OAFAF,EAAYR,KAAmB,QAAdL,EAAsB,CAACE,EAAGC,IAAMD,EAAEe,MAAQd,EAAEc,MAAQ,CAACf,EAAGC,IAAMA,EAAEc,MAAQf,EAAEe,OAEpFJ,EAAYC,IAAI,EAAGC,eAAgBA,IA4C7BgC,CAAehD,EAAQC,EAChC,CAAE,MAAOgD,GACP,MAAMC,EAAM,4CAA4C,EAAaC,eAGrE,OAFAC,EAAAA,EAAAA,IAAe,CAACF,EAAK,6EAEdzC,EAAmBT,EAAQqD,EAAAA,UAAUC,OAAQrD,EACtD,CAIF,OAAOQ,EAAmBT,EAAQU,EAAQT,IAE5C,CAACD,EAAqBU,EAAgBT,EAAiC,SACrE,MAAMsD,EAAiBC,EAAiBxD,GAAUA,EAAO,GAAGL,OAAO,GAAGsC,OAAO,GAAK,EAC5EwB,EAAgBD,EAAiBxD,GACnCA,EAAOA,EAAOF,OAAS,GAAGH,OAAO,GAAGsC,OAAOjC,EAAOA,EAAOF,OAAS,GAAGH,OAAO,GAAGsC,OAAOnC,OAAS,GAC/F,EAOJ,MAFY,GAHOE,EAAOF,OAAS,EAAIN,EAA2BQ,EAAO,IAAM,MAC7DA,EAAOF,OAAS,EAAIN,EAA2BQ,EAAOA,EAAOF,OAAS,IAAM,MAEpDyD,KAAkBE,KAAiBzD,EAAOF,UAAUY,KAAUT,MAM5G,SAASuD,EAAiBxD,GACxB,OAAOA,EAAOF,OAAS,GAAKE,EAAO,GAAGL,OAAOG,OAAS,GAAKE,EAAO,GAAGL,OAAO,GAAGsC,OAAOnC,OAAS,CACjG,CAEO,MAAMiD,EAAgB,KAC3B,MAAMW,EAAiC,iBAAhBC,YAMvB,OAJKD,IACHE,EAAAA,EAAAA,GAAqB,qBAAsB,CAAC,GAGvCF,E,woBCHT,MAAMG,EAA0B,2BAEzB,SAASD,EAAwEE,EAAUC,IAChGC,EAAAA,EAAAA,mBAAkB,GAAGH,IAA0BC,IAAS,E,kUAAA,IACnDC,GAAAA,CACHE,KAAM,CAEJC,WAAYC,EAAAA,OAAOC,KAAKC,EAAAA,IAAWC,QACnCC,WAAYC,EAAAA,KAGlB,CAKA,SAASC,EAAwBC,EAAeC,GAC9Cf,EAAqB,uBAAwB,CAC3Cc,QACAC,SACAC,MAAO,gBAEX,CAwCO,SAASC,EAA2BC,EAAmCC,GACxED,EAAWhF,SAAWiF,EAAWjF,OApCvC,SAA8BgF,EAAmCC,GAC/D,IAAK,MAAMC,KAAaD,EACtB,IAAK,MAAME,KAAaH,EAClBE,EAAUE,MAAQD,EAAUC,KAAOF,EAAU9D,QAAU+D,EAAU/D,OACnEuD,EAAwBO,EAAUE,IAAK,UAI/C,CA8BIC,CAAqBL,EAAYC,GACxBD,EAAWhF,OAASiF,EAAWjF,OA1B5C,SAA8BgF,EAAmCC,GAC/D,IAAK,MAAMC,KAAaD,EACFD,EAAWM,KAAMH,GAAcA,EAAUC,MAAQF,EAAUE,MAE7ET,EAAwBO,EAAUE,IAAK,UAG7C,CAqBIG,CAAqBP,EAAYC,GAhBrC,SAA4BD,EAAmCC,GAC7D,IAAK,MAAME,KAAaH,GACPC,EAAWK,KAAMJ,GAAcA,EAAUE,MAAQD,EAAUC,MAExET,EAAwBQ,EAAUC,IAAK,QAG7C,CAYII,CAAmBR,EAAYC,EAEnC,C,0oBCnOO,SAASQ,EAAaC,EAAcC,GACzC,MAAMC,EAAUD,EAAKtE,OAAO,CAACwE,EAAKzC,EAAK0C,IAAO,E,kUAAA,IAAKD,GAAAA,CAAK,CAAC,OAAOC,EAAI,KAAM1C,IAAQ,CAAE2C,WAAY,iBAEhGC,EAAAA,EAAON,MAAMA,EAAOE,IAEpBK,EAAAA,EAAAA,gBAAeC,QAAQ,CACrBnE,KAAMoE,EAAAA,UAAUC,WAAWC,KAC3BpC,QAAS0B,GAEb,CAEO,SAASrC,EAAeqC,GAC7BK,EAAAA,EAAOM,KAAKX,IAEZM,EAAAA,EAAAA,gBAAeC,QAAQ,CACrBnE,KAAMoE,EAAAA,UAAUI,aAAaF,KAC7BpC,QAAS0B,GAEb,C,kCCvBO,MAAMpF,EAAgB,IAAIiG,KAAKC,SAAS,KAAM,CAAEnE,YAAa,SAAUoE,O", "sources": ["webpack://grafana-metricsdrilldown-app/./services/levels.ts", "webpack://grafana-metricsdrilldown-app/./services/sorting.ts", "webpack://grafana-metricsdrilldown-app/./interactions.ts", "webpack://grafana-metricsdrilldown-app/./WingmanDataTrail/helpers/displayStatus.ts", "webpack://grafana-metricsdrilldown-app/./WingmanDataTrail/helpers/localCompare.ts"], "sourcesContent": ["import { type DataFrame } from '@grafana/data';\n\nexport function getLabelValueFromDataFrame(frame: DataFrame) {\n  const labels = frame.fields[1]?.labels;\n\n  if (!labels) {\n    return null;\n  }\n\n  const keys = Object.keys(labels);\n  if (keys.length === 0) {\n    return null;\n  }\n\n  return labels[keys[0]];\n}\n", "import { OutlierDetector, type OutlierOutput } from '@bsull/augurs/outlier';\nimport {\n  doStandardCalcs,\n  fieldReducers,\n  FieldType,\n  outerJoinDataFrames,\n  ReducerID,\n  type DataFrame,\n} from '@grafana/data';\nimport { memoize } from 'lodash';\n\nimport { displayWarning } from 'WingmanDataTrail/helpers/displayStatus';\nimport { localeCompare } from 'WingmanDataTrail/helpers/localCompare';\n\nimport { reportExploreMetrics } from '../interactions';\nimport { getLabelValueFromDataFrame } from './levels';\n\nexport type SortSeriesByOption = 'alphabetical' | 'alphabetical-reversed' | 'outliers' | ReducerID.stdDev;\nexport type SortSeriesDirection = 'asc' | 'desc';\n\n// Alphabetical sort\nconst sortAlphabetical = (series: DataFrame[], direction: SortSeriesDirection = 'asc') => {\n  const compareFn: (a: string, b: string) => number =\n    direction === 'asc' ? (a, b) => localeCompare(a, b) : (a, b) => localeCompare(b, a);\n\n  return series.sort((a, b) => {\n    const labelA = getLabelValueFromDataFrame(a);\n    if (!labelA) {\n      return 0;\n    }\n\n    const labelB = getLabelValueFromDataFrame(b);\n    if (!labelB) {\n      return 0;\n    }\n\n    return compareFn(labelA, labelB);\n  });\n};\n\n// Field reducer sort\nconst sortByFieldReducer = (series: DataFrame[], sortBy: string, direction: SortSeriesDirection = 'asc') => {\n  const fieldReducer = fieldReducers.get(sortBy);\n\n  const seriesCalcs = series.map((dataFrame) => {\n    const field = dataFrame.fields[1];\n    if (!field) {\n      return {\n        value: 0,\n        dataFrame,\n      };\n    }\n\n    const value = fieldReducer.reduce?.(field, true, true) ?? doStandardCalcs(field, true, true);\n    return {\n      value: value[sortBy] ?? 0,\n      dataFrame,\n    };\n  });\n\n  seriesCalcs.sort(direction === 'asc' ? (a, b) => a.value - b.value : (a, b) => b.value - a.value);\n\n  return seriesCalcs.map(({ dataFrame }) => dataFrame);\n};\n\n// Outlier sort\nconst sortByOutliers = (series: DataFrame[], direction: 'asc' | 'desc' = 'asc') => {\n  if (!wasmSupported()) {\n    throw new Error('WASM not supported');\n  }\n\n  const outliers = getOutliers(series);\n\n  const seriesCalcs = series.map((dataFrame, index) => ({\n    value: calculateOutlierValue(outliers, index),\n    dataFrame: dataFrame,\n  }));\n\n  seriesCalcs.sort(direction === 'asc' ? (a, b) => a.value - b.value : (a, b) => b.value - a.value);\n\n  return seriesCalcs.map(({ dataFrame }) => dataFrame);\n};\n\nconst getOutliers = (series: DataFrame[]): OutlierOutput => {\n  // Combine all frames into one by joining on time.\n  const joined = outerJoinDataFrames({ frames: series });\n  if (!joined) {\n    throw new Error('Error while joining frames into a single one');\n  }\n\n  // Get number fields: these are our series.\n  const joinedSeries = joined.fields.filter((f) => f.type === FieldType.number);\n  const points = joinedSeries.map((series) => new Float64Array(series.values));\n\n  return OutlierDetector.dbscan({ sensitivity: 0.9 }).detect(points);\n};\n\nconst calculateOutlierValue = (outliers: OutlierOutput, index: number): number => {\n  if (outliers.seriesResults[index].isOutlier) {\n    return -outliers.seriesResults[index].outlierIntervals.length;\n  }\n  return 0;\n};\n\nexport const sortSeries = memoize(\n  (origSeries: DataFrame[], sortBy: SortSeriesByOption, direction: SortSeriesDirection = 'asc') => {\n    if (!origSeries.length) {\n      return [];\n    }\n\n    const series = [...origSeries];\n\n    // Alphabetical sorting\n    if (sortBy === 'alphabetical') {\n      return sortAlphabetical(series, 'asc');\n    }\n\n    if (sortBy === 'alphabetical-reversed') {\n      return sortAlphabetical(series, 'desc');\n    }\n\n    // Outlier detection sorting\n    if (sortBy === 'outliers') {\n      try {\n        return sortByOutliers(series, direction);\n      } catch (e) {\n        const msg = `Error while sorting by outlying series: \"${(e as Error).toString()}\"!`;\n        displayWarning([msg, 'Falling back to standard deviation to identify the most variable series.']);\n\n        return sortByFieldReducer(series, ReducerID.stdDev, direction);\n      }\n    }\n\n    // Field reducer sorting (default case)\n    return sortByFieldReducer(series, sortBy, direction);\n  },\n  (series: DataFrame[], sortBy: string, direction: SortSeriesDirection = 'asc') => {\n    const firstTimestamp = seriesIsNotEmpty(series) ? series[0].fields[0].values[0] : 0;\n    const lastTimestamp = seriesIsNotEmpty(series)\n      ? series[series.length - 1].fields[0].values[series[series.length - 1].fields[0].values.length - 1]\n      : 0;\n\n    const firstValue = series.length > 0 ? getLabelValueFromDataFrame(series[0]) : '';\n    const lastValue = series.length > 0 ? getLabelValueFromDataFrame(series[series.length - 1]) : '';\n\n    const key = `${firstValue}_${lastValue}_${firstTimestamp}_${lastTimestamp}_${series.length}_${sortBy}_${direction}`;\n\n    return key;\n  }\n);\n\nfunction seriesIsNotEmpty(series: DataFrame[]) {\n  return series.length > 0 && series[0].fields.length > 0 && series[0].fields[0].values.length > 0;\n}\n\nexport const wasmSupported = () => {\n  const support = typeof WebAssembly === 'object';\n\n  if (!support) {\n    reportExploreMetrics('wasm_not_supported', {});\n  }\n\n  return support;\n};\n", "import { type AdHocVariableFilter } from '@grafana/data';\nimport { config, reportInteraction } from '@grafana/runtime';\n\nimport { type ExposedComponentName } from 'exposedComponents/components';\nimport { type ActionViewType } from 'MetricActionBar';\nimport { type SortSeriesByOption } from 'services/sorting';\nimport { type SnakeCase } from 'utils/utils.types';\nimport { type LayoutType } from 'WingmanDataTrail/ListControls/LayoutSwitcher';\nimport { type SortingOption as MetricsReducerSortByOption } from 'WingmanDataTrail/ListControls/MetricsSorter/MetricsSorter';\n\nimport { PLUGIN_ID } from './constants';\nimport { GIT_COMMIT } from './version';\n\nexport type ViewName = 'metrics-reducer' | 'metric-details';\n\ntype Interactions = {\n  // User selected a label to view its breakdown.\n  groupby_label_changed: {\n    label: string;\n  };\n  breakdown_panel_selected: {\n    label: string;\n  };\n  // User changed a label filter\n  label_filter_changed: {\n    label: string;\n    action: 'added' | 'removed' | 'changed';\n    cause: 'breakdown' | 'adhoc_filter';\n  };\n  // User changed the breakdown layout\n  breakdown_layout_changed: { layout: LayoutType };\n  // A metric exploration has started due to one of the following causes\n  exploration_started: {\n    cause: 'bookmark_clicked';\n  };\n  // A user has changed a bookmark\n  bookmark_changed: {\n    action: // Toggled on or off from the bookmark icon\n    | 'toggled_on'\n      | 'toggled_off'\n      // Deleted from the sidebar bookmarks list\n      | 'deleted';\n  };\n  // User changes metric explore settings\n  settings_changed: { stickyMainGraph?: boolean };\n  // User clicks on tab to change the action view\n  metric_action_view_changed: {\n    view: ActionViewType;\n\n    // The number of related logs\n    related_logs_count?: number;\n  };\n  // User clicks on one of the action buttons associated with a selected metric\n  selected_metric_action_clicked: {\n    action: // Opens the metric queries in Explore\n    | 'open_in_explore'\n      // Clicks on the share URL button\n      | 'share_url'\n      // Deselects the current selected metrics by clicking the \"Select new metric\" button\n      | 'unselect'\n      // When in embedded mode, clicked to open the exploration from the embedded view\n      | 'open_from_embedded';\n  };\n  // User clicks on one of the action buttons associated with related logs\n  related_logs_action_clicked: {\n    action: // Opens Logs Drilldown\n    | 'open_logs_drilldown'\n      // Logs data source changed\n      | 'logs_data_source_changed';\n  };\n  // User selects a metric\n  metric_selected: {\n    from: // By clicking \"Select\" on a metric panel when on the no-metric-selected metrics list view\n    | 'metric_list'\n      // By clicking \"Select\" on a metric panel when on the related metrics tab\n      | 'related_metrics';\n    // The number of search terms activated when the selection was made\n    searchTermCount: number | null;\n  };\n  // User opens/closes the prefix filter dropdown\n  prefix_filter_clicked: {\n    from: // By clicking \"Select\" on a metric panel when on the no-metric-selected metrics list view\n    | 'metric_list'\n      // By clicking \"Select\" on a metric panel when on the related metrics tab\n      | 'related_metrics';\n    action: // Opens the dropdown\n    | 'open'\n      // Closes the dropdown\n      | 'close';\n  };\n  // User types in the quick search bar\n  quick_search_used: {};\n  sorting_changed:\n    | {\n        // By clicking on the sort by variable in the metrics reducer\n        from: 'metrics-reducer';\n        // The sort by option selected\n        sortBy: MetricsReducerSortByOption;\n      }\n    | {\n        // By clicking on the sort by component in the label breakdown\n        from: 'label-breakdown';\n        // The sort by option selected\n        sortBy: SortSeriesByOption;\n      };\n  wasm_not_supported: {};\n  native_histogram_examples_closed: {};\n  native_histogram_example_clicked: {\n    metric: string;\n  };\n  // User toggles the Wingman sidebar\n  metrics_sidebar_toggled: {\n    action: // Opens the sidebar section\n    | 'opened'\n      // Closes the sidebar section\n      | 'closed';\n    section?: string;\n  };\n  // User clicks into the prefix filter section of the sidebar\n  sidebar_prefix_filter_section_clicked: {};\n  // User applies any prefix filter from the sidebar\n  sidebar_prefix_filter_applied: {\n    // Number of prefix filters applied (optional)\n    filter_count?: number;\n  };\n  // User clicks into the suffix filter section of the sidebar\n  sidebar_suffix_filter_section_clicked: {};\n  // User applies any suffix filter from the sidebar\n  sidebar_suffix_filter_applied: {\n    // Number of suffix filters applied (optional)\n    filter_count?: number;\n  };\n  // User selects a rules filter from the Wingman sidebar\n  sidebar_rules_filter_selected: {\n    filter_type: 'non_rules_metrics' | 'recording_rules';\n  };\n  // User applies a label filter from the sidebar\n  sidebar_group_by_label_filter_applied: {\n    label: string;\n  };\n  app_initialized: {\n    view: ViewName;\n  };\n  // User took an action to view an exposed component\n  exposed_component_viewed: {\n    component: SnakeCase<ExposedComponentName>;\n  };\n  // App migrated some legacy user prefs (see src/UserPreferences/userPreferences.ts)\n  user_preferences_migrated: {};\n};\n\ntype OtherEvents = {\n  extreme_value_filter_behavior_triggered: {\n    expression: string;\n  };\n};\n\ntype AllEvents = Interactions & OtherEvents;\n\nconst INTERACTION_NAME_PREFIX = 'grafana_explore_metrics_';\n\nexport function reportExploreMetrics<E extends keyof AllEvents, P extends AllEvents[E]>(event: E, payload: P) {\n  reportInteraction(`${INTERACTION_NAME_PREFIX}${event}`, {\n    ...payload,\n    meta: {\n      // same naming as Faro (see src/tracking/faro/faro.ts)\n      appRelease: config.apps[PLUGIN_ID].version,\n      appVersion: GIT_COMMIT,\n    },\n  });\n}\n\n/**\n * Reports a single label filter change event\n */\nfunction reportLabelFilterChange(label: string, action: 'added' | 'removed' | 'changed') {\n  reportExploreMetrics('label_filter_changed', {\n    label,\n    action,\n    cause: 'adhoc_filter',\n  });\n}\n\n/**\n * Detects and reports changes to an existing filter\n */\nfunction detectChangedFilters(newFilters: AdHocVariableFilter[], oldFilters: AdHocVariableFilter[]) {\n  for (const oldFilter of oldFilters) {\n    for (const newFilter of newFilters) {\n      if (oldFilter.key === newFilter.key && oldFilter.value !== newFilter.value) {\n        reportLabelFilterChange(oldFilter.key, 'changed');\n      }\n    }\n  }\n}\n\n/**\n * Detects and reports removed filters\n */\nfunction detectRemovedFilters(newFilters: AdHocVariableFilter[], oldFilters: AdHocVariableFilter[]) {\n  for (const oldFilter of oldFilters) {\n    const stillExists = newFilters.some((newFilter) => newFilter.key === oldFilter.key);\n    if (!stillExists) {\n      reportLabelFilterChange(oldFilter.key, 'removed');\n    }\n  }\n}\n\n/**\n * Detects and reports added filters\n */\nfunction detectAddedFilters(newFilters: AdHocVariableFilter[], oldFilters: AdHocVariableFilter[]) {\n  for (const newFilter of newFilters) {\n    const isNew = !oldFilters.some((oldFilter) => oldFilter.key === newFilter.key);\n    if (isNew) {\n      reportLabelFilterChange(newFilter.key, 'added');\n    }\n  }\n}\n\n/** Detect the single change in filters and report the event, assuming it came from manipulating the adhoc filter */\nexport function reportChangeInLabelFilters(newFilters: AdHocVariableFilter[], oldFilters: AdHocVariableFilter[]) {\n  if (newFilters.length === oldFilters.length) {\n    // Same number of filters - check for changed values\n    detectChangedFilters(newFilters, oldFilters);\n  } else if (newFilters.length < oldFilters.length) {\n    // Filters were removed\n    detectRemovedFilters(newFilters, oldFilters);\n  } else {\n    // Filters were added\n    detectAddedFilters(newFilters, oldFilters);\n  }\n}\n", "import { AppEvents } from '@grafana/data';\nimport { getAppEvents } from '@grafana/runtime';\n\nimport { logger } from 'tracking/logger/logger';\n\nexport function displayError(error: Error, msgs: string[]) {\n  const context = msgs.reduce((acc, msg, i) => ({ ...acc, [`info${i + 1}`]: msg }), { handheldBy: 'displayError' });\n\n  logger.error(error, context);\n\n  getAppEvents().publish({\n    type: AppEvents.alertError.name,\n    payload: msgs,\n  });\n}\n\nexport function displayWarning(msgs: string[]) {\n  logger.warn(msgs);\n\n  getAppEvents().publish({\n    type: AppEvents.alertWarning.name,\n    payload: msgs,\n  });\n}\n\nexport function displaySuccess(msgs: string[]) {\n  getAppEvents().publish({\n    type: AppEvents.alertSuccess.name,\n    payload: msgs,\n  });\n}\n", "export const localeCompare = new Intl.Collator('en', { sensitivity: 'base' }).compare;\n"], "names": ["getLabelValueFromDataFrame", "frame", "labels", "fields", "keys", "Object", "length", "sortAlphabetical", "series", "direction", "compareFn", "a", "b", "localeCompare", "sort", "labelA", "labelB", "sortByFieldReducer", "sortBy", "fieldReducer", "fieldReducers", "get", "seriesCalcs", "map", "dataFrame", "field", "value", "reduce", "doStandardCalcs", "getOutliers", "joined", "outerJoinDataFrames", "frames", "Error", "points", "filter", "f", "type", "FieldType", "number", "Float64Array", "values", "OutlierDetector", "dbscan", "sensitivity", "detect", "calculateOutlierValue", "outliers", "index", "seriesResults", "isOutlier", "outlierIntervals", "sortSeries", "memoize", "origSeries", "wasmSupported", "sortByOutliers", "e", "msg", "toString", "displayWarning", "ReducerID", "stdDev", "firstTimestamp", "seriesIsNotEmpty", "lastTimestamp", "support", "WebAssembly", "reportExploreMetrics", "INTERACTION_NAME_PREFIX", "event", "payload", "reportInteraction", "meta", "appRelease", "config", "apps", "PLUGIN_ID", "version", "appVersion", "GIT_COMMIT", "reportLabelFilterChange", "label", "action", "cause", "reportChangeInLabelFilters", "newFilters", "oldFilters", "old<PERSON><PERSON>er", "newFilter", "key", "detectChangedFilters", "some", "detectRemovedFilters", "detectAddedFilters", "displayError", "error", "msgs", "context", "acc", "i", "handheldBy", "logger", "getAppEvents", "publish", "AppEvents", "alertError", "name", "warn", "alertWarning", "Intl", "Collator", "compare"], "sourceRoot": ""}