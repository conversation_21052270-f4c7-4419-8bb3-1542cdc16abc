"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[557],{7319:(e,r,t)=>{t.r(r),t.d(r,{default:()=>d});var o=t(5959),n=t(6089),a=t(7781),i=t(2007);const c=t.p+"b6946652df0df52a6ebf.svg",s=t.p+"7c69e09a44ae38215563.svg";function p(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function l(e,r){return r=null!=r?r:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):function(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,o)}return t}(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}),e}function b(e,r){if(null==e)return{};var t,o,n=function(e,r){if(null==e)return{};var t,o,n={},a=Object.keys(e);for(o=0;o<a.length;o++)t=a[o],r.indexOf(t)>=0||(n[t]=e[t]);return n}(e,r);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)t=a[o],r.indexOf(t)>=0||Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}const u={eye:{secondary:t.p+"3982c6482f693636d827.svg",primary:t.p+"f6f180a5b21afe1799fd.svg",hover:t.p+"da86b141436a1efb9287.svg"},"search-minus":{secondary:t.p+"f4a80ec106902b21468c.svg",primary:t.p+"a02d697b42ff5ef7323b.svg",hover:t.p+"45984fdddc778c4b7076.svg"},"search-plus":{secondary:t.p+"f0b5af7b8afc5505b70a.svg",primary:t.p+"2168a10beed690100fcb.svg",hover:t.p+"26a78975976a604ba774.svg"},"share-alt":{secondary:t.p+"fca8b87950e835f73012.svg",hover:t.p+"3cf91cbcfc3c94965931.svg",primary:""},copy:{secondary:c,hover:s,primary:""}},f=o.forwardRef((e,r)=>{const{variant:t="secondary",name:a,className:c,tooltip:s}=e,f=b(e,["variant","name","className","tooltip"]),d=(0,i.useStyles2)(y,t,a,u);let g,m;return g="string"==typeof s?s:void 0,(0,o.useMemo)(()=>o.createElement(i.Tooltip,{ref:r,content:s},o.createElement("button",l(function(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{},o=Object.keys(t);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.forEach(function(r){p(e,r,t[r])})}return e}({},f),{ref:m,"aria-label":g,className:(0,n.cx)(d.button,c),type:"button"}),o.createElement("span",{className:d.img}))),[g,r,c,f,d,s,m])});f.displayName="ImgButton";const d=f,y=(e,r,t,o)=>{let i=e.colors.text.primary;return"primary"===r&&(i=e.colors.primary.text),{button:(0,n.css)({zIndex:0,position:"relative",margin:`0 ${e.spacing.x0_5} 0 0`,boxShadow:"none",border:"none",display:"inline-flex",background:"transparent",justifyContent:"center",alignItems:"center",padding:0,color:i,"&[disabled], &:disabled":{cursor:"not-allowed",color:e.colors.action.disabledText,opacity:.65},"&:focus, &:focus-visible":{outline:"2px dotted transparent",outlineOffset:"2px",boxShadow:`0 0 0 2px ${e.colors.background.canvas}, 0 0 0px 4px ${e.colors.primary.main}`,transitionTimingFunction:"cubic-bezier(0.19, 1, 0.22, 1)",transitionDuration:"0.2s",transitionProperty:"outline, outline-offset, box-shadow"},"&:focus:not(:focus-visible)":{outline:"none",boxShadow:"none"}}),icon:(0,n.css)({verticalAlign:"baseline"}),img:(0,n.css)({backgroundImage:"primary"===r?`url(${o[t].primary})`:`url(${o[t].secondary})`,width:"16px",height:"16px","&:before":{width:"16px",height:"16px",left:0,zIndex:-1,position:"absolute",opacity:0,borderRadius:e.shape.radius.default,content:'""',transform:"scale(1.45)",[e.transitions.handleMotion("no-preference","reduce")]:{transitionDuration:"0.2s",transitionTimingFunction:"cubic-bezier(0.4, 0, 0.2, 1)",transitionProperty:"opacity"}},"&:hover":{backgroundImage:`url(${o[t].hover})`,"&:before":{backgroundColor:"secondary"===r?e.colors.action.hover:a.colorManipulator.alpha(i,.12),opacity:1}}})}}}}]);
//# sourceMappingURL=557.js.map?_cache=a3b175be8d0fd60ff808