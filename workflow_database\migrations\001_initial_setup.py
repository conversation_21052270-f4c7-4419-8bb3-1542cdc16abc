from yoyo import step

steps = [
    # Create `cases` table if it doesn't exist
    step(
        """
        CREATE TABLE IF NOT EXISTS cases (
            id SERIAL PRIMARY KEY,
            case_name TEXT NOT NULL,
            reference_number VARCHAR(100),
            client VARCHAR(100)
        );
        """,
        "DROP TABLE IF EXISTS cases CASCADE;"
    ),
    # Create `evidence_items` table if it doesn't exist
    step(
        """
        CREATE TABLE IF NOT EXISTS evidence_items (
            id SERIAL PRIMARY KEY,
            evidence_item_name TEXT NOT NULL,
            case_id INT REFERENCES cases(id) ON DELETE CASCADE
        );
        """,
        "DROP TABLE IF EXISTS evidence_items CASCADE;"
    ),
    # Create `forensic_steps` table if it doesn't exist
    step(
        """
        CREATE TABLE IF NOT EXISTS forensic_steps (
            id INT PRIMARY KEY,
            name TEXT NOT NULL
        );
        """,
        "DROP TABLE IF EXISTS forensic_steps CASCADE;"
    ),
    # Create `processes` table if it doesn't exist
    step(
        """
        CREATE TABLE IF NOT EXISTS processes (
            id SERIAL PRIMARY KEY,
            evidence_item_id INT REFERENCES evidence_items(id),
            step_id INT REFERENCES forensic_steps(id),
            status INT, -- 0 = queued, 1 = running, 2 = finished, 3 = failed, 4 = canceled, 5 = assigned
            updated_at TIMESTAMP,
            input_data TEXT NOT NULL,
            output_data TEXT,
            prioritize BOOLEAN DEFAULT false
        );
        """,
        "DROP TABLE IF EXISTS processes CASCADE;"
    ),
    # Insert initial data into `forensic_steps`
    step(
        """
        INSERT INTO forensic_steps (id, name) VALUES
        (0, 'LagerDB'),
        (1, 'ImageStation'),
        (2, 'XwaysDÜ'),
        (3, 'FOCUS.AI')
        """,
        "DELETE FROM forensic_steps;"
    ),
]
