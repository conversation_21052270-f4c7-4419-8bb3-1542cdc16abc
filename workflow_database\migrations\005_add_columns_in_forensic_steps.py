from yoyo import step

__depends__ = {"004_remove_not_null_constraints_in_processes"}

steps = [
    step(
        """
        ALTER TABLE forensic_steps
        ADD COLUMN display_name VARCHAR(255);
        """,
        """
        ALTER TABLE forensic_steps
        DROP COLUMN display_name;
        """
    ),
    step(
        """
        UPDATE forensic_steps SET display_name = 'Fallaufnahme' WHERE name = 'LagerDB';
        UPDATE forensic_steps SET display_name = 'Imaging' WHERE name = 'ImageStation';
        UPDATE forensic_steps SET display_name = 'Triage' WHERE name = 'FOCUS.AI';
        UPDATE forensic_steps SET display_name = 'Voranalyse' WHERE name = 'XwaysDÜ';
        """,
        """
        UPDATE forensic_steps SET display_name = null WHERE name = 'LagerDB';
        UPDATE forensic_steps SET display_name = null WHERE name = 'ImageStation';
        UPDATE forensic_steps SET display_name = null WHERE name = 'FOCUS.AI';
        UPDATE forensic_steps SET display_name = null WHERE name = 'XwaysDÜ';
        """
    ),
    step(
        """
        ALTER TABLE forensic_steps
        ALTER COLUMN display_name SET NOT NULL;
        """,
        """
        ALTER TABLE forensic_steps
        ALTER COLUMN display_name DROP NOT NULL;
        """
    )

]
