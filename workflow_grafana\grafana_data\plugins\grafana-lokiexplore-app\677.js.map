{"version": 3, "file": "677.js?_cache=4abf8fc642aec3399b73", "mappings": "8tCAWO,SAASA,EAA8B,G,IAAA,kBAC5CC,EAAiB,MACjBC,EAAK,eACLC,GAH4C,EAIzCC,EAAAA,EAJyC,GAC5CH,oBACAC,QACAC,mBAGA,MAAME,EAAa,IAAIC,EAAAA,GAAeH,GAOtC,GANAE,EAAWE,iBAAkBH,IACvBH,GACFA,EAAkBG,EAAMI,UAIvBN,EAEH,OADAO,QAAQC,MAAM,sGACP,MAGTC,EAAAA,EAAAA,WAEA,MAAM,aAAEC,EAAY,YAAEC,IAAgBC,EAAAA,EAAAA,IAAoBZ,GAEpDa,EAAyCH,EAAaI,IAAKC,IAAY,CAC3EC,IAAKD,EAAOC,IACZC,SAAUF,EAAOE,SACjBX,MAAOS,EAAOT,SAGhB,OAAO,IAAIY,EAAAA,EAAW,E,kUAAA,IACjBhB,GAAAA,CACHC,aACAgB,mBAAoBR,EACpBS,UAAU,EACVC,qBAAsBR,IAE1B,CAEO,MAAMS,EAAqB,KAEnB,SAASC,EAAwBC,GAC9C,MAAOC,EAAaC,IAAkBC,EAAAA,EAAAA,UAA4B,MASlE,OAPAC,EAAAA,EAAAA,WAAU,KACHH,KACHI,EAAAA,EAAAA,MACAH,EAAe5B,EAA8B0B,MAE9C,CAACC,EAAaD,IAEZC,EAKH,kBAACK,EAAAA,GAAsBA,CACrBC,MAAON,EACPO,iBAAiB,EACjBC,2BAA2B,EAC3BC,UAAWZ,EACXa,qBAAsB,CAAC,OAAQ,KAAM,WAAYC,EAAAA,EAAsBC,EAAAA,IAEvE,kBAACZ,EAAYa,UAAS,CAACC,MAAOd,KAXzB,IAcX,C", "sources": ["webpack://grafana-lokiexplore-app/./Components/EmbeddedLogsExploration/EmbeddedLogs.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\n\nimport { AdHocFilterWithLabels, SceneTimeRange, UrlSyncContextProvider } from '@grafana/scenes';\n\nimport { drilldownLabelUrlKey, pageSlugUrlKey } from '../ServiceScene/ServiceSceneConstants';\nimport { EmbeddedLogsExplorationProps } from './types';\nimport { IndexScene } from 'Components/IndexScene/IndexScene';\nimport initRuntimeDs from 'services/datasource';\nimport { getMatcherFromQuery } from 'services/logqlMatchers';\nimport { initializeMetadataService } from 'services/metadata';\n\nexport function buildLogsExplorationFromState({\n  onTimeRangeChange,\n  query,\n  timeRangeState,\n  ...state\n}: EmbeddedLogsExplorationProps) {\n  const $timeRange = new SceneTimeRange(timeRangeState);\n  $timeRange.subscribeToState((state) => {\n    if (onTimeRangeChange) {\n      onTimeRangeChange(state.value);\n    }\n  });\n\n  if (!query) {\n    console.error('No query parameter found! Please pass in a valid logQL query string when embedding Logs Drilldown.');\n    return null;\n  }\n\n  initRuntimeDs();\n\n  const { labelFilters, lineFilters } = getMatcherFromQuery(query);\n\n  const initialLabels: AdHocFilterWithLabels[] = labelFilters.map((filter) => ({\n    key: filter.key,\n    operator: filter.operator,\n    value: filter.value,\n  }));\n\n  return new IndexScene({\n    ...state,\n    $timeRange,\n    defaultLineFilters: lineFilters,\n    embedded: true,\n    readOnlyLabelFilters: initialLabels,\n  });\n}\n\nexport const VARIABLE_NAMESPACE = 'ld';\n\nexport default function EmbeddedLogsExploration(props: EmbeddedLogsExplorationProps) {\n  const [exploration, setExploration] = useState<IndexScene | null>(null);\n\n  useEffect(() => {\n    if (!exploration) {\n      initializeMetadataService();\n      setExploration(buildLogsExplorationFromState(props));\n    }\n  }, [exploration, props]);\n\n  if (!exploration) {\n    return null;\n  }\n\n  return (\n    <UrlSyncContextProvider\n      scene={exploration}\n      updateUrlOnInit={false}\n      createBrowserHistorySteps={true}\n      namespace={VARIABLE_NAMESPACE}\n      excludeFromNamespace={['from', 'to', 'timezone', drilldownLabelUrlKey, pageSlugUrlKey]}\n    >\n      <exploration.Component model={exploration} />\n    </UrlSyncContextProvider>\n  );\n}\n"], "names": ["buildLogsExplorationFromState", "onTimeRangeChange", "query", "timeRangeState", "state", "$timeRange", "SceneTimeRange", "subscribeToState", "value", "console", "error", "initRuntimeDs", "labelFilters", "lineFilters", "getMatcherFromQuery", "initialLabels", "map", "filter", "key", "operator", "IndexScene", "defaultLineFilters", "embedded", "readOnlyLabelFilters", "VARIABLE_NAMESPACE", "EmbeddedLogsExploration", "props", "exploration", "setExploration", "useState", "useEffect", "initializeMetadataService", "UrlSyncContextProvider", "scene", "updateUrlOnInit", "createBrowserHistorySteps", "namespace", "excludeFromNamespace", "drilldownLabelUrlKey", "pageSlugUrlKey", "Component", "model"], "sourceRoot": ""}