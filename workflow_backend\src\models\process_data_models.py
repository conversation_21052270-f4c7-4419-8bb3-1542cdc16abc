from pathlib import Path

from pydantic import BaseModel


# for future ImageStation version 3
# TODO: sync this with ImageStation3 implementation
# TODO: use it
# class ImageStationResultData(BaseModel):
#     image_file_path: Path
#     device_size: int

# TODO: use it
# class XwaysDuInputData(BaseModel):
#     first_image_file_path: Path

class XwaysDuResultData(BaseModel):
    xways_project_path: Path | None
    benchmark: dict | None

# TODO: use it
# class FocusInputData(BaseModel):
#     first_image_file_path: Path
#     triage_file_path: Path
#     evidence_item_name: str