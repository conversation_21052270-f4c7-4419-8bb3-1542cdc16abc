{"version": 3, "file": "980.js", "mappings": "mKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,wCACxB,iBAAkB,kCAClB,yBAA0B,0CAE3B,yBAA0B,CACzB,sBAAuB,qCACvB,mBAAoB,uCAErB,gBAAiB,CAChBC,QAAS,sFACTC,SAAU,0CACVC,MAAO,mBAER,wBAAyB,CACxB,wBAAyB,mBACzB,sBAAuB,mBACvB,sBAAuB,kBAExB,iBAAkB,CACjB,iBAAkB,eAClB,cAAe,kBACf,uBAAwB,qBAEzB,iBAAkB,CACjB,eAAgB,gBAChB,aAAc,kBAEf,oCAAqC,CACpC,eAAgB,eAChB,iBAAkB,wCAEnBC,SAAU,CACT,gCAAiC,gCAElC,YAAa,CACZD,MAAO,CACNA,MAAO,UAGT,2BAA4B,CAC3BE,QAAS,YAEV,qBAAsB,CACrB,uBAAwB,yBACxB,sCAAuC,8CAExC,yBAA0B,CACzB,2DAA4D,uHAC5D,kBAAmB,+CAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,SAEnB,oBAAqB,CACpB,uBAAwB,mBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,wBAEpC,wBAAyB,CACxB,wBAAyB,mBACzB,mBAAoB,oBAErB,yBAA0B,CACzB,2BAA4B,eAC5B,aAAc,CACb,2BAA4B,eAE7B,qBAAsB,cACtB,sBAAuB,eACvB,eAAgB,CACf,2BAA4B,gBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,YAGb,8CAA+C,CAC9C,mBAAoB,QACpBC,QAAS,4FACT,gDAAiD,uEAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,6BACzB,uBAAwB,gCACxB,gCAAiC,mDACjC,oDAAqD,gFACrD,0BAA2B,sBAC3B,uBAAwB,uBACxB,mBAAoB,+BACpB,mDAAoD,iDACpD,uBAAwB,kCACxB,kDAAmD,yEACnD,iCAAkC,mCAClC,oCAAqC,sCAIxC,6BAA8B,CAC7B,+BAAgC,mCAChC,6BAA8B,iCAE/B,oBAAqB,CACpB,2BAA4B,eAE7B,8BAA+B,CAC9B,kBAAmB,kBAEpB,2BAA4B,CAC3BC,MAAO,YAER,yBAA0B,CACzB,mBAAoB,wBAErB,4BAA6B,CAC5B,6CAA8C,mEAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,SAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,SAGR,uBAAwB,CACvB,0BAA2B,kBAE5B,wBAAyB,CACxB,2BAA4B,iB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/id-ID/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Edit filter dengan kunci {{keyLabel}}\",\n\t\t\t\"managed-filter\": \"Filter {{origin}} yang dikelola\",\n\t\t\t\"remove-filter-with-key\": \"Hapus filter dengan kunci {{keyLabel}}\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Hapus nilai filter - {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Gunakan nilai kustom: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Jika Anda diarahkan ke sini menggunakan tautan, mungkin ada bug dalam aplikasi ini.\",\n\t\t\tsubTitle: \"URL tidak cocok dengan halaman mana pun\",\n\t\t\ttitle: \"Tidak ditemukan\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"Ciutkan tampilan\",\n\t\t\t\"expand-button-label\": \"Perluas tampilan\",\n\t\t\t\"remove-button-label\": \"Hapus tampilan\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Detail objek\",\n\t\t\t\"scene-graph\": \"Grafik tampilan\",\n\t\t\t\"title-scene-debugger\": \"Debugger tampilan\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Ciutkan baris\",\n\t\t\t\"expand-row\": \"Perbesar baris\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Perbandingan\",\n\t\t\t\"button-tooltip\": \"Aktifkan perbandingan kerangka waktu\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Widget pengubah ukuran panel\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Judul\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Jelajahi\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Memuat panel plugin...\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"Plugin panel tidak memiliki komponen panel\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Menampilkan terlalu banyak seri data dalam satu panel dapat memengaruhi kinerja dan membuat data lebih sulit dibaca.\",\n\t\t\t\"warning-message\": \"Menampilkan {{seriesLimit}} seri data saja\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Hapus\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Batalkan kueri\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Edit operator filter\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Tambahkan filter\",\n\t\t\t\"title-add-filter\": \"Tambahkan filter\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Hapus filter\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Pilih label\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Pilih label\",\n\t\t\t\"title-remove-filter\": \"Hapus filter\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Pilih nilai\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"default\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"hapus\",\n\t\t\ttooltip: \"Diterapkan secara default di dasbor ini. Jika diedit, ini akan diteruskan ke dasbor lain.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Pulihkan 'kelompokkan berdasarkan' yang ditetapkan oleh dasbor ini.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Nilai yang dipisahkan koma\",\n\t\t\t\t\t\"double-quoted-values\": \"Nilai dalam tanda kutip ganda\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Format tanggal dengan berbagai cara yang berbeda\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Format variabel multi-nilai menggunakan sintaks glob, contoh {value1, value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"Nilai HTML escaping\",\n\t\t\t\t\t\"json-stringify-value\": \"Nilai stringify JSON\",\n\t\t\t\t\t\"keep-value-as-is\": \"Pertahankan nilai apa adanya\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Beberapa nilai diformat seperti variabel=nilai\",\n\t\t\t\t\t\"single-quoted-values\": \"Nilai dalam tanda kutip tunggal\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Berguna untuk nilai escaping URL, memperhitungkan karakter sintaks URI\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Berguna untuk nilai URL escaping\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Nilai dipisahkan oleh karakter |\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Kelompokkan berdasarkan selektor\",\n\t\t\t\"placeholder-group-by-label\": \"Kelompokkan berdasarkan label\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Pilih nilai\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Memuat opsi...\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Terapkan\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Opsi tidak ditemukan\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Terjadi kesalahan saat mengambil label. Klik untuk mencoba lagi\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Halo\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Teks\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Masukkan nilai\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Pilih nilai\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}