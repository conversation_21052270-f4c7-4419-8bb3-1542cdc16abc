<template>
  <q-layout view="lHh Lpr lFf">
     <q-header  class="myTopDrawer"/>

    <!-- Left-Side Navigation Drawer -->
    <q-drawer v-model="drawerOpen"
              show-if-above side="left"
              bordered persistent
              :dark="false" :breakpoint="1230"
              style="background-color: #333; color: white;">

      <q-list class="logoBox">
        <div>
          <img class="center" alt="DigiFors ITF Workflow Software Logo"
               src="~assets/ITF-Workflow_Logo.webp">
        </div>
      </q-list>

      <q-list style="padding-top: 40px;">
        <EssentialLink
            v-for="link in drawerLinksList"
            :key="link.title" v-bind="link"
        />
      </q-list>
    </q-drawer>

    <!-- Main Content Area -->
    <q-page-container>
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { ref } from 'vue';
import EssentialLink from "src/components/EssentialLink.vue";

const drawerOpen = ref(false);

const drawerLinksList = [
  {
    title: '<PERSON>älle',
    icon: 'home',
    to: '/'
  },
      {
    title: 'Prozessübersicht',
    icon: 'dvr',
    to: '/processes'
  },
  {
    title: 'Weitere Prozesse',
    icon: 'format_list_bulleted',
    to: '/unassigned_processes'
  },
  // {
  //   title: 'LagerDB',
  //   icon: 'exit_to_app',
  //   to: '/lagerdb'
  // },
    {
    title: 'Monitoring',
    icon: 'insights',
    to: '/monitoring'
  },
  {
    title: 'ImageStation',
    icon: 'exit_to_app',
    to: '/imageStation'
  },
  {
    title: 'Hilfe',
    icon: 'help_outline',
    to: '/help'
  },
]
</script>

<style scoped>
.q-drawer__content .logoBox {
  text-align: center;
  background-color: #fff;
  height: 150px;
  color: #333;
  font-family: sans-serif;

  img {
    width: 80%;
    margin: 17px auto 0 auto;
  }
}

.q-table {
  margin-top: 20px;
  width: 100%;
}

.q-btn {
  text-transform: none;
}

.tableHeaderSearchInput :deep(.q-field__control), :deep(.q-field__append) {
  height: 22px;
  padding: 0px;
  font-size: 13px;
}

.tableTopBtnRow {
  margin: 0px 0px 15px 0px;

  .q-btn {
    float: right;
    width: auto;
    height: 38px;
    background-color: #be1717;
    color: white;
    margin-right: 5px;
  }
}
</style>
