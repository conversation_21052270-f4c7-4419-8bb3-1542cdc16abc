{"version": 3, "file": "864.js?_cache=c7042e4fc7e1fc7aad94", "mappings": "qOAgBA,SAASA,EAAWC,EAAWC,GAC7B,IAAIC,GAAU,IAAAC,UAAS,WACrB,MAAO,CACLF,OAAQA,EACRG,OAAQJ,IAEZ,GAAG,GACCK,GAAa,IAAAC,SAAO,GACpBC,GAAY,IAAAD,QAAOJ,GAEnBM,EADWH,EAAWI,SAAWC,QAAQT,GAAUM,EAAUE,QAAQR,QAvB3E,SAAwBU,EAAWC,GACjC,GAAID,EAAUE,SAAWD,EAAWC,OAClC,OAAO,EAGT,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUE,OAAQC,IACpC,GAAIH,EAAUG,KAAOF,EAAWE,GAC9B,OAAO,EAIX,OAAO,CACT,CAWqFC,CAAed,EAAQM,EAAUE,QAAQR,SACrGM,EAAUE,QAAU,CACzCR,OAAQA,EACRG,OAAQJ,KAMV,OAJA,IAAAgB,WAAU,WACRX,EAAWI,SAAU,EACrBF,EAAUE,QAAUD,CACtB,EAAG,CAACA,IACGA,EAAMJ,MACf,CAMA,IAAIa,EAAUlB,EACVmB,EANJ,SAAwBC,EAAUlB,GAChC,OAAOF,EAAW,WAChB,OAAOoB,CACT,EAAGlB,EACL,ECvCImB,EAAS,mBCCb,IAAIC,EAAU,SAAiBC,GAC7B,IAAIC,EAAMD,EAAKC,IACXC,EAAQF,EAAKE,MACbC,EAASH,EAAKG,OACdC,EAAOJ,EAAKI,KAiBhB,MAdW,CACTH,IAAKA,EACLC,MAAOA,EACPC,OAAQA,EACRC,KAAMA,EACNC,MAPUH,EAAQE,EAQlBE,OAPWH,EAASF,EAQpBM,EAAGH,EACHI,EAAGP,EACHQ,OAAQ,CACNF,GAAIL,EAAQE,GAAQ,EACpBI,GAAIL,EAASF,GAAO,GAI1B,EACIS,EAAS,SAAgBC,EAAQC,GACnC,MAAO,CACLX,IAAKU,EAAOV,IAAMW,EAASX,IAC3BG,KAAMO,EAAOP,KAAOQ,EAASR,KAC7BD,OAAQQ,EAAOR,OAASS,EAAST,OACjCD,MAAOS,EAAOT,MAAQU,EAASV,MAEnC,EACIW,EAAS,SAAgBF,EAAQG,GACnC,MAAO,CACLb,IAAKU,EAAOV,IAAMa,EAASb,IAC3BG,KAAMO,EAAOP,KAAOU,EAASV,KAC7BD,OAAQQ,EAAOR,OAASW,EAASX,OACjCD,MAAOS,EAAOT,MAAQY,EAASZ,MAEnC,EAWIa,EAAY,CACdd,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,GAEJY,EAAY,SAAmBC,GACjC,IAAIC,EAAYD,EAAMC,UAClBC,EAAeF,EAAMG,OACrBA,OAA0B,IAAjBD,EAA0BJ,EAAYI,EAC/CE,EAAeJ,EAAMK,OACrBA,OAA0B,IAAjBD,EAA0BN,EAAYM,EAC/CE,EAAgBN,EAAMO,QACtBA,OAA4B,IAAlBD,EAA2BR,EAAYQ,EACjDE,EAAY1B,EAAQW,EAAOQ,EAAWE,IACtCM,EAAa3B,EAAQc,EAAOK,EAAWI,IACvCK,EAAa5B,EAAQc,EAAOa,EAAYF,IAC5C,MAAO,CACLC,UAAWA,EACXP,UAAWnB,EAAQmB,GACnBQ,WAAYA,EACZC,WAAYA,EACZP,OAAQA,EACRE,OAAQA,EACRE,QAASA,EAEb,EAEII,EAAQ,SAAeC,GACzB,IAAIC,EAAQD,EAAIE,MAAM,GAAI,GAG1B,GAAe,OAFFF,EAAIE,OAAO,GAGtB,OAAO,EAGT,IAAIjD,EAASkD,OAAOF,GAEpB,OADEG,MAAMnD,IDtFV,SAAmBoD,GACf,IAAIA,EAIA,MAAM,IAAIC,MAAMrC,EAKxB,CC4E0JsC,EAAU,GAC3JtD,CACT,EASIuD,EAAS,SAAgBC,EAAUC,GACrC,IA1DyB5B,EAAQ6B,EA0D7BtB,EAAYoB,EAASpB,UACrBI,EAASgB,EAAShB,OAClBF,EAASkB,EAASlB,OAClBI,EAAUc,EAASd,QACnBiB,GA9D6BD,EA8DFD,EA7DxB,CACLtC,KAFuBU,EA8DLO,GA5DNjB,IAAMuC,EAAQhC,EAC1BJ,KAAMO,EAAOP,KAAOoC,EAAQjC,EAC5BJ,OAAQQ,EAAOR,OAASqC,EAAQhC,EAChCN,MAAOS,EAAOT,MAAQsC,EAAQjC,IA0DhC,OAAOS,EAAU,CACfE,UAAWuB,EACXnB,OAAQA,EACRF,OAAQA,EACRI,QAASA,GAEb,EACIkB,EAAa,SAAoBJ,EAAUK,GAK7C,YAJe,IAAXA,IACFA,EArBK,CACLpC,EAAGqC,OAAOC,YACVrC,EAAGoC,OAAOE,cAsBLT,EAAOC,EAAUK,EAC1B,EACII,EAAe,SAAsB7B,EAAW8B,GAClD,IAAI5B,EAAS,CACXnB,IAAK2B,EAAMoB,EAAOC,WAClB/C,MAAO0B,EAAMoB,EAAOE,aACpB/C,OAAQyB,EAAMoB,EAAOG,cACrB/C,KAAMwB,EAAMoB,EAAOI,aAEjB5B,EAAU,CACZvB,IAAK2B,EAAMoB,EAAOK,YAClBnD,MAAO0B,EAAMoB,EAAOM,cACpBnD,OAAQyB,EAAMoB,EAAOO,eACrBnD,KAAMwB,EAAMoB,EAAOQ,cAEjBlC,EAAS,CACXrB,IAAK2B,EAAMoB,EAAOS,gBAClBvD,MAAO0B,EAAMoB,EAAOU,kBACpBvD,OAAQyB,EAAMoB,EAAOW,mBACrBvD,KAAMwB,EAAMoB,EAAOY,kBAErB,OAAO5C,EAAU,CACfE,UAAWA,EACXE,OAAQA,EACRI,QAASA,EACTF,OAAQA,GAEZ,EACIuC,EAAS,SAAgBC,GAC3B,IAAI5C,EAAY4C,EAAGC,wBACff,EAASJ,OAAOoB,iBAAiBF,GACrC,OAAOf,EAAa7B,EAAW8B,EACjC,ECrJIiB,EAAYjC,OAAOC,OACnB,SAAkBH,GACd,MAAwB,iBAAVA,GAAsBA,GAAUA,CAClD,EACJ,SAASoC,EAAQC,EAAOC,GACpB,OAAID,IAAUC,MAGVH,EAAUE,KAAUF,EAAUG,GAItC,CACA,SAAS,EAAe/E,EAAWC,GAC/B,GAAID,EAAUE,SAAWD,EAAWC,OAChC,OAAO,EAEX,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUE,OAAQC,IAClC,IAAK0E,EAAQ7E,EAAUG,GAAIF,EAAWE,IAClC,OAAO,EAGf,OAAO,CACX,CAEA,SAAS6E,EAAWC,EAAUJ,QACV,IAAZA,IAAsBA,EAAU,GACpC,IAAIhF,EAAQ,KACZ,SAASqF,IAEL,IADA,IAAIC,EAAU,GACLC,EAAK,EAAGA,EAAKC,UAAUnF,OAAQkF,IACpCD,EAAQC,GAAMC,UAAUD,GAE5B,GAAIvF,GAASA,EAAMyF,WAAaC,MAAQV,EAAQM,EAAStF,EAAM2F,UAC3D,OAAO3F,EAAM4F,WAEjB,IAAIA,EAAaR,EAASS,MAAMH,KAAMJ,GAMtC,OALAtF,EAAQ,CACJ4F,WAAYA,EACZD,SAAUL,EACVG,SAAUC,MAEPE,CACX,CAIA,OAHAP,EAASS,MAAQ,WACb9F,EAAQ,IACZ,EACOqF,CACX,CCfA,QAjCc,SAAiBU,GAC7B,IAAIJ,EAAW,GACXK,EAAU,KAEVC,EAAY,WACd,IAAK,IAAIC,EAAOV,UAAUnF,OAAQ8F,EAAO,IAAIC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/EF,EAAKE,GAAQb,UAAUa,GAGzBV,EAAWQ,EAEPH,IAIJA,EAAUM,sBAAsB,WAC9BN,EAAU,KACVD,EAAGF,WAAM,EAAQF,EACnB,GACF,EAWA,OATAM,EAAUM,OAAS,WACZP,IAILQ,qBAAqBR,GACrBA,EAAU,KACZ,EAEOC,CACT,EC/BA,SAASQ,IACP,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GACjE,IAAK,IAAIC,EAAI,EAAGA,EAAItB,UAAUnF,OAAQyG,IAAK,CACzC,IAAIC,EAAIvB,UAAUsB,GAClB,IAAK,IAAIE,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOH,EAAEG,GAAKD,EAAEC,GAC/D,CACA,OAAOH,CACT,EAAGJ,EAASZ,MAAM,KAAML,UAC1B,CCeA,SAAS2B,EAAIC,EAAMC,GAQnB,CACgBF,EAAIP,KAAK,KAAM,QACjBO,EAAIP,KAAK,KAAM,SAE7B,SAASU,IAAU,CAQnB,SAASC,EAAW3C,EAAI4C,EAAUC,GAChC,MAAMC,EAAaF,EAASG,IAAIC,IAC9B,MAAMC,EARV,SAAoBC,EAAQC,GAC1B,MAAO,IACFD,KACAC,EAEP,CAGoBC,CAAWP,EAAeG,EAAQC,SAElD,OADAjD,EAAGqD,iBAAiBL,EAAQM,UAAWN,EAAQ7B,GAAI8B,GAC5C,WACLjD,EAAGuD,oBAAoBP,EAAQM,UAAWN,EAAQ7B,GAAI8B,EACxD,IAEF,OAAO,WACLH,EAAWU,QAAQC,IACjBA,KAEJ,CACF,CAEA,MAAM,GAAe,EACfC,EAAW,mBACjB,MAAMC,UAAqBtF,OAI3B,SAAS,EAAUD,EAAWqE,GAC5B,IAAIrE,EAGJ,MACQ,IAAIuF,EADR,EACqBD,EAEA,GAAGA,MAAajB,GAAW,KAEtD,CAZAkB,EAAaC,UAAUC,SAAW,WAChC,OAAO/C,KAAK2B,OACd,EAYA,MAAMqB,UAAsB,cAC1B,WAAAC,IAAexC,GACbyC,SAASzC,GACTT,KAAKmD,UAAY,KACjBnD,KAAK2C,OAASf,EACd5B,KAAKoD,cAAgBC,IACnB,MAAMF,EAAYnD,KAAKsD,eACnBH,EAAUI,cACZJ,EAAUK,WAMAH,EAAMI,iBACCZ,GACjBQ,EAAMK,kBAMV1D,KAAKsD,aAAe,KAClB,IAAKtD,KAAKmD,UACR,MAAM,IAAI5F,MAAM,mDAElB,OAAOyC,KAAKmD,WAEdnD,KAAK2D,aAAeR,IAClBnD,KAAKmD,UAAYA,EAErB,CACA,iBAAAS,GACE5D,KAAK2C,OAASd,EAAW7D,OAAQ,CAAC,CAChCwE,UAAW,QACXnC,GAAIL,KAAKoD,gBAEb,CACA,iBAAAS,CAAkBC,GAChB,KAAIA,aAAejB,GAOnB,MAAMiB,EAHJ9D,KAAK+D,SAAS,CAAC,EAInB,CACA,oBAAAC,GACEhE,KAAK2C,QACP,CACA,MAAAsB,GACE,OAAOjE,KAAKkE,MAAMC,SAASnE,KAAK2D,aAClC,EAGF,MAKMS,EAAWC,GAASA,EAAQ,EAI5BC,EAAe,CAACC,EAAQC,KAC5B,MAAMC,EAAeF,EAAOG,cAAgBF,EAAYE,YAClDC,EAAgBP,EAASG,EAAOF,OAChCO,EAAcR,EAASI,EAAYH,OACzC,OAAII,EACK,iDACmCE,wBAC1BC,UAGX,+CACmCD,kBAC9BJ,EAAOG,4BACPF,EAAYE,gCACRE,SAGZC,EAAc,CAACC,EAAIP,EAAQQ,IACZR,EAAOG,cAAgBK,EAAQL,YAEzC,oBACMI,mCACcC,EAAQC,cAE9B,oBACQF,oBACDP,EAAOG,6CACQK,EAAQC,8BACvBD,EAAQL,oBAclBO,EAAkBV,GAAU,4DAE3BH,EAASG,EAAOF,WA4BjBa,EAAS,CACbC,4BAlFkC,8NAmFlCC,YA7EkBC,GAAS,2CACWjB,EAASiB,EAAMd,OAAOF,WA6E5DiB,aA5CmBC,IACnB,MAAMC,EAAWD,EAAOf,YACxB,GAAIgB,EACF,OAAOlB,EAAaiB,EAAOhB,OAAQiB,GAErC,MAAMT,EAAUQ,EAAOR,QACvB,OAAIA,EACKF,EAAYU,EAAOP,YAAaO,EAAOhB,OAAQQ,GAEjD,kDAoCPU,UA9BgBvL,IAChB,GAAsB,WAAlBA,EAAOwL,OACT,MAAO,sCAEHT,EAAgB/K,EAAOqK,gBAG7B,MAAMiB,EAAWtL,EAAOsK,YAClBO,EAAU7K,EAAO6K,QACvB,OAAIS,EACK,6CAEHlB,EAAapK,EAAOqK,OAAQiB,WAG9BT,EACK,6CAEHF,EAAY3K,EAAO8K,YAAa9K,EAAOqK,OAAQQ,WAG9C,oEAEHE,EAAgB/K,EAAOqK,gBAS7B,IAAIoB,EAAWT,EAEf,MAAMU,EAAS,CACbjK,EAAG,EACHC,EAAG,GAECiK,EAAM,CAACC,EAAQC,KAAW,CAC9BpK,EAAGmK,EAAOnK,EAAIoK,EAAOpK,EACrBC,EAAGkK,EAAOlK,EAAImK,EAAOnK,IAEjBoK,EAAW,CAACF,EAAQC,KAAW,CACnCpK,EAAGmK,EAAOnK,EAAIoK,EAAOpK,EACrBC,EAAGkK,EAAOlK,EAAImK,EAAOnK,IAEjBqK,EAAY,CAACH,EAAQC,IAAWD,EAAOnK,IAAMoK,EAAOpK,GAAKmK,EAAOlK,IAAMmK,EAAOnK,EAC7EsK,EAASC,IAAS,CACtBxK,EAAe,IAAZwK,EAAMxK,GAAWwK,EAAMxK,EAAI,EAC9BC,EAAe,IAAZuK,EAAMvK,GAAWuK,EAAMvK,EAAI,IAE1BwK,EAAQ,CAACC,EAAMnJ,EAAOoJ,EAAa,IAC1B,MAATD,EACK,CACL1K,EAAGuB,EACHtB,EAAG0K,GAGA,CACL3K,EAAG2K,EACH1K,EAAGsB,GAGDqJ,EAAW,CAACT,EAAQC,IAAWS,KAAKC,MAAMV,EAAOpK,EAAImK,EAAOnK,IAAM,GAAKoK,EAAOnK,EAAIkK,EAAOlK,IAAM,GAC/F8K,EAAY,CAAC3K,EAAQ4K,IAAWH,KAAKI,OAAOD,EAAO1E,IAAIkE,GAASI,EAASxK,EAAQoK,KACjFhG,EAAQE,GAAM8F,IAAS,CAC3BxK,EAAG0E,EAAG8F,EAAMxK,GACZC,EAAGyE,EAAG8F,EAAMvK,KAgBd,MAAMiL,EAAmB,CAACC,EAASX,KAAU,CAC3C9K,IAAKyL,EAAQzL,IAAM8K,EAAMvK,EACzBJ,KAAMsL,EAAQtL,KAAO2K,EAAMxK,EAC3BJ,OAAQuL,EAAQvL,OAAS4K,EAAMvK,EAC/BN,MAAOwL,EAAQxL,MAAQ6K,EAAMxK,IAEzBoL,EAAaD,GAAW,CAAC,CAC7BnL,EAAGmL,EAAQtL,KACXI,EAAGkL,EAAQzL,KACV,CACDM,EAAGmL,EAAQxL,MACXM,EAAGkL,EAAQzL,KACV,CACDM,EAAGmL,EAAQtL,KACXI,EAAGkL,EAAQvL,QACV,CACDI,EAAGmL,EAAQxL,MACXM,EAAGkL,EAAQvL,SAwBPyL,GAAO,CAACjL,EAAQkL,IAChBA,GAASA,EAAMC,kBAvDH,EAAED,EAAOE,KACzB,MAAMjN,EAASiB,EAAQ,CACrBE,IAAKmL,KAAKY,IAAID,EAAQ9L,IAAK4L,EAAM5L,KACjCC,MAAOkL,KAAKI,IAAIO,EAAQ7L,MAAO2L,EAAM3L,OACrCC,OAAQiL,KAAKI,IAAIO,EAAQ5L,OAAQ0L,EAAM1L,QACvCC,KAAMgL,KAAKY,IAAID,EAAQ3L,KAAMyL,EAAMzL,QAErC,OAAItB,EAAOuB,OAAS,GAAKvB,EAAOwB,QAAU,EACjC,KAEFxB,CACR,EA6CUmN,CAAYJ,EAAMK,cAAevL,GAEnCZ,EAAQY,GAEjB,IAAIwL,GAAa,EACfC,OACAC,kBACAC,OACAT,YAEA,MAAMU,EA3BS,EAAC5L,EAAQkL,IACnBA,EAGEJ,EAAiB9K,EAAQkL,EAAMlJ,OAAO6J,KAAKC,cAFzC9L,EAyBQ+L,CAASN,EAAK3K,UAAWoK,GACpCc,EAtBS,EAAChM,EAAQ2L,EAAMD,IAC1BA,GAAmBA,EAAgBO,YAC9B,IACFjM,EACH,CAAC2L,EAAKO,KAAMlM,EAAO2L,EAAKO,KAAOR,EAAgBO,YAAYN,EAAKrB,OAG7DtK,EAeWmM,CAASP,EAAUD,EAAMD,GAE3C,MAAO,CACLD,OACAC,kBACAU,OAJcnB,GAAKe,EAAWd,GAMjC,EAEGmB,GAAkB,CAAEC,EAAWC,KAChCD,EAAUpB,OAAmE,GAAU,GACxF,MAAMsB,EAAaF,EAAUpB,MACvBuB,EAAaxC,EAASsC,EAAWC,EAAWxK,OAAO/D,SACnDyO,EAAqBvC,EAAOsC,GAC5BvB,EAAQ,IACTsB,EACHxK,OAAQ,CACN/D,QAASuO,EAAWxK,OAAO/D,QAC3BO,QAAS+N,EACTV,KAAM,CACJ1K,MAAOsL,EACPX,aAAcY,GAEhBrB,IAAKmB,EAAWxK,OAAOqJ,MAGrBD,EAAUI,GAAW,CACzBC,KAAMa,EAAUlB,QAAQK,KACxBC,gBAAiBY,EAAUlB,QAAQM,gBACnCC,KAAMW,EAAUX,KAChBT,UAOF,MALe,IACVoB,EACHpB,QACAE,UAGH,EAED,MAAMuB,GAAiBjJ,EAAWkJ,GAAcA,EAAWC,OAAO,CAACC,EAAUtO,KAC3EsO,EAAStO,EAAQuO,WAAWhE,IAAMvK,EAC3BsO,GACN,CAAC,IACEE,GAAiBtJ,EAAWuJ,GAAcA,EAAWJ,OAAO,CAACC,EAAUtO,KAC3EsO,EAAStO,EAAQuO,WAAWhE,IAAMvK,EAC3BsO,GACN,CAAC,IACEI,GAAkBxJ,EAAWkJ,GAAc3H,OAAOkI,OAAOP,IACzDQ,GAAkB1J,EAAWuJ,GAAchI,OAAOkI,OAAOF,IAE/D,IAAII,GAA+B3J,EAAW,CAACiF,EAAasE,KAC1D,MAAM9O,EAASiP,GAAgBH,GAAYK,OAAOC,GAAa5E,IAAgB4E,EAAUR,WAAWpE,aAAa6E,KAAK,CAACC,EAAGC,IAAMD,EAAEV,WAAWzE,MAAQoF,EAAEX,WAAWzE,OAClK,OAAOnK,IAGT,SAASwP,GAAkBC,GACzB,OAAIA,EAAOC,IAAyB,YAAnBD,EAAOC,GAAGlI,KAClBiI,EAAOC,GAAGpF,YAEZ,IACT,CACA,SAASqF,GAAcF,GACrB,OAAIA,EAAOC,IAAyB,YAAnBD,EAAOC,GAAGlI,KAClBiI,EAAOC,GAAG7E,QAEZ,IACT,CAEA,IAAI+E,GAA0BrK,EAAW,CAACsK,EAAQC,IAASA,EAAKX,OAAOY,GAAQA,EAAKnB,WAAWhE,KAAOiF,EAAOjB,WAAWhE,KAoDpHoF,GAAW,CAAEZ,EAAW9E,IAAgB8E,EAAUR,WAAWpE,cAAgBF,EAAYsE,WAAWhE,GAExG,MAAMqF,GAAgB,CACpBhE,MAAOP,EACP1I,MAAO,GAEHkN,GAAc,CAClBC,UAAW,CAAC,EACZC,QAAS,CAAC,EACVC,IAAK,IAOP,IAAIC,GALa,CACfC,UAAWL,GACXM,YAAaP,GACbP,GAAI,MAIFe,GAAW,CAAEC,EAAYC,IAAe3N,GAAS0N,GAAc1N,GAASA,GAAS2N,EAEjFC,GAAkC7D,IACpC,MAAM8D,EAAmBJ,GAAS1D,EAAM5L,IAAK4L,EAAM1L,QAC7CyP,EAAqBL,GAAS1D,EAAMzL,KAAMyL,EAAM3L,OACtD,OAAO6L,IAEL,GADoB4D,EAAiB5D,EAAQ9L,MAAQ0P,EAAiB5D,EAAQ5L,SAAWyP,EAAmB7D,EAAQ3L,OAASwP,EAAmB7D,EAAQ7L,OAEtJ,OAAO,EAET,MAAM2P,EAA+BF,EAAiB5D,EAAQ9L,MAAQ0P,EAAiB5D,EAAQ5L,QACzF2P,EAAiCF,EAAmB7D,EAAQ3L,OAASwP,EAAmB7D,EAAQ7L,OAEtG,GAD6B2P,GAAgCC,EAE3D,OAAO,EAET,MAAMC,EAAqBhE,EAAQ9L,IAAM4L,EAAM5L,KAAO8L,EAAQ5L,OAAS0L,EAAM1L,OACvE6P,EAAuBjE,EAAQ3L,KAAOyL,EAAMzL,MAAQ2L,EAAQ7L,MAAQ2L,EAAM3L,MAEhF,GADgC6P,GAAsBC,EAEpD,OAAO,EAGT,OADgCD,GAAsBD,GAAkCE,GAAwBH,EAGnH,EAEGI,GAAgCpE,IAClC,MAAM8D,EAAmBJ,GAAS1D,EAAM5L,IAAK4L,EAAM1L,QAC7CyP,EAAqBL,GAAS1D,EAAMzL,KAAMyL,EAAM3L,OACtD,OAAO6L,GACe4D,EAAiB5D,EAAQ9L,MAAQ0P,EAAiB5D,EAAQ5L,SAAWyP,EAAmB7D,EAAQ3L,OAASwP,EAAmB7D,EAAQ7L,MAG3J,EAED,MAAMgQ,GAAW,CACfC,UAAW,WACXlF,KAAM,IACNmF,cAAe,IACfnG,MAAO,MACP4C,IAAK,SACLwD,KAAM,SACNC,eAAgB,OAChBC,aAAc,QACdC,cAAe,SAEXC,GAAa,CACjBN,UAAW,aACXlF,KAAM,IACNmF,cAAe,IACfnG,MAAO,OACP4C,IAAK,QACLwD,KAAM,QACNC,eAAgB,MAChBC,aAAc,SACdC,cAAe,UAcjB,MAWME,GAAc,EAClB/P,OAAQgQ,EACRvH,cACAwH,WACAC,4BACAC,8BAEA,MAAMC,EAAkBF,EAlBI,EAAClQ,EAAQyI,KACrC,MAAMqD,EAAerD,EAAYyC,MAAQzC,EAAYyC,MAAMlJ,OAAO6J,KAAKC,aAAejC,EACtF,OAAOiB,EAAiB9K,EAAQ8L,IAgBoBuE,CAAsBL,EAAevH,GAAeuH,EACxG,MAf2B,EAAChQ,EAAQyI,EAAa0H,MAC5C1H,EAAY2C,QAAQgB,QAGlB+D,EAAwB1H,EAAY2C,QAAQgB,OAA5C+D,CAAoDnQ,GAWpDsQ,CAAqBF,EAAiB3H,EAAa0H,IAThC,EAACnQ,EAAQiQ,EAAUE,IAA4BA,EAAwBF,EAAxBE,CAAkCnQ,GASrBuQ,CAAoBH,EAAiBH,EAAUE,IAMjIK,GAAmB9L,GAAQqL,GAAY,IACxCrL,EACHyL,wBAAyBb,KAkC3B,SAASmB,IAAsB,cAC7BC,EAAa,YACbjI,EAAW,YACXkG,EAAW,SACXsB,EAAQ,mBACRU,EAAkB,KAClBC,IAEA,OAAOF,EAAc7D,OAAO,SAAiBgE,EAAQtD,GACnD,MAAMvN,EAnBV,SAAmBuN,EAAWoB,GAC5B,MAAM7N,EAAYyM,EAAU9B,KAAK3K,UAC3Bb,EAAW,CACfX,IAAKqP,EAAYvE,MAAMvK,EACvBN,MAAO,EACPC,OAAQ,EACRC,KAAMkP,EAAYvE,MAAMxK,GAE1B,OAAOR,EAAQW,EAAOe,EAAWb,GACnC,CAUmB6Q,CAAUvD,EAAWoB,GAC9B5F,EAAKwE,EAAUR,WAAWhE,GAChC8H,EAAOrC,IAAIuC,KAAKhI,GAnDOrE,MA0DvB,KA1DuBA,EAoDc,CACnC1E,SACAyI,cACAwH,WACAC,2BAA2B,GAxDEH,GAAY,IAC1CrL,EACHyL,wBAAyBpB,MA0DrB,OADA8B,EAAOvC,UAAUf,EAAUR,WAAWhE,KAAM,EACrC8H,EAET,MAAMG,EAjDe,EAACjI,EAAI6H,EAAMD,KAClC,GAAkC,kBAAvBA,EACT,OAAOA,EAET,IAAKC,EACH,OAAO,EAET,MAAM,UACJtC,EAAS,QACTC,GACEqC,EACJ,GAAItC,EAAUvF,GACZ,OAAO,EAET,MAAM+D,EAAWyB,EAAQxF,GACzB,OAAO+D,GAAWA,EAASkE,eAkCHC,CAAiBlI,EAAI6H,EAAMD,GAC3C7E,EAAe,CACnB7C,YAAaF,EACbiI,iBAGF,OADAH,EAAOtC,QAAQxF,GAAM+C,EACd+E,CACT,EAAG,CACDrC,IAAK,GACLD,QAAS,CAAC,EACVD,UAAW,CAAC,GAEhB,CASA,SAAS4C,IAAQ,kBACfC,EAAiB,WACjBC,EAAU,YACVzC,EAAW,YACXlG,IAEA,MAAM4I,EAbR,SAA4BpE,EAAY7G,GACtC,IAAK6G,EAAWrO,OACd,OAAO,EAET,MAAM0S,EAAkBrE,EAAWA,EAAWrO,OAAS,GAAGmO,WAAWzE,MACrE,OAAOlC,EAAQgL,WAAaE,EAAkBA,EAAkB,CAClE,CAOmBC,CAAmBJ,EAAmB,CACrDC,eAEF,MAAO,CACL1C,UAAWL,GACXM,cACAd,GAAI,CACFlI,KAAM,UACN8C,YAAa,CACXE,YAAaF,EAAYsE,WAAWhE,GACpCT,MAAO+I,IAIf,CACA,SAASG,IAAuB,UAC9BjE,EAAS,kBACT4D,EAAiB,YACjB1I,EAAW,SACXwH,EAAQ,YACRtB,EAAW,KACXiC,EAAI,MACJtI,EAAK,mBACLqI,IAEA,MAAMS,EAAajD,GAASZ,EAAW9E,GACvC,GAAa,MAATH,EACF,OAAO4I,GAAQ,CACbC,oBACAC,aACAzC,cACAlG,gBAGJ,MAAMgJ,EAAQN,EAAkBO,KAAKxD,GAAQA,EAAKnB,WAAWzE,QAAUA,GACvE,IAAKmJ,EACH,OAAOP,GAAQ,CACbC,oBACAC,aACAzC,cACAlG,gBAGJ,MAAMkJ,EAAkB5D,GAAwBR,EAAW4D,GACrDS,EAAYT,EAAkBU,QAAQJ,GAU5C,MAAO,CACL/C,UATgB+B,GAAsB,CACtCC,cAFeiB,EAAgBvQ,MAAMwQ,GAGrCnJ,cACAkG,cACAiC,OACAX,SAAUA,EAAS/E,MACnByF,uBAIAhC,cACAd,GAAI,CACFlI,KAAM,UACN8C,YAAa,CACXE,YAAaF,EAAYsE,WAAWhE,GACpCT,UAIR,CAEA,SAASwJ,GAAsB7I,EAAa8I,GAC1C,OAAOtT,QAAQsT,EAAcC,SAAS/I,GACxC,CAEA,IAiDIgJ,GAAkB,EACpBC,kBACAxJ,eACA6E,YACAN,aACAxE,cACA0I,oBACAgB,iBACAlC,WACA8B,oBAEA,MAAMK,EAAQD,EAAetE,GAE7B,GADCuE,GAAgI,GAAU,GACxH,YAAfA,EAAMzM,KAAoB,CAC5B,MAAM0L,EArCQ,GAChBa,kBACAxJ,eACAyI,oBACA1H,eAEA,IAAK0H,EAAkBvS,OACrB,OAAO,KAET,MAAMyT,EAAe5I,EAASnB,MACxBgK,EAAgBJ,EAAkBG,EAAe,EAAIA,EAAe,EACpEE,EAAapB,EAAkB,GAAGpE,WAAWzE,MAC7CkK,EAAYrB,EAAkBA,EAAkBvS,OAAS,GAAGmO,WAAWzE,MAE7E,OAAIgK,EAAgBC,GAGhBD,GAJe5J,EAAe8J,EAAYA,EAAY,GAEjD,KAKFF,CACR,EAgBoBG,CAAY,CAC3BP,kBACAxJ,eACAe,SAAU2I,EAAM3J,YAChB0I,sBAEF,OAAgB,MAAZE,EACK,KAEFG,GAAuB,CAC5BjE,YACA4D,oBACA1I,cACAwH,WACAW,KAAMuB,EAAezD,UACrBC,YAAawD,EAAexD,YAC5BrG,MAAO+I,GAEX,CACA,MAAMA,EAlFU,GAChBa,kBACAzJ,cACAwE,aACAjE,UACA+I,oBAEA,IAAKtJ,EAAYiK,iBACf,OAAO,KAET,MAAMC,EAAY3J,EAAQC,YAEpB2J,EADc3F,EAAW0F,GACM5F,WAAWzE,MAEhD,OADyCwJ,GAAsBa,EAAWZ,GAEpEG,EACKU,EAEFA,EAAmB,EAExBV,EACKU,EAAmB,EAErBA,CACR,EA0DkBC,CAAY,CAC3BX,kBACAzJ,cACAiG,UAAWyD,EAAezD,UAC1BzB,aACAjE,QAASoJ,EAAMpJ,QACf+I,kBAEF,OAAgB,MAAZV,EACK,KAEFG,GAAuB,CAC5BjE,YACA4D,oBACA1I,cACAwH,WACAW,KAAMuB,EAAezD,UACrBC,YAAawD,EAAexD,YAC5BrG,MAAO+I,GAEV,EAeGyB,GAAgB,EAClBf,gBACAnE,SACAX,iBAEA,MAAMjE,EAAU8E,GAAcF,GAC7B5E,GAAqE,GAAU,GAChF,MAAM+J,EAAc/J,EAAQC,YACtBnJ,EAASmN,EAAW8F,GAAatH,KAAKlL,UAAUT,OAChDkT,EAtB0B,GAChCtE,YACAqD,gBACAgB,cACApE,kBAEA,MAAMsE,EAAcxU,QAAQiQ,EAAUH,QAAQwE,IAAgBrE,EAAUJ,UAAUyE,IAClF,OAAIjB,GAAsBiB,EAAahB,GAC9BkB,EAAcpJ,EAASM,EAAOwE,EAAYvE,OAE5C6I,EAActE,EAAYvE,MAAQP,CAC1C,EAWoBqJ,CAA4B,CAC7CxE,UAAWd,EAAOc,UAClBqD,gBACAgB,cACApE,YAAaf,EAAOe,cAEtB,OAAO7E,EAAIhK,EAAQkT,EACpB,EAED,MAAMG,GAAqC,CAACxH,EAAMyH,IAAQA,EAAI3S,OAAOkL,EAAKrC,OAAS8J,EAAI7S,UAAUoL,EAAK+D,MAAQ,EAExG2D,GAA8B,CAAC1H,EAAM3L,EAAQsT,IAAatT,EAAO2L,EAAKgE,gBAAkB2D,EAAS7S,OAAOkL,EAAKgE,gBAAkB2D,EAAS/S,UAAUoL,EAAKkE,eAAiB,EACxK0D,GAAU,EACd5H,OACA6H,iBACAF,cACIjJ,EAAMsB,EAAKrB,KAAMkJ,EAAe1S,UAAU6K,EAAKO,KAAOiH,GAAmCxH,EAAM2H,GAAWD,GAA4B1H,EAAM6H,EAAe1S,UAAWwS,IACtKG,GAAW,EACf9H,OACA6H,iBACAF,cACIjJ,EAAMsB,EAAKrB,KAAMkJ,EAAe1S,UAAU6K,EAAKrC,OAXZ,EAACqC,EAAMyH,IAAQA,EAAI3S,OAAOkL,EAAKO,KAAOkH,EAAI7S,UAAUoL,EAAK+D,MAAQ,EAW5CgE,CAAiC/H,EAAM2H,GAAWD,GAA4B1H,EAAM6H,EAAe1S,UAAWwS,IAO5K,IAAIK,GAAiB,EACnB/F,SACAL,YACAN,aACAX,YACAyF,oBAEA,MAAMZ,EAAoB9D,GAA6Bf,EAAUS,WAAWhE,GAAIkE,GAC1E2G,EAAgBrG,EAAU9B,KAC1BE,EAAOW,EAAUX,KACvB,IAAKwF,EAAkBvS,OACrB,MAjBgB,GAClB+M,OACAkI,WACAP,cACIjJ,EAAMsB,EAAKrB,KAAMuJ,EAAS7S,WAAW2K,EAAKrC,OAAS6J,GAAmCxH,EAAM2H,GAAWD,GAA4B1H,EAAMkI,EAAS7S,WAAYsS,IAazJQ,CAAY,CACjBnI,OACAkI,SAAUvH,EAAUb,KACpB6H,SAAUM,IAGd,MAAM,UACJlF,EAAS,YACTC,GACEf,EACEmG,EAAerF,EAAUF,IAAI,GACnC,GAAIuF,EAAc,CAChB,MAAMC,EAAU/G,EAAW8G,GAC3B,GAAIjC,GAAsBiC,EAAchC,GACtC,OAAO0B,GAAS,CACd9H,OACA6H,eAAgBQ,EAAQvI,KACxB6H,SAAUM,IAGd,MAAMK,EAAmBvS,EAAOsS,EAAQvI,KAAMkD,EAAYvE,OAC1D,OAAOqJ,GAAS,CACd9H,OACA6H,eAAgBS,EAChBX,SAAUM,GAEd,CACA,MAAMhD,EAAOO,EAAkBA,EAAkBvS,OAAS,GAC1D,GAAIgS,EAAK7D,WAAWhE,KAAOwE,EAAUR,WAAWhE,GAC9C,OAAO6K,EAAcrT,UAAUT,OAEjC,GAAIgS,GAAsBlB,EAAK7D,WAAWhE,GAAIgJ,GAAgB,CAC5D,MAAMtG,EAAO/J,EAAOkP,EAAKnF,KAAMtB,EAAO4H,EAAcpD,YAAYvE,QAChE,OAAOmJ,GAAQ,CACb5H,OACA6H,eAAgB/H,EAChB6H,SAAUM,GAEd,CACA,OAAOL,GAAQ,CACb5H,OACA6H,eAAgB5C,EAAKnF,KACrB6H,SAAUM,GAEb,EAEG1D,GAA4B,CAAE5D,EAAWlC,KAC3C,MAAMc,EAAQoB,EAAUpB,MACxB,OAAKA,EAGEpB,EAAIM,EAAOc,EAAMlJ,OAAO6J,KAAKC,cAF3B1B,CAGV,EAgCD,IAAI8J,GAAoCxP,IACtC,MAAMyP,EA/BsC,GAC5CvG,SACAL,YACAjB,YACAW,aACA8E,oBAEA,MAAMpQ,EAAW4L,EAAU9B,KAAKlL,UAAUT,OACpC+N,EAAKD,EAAOC,GAClB,OAAKvB,GAGAuB,EAGW,YAAZA,EAAGlI,KACEgO,GAAe,CACpB/F,SACAL,YACAN,aACAX,YACAyF,kBAGGe,GAAc,CACnBlF,SACAX,aACA8E,kBAjBOpQ,GAqBmByS,CAAsC1P,GAC5D4H,EAAY5H,EAAK4H,UAEvB,OADyBA,EAAY4D,GAA0B5D,EAAW6H,GAAuBA,CAElG,EAEGE,GAAiB,CAAEpE,EAAU1D,KAC/B,MAAMV,EAAO5B,EAASsC,EAAW0D,EAASjO,OAAO/D,SAC3C6N,EAAe3B,EAAO0B,GAmB5B,MAZgB,CACdX,MAPY9L,EAAQ,CACpBE,IAAKiN,EAAU1M,EACfL,OAAQ+M,EAAU1M,EAAIoQ,EAAS/E,MAAMvL,OACrCF,KAAM8M,EAAU3M,EAChBL,MAAOgN,EAAU3M,EAAIqQ,EAAS/E,MAAMxL,QAIpCsC,OAAQ,CACN/D,QAASgS,EAASjO,OAAO/D,QACzBoN,IAAK4E,EAASjO,OAAOqJ,IACrB7M,QAAS+N,EACTV,KAAM,CACJ1K,MAAO0K,EACPC,iBAKP,EAED,SAASwI,GAAgBC,EAAKtH,GAC5B,OAAOsH,EAAIrO,IAAI6C,GAAMkE,EAAWlE,GAClC,CAUA,IAkDIyL,GAAmC,EACrCC,sBACAlH,YACA0C,eAEA,MAAMyE,EAPuB,EAAEzE,EAAU7F,IAAUN,EAAImG,EAASjO,OAAO6J,KAAKC,aAAc1B,GAO1DuK,CAAyB1E,EAAUwE,GAC7D/S,EAASuI,EAASyK,EAAyBnH,EAAU9B,KAAKlL,UAAUT,QAC1E,OAAOgK,EAAIyD,EAAUqH,OAAOrU,UAAUT,OAAQ4B,EAC/C,EAEGmT,GAAgC,EAClCtH,YACA9E,cACAqM,yBACA7E,WACAC,4BACA6E,kBAAiB,MAEjB,MAAMC,EAAe/K,EAAS6K,EAAwBvH,EAAU9B,KAAKlL,UAAUT,QAEzE4E,EAAO,CACX1E,OAFc8K,EAAiByC,EAAU9B,KAAKlL,UAAWyU,GAGzDvM,cACAyH,4BACAD,YAEF,OAAO8E,EAxgBsBrQ,KAAQqL,UAAY,IAC9CrL,EACHyL,yBA1CwCxE,EA0CoBjH,EAAK+D,YAAYkD,KA1C7BT,IAChD,MAAM8D,EAAmBJ,GAAS1D,EAAM5L,IAAK4L,EAAM1L,QAC7CyP,EAAqBL,GAAS1D,EAAMzL,KAAMyL,EAAM3L,OACtD,OAAO6L,GACDO,IAAS4D,GACJP,EAAiB5D,EAAQ9L,MAAQ0P,EAAiB5D,EAAQ5L,QAE5DyP,EAAmB7D,EAAQ3L,OAASwP,EAAmB7D,EAAQ7L,WAPjC,IAACoM,GAgjBhBsJ,CAAuBvQ,GAAQ8L,GAAiB9L,EACzE,EAEGwQ,GAAkB,EACpBhD,kBACA3E,YACA9E,cACAwE,aACAkF,iBACAlC,WACAkF,8BACAC,0BACArD,oBAEA,IAAKtJ,EAAY4M,UACf,OAAO,KAET,MAAMlE,EAAoB9D,GAA6B5E,EAAYsE,WAAWhE,GAAIkE,GAC5EvE,EAAeyF,GAASZ,EAAW9E,GACnCmF,EAlsBgB,GACtBsE,kBACA3E,YACA9E,cACA0I,oBACAgB,qBAEA,IAAK1J,EAAYiK,iBACf,OAAO,KAGT,IADiB/E,GAAkBwE,GAEjC,OAAO,KAET,SAASmD,EAAUtV,GACjB,MAAM6N,EAAK,CACTlI,KAAM,UACNqD,QAAS,CACPC,YAAajJ,EACb2I,YAAaF,EAAYsE,WAAWhE,KAGxC,MAAO,IACFoJ,EACHtE,KAEJ,CACA,MAAMW,EAAM2D,EAAezD,UAAUF,IAC/B+G,EAAY/G,EAAI5P,OAAS4P,EAAI,GAAK,KACxC,GAAI0D,EACF,OAAOqD,EAAYD,EAAUC,GAAa,KAE5C,MAAMC,EAAmBzH,GAAwBR,EAAW4D,GAC5D,IAAKoE,EACH,OAAKC,EAAiB5W,OAIf0W,EADME,EAAiBA,EAAiB5W,OAAS,GAClCmO,WAAWhE,IAHxB,KAKX,MAAM0M,EAAiBD,EAAiBE,UAAUC,GAAKA,EAAE5I,WAAWhE,KAAOwM,IACrD,IAApBE,GAA4H,GAAU,GACxI,MAAMnD,EAAgBmD,EAAiB,EACvC,OAAInD,EAAgB,EACX,KAGFgD,EADQE,EAAiBlD,GACRvF,WAAWhE,GACpC,EAkpBgB6M,CAAkB,CAC/B1D,kBACA3E,YACA9E,cACA0I,oBACAgB,oBACIF,GAAgB,CACpBC,kBACAxJ,eACA6E,YACAN,aACAxE,cACA0I,oBACAgB,iBACAlC,WACA8B,kBAEF,IAAKnE,EACH,OAAO,KAET,MAAM6G,EAAsBP,GAAiC,CAC3DtG,SACAL,YACAjB,UAAW7D,EACXwE,aACA8E,kBAUF,GAR+B8C,GAA8B,CAC3DtH,YACA9E,cACAqM,uBAAwBL,EACxBxE,SAAUA,EAAS/E,MACnBgF,2BAA2B,EAC3B6E,gBAAgB,IAEU,CAM1B,MAAO,CACLc,gBANsBrB,GAAiC,CACvDC,sBACAlH,YACA0C,aAIArC,SACAkI,kBAAmB,KAEvB,CACA,MAAMtL,EAAWP,EAASwK,EAAqBU,GACzCY,EA/IoB,GAC1BnI,SACAqC,WACAxH,cACAwE,aACA+I,sBAEA,MAAMC,EAAmB5B,GAAepE,EAAUnG,EAAImG,EAASjO,OAAOxD,QAASwX,IACzEE,EAAoBzN,EAAYyC,MAAQmB,GAAgB5D,EAAaqB,EAAIrB,EAAYyC,MAAMlJ,OAAOxD,QAASwX,IAAoBvN,EAC/HmI,EAAOhD,EAAOc,UACdyH,EAAqB1F,GAAsB,CAC/CC,cAAe4D,GAAgB1D,EAAKpC,IAAKvB,GACzCxE,cACAkG,YAAaf,EAAOe,YACpBsB,SAAUgG,EAAiB/K,MAC3B0F,OACAD,oBAAoB,IAEhByF,EAAsB3F,GAAsB,CAChDC,cAAe4D,GAAgB1D,EAAKpC,IAAKvB,GACzCxE,YAAayN,EACbvH,YAAaf,EAAOe,YACpBsB,SAAUA,EAAS/E,MACnB0F,OACAD,oBAAoB,IAEhBrC,EAAY,CAAC,EACbC,EAAU,CAAC,EACXsC,EAAS,CAACD,EAAMuF,EAAoBC,GAiB1C,OAhBAxF,EAAKpC,IAAI7H,QAAQoC,IACf,MAAM+C,EAvCV,SAAuB/C,EAAI8H,GACzB,IAAK,IAAIhS,EAAI,EAAGA,EAAIgS,EAAOjS,OAAQC,IAAK,CACtC,MAAMiN,EAAe+E,EAAOhS,GAAG0P,QAAQxF,GACvC,GAAI+C,EACF,OAAOA,CAEX,CACA,OAAO,IACT,CA+ByBuK,CAActN,EAAI8H,GACnC/E,EACFyC,EAAQxF,GAAM+C,EAGhBwC,EAAUvF,IAAM,IAEA,IACb6E,EACHc,UAAW,CACTF,IAAKoC,EAAKpC,IACVF,YACAC,WAIL,EAiGkB+H,CAAsB,CACrC1I,SACAqC,WACAxH,cACAwE,aACA+I,gBAAiBxL,IAEnB,MAAO,CACLqL,gBAAiBT,EACjBxH,OAAQmI,EACRD,kBAAmBtL,EAEtB,EAED,MAAM+L,GAAiBjK,IACrB,MAAMkK,EAAOlK,EAAUlB,QAAQgB,OAE/B,OADCoK,GAA4G,GAAU,GAChHA,GA2DT,MAAMC,GAAgC,CAAClJ,EAAWwE,KAChD,MAAMpQ,EAAW4L,EAAU9B,KAAKlL,UAAUT,OAC1C,OAAOgS,GAAsBvE,EAAUR,WAAWhE,GAAIgJ,GAAiB9H,EAAStI,EAAUoQ,EAAcpD,YAAYvE,OAASzI,GAEzH+U,GAA0B,CAACnJ,EAAWwE,KAC1C,MAAMpQ,EAAW4L,EAAU9B,KAAKlL,UAChC,OAAOuR,GAAsBvE,EAAUR,WAAWhE,GAAIgJ,GAAiBjH,EAAiBnJ,EAAUwI,EAAO4H,EAAcpD,YAAYvE,QAAUzI,GAG/I,IA0BIgV,GAAiBjT,EAAW,SAAwBiI,EAAMqH,GAC5D,MAAMlH,EAAekH,EAAWrH,EAAKrB,MACrC,MAAO,CACLnJ,MAAO2K,EACP1B,MAAOC,EAAMsB,EAAKrB,KAAMwB,GAE5B,GAEA,MAeM8K,GAAgB,CAAC1L,EAAOG,KAAQ,IACjCH,EACHlJ,OAAQ,IACHkJ,EAAMlJ,OACTqJ,SAGEwL,GAAiB,CAACvK,EAAWiB,EAAWN,KAC5C,MAAM/B,EAAQoB,EAAUpB,MACtBiD,GAASZ,EAAWjB,IAAyH,GAAU,GACvJA,EAAUlB,QAAQM,iBAAiJ,GAAU,GAC/K,MAAMoL,EAAkBH,GAAerK,EAAUX,KAAM4B,EAAUyF,YAAY5I,MACvE2M,EA3BgC,EAACzK,EAAWwK,EAAiB7J,KACnE,MAAMtB,EAAOW,EAAUX,KACvB,GAAkC,YAA9BW,EAAUS,WAAWiK,KACvB,OAAO3M,EAAMsB,EAAKrB,KAAMwM,EAAgBnL,EAAKrB,OAE/C,MAAM2M,EAAiB3K,EAAUlB,QAAQK,KAAKzK,WAAW2K,EAAK+D,MAIxDwH,EAHkB7J,GAA6Bf,EAAUS,WAAWhE,GAAIkE,GAC5CJ,OAAO,CAACsK,EAAKC,IAAcD,EAAMC,EAAUxC,OAAO9T,UAAU6K,EAAK+D,MAAO,GACxEoH,EAAgBnL,EAAKrB,MACjB2M,EACtC,OAAIC,GAAiB,EACZ,KAEF7M,EAAMsB,EAAKrB,KAAM4M,IAcDG,CAAgC/K,EAAWwK,EAAiB7J,GAC7EqK,EAAQ,CACZR,kBACA7K,YAAa8K,EACbQ,kBAAmBjL,EAAUpB,MAAQoB,EAAUpB,MAAMlJ,OAAOqJ,IAAM,MAEpE,IAAKH,EAAO,CACV,MAAME,EAAUI,GAAW,CACzBC,KAAMa,EAAUlB,QAAQK,KACxBC,gBAAiB4L,EACjB3L,KAAMW,EAAUX,KAChBT,MAAOoB,EAAUpB,QAEnB,MAAO,IACFoB,EACHlB,UAEJ,CACA,MAAMoM,EAAYT,EAAiBjN,EAAIoB,EAAMlJ,OAAOqJ,IAAK0L,GAAkB7L,EAAMlJ,OAAOqJ,IAClFoM,EAAWb,GAAc1L,EAAOsM,GAChCpM,EAAUI,GAAW,CACzBC,KAAMa,EAAUlB,QAAQK,KACxBC,gBAAiB4L,EACjB3L,KAAMW,EAAUX,KAChBT,MAAOuM,IAET,MAAO,IACFnL,EACHlB,UACAF,MAAOuM,IAmCX,IAkEIC,GAAgB,EAClBxF,kBACAiD,8BACA5H,YACAoK,SACA1K,aACAL,aACAqD,WACA8B,oBAEA,MAAMtJ,EA3QwB,GAC9ByJ,kBACAuC,sBACAjM,SACAoE,aACAqD,eAEA,MAAM7D,EAAS5D,EAAO4C,QAAQgB,OAC9B,IAAKA,EACH,OAAO,KAET,MAAMT,EAAOnD,EAAOmD,KACdiM,EAAyBhJ,GAASxC,EAAOT,EAAKrC,OAAQ8C,EAAOT,EAAKO,MAClE2L,EAAa3K,GAAgBN,GAAYU,OAAOhB,GAAaA,IAAc9D,GAAQ8E,OAAOhB,GAAaA,EAAU+I,WAAW/H,OAAOhB,GAAa7N,QAAQ6N,EAAUlB,QAAQgB,SAASkB,OAAOhB,GAAayC,GAA+BkB,EAAS/E,MAAxC6D,CAA+CwH,GAAejK,KAAagB,OAAOhB,IAC7R,MAAMwL,EAAiBvB,GAAejK,GACtC,OAAI4F,EACK9F,EAAOT,EAAKiE,cAAgBkI,EAAenM,EAAKiE,cAElDkI,EAAenM,EAAKgE,gBAAkBvD,EAAOT,EAAKgE,kBACxDrC,OAAOhB,IACR,MAAMwL,EAAiBvB,GAAejK,GAChCyL,EAA8BnJ,GAASkJ,EAAenM,EAAKrC,OAAQwO,EAAenM,EAAKO,MAC7F,OAAO0L,EAAuBE,EAAenM,EAAKrC,SAAWsO,EAAuBE,EAAenM,EAAKO,OAAS6L,EAA4B3L,EAAOT,EAAKrC,SAAWyO,EAA4B3L,EAAOT,EAAKO,QAC3MsB,KAAK,CAACC,EAAGC,KACV,MAAMlK,EAAQ+S,GAAe9I,GAAG9B,EAAKgE,gBAC/BlM,EAAS8S,GAAe7I,GAAG/B,EAAKgE,gBACtC,OAAIuC,EACK1O,EAAQC,EAEVA,EAASD,IACf8J,OAAO,CAAChB,EAAWhE,EAAO0P,IAAUzB,GAAejK,GAAWX,EAAKgE,kBAAoB4G,GAAeyB,EAAM,IAAIrM,EAAKgE,iBACxH,IAAKkI,EAAWjZ,OACd,OAAO,KAET,GAA0B,IAAtBiZ,EAAWjZ,OACb,OAAOiZ,EAAW,GAEpB,MAAMI,EAAWJ,EAAWvK,OAAOhB,GACPsC,GAAS2H,GAAejK,GAAWX,EAAKrC,OAAQiN,GAAejK,GAAWX,EAAKO,KAClGgM,CAAkBzD,EAAoB9I,EAAKrB,QAEpD,OAAwB,IAApB2N,EAASrZ,OACJqZ,EAAS,GAEdA,EAASrZ,OAAS,EACbqZ,EAASzK,KAAK,CAACC,EAAGC,IAAM6I,GAAe9I,GAAG9B,EAAKrC,OAASiN,GAAe7I,GAAG/B,EAAKrC,QAAQ,GAEzFuO,EAAWrK,KAAK,CAACC,EAAGC,KACzB,MAAMlK,EAAQmH,EAAU8J,EAAqBzJ,EAAWuL,GAAe9I,KACjEhK,EAASkH,EAAU8J,EAAqBzJ,EAAWuL,GAAe7I,KACxE,OAAIlK,IAAUC,EACLD,EAAQC,EAEV8S,GAAe9I,GAAG9B,EAAKrC,OAASiN,GAAe7I,GAAG/B,EAAKrC,SAC7D,EACJ,EAoNqB6O,CAA0B,CAC5CjG,kBACAuC,oBAAqBU,EACrB3M,OAAQmP,EACR/K,aACAqD,aAEF,IAAKxH,EACH,OAAO,KAET,MAAM0I,EAAoB9D,GAA6B5E,EAAYsE,WAAWhE,GAAIkE,GAC5EuG,EApNkB,GACxBiB,sBACAxE,WACAxH,cACA0I,oBACAY,oBAEA,MAAMqG,EAASjH,EAAkB7D,OAAOC,GAAaiD,GAAiB,CACpExQ,OAAQ0W,GAAwBnJ,EAAWwE,GAC3CtJ,cACAwH,SAAUA,EAAS/E,MACnBgF,2BAA2B,KACzB1C,KAAK,CAACC,EAAGC,KACX,MAAM2K,EAAc7N,EAASiK,EAAqBvE,GAA0BzH,EAAagO,GAA8BhJ,EAAGsE,KACpHuG,EAAc9N,EAASiK,EAAqBvE,GAA0BzH,EAAagO,GAA8B/I,EAAGqE,KAC1H,OAAIsG,EAAcC,GACR,EAENA,EAAcD,EACT,EAEF5K,EAAEV,WAAWzE,MAAQoF,EAAEX,WAAWzE,QAE3C,OAAO8P,EAAO,IAAM,IACrB,EA4LwBG,CAAoB,CACzC9D,oBAAqBU,EACrBlF,WACAxH,cACA0I,oBACAY,kBAEInE,EA9FiB,GACvBuH,8BACA3B,iBACArC,oBACA5D,YACAN,aACAxE,cACAwH,WACA8B,oBAEA,IAAKyB,EAAgB,CACnB,GAAIrC,EAAkBvS,OACpB,OAAO,KAET,MAAM4Z,EAAW,CACf9J,UAAWL,GACXM,YAAaP,GACbP,GAAI,CACFlI,KAAM,UACN8C,YAAa,CACXE,YAAaF,EAAYsE,WAAWhE,GACpCT,MAAO,KAIPmQ,EAA8BvE,GAAiC,CACnEtG,OAAQ4K,EACRjL,YACAjB,UAAW7D,EACXwE,aACA8E,kBAEIrG,EAAkByC,GAASZ,EAAW9E,GAAeA,EAAcoO,GAAepO,EAAa8E,EAAWN,GAShH,OAR+B4H,GAA8B,CAC3DtH,YACA9E,YAAaiD,EACboJ,uBAAwB2D,EACxBxI,SAAUA,EAAS/E,MACnBgF,2BAA2B,EAC3B6E,gBAAgB,IAEcyD,EAAW,IAC7C,CACA,MAAME,EAAsBja,QAAQ0W,EAA4B1M,EAAYkD,KAAKrB,OAASkJ,EAAe/H,KAAKlL,UAAUT,OAAO2I,EAAYkD,KAAKrB,OAC1IgI,EAAgB,MACpB,MAAMqG,EAAanF,EAAezG,WAAWzE,MAC7C,OAAIkL,EAAezG,WAAWhE,KAAOwE,EAAUR,WAAWhE,IAGtD2P,EAFKC,EAKFA,EAAa,CACrB,EATqB,GAUhBhK,EAAcgI,GAAelO,EAAYkD,KAAM4B,EAAUyF,YAC/D,OAAOxB,GAAuB,CAC5BjE,YACA4D,oBACA1I,cACAwH,WACAtB,cACAiC,KAAMvC,GACN/F,MAAOgK,GAEV,EA8BgBsG,CAAmB,CAChCzD,8BACA1M,cACA8E,YACAN,aACAuG,iBACArC,oBACAlB,WACA8B,kBAEF,IAAKnE,EACH,OAAO,KAET,MAAM6G,EAAsBP,GAAiC,CAC3DtG,SACAL,YACAjB,UAAW7D,EACXwE,aACA8E,kBAOF,MAAO,CACL8D,gBANsBrB,GAAiC,CACvDC,sBACAlH,YACA0C,aAIArC,SACAkI,kBAAmB,KAEtB,EAEG+C,GAAqBjL,IACvB,MAAMC,EAAKD,EAAOC,GAClB,OAAKA,EAGW,YAAZA,EAAGlI,KACEkI,EAAGpF,YAAYE,YAEjBkF,EAAG7E,QAAQL,YALT,IAMV,EAMD,IAAImQ,GAAkB,EACpBC,QACApT,WAEA,MAAMqT,EARmB,EAACpL,EAAQhB,KAClC,MAAM7D,EAAK8P,GAAkBjL,GAC7B,OAAO7E,EAAK6D,EAAW7D,GAAM,MAMNkQ,CAAmBF,EAAMnL,OAAQmL,EAAMG,WAAWtM,YACnEuM,EAA4B1a,QAAQua,GACpCI,EAAOL,EAAMG,WAAWtM,WAAWmM,EAAMM,SAAS/M,UAAUvD,IAC5D4O,EAASqB,GAAkBI,EAC3B5J,EAAYmI,EAAOhM,KAAK6D,UACxB8J,EAAmC,aAAd9J,IAAsC,YAAT7J,GAA+B,cAATA,IAAuC,eAAd6J,IAAwC,cAAT7J,GAAiC,eAATA,GAC9J,GAAI2T,IAAuBH,EACzB,OAAO,KAET,MAAMjH,EAA2B,cAATvM,GAAiC,eAATA,EAC1C4H,EAAYwL,EAAMG,WAAWjM,WAAW8L,EAAMM,SAAS9L,UAAUxE,IACjEoM,EAA8B4D,EAAMva,QAAQiN,KAAK8N,iBACjD,WACJtM,EAAU,WACVL,GACEmM,EAAMG,WACV,OAAOI,EAAqBpE,GAAgB,CAC1ChD,kBACAiD,8BACA5H,YACA9E,YAAakP,EACb1K,aACAgD,SAAU8I,EAAM9I,SAChBmF,wBAAyB2D,EAAMva,QAAQoW,OAAO4E,UAC9CrH,eAAgB4G,EAAMnL,OACtBmE,cAAegH,EAAMhH,gBAClB2F,GAAc,CACjBxF,kBACAiD,8BACA5H,YACAoK,SACA1K,aACAL,aACAqD,SAAU8I,EAAM9I,SAChB8B,cAAegH,EAAMhH,eAExB,EAED,SAAS0H,GAAkBV,GACzB,MAAuB,aAAhBA,EAAMW,OAAwC,eAAhBX,EAAMW,KAC7C,CAEA,SAASC,GAAkBzO,GACzB,MAAM8D,EAAmBJ,GAAS1D,EAAM5L,IAAK4L,EAAM1L,QAC7CyP,EAAqBL,GAAS1D,EAAMzL,KAAMyL,EAAM3L,OACtD,OAAO,SAAa6K,GAClB,OAAO4E,EAAiB5E,EAAMvK,IAAMoP,EAAmB7E,EAAMxK,EAC/D,CACF,CAqBA,SAASga,IAAiB,cACxBC,EAAa,UACbtM,EAAS,WACTX,IAEA,MAAMiL,EAAa3K,GAAgBN,GAAYU,OAAOY,IACpD,IAAKA,EAAKmH,UACR,OAAO,EAET,MAAMjJ,EAAS8B,EAAK9C,QAAQgB,OAC5B,IAAKA,EACH,OAAO,EAET,GAhC0B3I,EAgCQ2I,KAhCf5I,EAgCAqW,GA/BRpa,KAAOgE,EAAOlE,OAASiE,EAAMjE,MAAQkE,EAAOhE,MAAQ+D,EAAMlE,IAAMmE,EAAOjE,QAAUgE,EAAMhE,OAASiE,EAAOnE,KAgChH,OAAO,EAjCb,IAAuBkE,EAAOC,EAmC1B,GAAIkW,GAAkBvN,EAAlBuN,CAA0BE,EAAc/Z,QAC1C,OAAO,EAET,MAAM6L,EAAOuC,EAAKvC,KACZmO,EAAc1N,EAAOtM,OAAO6L,EAAK8D,eACjCE,EAAiBkK,EAAclO,EAAKgE,gBACpCC,EAAeiK,EAAclO,EAAKiE,cAClCmK,EAAcnL,GAASxC,EAAOT,EAAKgE,gBAAiBvD,EAAOT,EAAKiE,eAChEoK,EAAmBD,EAAYpK,GAC/BsK,EAAiBF,EAAYnK,GACnC,OAAKoK,IAAqBC,IAGtBD,EACKrK,EAAiBmK,EAEnBlK,EAAekK,KAExB,OAAKjC,EAAWjZ,OAGU,IAAtBiZ,EAAWjZ,OACNiZ,EAAW,GAAG9K,WAAWhE,GAtDpC,UAAyB,cACvB8Q,EAAa,UACbtM,EAAS,WACTsK,IAEA,MAAMqC,EAAc3M,EAAU9B,KAAKlL,UAAUT,OACvCsY,EAASP,EAAW3R,IAAIiU,IAC5B,MAAMxO,EAAOwO,EAAUxO,KACjB3L,EAASqK,EAAM8P,EAAUxO,KAAKrB,KAAMuP,EAAc/Z,OAAO6L,EAAKrB,MAAO6P,EAAU1O,KAAKlL,UAAUT,OAAO6L,EAAK8D,gBAChH,MAAO,CACL1G,GAAIoR,EAAUpN,WAAWhE,GACzByB,SAAUA,EAAS0P,EAAala,MAEjCwN,KAAK,CAACC,EAAGC,IAAMA,EAAElD,SAAWiD,EAAEjD,UACjC,OAAO4N,EAAO,GAAKA,EAAO,GAAGrP,GAAK,IACpC,CAyCSqR,CAAgB,CACrBP,gBACAtM,YACAsK,eARO,IAUX,CAEA,MAAMwC,GAAuB,CAAC7D,EAAMpM,IAAUhL,EAAQ0L,EAAiB0L,EAAMpM,IAU7E,SAASkQ,IAAe,UACtB5L,EAAS,GACT3F,IAEA,OAAOtK,QAAQiQ,EAAUH,QAAQxF,IAAO2F,EAAUJ,UAAUvF,GAC9D,CAqEA,IAuDIwR,GAAgB,EAClBC,aACAjN,YACAN,aACAL,aACAuF,iBACAlC,WACA8B,oBAEA,MAAM8H,EAAgBQ,GAAqB9M,EAAU9B,KAAKlL,UAAWia,GAC/DC,EAAgBb,GAAiB,CACrCC,gBACAtM,YACAX,eAEF,IAAK6N,EACH,OAAOhM,GAET,MAAMhG,EAAcmE,EAAW6N,GACzBtJ,EAAoB9D,GAA6B5E,EAAYsE,WAAWhE,GAAIkE,GAC5EyN,EA7JkB,EAAEpO,EAAWqO,KACrC,MAAMzP,EAAQoB,EAAUpB,MACxB,OAAKA,EAGEmP,GAAqBM,EAAMzP,EAAMlJ,OAAO6J,KAAK1K,OAF3CwZ,CAGV,EAuJ0CvE,CAAoB3N,EAAaoR,GAC1E,MA5EqB,GACrBtM,YACAmN,iCAAkCE,EAClCzI,iBACA1J,cACA0I,oBACAY,oBAEA,IAAKtJ,EAAYiK,iBACf,OAAO,KAET,MAAM/G,EAAOlD,EAAYkD,KACnBgD,EAAcgI,GAAelO,EAAYkD,KAAM4B,EAAUyF,YACzDlH,EAAe6C,EAAYxN,MAC3B0Z,EAAcD,EAAWjP,EAAKrC,OAC9BwR,EAAYF,EAAWjP,EAAKO,KAE5B6G,EADkBhF,GAAwBR,EAAW4D,GACvBO,KAAKqJ,IACvC,MAAMhS,EAAKgS,EAAMhO,WAAWhE,GACtBiS,EAAYD,EAAMtP,KAAKlL,UAEvB0a,EADYD,EAAUrP,EAAK+D,MArBL,EAuBtBwL,EAA0BpJ,GAAsB/I,EAAIgJ,GACpDkB,EAAcqH,GAAe,CACjC5L,UAAWyD,EAAezD,UAC1B3F,OAEF,OAAImS,EACEjI,EACK6H,EAAYE,EAAUrP,EAAKrC,OAAS2R,GAAaH,EAAYE,EAAUrP,EAAKO,KAAO+O,EAErFJ,EAAcG,EAAUrP,EAAKrC,OAASwC,EAAemP,GAAaJ,EAAcG,EAAUrP,EAAKO,KAAOJ,EAAemP,EAE1HhI,EACK6H,EAAYE,EAAUrP,EAAKrC,OAASwC,EAAemP,GAAaH,EAAYE,EAAUrP,EAAKO,KAAOJ,EAAemP,EAEnHJ,EAAcG,EAAUrP,EAAKrC,OAAS2R,GAAaJ,EAAcG,EAAUrP,EAAKO,KAAO+O,IAEhG,OAAKlI,EAGU,CACbpE,cACAD,UAAWyD,EAAezD,UAC1Bb,GAAI,CACFlI,KAAM,UACNqD,QAAS,CACPC,YAAa8J,EAAYhG,WAAWhE,GACpCJ,YAAaF,EAAYsE,WAAWhE,MATjC,IAcV,EAuBQoS,CAAiB,CACtBT,mCACAnN,YACA4E,iBACA1J,cACA0I,oBACAY,mBArImB,GACrB2I,iCAAkCE,EAClCrN,YACA9E,cACA0I,oBACAP,OACAX,WACA8B,oBAEA,MAAMpG,EAAOlD,EAAYkD,KACnBgD,EAAcgI,GAAelO,EAAYkD,KAAM4B,EAAUyF,YACzDlH,EAAe6C,EAAYxN,MAC3B0Z,EAAcD,EAAWjP,EAAKrC,OAC9BwR,EAAYF,EAAWjP,EAAKO,KAqB5BmF,EAlDR,UAAiB,UACf9D,EAAS,QACTyG,EAAO,WACP5C,IAEA,OAAK4C,EAGA5C,GAGD4C,EAAQjH,WAAWzE,MAAQiF,EAAUR,WAAWzE,MAC3C0L,EAAQjH,WAAWzE,MAAQ,EAH3B0L,EAAQjH,WAAWzE,MAHnB,IASX,CAmCmB8S,CAAQ,CACvB7N,YACAyG,QAtBsBjG,GAAwBR,EAAW4D,GAC3BO,KAAKqJ,IACnC,MAAMhS,EAAKgS,EAAMhO,WAAWhE,GACtB+Q,EAAciB,EAAMtP,KAAKlL,UAAUT,OAAO6L,EAAKrB,MAC/C4Q,EAA0BpJ,GAAsB/I,EAAIgJ,GACpDkB,EAAcqH,GAAe,CACjC5L,UAAWkC,EACX7H,OAEF,OAAImS,EACEjI,EACK6H,GAAahB,EAEfe,EAAcf,EAAchO,EAEjCmH,EACK6H,GAAahB,EAAchO,EAE7B+O,EAAcf,KACjB,KAIJ1I,WAAYjD,GAASZ,EAAW9E,KAElC,OAAO+I,GAAuB,CAC5BjE,YACA4D,oBACA1I,cACAwH,WACAW,OACAjC,cACArG,MAAO+I,GAEV,EAsFOgK,CAAiB,CACrBX,mCACAnN,YACA9E,cACA0I,oBACAP,KAAMuB,EAAezD,UACrBuB,WACA8B,iBAEH,EAEGuJ,GAAoB,CAAE1O,EAAY2O,KAAY,IAC7C3O,EACH,CAAC2O,EAAQxO,WAAWhE,IAAKwS,IAG3B,MAAMC,GAAyB,EAC7BrJ,iBACAvE,SACAhB,iBAEA,MAAMgE,EAAOiI,GAAkB1G,GACzBsJ,EAAM5C,GAAkBjL,GAC9B,IAAKgD,EACH,OAAOhE,EAET,GAAIgE,IAAS6K,EACX,OAAO7O,EAET,MAAM8O,EAAgB9O,EAAWgE,GACjC,IAAK8K,EAActQ,QAAQM,gBACzB,OAAOkB,EAET,MAAM2O,EA7ekBjP,KACxB,MAAMgL,EAAQhL,EAAUlB,QAAQM,gBAC/B4L,GAAiI,GAAU,GAC5I,MAAMpM,EAAQoB,EAAUpB,MACxB,IAAKA,EAAO,CACV,MAAME,EAAUI,GAAW,CACzBC,KAAMa,EAAUlB,QAAQK,KACxBE,KAAMW,EAAUX,KAChBT,MAAO,KACPQ,gBAAiB,OAEnB,MAAO,IACFY,EACHlB,UAEJ,CACA,MAAMuQ,EAAerE,EAAMC,kBAC1BoE,GAAkK,GAAU,GAC7K,MAAMlE,EAAWb,GAAc1L,EAAOyQ,GAChCvQ,EAAUI,GAAW,CACzBC,KAAMa,EAAUlB,QAAQK,KACxBE,KAAMW,EAAUX,KAChBT,MAAOuM,EACP/L,gBAAiB,OAEnB,MAAO,IACFY,EACHlB,UACAF,MAAOuM,IAidOmE,CAAkBF,GAClC,OAAOJ,GAAkB1O,EAAY2O,IAEvC,IA2BI/R,GAAS,EACXuP,QACAlD,gBAAiBgG,EACjB3C,WAAY4C,EACZ7L,SAAU8L,EACVnO,OAAQoO,EACRlG,wBAEA,MAAM7F,EAAW8L,GAAkBhD,EAAM9I,SACnCiJ,EAAa4C,GAAoB/C,EAAMG,WACvCrD,EAAkBgG,GAAyB9C,EAAMva,QAAQoW,OAAO4E,UAChE9X,EAASuI,EAAS4L,EAAiBkD,EAAM9a,QAAQ2W,OAAO4E,WACxD5E,EAAS,CACblT,SACA8X,UAAW3D,EACX0D,gBAAiBzP,EAAIiP,EAAM9a,QAAQ2W,OAAO2E,gBAAiB7X,IAEvD+J,EAAO,CACX+N,UAAW1P,EAAI8K,EAAO4E,UAAWvJ,EAASjO,OAAOxD,SACjD+a,gBAAiBzP,EAAI8K,EAAO2E,gBAAiBtJ,EAASjO,OAAOxD,SAC7DkD,OAAQoI,EAAI8K,EAAOlT,OAAQuO,EAASjO,OAAO6J,KAAK1K,QAE5C3C,EAAU,CACdoW,SACAnJ,QAEF,GAAoB,eAAhBsN,EAAMW,MACR,MAAO,IACFX,EACHG,aACAjJ,WACAzR,WAGJ,MAAM+O,EAAY2L,EAAWjM,WAAW8L,EAAMM,SAAS9L,UAAUxE,IAC3DkT,EAAYD,GAAgBzB,GAAc,CAC9CC,WAAY/O,EAAK/J,OACjB6L,YACAN,WAAYiM,EAAWjM,WACvBL,WAAYsM,EAAWtM,WACvBuF,eAAgB4G,EAAMnL,OACtBqC,WACA8B,cAAegH,EAAMhH,gBAEjBmK,EAvEoB,GAC1B3O,YACAN,aACAL,aACAuF,iBACAvE,aAEA,MAAMuO,EAAUX,GAAuB,CACrCrJ,iBACAvE,SACAhB,eAEI+K,EAASkB,GAAkBjL,GACjC,IAAK+J,EACH,OAAOwE,EAET,MAAM7P,EAAYM,EAAW+K,GAC7B,GAAIxJ,GAASZ,EAAWjB,GACtB,OAAO6P,EAET,GAAI7P,EAAUlB,QAAQM,gBACpB,OAAOyQ,EAET,MAAMC,EAAUvF,GAAevK,EAAWiB,EAAWN,GACrD,OAAOqO,GAAkBa,EAASC,EACnC,EA8CiCC,CAAsB,CACpD9O,YACAK,OAAQqO,EACR9J,eAAgB4G,EAAMnL,OACtBX,WAAYiM,EAAWjM,WACvBL,WAAYsM,EAAWtM,aAczB,MAZe,IACVmM,EACHva,UACA0a,WAAY,CACVjM,WAAYiM,EAAWjM,WACvBL,WAAYsP,GAEdtO,OAAQqO,EACRhM,WACA6F,kBAAmBA,GAAqB,KACxCnF,oBAAoBmF,GAA4B,KAGnD,EAKD,IAAIwG,GAAY,EACd1O,SACAqC,WACAhD,aACAxE,cACAkI,yBAEA,MAAMC,EAAOhD,EAAOc,UACdgC,EAXR,SAAuB6D,EAAKtH,GAC1B,OAAOsH,EAAIrO,IAAI6C,GAAMkE,EAAWlE,GAClC,CASwBwT,CAAc3L,EAAKpC,IAAKvB,GACxCyB,EAAY+B,GAAsB,CACtCC,gBACAjI,cACAkG,YAAaf,EAAOe,YACpBsB,SAAUA,EAAS/E,MACnByF,qBACAC,SAEF,MAAO,IACFhD,EACHc,YAEH,EAEG8N,GAA2B,EAC7B5O,SACAL,YACAjB,YACAW,aACAgD,WACA8B,oBAEA,MAAM0C,EAAsBP,GAAiC,CAC3DtG,SACAL,YACAN,aACAX,YACAyF,kBAEF,OAAOyC,GAAiC,CACtCC,sBACAlH,YACA0C,YAEH,EAEGwM,GAAc,EAChB1D,QACAG,WAAY4C,EACZ7L,SAAU8L,MAEe,SAAvBhD,EAAM2D,cAAsF,GAAU,GACxG,MAAMC,EAAuB5D,EAAMnL,OAC7BqC,EAAW8L,GAAkBhD,EAAM9I,SACnCiJ,EAAa4C,GAAoB/C,EAAMG,YACvC,WACJjM,EAAU,WACVL,GACEsM,EACE3L,EAAYN,EAAW8L,EAAMM,SAAS9L,UAAUxE,IAChD4O,EAASkB,GAAkB8D,GAChChF,GAAwH,GAAU,GACnI,MAAMlP,EAAcmE,EAAW+K,GACzB/J,EAAS0O,GAAU,CACvB1O,OAAQ+O,EACR1M,WACAxH,cACAwE,eAEI4I,EAAkB2G,GAAyB,CAC/C5O,SACAL,YACAjB,UAAW7D,EACXwE,aACAgD,WACA8B,cAAegH,EAAMhH,gBAEvB,OAAOvI,GAAO,CACZoE,SACAiI,kBACAkD,QACAG,aACAjJ,YAEH,EAOG2M,GAAgB,EAClBrP,YACA6L,OACAnM,aACAgD,eAEA,MAAMtB,EAAcgI,GAAeyC,EAAKzN,KAAM4B,EAAUyF,YAClD6J,EAAaxP,GAA6B+L,EAAKrM,WAAWhE,GAAIkE,GAC9D6P,EAAWD,EAAWhL,QAAQtE,IACpB,IAAduP,GAA2H,GAAU,GACvI,MAAMpM,EAAgBmM,EAAWzb,MAAM0b,EAAW,GAC5C9K,EAAWtB,EAAc7D,OAAO,CAACC,EAAUoB,KAC/CpB,EAASoB,EAAKnB,WAAWhE,KAAM,EACxB+D,GACN,CAAC,GACEiF,EAAgB,CACpBgL,cAAwC,YAAzB3D,EAAKrM,WAAWiK,KAC/BrI,cACAqD,YAvBkB,IAACjF,EAyCrB,MAAO,CACLa,OATa,CACbc,UATgB+B,GAAsB,CACtCC,gBACAjI,YAAa2Q,EACbzK,cACAiC,KAAM,KACNX,SAAUA,EAAS/E,MACnByF,oBAAoB,IAIpBhC,cACAd,GAAI,CACFlI,KAAM,UACN8C,aAtCiBsE,EAsCYQ,EAAUR,WAtCR,CACnCzE,MAAOyE,EAAWzE,MAClBK,YAAaoE,EAAWpE,gBAyCtBoJ,gBAEH,EAOD,MAAMzI,GAAQ0T,IACR,GAMAC,GAASD,IACT,GAON,IAyBIE,GAAkC,EACpCC,YACAC,oBACAnN,eAEA,MAAMoN,EAAqBpN,EAASjO,OAAO6J,KAAK1K,MAChD,OAAOgc,EAAUjX,IAAIqH,IACnB,MAAM5E,EAAc4E,EAAUR,WAAWpE,YAEnCuC,EAfK,CAACoB,IACd,MAAMpB,EAAQoB,EAAUpB,MAExB,OADCA,GAAyG,GAAU,GAC7GA,CACR,EAWiBoS,CADGF,EAAkBzU,IAE7B4U,EAAwBrS,EAAMlJ,OAAO6J,KAAK1K,MAE1Cqc,EArCY,GACpBjQ,YACA7L,OAAQ+b,EACRC,0BAEA,MAAM9I,EAASlT,EAAO6L,EAAUqH,OAAQ6I,GAClChS,EAAO1J,EAAW6S,EAAQ8I,GAUhC,MATc,IACTnQ,EACHoQ,YAAa,IACRpQ,EAAUoQ,YACb/I,UAEFA,SACAnJ,OAGH,EAoBiBmS,CAAgB,CAC5BrQ,YACA7L,OAHkBoI,EAAIuT,EAAoBE,GAI1CG,oBAAqBzN,EAASjO,OAAO/D,UAEvC,OAAOuf,GAEV,EA8ED,MAAMK,GAAa9E,GAAgC,SAAvBA,EAAM2D,aAC5BoB,GAAsB,CAAC/E,EAAOwC,EAASwC,KAC3C,MAAM7E,EAhJgB,EAAEA,EAAYqC,KAAY,CAChDtO,WAAYiM,EAAWjM,WACvBL,WAAY0O,GAAkBpC,EAAWtM,WAAY2O,KA8IlCyC,CAAkBjF,EAAMG,WAAYqC,GACvD,OAAKsC,GAAW9E,IAAUgF,EACjBvU,GAAO,CACZuP,QACAG,eAGGuD,GAAY,CACjB1D,QACAG,gBAGJ,SAAS+E,GAAwBlF,GAC/B,OAAIA,EAAMvR,YAAqC,SAAvBuR,EAAM2D,aACrB,IACF3D,EACHjD,kBAAmB,MAGhBiD,CACT,CACA,MAAMmF,GAAS,CACbxE,MAAO,OACPyE,UAAW,KACXC,aAAa,GAEf,IAAIC,GAAU,CAAEtF,EAAQmF,GAAQI,KAC9B,GAAoB,UAAhBA,EAAO3Y,KACT,MAAO,IACFuY,GACHE,aAAa,GAGjB,GAAoB,oBAAhBE,EAAO3Y,KAA4B,CACnB,SAAhBoT,EAAMW,OAA+H,GAAU,GACjJ,MAAM,SACJL,EAAQ,gBACRxD,EAAe,SACf5F,EAAQ,WACRiJ,EAAU,aACVwD,GACE4B,EAAOC,QACLhR,EAAY2L,EAAWjM,WAAWoM,EAAS9L,UAAUxE,IACrDqQ,EAAOF,EAAWtM,WAAWyM,EAAS/M,UAAUvD,IAChD6L,EAAS,CACb4E,UAAW3D,EACX0D,gBAAiBhM,EAAUqH,OAAOrU,UAAUT,OAC5C4B,OAAQmI,GAEJ5L,EAAU,CACd2W,SACAnJ,KAAM,CACJ+N,UAAW1P,EAAI8K,EAAO4E,UAAWvJ,EAASjO,OAAO/D,SACjDsb,gBAAiBzP,EAAI8K,EAAO4E,UAAWvJ,EAASjO,OAAO/D,SACvDyD,OAAQoI,EAAI8K,EAAO4E,UAAWvJ,EAASjO,OAAO6J,KAAK1K,SAGjDqd,EAAwBtR,GAAgBgM,EAAWtM,YAAY6R,MAAMvQ,IAASA,EAAKwQ,gBACnF,OACJ9Q,EAAM,cACNmE,GACE6K,GAAc,CAChBrP,YACA6L,OACAnM,WAAYiM,EAAWjM,WACvBgD,aAkBF,MAhBe,CACbyJ,MAAO,WACPlS,YAAY,EACZ6R,WACAqD,eACAxD,aACAjb,UACAO,QAASP,EACTugB,wBACA5Q,SACAmE,gBACA4M,aAAc/Q,EACdqC,WACA6F,kBAAmB,KACnBnF,mBAAoB,KAGxB,CACA,GAAoB,wBAAhB2N,EAAO3Y,KAAgC,CACzC,GAAoB,eAAhBoT,EAAMW,OAA0C,iBAAhBX,EAAMW,MACxC,OAAOX,EAES,aAAhBA,EAAMW,OAAwI,GAAU,GAK1J,MAJe,IACVX,EACHW,MAAO,aAGX,CACA,GAAoB,2BAAhB4E,EAAO3Y,KAET,MADkB,eAAhBoT,EAAMW,OAA0C,iBAAhBX,EAAMW,OAAqJ,GAAU,GA9KvK,GAClCX,QACA6F,gBAEAtV,KACA,MAAMuV,EAAmBD,EAAUE,SAAS5Y,IAAIsD,IAC9C,MAAMuV,EAAWhG,EAAMG,WAAWtM,WAAWpD,EAAOb,aAEpD,OADiB0D,GAAgB0S,EAAUvV,EAAOxH,UAG9C4K,EAAa,IACdmM,EAAMG,WAAWtM,cACjBD,GAAekS,IAEdG,EAAmBhS,GAAekQ,GAAgC,CACtEC,UAAWyB,EAAUzB,UACrBC,kBAAmBxQ,EACnBqD,SAAU8I,EAAM9I,YAEZhD,EAAa,IACd8L,EAAMG,WAAWjM,cACjB+R,GAELJ,EAAUK,SAAStY,QAAQoC,WAClBkE,EAAWlE,KAEpB,MAAMmQ,EAAa,CACjBtM,aACAK,cAEIiS,EAAYrG,GAAkBE,EAAMnL,QACpCuR,EAAUD,EAAYhG,EAAWtM,WAAWsS,GAAa,KACzD3R,EAAY2L,EAAWjM,WAAW8L,EAAMM,SAAS9L,UAAUxE,IAC3DqQ,EAAOF,EAAWtM,WAAWmM,EAAMM,SAAS/M,UAAUvD,KAE1D6E,OAAQ+Q,EAAY,cACpB5M,GACE6K,GAAc,CAChBrP,YACA6L,OACAnM,aACAgD,SAAU8I,EAAM9I,WAEZkC,EAAiBgN,GAAWA,EAAQzM,iBAAmBqG,EAAMnL,OAAS+Q,EACtE/Q,EAAS2M,GAAc,CAC3BC,WAAYzB,EAAMva,QAAQiN,KAAK/J,OAC/B6L,UAAW2L,EAAWjM,WAAW8L,EAAMM,SAAS9L,UAAUxE,IAC1DkE,WAAYiM,EAAWjM,WACvBL,WAAYsM,EAAWtM,WACvBuF,iBACAlC,SAAU8I,EAAM9I,SAChB8B,kBAEFkL,KACA,MAAMmC,EAAgB,IACjBrG,EACHW,MAAO,WACP9L,SACA+Q,eACAzF,aACAnH,gBACApB,oBAAoB,GAEtB,MAAoB,eAAhBoI,EAAMW,MACD0F,EAEW,IACfA,EACH1F,MAAO,eACP/P,OAAQoP,EAAMpP,OACd0V,WAAW,EAGd,EAsGUC,CAA8B,CACnCvG,QACA6F,UAAWN,EAAOC,UAGtB,GAAoB,SAAhBD,EAAO3Y,KAAiB,CAC1B,GAAoB,iBAAhBoT,EAAMW,MACR,OAAOX,EAERU,GAAkBV,IAA4H,GAAU,GACzJ,MACEnE,OAAQiB,GACNyI,EAAOC,QACX,OAAIrU,EAAU2L,EAAiBkD,EAAMva,QAAQoW,OAAO4E,WAC3CT,EAEFvP,GAAO,CACZuP,QACAlD,kBACAjI,OAAQiQ,GAAW9E,GAASA,EAAMnL,OAAS,MAE/C,CACA,GAAoB,4BAAhB0Q,EAAO3Y,KAAoC,CAC7C,GAAoB,iBAAhBoT,EAAMW,MACR,OAAOuE,GAAwBlF,GAEjC,GAAoB,eAAhBA,EAAMW,MACR,OAAOuE,GAAwBlF,GAEhCU,GAAkBV,IAA4H,GAAU,GACzJ,MAAM,GACJhQ,EAAE,UACFwD,GACE+R,EAAOC,QACLve,EAAS+Y,EAAMG,WAAWtM,WAAW7D,GAC3C,IAAK/I,EACH,OAAO+Y,EAET,MAAMnN,EAAWS,GAAgBrM,EAAQuM,GACzC,OAAOuR,GAAoB/E,EAAOnN,GAAU,EAC9C,CACA,GAAoB,gCAAhB0S,EAAO3Y,KAAwC,CACjD,GAAoB,iBAAhBoT,EAAMW,MACR,OAAOX,EAERU,GAAkBV,IAAiI,GAAU,GAC9J,MAAM,GACJhQ,EAAE,UACFsM,GACEiJ,EAAOC,QACLve,EAAS+Y,EAAMG,WAAWtM,WAAW7D,GAC1C/I,GAAoI,GAAU,GAC7IA,EAAOqV,YAAcA,GAC6B,GAAU,GAC9D,MAAMkG,EAAU,IACXvb,EACHqV,aAEF,OAAOyI,GAAoB/E,EAAOwC,GAAS,EAC7C,CACA,GAAoB,wCAAhB+C,EAAO3Y,KAAgD,CACzD,GAAoB,iBAAhBoT,EAAMW,MACR,OAAOX,EAERU,GAAkBV,IAAiI,GAAU,GAC9J,MAAM,GACJhQ,EAAE,iBACF2J,GACE4L,EAAOC,QACLve,EAAS+Y,EAAMG,WAAWtM,WAAW7D,GAC1C/I,GAA6I,GAAU,GACtJA,EAAO0S,mBAAqBA,GAC6B,GAAU,GACrE,MAAM6I,EAAU,IACXvb,EACH0S,oBAEF,OAAOoL,GAAoB/E,EAAOwC,GAAS,EAC7C,CACA,GAAoB,0BAAhB+C,EAAO3Y,KAAkC,CAC3C,GAAoB,iBAAhBoT,EAAMW,OAA4C,mBAAhBX,EAAMW,MAC1C,OAAOX,EAERU,GAAkBV,IAAqH,GAAU,GACjJA,EAAMyF,uBAAkJ,GAAU,GACnK,MAAMjS,EAAY+R,EAAOC,QAAQhS,UACjC,GAAIrC,EAAU6O,EAAM9I,SAASjO,OAAOxD,QAAS+N,GAC3C,OAAO0R,GAAwBlF,GAEjC,MAAM9I,EAAWoE,GAAe0E,EAAM9I,SAAU1D,GAChD,OAAIsR,GAAW9E,GACN0D,GAAY,CACjB1D,QACA9I,aAGGzG,GAAO,CACZuP,QACA9I,YAEJ,CACA,GAAoB,+BAAhBqO,EAAO3Y,KAAuC,CAChD,IAAK8T,GAAkBV,GACrB,OAAOA,EAET,MAAMvB,EAAY8G,EAAOC,QAAQ/G,UACjC,GAAItN,EAAUsN,EAAWuB,EAAM9I,SAASjO,OAAOqJ,KAC7C,OAAO0N,EAET,MAAMnC,EAAgB,IACjBmC,EAAM9I,SACTjO,OAAQ,IACH+W,EAAM9I,SAASjO,OAClBqJ,IAAKmM,IAGT,MAAO,IACFuB,EACH9I,SAAU2G,EAEd,CACA,GAAoB,YAAhB0H,EAAO3Y,MAAsC,cAAhB2Y,EAAO3Y,MAAwC,cAAhB2Y,EAAO3Y,MAAwC,eAAhB2Y,EAAO3Y,KAAuB,CAC3H,GAAoB,eAAhBoT,EAAMW,OAA0C,iBAAhBX,EAAMW,MACxC,OAAOX,EAES,aAAhBA,EAAMW,OAA0I,GAAU,GAC5J,MAAMvb,EAAS2a,GAAgB,CAC7BC,QACApT,KAAM2Y,EAAO3Y,OAEf,OAAKxH,EAGEqL,GAAO,CACZuP,QACAnL,OAAQzP,EAAOyP,OACfiI,gBAAiB1X,EAAO0X,gBACxBC,kBAAmB3X,EAAO2X,oBANnBiD,CAQX,CACA,GAAoB,iBAAhBuF,EAAO3Y,KAAyB,CAClC,MAAMgE,EAAS2U,EAAOC,QAAQ5U,OACZ,eAAhBoP,EAAMW,OAA4J,GAAU,GAO9K,MANiB,IACZX,EACHW,MAAO,eACP2F,WAAW,EACX1V,SAGJ,CACA,GAAoB,iBAAhB2U,EAAO3Y,KAAyB,CAClC,MAAM,UACJwY,EAAS,aACToB,EAAY,oBACZC,GACElB,EAAOC,QACO,aAAhBxF,EAAMW,OAAwC,iBAAhBX,EAAMW,OAAwI,GAAU,GAQxL,MAPe,CACbA,MAAO,iBACPyE,YACAoB,eACAC,sBACAtG,WAAYH,EAAMG,WAGtB,CACA,GAAoB,kBAAhBoF,EAAO3Y,KAA0B,CACnC,MAAM,UACJwY,GACEG,EAAOC,QACX,MAAO,CACL7E,MAAO,OACPyE,YACAC,aAAa,EAEjB,CACA,OAAOrF,CACR,EAED,MAIM0G,GAAS/a,IAAQ,CACrBiB,KAAM,OACN4Y,QAAS7Z,IAMLgb,GAAuBhb,IAAQ,CACnCiB,KAAM,yBACN4Y,QAAS7Z,IAELib,GAAqB,KAAM,CAC/Bha,KAAM,sBACN4Y,QAAS,OAELqB,GAAwBlb,IAAQ,CACpCiB,KAAM,0BACN4Y,QAAS7Z,IAELmb,GAA2Bnb,IAAQ,CACvCiB,KAAM,8BACN4Y,QAAS7Z,IAELob,GAAkCpb,IAAQ,CAC9CiB,KAAM,sCACN4Y,QAAS7Z,IAELqb,GAAOrb,IAAQ,CACnBiB,KAAM,OACN4Y,QAAS7Z,IAULsb,GAAS,KAAM,CACnBra,KAAM,UACN4Y,QAAS,OAEL0B,GAAW,KAAM,CACrBta,KAAM,YACN4Y,QAAS,OAEL2B,GAAY,KAAM,CACtBva,KAAM,aACN4Y,QAAS,OAEL4B,GAAW,KAAM,CACrBxa,KAAM,YACN4Y,QAAS,OAEL6B,GAAQ,KAAM,CAClBza,KAAM,QACN4Y,QAAS,OAML8B,GAAe3b,IAAQ,CAC3BiB,KAAM,gBACN4Y,QAAS7Z,IAEL4b,GAAS5b,IAAQ,CACrBiB,KAAM,OACN4Y,QAAS7Z,IAML6b,GAAwB,KAAM,CAClC5a,KAAM,0BACN4Y,QAAS,OAkGX,MAAMiC,GAEE,0BAEFxX,GACK,CACPyX,KAAM,EACNC,UAAW,IAHT1X,GAKG,CACLyX,KAAM,KAGJE,GAAU,CACdC,YAAa,GACbC,YAAa,IACbC,YAAa,KAETC,GAAoB,GAAGJ,GAAQC,gBAjBtB,+BAkBTI,GAAc,CAClBC,MAAO,WAAWF,KAClBG,KAAM,aAAaH,eAA8BA,KACjDN,KAAMU,IACJ,MAAMC,EAAS,GAAGD,MAAaX,KAC/B,MAAO,aAAaY,cAAmBA,KAEzCR,YAAa,aAAaG,KAC1BpD,YAAa,UAAUoD,aAA4BA,cAA6BA,MAE5EM,GAAS3f,GAAUwI,EAAUxI,EAAQmI,QAAUyX,EAAY,aAAa5f,EAAO9B,QAAQ8B,EAAO7B,OAC9F0hB,GACJF,GADIE,GAEE,CAAC7f,EAAQ8f,KACb,MAAMC,EAAYJ,GAAO3f,GACzB,GAAK+f,EAGL,OAAKD,EAGE,GAAGC,WAAmBzY,GAAcyX,QAFlCgB,IAMP,YACJZ,GAAW,YACXC,IACEH,GACEe,GAAgBZ,GAAcD,GAwKpC,IAAIJ,GApFmB,EACrBkB,WACAC,cACIC,GAAQvD,IACZ,GAAoB,SAAhBA,EAAO3Y,KAET,YADAkc,EAAKvD,GAGP,MAAMvF,EAAQ4I,IACRhY,EAAS2U,EAAOC,QAAQ5U,OAC9B,GAAoB,eAAhBoP,EAAMW,MAIR,YAHAkI,EAvPgBld,KAAQ,CAC1BiB,KAAM,eACN4Y,QAAS7Z,IAqPEod,CAAY,CACnBnY,YAIJ,GAAoB,SAAhBoP,EAAMW,MACR,OAEuC,iBAAhBX,EAAMW,OAA4BX,EAAMsG,WAC8E,GAAU,GACvI,aAAhBtG,EAAMW,OAAwC,iBAAhBX,EAAMW,OAA+H,GAAU,GAC/K,MAAML,EAAWN,EAAMM,SACjBH,EAAaH,EAAMG,WACnB3L,EAAY2L,EAAWjM,WAAW8L,EAAMM,SAAS9L,UAAUxE,KAC3D,OACJ6E,EAAM,uBACNmU,GAhEgB,GAClB9U,aACAtD,SACAqY,aACA5I,OACAnJ,WACA0O,mBAEA,IAAKqD,EAAWnU,IAAiB,SAAXlE,EAQpB,MAAO,CACLiE,OAR2B0O,GAAU,CACrCrP,aACAW,OAAQ+Q,EACRlW,YAAa2Q,EACbnJ,WACAU,oBAAoB,IAIpBoR,wBAAwB,GAG5B,MAA2B,YAAvBC,EAAWnU,GAAGlI,KACT,CACLiI,OAAQoU,EACRD,wBAAwB,GAOrB,CACLnU,OALsB,IACnBoU,EACHtT,UAAWL,IAIX0T,wBAAwB,EAE3B,EA8BKE,CAAc,CAChBtY,SACAqY,WAAYjJ,EAAMnL,OAClBmE,cAAegH,EAAMhH,cACrB4M,aAAc5F,EAAM4F,aACpBvF,KAAML,EAAMG,WAAWtM,WAAWmM,EAAMM,SAAS/M,UAAUvD,IAC3DkH,SAAU8I,EAAM9I,SAChBhD,WAAY8L,EAAMG,WAAWjM,aAEzBxE,EAAcsZ,EAAyBpU,GAAkBC,GAAU,KACnE5E,EAAU+Y,EAAyBjU,GAAcF,GAAU,KAC3DpF,EAAS,CACbF,MAAO+Q,EAAS9L,UAAUjF,MAC1BK,YAAa0Q,EAAS/M,UAAUvD,IAE5B5K,EAAS,CACb8K,YAAasE,EAAUR,WAAWhE,GAClCpD,KAAM4H,EAAUR,WAAWpH,KAC3B6C,SACAmB,SACAqN,KAAM+B,EAAM2D,aACZjU,cACAO,WAEIwW,EAnHqB,GAC3B5R,SACAL,YACA2L,aACAjJ,WACA8B,oBAEA,MAAM,WACJ9E,EAAU,WACVL,GACEsM,EACEvQ,EAAckQ,GAAkBjL,GAChCnF,EAAcE,EAAciE,EAAWjE,GAAe,KACtDyQ,EAAOxM,EAAWW,EAAUR,WAAWpE,aACvCuZ,EAAkB1F,GAAyB,CAC/C5O,SACAL,YACAN,aACA8E,gBACAzF,UAAW7D,GAAe2Q,EAC1BnJ,aAGF,OADehG,EAASiY,EAAiB3U,EAAUqH,OAAOrU,UAAUT,OAErE,EA2F6BqiB,CAAuB,CACjDvU,SACAL,YACA2L,aACAjJ,SAAU8I,EAAM9I,SAChB8B,cAAegH,EAAMhH,gBAEjBoM,EAAY,CAChB9E,SAAUN,EAAMM,SAChBtH,cAAegH,EAAMhH,cACrB5T,SACAyP,UAGF,MAD6B1D,EAAU6O,EAAMva,QAAQoW,OAAOlT,OAAQ8d,IAAwB/gB,QAAQN,EAAO6K,UAKzG,YAHA4Y,EAASvB,GAAa,CACpBlC,eAIJ,MAAMoB,EAzJc,GACpB/gB,UACAiK,cACAkB,aAEA,MAAMyY,EAAa5X,EAAShM,EAASiK,GACrC,GAAI2Z,GAAc,EAChB,OAAOvB,GAET,GAAIuB,GAXwB,KAY1B,OAAOtB,GAET,MACMK,EAAWN,GAAca,IADZU,EAdS,MAiB5B,OAAO/gB,QADyB,WAAXsI,EAfI,GAekBwX,EAAgCA,GAChDkB,QAAQ,GACpC,EAyIsBC,CAAgB,CACnC9jB,QAASua,EAAMva,QAAQoW,OAAOlT,OAC9B+G,YAAa+W,EACb7V,WAOFiY,EA1UkBld,KAAQ,CAC1BiB,KAAM,eACN4Y,QAAS7Z,IAwUA6d,CALI,CACX/C,sBACAD,eACApB,gBAMA,GAAkB,KAAO,CAC3Bve,EAAGqC,OAAOC,YACVrC,EAAGoC,OAAOE,cAkBZ,SAASqgB,IAAkB,eACzBC,IAKA,MAAMC,EAAY,EAHlB,WACED,EAAe,KACjB,GAEMtc,EAtBR,SAAgCqD,GAC9B,MAAO,CACL/C,UAAW,SACXL,QAAS,CACPuc,SAAS,EACTC,SAAS,GAEXte,GAAIgD,IACEA,EAAMtH,SAAWiC,QAAUqF,EAAMtH,SAAWiC,OAAO4gB,UAGvDrZ,KAGN,CAQkBsZ,CAAuBJ,GACvC,IAAI9b,EAASf,EACb,SAASkd,IACP,OAAOnc,IAAWf,CACpB,CAWA,MAAO,CACLyD,MAXF,WACIyZ,KAA4H,GAAU,GACxInc,EAASd,EAAW7D,OAAQ,CAACkE,GAC/B,EASE6c,KARF,WACGD,KAAuH,GAAU,GAClIL,EAAU5d,SACV8B,IACAA,EAASf,CACX,EAIEkd,WAEJ,CAqBA,IAAIE,GAlBmBC,IACrB,MAAMC,EAAWX,GAAkB,CACjCC,eAAgBlW,IACd2W,EAAMtB,SA/ZuB,CACjCjc,KAAM,wBACN4Y,QA6ZsC,CAChChS,kBAIN,OAAOsV,GAAQvD,IACR6E,EAASJ,YAA8B,oBAAhBzE,EAAO3Y,MACjCwd,EAAS7Z,QAEP6Z,EAASJ,YAbCzE,IAA0B,kBAAhBA,EAAO3Y,MAA4C,iBAAhB2Y,EAAO3Y,MAA2C,UAAhB2Y,EAAO3Y,KAazEyd,CAAU9E,IACnC6E,EAASH,OAEXnB,EAAKvD,KA+BL+E,GAAkB,KACpB,MAAMC,EAAU,GA0BhB,MAAO,CACLxZ,IApBUxF,IACV,MAAMif,EAAUC,WAAW,IAPbD,KACd,MAAMjb,EAAQgb,EAAQ5N,UAAUxH,GAAQA,EAAKqV,UAAYA,IAC5C,IAAXjb,GAAmG,GAAU,GAC/G,MAAOmb,GAASH,EAAQI,OAAOpb,EAAO,GACtCmb,EAAMvkB,YAG2BykB,CAAQJ,IACnCE,EAAQ,CACZF,UACArkB,SAAUoF,GAEZgf,EAAQvS,KAAK0S,IAebrD,MAbY,KACZ,IAAKkD,EAAQ1kB,OACX,OAEF,MAAMglB,EAAU,IAAIN,GACpBA,EAAQ1kB,OAAS,EACjBglB,EAAQjd,QAAQ8c,IACdI,aAAaJ,EAAMF,SACnBE,EAAMvkB,cAOX,EAED,MA2BM4kB,GAAc,CAAC9G,EAAK1Y,KACxBgF,KACAhF,IACA2Y,MAEI8G,GAAe,CAAC1K,EAAUrC,KAAS,CACvC/N,YAAaoQ,EAAS9L,UAAUxE,GAChCpD,KAAM0T,EAAS/M,UAAU3G,KACzB6C,OAAQ,CACNG,YAAa0Q,EAAS/M,UAAUvD,GAChCT,MAAO+Q,EAAS9L,UAAUjF,OAE5B0O,SAEF,SAAS2M,GAAQK,EAAWC,EAAMC,EAAUC,GAC1C,IAAKH,EAEH,YADAE,EAASC,EAAkBF,IAG7B,MAAMG,EAzGkB,CAACF,IACzB,IAAIG,GAAY,EACZC,GAAY,EAChB,MAAMC,EAAYf,WAAW,KAC3Bc,GAAY,IAERnmB,EAASyH,IACTye,GAIAC,IAOJD,GAAY,EACZH,EAASte,GACTie,aAAaU,KAGf,OADApmB,EAAOkmB,UAAY,IAAMA,EAClBlmB,CACR,EAiFoBqmB,CAAoBN,GAIvCF,EAAUC,EAHO,CACfC,SAAUE,IAGPA,EAAWC,aACdH,EAASC,EAAkBF,GAE/B,CACA,IAkGIQ,GAAa,CAAEC,EAAeR,KAChC,MAAMS,EAnGW,EAAED,EAAeR,KAClC,MAAMU,EAAevB,KACrB,IAAIwB,EAAW,KACf,MAmEMpE,EAAOtiB,IACV0mB,GAAqI,GAAU,GAChJA,EAAW,KACXf,GAAY,EAAa,IAAMH,GAAQe,IAAgBhb,UAAWvL,EAAQ+lB,EAAUta,EAASF,aAc/F,MAAO,CACLob,cArFoB,CAAC7b,EAAa+N,KAChC6N,GAAgJ,GAAU,GAC5Jf,GAAY,EAAmB,KAC7B,MAAMxf,EAAKogB,IAAgBK,gBACvBzgB,GAKFA,EAJe,CACb2E,cACA+N,YA+ENgO,YAzEkB,CAAC3L,EAAUrC,KAC3B6N,GAAkJ,GAAU,GAC9Jf,GAAY,EAAqB,KAC/B,MAAMxf,EAAKogB,IAAgBO,kBACvB3gB,GACFA,EAAGyf,GAAa1K,EAAUrC,OAqE9B1N,MAjEY,CAAC+P,EAAUrC,KACrB6N,GAAkJ,GAAU,GAC9J,MAAMZ,EAAOF,GAAa1K,EAAUrC,GACpC6N,EAAW,CACT7N,OACAkO,aAAc7L,EACd8L,aAAclB,EAAKzb,OACnB4c,YAAa,MAEfR,EAAa9a,IAAI,KACfga,GAAY,EAAe,IAAMH,GAAQe,IAAgBrb,YAAa4a,EAAMC,EAAUta,EAASP,iBAwDjGG,OArDa,CAAC6P,EAAUzL,KACxB,MAAMnE,EAAWkE,GAAkBC,GAC7B5E,EAAU8E,GAAcF,GAC7BiX,GAAqI,GAAU,GAChJ,MAAMQ,GA/Ec,EAAC7hB,EAAOC,KAC9B,GAAID,IAAUC,EACZ,OAAO,EAET,MAAM6hB,EAAmB9hB,EAAM+J,UAAUxE,KAAOtF,EAAO8J,UAAUxE,IAAMvF,EAAM+J,UAAU5E,cAAgBlF,EAAO8J,UAAU5E,aAAenF,EAAM+J,UAAU5H,OAASlC,EAAO8J,UAAU5H,MAAQnC,EAAM+J,UAAUjF,QAAU7E,EAAO8J,UAAUjF,MAC9Nid,EAAmB/hB,EAAM8I,UAAUvD,KAAOtF,EAAO6I,UAAUvD,IAAMvF,EAAM8I,UAAU3G,OAASlC,EAAO6I,UAAU3G,KACjH,OAAO2f,GAAoBC,GAyEGC,CAAgBnM,EAAUwL,EAASK,cAC3DG,IACFR,EAASK,aAAe7L,GAE1B,MAAMoM,GArGwBhiB,EAqGuCgG,IApG1D,OADYjG,EAqGuBqhB,EAASM,eApG1B,MAAV1hB,GAGR,MAATD,GAA2B,MAAVC,GAGdD,EAAMmF,cAAgBlF,EAAOkF,aAAenF,EAAM8E,QAAU7E,EAAO6E,QAPlD,IAAC9E,EAAOC,EAsG1BgiB,IACFZ,EAASM,aAAe1b,GAE1B,MAAMic,GAhGa,EAACliB,EAAOC,IAChB,MAATD,GAA2B,MAAVC,GAGR,MAATD,GAA2B,MAAVC,GAGdD,EAAMyF,cAAgBxF,EAAOwF,aAAezF,EAAMmF,cAAgBlF,EAAOkF,YAyFlDgd,CAAed,EAASO,YAAapc,GAIjE,GAHI0c,IACFb,EAASO,YAAcpc,IAEpBqc,IAAuBI,IAAuBC,EACjD,OAEF,MAAMzB,EAAO,IACRF,GAAa1K,EAAUwL,EAAS7N,MACnChO,UACAP,YAAagB,GAEfmb,EAAa9a,IAAI,KACfga,GAAY,EAAgB,IAAMH,GAAQe,IAAgBnb,aAAc0a,EAAMC,EAAUta,EAASL,kBA6BnG6W,MA1BY,KACXyE,GAAkH,GAAU,GAC7HD,EAAaxE,SAyBbK,OACAmF,MAnBY,KACZ,IAAKf,EACH,OAEF,MAAM1mB,EAAS,IACV4lB,GAAac,EAASK,aAAcL,EAAS7N,MAChDhO,QAAS,KACTP,YAAa,KACbkB,OAAQ,UAEV8W,EAAKtiB,IAWR,EAGmB0nB,CAAanB,EAAeR,GAC9C,OAAOhB,GAASrB,GAAQvD,IACtB,GAAoB,2BAAhBA,EAAO3Y,KAET,YADAgf,EAAUG,cAAcxG,EAAOC,QAAQtV,YAAaqV,EAAOC,QAAQ7B,cAGrE,GAAoB,oBAAhB4B,EAAO3Y,KAA4B,CACrC,MAAM0T,EAAWiF,EAAOC,QAAQlF,SAIhC,OAHAsL,EAAUK,YAAY3L,EAAUiF,EAAOC,QAAQ7B,cAC/CmF,EAAKvD,QACLqG,EAAUrb,MAAM+P,EAAUiF,EAAOC,QAAQ7B,aAE3C,CACA,GAAoB,kBAAhB4B,EAAO3Y,KAA0B,CACnC,MAAMxH,EAASmgB,EAAOC,QAAQJ,UAAUhgB,OAIxC,OAHAwmB,EAAUvE,QACVyB,EAAKvD,QACLqG,EAAUlE,KAAKtiB,EAEjB,CAEA,GADA0jB,EAAKvD,GACe,UAAhBA,EAAO3Y,KAET,YADAgf,EAAUiB,QAGZ,MAAM7M,EAAQmK,EAAMvB,WACA,aAAhB5I,EAAMW,OACRiL,EAAUnb,OAAOuP,EAAMM,SAAUN,EAAMnL,QAG5C,EAaD,IAAIkY,GAXkC5C,GAASrB,GAAQvD,IACrD,GAAoB,4BAAhBA,EAAO3Y,KAET,YADAkc,EAAKvD,GAGP,MAAMvF,EAAQmK,EAAMvB,WACF,mBAAhB5I,EAAMW,OAAqJ,GAAU,GACvKwJ,EAAMtB,SAASvB,GAAa,CAC1BlC,UAAWpF,EAAMoF,cA8CrB,IAAI4H,GAzCyC7C,IAC3C,IAAItc,EAAS,KACTrC,EAAU,KAWd,OAAOsd,GAAQvD,IAKb,GAJoB,UAAhBA,EAAO3Y,MAAoC,kBAAhB2Y,EAAO3Y,MAA4C,4BAAhB2Y,EAAO3Y,OAVrEpB,IACFQ,qBAAqBR,GACrBA,EAAU,MAERqC,IACFA,IACAA,EAAS,OAOXib,EAAKvD,GACe,iBAAhBA,EAAO3Y,KACT,OAEF,MAAMQ,EAAU,CACdM,UAAW,SACXL,QAAS,CACPwc,SAAS,EACTD,SAAS,EACTqD,MAAM,GAER1hB,GAAI,WAEkB,mBADN4e,EAAMvB,WACVjI,OACRwJ,EAAMtB,SArqBoB,CAClCjc,KAAM,0BACN4Y,QAAS,MAqqBL,GAEFha,EAAUM,sBAAsB,KAC9BN,EAAU,KACVqC,EAASd,EAAW7D,OAAQ,CAACkE,QA6EnC,IAAI8f,GAhBgB/C,GAASrB,GAAQvD,IAEnC,GADAuD,EAAKvD,GACe,2BAAhBA,EAAO3Y,KACT,OAEF,MAAMugB,EAAkBhD,EAAMvB,WACA,iBAA1BuE,EAAgBxM,QAGhBwM,EAAgB7G,WAGpB6D,EAAMtB,SAAStB,GAAO,CACpB3W,OAAQuc,EAAgBvc,YAK5B,MAAMwc,GAED,EAAAC,QACL,IAAIC,GAAc,EAChBC,mBACAC,eACAC,eACA9B,gBACAR,WACAuC,mBACI,WAAAJ,aAAchI,GAAS8H,IAAiB,IAAAO,kBA7qBjCC,EA6qBuDH,EA7qB5C,IAAM3E,GAAQvD,IAChB,oBAAhBA,EAAO3Y,MACTghB,EAAQ9B,WAEU,iBAAhBvG,EAAO3Y,MACTghB,EAAQC,SAAStI,EAAOC,QAAQJ,UAAUhgB,OAAOwL,QAE/B,UAAhB2U,EAAO3Y,MAAoC,kBAAhB2Y,EAAO3Y,MACpCghB,EAAQE,UAEVhF,EAAKvD,KAglBuB,CAACqI,GAAW,IAAM9E,GAAQvD,IAClC,kBAAhBA,EAAO3Y,MAA4C,UAAhB2Y,EAAO3Y,MAAoC,iBAAhB2Y,EAAO3Y,MACvEghB,EAAQG,iBAEVjF,EAAKvD,IA+E4EyI,CAAwBT,GA5tBhG,CAACK,GAAW,EACrBhF,WACAC,cACIC,GAAQvD,IACZ,GAAoB,SAAhBA,EAAO3Y,KAET,YADAkc,EAAKvD,GAGP,MAAM,GACJvV,EAAE,gBACF8M,EAAe,aACf6G,GACE4B,EAAOC,QACLtgB,EAAU0jB,IACM,mBAAlB1jB,EAAQyb,OACVkI,EAASvB,GAAa,CACpBlC,UAAWlgB,EAAQkgB,aAGA,SAArBwD,IAAWjI,OAAmH,GAAU,GAC1IkI,EAASxB,MACTwB,EA7ImC,CACnCjc,KAAM,yBACN4Y,QA2I8B,CAC5BtV,YAAaF,EACb2T,kBAEF,MAGMsK,EAAU,CACd/d,YAAaF,EACbke,cALoB,CACpBC,yBAA2C,SAAjBxK,KAMtB,SACJrD,EAAQ,WACRH,EAAU,SACVjJ,GACE0W,EAAQQ,gBAAgBH,GAE5BpF,EAtJ6B,CAC7Bjc,KAAM,kBACN4Y,QAoJwB,CACtBlF,WACAH,aACArD,kBACA6G,eACAzM,eAirB0HmX,CAAKd,GAAmB7F,GAAMqF,GAAqBC,GAA4BE,GA7C5L,CAACQ,GAAgBvD,GAASrB,GAAQvD,IACjD,GAFiBA,IAA0B,kBAAhBA,EAAO3Y,MAA4C,iBAAhB2Y,EAAO3Y,MAA2C,UAAhB2Y,EAAO3Y,KAEnG0hB,CAAW/I,GAGb,OAFAmI,EAAazD,YACbnB,EAAKvD,GAGP,GAAoB,oBAAhBA,EAAO3Y,KAA4B,CACrCkc,EAAKvD,GACL,MAAMvF,EAAQmK,EAAMvB,WAGpB,MAFkB,aAAhB5I,EAAMW,OAA0I,GAAU,QAC5J+M,EAAand,MAAMyP,EAErB,CACA8I,EAAKvD,GACLmI,EAAazkB,OAAOkhB,EAAMvB,aA+BgM2F,CAAWb,GAAexD,GA5E1O,CAAC0D,IACX,IAAIY,GAAa,EACjB,MAAO,IAAM1F,GAAQvD,IACnB,GAAoB,oBAAhBA,EAAO3Y,KAKT,OAJA4hB,GAAa,EACbZ,EAAQa,eAAelJ,EAAOC,QAAQlF,SAAS9L,UAAUxE,IACzD8Y,EAAKvD,QACLqI,EAAQc,0BAIV,GADA5F,EAAKvD,GACAiJ,EAAL,CAGA,GAAoB,UAAhBjJ,EAAO3Y,KAGT,OAFA4hB,GAAa,OACbZ,EAAQc,0BAGV,GAAoB,kBAAhBnJ,EAAO3Y,KAA0B,CACnC4hB,GAAa,EACb,MAAMppB,EAASmgB,EAAOC,QAAQJ,UAAUhgB,OACpCA,EAAO6K,SACT2d,EAAQe,eAAevpB,EAAO8K,YAAa9K,EAAO6K,QAAQC,aAE5D0d,EAAQc,yBACV,CAbA,EAeH,EAgDuQ,CAAMlB,GAAe9B,GAAWC,EAAeR,MA7qB3S,IAACyC,CA6qBsT,EA8EnU,IAAIgB,GAAe,EACjBC,eACAC,cACAloB,SACAD,YAEA,MAAM8X,EAAYvN,EAAS,CACzBrK,EAAGioB,EACHhoB,EAAG+nB,GACF,CACDhoB,EAAGF,EACHG,EAAGF,IAML,MAJ0B,CACxBC,EAAG6K,KAAKY,IAAI,EAAGmM,EAAU5X,GACzBC,EAAG4K,KAAKY,IAAI,EAAGmM,EAAU3X,GAG5B,EAEGioB,GAAqB,KACvB,MAAMC,EAAMlF,SAASmF,gBAErB,OADCD,GAAyG,GAAU,GAC7GA,CACR,EAEGE,GAAqB,KACvB,MAAMF,EAAMD,KAOZ,OANkBH,GAAa,CAC7BC,aAAcG,EAAIH,aAClBC,YAAaE,EAAIF,YACjBnoB,MAAOqoB,EAAIG,YACXvoB,OAAQooB,EAAII,cAGf,EAiCGC,GAAoB,EACtB/O,WACA4N,gBACAoB,eAEA/e,KACA,MAAM2G,EArCU,MAChB,MAAMjO,EAAS,KACTwV,EAAYyQ,KACZ3oB,EAAM0C,EAAOnC,EACbJ,EAAOuC,EAAOpC,EACdmoB,EAAMD,KACNpoB,EAAQqoB,EAAIG,YACZvoB,EAASooB,EAAII,aAqBnB,MAZiB,CACfjd,MAPY9L,EAAQ,CACpBE,MACAG,OACAF,MALYE,EAAOC,EAMnBF,OALaF,EAAMK,IASnBqC,OAAQ,CACN/D,QAAS+D,EACTxD,QAASwD,EACTqJ,IAAKmM,EACL3L,KAAM,CACJ1K,MAAO0I,EACPiC,aAAcjC,IAKrB,EAQkBye,GACXC,EAAetY,EAASjO,OAAOxD,QAC/B4a,EAAOC,EAAS/M,UAChBM,EAAayb,EAAS/b,UAAUkc,aAAapP,EAAKzT,MAAMO,IAAIud,GAASA,EAAMrc,UAAUqhB,2BAA2BF,EAActB,IAC9Hha,EAAaob,EAAS9a,UAAUib,aAAanP,EAAS9L,UAAU5H,MAAMO,IAAIud,GAASA,EAAMiF,aAAaH,IACtGrP,EAAa,CACjBjM,WAAYD,GAAeC,GAC3BL,WAAYD,GAAeC,IAE7BqQ,KAMA,MALe,CACb/D,aACAG,WACApJ,WAGH,EAED,SAAS0Y,GAAoBN,EAAUxD,EAAUpB,GAC/C,GAAIA,EAAM1W,WAAWhE,KAAO8b,EAAS9b,GACnC,OAAO,EAET,GAAI0a,EAAM1W,WAAWpH,OAASkf,EAASlf,KACrC,OAAO,EAGT,MAA6B,YADhB0iB,EAAS/b,UAAUsc,QAAQnF,EAAM1W,WAAWpE,aAChDoE,WAAWiK,IAUtB,CACA,IAAI6R,GAAyB,CAAER,EAAUjhB,KACvC,IAAI0hB,EAAa,KACjB,MAAMnE,EAxLR,UAAyB,SACvB0D,EAAQ,UACRjhB,IAEA,IAAI2hB,EATgB,CACpB5L,UAAW,CAAC,EACZ8B,SAAU,CAAC,EACXH,SAAU,CAAC,GAOPva,EAAU,KACd,MAAMykB,EAAU,KACVzkB,IAGJ6C,EAAUuY,qBACVpb,EAAUM,sBAAsB,KAC9BN,EAAU,KACV+E,KACA,MAAM,UACJ6T,EAAS,SACT8B,EAAQ,SACRH,GACEiK,EACEzR,EAAQrS,OAAOgkB,KAAK9L,GAAWjX,IAAI6C,GAAMsf,EAAS9a,UAAUqb,QAAQ7f,GAAI2f,aAAa7e,IAAS2D,KAAK,CAACC,EAAGC,IAAMD,EAAEV,WAAWzE,MAAQoF,EAAEX,WAAWzE,OAC/IiT,EAAUtW,OAAOgkB,KAAKnK,GAAU5Y,IAAI6C,IAGjC,CACLJ,YAAaI,EACb/G,OAJYqmB,EAAS/b,UAAUsc,QAAQ7f,GACpB3B,UAAU8hB,4BAM3B/qB,EAAS,CACbgf,UAAW7F,EACX2H,SAAUha,OAAOgkB,KAAKhK,GACtBH,SAAUvD,GAEZwN,EAtCgB,CACpB5L,UAAW,CAAC,EACZ8B,SAAU,CAAC,EACXH,SAAU,CAAC,GAoCP7B,KACA7V,EAAU+hB,QAAQhrB,OA6BtB,MAAO,CACL2L,IA3BU2Z,IACV,MAAM1a,EAAK0a,EAAM1W,WAAWhE,GAC5BggB,EAAQ5L,UAAUpU,GAAM0a,EACxBsF,EAAQjK,SAAS2E,EAAM1W,WAAWpE,cAAe,EAC7CogB,EAAQ9J,SAASlW,WACZggB,EAAQ9J,SAASlW,GAE1BigB,KAqBAhb,OAnBayV,IACb,MAAM1W,EAAa0W,EAAM1W,WACzBgc,EAAQ9J,SAASlS,EAAWhE,KAAM,EAClCggB,EAAQjK,SAAS/R,EAAWpE,cAAe,EACvCogB,EAAQ5L,UAAUpQ,EAAWhE,YACxBggB,EAAQ5L,UAAUpQ,EAAWhE,IAEtCigB,KAaAhG,KAXW,KACNze,IAGLQ,qBAAqBR,GACrBA,EAAU,KACVwkB,EAnEkB,CACpB5L,UAAW,CAAC,EACZ8B,SAAU,CAAC,EACXH,SAAU,CAAC,KAuEb,CAmHoBsK,CAAgB,CAChChiB,UAAW,CACT+hB,QAAS/hB,EAAUsY,qBACnBC,mBAAoBvY,EAAUuY,oBAEhC0I,aAgDIgB,EAAa/hB,IAChBwhB,GAAoI,GAAU,GAC/I,MAAMjE,EAAWiE,EAAWzP,SAAS9L,UAClB,aAAfjG,EAAM3B,MACJgjB,GAAoBN,EAAUxD,EAAUvd,EAAMnG,QAChDwjB,EAAU7a,IAAIxC,EAAMnG,OAGL,YAAfmG,EAAM3B,MACJgjB,GAAoBN,EAAUxD,EAAUvd,EAAMnG,QAChDwjB,EAAU3W,OAAO1G,EAAMnG,QAuBvBwlB,EAAU,CACd9G,yBAhF+B,CAAC9W,EAAIsM,KACnCgT,EAAS/b,UAAUgd,OAAOvgB,IAA6I,GAAU,GAC7K+f,GAGL1hB,EAAUyY,yBAAyB,CACjC9W,KACAsM,eA0EFyK,gCAvEsC,CAAC/W,EAAI2J,KACtCoW,IAGJT,EAAS/b,UAAUgd,OAAOvgB,IAAmJ,GAAU,GACxL3B,EAAU0Y,gCAAgC,CACxC/W,KACA2J,uBAiEFrG,gBApDsB,CAACtD,EAAInH,KACtBknB,GAGLT,EAAS/b,UAAUsc,QAAQ7f,GAAI3B,UAAUpF,OAAOJ,IAiDhDge,sBA/D4B,CAAC7W,EAAIwD,KAC5Buc,IAGJT,EAAS/b,UAAUgd,OAAOvgB,IAAwI,GAAU,GAC7K3B,EAAUwY,sBAAsB,CAC9B7W,KACAwD,gBAyDF4a,gBAxBsBH,IACpB8B,GAAuJ,GAAU,GACnK,MAAMrF,EAAQ4E,EAAS9a,UAAUqb,QAAQ5B,EAAQ/d,aAC3CmQ,EAAOiP,EAAS/b,UAAUsc,QAAQnF,EAAM1W,WAAWpE,aACnD0Q,EAAW,CACf9L,UAAWkW,EAAM1W,WACjBT,UAAW8M,EAAKrM,YAEZwc,EAAclB,EAASmB,UAAUH,GAKvC,OAJAP,EAAa,CACXzP,WACAkQ,eAEKnB,GAAkB,CACvB/O,WACAgP,WACApB,cAAeD,EAAQC,iBASzBH,eAjDqB,KACrB,IAAKgC,EACH,OAEFnE,EAAU3B,OACV,MAAM5J,EAAO0P,EAAWzP,SAAS/M,UACjC+b,EAAS/b,UAAUkc,aAAapP,EAAKzT,MAAMgB,QAAQ8c,GAASA,EAAMrc,UAAUqiB,eAC5EX,EAAWS,cACXT,EAAa,OA2Cf,OAAOnC,CACR,EAEG+C,GAAe,CAAE3Q,EAAOhQ,IACN,SAAhBgQ,EAAMW,OAGU,mBAAhBX,EAAMW,QAGNX,EAAMoF,UAAUhgB,OAAO8K,cAAgBF,GAGF,SAAlCgQ,EAAMoF,UAAUhgB,OAAOwL,QAG5BggB,GAAgB/nB,IAClBK,OAAO2nB,SAAShoB,EAAOhC,EAAGgC,EAAO/B,EAClC,EAED,MAAMgqB,GAA0BnmB,EAAWkJ,GAAcM,GAAgBN,GAAYU,OAAOhB,KACrFA,EAAU+I,aAGV/I,EAAUpB,QAYjB,IAAI4e,GAA6B,EAC/BhqB,SACA2I,cACAmE,iBAEA,GAAInE,EAAa,CACf,MAAM2O,EAAYxK,EAAWnE,GAC7B,OAAK2O,EAAUlM,MAGRkM,EAFE,IAGX,CACA,MAAMA,EAnB2B,EAACpX,EAAQ4M,KAC1C,MAAMmd,EAAQF,GAAwBjd,GAAY8E,KAAKpF,IACpDA,EAAUpB,OAAqF,GAAU,GACnGyO,GAAkBrN,EAAUpB,MAAMK,cAAlCoO,CAAiD3Z,MACpD,KACN,OAAO+pB,GAcWC,CAA2BlqB,EAAQ8M,GACrD,OAAOwK,CACR,EAED,MAAM6S,GAA6B,CACjCC,oBAAqB,IACrBC,sBAAuB,IACvBC,eAAgB,GAChBC,KAAMC,GAAcA,GAAc,EAClCC,kBAAmB,CACjBC,gBAAiB,KACjBC,aAAc,KAEhBC,UAAU,GAGZ,IAWIC,GAAgB,EAClBC,eACAC,aACArsB,cAEA,MAAMssB,EAAQD,EAAaD,EAC3B,GAAc,IAAVE,EAMF,OAAO,EAIT,OAFuBtsB,EAAUosB,GACGE,CAErC,EAgDGC,GAAW,EACbC,iBACAC,aACAC,gBACAC,yBACAC,6BAEA,MAAMppB,EAnDmB,EAAEgpB,EAAgBC,EAAYG,EAAyB,IAAMnB,MACtF,MAAMoB,EAAsBD,IAC5B,GAAIJ,EAAiBC,EAAWK,mBAC9B,OAAO,EAET,GAAIN,GAAkBC,EAAWM,iBAC/B,OAAOF,EAAoBjB,eAE7B,GAAIY,IAAmBC,EAAWK,mBAChC,OAXY,EAad,MAKME,EAAmC,EALFb,GAAc,CACnDC,aAAcK,EAAWM,iBACzBV,WAAYI,EAAWK,mBACvB9sB,QAASwsB,IAGLhpB,EAASqpB,EAAoBjB,eAAiBiB,EAAoBhB,KAAKmB,GAC7E,OAAO/gB,KAAKghB,KAAKzpB,EAClB,EAgCgB0pB,CAAqBV,EAAgBC,EAAYG,GAChE,OAAe,IAAXppB,EACK,EAEJmpB,EAGE1gB,KAAKY,IArCU,EAAEsgB,EAAgBT,EAAeE,KACvD,MAAMC,EAAsBD,IACtBX,EAAeY,EAAoBd,kBAAkBE,aACrDmB,EAASP,EAAoBd,kBAAkBC,gBAC/CI,EAAeM,EACfL,EAAae,EAEbC,EADMC,KAAKrQ,MACKmP,EACtB,GAAIiB,GAAWD,EACb,OAAOD,EAET,GAAIE,EAAUpB,EACZ,OAnCY,EAqCd,MAAMsB,EAAyCpB,GAAc,CAC3DC,aAAcH,EACdI,aACArsB,QAASqtB,IAEL7pB,EAAS2pB,EAAiBN,EAAoBhB,KAAK0B,GACzD,OAAOthB,KAAKghB,KAAKzpB,EAClB,EAgBiBgqB,CAAkBhqB,EAAQkpB,EAAeE,GA5D3C,GA0DLppB,CAGV,EAEGiqB,GAAkB,EACpBC,YACAC,kBACAjB,gBACAvf,OACAwf,yBACAC,6BAEA,MAAMH,EArGoB,EAAEiB,EAAWvgB,EAAMyf,EAAyB,IAAMnB,MAC5E,MAAMoB,EAAsBD,IAO5B,MAJmB,CACjBE,mBAHyBY,EAAUvgB,EAAK+D,MAAQ2b,EAAoBnB,oBAIpEqB,iBAHuBW,EAAUvgB,EAAK+D,MAAQ2b,EAAoBlB,sBAMrE,EA4FoBiC,CAAsBF,EAAWvgB,EAAMyf,GAE1D,OADsBe,EAAgBxgB,EAAKO,KAAOigB,EAAgBxgB,EAAKrC,OAE9DyhB,GAAS,CACdC,eAAgBmB,EAAgBxgB,EAAKO,KACrC+e,aACAC,gBACAC,yBACAC,4BAGI,EAAIL,GAAS,CACnBC,eAAgBmB,EAAgBxgB,EAAKrC,OACrC2hB,aACAC,gBACAC,yBACAC,0BAEH,EAqBD,MAAMiB,GAAQjoB,EAAMjD,GAAmB,IAAVA,EAAc,EAAIA,GAC/C,IAAImrB,GAAc,EAChBpB,gBACAgB,YACA9gB,UACAtL,SACAqrB,yBACAC,6BAEA,MAAMe,EAAkB,CACtB7sB,IAAKQ,EAAOD,EAAIqsB,EAAU5sB,IAC1BC,MAAO2sB,EAAU3sB,MAAQO,EAAOF,EAChCJ,OAAQ0sB,EAAU1sB,OAASM,EAAOD,EAClCJ,KAAMK,EAAOF,EAAIssB,EAAUzsB,MAEvBI,EAAIosB,GAAgB,CACxBC,YACAC,kBACAjB,gBACAvf,KAAM4D,GACN4b,yBACAC,2BAEIxrB,EAAIqsB,GAAgB,CACxBC,YACAC,kBACAjB,gBACAvf,KAAMmE,GACNqb,yBACAC,2BAEImB,EAAWF,GAAM,CACrBzsB,IACAC,MAEF,GAAIqK,EAAUqiB,EAAU1iB,GACtB,OAAO,KAET,MAAM2iB,EAzDkB,GACxBN,YACA9gB,UACAugB,qBAEA,MAAMc,EAAqBrhB,EAAQzL,OAASusB,EAAUvsB,OAChD+sB,EAAuBthB,EAAQ1L,MAAQwsB,EAAUxsB,MACvD,OAAKgtB,GAAyBD,EAG1BC,GAAwBD,EACnB,KAEF,CACL7sB,EAAG8sB,EAAuB,EAAIf,EAAe/rB,EAC7CC,EAAG4sB,EAAqB,EAAId,EAAe9rB,GAPpC8rB,CASV,EAwCiBgB,CAAoB,CAClCT,YACA9gB,UACAugB,eAAgBY,IAElB,OAAKC,EAGEtiB,EAAUsiB,EAAS3iB,GAAU,KAAO2iB,EAFlC,IAGV,EAED,MAAMI,GAAiBxoB,EAAMjD,GACb,IAAVA,EACK,EAEFA,EAAQ,EAAI,GAAK,GAEpB0rB,GAAa,MACjB,MAAMC,EAAe,CAAC9sB,EAAQqL,IACxBrL,EAAS,EACJA,EAELA,EAASqL,EACJrL,EAASqL,EAEX,EAET,MAAO,EACL7M,UACA6M,MACAzJ,aAEA,MAAMmrB,EAAejjB,EAAItL,EAASoD,GAC5BorB,EAAU,CACdptB,EAAGktB,EAAaC,EAAantB,EAAGyL,EAAIzL,GACpCC,EAAGitB,EAAaC,EAAaltB,EAAGwL,EAAIxL,IAEtC,OAAIqK,EAAU8iB,EAASnjB,GACd,KAEFmjB,EAEV,EAzBkB,GA0BbC,GAAqB,EACzB5hB,IAAK6hB,EACL1uB,UACAoD,aAEA,MAAMyJ,EAAM,CACVzL,EAAG6K,KAAKY,IAAI7M,EAAQoB,EAAGstB,EAAOttB,GAC9BC,EAAG4K,KAAKY,IAAI7M,EAAQqB,EAAGqtB,EAAOrtB,IAE1BstB,EAAiBP,GAAehrB,GAChCorB,EAAUH,GAAW,CACzBxhB,MACA7M,UACAoD,OAAQurB,IAEV,OAAKH,IAGoB,IAArBG,EAAevtB,GAAyB,IAAdotB,EAAQptB,GAGb,IAArButB,EAAettB,GAAyB,IAAdmtB,EAAQntB,IAKlCutB,GAAkB,CAACnd,EAAUrO,IAAWqrB,GAAmB,CAC/DzuB,QAASyR,EAASjO,OAAOxD,QACzB6M,IAAK4E,EAASjO,OAAOqJ,IACrBzJ,WAcIyrB,GAAqB,CAAC/gB,EAAW1K,KACrC,MAAMsJ,EAAQoB,EAAUpB,MACxB,QAAKA,GAGE+hB,GAAmB,CACxBzuB,QAAS0M,EAAMlJ,OAAOxD,QACtB6M,IAAKH,EAAMlJ,OAAOqJ,IAClBzJ,YAkBJ,IA0CI,GAAS,EACXmX,QACAmS,gBACAC,yBACAxB,eACAtd,kBACA+e,6BAEA,MAAMtrB,EAASiZ,EAAMva,QAAQiN,KAAK8N,gBAE5BnO,EADY2N,EAAMG,WAAWjM,WAAW8L,EAAMM,SAAS9L,UAAUxE,IAC7C0C,KAAK3K,UAC/B,GAAIiY,EAAMyF,sBAAuB,CAC/B,MACM5c,EAvDkB,GAC1BqO,WACA7E,UACAtL,SACAorB,gBACAC,yBACAC,6BAEA,MAAMppB,EAASsqB,GAAY,CACzBpB,gBACAgB,UAAWjc,EAAS/E,MACpBE,UACAtL,SACAqrB,yBACAC,2BAEF,OAAOppB,GAAUorB,GAAgBnd,EAAUjO,GAAUA,EAAS,IAC/D,EAsCkBsrB,CAAsB,CACnCpC,gBACAjb,SAHe8I,EAAM9I,SAIrB7E,UACAtL,SACAqrB,yBACAC,2BAEF,GAAIxpB,EAEF,YADA+nB,EAAa/nB,EAGjB,CACA,MAAM0K,EAAYwd,GAA2B,CAC3ChqB,SACA2I,YAAaoQ,GAAkBE,EAAMnL,QACrChB,WAAYmM,EAAMG,WAAWtM,aAE/B,IAAKN,EACH,OAEF,MAAM1K,EAzDuB,GAC7B0K,YACAlB,UACAtL,SACAorB,gBACAC,yBACAC,6BAEA,MAAMlgB,EAAQoB,EAAUpB,MACxB,IAAKA,EACH,OAAO,KAET,MAAMlJ,EAASsqB,GAAY,CACzBpB,gBACAgB,UAAWhhB,EAAMK,cACjBH,UACAtL,SACAqrB,yBACAC,2BAEF,OAAOppB,GAAUqrB,GAAmB/gB,EAAWtK,GAAUA,EAAS,IACnE,EAoCgBurB,CAAyB,CACtCrC,gBACA5e,YACAlB,UACAtL,SACAqrB,yBACAC,2BAEExpB,GACFyK,EAAgBC,EAAUS,WAAWhE,GAAInH,EAE5C,EAiEG4rB,GAAqB,EACvBzN,OACA1T,kBACAsd,mBAEA,MAMM8D,EAA+B,CAACnhB,EAAW1K,KAC/C,IAAKyrB,GAAmB/gB,EAAW1K,GACjC,OAAOA,EAET,MAAMorB,EAtLkB,EAAC1gB,EAAW1K,KACtC,MAAMsJ,EAAQoB,EAAUpB,MACxB,OAAKA,GAGAmiB,GAAmB/gB,EAAW1K,GAG5BirB,GAAW,CAChBruB,QAAS0M,EAAMlJ,OAAOxD,QACtB6M,IAAKH,EAAMlJ,OAAOqJ,IAClBzJ,WARO,MAmLS8rB,CAAoBphB,EAAW1K,GAC/C,IAAKorB,EAEH,OADA3gB,EAAgBC,EAAUS,WAAWhE,GAAInH,GAClC,KAET,MAAM+rB,EAA4B1jB,EAASrI,EAAQorB,GACnD3gB,EAAgBC,EAAUS,WAAWhE,GAAI4kB,GAEzC,OADkB1jB,EAASrI,EAAQ+rB,IAG/BC,EAA4B,CAACpP,EAAuBvO,EAAUrO,KAClE,IAAK4c,EACH,OAAO5c,EAET,IAAKwrB,GAAgBnd,EAAUrO,GAC7B,OAAOA,EAET,MAAMorB,EA9Ne,EAAC/c,EAAUrO,KAClC,IAAKwrB,GAAgBnd,EAAUrO,GAC7B,OAAO,KAET,MAAMyJ,EAAM4E,EAASjO,OAAOqJ,IACtB7M,EAAUyR,EAASjO,OAAOxD,QAChC,OAAOquB,GAAW,CAChBruB,UACA6M,MACAzJ,YAqNgBisB,CAAiB5d,EAAUrO,GAC3C,IAAKorB,EAEH,OADArD,EAAa/nB,GACN,KAET,MAAMksB,EAAyB7jB,EAASrI,EAAQorB,GAChDrD,EAAamE,GAEb,OADkB7jB,EAASrI,EAAQksB,IAqBrC,OAlBqB/U,IACnB,MAAMiO,EAAUjO,EAAMjD,kBACtB,IAAKkR,EACH,OAEF,MAAMve,EAAcoQ,GAAkBE,EAAMnL,QAC3CnF,GAAsI,GAAU,GACjJ,MAAMslB,EAAqBN,EAA6B1U,EAAMG,WAAWtM,WAAWnE,GAAcue,GAClG,IAAK+G,EACH,OAEF,MAAM9d,EAAW8I,EAAM9I,SACjB+d,EAAkBJ,EAA0B7U,EAAMyF,sBAAuBvO,EAAU8d,GACpFC,GAlDc,EAACjV,EAAOrX,KAC3B,MAAMkT,EAAS9K,EAAIiP,EAAMva,QAAQoW,OAAO4E,UAAW9X,GACnDqe,EAAK,CACHnL,YAkDFqZ,CAAalV,EAAOiV,GAGvB,EAEGE,GAAqB,EACvB7hB,kBACAsd,eACA5J,OACAqL,6BAEA,MAAM+C,EApIkB,GACxBxE,eACAtd,kBACA+e,yBAAyB,IAAMnB,OAE/B,MAAMmE,EAAuB,EAAQzE,GAC/B0E,EAA0B,EAAQhiB,GACxC,IAAIwY,EAAW,KACf,MAAMyJ,EAAYvV,IACf8L,GAA6G,GAAU,GACxH,MAAM,uBACJsG,EAAsB,cACtBD,GACErG,EACJ,GAAO,CACL9L,QACA4Q,aAAcyE,EACd/hB,gBAAiBgiB,EACjBnD,gBACAC,yBACAC,4BAoCJ,MAAO,CACL9hB,MAlCcyP,IACdzP,KACEub,GAA0H,GAAU,GACtI,MAAMqG,EAAgBY,KAAKrQ,MAC3B,IAAI8S,GAAkB,EACtB,MAAMC,EAAqB,KACzBD,GAAkB,GAEpB,GAAO,CACLxV,QACAmS,cAAe,EACfC,wBAAwB,EACxBxB,aAAc6E,EACdniB,gBAAiBmiB,EACjBpD,2BAEFvG,EAAW,CACTqG,gBACAC,uBAAwBoD,GAE1BtR,KACIsR,GACFD,EAAUvV,IAaZiK,KAVW,KACN6B,IAGLuJ,EAAqBtpB,SACrBupB,EAAwBvpB,SACxB+f,EAAW,OAKX7iB,OAAQssB,EAEX,EAuEuBG,CAAoB,CACxC9E,eACAtd,kBACA+e,2BAEIsD,EAAalB,GAAmB,CACpCzN,OACA4J,eACAtd,oBAqBF,MALiB,CACfrK,OAfa+W,IACeqS,IACJV,UAA4B,aAAhB3R,EAAMW,QAGf,UAAvBX,EAAM2D,aAIL3D,EAAMjD,mBAGX4Y,EAAW3V,GANToV,EAAcnsB,OAAO+W,KAUvBzP,MAAO6kB,EAAc7kB,MACrB0Z,KAAMmL,EAAcnL,KAGvB,EAED,MAAM,GAAS,WACT2L,GAAa,MACjB,MAAMC,EAAO,GAAG,iBAChB,MAAO,CACLA,OACA3lB,YAAa,GAAG2lB,iBAChBC,UAAW,GAAGD,eAEjB,EAPkB,GAQbrhB,GAAY,MAChB,MAAMqhB,EAAO,GAAG,eAChB,MAAO,CACLA,OACAC,UAAW,GAAGD,eACd7lB,GAAI,GAAG6lB,OAEV,EAPiB,GAQZtiB,GAAY,MAChB,MAAMsiB,EAAO,GAAG,eAChB,MAAO,CACLA,OACAC,UAAW,GAAGD,eACd7lB,GAAI,GAAG6lB,OAEV,EAPiB,GAQZE,GAAkB,CACtBD,UAAW,GAAG,kCAIVE,GAAY,CAACC,EAAOC,IAAaD,EAAM9oB,IAAIgpB,IAC/C,MAAM/tB,EAAQ+tB,EAAK7sB,OAAO4sB,GAC1B,OAAK9tB,EAGE,GAAG+tB,EAAKC,cAAchuB,MAFpB,KAGRiuB,KAAK,KAER,IAAIC,GAAeR,IACjB,MAAMS,GAVgBC,EAUcV,EAVHW,GAAa,IAAIA,MAAcD,OAA1CA,MAWtB,MAAME,EAAe,MACnB,MAAMC,EAAa,2DAInB,MAAO,CACLP,SAAUG,EAAYX,GAAWE,WACjCxsB,OAAQ,CACNstB,OAAQ,mJAKR9I,QAAS6I,EACT7K,SAjBgB,wBAkBhB+K,cAAeF,GAGpB,EAlBoB,GAoDfV,EAAQ,CAjCM,MAClB,MAAMa,EAAa,uBACH7O,GAAYJ,qBAE5B,MAAO,CACLuO,SAAUG,EAAY/hB,GAAUshB,WAChCxsB,OAAQ,CACNwiB,SAAUgL,EACVD,cAAeC,EACfC,WAAYD,GAGjB,EAZmB,GAiCQJ,EApBR,CAClBN,SAAUG,EAAYhjB,GAAUuiB,WAChCxsB,OAAQ,CACNstB,OAAQ,2BAGC,CACXR,SAAU,OACV9sB,OAAQ,CACNwiB,SAAU,6OAYd,MAAO,CACL8K,OAAQZ,GAAUC,EAAO,UACzBnI,QAASkI,GAAUC,EAAO,WAC1BnK,SAAUkK,GAAUC,EAAO,YAC3BY,cAAeb,GAAUC,EAAO,iBAChCc,WAAYf,GAAUC,EAAO,cAEhC,EAGD,IAAIe,GADgD,oBAAX9tB,aAAqD,IAApBA,OAAO4gB,eAAqE,IAAlC5gB,OAAO4gB,SAASmN,cAAgC,EAAAD,gBAAoB,EAAAhxB,UAGxL,MAAMkxB,GAAU,KACd,MAAMC,EAAOrN,SAASsN,cAAc,QAEpC,OADCD,GAA+G,GAAU,GACnHA,GAEHE,GAAgBC,IACpB,MAAMltB,EAAK0f,SAASmN,cAAc,SAKlC,OAJIK,GACFltB,EAAGmtB,aAAa,QAASD,GAE3BltB,EAAGwC,KAAO,WACHxC,GA6DT,SAASotB,GAAiBC,EAAYrB,GACpC,OAAOxqB,MAAM8rB,KAAKD,EAAWD,iBAAiBpB,GAChD,CAEA,IAAIuB,GAAmBvtB,GACjBA,GAAMA,EAAGwtB,eAAiBxtB,EAAGwtB,cAAcC,YACtCztB,EAAGwtB,cAAcC,YAEnB3uB,OAGT,SAAS4uB,GAAc1tB,GACrB,OAAOA,aAAcutB,GAAgBvtB,GAAI2tB,WAC3C,CAEA,SAASC,GAAelC,EAAW5lB,GACjC,MAAMkmB,EAAW,IAAIR,GAAWE,cAAcA,MACxCmC,EAAWT,GAAiB1N,SAAUsM,GAC5C,IAAK6B,EAASpyB,OAEZ,OAAO,KAET,MAAMqyB,EAASD,EAAStf,KAAKvO,GACpBA,EAAG+tB,aAAavC,GAAW1lB,eAAiBA,GAErD,OAAKgoB,GAIAJ,GAAcI,GAIZA,EANE,IAOX,CA6EA,SAASE,KACP,MAAM7N,EAAU,CACdrW,WAAY,CAAC,EACbL,WAAY,CAAC,GAETwkB,EAAc,GAWpB,SAASC,EAAO/pB,GACV8pB,EAAYxyB,QACdwyB,EAAYzqB,QAAQ2qB,GAAMA,EAAGhqB,GAEjC,CACA,SAASiqB,EAAkBxoB,GACzB,OAAOua,EAAQrW,WAAWlE,IAAO,IACnC,CA+CA,SAASyoB,EAAkBzoB,GACzB,OAAOua,EAAQ1W,WAAW7D,IAAO,IACnC,CA8BA,MAAO,CACLwE,UA1EmB,CACnBkkB,SAAUhO,IACRH,EAAQrW,WAAWwW,EAAM1W,WAAWhE,IAAM0a,EAC1C4N,EAAO,CACL1rB,KAAM,WACNxE,MAAOsiB,KAGXja,OAAQ,CAACia,EAAO7S,KACd,MAAMpS,EAAU8kB,EAAQrW,WAAW2D,EAAK7D,WAAWhE,IAC9CvK,GAGDA,EAAQkzB,WAAajO,EAAMiO,kBAGxBpO,EAAQrW,WAAW2D,EAAK7D,WAAWhE,IAC1Cua,EAAQrW,WAAWwW,EAAM1W,WAAWhE,IAAM0a,IAE5CkO,WAAYlO,IACV,MAAMxa,EAAcwa,EAAM1W,WAAWhE,GAC/BvK,EAAU+yB,EAAkBtoB,GAC7BzK,GAGDilB,EAAMiO,WAAalzB,EAAQkzB,kBAGxBpO,EAAQrW,WAAWhE,GACtBqa,EAAQ1W,WAAW6W,EAAM1W,WAAWpE,cACtC0oB,EAAO,CACL1rB,KAAM,UACNxE,MAAOsiB,MAIbmF,QAzCF,SAA0B7f,GACxB,MAAM0a,EAAQ8N,EAAkBxoB,GAEhC,OADC0a,GAAkH,GAAU,GACtHA,CACT,EAsCEmO,SAAUL,EACVjI,OAAQvgB,GAAMtK,QAAQ8yB,EAAkBxoB,IACxCyf,aAAc7iB,GAAQV,OAAOkI,OAAOmW,EAAQrW,YAAYK,OAAOmW,GAASA,EAAM1W,WAAWpH,OAASA,IAoClG2G,UA1BmB,CACnBmlB,SAAUhO,IACRH,EAAQ1W,WAAW6W,EAAM1W,WAAWhE,IAAM0a,GAE5CkO,WAAYlO,IACV,MAAMjlB,EAAUgzB,EAAkB/N,EAAM1W,WAAWhE,IAC9CvK,GAGDilB,EAAMiO,WAAalzB,EAAQkzB,iBAGxBpO,EAAQ1W,WAAW6W,EAAM1W,WAAWhE,KAE7C6f,QAnBF,SAA0B7f,GACxB,MAAM0a,EAAQ+N,EAAkBzoB,GAEhC,OADC0a,GAAkH,GAAU,GACtHA,CACT,EAgBEmO,SAAUJ,EACVlI,OAAQvgB,GAAMtK,QAAQ+yB,EAAkBzoB,IACxCyf,aAAc7iB,GAAQV,OAAOkI,OAAOmW,EAAQ1W,YAAYU,OAAOmW,GAASA,EAAM1W,WAAWpH,OAASA,IAUlG6jB,UAnGF,SAAmB8H,GAEjB,OADAF,EAAYrgB,KAAKugB,GACV,WACL,MAAMhpB,EAAQ8oB,EAAYvf,QAAQyf,IACnB,IAAXhpB,GAGJ8oB,EAAY1N,OAAOpb,EAAO,EAC5B,CACF,EA2FE+jB,MATF,WACE/I,EAAQrW,WAAa,CAAC,EACtBqW,EAAQ1W,WAAa,CAAC,EACtBwkB,EAAYxyB,OAAS,CACvB,EAOF,CAgBA,IAAIizB,GAAe,kBAAoB,MAEnCC,GAAiB,KACnB,MAAMC,EAAOlP,SAASkP,KAEtB,OADCA,GAA+F,GAAU,GACnGA,CACR,EAaD,IAAIC,GAXmB,CACrB3pB,SAAU,WACV3I,MAAO,MACPC,OAAQ,MACRc,OAAQ,OACRE,OAAQ,IACRE,QAAS,IACToxB,SAAU,SACVhnB,KAAM,gBACN,YAAa,eAIf,MAAMinB,GAAQrD,GAAa,oBAAoBA,IA4C/C,IAAIsD,GAAU,EACd,MAAMC,GAAW,CACfC,UAAW,MAYb,IAAIC,GAAgB,cAJpB,SAAqBnzB,EAAQiH,EAAUgsB,IACrC,MAAMrpB,EAAK,YACX,OAAO/J,EAAQ,IAAM,GAAGG,IAASiH,EAAQisB,YAAYtpB,IAAM,CAAC3C,EAAQisB,UAAWlzB,EAAQ4J,GACzF,EANA,SAA+B5J,EAAQiH,EAAUgsB,IAC/C,OAAOpzB,EAAQ,IAAM,GAAGG,IAASiH,EAAQisB,YAAYF,OAAa,CAAC/rB,EAAQisB,UAAWlzB,GACxF,EAwCA,IAAIozB,GAAa,kBAAoB,MAmFrC,SAASC,GAAOC,GACV,CAGN,CAEA,SAASC,GAAmBpuB,EAAItG,GAC9Bw0B,IAaF,CASA,SAASG,GAAYn0B,GACnB,MAAMo0B,GAAM,IAAAv0B,QAAOG,GAInB,OAHA,IAAAO,WAAU,KACR6zB,EAAIp0B,QAAUA,IAETo0B,CACT,CAqCA,SAASprB,GAAWuR,GAClB,MAAoB,SAAhBA,EAAMW,OAAoC,mBAAhBX,EAAMW,OAG7BX,EAAMvR,UACf,CAEA,MAAMqrB,GAAM,EACNC,GAAQ,GAGRC,GAAS,GACTC,GAAW,GACX9mB,GAAM,GACNkN,GAAO,GAMP6Z,GAAgB,CACpB,CAACH,KAAQ,EACT,CAACD,KAAM,GAET,IAAIK,GAA4B5rB,IAC1B2rB,GAAc3rB,EAAM6rB,UACtB7rB,EAAMK,gBAET,EAWD,IAAIyrB,GATuB,MACzB,MAAMxE,EAAO,mBACb,GAAwB,oBAAb/L,SACT,OAAO+L,EAIT,MAFmB,CAACA,EAAM,KAAKA,IAAQ,SAASA,IAAQ,MAAMA,IAAQ,IAAIA,KAC7Cld,KAAKjL,GAAa,KAAKA,MAAeoc,WAC/C+L,CACrB,EAR0B,GAgB3B,MAAMyE,GAAS,CACb1tB,KAAM,QAER,SAAS2tB,IAAmB,OAC1BxuB,EAAM,UACNqZ,EAAS,SACToV,EAAQ,SACRC,IAEA,MAAO,CAAC,CACN/sB,UAAW,YACXnC,GAAIgD,IACF,MAAM,OACJmsB,EAAM,QACNC,EAAO,QACPC,GACErsB,EACJ,GAtBgB,IAsBZmsB,EACF,OAEF,MAAMrpB,EAAQ,CACZxK,EAAG8zB,EACH7zB,EAAG8zB,GAECja,EAAQ6Z,IACd,GAAmB,aAAf7Z,EAAM/T,KAGR,OAFA2B,EAAMK,sBACN+R,EAAMka,QAAQ7T,KAAK3V,GAGJ,YAAfsP,EAAM/T,MAAmG,GAAU,GACrH,MAAMkuB,EAAUna,EAAMtP,MACtB,GAnCkCzI,EAmCEkyB,EAnCQr1B,EAmCC4L,IAlC1CK,KAAKqpB,IAAIt1B,EAAQoB,EAAI+B,EAAS/B,IAFV,GAEwC6K,KAAKqpB,IAAIt1B,EAAQqB,EAAI8B,EAAS9B,IAFtE,GAqCrB,OApCR,IAAwC8B,EAAUnD,EAsC5C8I,EAAMK,iBACN,MAAMisB,EAAUla,EAAMka,QAAQG,UAAU3pB,GACxCopB,EAAS,CACP7tB,KAAM,WACNiuB,cAGH,CACDntB,UAAW,UACXnC,GAAIgD,IACF,MAAMoS,EAAQ6Z,IACK,aAAf7Z,EAAM/T,MAIV2B,EAAMK,iBACN+R,EAAMka,QAAQnT,KAAK,CACjBuT,sBAAsB,IAExB7V,KAPErZ,MASH,CACD2B,UAAW,YACXnC,GAAIgD,IACsB,aAApBisB,IAAW5tB,MACb2B,EAAMK,iBAER7C,MAED,CACD2B,UAAW,UACXnC,GAAIgD,IAEF,GAAmB,YADLisB,IACJ5tB,KAIV,OA7GS,KA6GL2B,EAAM6rB,SACR7rB,EAAMK,sBACN7C,UAGFouB,GAAyB5rB,GARvBxC,MAUH,CACD2B,UAAW,SACXnC,GAAIQ,GACH,CACD2B,UAAW,SACXL,QAAS,CACPuc,SAAS,EACTC,SAAS,GAEXte,GAAI,KACsB,YAApBivB,IAAW5tB,MACbb,MAGH,CACD2B,UAAW,uBACXnC,GAAIgD,IACF,MAAMoS,EAAQ6Z,IACG,SAAf7Z,EAAM/T,MAAkG,GAAU,GAChH+T,EAAMka,QAAQK,0BAChBnvB,IAGFwC,EAAMK,mBAEP,CACDlB,UAAW2sB,GACX9uB,GAAIQ,GAER,CAsHA,SAASovB,KAAU,CACnB,MAAMC,GAAiB,CACrB,CAACnB,KAAW,EACZ,CAACD,KAAS,EACV,CAAC3Z,KAAO,EACR,CAAClN,KAAM,GAET,SAASkoB,GAAoBR,EAAS5Q,GACpC,SAASle,IACPke,IACA4Q,EAAQ9uB,QACV,CAKA,MAAO,CAAC,CACN2B,UAAW,UACXnC,GAAIgD,GAzRO,KA0RLA,EAAM6rB,SACR7rB,EAAMK,sBACN7C,KA3RM,KA8RJwC,EAAM6rB,SACR7rB,EAAMK,iBAZVqb,SACA4Q,EAAQnT,QA5QM,KA2RRnZ,EAAM6rB,SACR7rB,EAAMK,sBACNisB,EAAQ3T,YA/RA,KAkSN3Y,EAAM6rB,SACR7rB,EAAMK,sBACNisB,EAAQ5T,UAnSG,KAsST1Y,EAAM6rB,SACR7rB,EAAMK,sBACNisB,EAAQ1T,aA1SE,KA6SR5Y,EAAM6rB,SACR7rB,EAAMK,sBACNisB,EAAQzT,iBAGNgU,GAAe7sB,EAAM6rB,SACvB7rB,EAAMK,iBAGRurB,GAAyB5rB,KAE1B,CACDb,UAAW,YACXnC,GAAIQ,GACH,CACD2B,UAAW,UACXnC,GAAIQ,GACH,CACD2B,UAAW,QACXnC,GAAIQ,GACH,CACD2B,UAAW,aACXnC,GAAIQ,GACH,CACD2B,UAAW,SACXnC,GAAIQ,GACH,CACD2B,UAAW,QACXnC,GAAIQ,EACJsB,QAAS,CACPuc,SAAS,IAEV,CACDlc,UAAW2sB,GACX9uB,GAAIQ,GAER,CAqDA,MAAMuvB,GAAO,CACX1uB,KAAM,QAuQR,MAAM2uB,GAAsB,CAAC,QAAS,SAAU,WAAY,SAAU,SAAU,WAAY,QAAS,SACrG,SAASC,GAAuBC,EAAQh2B,GACtC,GAAe,MAAXA,EACF,OAAO,EAGT,GAD4B81B,GAAoBG,SAASj2B,EAAQk2B,QAAQC,eAEvE,OAAO,EAET,MAAMnF,EAAYhxB,EAAQ0yB,aAAa,mBACvC,MAAkB,SAAd1B,GAAsC,KAAdA,GAGxBhxB,IAAYg2B,GAGTD,GAAuBC,EAAQh2B,EAAQo2B,cAChD,CACA,SAASC,GAA4BtnB,EAAWjG,GAC9C,MAAMtH,EAASsH,EAAMtH,OACrB,QAAK6wB,GAAc7wB,IAGZu0B,GAAuBhnB,EAAWvN,EAC3C,CAEA,IAAI80B,GAA8B3xB,GAAM/D,EAAQ+D,EAAGC,yBAAyBtD,OAM5E,MAAMi1B,GAAuB,MAC3B,MAAMnG,EAAO,UACb,GAAwB,oBAAb/L,SACT,OAAO+L,EAIT,MAFmB,CAACA,EAAM,oBAAqB,yBACtBld,KAAKsjB,GAAQA,KAAQC,QAAQluB,YACtC6nB,CACjB,EAR4B,GAS7B,SAASsG,GAAgB/xB,EAAIgsB,GAC3B,OAAU,MAANhsB,EACK,KAELA,EAAG4xB,IAAsB5F,GACpBhsB,EAEF+xB,GAAgB/xB,EAAGyxB,cAAezF,EAC3C,CACA,SAASnb,GAAQ7Q,EAAIgsB,GACnB,OAAIhsB,EAAG6Q,QACE7Q,EAAG6Q,QAAQmb,GAEb+F,GAAgB/xB,EAAIgsB,EAC7B,CAKA,SAASgG,GAA+BtG,EAAWvnB,GACjD,MAAMtH,EAASsH,EAAMtH,OACrB,MAlCiBmD,EAkCFnD,aAjCM0wB,GAAgBvtB,GAAI8xB,SAmCvC,OAAO,KApCX,IAAmB9xB,EAsCjB,MAAMgsB,EATR,SAAqBN,GACnB,MAAO,IAAIF,GAAWE,cAAcA,KACtC,CAOmBS,CAAYT,GACvBoC,EAASjd,GAAQhU,EAAQmvB,GAC/B,OAAK8B,GAGAJ,GAAcI,GAIZA,EANE,IAOX,CAyBA,SAAStpB,GAAeL,GACtBA,EAAMK,gBACR,CACA,SAASob,IAAS,SAChBqS,EAAQ,MACR1b,EAAK,aACL2b,EAAY,WACZC,IAEA,QAAKD,KAcDD,IAAa1b,CAiBnB,CACA,SAAS6b,IAAS,QAChBC,EAAO,MACPtS,EAAK,SACLmF,EAAQ,YACRpf,IAEA,GAAIusB,EAAQC,YACV,OAAO,EAET,MAAMhS,EAAQ4E,EAAS9a,UAAUqkB,SAAS3oB,GAC1C,QAAKwa,MAIAA,EAAMrd,QAAQiP,aAGdqU,GAAaxG,EAAMvB,WAAY1Y,GAItC,CACA,SAASysB,IAAS,QAChBF,EAAO,UACP3G,EAAS,MACT3L,EAAK,SACLmF,EAAQ,YACRpf,EAAW,gBACX0sB,EAAe,YACfC,IAQA,IANoBL,GAAS,CAC3BC,UACAtS,QACAmF,WACApf,gBAGA,OAAO,KAET,MAAMwa,EAAQ4E,EAAS9a,UAAUqb,QAAQ3f,GACnC9F,EAlGR,SAAuB0rB,EAAW5lB,GAChC,MAAMkmB,EAAW,IAAI5hB,GAAUshB,cAAcA,MAEvCgH,EADWtF,GAAiB1N,SAAUsM,GACfzd,KAAKvO,GACzBA,EAAG+tB,aAAa3jB,GAAUxE,MAAQE,GAE3C,OAAK4sB,GAGAhF,GAAcgF,GAIZA,EANE,IAOX,CAoFaC,CAAcjH,EAAWpL,EAAM1W,WAAWhE,IACrD,IAAK5F,EAEH,OAAO,KAET,GAAIyyB,IAAgBnS,EAAMrd,QAAQ2vB,4BAA8BlB,GAA4B1xB,EAAIyyB,GAC9F,OAAO,KAET,MAAMI,EAAOR,EAAQS,MAAMN,GAAmB9vB,GAC9C,IAAI6T,EAAQ,WACZ,SAASwc,IACP,OAAOzS,EAAMrd,QAAQ6tB,uBACvB,CACA,SAASoB,IACP,OAAOG,EAAQzS,SAASiT,EAC1B,CAWA,MAAMG,EAVN,SAAqBf,EAAUgB,GACzBrT,GAAS,CACXqS,WACA1b,QACA2b,eACAC,YAAY,KAEZpS,EAAMtB,SAASwU,IAEnB,EAC4CjxB,KAAK,KAAM,YACvD,SAASiiB,EAAK1iB,GACZ,SAASyZ,IACPqX,EAAQa,UACR3c,EAAQ,WACV,CAOA,SAASuD,EAAOtT,EAAQvD,EAAU,CAChC4tB,sBAAsB,IAGtB,GADAtvB,EAAK4xB,UACDlwB,EAAQ4tB,qBAAsB,CAChC,MAAMptB,EAASd,EAAW7D,OAAQ,CAAC,CACjCwE,UAAW,QACXnC,GAAIqD,GACJvB,QAAS,CACP4f,MAAM,EACNrD,SAAS,EACTC,SAAS,MAGbY,WAAW5c,EACb,CACAuX,IACA+E,EAAMtB,SAAStB,GAAO,CACpB3W,WAEJ,CACA,MA3Bc,aAAV+P,IACFyE,IAC4F,GAAU,IAExG+E,EAAMtB,SAASnC,GAAO/a,EAAK6xB,iBAC3B7c,EAAQ,WAsBD,CACLqJ,SAAU,IAAMA,GAAS,CACvBqS,SAAU,WACV1b,QACA2b,eACAC,YAAY,IAEdrB,wBAAyBiC,EACzBzV,KAAMra,GAAW6W,EAAO,OAAQ7W,GAChCtB,OAAQsB,GAAW6W,EAAO,SAAU7W,MACjC1B,EAAKkvB,QAEZ,CA+DA,MAZgB,CACd7Q,SAAU,IAAMA,GAAS,CACvBqS,SAAU,WACV1b,QACA2b,eACAC,YAAY,IAEdrB,wBAAyBiC,EACzBnC,UA1DF,SAAmBle,GACjB,MAAM2gB,EAAS,EAAQ5hB,IACrBuhB,EAAwB,IAAMpW,GAAK,CACjCnL,cAcJ,MAAO,IAXKwS,EAAK,CACfmP,eAAgB,CACdxtB,GAAIE,EACJ4M,kBACA6G,aAAc,SAEhB4Z,QAAS,IAAME,EAAO1xB,SACtB8uB,QAAS,CACP7T,KAAMyW,KAKRzW,KAAMyW,EAEV,EAsCEC,SArCF,WACE,MAAM7C,EAAU,CACd5T,OAAQ,IAAMmW,EAAwBnW,IACtCE,UAAW,IAAMiW,EAAwBjW,IACzCD,SAAU,IAAMkW,EAAwBlW,IACxCE,SAAU,IAAMgW,EAAwBhW,KAE1C,OAAOiH,EAAK,CACVmP,eAAgB,CACdxtB,GAAIE,EACJ4M,gBAAiBif,GAA2B3xB,GAC5CuZ,aAAc,QAEhB4Z,QAASzwB,EACT+tB,WAEJ,EAsBEhO,MArBF,WACwB7C,GAAS,CAC7BqS,SAAU,WACV1b,QACA2b,eACAC,YAAY,KAGZE,EAAQa,SAEZ,EAcF,CACA,MAAMK,GAAiB,CAj0BvB,SAAwBC,GACtB,MAAMC,GAAW,IAAAv4B,QAAOg1B,IAClBwD,GAAkB,IAAAx4B,QAAOwH,GACzBixB,EAAsB93B,EAAQ,KAAM,CACxCyH,UAAW,YACXnC,GAAI,SAAqBgD,GACvB,GAAIA,EAAMyvB,iBACR,OAEF,GA3HgB,IA2HZzvB,EAAMmsB,OACR,OAEF,GAAInsB,EAAM0vB,SAAW1vB,EAAM2vB,SAAW3vB,EAAM4vB,UAAY5vB,EAAM6vB,OAC5D,OAEF,MAAMluB,EAAc0tB,EAAIS,uBAAuB9vB,GAC/C,IAAK2B,EACH,OAEF,MAAM2qB,EAAU+C,EAAIU,WAAWpuB,EAAa+Z,EAAM,CAChD4S,YAAatuB,IAEf,IAAKssB,EACH,OAEFtsB,EAAMK,iBACN,MAAMyC,EAAQ,CACZxK,EAAG0H,EAAMosB,QACT7zB,EAAGyH,EAAMqsB,SAEXkD,EAAgBr4B,UAChB84B,EAAiB1D,EAASxpB,EAC5B,IACE,CAACusB,IACCY,EAA2Bv4B,EAAQ,KAAM,CAC7CyH,UAAW,4BACXnC,GAAIgD,IACF,GAAIA,EAAMyvB,iBACR,OAEF,MAAMhuB,EAAK4tB,EAAIS,uBAAuB9vB,GACtC,IAAKyB,EACH,OAEF,MAAM3C,EAAUuwB,EAAIa,wBAAwBzuB,GACvC3C,IAGDA,EAAQ6tB,yBAGP0C,EAAIc,WAAW1uB,IAGpBzB,EAAMK,qBAEN,CAACgvB,IACCe,EAAmBz4B,EAAY,WAKnC43B,EAAgBr4B,QAAUsH,EAAW7D,OAAQ,CAACs1B,EAA0BT,GAJxD,CACdnU,SAAS,EACTC,SAAS,GAGb,EAAG,CAAC2U,EAA0BT,IACxB9T,EAAO/jB,EAAY,KAEF,SADL23B,EAASp4B,QACbmH,OAGZixB,EAASp4B,QAAU60B,GACnBwD,EAAgBr4B,UAChBk5B,MACC,CAACA,IACE5yB,EAAS7F,EAAY,KACzB,MAAMya,EAAQkd,EAASp4B,QACvBwkB,IACmB,aAAftJ,EAAM/T,MACR+T,EAAMka,QAAQ9uB,OAAO,CACnBkvB,sBAAsB,IAGP,YAAfta,EAAM/T,MACR+T,EAAMka,QAAQhO,SAEf,CAAC5C,IACE2U,EAAsB14B,EAAY,WACtC,MAIM8G,EAAWutB,GAAmB,CAClCxuB,SACAqZ,UAAW6E,EACXuQ,SAAU,IAAMqD,EAASp4B,QACzBg1B,SAAU9Z,IACRkd,EAASp4B,QAAUkb,KAGvBmd,EAAgBr4B,QAAUsH,EAAW7D,OAAQ8D,EAZ7B,CACd6c,SAAS,EACTD,SAAS,GAWb,EAAG,CAAC7d,EAAQke,IACNsU,EAAmBr4B,EAAY,SAA0B20B,EAASxpB,GAC1C,SAA1BwsB,EAASp4B,QAAQmH,MAA4H,GAAU,GACzJixB,EAASp4B,QAAU,CACjBmH,KAAM,UACNyE,QACAwpB,WAEF+D,GACF,EAAG,CAACA,IACJ5H,GAAgB,WAEd,OADA2H,IACO,WACLb,EAAgBr4B,SAClB,CACF,EAAG,CAACk5B,GACN,EAmFA,SAA2Bf,GACzB,MAAME,GAAkB,IAAAx4B,QAAO61B,IACzB4C,EAAsB93B,EAAQ,KAAM,CACxCyH,UAAW,UACXnC,GAAI,SAAmBgD,GACrB,GAAIA,EAAMyvB,iBACR,OAEF,GA/VQ,KA+VJzvB,EAAM6rB,QACR,OAEF,MAAMlqB,EAAc0tB,EAAIS,uBAAuB9vB,GAC/C,IAAK2B,EACH,OAEF,MAAM2uB,EAAUjB,EAAIU,WAAWpuB,EAAa+Z,EAAM,CAChD4S,YAAatuB,IAEf,IAAKswB,EACH,OAEFtwB,EAAMK,iBACN,IAAIkwB,GAAc,EAClB,MAAMjE,EAAUgE,EAAQnB,WAExB,SAASzT,IACN6U,GAAqI,GAAU,GAChJA,GAAc,EACdhB,EAAgBr4B,UAChBk5B,GACF,CANAb,EAAgBr4B,UAOhBq4B,EAAgBr4B,QAAUsH,EAAW7D,OAAQmyB,GAAoBR,EAAS5Q,GAAO,CAC/EJ,SAAS,EACTD,SAAS,GAEb,IACE,CAACgU,IACCe,EAAmBz4B,EAAY,WAKnC43B,EAAgBr4B,QAAUsH,EAAW7D,OAAQ,CAAC60B,GAJ9B,CACdnU,SAAS,EACTC,SAAS,GAGb,EAAG,CAACkU,IACJ/G,GAAgB,WAEd,OADA2H,IACO,WACLb,EAAgBr4B,SAClB,CACF,EAAG,CAACk5B,GACN,EA8HA,SAAwBf,GACtB,MAAMC,GAAW,IAAAv4B,QAAOg2B,IAClBwC,GAAkB,IAAAx4B,QAAOwH,GACzB0tB,EAAWt0B,EAAY,WAC3B,OAAO23B,EAASp4B,OAClB,EAAG,IACGg1B,EAAWv0B,EAAY,SAAkBya,GAC7Ckd,EAASp4B,QAAUkb,CACrB,EAAG,IACGod,EAAsB93B,EAAQ,KAAM,CACxCyH,UAAW,aACXnC,GAAI,SAAsBgD,GACxB,GAAIA,EAAMyvB,iBACR,OAEF,MAAM9tB,EAAc0tB,EAAIS,uBAAuB9vB,GAC/C,IAAK2B,EACH,OAEF,MAAM2qB,EAAU+C,EAAIU,WAAWpuB,EAAa+Z,EAAM,CAChD4S,YAAatuB,IAEf,IAAKssB,EACH,OAEF,MAAMkE,EAAQxwB,EAAMywB,QAAQ,IACtB,QACJrE,EAAO,QACPC,GACEmE,EACE1tB,EAAQ,CACZxK,EAAG8zB,EACH7zB,EAAG8zB,GAELkD,EAAgBr4B,UAChB84B,EAAiB1D,EAASxpB,EAC5B,IACE,CAACusB,IACCe,EAAmBz4B,EAAY,WAKnC43B,EAAgBr4B,QAAUsH,EAAW7D,OAAQ,CAAC60B,GAJ9B,CACdlU,SAAS,EACTD,SAAS,GAGb,EAAG,CAACmU,IACE9T,EAAO/jB,EAAY,KACvB,MAAMT,EAAUo4B,EAASp4B,QACJ,SAAjBA,EAAQmH,OAGS,YAAjBnH,EAAQmH,MACVke,aAAarlB,EAAQw5B,kBAEvBxE,EAASa,IACTwC,EAAgBr4B,UAChBk5B,MACC,CAACA,EAAkBlE,IAChB1uB,EAAS7F,EAAY,KACzB,MAAMya,EAAQkd,EAASp4B,QACvBwkB,IACmB,aAAftJ,EAAM/T,MACR+T,EAAMka,QAAQ9uB,OAAO,CACnBkvB,sBAAsB,IAGP,YAAfta,EAAM/T,MACR+T,EAAMka,QAAQhO,SAEf,CAAC5C,IACE2U,EAAsB14B,EAAY,WACtC,MAAMmH,EAAU,CACdwc,SAAS,EACTD,SAAS,GAELje,EAAO,CACXI,SACAqZ,UAAW6E,EACXuQ,YAEI0E,EAAenyB,EAAW7D,OAtKpC,UAA2B,OACzB6C,EAAM,UACNqZ,EAAS,SACToV,IAEA,MAAO,CAAC,CACN9sB,UAAW,YACXL,QAAS,CACPwc,SAAS,GAEXte,GAAIgD,IACF,MAAMoS,EAAQ6Z,IACd,GAAmB,aAAf7Z,EAAM/T,KAER,YADAb,IAGF4U,EAAMwe,UAAW,EACjB,MAAM,QACJxE,EAAO,QACPC,GACErsB,EAAMywB,QAAQ,GACZ3tB,EAAQ,CACZxK,EAAG8zB,EACH7zB,EAAG8zB,GAELrsB,EAAMK,iBACN+R,EAAMka,QAAQ7T,KAAK3V,KAEpB,CACD3D,UAAW,WACXnC,GAAIgD,IACF,MAAMoS,EAAQ6Z,IACK,aAAf7Z,EAAM/T,MAIV2B,EAAMK,iBACN+R,EAAMka,QAAQnT,KAAK,CACjBuT,sBAAsB,IAExB7V,KAPErZ,MASH,CACD2B,UAAW,cACXnC,GAAIgD,IACsB,aAApBisB,IAAW5tB,MAIf2B,EAAMK,iBACN7C,KAJEA,MAMH,CACD2B,UAAW,mBACXnC,GAAIgD,IACF,MAAMoS,EAAQ6Z,IACG,SAAf7Z,EAAM/T,MAA8E,GAAU,GAChG,MAAMmyB,EAAQxwB,EAAMywB,QAAQ,GAC5B,IAAKD,EACH,OAGF,KADqBA,EAAMK,OA9FL,KAgGpB,OAEF,MAAMC,EAAgB1e,EAAMka,QAAQK,0BACpC,GAAmB,YAAfva,EAAM/T,KAMV,OAAIyyB,EACE1e,EAAMwe,cACR5wB,EAAMK,sBAGR7C,SAGFwC,EAAMK,iBAbAywB,GACFtzB,MAcL,CACD2B,UAAW2sB,GACX9uB,GAAIQ,GAER,CAgF4CuzB,CAAkB3zB,GAAO0B,GAC3DkyB,EAAexyB,EAAW7D,OAvMpC,UAA2B,OACzB6C,EAAM,SACNyuB,IAEA,MAAO,CAAC,CACN9sB,UAAW,oBACXnC,GAAIQ,GACH,CACD2B,UAAW,SACXnC,GAAIQ,GACH,CACD2B,UAAW,cACXnC,GAAIgD,IACFA,EAAMK,mBAEP,CACDlB,UAAW,UACXnC,GAAIgD,IACsB,aAApBisB,IAAW5tB,MAnaN,KAuaL2B,EAAM6rB,SACR7rB,EAAMK,iBAER7C,KANEA,MAQH,CACD2B,UAAW2sB,GACX9uB,GAAIQ,GAER,CAwK4CyzB,CAAkB7zB,GAAO0B,GACjEywB,EAAgBr4B,QAAU,WACxBy5B,IACAK,GACF,CACF,EAAG,CAACxzB,EAAQyuB,EAAUvQ,IAChBwV,EAAgBv5B,EAAY,WAChC,MAAMya,EAAQ6Z,IACG,YAAf7Z,EAAM/T,MAAmI,GAAU,GACrJ,MAAMiuB,EAAUla,EAAMka,QAAQG,UAAUra,EAAMtP,OAC9CopB,EAAS,CACP7tB,KAAM,WACNiuB,UACAsE,UAAU,GAEd,EAAG,CAAC3E,EAAUC,IACR8D,EAAmBr4B,EAAY,SAA0B20B,EAASxpB,GAChD,SAApBmpB,IAAW5tB,MAA4H,GAAU,GACnJ,MAAMqyB,EAAmBxU,WAAWgV,EA3Nf,KA4NrBhF,EAAS,CACP7tB,KAAM,UACNyE,QACAwpB,UACAoE,qBAEFL,GACF,EAAG,CAACA,EAAqBpE,EAAUC,EAAUgF,IAC7CzI,GAAgB,WAEd,OADA2H,IACO,WACLb,EAAgBr4B,UAChB,MAAMkb,EAAQ6Z,IACK,YAAf7Z,EAAM/T,OACRke,aAAanK,EAAMse,kBACnBxE,EAASa,IAEb,CACF,EAAG,CAACd,EAAUmE,EAAkBlE,IAChCzD,GAAgB,WASd,OARejqB,EAAW7D,OAAQ,CAAC,CACjCwE,UAAW,YACXnC,GAAI,OACJ8B,QAAS,CACPwc,SAAS,EACTD,SAAS,KAIf,EAAG,GACL,GA2UA,SAAS8V,IAAiB,UACxB5J,EAAS,MACT3L,EAAK,SACLmF,EAAQ,cACRqQ,EAAa,qBACbC,IAEA,MAAMC,EAAa,IAAKD,EAAuBjC,GAAiB,MAASgC,GAAiB,IACpFlD,GAAU,IAAAt3B,UAAS,IAxgC3B,WACE,IAAI83B,EAAO,KAeX,SAASK,IACNL,GAA+G,GAAU,GAC1HA,EAAO,IACT,CAOA,MAAO,CACLP,UAzBF,WACE,OAAOh3B,QAAQu3B,EACjB,EAwBEjT,SAvBF,SAAkB5hB,GAChB,OAAOA,IAAU60B,CACnB,EAsBEC,MArBF,SAAe4C,GACX7C,GAAgH,GAAU,GAC5H,MAAM8C,EAAU,CACdD,WAGF,OADA7C,EAAO8C,EACAA,CACT,EAeEzC,UACA0C,WAXF,WACM/C,IACFA,EAAK6C,UACLxC,IAEJ,EAQF,CAu+BiC2C,IAAU,GACnCC,EAAiBh6B,EAAY,SAAwB6N,EAAUtO,GAC/DgJ,GAAWsF,KAActF,GAAWhJ,IACtCg3B,EAAQuD,YAEZ,EAAG,CAACvD,IACJzF,GAAgB,WACd,IAAIjjB,EAAWoW,EAAMvB,WAMrB,OALoBuB,EAAMsG,UAAU,KAClC,MAAMhrB,EAAU0kB,EAAMvB,WACtBsX,EAAensB,EAAUtO,GACzBsO,EAAWtO,GAGf,EAAG,CAACg3B,EAAStS,EAAO+V,IACpBlJ,GAAgB,IACPyF,EAAQuD,WACd,CAACvD,EAAQuD,aACZ,MAAMtB,EAAax4B,EAAYgK,GACtBssB,GAAS,CACdC,UACAnN,WACAnF,QACAja,gBAED,CAACusB,EAASnN,EAAUnF,IACjBmU,EAAap4B,EAAY,CAACgK,EAAaiwB,EAAW9yB,IAAYsvB,GAAS,CAC3EF,UACAnN,WACAwG,YACA3L,QACAja,cACA0sB,gBAAiBuD,GAAa,KAC9BtD,YAAaxvB,GAAWA,EAAQwvB,YAAcxvB,EAAQwvB,YAAc,OAClE,CAAC/G,EAAW2G,EAASnN,EAAUnF,IAC7BkU,EAAyBn4B,EAAYqI,GA9R7C,SAA2CunB,EAAWvnB,GACpD,MAAM2pB,EAASkE,GAA+BtG,EAAWvnB,GACzD,OAAK2pB,EAGEA,EAAOC,aAAavC,GAAW1lB,aAF7B,IAGX,CAwRsDkwB,CAAkCtK,EAAWvnB,GAAQ,CAACunB,IACpG2I,EAA0Bv4B,EAAY8J,IAC1C,MAAM0a,EAAQ4E,EAAS9a,UAAUqkB,SAAS7oB,GAC1C,OAAO0a,EAAQA,EAAMrd,QAAU,MAC9B,CAACiiB,EAAS9a,YACP6rB,EAAiBn6B,EAAY,WAC5Bu2B,EAAQC,cAGbD,EAAQuD,aACuB,SAA3B7V,EAAMvB,WAAWjI,OACnBwJ,EAAMtB,SAASxB,MAEnB,EAAG,CAACoV,EAAStS,IACPmW,EAAgBp6B,EAAY,IAAMu2B,EAAQC,YAAa,CAACD,IACxDmB,EAAM33B,EAAQ,KAAM,CACxBy4B,aACAJ,aACAD,yBACAI,0BACA4B,iBACAC,kBACE,CAAC5B,EAAYJ,EAAYD,EAAwBI,EAAyB4B,EAAgBC,IAzY9F7G,KA2YA,IAAK,IAAI3zB,EAAI,EAAGA,EAAI+5B,EAAWh6B,OAAQC,IACrC+5B,EAAW/5B,GAAG83B,EAElB,CAEA,MAAM2C,GAAmBnxB,IAAS,CAChC4c,gBAAiBzf,IACf,MAAMi0B,EAAyB,KACzBpxB,EAAM4c,iBACR5c,EAAM4c,gBAAgBzf,IAGtB,YAAck0B,WAAW,OAAS,YAAcA,WAAW,MAC7DD,KAEA,IAAAE,WAAUF,IAGdtU,kBAAmB9c,EAAM8c,kBACzB5b,YAAalB,EAAMkB,YACnBK,UAAWvB,EAAMuB,UACjBH,aAAcpB,EAAMoB,eAEhBmwB,GAA4BvxB,IAAS,IACtC8hB,MACA9hB,EAAMkjB,oBACTd,kBAAmB,IACdN,GAA2BM,qBAC3BpiB,EAAMkjB,uBAGb,SAASsO,GAASC,GAEhB,OADCA,EAAQp7B,SAA2G,GAAU,GACvHo7B,EAAQp7B,OACjB,CACA,SAASq7B,GAAI1xB,GACX,MAAM,UACJ0mB,EAAS,aACTjnB,EAAY,QACZkyB,EAAO,MACPzJ,EAAK,4BACLjnB,GACEjB,EACE4xB,GAAe,IAAA17B,QAAO,MA5nC5Bq0B,KA8nCA,MAAMsH,EAAerH,GAAYxqB,GAC3Buc,EAAgBzlB,EAAY,IACzBq6B,GAAiBU,EAAax7B,SACpC,CAACw7B,IACE5O,EAAyBnsB,EAAY,IAClCy6B,GAA0BM,EAAax7B,SAC7C,CAACw7B,IACE9V,EA30CR,SAAsB2K,GACpB,MAAM9lB,EAAK/J,EAAQ,IAAMkzB,GAAMrD,GAAY,CAACA,IACtC+D,GAAM,IAAAv0B,QAAO,MAsCnB,OArCA,IAAAU,WAAU,WACR,MAAMoE,EAAK0f,SAASmN,cAAc,OAOlC,OANA4C,EAAIp0B,QAAU2E,EACdA,EAAG4F,GAAKA,EACR5F,EAAGmtB,aAAa,YAAa,aAC7BntB,EAAGmtB,aAAa,cAAe,QAC/BtrB,EAAS7B,EAAG82B,MAAOjI,IACnBF,KAAiBoI,YAAY/2B,GACtB,WACLqgB,WAAW,WACT,MAAMuO,EAAOD,KACTC,EAAK9Z,SAAS9U,IAChB4uB,EAAKoI,YAAYh3B,GAEfA,IAAOyvB,EAAIp0B,UACbo0B,EAAIp0B,QAAU,KAElB,EACF,CACF,EAAG,CAACuK,IACa9J,EAAY2G,IAC3B,MAAMzC,EAAKyvB,EAAIp0B,QACX2E,IACFA,EAAGi3B,YAAcx0B,IAalB,GAEL,CAkyCmBy0B,CAAaxL,GACxByL,EA3wCR,UAA8B,UAC5BzL,EAAS,KACT0L,IAEA,MAAM7I,EAAWY,GAAc,cAAe,CAC5CD,UAAW,MAEPtpB,EAAK/J,EAAQ,IAbrB,UAAsB,UACpB6vB,EAAS,SACT6C,IAEA,MAAO,mBAAmB7C,KAAa6C,GACzC,CAQ2B8I,CAAa,CACpC3L,YACA6C,aACE,CAACA,EAAU7C,IAcf,OAbA,IAAA9vB,WAAU,WACR,MAAMoE,EAAK0f,SAASmN,cAAc,OAKlC,OAJA7sB,EAAG4F,GAAKA,EACR5F,EAAGi3B,YAAcG,EACjBp3B,EAAG82B,MAAMQ,QAAU,OACnB3I,KAAiBoI,YAAY/2B,GACtB,WACL,MAAM4uB,EAAOD,KACTC,EAAK9Z,SAAS9U,IAChB4uB,EAAKoI,YAAYh3B,EAErB,CACF,EAAG,CAAC4F,EAAIwxB,IACDxxB,CACT,CAkvCwC2xB,CAAqB,CACzD7L,YACA0L,KAAMnxB,IAEFod,EA5oDR,SAAyBqI,EAAWwB,GAClC,MAAMhuB,EAASrD,EAAQ,IAAMqwB,GAAYR,GAAY,CAACA,IAChD8L,GAAY,IAAAt8B,QAAO,MACnBu8B,GAAa,IAAAv8B,QAAO,MACpBw8B,EAAkB57B,EAAYyE,EAAW8U,IAC7C,MAAMrV,EAAKy3B,EAAWp8B,QACrB2E,GAAqH,GAAU,GAChIA,EAAGi3B,YAAc5hB,IACf,IACEsiB,EAAiB77B,EAAYuZ,IACjC,MAAMrV,EAAKw3B,EAAUn8B,QACpB2E,GAAqH,GAAU,GAChIA,EAAGi3B,YAAc5hB,GAChB,IACHuX,GAAgB,MACX4K,EAAUn8B,SAAYo8B,EAAWp8B,UAAwG,GAAU,GACtJ,MAAMmxB,EAASS,GAAcC,GACvB0K,EAAU3K,GAAcC,GAS9B,OARAsK,EAAUn8B,QAAUmxB,EACpBiL,EAAWp8B,QAAUu8B,EACrBpL,EAAOW,aAAa,GAAG,YAAiBzB,GACxCkM,EAAQzK,aAAa,GAAG,aAAkBzB,GAC1CoB,KAAUiK,YAAYvK,GACtBM,KAAUiK,YAAYa,GACtBD,EAAez4B,EAAOstB,QACtBkL,EAAgBx4B,EAAOwkB,SAChB,KACL,MAAM7Y,EAAS4kB,IACb,MAAMp0B,EAAUo0B,EAAIp0B,QACnBA,GAA4G,GAAU,GACvHyxB,KAAUkK,YAAY37B,GACtBo0B,EAAIp0B,QAAU,MAEhBwP,EAAO2sB,GACP3sB,EAAO4sB,KAER,CAACvK,EAAOyK,EAAgBD,EAAiBx4B,EAAOstB,OAAQttB,EAAOwkB,QAASgI,IAC3E,MAAMhK,EAAW5lB,EAAY,IAAM47B,EAAgBx4B,EAAOwiB,UAAW,CAACgW,EAAiBx4B,EAAOwiB,WACxF+B,EAAW3nB,EAAY0K,IAK3BkxB,EAJe,SAAXlxB,EAIYtH,EAAOytB,WAHLztB,EAAOutB,gBAIxB,CAACiL,EAAiBx4B,EAAOutB,cAAevtB,EAAOytB,aAC5CjJ,EAAU5nB,EAAY,KACrB27B,EAAWp8B,SAGhBq8B,EAAgBx4B,EAAOwkB,UACtB,CAACgU,EAAiBx4B,EAAOwkB,UAM5B,OALgB7nB,EAAQ,KAAM,CAC5B6lB,WACA+B,WACAC,YACE,CAAChC,EAAU+B,EAAUC,GAE3B,CAmlDuBmU,CAAgBnM,EAAWwB,GAC1C4K,EAAeh8B,EAAYqf,IAC/Bqb,GAASI,GAAcnY,SAAStD,IAC/B,IACG4c,EAAmBl8B,EAAQ,KAAM,IAAAm8B,oBAAmB,CACxDzb,wBACAE,yBACAC,4BACAC,mCACAH,uBACCsb,GAAe,CAACA,IACb5S,EA/3CR,WACE,MAAMA,EAAWrpB,EAAQmyB,GAAgB,IAUzC,OATA,IAAApyB,WAAU,IACD,WACD,YAAcy6B,WAAW,OAAS,YAAcA,WAAW,MAC7D30B,sBAAsBwjB,EAASgE,OAE/BhE,EAASgE,OAEb,EACC,CAAChE,IACGA,CACT,CAm3CmB+S,GACX9U,EAAmBtnB,EAAQ,IACxB6pB,GAAuBR,EAAU6S,GACvC,CAAC7S,EAAU6S,IACRzU,EAAeznB,EAAQ,IAAMkvB,GAAmB,CACpDvE,gBACAtd,gBAAiBia,EAAiBja,gBAClC+e,6BACG,IAAA+P,oBAAmB,CACpBpb,SACCkb,KACD,CAAC3U,EAAiBja,gBAAiB4uB,EAAc7P,IAC/C7E,EApkDR,SAAyBsI,GACvB,MAAMwM,GAAa,IAAAh9B,QAAO,CAAC,GACrBi9B,GAAY,IAAAj9B,QAAO,MACnBk9B,GAAuB,IAAAl9B,QAAO,MAC9Bm9B,GAAe,IAAAn9B,SAAO,GACtBozB,EAAWxyB,EAAY,SAAkB8J,EAAI0yB,GACjD,MAAMhY,EAAQ,CACZ1a,KACA0yB,SAGF,OADAJ,EAAW78B,QAAQuK,GAAM0a,EAClB,WACL,MAAMH,EAAU+X,EAAW78B,QACX8kB,EAAQva,KACR0a,UACPH,EAAQva,EAEnB,CACF,EAAG,IACG2yB,EAAez8B,EAAY,SAAsB08B,GACrD,MAAM1K,EAASF,GAAelC,EAAW8M,GACrC1K,GAAUA,IAAWpO,SAAS+Y,eAChC3K,EAAOwK,OAEX,EAAG,CAAC5M,IACEnH,EAAiBzoB,EAAY,SAAwB6N,EAAU+uB,GAC/DP,EAAU98B,UAAYsO,IACxBwuB,EAAU98B,QAAUq9B,EAExB,EAAG,IACGpU,EAA0BxoB,EAAY,WACtCs8B,EAAqB/8B,SAGpBg9B,EAAah9B,UAGlB+8B,EAAqB/8B,QAAUqG,sBAAsB,KACnD02B,EAAqB/8B,QAAU,KAC/B,MAAMs9B,EAASR,EAAU98B,QACrBs9B,GACFJ,EAAaI,KAGnB,EAAG,CAACJ,IACElU,EAAiBvoB,EAAY,SAAwB8J,GACzDuyB,EAAU98B,QAAU,KACpB,MAAMu9B,EAAUlZ,SAAS+Y,cACpBG,GAGDA,EAAQ7K,aAAavC,GAAW1lB,eAAiBF,IAGrDuyB,EAAU98B,QAAUuK,EACtB,EAAG,IAiBH,OAhBAgnB,GAAgB,KACdyL,EAAah9B,SAAU,EAChB,WACLg9B,EAAah9B,SAAU,EACvB,MAAM+F,EAAUg3B,EAAqB/8B,QACjC+F,GACFQ,qBAAqBR,EAEzB,GACC,IACavF,EAAQ,KAAM,CAC5ByyB,WACAjK,iBACAC,0BACAC,mBACE,CAAC+J,EAAUjK,EAAgBC,EAAyBC,GAE1D,CA2/CuBsU,CAAgBnN,GAC/B3L,EAAQlkB,EAAQ,IAAMqnB,GAAY,CACtCnC,WACAuC,eACAH,mBACAC,eACA7B,gBACA8B,iBACE,CAACtC,EAAUuC,EAAcH,EAAkBC,EAAc7B,EAAe8B,IAM5EuT,EAAav7B,QAAU0kB,EACvB,MAAM+Y,EAAgBh9B,EAAY,KAChC,MAAMT,EAAUm7B,GAASI,GAEL,SADNv7B,EAAQmjB,WACZjI,OACRlb,EAAQojB,SAASxB,OAElB,IACG5Y,EAAavI,EAAY,KAC7B,MAAM8Z,EAAQ4gB,GAASI,GAAcpY,WACrC,MAAoB,mBAAhB5I,EAAMW,OAGU,SAAhBX,EAAMW,OAGHX,EAAMvR,YACZ,IAKHI,EAJqB5I,EAAQ,KAAM,CACjCwI,aACAC,SAAUw0B,IACR,CAACz0B,EAAYy0B,KAEjB,MAAMC,EAAaj9B,EAAY8J,GAAM2gB,GAAaiQ,GAASI,GAAcpY,WAAY5Y,GAAK,IACpFozB,EAAuBl9B,EAAY,IAAMwa,GAAkBkgB,GAASI,GAAcpY,YAAa,IAC/Fya,EAAap9B,EAAQ,KAAM,CAC/B2nB,QAASL,EACTmV,MAAOlV,EACPsI,YACAwN,QAASH,EACTziB,kBAAmB0iB,EACnB7B,gCACAjS,aACE,CAACwG,EAAWvI,EAAkBgU,EAA+B/T,EAAc2V,EAAYC,EAAsB9T,IAWjH,OAVAoQ,GAAiB,CACf5J,YACA3L,QACAmF,WACAqQ,cAAeoB,GAAW,KAC1BnB,sBAAqD,IAA/BxwB,EAAMwwB,wBAE9B,IAAA55B,WAAU,IACDk9B,EACN,CAACA,IACG,kBAAoB1J,GAAW+J,SAAU,CAC9Cn7B,MAAOi7B,GACN,kBAAoB,EAAAE,SAAU,CAC/B/M,QAASsC,GACT3O,MAAOA,GACN/a,EAAMC,UACX,CAEA,IAAIm0B,GAAQ,EAUZ,IAAIC,GAAuB,cAH3B,WACE,OAAO,WACT,EALA,WACE,OAAOx9B,EAAQ,IAAM,GAAGu9B,KAAW,GACrC,EAcA,SAASE,GAAgBt0B,GACvB,MAAM0mB,EAAY2N,KACZpzB,EAA8BjB,EAAMiB,6BAA+BQ,EAASR,4BAClF,OAAO,kBAAoBnC,EAAe,KAAMW,GAAgB,kBAAoBiyB,GAAK,CACvFxJ,MAAOloB,EAAMkoB,MACbxB,UAAWA,EACXjnB,aAAcA,EACdwB,4BAA6BA,EAC7BuvB,qBAAsBxwB,EAAMwwB,qBAC5BmB,QAAS3xB,EAAM2xB,QACf/U,gBAAiB5c,EAAM4c,gBACvBE,kBAAmB9c,EAAM8c,kBACzB5b,YAAalB,EAAMkB,YACnBE,aAAcpB,EAAMoB,aACpBG,UAAWvB,EAAMuB,UACjB2hB,oBAAqBljB,EAAMkjB,qBAC1BljB,EAAMC,UACX,CAEA,MAAMs0B,GACM,IADNA,GAEW,KAEXC,GAAwB,CAACC,EAA2BhW,IACpDA,EACK5F,GAAYP,KAAKmG,EAASzF,UAE/Byb,EACK5b,GAAYE,KAEdF,GAAYC,MAEf4b,GAAqB,CAACrb,EAAasb,KACvC,GAAKtb,EAGL,OAAOsb,EAAkB9zB,GAAgByX,KAAOzX,GAAgB0X,WAyClE,SAASqc,GAAWC,GAClB,MAAuB,aAAhBA,EAAOr3B,KAlChB,SAA0Bkf,GACxB,MACMzR,EADYyR,EAASzN,UACLxC,QAChB,OACJlT,EAAM,YACNqR,EAAW,SACX6T,GACE/B,EACErD,EAAc/iB,QAAQsU,GACtB/B,EAfyB6T,IACI,MAA/BA,EAASlU,mBACJkU,EAASlU,mBAEO,SAAlBkU,EAAS7N,KAWMimB,CAAyBpY,GACzCiY,EAAkBr+B,QAAQmoB,GAC1BsW,EAAYJ,EAAkBvb,GAAgB7f,EAAQ8f,GAAeD,GAAkB7f,GAc7F,MAbc,CACZ2G,SAAU,QACV/I,IAAK8T,EAAItS,UAAUxB,IACnBG,KAAM2T,EAAItS,UAAUrB,KACpB09B,UAAW,aACXz9B,MAAO0T,EAAI7S,UAAUb,MACrBC,OAAQyT,EAAI7S,UAAUZ,OACtBkwB,WAAY8M,GAAsB3rB,EAAe4V,GACjDsW,YACAE,QAASP,GAAmBrb,EAAasb,GACzCO,OAAQP,EAAkBJ,GAA8BA,GACxDY,cAAe,OAGnB,CAQsCC,CAAiBP,GAN9C,CACLE,UAAW3b,IAFYic,EAOwDR,GALxCt7B,QACvCmuB,WAAY2N,EAAUC,+BAA4Bnc,EAAY,QAHlE,IAA2Bkc,CAQ3B,CA0BA,SAASE,GAAsBh5B,GAC7B,MAAMgtB,EAAWY,GAAc,cACzB,WACJvlB,EAAU,SACVsb,EAAQ,gBACRsV,EAAe,2BACf5H,EAA0B,wBAC1B9B,EAAuB,UACvB5e,GACE3Q,EACE0B,EAAUpH,EAAQ,KAAM,CAC5B+2B,6BACA9B,0BACA5e,cACE,CAAC0gB,EAA4B1gB,EAAW4e,IACtCvL,EAAezpB,EAAYspB,IAC/B,MAAMplB,EAAKw6B,IAEX,OADCx6B,GAA2G,GAAU,GAzC1H,SAAwB4J,EAAY5J,EAAIolB,EAAe1e,GACrD,MAAM+zB,EAAiB37B,OAAOoB,iBAAiBF,GACzC5C,EAAY4C,EAAGC,wBACfwR,EAASxS,EAAa7B,EAAWq9B,GACjCnyB,EAAO1J,EAAW6S,EAAQ2T,GAiBhC,MAPkB,CAChBxb,aACA4Q,YAXkB,CAClB/I,SACA8f,QAASvxB,EAAGuxB,QAAQC,cACpB8F,QAASmD,EAAenD,SASxBznB,WAPiB,CACjBpT,EAAGgV,EAAO9T,UAAUpB,MACpBG,EAAG+U,EAAO9T,UAAUnB,QAMpBiV,SACAnJ,OAGJ,CAoBWoyB,CAAe9wB,EAAY5J,EAAIolB,IACrC,CAACxb,EAAY4wB,IACVla,EAAQzkB,EAAQ,KAAM,CAC1B0yB,WACA3kB,aACA3G,UACAsiB,iBACE,CAAC3b,EAAY2b,EAActiB,EAASsrB,IAClCoM,GAAe,IAAAz/B,QAAOolB,GACtBsa,GAAoB,IAAA1/B,SAAO,GACjC0xB,GAAgB,KACd1H,EAAS9a,UAAUkkB,SAASqM,EAAat/B,SAClC,IAAM6pB,EAAS9a,UAAUokB,WAAWmM,EAAat/B,UACvD,CAAC6pB,EAAS9a,YACbwiB,GAAgB,KACd,GAAIgO,EAAkBv/B,QAEpB,YADAu/B,EAAkBv/B,SAAU,GAG9B,MAAMoS,EAAOktB,EAAat/B,QAC1Bs/B,EAAat/B,QAAUilB,EACvB4E,EAAS9a,UAAU/D,OAAOia,EAAO7S,IAChC,CAAC6S,EAAO4E,EAAS9a,WACtB,CAEA,IAAIywB,GAAmB,kBAAoB,MAuC3C,SAASC,GAAmBC,GAC1B,MAAM//B,GAAS,IAAAggC,YAAWD,GAE1B,OADC//B,GAAuG,GAAU,GAC3GA,CACT,CAEA,SAASigC,GAAgB92B,GACvBA,EAAMK,gBACR,CA+FA,IAAI02B,GA9Fcl2B,IAChB,MAAMyqB,GAAM,IAAAv0B,QAAO,MACbigC,EAASr/B,EAAY,CAACkE,EAAK,QAC/ByvB,EAAIp0B,QAAU2E,GACb,IACGo7B,EAASt/B,EAAY,IAAM2zB,EAAIp0B,QAAS,KACxC,UACJqwB,EAAS,8BACTyL,EAA6B,SAC7BjS,GACE4V,GAAmB1L,KACjB,KACJ5sB,EAAI,YACJgD,GACEs1B,GAAmBD,IACjBjxB,EAAa/N,EAAQ,KAAM,CAC/B+J,GAAIZ,EAAMc,YACVX,MAAOH,EAAMG,MACb3C,OACAgD,gBACE,CAACR,EAAMc,YAAad,EAAMG,MAAO3C,EAAMgD,KACrC,SACJP,EAAQ,YACRa,EAAW,UACXoM,EAAS,wBACT4e,EAAuB,2BACvB8B,EAA0B,QAC1ByI,EAAO,OACPxB,EACAzc,sBAAuBke,GACrBt2B,EAGJ,GArEAuqB,KAmBAF,MAkDKgM,EAAS,CASZd,GARqB1+B,EAAQ,KAAM,CACjC+N,aACAsb,WACAsV,gBAAiBY,EACjBxI,6BACA9B,0BACA5e,cACE,CAACtI,EAAYsb,EAAUkW,EAAQxI,EAA4B9B,EAAyB5e,IAE1F,CACA,MAAMqpB,EAAkB1/B,EAAQ,IAAMqW,EAAY,CAChDspB,SAAU,EACVC,KAAM,SACN,mBAAoBtE,EACpB,oCAAqCrxB,EACrC,kCAAmC4lB,EACnCthB,WAAW,EACXlE,YAAa+0B,IACX,KAAM,CAACvP,EAAWyL,EAA+BrxB,EAAaoM,IAC5DwpB,EAAY5/B,EAAYqI,IACR,aAAhB01B,EAAOr3B,MAGNq3B,EAAOpW,UAGe,cAAvBtf,EAAMw3B,eAGN,YAActF,WAAW,OAAS,YAAcA,WAAW,MAC7DiF,KAEA,IAAAhF,WAAUgF,KAEX,CAACA,EAA6BzB,IAC3B+B,EAAW//B,EAAQ,KACvB,MAAMi7B,EAAQ8C,GAAWC,GACnBgC,EAAkC,aAAhBhC,EAAOr3B,MAAuBq3B,EAAOpW,SAAWiY,OAAYvd,EAWpF,MAVe,CACb2d,SAAUX,EACVY,eAAgB,CACd,gCAAiCrQ,EACjC,wBAAyB5lB,EACzBgxB,QACA+E,mBAEFN,oBAGD,CAAC7P,EAAW6P,EAAiBz1B,EAAa+zB,EAAQ6B,EAAWP,IAC1Da,EAASngC,EAAQ,KAAM,CAC3BiK,YAAa8D,EAAWhE,GACxBpD,KAAMoH,EAAWpH,KACjB6C,OAAQ,CACNF,MAAOyE,EAAWzE,MAClBK,YAAaoE,EAAWpE,eAExB,CAACoE,EAAWpE,YAAaoE,EAAWhE,GAAIgE,EAAWzE,MAAOyE,EAAWpH,OACzE,OAAO,kBAAoB,aAAgB,KAAMyC,EAAS22B,EAAU/B,EAAOoC,SAAUD,KAInFE,GAAgB,CAAE5xB,EAAGC,IAAMD,IAAMC,EAEjC4xB,GAA+BnhC,IACjC,MAAM,QACJ6K,EAAO,YACPP,GACEtK,EACJ,OAAIsK,EACKA,EAAYE,YAEjBK,EACKA,EAAQL,YAEV,IACR,EAqFD,SAAS42B,GAAqBC,EAAmB,MAC/C,MAAO,CACLh4B,YAAY,EACZs1B,iBAAiB,EACjB0B,SAAS,EACTiB,cAAe,KACfzoB,KAAM,KACN0oB,aAAc,KACdF,mBACAzsB,YAAa,KAEjB,CACA,MAAM4sB,GAAS,CACb3C,OAAQ,CACNr3B,KAAM,YACNjE,OAAQmI,EACR21B,iBAAkB,KAClB/B,2BAA2B,EAC3B2B,SAAUG,GAAqB,QA8DnC,MAMMK,GAAuB,CAC3Brf,sBAAuBA,IAEnBsf,IAAqB,IAAAC,SATG,KAC5B,MAAMC,EA9JR,WACE,MAAMC,EAAiBt8B,EAAW,CAAC9D,EAAGC,KAAM,CAC1CD,IACAC,OAEIogC,EAAsBv8B,EAAW,CAACsT,EAAMwnB,EAASkB,EAAe,KAAM3sB,EAAc,KAAM6T,EAAW,QAAS,CAClHpf,YAAY,EACZg3B,UACA1B,gBAAiBr+B,QAAQmoB,GACzB6Y,cAAe7Y,EACf5P,OACA0oB,eACA3sB,cACAysB,iBAAkB,QAEdU,EAAmBx8B,EAAW,CAAChC,EAAQsV,EAAMI,EAAWonB,EAASkB,EAAe,KAAM3sB,EAAc,KAAMpC,EAAqB,QAAS,CAC5IqsB,OAAQ,CACNr3B,KAAM,WACNihB,SAAU,KACV8Y,eACA3sB,cACAiE,OACAtV,SACA0V,YACAzG,qBACAyuB,SAAUa,EAAoBjpB,EAAMwnB,EAASkB,EAAc3sB,EAAa,UAkD5E,MA/CiB,CAACgG,EAAOonB,KACvB,GAAI34B,GAAWuR,GAAQ,CACrB,GAAIA,EAAMM,SAAS9L,UAAUxE,KAAOo3B,EAASl3B,YAC3C,OAAO,KAET,MAAMvH,EAASqX,EAAMva,QAAQoW,OAAOlT,OAC9B0V,EAAY2B,EAAMG,WAAWjM,WAAWkzB,EAASl3B,aACjDy2B,EAAe7mB,GAAkBE,EAAMnL,QACvCmF,GAvCqBnF,EAuCkBmL,EAAMnL,QAtCzCC,IAAyB,YAAnBD,EAAOC,GAAGlI,KAAqBiI,EAAOC,GAAG7E,QAAQC,YAAc,KAuCzE0H,EAAqBoI,EAAMpI,mBACjC,OAAOuvB,EAAiBF,EAAet+B,EAAO9B,EAAG8B,EAAO7B,GAAIkZ,EAAM2D,aAActF,EAAW+oB,EAAS3B,QAASkB,EAAc3sB,EAAapC,EAC1I,CA1C6B/C,MA2C7B,GAAoB,mBAAhBmL,EAAMW,MAA4B,CACpC,MAAMyE,EAAYpF,EAAMoF,UACxB,GAAIA,EAAUhgB,OAAO8K,cAAgBk3B,EAASl3B,YAC5C,OAAO,KAET,MAAMu1B,EAAU2B,EAAS3B,QACnBpnB,EAAY2B,EAAMG,WAAWjM,WAAWkzB,EAASl3B,aACjD9K,EAASggB,EAAUhgB,OACnB6Y,EAAO7Y,EAAO6Y,KACd0oB,EAAeJ,GAA4BnhC,GAC3C4U,EAxDqB5U,IACxBA,EAAO6K,QAAU7K,EAAO6K,QAAQC,YAAc,KAuD7Bm3B,CAAyBjiC,GAEvCyoB,EAAW,CACfzF,SAFepI,EAAMwG,aAGrB8gB,MAAO7f,GACPa,OAAQtI,EAAMyG,oBACd4d,QAASrqB,EAAc/J,GAAgByX,KAAO,KAC9C6f,MAAOvtB,EAAc/J,GAAcyX,KAAO,MAE5C,MAAO,CACLuc,OAAQ,CACNr3B,KAAM,WACNjE,OAAQqX,EAAMyG,oBACdpI,YACAwP,WACA8Y,eACA3sB,cACAiE,OACArG,mBAAoB,KACpByuB,SAAUa,EAAoBjpB,EAAMwnB,EAASkB,EAAc3sB,EAAa6T,IAG9E,CACA,OAAO,KAGX,CAkF2B2Z,GACnBC,EA7DR,WACE,MAAMR,EAAiBt8B,EAAW,CAAC9D,EAAGC,KAAM,CAC1CD,IACAC,OAEIogC,EAAsBv8B,EAAW67B,IACjCW,EAAmBx8B,EAAW,CAAChC,EAAQ89B,EAAmB,KAAM/B,KAA8B,CAClGT,OAAQ,CACNr3B,KAAM,YACNjE,SACA89B,mBACA/B,4BACA2B,SAAUa,EAAoBT,OAG5BiB,EAAcjB,GACXA,EAAmBU,EAAiBr2B,EAAQ21B,GAAkB,GAAQ,KAEzEkB,EAAW,CAACC,EAAOC,EAAYhzB,EAAQmE,KAC3C,MAAM8uB,EAAqBjzB,EAAOc,UAAUH,QAAQoyB,GAC9CG,EAA+BriC,QAAQsT,EAAcgL,eAAiBhL,EAAcC,SAAS2uB,IAC7F33B,EAAU8E,GAAcF,GACxB4xB,EAAmBx2B,GAAWA,EAAQC,cAAgB03B,EAAQC,EAAa,KACjF,IAAKC,EAAoB,CACvB,IAAKC,EACH,OAAOL,EAAYjB,GAErB,GAAI5xB,EAAOc,UAAUJ,UAAUqyB,GAC7B,OAAO,KAET,MAAM/+B,EAASuI,EAAO4H,EAAcpD,YAAYvE,OAC1C1I,EAASs+B,EAAep+B,EAAOhC,EAAGgC,EAAO/B,GAC/C,OAAOqgC,EAAiBx+B,EAAQ89B,GAAkB,EACpD,CACA,GAAIsB,EACF,OAAOL,EAAYjB,GAErB,MAAMxsB,EAAapF,EAAOe,YAAYvE,MAChC1I,EAASs+B,EAAehtB,EAAWpT,EAAGoT,EAAWnT,GACvD,OAAOqgC,EAAiBx+B,EAAQ89B,EAAkBqB,EAAmB7vB,gBAkBvE,MAhBiB,CAAC+H,EAAOonB,KACvB,GAAI34B,GAAWuR,GACb,OAAIA,EAAMM,SAAS9L,UAAUxE,KAAOo3B,EAASl3B,YACpC,KAEFy3B,EAASP,EAASl3B,YAAa8P,EAAMM,SAAS9L,UAAUxE,GAAIgQ,EAAMnL,OAAQmL,EAAMhH,eAEzF,GAAoB,mBAAhBgH,EAAMW,MAA4B,CACpC,MAAMyE,EAAYpF,EAAMoF,UACxB,OAAIA,EAAUhgB,OAAO8K,cAAgBk3B,EAASl3B,YACrC,KAEFy3B,EAASP,EAASl3B,YAAakV,EAAUhgB,OAAO8K,YAAakV,EAAUvQ,OAAQuQ,EAAUpM,cAClG,CACA,OAAO,KAGX,CAG4BgvB,GAE1B,MADiB,CAAChoB,EAAOonB,IAAaJ,EAAiBhnB,EAAOonB,IAAaK,EAAkBznB,EAAOonB,IAAaR,IAMzDC,GAAsB,KAAM,CACpFrQ,QAASsC,GACTmP,mBAAoB3B,IAFK,CAGxBhB,IACH,IAAI4C,GAAuBpB,GAE3B,SAASqB,GAAiB/4B,GAGxB,OAFyB81B,GAAmBD,IACHmD,kBACjBh5B,EAAMc,aAAgBd,EAAMq2B,QAG7C,kBAAoByC,GAAsB94B,GAFxC,IAGX,CACA,SAASi5B,GAAgBj5B,GACvB,MAAMkN,EAA4C,kBAAzBlN,EAAMk5B,iBAAgCl5B,EAAMk5B,eAC/DtL,EAA6Bt3B,QAAQ0J,EAAMm5B,mCAC3CrN,EAA0Bx1B,QAAQ0J,EAAM8rB,yBAC9C,OAAO,kBAAoBiN,GAAkBl8B,EAAS,CAAC,EAAGmD,EAAO,CAC/Dq2B,SAAS,EACTnpB,UAAWA,EACX0gB,2BAA4BA,EAC5B9B,wBAAyBA,IAE7B,CAEA,MAAM,GAAUrF,GAAQztB,GAASytB,IAASztB,EACpCogC,GAAW,GAAQ,UACnBC,GAAS,GAAQ,QAEjBC,IADY,GAAQ,WACT,CAACxP,EAAU3tB,IAAOA,EAAG2tB,EAASyP,YAAcp9B,EAAG2tB,EAAS0P,YAEnEC,GAAsBz+B,IAC1B,MAAM82B,EAAQh4B,OAAOoB,iBAAiBF,GAChC8uB,EAAW,CACfyP,UAAWzH,EAAMyH,UACjBC,UAAW1H,EAAM0H,WAEnB,OAAOF,GAASxP,EAAUsP,KAAaE,GAASxP,EAAUuP,KAgCtDK,GAAuB1+B,GACjB,MAANA,GAGAA,IAAO0f,SAASkP,MAGhB5uB,IAAO0f,SAASmF,gBALX,KAQJ4Z,GAAoBz+B,GAGlBA,EAFE0+B,GAAqB1+B,EAAGyxB,eAKnC,IAiBIkN,GAAa3+B,IAAM,CACrBvD,EAAGuD,EAAG4+B,WACNliC,EAAGsD,EAAG6+B,YAGR,MAAMC,GAAa9+B,IACjB,IAAKA,EACH,OAAO,EAGT,MAAuB,UADTlB,OAAOoB,iBAAiBF,GAC5BkF,UAGH45B,GAAW9+B,EAAGyxB,gBAmGvB,IAAIlM,GAAe,EACjBkK,MACA7lB,aACAm1B,MACA3Z,eACA/Y,YACA2yB,iBACAzvB,mBACAvH,wBAEA,MAAMi3B,EAAoBF,EAAIE,kBACxBxtB,EAtCU,EAACytB,EAAWD,KAC5B,MAAMxT,EAAO1rB,EAAOm/B,GACpB,IAAKD,EACH,OAAOxT,EAET,GAAIyT,IAAcD,EAChB,OAAOxT,EAET,MAAMtvB,EAAMsvB,EAAK7tB,WAAWzB,IAAM8iC,EAAkBJ,UAC9CviC,EAAOmvB,EAAK7tB,WAAWtB,KAAO2iC,EAAkBL,WAChDviC,EAASF,EAAM8iC,EAAkBxa,aACjCroB,EAAQE,EAAO2iC,EAAkBva,YAOjCtnB,EAAYR,EANC,CACjBT,MACAC,QACAC,SACAC,QAEmCmvB,EAAKjuB,QAO1C,OANeN,EAAU,CACvBE,YACAE,OAAQmuB,EAAKnuB,OACbE,OAAQiuB,EAAKjuB,OACbE,QAAS+tB,EAAK/tB,WAeDyhC,CAAU1P,EAAKwP,GACxB32B,EAAO1J,EAAW6S,EAAQ2T,GAC1BvU,EAAU,MACd,IAAKouB,EACH,OAAO,KAET,MAAMG,EAAcr/B,EAAOk/B,GACrBI,EAAa,CACjB5a,aAAcwa,EAAkBxa,aAChCC,YAAaua,EAAkBva,aAEjC,MAAO,CACLjT,OAAQ2tB,EACR92B,KAAM1J,EAAWwgC,EAAaha,GAC9BvmB,OAAQ8/B,GAAUM,GAClBI,aACAr3B,oBAEH,EAhBe,GAiBViM,EAtHoB,GAC1BrK,aACAsI,YACA3C,mBACAgM,gBACAlP,YACAoF,SACAnJ,OACAuI,cAEA,MAAM9I,EAAQ,MACZ,IAAK8I,EACH,OAAO,KAET,MAAM,WACJwuB,EACA5tB,OAAQ2tB,GACNvuB,EACEwD,EAAYmQ,GAAa,CAC7BC,aAAc4a,EAAW5a,aACzBC,YAAa2a,EAAW3a,YACxBloB,OAAQ4iC,EAAYxhC,WAAWpB,OAC/BD,MAAO6iC,EAAYxhC,WAAWrB,QAEhC,MAAO,CACL6L,cAAeyI,EAAQvI,KAAK3K,UAC5ByhC,cACAC,aACAr3B,kBAAmB6I,EAAQ7I,kBAC3BnJ,OAAQ,CACN/D,QAAS+V,EAAQhS,OACjBxD,QAASwV,EAAQhS,OACjBqJ,IAAKmM,EACL3L,KAAM,CACJ1K,MAAO0I,EACPiC,aAAcjC,IAIrB,EA7Ba,GA8BR8B,EAAqB,aAAd6D,EAA2BD,GAAWO,GAkBnD,MAXkB,CAChB/C,aACA2F,mBACAgM,gBACA/S,OACA0J,YACAT,SACAnJ,OACAP,QACAE,QAfcI,GAAW,CACzBC,OACAC,gBAAiB,KACjBC,OACAT,UAcH,EA2DmBu3B,CAAsB,CACtC11B,aACAsI,WAAY8sB,EACZzvB,mBACAgM,cAAewjB,EAAIxjB,cACnBlP,YACAoF,SACAnJ,OACAuI,YAEF,OAAOoD,CACR,EAED,MAAMsrB,GAAY,CAChB/f,SAAS,GAELggB,GAAU,CACdhgB,SAAS,GAEX,IAAIigB,GAAsBx8B,GAAWA,EAAQ8gB,yBAA2Bwb,GAAYC,GAEpF,MAAME,GAA+Bhe,GAAYA,GAAYA,EAASqd,IAAIE,mBAAqB,KAC/F,SAASU,GAAsBp+B,GAC7B,MAAMq+B,GAAmB,IAAA1kC,QAAO,MAC1B+9B,EAAa6B,GAAmB1L,IAChCb,EAAWY,GAAc,cACzB,SACJjK,EAAQ,QACR1B,GACEyV,EACE4G,EAAcrQ,GAAYjuB,GAC1BqI,EAAa/N,EAAQ,KAAM,CAC/B+J,GAAIrE,EAAKiE,YACThD,KAAMjB,EAAKiB,KACXqR,KAAMtS,EAAKsS,OACT,CAACtS,EAAKiE,YAAajE,EAAKsS,KAAMtS,EAAKiB,OACjCs9B,GAAyB,IAAA5kC,QAAO0O,GAChCm2B,EAAuBlkC,EAAQ,IAAM0E,EAAW,CAAC9D,EAAGC,KACvDkjC,EAAiBvkC,SAA6G,GAAU,GACzI,MAAMwD,EAAS,CACbpC,IACAC,KAEF8mB,EAAQ/G,sBAAsB7S,EAAWhE,GAAI/G,KAC3C,CAAC+K,EAAWhE,GAAI4d,IACdwc,EAAmBlkC,EAAY,KACnC,MAAM4lB,EAAWke,EAAiBvkC,QAClC,OAAKqmB,GAAaA,EAASqd,IAAIE,kBAGxBN,GAAUjd,EAASqd,IAAIE,mBAFrBv4B,GAGR,IACGu5B,EAAenkC,EAAY,KAC/B,MAAM+C,EAASmhC,IACfD,EAAqBlhC,EAAOpC,EAAGoC,EAAOnC,IACrC,CAACsjC,EAAkBD,IAChBG,EAAuBrkC,EAAQ,IAAM,EAAQokC,GAAe,CAACA,IAC7DE,EAAkBrkC,EAAY,KAClC,MAAM4lB,EAAWke,EAAiBvkC,QAC5BwV,EAAU6uB,GAA6Bhe,GAC3CA,GAAY7Q,GAAuH,GAAU,GAC/H6Q,EAASoC,cACbC,yBACVkc,IAGFC,KACC,CAACA,EAAsBD,IACpB3a,EAA6BxpB,EAAY,CAACspB,EAAcniB,KAC1D28B,EAAiBvkC,SAA6H,GAAU,GAC1J,MAAMsO,EAAWk2B,EAAYxkC,QACvBo0B,EAAM9lB,EAASy2B,kBACpB3Q,GAA2G,GAAU,GACtH,MAAMsP,EAxMG,CAAC54B,IAGL,CACL84B,kBAHwBP,GAAqBv4B,GAI7CoV,cAHoBujB,GAAW34B,KAsMnBk6B,CAAO5Q,GACb/N,EAAW,CACf+N,MACA7lB,aACAm1B,MACAjb,cAAe7gB,GAEjB28B,EAAiBvkC,QAAUqmB,EAC3B,MAAMzN,EAAYsR,GAAa,CAC7BkK,MACA7lB,aACAm1B,MACA3Z,eACA/Y,UAAW1C,EAAS0C,UACpB2yB,eAAgBr1B,EAASq1B,eACzBzvB,iBAAkB5F,EAAS4F,iBAC3BvH,mBAAoB2B,EAAS22B,0BAEzBj3B,EAAa01B,EAAIE,kBAQvB,OAPI51B,IACFA,EAAW8jB,aAAaxB,GAAgBD,UAAWuN,EAAWvN,WAC9DriB,EAAWhG,iBAAiB,SAAU88B,EAAiBV,GAAmB/d,EAASoC,iBAK9E7P,GACN,CAACglB,EAAWvN,UAAW9hB,EAAYu2B,EAAiBN,IACjD9Z,EAAyBjqB,EAAY,KACzC,MAAM4lB,EAAWke,EAAiBvkC,QAC5BwV,EAAU6uB,GAA6Bhe,GAE7C,OADEA,GAAY7Q,GAAyJ,GAAU,GAC1K8tB,GAAU9tB,IAChB,IACGyV,EAAcxqB,EAAY,KAC9B,MAAM4lB,EAAWke,EAAiBvkC,QACjCqmB,GAA8G,GAAU,GACzH,MAAM7Q,EAAU6uB,GAA6Bhe,GAC7Cke,EAAiBvkC,QAAU,KACtBwV,IAGLqvB,EAAqBv+B,SACrBkP,EAAQ0vB,gBAAgB5U,GAAgBD,WACxC7a,EAAQtN,oBAAoB,SAAU48B,EAAiBV,GAAmB/d,EAASoC,kBAClF,CAACqc,EAAiBD,IACfrhC,EAAS/C,EAAY2C,IACzB,MAAMijB,EAAWke,EAAiBvkC,QACjCqmB,GAA6G,GAAU,GACxH,MAAM7Q,EAAU6uB,GAA6Bhe,GAC5C7Q,GAA6H,GAAU,GACxIA,EAAQguB,WAAapgC,EAAO/B,EAC5BmU,EAAQ+tB,YAAcngC,EAAOhC,GAC5B,IACGwH,EAAYpI,EAAQ,KACjB,CACLypB,6BACAS,yBACAO,cACAznB,WAED,CAACynB,EAAahB,EAA4BS,EAAwBlnB,IAC/DyhB,EAAQzkB,EAAQ,KAAM,CAC1B0yB,WACA3kB,aACA3F,cACE,CAACA,EAAW2F,EAAY2kB,IAC5B3B,GAAgB,KACdkT,EAAuBzkC,QAAUilB,EAAM1W,WACvCsb,EAAS/b,UAAUmlB,SAAShO,GACrB,KACDsf,EAAiBvkC,SAEnBirB,IAEFpB,EAAS/b,UAAUqlB,WAAWlO,KAE/B,CAACrc,EAAW2F,EAAY0c,EAAahG,EAAOkD,EAAS0B,EAAS/b,YACjEyjB,GAAgB,KACTgT,EAAiBvkC,SAGtBmoB,EAAQ9G,yBAAyBojB,EAAuBzkC,QAAQuK,IAAKrE,EAAKy9B,iBACzE,CAACz9B,EAAKy9B,eAAgBxb,IACzBoJ,GAAgB,KACTgT,EAAiBvkC,SAGtBmoB,EAAQ7G,gCAAgCmjB,EAAuBzkC,QAAQuK,GAAIrE,EAAKgO,mBAC/E,CAAChO,EAAKgO,iBAAkBiU,GAC7B,CAEA,SAASgd,KAAQ,CACjB,MAAMC,GAAQ,CACZlkC,MAAO,EACPC,OAAQ,EACRc,OArkNgB,CAChBnB,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,IAolNFokC,GAAW,EACfC,yBACAnmB,cACAomB,cAEA,MAAMr0B,EAtBQ,GACdo0B,yBACAnmB,cACAomB,aAEID,GAGY,UAAZC,EAFKH,GAKF,CACLjkC,OAAQge,EAAY/I,OAAOrU,UAAUZ,OACrCD,MAAOie,EAAY/I,OAAOrU,UAAUb,MACpCe,OAAQkd,EAAY/I,OAAOnU,QAQhBujC,CAAQ,CACnBF,yBACAnmB,cACAomB,YAEF,MAAO,CACLtJ,QAAS9c,EAAY8c,QACrB0C,UAAW,aACXz9B,MAAOgQ,EAAKhQ,MACZC,OAAQ+P,EAAK/P,OACb2C,UAAWoN,EAAKjP,OAAOnB,IACvBiD,YAAamN,EAAKjP,OAAOlB,MACzBiD,aAAckN,EAAKjP,OAAOjB,OAC1BiD,WAAYiN,EAAKjP,OAAOhB,KACxBwkC,WAAY,IACZC,SAAU,IACV5G,cAAe,OACfzN,WAAwB,SAAZkU,EAAqB/iB,GAAYrD,YAAc,OA0D/D,IAAIwmB,GAAgB,SAvDAh8B,IAClB,MAAMi8B,GAAsB,IAAA/lC,QAAO,MAC7BgmC,EAA2BplC,EAAY,KACtCmlC,EAAoB5lC,UAGzBqlB,aAAaugB,EAAoB5lC,SACjC4lC,EAAoB5lC,QAAU,OAC7B,KACG,QACJulC,EAAO,gBACP/E,EAAe,QACfsF,EAAO,UACPzV,GACE1mB,GACG27B,EAAwBS,IAA6B,IAAArmC,UAA2B,SAAlBiK,EAAM47B,UAC3E,IAAAhlC,WAAU,IACH+kC,EAGW,SAAZC,GACFM,IACAE,GAA0B,GACnBZ,IAELS,EAAoB5lC,QACfmlC,IAETS,EAAoB5lC,QAAUglB,WAAW,KACvC4gB,EAAoB5lC,QAAU,KAC9B+lC,GAA0B,KAErBF,GAdEV,GAeR,CAACI,EAASD,EAAwBO,IACrC,MAAMG,EAAkBvlC,EAAYqI,IACP,WAAvBA,EAAMw3B,eAGVE,IACgB,UAAZ+E,GACFO,MAED,CAACP,EAASO,EAAStF,IAChB/E,EAAQ4J,GAAS,CACrBC,yBACAC,QAAS57B,EAAM47B,QACfpmB,YAAaxV,EAAMwV,cAErB,OAAO,kBAAoBxV,EAAMwV,YAAY+W,QAAS,CACpDuF,QACA,kCAAmCpL,EACnCmQ,gBAAiBwF,EACjB5R,IAAKzqB,EAAM82B,aAmEf,MAAMwF,WAAqB,kBACzB,WAAAv9B,IAAexC,GACbyC,SAASzC,GACTT,KAAK8U,MAAQ,CACX2rB,UAAWjmC,QAAQwF,KAAKkE,MAAMw8B,IAC9B1gB,KAAMhgB,KAAKkE,MAAMw8B,GACjBZ,QAAS9/B,KAAKkE,MAAM6I,eAAiB/M,KAAKkE,MAAMw8B,GAAK,OAAS,QAEhE1gC,KAAKqgC,QAAU,KACc,UAAvBrgC,KAAK8U,MAAMgrB,SAGf9/B,KAAK+D,SAAS,CACZ08B,WAAW,IAGjB,CACA,+BAAOE,CAAyBz8B,EAAO4Q,GACrC,OAAK5Q,EAAM6I,cAOP7I,EAAMw8B,GACD,CACLD,WAAW,EACXzgB,KAAM9b,EAAMw8B,GACZZ,QAAS,QAGThrB,EAAM2rB,UACD,CACLA,WAAW,EACXzgB,KAAMlL,EAAMkL,KACZ8f,QAAS,SAGN,CACLW,WAAW,EACXX,QAAS,QACT9f,KAAM,MAvBC,CACLygB,UAAWjmC,QAAQ0J,EAAMw8B,IACzB1gB,KAAM9b,EAAMw8B,GACZZ,QAAS,OAsBf,CACA,MAAA77B,GACE,IAAKjE,KAAK8U,MAAM2rB,UACd,OAAO,KAET,MAAM3F,EAAW,CACfuF,QAASrgC,KAAKqgC,QACdrgB,KAAMhgB,KAAK8U,MAAMkL,KACjB8f,QAAS9/B,KAAK8U,MAAMgrB,SAEtB,OAAO9/B,KAAKkE,MAAMC,SAAS22B,EAC7B,EA2GF,IAAI8F,GAxGc18B,IAChB,MAAMi0B,GAAa,IAAA+B,YAAW5L,IAC7B6J,GAAsG,GAAU,GACjH,MAAM,UACJvN,EAAS,kBACTpV,GACE2iB,EACE0I,GAAe,IAAAzmC,QAAO,MACtB0mC,GAAiB,IAAA1mC,QAAO,OACxB,SACJ+J,EAAQ,YACRO,EAAW,KACXhD,EAAI,KACJqR,EAAI,UACJxH,EAAS,wBACTi0B,EAAuB,eACvBtB,EAAc,iBACdzvB,EAAgB,SAChB0sB,EAAQ,SACR4F,EAAQ,wBACRC,EAAuB,qBACvBC,GACE/8B,EACEo7B,EAAkBtkC,EAAY,IAAM6lC,EAAatmC,QAAS,IAC1D2mC,EAAkBlmC,EAAY,CAACkC,EAAQ,QAC3C2jC,EAAatmC,QAAU2C,GACtB,IAEGikC,GADoBnmC,EAAY,IAAM8lC,EAAevmC,QAAS,IAC1CS,EAAY,CAACkC,EAAQ,QAC7C4jC,EAAevmC,QAAU2C,GACxB,KAnGHuxB,KAyGA,MAAM2S,EAA6BpmC,EAAY,KACzCwa,KACFwrB,EAAwB,CACtBztB,UAAWyQ,QAGd,CAACxO,EAAmBwrB,IACvBnC,GAAsB,CACpBn6B,cACAhD,OACAqR,OACAxH,YACA2yB,iBACAzvB,mBACA+wB,0BACAF,oBAEF,MAAM5lB,EAAc3e,EAAQ,IAAM,kBAAoBylC,GAAc,CAClEE,GAAIx8B,EAAMwV,YACV3M,cAAe7I,EAAMm9B,0BACpB,EACDhB,UACArgB,OACA8f,aACI,kBAAoBI,GAAe,CACvCxmB,YAAasG,EACbqgB,QAASA,EACTrF,SAAUmG,EACVrB,QAASA,EACTlV,UAAWA,EACXmQ,gBAAiBqG,KACd,CAACxW,EAAWwW,EAA4Bl9B,EAAMwV,YAAaxV,EAAMm9B,yBAA0BF,IAC1FrG,EAAW//B,EAAQ,KAAM,CAC7BigC,SAAUkG,EACVxnB,cACA4nB,eAAgB,CACd,wBAAyB58B,EACzB,gCAAiCkmB,KAEjC,CAACA,EAAWlmB,EAAagV,EAAawnB,IACpChE,EAAkB6D,EAAWA,EAASngB,SAAS5b,YAAc,KAC7Du8B,EAAmBxmC,EAAQ,KAAM,CACrC2J,cACAhD,OACAw7B,oBACE,CAACx4B,EAAaw4B,EAAiBx7B,IAmBnC,OAAO,kBAAoBq4B,GAAiB1B,SAAU,CACpDn7B,MAAOqkC,GACNp9B,EAAS22B,EAAUK,GApBtB,WACE,IAAK4F,EACH,OAAO,KAET,MAAM,SACJngB,EAAQ,OACR3c,GACE88B,EACES,EAAO,kBAAoBvE,GAAkB,CACjDj4B,YAAa4b,EAAS5b,YACtBX,MAAOuc,EAASrc,OAAOF,MACvBk2B,SAAS,EACTnpB,WAAW,EACX4e,yBAAyB,EACzB8B,4BAA4B,GAC3B,CAAC2P,EAAmBC,IAAsBz9B,EAAOw9B,EAAmBC,EAAmB9gB,IAC1F,OAAO,iBAAsB4gB,EAAMP,IACrC,CAGiCU,KAQnC,MAAMC,GAAe,CACnB7uB,KAAM,WACNrR,KAAM,UACN6J,UAAW,WACX2yB,gBAAgB,EAChBzvB,kBAAkB,EAClB+wB,yBAAyB,EACzBqC,YAAa,KACbZ,qBAZF,WAEE,OADCriB,SAASkP,MAAgG,GAAU,GAC7GlP,SAASkP,IAClB,GAWMgU,GAA+B5F,IACnC,IAGI6F,EAHAC,EAAc,IACb9F,GAGL,IAAK6F,KAAkBH,QACYvkB,IAA7B6e,EAAS6F,KACXC,EAAc,IACTA,EACH,CAACD,GAAiBH,GAAaG,KAIrC,OAAOC,GAEHC,GAAiB,CAACvgC,EAAM0T,IAAa1T,IAAS0T,EAAS/M,UAAU3G,KACjEwgC,GAAe,CAAC9sB,EAAUH,IAAeA,EAAWjM,WAAWoM,EAAS9L,UAAUxE,IA4GlFq9B,GAAqB,CACzBnB,wBAv2J8BvgC,IAAQ,CACtCiB,KAAM,6BACN4Y,QAAS7Z,KAi3JX,IAAI2hC,IAVuB,IAAAvG,SA9GC,KAC1B,MAAMwG,EAAoB,CACxB3oB,YAAa,KACb2nB,0BAA0B,EAC1BlG,SAAU,CACRmH,gBAAgB,EAChBC,iBAAkB,KAClBC,qBAAsB,KACtBC,oBAAoB,GAEtB1B,SAAU,MAEN2B,EAAuB,IACxBL,EACHhB,0BAA0B,GAEtBsB,EAAqBljC,EAAWqJ,IAAc,CAClD9D,YAAa8D,EAAWhE,GACxBpD,KAAMoH,EAAWpH,KACjB6C,OAAQ,CACNF,MAAOyE,EAAWzE,MAClBK,YAAaoE,EAAWpE,gBAGtBk+B,EAAcnjC,EAAW,CAACqF,EAAIsM,EAAWyxB,EAA2BC,EAAyBliB,EAAUihB,KAC3G,MAAM78B,EAAc4b,EAAS9X,WAAWhE,GAExC,GADe8b,EAAS9X,WAAWpE,cAAgBI,EACvC,CACV,MAAMi8B,EAAWc,EAAc,CAC7B59B,OAAQ49B,EACRjhB,SAAU+hB,EAAmB/hB,EAAS9X,aACpC,KACEqyB,EAAW,CACfmH,eAAgBO,EAChBN,iBAAkBM,EAA4B79B,EAAc,KAC5Dw9B,qBAAsBx9B,EACtBy9B,oBAAoB,GAEtB,MAAO,CACL/oB,YAAakH,EAASlH,YACtB2nB,0BAA0B,EAC1BlG,WACA4F,WAEJ,CACA,IAAK3vB,EACH,OAAOsxB,EAET,IAAKI,EACH,OAAOT,EAET,MAAMlH,EAAW,CACfmH,eAAgBO,EAChBN,iBAAkBv9B,EAClBw9B,qBAAsB,KACtBC,oBAAoB,GAEtB,MAAO,CACL/oB,YAAakH,EAASlH,YACtB2nB,0BAA0B,EAC1BlG,WACA4F,SAAU,QA4Cd,MAzCiB,CAACjsB,EAAOonB,KACvB,MAAM6G,EAA2BjB,GAA6B5F,GACxDp3B,EAAKi+B,EAAyBr+B,YAC9BhD,EAAOqhC,EAAyBrhC,KAChC0P,GAAa2xB,EAAyB7E,eACtC2D,EAAckB,EAAyBlB,YAC7C,GAAIt+B,GAAWuR,GAAQ,CACrB,MAAMM,EAAWN,EAAMM,SACvB,IAAK6sB,GAAevgC,EAAM0T,GACxB,OAAOstB,EAET,MAAM9hB,EAAWshB,GAAa9sB,EAAUN,EAAMG,YACxCqtB,EAAiB1tB,GAAkBE,EAAMnL,UAAY7E,EAC3D,OAAO89B,EAAY99B,EAAIsM,EAAWkxB,EAAgBA,EAAgB1hB,EAAUihB,EAC9E,CACA,GAAoB,mBAAhB/sB,EAAMW,MAA4B,CACpC,MAAMyE,EAAYpF,EAAMoF,UACxB,IAAK+nB,GAAevgC,EAAMwY,EAAU9E,UAClC,OAAOstB,EAET,MAAM9hB,EAAWshB,GAAahoB,EAAU9E,SAAUN,EAAMG,YACxD,OAAO2tB,EAAY99B,EAAIsM,EAAWiqB,GAA4BnhB,EAAUhgB,UAAY4K,EAAI8P,GAAkBsF,EAAUvQ,UAAY7E,EAAI8b,EAAUihB,EAChJ,CACA,GAAoB,SAAhB/sB,EAAMW,OAAoBX,EAAMoF,YAAcpF,EAAMqF,YAAa,CACnE,MAAMD,EAAYpF,EAAMoF,UACxB,IAAK+nB,GAAevgC,EAAMwY,EAAU9E,UAClC,OAAOstB,EAET,MAAMxnB,EAAUtG,GAAkBsF,EAAUvQ,UAAY7E,EAClDk+B,EAAexoC,QAAQ0f,EAAUvQ,OAAOC,IAAmC,YAA7BsQ,EAAUvQ,OAAOC,GAAGlI,MAClEuhC,EAAS/oB,EAAU9E,SAAS/M,UAAUvD,KAAOA,EACnD,OAAIoW,EACK8nB,EAAeX,EAAoBK,EAExCO,EACKZ,EAEFK,CACT,CACA,OAAOA,IAO6CP,GAAoB,CAACe,EAAYC,EAAejH,KAC/F,IACF4F,GAA6B5F,MAC7BgH,KACAC,IAEJ,CACD7X,QAASsC,GACTmP,mBAAoB3B,IARK,CASxBwF,G,mDCj0OKwC,E,8BCDJC,EAAsC,WAStC,OARAA,EAAWriC,OAAOC,QAAU,SAASI,GACjC,IAAK,IAAIiiC,EAAG1oC,EAAI,EAAGuG,EAAIrB,UAAUnF,OAAQC,EAAIuG,EAAGvG,IAE5C,IAAK,IAAI2oC,KADTD,EAAIxjC,UAAUlF,GACOoG,OAAO8B,UAAUvB,eAAeC,KAAK8hC,EAAGC,KACzDliC,EAAEkiC,GAAKD,EAAEC,IAEjB,OAAOliC,CACX,EACOgiC,EAASljC,MAAMH,KAAMF,UAChC,EAGI0jC,EAAc,CACd/nC,MAAO,OACPC,OAAQ,OACRL,IAAK,MACLG,KAAM,MACNioC,OAAQ,cAERC,EAAc,CACdjoC,MAAO,OACPC,OAAQ,OACRL,IAAK,MACLG,KAAM,MACNioC,OAAQ,cAERE,EAAW,CACXloC,MAAO,OACPC,OAAQ,OACR0I,SAAU,WACVg1B,OAAQ,GAERh7B,EAAS,CACT/C,IAAKgoC,EAASA,EAAS,CAAC,EAAGG,GAAc,CAAEnoC,IAAK,SAChDC,MAAO+nC,EAASA,EAAS,CAAC,EAAGK,GAAc,CAAEloC,UAAM6hB,EAAW/hB,MAAO,SACrEC,OAAQ8nC,EAASA,EAAS,CAAC,EAAGG,GAAc,CAAEnoC,SAAKgiB,EAAW9hB,OAAQ,SACtEC,KAAM6nC,EAASA,EAAS,CAAC,EAAGK,GAAc,CAAEloC,KAAM,SAClDooC,SAAUP,EAASA,EAAS,CAAC,EAAGM,GAAW,CAAEroC,MAAO,QAASD,IAAK,QAASooC,OAAQ,cACnFI,YAAaR,EAASA,EAAS,CAAC,EAAGM,GAAW,CAAEroC,MAAO,QAASC,OAAQ,QAASkoC,OAAQ,cACzFK,WAAYT,EAASA,EAAS,CAAC,EAAGM,GAAW,CAAEnoC,KAAM,QAASD,OAAQ,QAASkoC,OAAQ,cACvFM,QAASV,EAASA,EAAS,CAAC,EAAGM,GAAW,CAAEnoC,KAAM,QAASH,IAAK,QAASooC,OAAQ,eAE1EO,GAAU,IAAAC,MAAK,SAAU//B,GAChC,IAAIggC,EAAgBhgC,EAAMggC,cAAe34B,EAAYrH,EAAMqH,UAAWpH,EAAWD,EAAMC,SAAUggC,EAAgBjgC,EAAMigC,cAAeC,EAAYlgC,EAAMkgC,UACpJC,GAAc,IAAArpC,aAAY,SAAUoG,GACpC8iC,EAAc9iC,EAAGmK,EACrB,EAAG,CAAC24B,EAAe34B,IACf+4B,GAAe,IAAAtpC,aAAY,SAAUoG,GACrC8iC,EAAc9iC,EAAGmK,EACrB,EAAG,CAAC24B,EAAe34B,IACfyqB,GAAQ,IAAAj7B,SAAQ,WAChB,OAAOsoC,EAASA,EAAS,CAAEj/B,SAAU,WAAYmgC,WAAY,QAAUnmC,EAAOmN,IAAc44B,QAAqDA,EAAgB,CAAC,EACtK,EAAG,CAACA,EAAe54B,IACnB,OAAQ,SAAK,MAAO,CAAE64B,UAAWA,QAAa/mB,EAAW2Y,MAAOA,EAAOqO,YAAaA,EAAaC,aAAcA,EAAcngC,SAAUA,GAC3I,GDvDIqgC,GACIpB,EAAgB,SAAU1xB,EAAGjI,GAI7B,OAHA25B,EAAgBpiC,OAAOyjC,gBAClB,CAAEC,UAAW,cAAgBhkC,OAAS,SAAUgR,EAAGjI,GAAKiI,EAAEgzB,UAAYj7B,CAAG,GAC1E,SAAUiI,EAAGjI,GAAK,IAAK,IAAI85B,KAAK95B,EAAOzI,OAAO8B,UAAUvB,eAAeC,KAAKiI,EAAG85B,KAAI7xB,EAAE6xB,GAAK95B,EAAE85B,GAAI,EAC7FH,EAAc1xB,EAAGjI,EAC5B,EACO,SAAUiI,EAAGjI,GAChB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAIk7B,UAAU,uBAAyBC,OAAOn7B,GAAK,iCAE7D,SAASo7B,IAAO7kC,KAAKiD,YAAcyO,CAAG,CADtC0xB,EAAc1xB,EAAGjI,GAEjBiI,EAAE5O,UAAkB,OAAN2G,EAAazI,OAAO+zB,OAAOtrB,IAAMo7B,EAAG/hC,UAAY2G,EAAE3G,UAAW,IAAI+hC,EACnF,GAEA,EAAsC,WAStC,OARA,EAAW7jC,OAAOC,QAAU,SAASI,GACjC,IAAK,IAAIiiC,EAAG1oC,EAAI,EAAGuG,EAAIrB,UAAUnF,OAAQC,EAAIuG,EAAGvG,IAE5C,IAAK,IAAI2oC,KADTD,EAAIxjC,UAAUlF,GACOoG,OAAO8B,UAAUvB,eAAeC,KAAK8hC,EAAGC,KACzDliC,EAAEkiC,GAAKD,EAAEC,IAEjB,OAAOliC,CACX,EACO,EAASlB,MAAMH,KAAMF,UAChC,EAKIglC,EAAe,CACfrpC,MAAO,OACPC,OAAQ,QAERqpC,EAAQ,SAAU5jC,EAAGyF,EAAKQ,GAAO,OAAOZ,KAAKY,IAAIZ,KAAKI,IAAIzF,EAAGiG,GAAMR,EAAM,EACzEqW,EAAO,SAAU9b,EAAGsK,EAAMu5B,GAC1B,IAAIC,EAAIz+B,KAAK0+B,MAAM/jC,EAAIsK,GACvB,OAAOw5B,EAAIx5B,EAAOu5B,GAAWC,EAAI,EACrC,EACIE,EAAe,SAAUC,EAAKrpC,GAC9B,OAAO,IAAIspC,OAAOD,EAAK,KAAKE,KAAKvpC,EACrC,EAEIwpC,EAAe,SAAUliC,GACzB,OAAO7I,QAAQ6I,EAAMywB,SAAWzwB,EAAMywB,QAAQn5B,OAClD,EAKI6qC,EAAkB,SAAUrkC,EAAGskC,EAAWC,QAC1B,IAAZA,IAAsBA,EAAU,GACpC,IAAIC,EAAkBF,EAAU78B,OAAO,SAAUg9B,EAAMC,EAAMxhC,GAAS,OAAQmC,KAAKqpB,IAAIgW,EAAO1kC,GAAKqF,KAAKqpB,IAAI4V,EAAUG,GAAQzkC,GAAKkD,EAAQuhC,CAAO,EAAG,GACjJE,EAAMt/B,KAAKqpB,IAAI4V,EAAUE,GAAmBxkC,GAChD,OAAmB,IAAZukC,GAAiBI,EAAMJ,EAAUD,EAAUE,GAAmBxkC,CACzE,EACI4kC,EAAgB,SAAU5kC,GAE1B,MAAU,UADVA,EAAIA,EAAE4B,aAIF5B,EAAE6kC,SAAS,OAGX7kC,EAAE6kC,SAAS,MAGX7kC,EAAE6kC,SAAS,OAGX7kC,EAAE6kC,SAAS,OAGX7kC,EAAE6kC,SAAS,SAGX7kC,EAAE6kC,SAAS,QAjBJ7kC,EAoBJ,GAAG8kC,OAAO9kC,EAAG,KACxB,EACI+kC,EAAe,SAAUz6B,EAAM06B,EAAYC,EAAYC,GACvD,GAAI56B,GAAwB,iBAATA,EAAmB,CAClC,GAAIA,EAAKu6B,SAAS,MACd,OAAO5oC,OAAOqO,EAAK66B,QAAQ,KAAM,KAErC,GAAI76B,EAAKu6B,SAAS,KAEd,OAAOG,GADK/oC,OAAOqO,EAAK66B,QAAQ,IAAK,KAAO,KAGhD,GAAI76B,EAAKu6B,SAAS,MAEd,OAAOI,GADKhpC,OAAOqO,EAAK66B,QAAQ,KAAM,KAAO,KAGjD,GAAI76B,EAAKu6B,SAAS,MAEd,OAAOK,GADKjpC,OAAOqO,EAAK66B,QAAQ,KAAM,KAAO,IAGrD,CACA,OAAO76B,CACX,EAoBI86B,EAAe,CACf,KACA,MACA,QACA,YACA,OACA,UACA,OACA,SACA,oBACA,OACA,cACA,WACA,YACA,WACA,YACA,kBACA,4BACA,6BACA,SACA,eACA,gBACA,qBACA,qBACA,WACA,gBACA,WACA,eACA,kBACA,QACA,cACA,WAGAC,EAAgB,qBAChBC,EAA2B,SAAUC,GAErC,SAASD,EAAUviC,GACf,IAAIyiC,EAAIC,EAAIC,EAAIC,EACZC,EAAQL,EAAOllC,KAAKxB,KAAMkE,IAAUlE,KA8ExC,OA7EA+mC,EAAMC,MAAQ,EACdD,EAAME,UAAY,KAElBF,EAAMG,WAAa,EACnBH,EAAMI,UAAY,EAElBJ,EAAMK,cAAgB,EACtBL,EAAMM,eAAiB,EACvBN,EAAMO,aAAe,EACrBP,EAAMQ,gBAAkB,EAExBR,EAAMS,WAAa,EACnBT,EAAMU,UAAY,EAClBV,EAAMW,MAAQ,CACVjsC,MAAO,EACPC,OAAQ,GAEZqrC,EAAMY,WAAa,WACf,IAAKZ,EAAME,YAAcF,EAAM/oC,OAC3B,OAAO,KAEX,IAAIuyB,EAASwW,EAAMxa,WACnB,IAAKgE,EACD,OAAO,KAEX,IAAIqX,EAAUb,EAAM/oC,OAAO4gB,SAASmN,cAAc,OAclD,OAbA6b,EAAQ5R,MAAMv6B,MAAQ,OACtBmsC,EAAQ5R,MAAMt6B,OAAS,OACvBksC,EAAQ5R,MAAM5xB,SAAW,WACzBwjC,EAAQ5R,MAAMiD,UAAY,cAC1B2O,EAAQ5R,MAAMx6B,KAAO,IACrBosC,EAAQ5R,MAAM6R,KAAO,WACjBD,EAAQE,UACRF,EAAQE,UAAUjiC,IAAI2gC,GAGtBoB,EAAQxD,WAAaoC,EAEzBjW,EAAO0F,YAAY2R,GACZA,CACX,EACAb,EAAMgB,WAAa,SAAUpd,GACzB,IAAI4F,EAASwW,EAAMxa,WACdgE,GAGLA,EAAO2F,YAAYvL,EACvB,EACAoc,EAAMjyB,MAAQ,CACVkzB,YAAY,EACZvsC,MAAuF,QAA/EmrC,EAAgC,QAA1BD,EAAKI,EAAMkB,iBAA8B,IAAPtB,OAAgB,EAASA,EAAGlrC,aAA0B,IAAPmrC,EAAgBA,EAAK,OACpHlrC,OAAyF,QAAhForC,EAAgC,QAA1BD,EAAKE,EAAMkB,iBAA8B,IAAPpB,OAAgB,EAASA,EAAGnrC,cAA2B,IAAPorC,EAAgBA,EAAK,OACtHv7B,UAAW,QACX7N,SAAU,CACN/B,EAAG,EACHC,EAAG,EACHH,MAAO,EACPC,OAAQ,GAEZwsC,gBAAiB,CACbxsC,OAAQ,OACRD,MAAO,OACP0sC,gBAAiB,gBACjB1E,OAAQ,OACRtK,QAAS,EACT/0B,SAAU,QACVg1B,OAAQ,KACR/9B,IAAK,IACLG,KAAM,IACND,OAAQ,IACRD,MAAO,KAEX8sC,eAAW/qB,GAEf0pB,EAAM7C,cAAgB6C,EAAM7C,cAAchjC,KAAK6lC,GAC/CA,EAAMsB,YAActB,EAAMsB,YAAYnnC,KAAK6lC,GAC3CA,EAAMuB,UAAYvB,EAAMuB,UAAUpnC,KAAK6lC,GAChCA,CACX,CA8hBA,OAhnBAvC,EAAUiC,EAAWC,GAmFrB1lC,OAAOunC,eAAe9B,EAAU3jC,UAAW,aAAc,CACrD0lC,IAAK,WACD,OAAKxoC,KAAKinC,UAGHjnC,KAAKinC,UAAU1a,WAFX,IAGf,EACAkc,YAAY,EACZC,cAAc,IAElB1nC,OAAOunC,eAAe9B,EAAU3jC,UAAW,SAAU,CACjD0lC,IAAK,WACD,OAAKxoC,KAAKinC,WAGLjnC,KAAKinC,UAAUva,cAGb1sB,KAAKinC,UAAUva,cAAcC,YALzB,IAMf,EACA8b,YAAY,EACZC,cAAc,IAElB1nC,OAAOunC,eAAe9B,EAAU3jC,UAAW,YAAa,CACpD0lC,IAAK,WACD,OAAOxoC,KAAKkE,MAAMuH,MAAQzL,KAAKkE,MAAMykC,aAAe7D,CACxD,EACA2D,YAAY,EACZC,cAAc,IAElB1nC,OAAOunC,eAAe9B,EAAU3jC,UAAW,OAAQ,CAC/C0lC,IAAK,WACD,IAAI/sC,EAAQ,EACRC,EAAS,EACb,GAAIsE,KAAKinC,WAAajnC,KAAKhC,OAAQ,CAC/B,IAAI4qC,EAAW5oC,KAAKinC,UAAU4B,YAC1BC,EAAY9oC,KAAKinC,UAAU8B,aAG3BC,EAAchpC,KAAKinC,UAAUjR,MAAM5xB,SACnB,aAAhB4kC,IACAhpC,KAAKinC,UAAUjR,MAAM5xB,SAAW,YAGpC3I,EAAuC,SAA/BuE,KAAKinC,UAAUjR,MAAMv6B,MAAmBuE,KAAKinC,UAAU4B,YAAcD,EAC7EltC,EAAyC,SAAhCsE,KAAKinC,UAAUjR,MAAMt6B,OAAoBsE,KAAKinC,UAAU8B,aAAeD,EAEhF9oC,KAAKinC,UAAUjR,MAAM5xB,SAAW4kC,CACpC,CACA,MAAO,CAAEvtC,MAAOA,EAAOC,OAAQA,EACnC,EACA+sC,YAAY,EACZC,cAAc,IAElB1nC,OAAOunC,eAAe9B,EAAU3jC,UAAW,YAAa,CACpD0lC,IAAK,WACD,IAAIzB,EAAQ/mC,KACRyL,EAAOzL,KAAKkE,MAAMuH,KAClBs0B,EAAU,SAAUhnB,GACpB,IAAI4tB,EACJ,QAAgC,IAArBI,EAAMjyB,MAAMiE,IAA6C,SAArBguB,EAAMjyB,MAAMiE,GACvD,MAAO,OAEX,GAAIguB,EAAMkB,WAAalB,EAAMkB,UAAUlvB,KAAyC,QAA/B4tB,EAAKI,EAAMkB,UAAUlvB,UAAyB,IAAP4tB,OAAgB,EAASA,EAAG5jC,WAAWijC,SAAS,MAAO,CAC3I,GAAIe,EAAMjyB,MAAMiE,GAAKhW,WAAWijC,SAAS,KACrC,OAAOe,EAAMjyB,MAAMiE,GAAKhW,WAE5B,IAAIojC,EAAaY,EAAMkC,gBAEnBC,EADQ9rC,OAAO2pC,EAAMjyB,MAAMiE,GAAKhW,WAAWujC,QAAQ,KAAM,KACtCH,EAAWptB,GAAQ,IAC1C,MAAO,GAAGktB,OAAOiD,EAAS,IAC9B,CACA,OAAOnD,EAAcgB,EAAMjyB,MAAMiE,GACrC,EAOA,MAAO,CAAEtd,MANGgQ,QAA8B,IAAfA,EAAKhQ,QAA0BuE,KAAK8U,MAAMkzB,WAC/DjC,EAAct6B,EAAKhQ,OACnBskC,EAAQ,SAISrkC,OAHV+P,QAA+B,IAAhBA,EAAK/P,SAA2BsE,KAAK8U,MAAMkzB,WACjEjC,EAAct6B,EAAK/P,QACnBqkC,EAAQ,UAElB,EACA0I,YAAY,EACZC,cAAc,IAElBjC,EAAU3jC,UAAUmmC,cAAgB,WAChC,IAAKjpC,KAAKusB,WACN,OAAKvsB,KAAKhC,OAGH,CAAEvC,MAAOuE,KAAKhC,OAAOooC,WAAY1qC,OAAQsE,KAAKhC,OAAOqoC,aAFjD,CAAE5qC,MAAO,EAAGC,OAAQ,GAInC,IAAIivB,EAAO3qB,KAAK2nC,aAChB,IAAKhd,EACD,MAAO,CAAElvB,MAAO,EAAGC,OAAQ,GAG/B,IAAIytC,GAAc,EACdC,EAAOppC,KAAKusB,WAAWyJ,MAAMqT,SACpB,SAATD,IACAD,GAAc,EACdnpC,KAAKusB,WAAWyJ,MAAMqT,SAAW,QAGrC1e,EAAKqL,MAAM5xB,SAAW,WACtBumB,EAAKqL,MAAMsT,SAAW,OACtB3e,EAAKqL,MAAMuT,UAAY,OACvB,IAAI99B,EAAO,CACPhQ,MAAOkvB,EAAKke,YACZntC,OAAQivB,EAAKoe,cAMjB,OAJII,IACAnpC,KAAKusB,WAAWyJ,MAAMqT,SAAWD,GAErCppC,KAAK+nC,WAAWpd,GACTlf,CACX,EACAg7B,EAAU3jC,UAAUjB,WAAa,WACzB7B,KAAKhC,SACLgC,KAAKhC,OAAOuE,iBAAiB,UAAWvC,KAAKsoC,WAC7CtoC,KAAKhC,OAAOuE,iBAAiB,YAAavC,KAAKqoC,aAC/CroC,KAAKhC,OAAOuE,iBAAiB,aAAcvC,KAAKsoC,WAChDtoC,KAAKhC,OAAOuE,iBAAiB,YAAavC,KAAKqoC,YAAa,CACxD1pB,SAAS,EACTD,SAAS,IAEb1e,KAAKhC,OAAOuE,iBAAiB,WAAYvC,KAAKsoC,WAEtD,EACA7B,EAAU3jC,UAAU0mC,aAAe,WAC3BxpC,KAAKhC,SACLgC,KAAKhC,OAAOyE,oBAAoB,UAAWzC,KAAKsoC,WAChDtoC,KAAKhC,OAAOyE,oBAAoB,YAAazC,KAAKqoC,aAClDroC,KAAKhC,OAAOyE,oBAAoB,aAAczC,KAAKsoC,WACnDtoC,KAAKhC,OAAOyE,oBAAoB,YAAazC,KAAKqoC,aAAa,GAC/DroC,KAAKhC,OAAOyE,oBAAoB,WAAYzC,KAAKsoC,WAEzD,EACA7B,EAAU3jC,UAAUc,kBAAoB,WACpC,GAAK5D,KAAKinC,WAAcjnC,KAAKhC,OAA7B,CAGA,IAAIyrC,EAAgBzpC,KAAKhC,OAAOoB,iBAAiBY,KAAKinC,WACtDjnC,KAAK+D,SAAS,CACVtI,MAAOuE,KAAK8U,MAAMrZ,OAASuE,KAAKyL,KAAKhQ,MACrCC,OAAQsE,KAAK8U,MAAMpZ,QAAUsE,KAAKyL,KAAK/P,OACvC0sC,UAAuC,SAA5BqB,EAAcrB,UAAuBqB,EAAcrB,eAAY/qB,GAL9E,CAOJ,EACAopB,EAAU3jC,UAAUkB,qBAAuB,WACnChE,KAAKhC,QACLgC,KAAKwpC,cAEb,EACA/C,EAAU3jC,UAAU4mC,yBAA2B,SAAUC,EAASC,GAC9D,IAAI3B,EAAYjoC,KAAKioC,WAAajoC,KAAKioC,UAAU2B,GACjD,MAA4B,SAArB5pC,KAAK8U,MAAM80B,IACd5pC,KAAK8U,MAAMpX,SAASksC,KAAUD,QACR,IAAd1B,GAA2C,SAAdA,EAEnC0B,EADA,MAEV,EACAlD,EAAU3jC,UAAU+mC,4BAA8B,SAAUC,EAAUC,GAClE,IAIIC,EACAC,EALAC,EAAoBlqC,KAAKkE,MAAMgmC,kBAC/B3+B,EAAYvL,KAAK8U,MAAMvJ,UACvB4+B,EAAmBD,GAAqB/E,EAAa,OAAQ55B,GAC7D6+B,EAAoBF,GAAqB/E,EAAa,MAAO55B,GAGjE,GAA0B,WAAtBvL,KAAKkE,MAAMmmC,OAAqB,CAChC,IAAIC,EAAWtqC,KAAKusB,WAChB+d,IACAN,EAAaG,EACPnqC,KAAKqnC,eAAiBrnC,KAAKknC,WAC3BoD,EAASzB,aAAe7oC,KAAKknC,WAAalnC,KAAKonC,eACrD6C,EAAcG,EACRpqC,KAAKunC,gBAAkBvnC,KAAKmnC,UAC5BmD,EAASvB,cAAgB/oC,KAAKmnC,UAAYnnC,KAAKsnC,cAE7D,KAC+B,WAAtBtnC,KAAKkE,MAAMmmC,OACZrqC,KAAKhC,SACLgsC,EAAaG,EAAmBnqC,KAAKqnC,eAAiBrnC,KAAKhC,OAAOooC,WAAapmC,KAAKonC,cACpF6C,EAAcG,EAAoBpqC,KAAKunC,gBAAkBvnC,KAAKhC,OAAOqoC,YAAcrmC,KAAKsnC,cAGvFtnC,KAAKkE,MAAMmmC,SAChBL,EAAaG,EACPnqC,KAAKqnC,eAAiBrnC,KAAKwnC,WAC3BxnC,KAAKkE,MAAMmmC,OAAOxB,aAAe7oC,KAAKwnC,WAAaxnC,KAAKonC,eAC9D6C,EAAcG,EACRpqC,KAAKunC,gBAAkBvnC,KAAKynC,UAC5BznC,KAAKkE,MAAMmmC,OAAOtB,cAAgB/oC,KAAKynC,UAAYznC,KAAKsnC,eAQlE,OANI0C,GAAc5sC,OAAOmtC,SAASP,KAC9BF,EAAWA,GAAYA,EAAWE,EAAaF,EAAWE,GAE1DC,GAAe7sC,OAAOmtC,SAASN,KAC/BF,EAAYA,GAAaA,EAAYE,EAAcF,EAAYE,GAE5D,CAAEH,SAAUA,EAAUC,UAAWA,EAC5C,EACAtD,EAAU3jC,UAAU0nC,8BAAgC,SAAU/a,EAASC,GACnE,IAnUwB+a,EAmUpBpO,EAAQr8B,KAAKkE,MAAMm4B,OAAS,EAC5BsK,GApUoB8D,EAoUCzqC,KAAKkE,MAAMwmC,aAAe,EApUZhqC,MAAMiqC,QAAQF,GAAOA,EAAM,CAACA,EAAKA,IAoUjBG,EAAejE,EAAG,GAAIkE,EAAelE,EAAG,GAC3FC,EAAK5mC,KAAK8U,MAAOvJ,EAAYq7B,EAAGr7B,UAAW7N,EAAWkpC,EAAGlpC,SACzDmpC,EAAK7mC,KAAKkE,MAAO4mC,EAAkBjE,EAAGiE,gBAAiBC,EAA6BlE,EAAGkE,2BAA4BC,EAA4BnE,EAAGmE,0BAClJC,EAAWvtC,EAASjC,MACpByvC,EAAYxtC,EAAShC,OACrByvC,EAAcJ,GAA8B,EAC5CK,EAAaJ,GAA6B,EAyB9C,OAxBI7F,EAAa,QAAS55B,KACtB0/B,EAAWvtC,EAASjC,OAAUg0B,EAAU/xB,EAAS/B,GAAKivC,EAAgBvO,EAClEyO,IACAI,GAAaD,EAAWG,GAAcprC,KAAKgnC,MAAQmE,IAGvDhG,EAAa,OAAQ55B,KACrB0/B,EAAWvtC,EAASjC,OAAUg0B,EAAU/xB,EAAS/B,GAAKivC,EAAgBvO,EAClEyO,IACAI,GAAaD,EAAWG,GAAcprC,KAAKgnC,MAAQmE,IAGvDhG,EAAa,SAAU55B,KACvB2/B,EAAYxtC,EAAShC,QAAWg0B,EAAUhyB,EAAS9B,GAAKivC,EAAgBxO,EACpEyO,IACAG,GAAYC,EAAYC,GAAenrC,KAAKgnC,MAAQoE,IAGxDjG,EAAa,MAAO55B,KACpB2/B,EAAYxtC,EAAShC,QAAWg0B,EAAUhyB,EAAS9B,GAAKivC,EAAgBxO,EACpEyO,IACAG,GAAYC,EAAYC,GAAenrC,KAAKgnC,MAAQoE,IAGrD,CAAEH,SAAUA,EAAUC,UAAWA,EAC5C,EACAzE,EAAU3jC,UAAUuoC,gCAAkC,SAAUJ,EAAUC,EAAW9jC,EAAKR,GACtF,IAAI+/B,EAAK3mC,KAAKkE,MAAO4mC,EAAkBnE,EAAGmE,gBAAiBC,EAA6BpE,EAAGoE,2BAA4BC,EAA4BrE,EAAGqE,0BAClJM,OAAwC,IAAd1kC,EAAInL,MAAwB,GAAKmL,EAAInL,MAC/D8vC,OAAwC,IAAdnkC,EAAI3L,OAAyB2L,EAAI3L,MAAQ,EAAIwvC,EAAW7jC,EAAI3L,MACtF+vC,OAA0C,IAAf5kC,EAAIlL,OAAyB,GAAKkL,EAAIlL,OACjE+vC,OAA0C,IAAfrkC,EAAI1L,QAA0B0L,EAAI1L,OAAS,EAAIwvC,EAAY9jC,EAAI1L,OAC1FyvC,EAAcJ,GAA8B,EAC5CK,EAAaJ,GAA6B,EAC9C,GAAIF,EAAiB,CACjB,IAAIY,GAAiBF,EAAoBL,GAAenrC,KAAKgnC,MAAQoE,EACjEO,GAAiBF,EAAoBN,GAAenrC,KAAKgnC,MAAQoE,EACjEQ,GAAkBN,EAAmBF,GAAcprC,KAAKgnC,MAAQmE,EAChEU,GAAkBN,EAAmBH,GAAcprC,KAAKgnC,MAAQmE,EAChEW,EAAiBtlC,KAAKY,IAAIkkC,EAAkBI,GAC5CK,EAAiBvlC,KAAKI,IAAI2kC,EAAkBI,GAC5CK,EAAkBxlC,KAAKY,IAAIokC,EAAmBI,GAC9CK,EAAkBzlC,KAAKI,IAAI6kC,EAAmBI,GAClDZ,EAAWlG,EAAMkG,EAAUa,EAAgBC,GAC3Cb,EAAYnG,EAAMmG,EAAWc,EAAiBC,EAClD,MAEIhB,EAAWlG,EAAMkG,EAAUK,EAAkBC,GAC7CL,EAAYnG,EAAMmG,EAAWM,EAAmBC,GAEpD,MAAO,CAAER,SAAUA,EAAUC,UAAWA,EAC5C,EACAzE,EAAU3jC,UAAUopC,sBAAwB,WACxC,IAAIC,EAAgB,GAAKnsC,KAAKkE,MAAMm4B,OAAS,GAE7C,GAA0B,WAAtBr8B,KAAKkE,MAAMmmC,OAAqB,CAChC,IAAI+B,EAAWpsC,KAAKusB,WACpB,GAAI6f,EAAU,CACV,IAAIC,EAAaD,EAASjtC,wBAC1Ba,KAAKknC,WAAamF,EAAW7wC,KAAO2wC,EACpCnsC,KAAKmnC,UAAYkF,EAAWhxC,IAAM8wC,CACtC,CACJ,CAEA,GAAInsC,KAAKkE,MAAMmmC,QAAuC,iBAAtBrqC,KAAKkE,MAAMmmC,OAAqB,CAC5D,IAAI1zB,EAAa3W,KAAKkE,MAAMmmC,OAAOlrC,wBACnCa,KAAKwnC,WAAa7wB,EAAWnb,KAAO2wC,EACpCnsC,KAAKynC,UAAY9wB,EAAWtb,IAAM8wC,CACtC,CAEA,GAAInsC,KAAKinC,UAAW,CAChB,IAAIN,EAAK3mC,KAAKinC,UAAU9nC,wBAAyB3D,EAAOmrC,EAAGnrC,KAAM8wC,EAAQ3F,EAAGtrC,IAAKC,EAAQqrC,EAAGrrC,MAAOC,EAASorC,EAAGprC,OAC/GyE,KAAKonC,cAAgB5rC,EAAO2wC,EAC5BnsC,KAAKqnC,eAAiB/rC,EAAQ6wC,EAC9BnsC,KAAKsnC,aAAegF,EAAQH,EAC5BnsC,KAAKunC,gBAAkBhsC,EAAS4wC,CACpC,CACJ,EACA1F,EAAU3jC,UAAUohC,cAAgB,SAAU7gC,EAAOkI,GACjD,GAAKvL,KAAKinC,WAAcjnC,KAAKhC,OAA7B,CAGA,IA8BIoqC,EA9BA3Y,EAAU,EACVC,EAAU,EASd,GARIrsB,EAAMkpC,aAxeC,SAAUlpC,GACzB,OAAO7I,SAAS6I,EAAMosB,SAA6B,IAAlBpsB,EAAMosB,WAClCpsB,EAAMqsB,SAA6B,IAAlBrsB,EAAMqsB,SAChC,CAqeiC8c,CAAanpC,EAAMkpC,cACxC9c,EAAUpsB,EAAMkpC,YAAY9c,QAC5BC,EAAUrsB,EAAMkpC,YAAY7c,SAEvBrsB,EAAMkpC,aAAehH,EAAaliC,EAAMkpC,eAC7C9c,EAAUpsB,EAAMkpC,YAAYzY,QAAQ,GAAGrE,QACvCC,EAAUrsB,EAAMkpC,YAAYzY,QAAQ,GAAGpE,SAEvC1vB,KAAKkE,MAAMggC,cACX,GAAIlkC,KAAKinC,UAEL,IAAoB,IADFjnC,KAAKkE,MAAMggC,cAAc7gC,EAAOkI,EAAWvL,KAAKinC,WAE9D,OAKRjnC,KAAKkE,MAAMuH,YAC2B,IAA3BzL,KAAKkE,MAAMuH,KAAK/P,QAA0BsE,KAAKkE,MAAMuH,KAAK/P,SAAWsE,KAAK8U,MAAMpZ,QACvFsE,KAAK+D,SAAS,CAAErI,OAAQsE,KAAKkE,MAAMuH,KAAK/P,cAEP,IAA1BsE,KAAKkE,MAAMuH,KAAKhQ,OAAyBuE,KAAKkE,MAAMuH,KAAKhQ,QAAUuE,KAAK8U,MAAMrZ,OACrFuE,KAAK+D,SAAS,CAAEtI,MAAOuE,KAAKkE,MAAMuH,KAAKhQ,SAI/CuE,KAAKgnC,MACqC,iBAA/BhnC,KAAKkE,MAAM4mC,gBAA+B9qC,KAAKkE,MAAM4mC,gBAAkB9qC,KAAKyL,KAAKhQ,MAAQuE,KAAKyL,KAAK/P,OAE9G,IAAI+tC,EAAgBzpC,KAAKhC,OAAOoB,iBAAiBY,KAAKinC,WACtD,GAAgC,SAA5BwC,EAAcrB,UAAsB,CACpC,IAAIqE,EAAWzsC,KAAKusB,WACpB,GAAIkgB,EAAU,CACV,IAAIrH,EAAMplC,KAAKhC,OAAOoB,iBAAiBqtC,GAAUC,cACjD1sC,KAAK2sC,QAAUvH,EAAI7P,WAAW,OAAS,MAAQ,SAC/C6S,EAAYqB,EAAcrB,SAC9B,CACJ,CAEApoC,KAAKksC,wBACLlsC,KAAK6B,aACL,IAAIiT,EAAQ,CACRpX,SAAU,CACN/B,EAAG8zB,EACH7zB,EAAG8zB,EACHj0B,MAAOuE,KAAKyL,KAAKhQ,MACjBC,OAAQsE,KAAKyL,KAAK/P,QAEtBssC,YAAY,EACZE,gBAAiB,EAAS,EAAS,CAAC,EAAGloC,KAAK8U,MAAMozB,iBAAkB,CAAEzE,OAAQzjC,KAAKhC,OAAOoB,iBAAiBiE,EAAMtH,QAAQ0nC,QAAU,SACnIl4B,UAAWA,EACX68B,UAAWA,GAEfpoC,KAAK+D,SAAS+Q,EAxDd,CAyDJ,EACA2xB,EAAU3jC,UAAUulC,YAAc,SAAUhlC,GACxC,IAAI0jC,EAAQ/mC,KACZ,GAAKA,KAAK8U,MAAMkzB,YAAehoC,KAAKinC,WAAcjnC,KAAKhC,OAAvD,CAGA,GAAIgC,KAAKhC,OAAO4uC,YAAcrH,EAAaliC,GACvC,IACIA,EAAMK,iBACNL,EAAMwpC,iBACV,CACA,MAAOzrC,GAEP,CAEJ,IAAIulC,EAAK3mC,KAAKkE,MAAO4lC,EAAWnD,EAAGmD,SAAUC,EAAYpD,EAAGoD,UAAWT,EAAW3C,EAAG2C,SAAUC,EAAY5C,EAAG4C,UAC1G9Z,EAAU8V,EAAaliC,GAASA,EAAMywB,QAAQ,GAAGrE,QAAUpsB,EAAMosB,QACjEC,EAAU6V,EAAaliC,GAASA,EAAMywB,QAAQ,GAAGpE,QAAUrsB,EAAMqsB,QACjEkX,EAAK5mC,KAAK8U,MAAOvJ,EAAYq7B,EAAGr7B,UAAW7N,EAAWkpC,EAAGlpC,SAAUjC,EAAQmrC,EAAGnrC,MAAOC,EAASkrC,EAAGlrC,OACjGyqC,EAAanmC,KAAKipC,gBAClB7hC,EA3fU,SAAU++B,EAAYC,EAAYC,EAAayD,EAAUC,EAAWT,EAAUC,GAKhG,OAJAO,EAAW5D,EAAa4D,EAAU3D,EAAW1qC,MAAO2qC,EAAYC,GAChE0D,EAAY7D,EAAa6D,EAAW5D,EAAWzqC,OAAQ0qC,EAAYC,GACnEiD,EAAWpD,EAAaoD,EAAUnD,EAAW1qC,MAAO2qC,EAAYC,GAChEkD,EAAYrD,EAAaqD,EAAWpD,EAAWzqC,OAAQ0qC,EAAYC,GAC5D,CACHyD,cAA8B,IAAbA,OAA2BzsB,EAAYjgB,OAAO0sC,GAC/DC,eAAgC,IAAdA,OAA4B1sB,EAAYjgB,OAAO2sC,GACjET,cAA8B,IAAbA,OAA2BjsB,EAAYjgB,OAAOksC,GAC/DC,eAAgC,IAAdA,OAA4BlsB,EAAYjgB,OAAOmsC,GAEzE,CAgfkBuD,CAAgB3G,EAAYnmC,KAAKhC,OAAOooC,WAAYpmC,KAAKhC,OAAOqoC,YAAayD,EAAUC,EAAWT,EAAUC,GACtHO,EAAW1iC,EAAI0iC,SACfC,EAAY3iC,EAAI2iC,UAChBT,EAAWliC,EAAIkiC,SACfC,EAAYniC,EAAImiC,UAEhB,IAAI1C,EAAK7mC,KAAKwqC,8BAA8B/a,EAASC,GAAUwb,EAAYrE,EAAGqE,UAAWD,EAAWpE,EAAGoE,SAEnG8B,EAAc/sC,KAAK6pC,4BAA4BC,EAAUC,GACzD/pC,KAAKkE,MAAM+Y,MAAQjd,KAAKkE,MAAM+Y,KAAKthB,IACnCsvC,EAAWzF,EAAgByF,EAAUjrC,KAAKkE,MAAM+Y,KAAKthB,EAAGqE,KAAKkE,MAAMwhC,UAEnE1lC,KAAKkE,MAAM+Y,MAAQjd,KAAKkE,MAAM+Y,KAAKrhB,IACnCsvC,EAAY1F,EAAgB0F,EAAWlrC,KAAKkE,MAAM+Y,KAAKrhB,EAAGoE,KAAKkE,MAAMwhC,UAGzE,IAAIiE,EAAU3pC,KAAKqrC,gCAAgCJ,EAAUC,EAAW,CAAEzvC,MAAOsxC,EAAYjD,SAAUpuC,OAAQqxC,EAAYhD,WAAa,CAAEtuC,MAAO6tC,EAAU5tC,OAAQ6tC,IAGnK,GAFA0B,EAAWtB,EAAQsB,SACnBC,EAAYvB,EAAQuB,UAChBlrC,KAAKkE,MAAM8oC,KAAM,CACjB,IAAIC,EAAehwB,EAAKguB,EAAUjrC,KAAKkE,MAAM8oC,KAAK,GAAIhtC,KAAKkE,MAAM8gC,QAAUhlC,KAAKkE,MAAM8gC,QAAQ,GAAK,GAC/FkI,EAAgBjwB,EAAKiuB,EAAWlrC,KAAKkE,MAAM8oC,KAAK,GAAIhtC,KAAKkE,MAAM8gC,QAAUhlC,KAAKkE,MAAM8gC,QAAQ,GAAK,GACjGc,EAAM9lC,KAAKkE,MAAMwhC,SAAW,EAGhCuF,EAFgB,IAARnF,GAAat/B,KAAKqpB,IAAIod,EAAehC,IAAanF,EAAMmH,EAAehC,EAG/EC,EAFgB,IAARpF,GAAat/B,KAAKqpB,IAAIqd,EAAgBhC,IAAcpF,EAAMoH,EAAgBhC,CAGtF,CACA,IAAIxD,EAAQ,CACRjsC,MAAOwvC,EAAWvtC,EAASjC,MAC3BC,OAAQwvC,EAAYxtC,EAAShC,QAGjC,GADAsE,KAAK0nC,MAAQA,EACTjsC,GAA0B,iBAAVA,EAChB,GAAIA,EAAMuqC,SAAS,KAAM,CACrB,IAAIkD,EAAW+B,EAAW9E,EAAW1qC,MAAS,IAC9CwvC,EAAW,GAAGhF,OAAOiD,EAAS,IAClC,MACK,GAAIztC,EAAMuqC,SAAS,MAAO,CAC3B,IAAImH,EAAMlC,EAAWjrC,KAAKhC,OAAOooC,WAAc,IAC/C6E,EAAW,GAAGhF,OAAOkH,EAAI,KAC7B,MACK,GAAI1xC,EAAMuqC,SAAS,MAAO,CAC3B,IAAIoH,EAAMnC,EAAWjrC,KAAKhC,OAAOqoC,YAAe,IAChD4E,EAAW,GAAGhF,OAAOmH,EAAI,KAC7B,CAEJ,GAAI1xC,GAA4B,iBAAXA,EACjB,GAAIA,EAAOsqC,SAAS,KAAM,CAClBkD,EAAWgC,EAAY/E,EAAWzqC,OAAU,IAChDwvC,EAAY,GAAGjF,OAAOiD,EAAS,IACnC,MACK,GAAIxtC,EAAOsqC,SAAS,MAAO,CACxBmH,EAAMjC,EAAYlrC,KAAKhC,OAAOooC,WAAc,IAChD8E,EAAY,GAAGjF,OAAOkH,EAAI,KAC9B,MACK,GAAIzxC,EAAOsqC,SAAS,MAAO,CACxBoH,EAAMlC,EAAYlrC,KAAKhC,OAAOqoC,YAAe,IACjD6E,EAAY,GAAGjF,OAAOmH,EAAI,KAC9B,CAEJ,IAAIC,EAAW,CACX5xC,MAAOuE,KAAK0pC,yBAAyBuB,EAAU,SAC/CvvC,OAAQsE,KAAK0pC,yBAAyBwB,EAAW,WAEhC,QAAjBlrC,KAAK2sC,QACLU,EAASjF,UAAYiF,EAAS5xC,MAER,WAAjBuE,KAAK2sC,UACVU,EAASjF,UAAYiF,EAAS3xC,QAElC,IAAI4xC,EAAettC,KAAK8U,MAAMrZ,QAAU4xC,EAAS5xC,MAC7C8xC,EAAgBvtC,KAAK8U,MAAMpZ,SAAW2xC,EAAS3xC,OAC/C8xC,EAAkBxtC,KAAK8U,MAAMszB,YAAciF,EAASjF,UACpDqF,EAAUH,GAAgBC,GAAiBC,EAC3CC,IAEA,IAAAjY,WAAU,WACNuR,EAAMhjC,SAASspC,EACnB,GAEArtC,KAAKkE,MAAMwpC,UACPD,GACAztC,KAAKkE,MAAMwpC,SAASrqC,EAAOkI,EAAWvL,KAAKinC,UAAWS,EAlG9D,CAqGJ,EACAjB,EAAU3jC,UAAUwlC,UAAY,SAAUjlC,GACtC,IAAIsjC,EAAIC,EACJC,EAAK7mC,KAAK8U,MAAOkzB,EAAanB,EAAGmB,WAAYz8B,EAAYs7B,EAAGt7B,UAAsBs7B,EAAGnpC,SACpFsqC,GAAehoC,KAAKinC,YAGrBjnC,KAAKkE,MAAMypC,cACX3tC,KAAKkE,MAAMypC,aAAatqC,EAAOkI,EAAWvL,KAAKinC,UAAWjnC,KAAK0nC,OAE/D1nC,KAAKkE,MAAMuH,MACXzL,KAAK+D,SAAS,CAAEtI,MAAwC,QAAhCkrC,EAAK3mC,KAAKkE,MAAMuH,KAAKhQ,aAA0B,IAAPkrC,EAAgBA,EAAK,OAAQjrC,OAA0C,QAAjCkrC,EAAK5mC,KAAKkE,MAAMuH,KAAK/P,cAA2B,IAAPkrC,EAAgBA,EAAK,SAExK5mC,KAAKwpC,eACLxpC,KAAK+D,SAAS,CACVikC,YAAY,EACZE,gBAAiB,EAAS,EAAS,CAAC,EAAGloC,KAAK8U,MAAMozB,iBAAkB,CAAEzE,OAAQ,WAEtF,EACAgD,EAAU3jC,UAAU8qC,WAAa,SAAUniC,GACvC,IAAIk7B,EAAIC,EACR5mC,KAAK+D,SAAS,CAAEtI,MAA6B,QAArBkrC,EAAKl7B,EAAKhQ,aAA0B,IAAPkrC,EAAgBA,EAAK,OAAQjrC,OAA+B,QAAtBkrC,EAAKn7B,EAAK/P,cAA2B,IAAPkrC,EAAgBA,EAAK,QAClJ,EACAH,EAAU3jC,UAAU+qC,cAAgB,WAChC,IAAI9G,EAAQ/mC,KACR2mC,EAAK3mC,KAAKkE,MAAO4pC,EAASnH,EAAGmH,OAAQC,EAAepH,EAAGoH,aAAcC,EAAgBrH,EAAGqH,cAAeC,EAAqBtH,EAAGsH,mBAAoBC,EAAqBvH,EAAGuH,mBAAoBC,EAAkBxH,EAAGwH,gBACxN,IAAKL,EACD,OAAO,KAEX,IAAIM,EAAWptC,OAAOgkB,KAAK8oB,GAAQ7rC,IAAI,SAAUmjC,GAC7C,OAAoB,IAAhB0I,EAAO1I,IACC,SAAKpB,EAAS,CAAEz4B,UAAW65B,EAAKlB,cAAe6C,EAAM7C,cAAeC,cAAe4J,GAAgBA,EAAa3I,GAAMhB,UAAW4J,GAAiBA,EAAc5I,GAAMjhC,SAAUgqC,GAAmBA,EAAgB/I,GAAO+I,EAAgB/I,GAAO,MAAQA,GAE9P,IACX,GAEA,OAAQ,SAAK,MAAO,CAAEhB,UAAW8J,EAAoBlY,MAAOiY,EAAoB9pC,SAAUiqC,GAC9F,EACA3H,EAAU3jC,UAAUmB,OAAS,WACzB,IAAI8iC,EAAQ/mC,KACRquC,EAAertC,OAAOgkB,KAAKhlB,KAAKkE,OAAO0E,OAAO,SAAU0lC,EAAKv1B,GAC7D,OAAmC,IAA/BwtB,EAAa34B,QAAQmL,KAGzBu1B,EAAIv1B,GAAOguB,EAAM7iC,MAAM6U,IAFZu1B,CAIf,EAAG,CAAC,GACAtY,EAAQ,EAAS,EAAS,EAAS,CAAE5xB,SAAU,WAAYmgC,WAAYvkC,KAAK8U,MAAMkzB,WAAa,OAAS,QAAUhoC,KAAKkE,MAAM8xB,OAAQh2B,KAAKuuC,WAAY,CAAEzE,SAAU9pC,KAAKkE,MAAM4lC,SAAUC,UAAW/pC,KAAKkE,MAAM6lC,UAAWT,SAAUtpC,KAAKkE,MAAMolC,SAAUC,UAAWvpC,KAAKkE,MAAMqlC,UAAWrQ,UAAW,aAAc8G,WAAY,IAC7ThgC,KAAK8U,MAAMszB,YACXpS,EAAMoS,UAAYpoC,KAAK8U,MAAMszB,WAEjC,IAAIoG,EAAUxuC,KAAKkE,MAAMuqC,IAAM,MAC/B,OAAQ,UAAMD,EAAS,EAAS,CAAExY,MAAOA,EAAOoO,UAAWpkC,KAAKkE,MAAMkgC,WAAaiK,EAAc,CAG7F1f,IAAK,SAAU+f,GACPA,IACA3H,EAAME,UAAYyH,EAE1B,EAAGvqC,SAAU,CAACnE,KAAK8U,MAAMkzB,aAAc,SAAK,MAAO,CAAEhS,MAAOh2B,KAAK8U,MAAMozB,kBAAoBloC,KAAKkE,MAAMC,SAAUnE,KAAK6tC,mBAC7H,EACApH,EAAU7E,aAAe,CACrB6M,GAAI,MACJvK,cAAe,WAAc,EAC7BwJ,SAAU,WAAc,EACxBC,aAAc,WAAc,EAC5BG,OAAQ,CACJzyC,KAAK,EACLC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNooC,UAAU,EACVC,aAAa,EACbC,YAAY,EACZC,SAAS,GAEb/N,MAAO,CAAC,EACRgX,KAAM,CAAC,EAAG,GACVhI,QAAS,CAAC,EAAG,GACb8F,iBAAiB,EACjBE,0BAA2B,EAC3BD,2BAA4B,EAC5B1O,MAAO,EACPqO,YAAa,EACbhF,QAAS,GAENe,CACX,CAlnB8B,CAknB5B,EAAAkI,c,uBE7wBF,IAAiDC,IASxC,SAASC,GAClB,OAAgB,SAAUC,GAEhB,IAAIC,EAAmB,CAAC,EAGxB,SAAS,EAAoBC,GAG5B,GAAGD,EAAiBC,GACnB,OAAOD,EAAiBC,GAAUC,QAGnC,IAAIC,EAASH,EAAiBC,GAAY,CACzCp0C,EAAGo0C,EACHG,GAAG,EACHF,QAAS,CAAC,GAUX,OANAH,EAAQE,GAAUxtC,KAAK0tC,EAAOD,QAASC,EAAQA,EAAOD,QAAS,GAG/DC,EAAOC,GAAI,EAGJD,EAAOD,OACf,CAuCA,OAnCA,EAAoBG,EAAIN,EAGxB,EAAoBJ,EAAIK,EAGxB,EAAoBn0C,EAAI,SAASsC,GAAS,OAAOA,CAAO,EAGxD,EAAoBwU,EAAI,SAASu9B,EAASle,EAAMse,GAC3C,EAAoBC,EAAEL,EAASle,IAClC/vB,OAAOunC,eAAe0G,EAASle,EAAM,CACpC2X,cAAc,EACdD,YAAY,EACZD,IAAK6G,GAGR,EAGA,EAAoBluC,EAAI,SAAS+tC,GAChC,IAAIG,EAASH,GAAUA,EAAOK,WAC7B,WAAwB,OAAOL,EAAgB,OAAG,EAClD,WAA8B,OAAOA,CAAQ,EAE9C,OADA,EAAoBx9B,EAAE29B,EAAQ,IAAKA,GAC5BA,CACR,EAGA,EAAoBC,EAAI,SAASE,EAAQxkB,GAAY,OAAOhqB,OAAO8B,UAAUvB,eAAeC,KAAKguC,EAAQxkB,EAAW,EAGpH,EAAoBuY,EAAI,GAGjB,EAAoB,EAAoBD,EAAI,GACnD,CAlEM,CAoEN,CAEJ,SAAU4L,EAAQD,GAGxB,IAOIQ,EACAC,EARAC,EAAUT,EAAOD,QAAU,CAAC,EAUhC,SAASW,IACL,MAAM,IAAIryC,MAAM,kCACpB,CACA,SAASsyC,IACL,MAAM,IAAItyC,MAAM,oCACpB,CAqBA,SAASuyC,EAAWC,GAChB,GAAIN,IAAqBlwB,WAErB,OAAOA,WAAWwwB,EAAK,GAG3B,IAAKN,IAAqBG,IAAqBH,IAAqBlwB,WAEhE,OADAkwB,EAAmBlwB,WACZA,WAAWwwB,EAAK,GAE3B,IAEI,OAAON,EAAiBM,EAAK,EACjC,CAAE,MAAM3uC,GACJ,IAEI,OAAOquC,EAAiBjuC,KAAK,KAAMuuC,EAAK,EAC5C,CAAE,MAAM3uC,GAEJ,OAAOquC,EAAiBjuC,KAAKxB,KAAM+vC,EAAK,EAC5C,CACJ,CAGJ,EA5CC,WACG,IAEQN,EADsB,mBAAflwB,WACYA,WAEAqwB,CAE3B,CAAE,MAAOxuC,GACLquC,EAAmBG,CACvB,CACA,IAEQF,EADwB,mBAAjB9vB,aACcA,aAEAiwB,CAE7B,CAAE,MAAOzuC,GACLsuC,EAAqBG,CACzB,CACJ,CAnBA,GAwEA,IAEIG,EAFAC,EAAQ,GACRC,GAAW,EAEXC,GAAc,EAElB,SAASC,IACAF,GAAaF,IAGlBE,GAAW,EACPF,EAAar1C,OACbs1C,EAAQD,EAAa/J,OAAOgK,GAE5BE,GAAc,EAEdF,EAAMt1C,QACN01C,IAER,CAEA,SAASA,IACL,IAAIH,EAAJ,CAGA,IAAII,EAAUR,EAAWM,GACzBF,GAAW,EAGX,IADA,IAAIK,EAAMN,EAAMt1C,OACV41C,GAAK,CAGP,IAFAP,EAAeC,EACfA,EAAQ,KACCE,EAAaI,GACdP,GACAA,EAAaG,GAAYK,MAGjCL,GAAc,EACdI,EAAMN,EAAMt1C,MAChB,CACAq1C,EAAe,KACfE,GAAW,EAnEf,SAAyBO,GACrB,GAAIf,IAAuB9vB,aAEvB,OAAOA,aAAa6wB,GAGxB,IAAKf,IAAuBG,IAAwBH,IAAuB9vB,aAEvE,OADA8vB,EAAqB9vB,aACdA,aAAa6wB,GAExB,IAEI,OAAOf,EAAmBe,EAC9B,CAAE,MAAOrvC,GACL,IAEI,OAAOsuC,EAAmBluC,KAAK,KAAMivC,EACzC,CAAE,MAAOrvC,GAGL,OAAOsuC,EAAmBluC,KAAKxB,KAAMywC,EACzC,CACJ,CAIJ,CA0CIC,CAAgBJ,EAlBhB,CAmBJ,CAgBA,SAASK,EAAKZ,EAAKh8B,GACf/T,KAAK+vC,IAAMA,EACX/vC,KAAK+T,MAAQA,CACjB,CAWA,SAAS2rB,IAAQ,CA5BjBiQ,EAAQiB,SAAW,SAAUb,GACzB,IAAItvC,EAAO,IAAIC,MAAMZ,UAAUnF,OAAS,GACxC,GAAImF,UAAUnF,OAAS,EACnB,IAAK,IAAIC,EAAI,EAAGA,EAAIkF,UAAUnF,OAAQC,IAClC6F,EAAK7F,EAAI,GAAKkF,UAAUlF,GAGhCq1C,EAAMnjC,KAAK,IAAI6jC,EAAKZ,EAAKtvC,IACJ,IAAjBwvC,EAAMt1C,QAAiBu1C,GACvBJ,EAAWO,EAEnB,EAOAM,EAAK7tC,UAAU0tC,IAAM,WACjBxwC,KAAK+vC,IAAI5vC,MAAM,KAAMH,KAAK+T,MAC9B,EACA47B,EAAQkB,MAAQ,UAChBlB,EAAQmB,SAAU,EAClBnB,EAAQ1R,IAAM,CAAC,EACf0R,EAAQoB,KAAO,GACfpB,EAAQqB,QAAU,GAClBrB,EAAQsB,SAAW,CAAC,EAIpBtB,EAAQjP,GAAKhB,EACbiQ,EAAQuB,YAAcxR,EACtBiQ,EAAQ5tB,KAAO2d,EACfiQ,EAAQwB,IAAMzR,EACdiQ,EAAQyB,eAAiB1R,EACzBiQ,EAAQ0B,mBAAqB3R,EAC7BiQ,EAAQ2B,KAAO5R,EACfiQ,EAAQ4B,gBAAkB7R,EAC1BiQ,EAAQ6B,oBAAsB9R,EAE9BiQ,EAAQ8B,UAAY,SAAU1gB,GAAQ,MAAO,EAAG,EAEhD4e,EAAQztC,QAAU,SAAU6uB,GACxB,MAAM,IAAIxzB,MAAM,mCACpB,EAEAoyC,EAAQ+B,IAAM,WAAc,MAAO,GAAI,EACvC/B,EAAQgC,MAAQ,SAAUvM,GACtB,MAAM,IAAI7nC,MAAM,iCACpB,EACAoyC,EAAQiC,MAAQ,WAAa,OAAO,CAAG,CAGhC,EAED,SAAU1C,EAAQD,EAAS4C,GAEjC,aAcA,SAASC,EAAkBC,GACzB,OAAO,WACL,OAAOA,CACT,CACF,CAOA,IAAIC,EAAgB,WAA0B,EAE9CA,EAAcC,YAAcH,EAC5BE,EAAcE,iBAAmBJ,GAAkB,GACnDE,EAAcG,gBAAkBL,GAAkB,GAClDE,EAAcI,gBAAkBN,EAAkB,MAClDE,EAAcK,gBAAkB,WAC9B,OAAOryC,IACT,EACAgyC,EAAcM,oBAAsB,SAAUP,GAC5C,OAAOA,CACT,EAEA7C,EAAOD,QAAU+C,CAEV,EAED,SAAU9C,EAAQD,EAAS,GAEjC,cAC4B,SAASU,GAuBrC,IAAI4C,EAAiB,SAAwBC,GAAS,EAEzB,eAAzB7C,EAAQ1R,IAAIwU,WACdF,EAAiB,SAAwBC,GACvC,QAAen1B,IAAXm1B,EACF,MAAM,IAAIj1C,MAAM,+CAEpB,GAwBF2xC,EAAOD,QArBP,SAAmB3xC,EAAWk1C,EAAQhpC,EAAGC,EAAGilC,EAAGh9B,EAAGtQ,EAAGsxC,GAGnD,GAFAH,EAAeC,IAEVl1C,EAAW,CACd,IAAImG,EACJ,QAAe4Z,IAAXm1B,EACF/uC,EAAQ,IAAIlG,MAAM,qIACb,CACL,IAAIkD,EAAO,CAAC+I,EAAGC,EAAGilC,EAAGh9B,EAAGtQ,EAAGsxC,GACvBC,EAAW,GACflvC,EAAQ,IAAIlG,MAAMi1C,EAAOlM,QAAQ,MAAO,WACtC,OAAO7lC,EAAKkyC,IACd,KACM5hB,KAAO,qBACf,CAGA,MADAttB,EAAMmvC,YAAc,EACdnvC,CACR,CACF,CAG2B,GAAEjC,KAAKytC,EAAS,EAAoB,GAExD,EAED,SAAUC,EAAQD,EAAS4C,GAEjC,aAcA3C,EAAOD,QAFoB,8CAKpB,EAED,SAAUC,EAAQD,GAExBC,EAAOD,QAAUJ,CAEV,EAED,SAAUK,EAAQD,EAAS,GAEjC,aAGAjuC,OAAOunC,eAAe0G,EAAS,aAAc,CAC3C/xC,OAAO,IAGT,IAIgC21C,EAJ5BC,EAAS,EAAoB,GAM7BC,IAF4BF,EAFKC,IAEgBD,EAAItD,WAAasD,EAAM,CAAEG,QAASH,IAEvDG,QAAQC,cAAc,CACpDC,aAAc,WAAyB,EACvCC,eAAgB,WAA2B,IAG7ClE,EAAQ+D,QAAUD,EAClB7D,EAAOD,QAAUA,EAAiB,OAE3B,EAED,SAAUC,EAAQD,EAAS,GAEjC,cAC4B,SAASU,GAYrC,IAaQyD,EAJJC,EATgB,EAAoB,GAWX,eAAzB1D,EAAQ1R,IAAIwU,WAERW,EAAe,SAAsBZ,GACvC,IAAK,IAAIhyC,EAAOV,UAAUnF,OAAQ8F,EAAOC,MAAMF,EAAO,EAAIA,EAAO,EAAI,GAAIG,EAAO,EAAGA,EAAOH,EAAMG,IAC9FF,EAAKE,EAAO,GAAKb,UAAUa,GAG7B,IAAIgyC,EAAW,EACXhxC,EAAU,YAAc6wC,EAAOlM,QAAQ,MAAO,WAChD,OAAO7lC,EAAKkyC,IACd,GACuB,oBAAZW,SACTA,QAAQ7vC,MAAM9B,GAEhB,IAIE,MAAM,IAAIpE,MAAMoE,EAClB,CAAE,MAAOhG,GAAI,CACf,EAEA03C,EAAU,SAAiB/1C,EAAWk1C,GACpC,QAAen1B,IAAXm1B,EACF,MAAM,IAAIj1C,MAAM,6EAGlB,GAAsD,IAAlDi1C,EAAO5kC,QAAQ,iCAIdtQ,EAAW,CACd,IAAK,IAAIi2C,EAAQzzC,UAAUnF,OAAQ8F,EAAOC,MAAM6yC,EAAQ,EAAIA,EAAQ,EAAI,GAAIC,EAAQ,EAAGA,EAAQD,EAAOC,IACpG/yC,EAAK+yC,EAAQ,GAAK1zC,UAAU0zC,GAG9BJ,EAAajzC,WAAMkd,EAAW,CAACm1B,GAAQvM,OAAOxlC,GAChD,CACF,GAIJyuC,EAAOD,QAAUoE,CACU,GAAE7xC,KAAKytC,EAAS,EAAoB,GAExD,EAED,SAAUC,EAAQD,EAAS,IAEL,SAASU,GASrC,GAA6B,eAAzBA,EAAQ1R,IAAIwU,SAA2B,CACzC,IAAIgB,EAAwC,mBAAXC,QAC/BA,OAAOC,KACPD,OAAOC,IAAI,kBACX,MAWFzE,EAAOD,QAAU,EAAoB,GAApB,CATI,SAASO,GAC5B,MAAyB,iBAAXA,GACD,OAAXA,GACAA,EAAOoE,WAAaH,CACxB,GAI0B,EAE5B,MAGEvE,EAAOD,QAAU,EAAoB,GAApB,EAGQ,GAAEztC,KAAKytC,EAAS,EAAoB,GAExD,EAED,SAAUC,EAAQD,EAAS,GAEjC,aAGAjuC,OAAOunC,eAAe0G,EAAS,aAAc,CAC3C/xC,OAAO,IAGT,IAAI22C,EAAe,WAAc,SAASC,EAAiB/3C,EAAQmI,GAAS,IAAK,IAAItJ,EAAI,EAAGA,EAAIsJ,EAAMvJ,OAAQC,IAAK,CAAE,IAAIkO,EAAa5E,EAAMtJ,GAAIkO,EAAW2/B,WAAa3/B,EAAW2/B,aAAc,EAAO3/B,EAAW4/B,cAAe,EAAU,UAAW5/B,IAAYA,EAAWirC,UAAW,GAAM/yC,OAAOunC,eAAexsC,EAAQ+M,EAAWiQ,IAAKjQ,EAAa,CAAE,CAAE,OAAO,SAAUkrC,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYH,EAAiBE,EAAYlxC,UAAWmxC,GAAiBC,GAAaJ,EAAiBE,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEflB,EAAS,EAAoB,GAE7BqB,EAAUC,EAAuBtB,GAIjCuB,EAAcD,EAFD,EAAoB,IAMjCE,EAAsBF,EAFD,EAAoB,IAI7C,SAASA,EAAuBvB,GAAO,OAAOA,GAAOA,EAAItD,WAAasD,EAAM,CAAEG,QAASH,EAAO,CAI9F,SAAS0B,EAA2BC,EAAMhzC,GAAQ,IAAKgzC,EAAQ,MAAM,IAAIC,eAAe,6DAAgE,OAAOjzC,GAAyB,iBAATA,GAAqC,mBAATA,EAA8BgzC,EAAPhzC,CAAa,CAS/O,IAAIkzC,EAAa,SAAUC,GAGzB,SAASD,IACP,IAAIt5C,EAEAw5C,EAAO7N,GAjBf,SAAyB8N,EAAUb,GAAe,KAAMa,aAAoBb,GAAgB,MAAM,IAAIrP,UAAU,oCAAwC,CAmBpJmQ,CAAgB90C,KAAM00C,GAEtB,IAAK,IAAIl0C,EAAOV,UAAUnF,OAAQ8F,EAAOC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC3EF,EAAKE,GAAQb,UAAUa,GAGzB,OAAei0C,EAAS7N,EAAQwN,EAA2Bv0C,MAAO5E,EAAOs5C,EAAWhQ,WAAa1jC,OAAO+zC,eAAeL,IAAalzC,KAAKrB,MAAM/E,EAAM,CAAC4E,MAAMimC,OAAOxlC,KAAiBsmC,EAAMiO,MAAQ,CAAC,EAAGjO,EAAMmM,aAAe,SAAU1R,EAAM50B,GACzOA,EAAOlK,QAAQ,SAAUuyC,GAClBlO,EAAMiO,MAAMC,KACflO,EAAMiO,MAAMC,GAAS,IAGlBlO,EAAMmO,SAAS1T,EAAMyT,KACpBlO,EAAMiO,MAAMC,GAAOt6C,OAAS,GAC9BosC,EAAMoO,mBAAmBpO,EAAMiO,MAAMC,GAAO,GAAIzT,GAElDuF,EAAMiO,MAAMC,GAAOnoC,KAAK00B,GAE5B,GACAuF,EAAMqO,UAAU5T,EAAM50B,EACxB,EAAGm6B,EAAMoM,eAAiB,SAAU3R,EAAM50B,GACxCA,EAAOlK,QAAQ,SAAUuyC,GACnBlO,EAAMmO,SAAS1T,EAAMyT,KACvBlO,EAAMsO,aAAa7T,GACnBuF,EAAMiO,MAAMC,GAAOx1B,OAAOsnB,EAAMiO,MAAMC,GAAOrnC,QAAQ4zB,GAAO,GAEhE,EACF,EAAGuF,EAAMqO,UAAY,SAAU5T,EAAM50B,GAEnC40B,EAAK8T,SAAWvO,EAAMwO,iBAAiBr0C,KAAK6lC,EAAOvF,EAAM50B,EAC3D,EAAGm6B,EAAMsO,aAAe,SAAU7T,GAEhCA,EAAK8T,SAAW,IAClB,EAAGvO,EAAMmO,SAAW,SAAU1T,EAAMyT,GAClC,QAAKlO,EAAMiO,MAAMC,IAIVlO,EAAMiO,MAAMC,GAAOxnC,KAAK,SAAU+nC,GACvC,OAAOA,IAAShU,CAClB,EACF,EAAGuF,EAAMwO,iBAAmB,SAAU/T,EAAM50B,GACrCm6B,EAAM7iC,MAAMuxC,SAIjBz3C,OAAO4C,sBAAsB,WAC3BmmC,EAAM2O,oBAAoBlU,EAAM50B,EAClC,EACF,EAAGm6B,EAAM2O,oBAAsB,SAAUC,EAAc/oC,GACrDA,EAAOlK,QAAQ,SAAUuyC,GACvBlO,EAAMiO,MAAMC,GAAOvyC,QAAQ,SAAU8yC,GAE/BG,IAAiBH,IAEnBzO,EAAMsO,aAAaG,EAAMP,GACzBlO,EAAMoO,mBAAmBQ,EAAcH,GAEvCx3C,OAAO4C,sBAAsB,WAC3B,IAAIg1C,EAAa50C,OAAOgkB,KAAK+hB,EAAMiO,OAAO3rC,OAAO,SAAUwsC,GACzD,OAAO9O,EAAMiO,MAAMa,GAAWrlB,SAASglB,EACzC,GACAzO,EAAMqO,UAAUI,EAAMI,EACxB,GAEJ,EACF,GACI7O,EAAM7iC,MAAM4xC,QAAQ/O,EAAM7iC,MAAM4xC,OAAOH,EAC7C,EAAWpB,EAA2BxN,EAAnC6N,EACL,CAoDA,OAxIF,SAAmBmB,EAAUC,GAAc,GAA0B,mBAAfA,GAA4C,OAAfA,EAAuB,MAAM,IAAIrR,UAAU,kEAAoEqR,GAAeD,EAASjzC,UAAY9B,OAAO+zB,OAAOihB,GAAcA,EAAWlzC,UAAW,CAAEG,YAAa,CAAE/F,MAAO64C,EAAUtN,YAAY,EAAOsL,UAAU,EAAMrL,cAAc,KAAesN,IAAYh1C,OAAOyjC,eAAiBzjC,OAAOyjC,eAAesR,EAAUC,GAAcD,EAASrR,UAAYsR,EAAY,CAQ3eC,CAAUvB,EAAYC,GA8EtBd,EAAaa,EAAY,CAAC,CACxB37B,IAAK,kBACL7b,MAAO,WACL,MAAO,CACLg2C,aAAclzC,KAAKkzC,aACnBC,eAAgBnzC,KAAKmzC,eAEzB,GACC,CACDp6B,IAAK,qBACL7b,MAAO,SAA4By4C,EAAcH,GAC/C,IAAIzX,EAAY4X,EAAa5X,UACzBpa,EAAegyB,EAAahyB,aAC5BO,EAAeyxB,EAAazxB,aAC5B4Z,EAAa6X,EAAa7X,WAC1Bla,EAAc+xB,EAAa/xB,YAC3BK,EAAc0xB,EAAa1xB,YAG3BiyB,EAAkBvyB,EAAeO,EACjCiyB,EAAmBvyB,EAAcK,EAEjCmyB,EAASp2C,KAAKkE,MACdmyC,EAAeD,EAAOC,aACtB/qC,EAAW8qC,EAAO9qC,SAClBO,EAAauqC,EAAOvqC,WAIpByqC,EAAad,EAAK7xB,aAAeO,EACjCqyB,EAAYf,EAAK5xB,YAAcK,EAE/B3Y,GAAY4qC,EAAkB,IAChCV,EAAKzX,UAAYsY,EAAeC,EAAavY,EAAYmY,EAAkBnY,GAEzElyB,GAAcsqC,EAAmB,IACnCX,EAAK1X,WAAauY,EAAeE,EAAYzY,EAAaqY,EAAmBrY,EAEjF,GACC,CACD/kB,IAAK,SACL7b,MAAO,WACL,OAAOi3C,EAAQnB,QAAQjnB,cACrBuoB,EAAoBtB,QAAQ3a,SAC5B,CAAEn7B,MAAO8C,KAAKw2C,mBACdrC,EAAQnB,QAAQyD,SAASC,KAAK12C,KAAKkE,MAAMC,UAE7C,KAGKuwC,CACT,CAlIiB,CAkIf5B,EAAO6D,WAETjC,EAAWkC,UAAY,CAMrBd,OAAQzB,EAAYrB,QAAQ6D,KAC5B1yC,SAAUkwC,EAAYrB,QAAQpL,QAAQkP,WACtCT,aAAchC,EAAYrB,QAAQ+D,KAClCzrC,SAAU+oC,EAAYrB,QAAQ+D,KAC9BlrC,WAAYwoC,EAAYrB,QAAQ+D,KAChCtB,QAASpB,EAAYrB,QAAQ+D,MAE/BrC,EAAW9S,aAAe,CACxByU,cAAc,EACd/qC,UAAU,EACVO,YAAY,EACZ4pC,SAAS,GAEXxG,EAAQ+D,QAAU0B,EAClBxF,EAAOD,QAAUA,EAAiB,OAE3B,EAED,SAAUC,EAAQD,EAAS,GAEjC,aAGAjuC,OAAOunC,eAAe0G,EAAS,aAAc,CAC3C/xC,OAAO,IAGT,IAAI22C,EAAe,WAAc,SAASC,EAAiB/3C,EAAQmI,GAAS,IAAK,IAAItJ,EAAI,EAAGA,EAAIsJ,EAAMvJ,OAAQC,IAAK,CAAE,IAAIkO,EAAa5E,EAAMtJ,GAAIkO,EAAW2/B,WAAa3/B,EAAW2/B,aAAc,EAAO3/B,EAAW4/B,cAAe,EAAU,UAAW5/B,IAAYA,EAAWirC,UAAW,GAAM/yC,OAAOunC,eAAexsC,EAAQ+M,EAAWiQ,IAAKjQ,EAAa,CAAE,CAAE,OAAO,SAAUkrC,EAAaC,EAAYC,GAAiJ,OAA9HD,GAAYH,EAAiBE,EAAYlxC,UAAWmxC,GAAiBC,GAAaJ,EAAiBE,EAAaE,GAAqBF,CAAa,CAAG,CAA7hB,GAEflB,EAAS,EAAoB,GAI7BuB,EAAcD,EAFD,EAAoB,IAMjCE,EAAsBF,EAFD,EAAoB,IAI7C,SAASA,EAAuBvB,GAAO,OAAOA,GAAOA,EAAItD,WAAasD,EAAM,CAAEG,QAASH,EAAO,CAgB9F,IAAImE,EAAiB,SAAUrC,GAG7B,SAASqC,EAAe9yC,IAjB1B,SAAyB2wC,EAAUb,GAAe,KAAMa,aAAoBb,GAAgB,MAAM,IAAIrP,UAAU,oCAAwC,CAkBpJmQ,CAAgB90C,KAAMg3C,GAEtB,IAAIjQ,EAlBR,SAAoCyN,EAAMhzC,GAAQ,IAAKgzC,EAAQ,MAAM,IAAIC,eAAe,6DAAgE,OAAOjzC,GAAyB,iBAATA,GAAqC,mBAATA,EAA8BgzC,EAAPhzC,CAAa,CAkB/N+yC,CAA2Bv0C,MAAOg3C,EAAetS,WAAa1jC,OAAO+zC,eAAeiC,IAAiBx1C,KAAKxB,KAAMkE,IAe5H,OAbA6iC,EAAMkQ,QAAU,SAAUrqC,GACxB,MAAO,GAAGq5B,OAAOr5B,EACnB,EAEAm6B,EAAMmQ,WAAa,WACbnQ,EAAM7iC,MAAMizC,SACdpQ,EAAMvF,KAAOuF,EAAM7iC,MAAMizC,SAAS58C,QAElCwsC,EAAMvF,KAAOuF,EAAMqQ,SAAS78C,OAEhC,EAEAwsC,EAAMqQ,SAAWlzC,EAAM82B,SAAW92B,EAAM82B,UAAW,EAAI8X,EAAOuE,aACvDtQ,CACT,CAuDA,OAvFF,SAAmBgP,EAAUC,GAAc,GAA0B,mBAAfA,GAA4C,OAAfA,EAAuB,MAAM,IAAIrR,UAAU,kEAAoEqR,GAAeD,EAASjzC,UAAY9B,OAAO+zB,OAAOihB,GAAcA,EAAWlzC,UAAW,CAAEG,YAAa,CAAE/F,MAAO64C,EAAUtN,YAAY,EAAOsL,UAAU,EAAMrL,cAAc,KAAesN,IAAYh1C,OAAOyjC,eAAiBzjC,OAAOyjC,eAAesR,EAAUC,GAAcD,EAASrR,UAAYsR,EAAY,CAW3eC,CAAUe,EAAgBrC,GAuB1Bd,EAAamD,EAAgB,CAAC,CAC5Bj+B,IAAK,oBACL7b,MAAO,WACD8C,KAAKkE,MAAMuxC,UACbz1C,KAAKk3C,aACDl3C,KAAKwhC,MACPxhC,KAAKsrB,QAAQ4nB,aAAalzC,KAAKwhC,KAAMxhC,KAAKi3C,QAAQj3C,KAAKkE,MAAM+wC,QAGnE,GACC,CACDl8B,IAAK,qBACL7b,MAAO,SAA4Bo6C,GAC7Bt3C,KAAKkE,MAAMizC,WAAaG,EAAUH,WAChCn3C,KAAKwhC,MACPxhC,KAAKsrB,QAAQ6nB,eAAenzC,KAAKwhC,KAAMxhC,KAAKi3C,QAAQK,EAAUrC,QAEhEj1C,KAAKk3C,aACDl3C,KAAKwhC,MACPxhC,KAAKsrB,QAAQ4nB,aAAalzC,KAAKwhC,KAAMxhC,KAAKi3C,QAAQK,EAAUrC,SAG5Dj1C,KAAKwhC,MAAQxhC,KAAKkE,MAAMuxC,UAAY6B,EAAU7B,UAC5Cz1C,KAAKkE,MAAMuxC,QACbz1C,KAAKsrB,QAAQ4nB,aAAalzC,KAAKwhC,KAAMxhC,KAAKi3C,QAAQK,EAAUrC,QAE5Dj1C,KAAKsrB,QAAQ6nB,eAAenzC,KAAKwhC,KAAMxhC,KAAKi3C,QAAQK,EAAUrC,SAG9Dj1C,KAAKwhC,MAAQxhC,KAAKkE,MAAMuxC,SAAWz1C,KAAKkE,MAAM+wC,QAAUqC,EAAUrC,QACpEj1C,KAAKsrB,QAAQ6nB,eAAenzC,KAAKwhC,KAAMxhC,KAAKi3C,QAAQK,EAAUrC,QAC9Dj1C,KAAKsrB,QAAQ4nB,aAAalzC,KAAKwhC,KAAMxhC,KAAKi3C,QAAQj3C,KAAKkE,MAAM+wC,QAEjE,GACC,CACDl8B,IAAK,uBACL7b,MAAO,WACD8C,KAAKwhC,MAAQxhC,KAAKkE,MAAMuxC,SAC1Bz1C,KAAKsrB,QAAQ6nB,eAAenzC,KAAKwhC,KAAMxhC,KAAKi3C,QAAQj3C,KAAKkE,MAAM+wC,OAEnE,GACC,CACDl8B,IAAK,SACL7b,MAAO,WACL,OAAI8C,KAAKkE,MAAMizC,SACNn3C,KAAKkE,MAAMC,UAEb,EAAI2uC,EAAOyE,cAAczE,EAAO2D,SAASC,KAAK12C,KAAKkE,MAAMC,UAAW,CACzEwqB,IAAK3uB,KAAKo3C,UAEd,KAGKJ,CACT,CA9EqB,CA8EnBlE,EAAO6D,WAETK,EAAeQ,YAAclD,EAAoBtB,QACjDgE,EAAeJ,UAAY,CACzBzyC,SAAUkwC,EAAYrB,QAAQxR,KAAKsV,WACnCK,SAAU9C,EAAYrB,QAAQyE,UAAU,CAACpD,EAAYrB,QAAQ6D,KAAMxC,EAAYrB,QAAQ0E,MAAM,CAAEn9C,QAAS85C,EAAYrB,QAAQ2E,QAC5H1C,MAAOZ,EAAYrB,QAAQyE,UAAU,CAACpD,EAAYrB,QAAQ4E,OAAQvD,EAAYrB,QAAQ6E,QAAQxD,EAAYrB,QAAQ4E,UAClHnC,QAASpB,EAAYrB,QAAQ+D,KAC7B/b,SAAUqZ,EAAYrB,QAAQyE,UAAU,CACxCpD,EAAYrB,QAAQ6D,KAAMxC,EAAYrB,QAAQ0E,MAAM,CAAEn9C,QAAS85C,EAAYrB,QAAQ2E,SAErFX,EAAepV,aAAe,CAC5BqT,MAAO,UACPQ,SAAS,GAEXxG,EAAQ+D,QAAUgE,EAClB9H,EAAOD,QAAUA,EAAiB,OAE3B,EAED,SAAUC,EAAQD,EAAS,GAEjC,aAGAjuC,OAAOunC,eAAe0G,EAAS,aAAc,CAC3C/xC,OAAO,IAGT,IAAI46C,EAAc,EAAoB,GAEtC92C,OAAOunC,eAAe0G,EAAS,aAAc,CAC3CxG,YAAY,EACZD,IAAK,WACH,OAAO4L,EAAuB0D,GAAa9E,OAC7C,IAGF,IAAI+E,EAAkB,EAAoB,GAS1C,SAAS3D,EAAuBvB,GAAO,OAAOA,GAAOA,EAAItD,WAAasD,EAAM,CAAEG,QAASH,EAAO,CAP9F7xC,OAAOunC,eAAe0G,EAAS,iBAAkB,CAC/CxG,YAAY,EACZD,IAAK,WACH,OAAO4L,EAAuB2D,GAAiB/E,OACjD,GAKK,EAED,SAAU9D,EAAQD,EAAS,GAEjC,cAC4B,SAASU,GAWrC,GAA6B,eAAzBA,EAAQ1R,IAAIwU,SACd,IAAIj1C,EAAY,EAAoB,GAChC61C,EAAU,EAAoB,GAC9B2E,EAAuB,EAAoB,GAC3CC,EAAqB,CAAC,EA6C5B/I,EAAOD,QA/BP,SAAwBiJ,EAAWhvC,EAAQ1D,EAAU2yC,EAAeC,GAClE,GAA6B,eAAzBzI,EAAQ1R,IAAIwU,SACd,IAAK,IAAI4F,KAAgBH,EACvB,GAAIA,EAAU32C,eAAe82C,GAAe,CAC1C,IAAI50C,EAIJ,IAGEjG,EAA6C,mBAA5B06C,EAAUG,GAA8B,oFAA0FF,GAAiB,cAAe3yC,EAAU6yC,GAC7L50C,EAAQy0C,EAAUG,GAAcnvC,EAAQmvC,EAAcF,EAAe3yC,EAAU,KAAMwyC,EACvF,CAAE,MAAOM,GACP70C,EAAQ60C,CACV,CAEA,GADAjF,GAAS5vC,GAASA,aAAiBlG,MAAO,2RAAgT46C,GAAiB,cAAe3yC,EAAU6yC,SAAqB50C,GACrZA,aAAiBlG,SAAWkG,EAAM9B,WAAWs2C,GAAqB,CAGpEA,EAAmBx0C,EAAM9B,UAAW,EAEpC,IAAI42C,EAAQH,EAAWA,IAAa,GAEpC/E,GAAQ,EAAO,uBAAwB7tC,EAAU/B,EAAM9B,QAAkB,MAAT42C,EAAgBA,EAAQ,GAC1F,CACF,CAGN,CAI2B,GAAE/2C,KAAKytC,EAAS,EAAoB,GAExD,EAED,SAAUC,EAAQD,EAAS,GAEjC,aAYA,IAAI+C,EAAgB,EAAoB,GACpCx0C,EAAY,EAAoB,GAChCw6C,EAAuB,EAAoB,GAE/C9I,EAAOD,QAAU,WACf,SAASuJ,EAAKt0C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,EAAcC,GAChEA,IAAWX,GAIfx6C,GACE,EACA,kLAIJ,CAEA,SAASo7C,IACP,OAAOJ,CACT,CAHAA,EAAK1B,WAAa0B,EAMlB,IAAIK,EAAiB,CACnB9kC,MAAOykC,EACPzB,KAAMyB,EACN3B,KAAM2B,EACNM,OAAQN,EACRhJ,OAAQgJ,EACRZ,OAAQY,EACRO,OAAQP,EAERb,IAAKa,EACLX,QAASe,EACThR,QAAS4Q,EACTQ,WAAYJ,EACZpX,KAAMgX,EACNS,SAAUL,EACVM,MAAON,EACPnB,UAAWmB,EACXlB,MAAOkB,GAMT,OAHAC,EAAeM,eAAiBnH,EAChC6G,EAAeO,UAAYP,EAEpBA,CACT,CAGO,EAED,SAAU3J,EAAQD,EAAS,GAEjC,cAC4B,SAASU,GAWrC,IAAIqC,EAAgB,EAAoB,GACpCx0C,EAAY,EAAoB,GAChC61C,EAAU,EAAoB,GAE9B2E,EAAuB,EAAoB,GAC3CmB,EAAiB,EAAoB,IAEzCjK,EAAOD,QAAU,SAASoK,EAAgBC,GAExC,IAAIC,EAAoC,mBAAX7F,QAAyBA,OAAO8F,SAuEzDC,EAAY,gBAIZZ,EAAiB,CACnB9kC,MAAO2lC,EAA2B,SAClC3C,KAAM2C,EAA2B,WACjC7C,KAAM6C,EAA2B,YACjCZ,OAAQY,EAA2B,UACnClK,OAAQkK,EAA2B,UACnC9B,OAAQ8B,EAA2B,UACnCX,OAAQW,EAA2B,UAEnC/B,IAyHOgC,EAA2B3H,EAAcI,iBAxHhDyF,QA2HF,SAAkC+B,GAkBhC,OAAOD,EAjBP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,GAC1D,GAA2B,mBAAhBkB,EACT,OAAO,IAAIC,EAAc,aAAenB,EAAe,mBAAqBP,EAAgB,mDAE9F,IAAI2B,EAAY51C,EAAMu0C,GACtB,IAAK/3C,MAAMiqC,QAAQmP,GAEjB,OAAO,IAAID,EAAc,WAAar0C,EAAW,KAAOkzC,EAA/B,cADVqB,EAAYD,GAC6E,kBAAoB3B,EAAgB,yBAE9I,IAAK,IAAIv9C,EAAI,EAAGA,EAAIk/C,EAAUn/C,OAAQC,IAAK,CACzC,IAAI6I,EAAQm2C,EAAYE,EAAWl/C,EAAGu9C,EAAe3yC,EAAUkzC,EAAe,IAAM99C,EAAI,IAAKo9C,GAC7F,GAAIv0C,aAAiBlG,MACnB,OAAOkG,CAEX,CACA,OAAO,IACT,EAEF,EA7IEmkC,QAwJO+R,EARP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,GAC1D,IAAIoB,EAAY51C,EAAMu0C,GACtB,OAAKY,EAAeS,GAIb,KAFE,IAAID,EAAc,WAAar0C,EAAW,KAAOkzC,EAA/B,cADVqB,EAAYD,GAC6E,kBAAoB3B,EAAgB,qCAGhJ,GAtJAa,WA0JF,SAAmCgB,GASjC,OAAOL,EARP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,GAC1D,KAAMx0C,EAAMu0C,aAAqBuB,GAAgB,CAC/C,IAAIC,EAAoBD,EAAcjpB,MAAQ0oB,EAE9C,OAAO,IAAII,EAAc,WAAar0C,EAAW,KAAOkzC,EAA/B,gBA0OToB,EA3OmB51C,EAAMu0C,IA4O9Bx1C,aAAgB62C,EAAU72C,YAAY8tB,KAG9C+oB,EAAU72C,YAAY8tB,KAFpB0oB,GA5O0G,kBAAoBtB,EAA1G,4BAA+J8B,EAAoB,KAC9M,CAyOJ,IAAsBH,EAxOlB,OAAO,IACT,EAEF,EAnKEtY,KAwPOmY,EANP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,GAC1D,OAAKwB,EAAOh2C,EAAMu0C,IAGX,KAFE,IAAIoB,EAAc,WAAar0C,EAAW,KAAOkzC,EAA/B,kBAAwEP,EAAgB,2BAGrH,GAtPAc,SAwLF,SAAmCW,GAoBjC,OAAOD,EAnBP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,GAC1D,GAA2B,mBAAhBkB,EACT,OAAO,IAAIC,EAAc,aAAenB,EAAe,mBAAqBP,EAAgB,oDAE9F,IAAI2B,EAAY51C,EAAMu0C,GAClB0B,EAAWJ,EAAYD,GAC3B,GAAiB,WAAbK,EACF,OAAO,IAAIN,EAAc,WAAar0C,EAAW,KAAOkzC,EAA/B,cAAoEyB,EAAW,kBAAoBhC,EAAgB,0BAE9I,IAAK,IAAIp/B,KAAO+gC,EACd,GAAIA,EAAUv4C,eAAewX,GAAM,CACjC,IAAItV,EAAQm2C,EAAYE,EAAW/gC,EAAKo/B,EAAe3yC,EAAUkzC,EAAe,IAAM3/B,EAAKi/B,GAC3F,GAAIv0C,aAAiBlG,MACnB,OAAOkG,CAEX,CAEF,OAAO,IACT,EAEF,EA5MEy1C,MAmKF,SAA+BkB,GAC7B,OAAK15C,MAAMiqC,QAAQyP,GAgBZT,EAXP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,GAE1D,IADA,IAAIoB,EAAY51C,EAAMu0C,GACb79C,EAAI,EAAGA,EAAIw/C,EAAez/C,OAAQC,IACzC,GAAIy/C,EAAGP,EAAWM,EAAex/C,IAC/B,OAAO,KAKX,OAAO,IAAIi/C,EAAc,WAAar0C,EAAW,KAAOkzC,EAAe,eAAiBoB,EAA/D,kBAAqG3B,EAAgB,sBAD3HmC,KAAKC,UAAUH,GACmJ,IACvL,IAd2B,eAAzBzK,EAAQ1R,IAAIwU,UAA4BY,GAAQ,EAAO,sEAChDrB,EAAcI,gBAezB,EApLEqF,UA6MF,SAAgC+C,GAC9B,IAAK95C,MAAMiqC,QAAQ6P,GAEjB,MADyB,eAAzB7K,EAAQ1R,IAAIwU,UAA4BY,GAAQ,EAAO,0EAChDrB,EAAcI,gBAGvB,IAAK,IAAIx3C,EAAI,EAAGA,EAAI4/C,EAAoB7/C,OAAQC,IAAK,CACnD,IAAI6/C,EAAUD,EAAoB5/C,GAClC,GAAuB,mBAAZ6/C,EAQT,OAPApH,GACE,EACA,4GAEAqH,EAAyBD,GACzB7/C,GAEKo3C,EAAcI,eAEzB,CAYA,OAAOuH,EAVP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,GAC1D,IAAK,IAAI99C,EAAI,EAAGA,EAAI4/C,EAAoB7/C,OAAQC,IAE9C,GAA6F,OAAzF6/C,EADUD,EAAoB5/C,IACtBsJ,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,EAAcV,GAClE,OAAO,KAIX,OAAO,IAAI6B,EAAc,WAAar0C,EAAW,KAAOkzC,EAA/B,kBAAwEP,EAAgB,KACnH,EAEF,EA3OET,MAuPF,SAAgCiD,GAmB9B,OAAOhB,EAlBP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,GAC1D,IAAIoB,EAAY51C,EAAMu0C,GAClB0B,EAAWJ,EAAYD,GAC3B,GAAiB,WAAbK,EACF,OAAO,IAAIN,EAAc,WAAar0C,EAAW,KAAOkzC,EAAe,cAAgByB,EAA9D,kBAAmGhC,EAAgB,yBAE9I,IAAK,IAAIp/B,KAAO4hC,EAAY,CAC1B,IAAIF,EAAUE,EAAW5hC,GACzB,GAAK0hC,EAAL,CAGA,IAAIh3C,EAAQg3C,EAAQX,EAAW/gC,EAAKo/B,EAAe3yC,EAAUkzC,EAAe,IAAM3/B,EAAKi/B,GACvF,GAAIv0C,EACF,OAAOA,CAHT,CAKF,CACA,OAAO,IACT,EAEF,GAnQA,SAAS42C,EAAG1+C,EAAGC,GAEb,OAAID,IAAMC,EAGK,IAAND,GAAW,EAAIA,GAAM,EAAIC,EAGzBD,GAAMA,GAAKC,GAAMA,CAE5B,CAUA,SAASi+C,EAAcl4C,GACrB3B,KAAK2B,QAAUA,EACf3B,KAAKu4C,MAAQ,EACf,CAIA,SAASoB,EAA2BiB,GAClC,GAA6B,eAAzBjL,EAAQ1R,IAAIwU,SACd,IAAIoI,EAA0B,CAAC,EAC3BC,EAA6B,EAEnC,SAASC,EAAUjE,EAAY5yC,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,EAAcC,GAIrF,GAHAR,EAAgBA,GAAiBsB,EACjCf,EAAeA,GAAgBD,EAE3BE,IAAWX,EACb,GAAIsB,EAEF97C,GACE,EACA,0LAIG,GAA6B,eAAzBmyC,EAAQ1R,IAAIwU,UAAgD,oBAAZa,QAAyB,CAElF,IAAI0H,EAAW7C,EAAgB,IAAMM,GAElCoC,EAAwBG,IAEzBF,EAA6B,IAE7BzH,GACE,EACA,8SAKAqF,EACAP,GAEF0C,EAAwBG,IAAY,EACpCF,IAEJ,CAEF,OAAuB,MAAnB52C,EAAMu0C,GACJ3B,EACsB,OAApB5yC,EAAMu0C,GACD,IAAIoB,EAAc,OAASr0C,EAAW,KAAOkzC,EAA3B,+BAAiFP,EAAgB,+BAErH,IAAI0B,EAAc,OAASr0C,EAAW,KAAOkzC,EAA3B,+BAAiFP,EAAgB,oCAErH,KAEAyC,EAAS12C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,EAE9D,CAEA,IAAIuC,EAAmBF,EAAU75C,KAAK,MAAM,GAG5C,OAFA+5C,EAAiBnE,WAAaiE,EAAU75C,KAAK,MAAM,GAE5C+5C,CACT,CAEA,SAASvB,EAA2BwB,GAclC,OAAOvB,EAbP,SAAkBz1C,EAAOu0C,EAAUN,EAAe3yC,EAAUkzC,EAAcC,GACxE,IAAImB,EAAY51C,EAAMu0C,GAEtB,OADesB,EAAYD,KACVoB,EAMR,IAAIrB,EAAc,WAAar0C,EAAW,KAAOkzC,EAA/B,cAFPyC,EAAerB,GAE0E,kBAAoB3B,EAAtG,gBAA+I+C,EAAe,MAElL,IACT,EAEF,CA+JA,SAAShB,EAAOJ,GACd,cAAeA,GACb,IAAK,SACL,IAAK,SACL,IAAK,YACH,OAAO,EACT,IAAK,UACH,OAAQA,EACV,IAAK,SACH,GAAIp5C,MAAMiqC,QAAQmP,GAChB,OAAOA,EAAUt/B,MAAM0/B,GAEzB,GAAkB,OAAdJ,GAAsBT,EAAeS,GACvC,OAAO,EAGT,IAAIsB,EAxWV,SAAuBC,GACrB,IAAID,EAAaC,IAAkB9B,GAAmB8B,EAAc9B,IAAoB8B,EAjB/D,eAkBzB,GAA0B,mBAAfD,EACT,OAAOA,CAEX,CAmWuBE,CAAcxB,GAC/B,IAAIsB,EAqBF,OAAO,EApBP,IACIG,EADA/B,EAAW4B,EAAW55C,KAAKs4C,GAE/B,GAAIsB,IAAetB,EAAUz6B,SAC3B,OAASk8B,EAAO/B,EAAS57B,QAAQ49B,MAC/B,IAAKtB,EAAOqB,EAAKr+C,OACf,OAAO,OAKX,OAASq+C,EAAO/B,EAAS57B,QAAQ49B,MAAM,CACrC,IAAIh8B,EAAQ+7B,EAAKr+C,MACjB,GAAIsiB,IACG06B,EAAO16B,EAAM,IAChB,OAAO,CAGb,CAMJ,OAAO,EACT,QACE,OAAO,EAEb,CAsBA,SAASu6B,EAAYD,GACnB,IAAIK,SAAkBL,EACtB,OAAIp5C,MAAMiqC,QAAQmP,GACT,QAELA,aAAqBzU,OAIhB,SA7BX,SAAkB8U,EAAUL,GAE1B,MAAiB,WAAbK,GAK+B,WAA/BL,EAAU,kBAKQ,mBAAXpG,QAAyBoG,aAAqBpG,MAK3D,CAcM+H,CAAStB,EAAUL,GACd,SAEFK,CACT,CAIA,SAASgB,EAAerB,GACtB,GAAI,MAAOA,EACT,MAAO,GAAKA,EAEd,IAAIK,EAAWJ,EAAYD,GAC3B,GAAiB,WAAbK,EAAuB,CACzB,GAAIL,aAAqBjyB,KACvB,MAAO,OACF,GAAIiyB,aAAqBzU,OAC9B,MAAO,QAEX,CACA,OAAO8U,CACT,CAIA,SAASO,EAAyBx9C,GAChC,IAAIwE,EAAOy5C,EAAej+C,GAC1B,OAAQwE,GACN,IAAK,QACL,IAAK,SACH,MAAO,MAAQA,EACjB,IAAK,UACL,IAAK,OACL,IAAK,SACH,MAAO,KAAOA,EAChB,QACE,OAAOA,EAEb,CAaA,OA7WAm4C,EAAc/2C,UAAYvF,MAAMuF,UA0WhC+1C,EAAeM,eAAiBA,EAChCN,EAAeO,UAAYP,EAEpBA,CACT,CAE2B,GAAEr3C,KAAKytC,EAAS,EAAoB,GAExD,GAEP,EA7gDEC,EAAOD,QAAUL,EAAQ,EAAQ,M,6DCInC,SAAS8M,EAA0Cv5C,GAC/C,MAAQwsB,IAAKA,EAAKxf,IAAKA,EAAKu+B,SAAUA,GAAavrC,GACnD,EAAI,aAAkB,KAClB,IAAIylC,EAAUjZ,aAAiC,EAASA,EAAIp0B,QAC5D,GAAKqtC,EAAL,CACA,QAPoC,IAA1B5pC,OAAO29C,eASb,OADA39C,OAAOuE,iBAAiB,SAAUmrC,GAAU,GACrC,KACH1vC,OAAOyE,oBAAoB,SAAUirC,GAAU,IAEhD,CACH,MAAMkO,EAAyB,IAAI59C,OAAO29C,eAAgBt8B,IACjDA,EAAQ1kB,QACb+yC,MAKJ,OAHAkO,EAAuBC,QAAQjU,EAAS,CACpCz4B,IAAKA,IAEF,KACCy4B,GAASgU,EAAuBE,UAAUlU,GAEtD,CAjBoB,GAkBrB,CACC8F,EACA/e,EACAxf,GAER,C", "sources": ["webpack://grafana-lokiexplore-app/../node_modules/use-memo-one/dist/use-memo-one.esm.js", "webpack://grafana-lokiexplore-app/../node_modules/tiny-invariant/dist/esm/tiny-invariant.js", "webpack://grafana-lokiexplore-app/../node_modules/css-box-model/dist/css-box-model.esm.js", "webpack://grafana-lokiexplore-app/../node_modules/@hello-pangea/dnd/node_modules/memoize-one/dist/memoize-one.esm.js", "webpack://grafana-lokiexplore-app/../node_modules/raf-schd/dist/raf-schd.esm.js", "webpack://grafana-lokiexplore-app/../node_modules/@babel/runtime/helpers/esm/extends.js", "webpack://grafana-lokiexplore-app/../node_modules/@hello-pangea/dnd/dist/dnd.esm.js", "webpack://grafana-lokiexplore-app/../node_modules/re-resizable/lib/index.js", "webpack://grafana-lokiexplore-app/../node_modules/re-resizable/lib/resizer.js", "webpack://grafana-lokiexplore-app/../node_modules/react-scroll-sync/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@react-aria/utils/dist/useResizeObserver.mjs"], "sourcesContent": ["import { useState, useRef, useEffect } from 'react';\n\nfunction areInputsEqual(newInputs, lastInputs) {\n  if (newInputs.length !== lastInputs.length) {\n    return false;\n  }\n\n  for (var i = 0; i < newInputs.length; i++) {\n    if (newInputs[i] !== lastInputs[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction useMemoOne(getResult, inputs) {\n  var initial = useState(function () {\n    return {\n      inputs: inputs,\n      result: getResult()\n    };\n  })[0];\n  var isFirstRun = useRef(true);\n  var committed = useRef(initial);\n  var useCache = isFirstRun.current || Boolean(inputs && committed.current.inputs && areInputsEqual(inputs, committed.current.inputs));\n  var cache = useCache ? committed.current : {\n    inputs: inputs,\n    result: getResult()\n  };\n  useEffect(function () {\n    isFirstRun.current = false;\n    committed.current = cache;\n  }, [cache]);\n  return cache.result;\n}\nfunction useCallbackOne(callback, inputs) {\n  return useMemoOne(function () {\n    return callback;\n  }, inputs);\n}\nvar useMemo = useMemoOne;\nvar useCallback = useCallbackOne;\n\nexport { useCallback, useCallbackOne, useMemo, useMemoOne };\n", "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n", "import invariant from 'tiny-invariant';\n\nvar getRect = function getRect(_ref) {\n  var top = _ref.top,\n      right = _ref.right,\n      bottom = _ref.bottom,\n      left = _ref.left;\n  var width = right - left;\n  var height = bottom - top;\n  var rect = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left,\n    width: width,\n    height: height,\n    x: left,\n    y: top,\n    center: {\n      x: (right + left) / 2,\n      y: (bottom + top) / 2\n    }\n  };\n  return rect;\n};\nvar expand = function expand(target, expandBy) {\n  return {\n    top: target.top - expandBy.top,\n    left: target.left - expandBy.left,\n    bottom: target.bottom + expandBy.bottom,\n    right: target.right + expandBy.right\n  };\n};\nvar shrink = function shrink(target, shrinkBy) {\n  return {\n    top: target.top + shrinkBy.top,\n    left: target.left + shrinkBy.left,\n    bottom: target.bottom - shrinkBy.bottom,\n    right: target.right - shrinkBy.right\n  };\n};\n\nvar shift = function shift(target, shiftBy) {\n  return {\n    top: target.top + shiftBy.y,\n    left: target.left + shiftBy.x,\n    bottom: target.bottom + shiftBy.y,\n    right: target.right + shiftBy.x\n  };\n};\n\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nvar createBox = function createBox(_ref2) {\n  var borderBox = _ref2.borderBox,\n      _ref2$margin = _ref2.margin,\n      margin = _ref2$margin === void 0 ? noSpacing : _ref2$margin,\n      _ref2$border = _ref2.border,\n      border = _ref2$border === void 0 ? noSpacing : _ref2$border,\n      _ref2$padding = _ref2.padding,\n      padding = _ref2$padding === void 0 ? noSpacing : _ref2$padding;\n  var marginBox = getRect(expand(borderBox, margin));\n  var paddingBox = getRect(shrink(borderBox, border));\n  var contentBox = getRect(shrink(paddingBox, padding));\n  return {\n    marginBox: marginBox,\n    borderBox: getRect(borderBox),\n    paddingBox: paddingBox,\n    contentBox: contentBox,\n    margin: margin,\n    border: border,\n    padding: padding\n  };\n};\n\nvar parse = function parse(raw) {\n  var value = raw.slice(0, -2);\n  var suffix = raw.slice(-2);\n\n  if (suffix !== 'px') {\n    return 0;\n  }\n\n  var result = Number(value);\n  !!isNaN(result) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Could not parse value [raw: \" + raw + \", without suffix: \" + value + \"]\") : invariant(false) : void 0;\n  return result;\n};\n\nvar getWindowScroll = function getWindowScroll() {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n};\n\nvar offset = function offset(original, change) {\n  var borderBox = original.borderBox,\n      border = original.border,\n      margin = original.margin,\n      padding = original.padding;\n  var shifted = shift(borderBox, change);\n  return createBox({\n    borderBox: shifted,\n    border: border,\n    margin: margin,\n    padding: padding\n  });\n};\nvar withScroll = function withScroll(original, scroll) {\n  if (scroll === void 0) {\n    scroll = getWindowScroll();\n  }\n\n  return offset(original, scroll);\n};\nvar calculateBox = function calculateBox(borderBox, styles) {\n  var margin = {\n    top: parse(styles.marginTop),\n    right: parse(styles.marginRight),\n    bottom: parse(styles.marginBottom),\n    left: parse(styles.marginLeft)\n  };\n  var padding = {\n    top: parse(styles.paddingTop),\n    right: parse(styles.paddingRight),\n    bottom: parse(styles.paddingBottom),\n    left: parse(styles.paddingLeft)\n  };\n  var border = {\n    top: parse(styles.borderTopWidth),\n    right: parse(styles.borderRightWidth),\n    bottom: parse(styles.borderBottomWidth),\n    left: parse(styles.borderLeftWidth)\n  };\n  return createBox({\n    borderBox: borderBox,\n    margin: margin,\n    padding: padding,\n    border: border\n  });\n};\nvar getBox = function getBox(el) {\n  var borderBox = el.getBoundingClientRect();\n  var styles = window.getComputedStyle(el);\n  return calculateBox(borderBox, styles);\n};\n\nexport { calculateBox, createBox, expand, getBox, getRect, offset, shrink, withScroll };\n", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var cache = null;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (cache && cache.lastThis === this && isEqual(newArgs, cache.lastArgs)) {\n            return cache.lastResult;\n        }\n        var lastResult = resultFn.apply(this, newArgs);\n        cache = {\n            lastResult: lastResult,\n            lastArgs: newArgs,\n            lastThis: this,\n        };\n        return lastResult;\n    }\n    memoized.clear = function clear() {\n        cache = null;\n    };\n    return memoized;\n}\n\nexport { memoizeOne as default };\n", "var rafSchd = function rafSchd(fn) {\n  var lastArgs = [];\n  var frameId = null;\n\n  var wrapperFn = function wrapperFn() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    lastArgs = args;\n\n    if (frameId) {\n      return;\n    }\n\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      fn.apply(void 0, lastArgs);\n    });\n  };\n\n  wrapperFn.cancel = function () {\n    if (!frameId) {\n      return;\n    }\n\n    cancelAnimationFrame(frameId);\n    frameId = null;\n  };\n\n  return wrapperFn;\n};\n\nexport default rafSchd;\n", "function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };", "import React, { useLayoutEffect as useLayoutEffect$1, useEffect, useRef, useState, useContext } from 'react';\nimport ReactDOM, { flushSync } from 'react-dom';\nimport { createStore as createStore$1, applyMiddleware, compose, bindActionCreators } from 'redux';\nimport { Provider, connect } from 'react-redux';\nimport { useMemo, useCallback } from 'use-memo-one';\nimport { getRect, expand, offset, withScroll, calculateBox, getBox, createBox } from 'css-box-model';\nimport memoizeOne from 'memoize-one';\nimport rafSchd from 'raf-schd';\nimport _extends from '@babel/runtime/helpers/esm/extends';\n\nconst isProduction$1 = process.env.NODE_ENV === 'production';\nconst spacesAndTabs = /[ \\t]{2,}/g;\nconst lineStartWithSpaces = /^[ \\t]*/gm;\nconst clean$2 = value => value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\nconst getDevMessage = message => clean$2(`\n  %c@hello-pangea/dnd\n\n  %c${clean$2(message)}\n\n  %c👷‍ This is a development only message. It will be removed in production builds.\n`);\nconst getFormattedMessage = message => [getDevMessage(message), 'color: #00C584; font-size: 1.2em; font-weight: bold;', 'line-height: 1.5', 'color: #723874;'];\nconst isDisabledFlag = '__@hello-pangea/dnd-disable-dev-warnings';\nfunction log(type, message) {\n  if (isProduction$1) {\n    return;\n  }\n  if (typeof window !== 'undefined' && window[isDisabledFlag]) {\n    return;\n  }\n  console[type](...getFormattedMessage(message));\n}\nconst warning = log.bind(null, 'warn');\nconst error = log.bind(null, 'error');\n\nfunction noop$2() {}\n\nfunction getOptions(shared, fromBinding) {\n  return {\n    ...shared,\n    ...fromBinding\n  };\n}\nfunction bindEvents(el, bindings, sharedOptions) {\n  const unbindings = bindings.map(binding => {\n    const options = getOptions(sharedOptions, binding.options);\n    el.addEventListener(binding.eventName, binding.fn, options);\n    return function unbind() {\n      el.removeEventListener(binding.eventName, binding.fn, options);\n    };\n  });\n  return function unbindAll() {\n    unbindings.forEach(unbind => {\n      unbind();\n    });\n  };\n}\n\nconst isProduction = process.env.NODE_ENV === 'production';\nconst prefix$1 = 'Invariant failed';\nclass RbdInvariant extends Error {}\nRbdInvariant.prototype.toString = function toString() {\n  return this.message;\n};\nfunction invariant(condition, message) {\n  if (condition) {\n    return;\n  }\n  if (isProduction) {\n    throw new RbdInvariant(prefix$1);\n  } else {\n    throw new RbdInvariant(`${prefix$1}: ${message || ''}`);\n  }\n}\n\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.callbacks = null;\n    this.unbind = noop$2;\n    this.onWindowError = event => {\n      const callbacks = this.getCallbacks();\n      if (callbacks.isDragging()) {\n        callbacks.tryAbort();\n        process.env.NODE_ENV !== \"production\" ? warning(`\n        An error was caught by our window 'error' event listener while a drag was occurring.\n        The active drag has been aborted.\n      `) : void 0;\n      }\n      const err = event.error;\n      if (err instanceof RbdInvariant) {\n        event.preventDefault();\n        if (process.env.NODE_ENV !== 'production') {\n          error(err.message);\n        }\n      }\n    };\n    this.getCallbacks = () => {\n      if (!this.callbacks) {\n        throw new Error('Unable to find AppCallbacks in <ErrorBoundary/>');\n      }\n      return this.callbacks;\n    };\n    this.setCallbacks = callbacks => {\n      this.callbacks = callbacks;\n    };\n  }\n  componentDidMount() {\n    this.unbind = bindEvents(window, [{\n      eventName: 'error',\n      fn: this.onWindowError\n    }]);\n  }\n  componentDidCatch(err) {\n    if (err instanceof RbdInvariant) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(err.message);\n      }\n      this.setState({});\n      return;\n    }\n    throw err;\n  }\n  componentWillUnmount() {\n    this.unbind();\n  }\n  render() {\n    return this.props.children(this.setCallbacks);\n  }\n}\n\nconst dragHandleUsageInstructions = `\n  Press space bar to start a drag.\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\n  Some screen readers may require you to be in focus mode or to use your pass through key\n`;\nconst position = index => index + 1;\nconst onDragStart = start => `\n  You have lifted an item in position ${position(start.source.index)}\n`;\nconst withLocation = (source, destination) => {\n  const isInHomeList = source.droppableId === destination.droppableId;\n  const startPosition = position(source.index);\n  const endPosition = position(destination.index);\n  if (isInHomeList) {\n    return `\n      You have moved the item from position ${startPosition}\n      to position ${endPosition}\n    `;\n  }\n  return `\n    You have moved the item from position ${startPosition}\n    in list ${source.droppableId}\n    to list ${destination.droppableId}\n    in position ${endPosition}\n  `;\n};\nconst withCombine = (id, source, combine) => {\n  const inHomeList = source.droppableId === combine.droppableId;\n  if (inHomeList) {\n    return `\n      The item ${id}\n      has been combined with ${combine.draggableId}`;\n  }\n  return `\n      The item ${id}\n      in list ${source.droppableId}\n      has been combined with ${combine.draggableId}\n      in list ${combine.droppableId}\n    `;\n};\nconst onDragUpdate = update => {\n  const location = update.destination;\n  if (location) {\n    return withLocation(update.source, location);\n  }\n  const combine = update.combine;\n  if (combine) {\n    return withCombine(update.draggableId, update.source, combine);\n  }\n  return 'You are over an area that cannot be dropped on';\n};\nconst returnedToStart = source => `\n  The item has returned to its starting position\n  of ${position(source.index)}\n`;\nconst onDragEnd = result => {\n  if (result.reason === 'CANCEL') {\n    return `\n      Movement cancelled.\n      ${returnedToStart(result.source)}\n    `;\n  }\n  const location = result.destination;\n  const combine = result.combine;\n  if (location) {\n    return `\n      You have dropped the item.\n      ${withLocation(result.source, location)}\n    `;\n  }\n  if (combine) {\n    return `\n      You have dropped the item.\n      ${withCombine(result.draggableId, result.source, combine)}\n    `;\n  }\n  return `\n    The item has been dropped while not over a drop area.\n    ${returnedToStart(result.source)}\n  `;\n};\nconst preset = {\n  dragHandleUsageInstructions,\n  onDragStart,\n  onDragUpdate,\n  onDragEnd\n};\nvar preset$1 = preset;\n\nconst origin = {\n  x: 0,\n  y: 0\n};\nconst add = (point1, point2) => ({\n  x: point1.x + point2.x,\n  y: point1.y + point2.y\n});\nconst subtract = (point1, point2) => ({\n  x: point1.x - point2.x,\n  y: point1.y - point2.y\n});\nconst isEqual$1 = (point1, point2) => point1.x === point2.x && point1.y === point2.y;\nconst negate = point => ({\n  x: point.x !== 0 ? -point.x : 0,\n  y: point.y !== 0 ? -point.y : 0\n});\nconst patch = (line, value, otherValue = 0) => {\n  if (line === 'x') {\n    return {\n      x: value,\n      y: otherValue\n    };\n  }\n  return {\n    x: otherValue,\n    y: value\n  };\n};\nconst distance = (point1, point2) => Math.sqrt((point2.x - point1.x) ** 2 + (point2.y - point1.y) ** 2);\nconst closest$1 = (target, points) => Math.min(...points.map(point => distance(target, point)));\nconst apply = fn => point => ({\n  x: fn(point.x),\n  y: fn(point.y)\n});\n\nvar executeClip = ((frame, subject) => {\n  const result = getRect({\n    top: Math.max(subject.top, frame.top),\n    right: Math.min(subject.right, frame.right),\n    bottom: Math.min(subject.bottom, frame.bottom),\n    left: Math.max(subject.left, frame.left)\n  });\n  if (result.width <= 0 || result.height <= 0) {\n    return null;\n  }\n  return result;\n});\n\nconst offsetByPosition = (spacing, point) => ({\n  top: spacing.top + point.y,\n  left: spacing.left + point.x,\n  bottom: spacing.bottom + point.y,\n  right: spacing.right + point.x\n});\nconst getCorners = spacing => [{\n  x: spacing.left,\n  y: spacing.top\n}, {\n  x: spacing.right,\n  y: spacing.top\n}, {\n  x: spacing.left,\n  y: spacing.bottom\n}, {\n  x: spacing.right,\n  y: spacing.bottom\n}];\nconst noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\n\nconst scroll$1 = (target, frame) => {\n  if (!frame) {\n    return target;\n  }\n  return offsetByPosition(target, frame.scroll.diff.displacement);\n};\nconst increase = (target, axis, withPlaceholder) => {\n  if (withPlaceholder && withPlaceholder.increasedBy) {\n    return {\n      ...target,\n      [axis.end]: target[axis.end] + withPlaceholder.increasedBy[axis.line]\n    };\n  }\n  return target;\n};\nconst clip = (target, frame) => {\n  if (frame && frame.shouldClipSubject) {\n    return executeClip(frame.pageMarginBox, target);\n  }\n  return getRect(target);\n};\nvar getSubject = (({\n  page,\n  withPlaceholder,\n  axis,\n  frame\n}) => {\n  const scrolled = scroll$1(page.marginBox, frame);\n  const increased = increase(scrolled, axis, withPlaceholder);\n  const clipped = clip(increased, frame);\n  return {\n    page,\n    withPlaceholder,\n    active: clipped\n  };\n});\n\nvar scrollDroppable = ((droppable, newScroll) => {\n  !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  const scrollable = droppable.frame;\n  const scrollDiff = subtract(newScroll, scrollable.scroll.initial);\n  const scrollDisplacement = negate(scrollDiff);\n  const frame = {\n    ...scrollable,\n    scroll: {\n      initial: scrollable.scroll.initial,\n      current: newScroll,\n      diff: {\n        value: scrollDiff,\n        displacement: scrollDisplacement\n      },\n      max: scrollable.scroll.max\n    }\n  };\n  const subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: droppable.subject.withPlaceholder,\n    axis: droppable.axis,\n    frame\n  });\n  const result = {\n    ...droppable,\n    frame,\n    subject\n  };\n  return result;\n});\n\nconst toDroppableMap = memoizeOne(droppables => droppables.reduce((previous, current) => {\n  previous[current.descriptor.id] = current;\n  return previous;\n}, {}));\nconst toDraggableMap = memoizeOne(draggables => draggables.reduce((previous, current) => {\n  previous[current.descriptor.id] = current;\n  return previous;\n}, {}));\nconst toDroppableList = memoizeOne(droppables => Object.values(droppables));\nconst toDraggableList = memoizeOne(draggables => Object.values(draggables));\n\nvar getDraggablesInsideDroppable = memoizeOne((droppableId, draggables) => {\n  const result = toDraggableList(draggables).filter(draggable => droppableId === draggable.descriptor.droppableId).sort((a, b) => a.descriptor.index - b.descriptor.index);\n  return result;\n});\n\nfunction tryGetDestination(impact) {\n  if (impact.at && impact.at.type === 'REORDER') {\n    return impact.at.destination;\n  }\n  return null;\n}\nfunction tryGetCombine(impact) {\n  if (impact.at && impact.at.type === 'COMBINE') {\n    return impact.at.combine;\n  }\n  return null;\n}\n\nvar removeDraggableFromList = memoizeOne((remove, list) => list.filter(item => item.descriptor.id !== remove.descriptor.id));\n\nvar moveToNextCombine = (({\n  isMovingForward,\n  draggable,\n  destination,\n  insideDestination,\n  previousImpact\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const location = tryGetDestination(previousImpact);\n  if (!location) {\n    return null;\n  }\n  function getImpact(target) {\n    const at = {\n      type: 'COMBINE',\n      combine: {\n        draggableId: target,\n        droppableId: destination.descriptor.id\n      }\n    };\n    return {\n      ...previousImpact,\n      at\n    };\n  }\n  const all = previousImpact.displaced.all;\n  const closestId = all.length ? all[0] : null;\n  if (isMovingForward) {\n    return closestId ? getImpact(closestId) : null;\n  }\n  const withoutDraggable = removeDraggableFromList(draggable, insideDestination);\n  if (!closestId) {\n    if (!withoutDraggable.length) {\n      return null;\n    }\n    const last = withoutDraggable[withoutDraggable.length - 1];\n    return getImpact(last.descriptor.id);\n  }\n  const indexOfClosest = withoutDraggable.findIndex(d => d.descriptor.id === closestId);\n  !(indexOfClosest !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find displaced item in set') : invariant(false) : void 0;\n  const proposedIndex = indexOfClosest - 1;\n  if (proposedIndex < 0) {\n    return null;\n  }\n  const before = withoutDraggable[proposedIndex];\n  return getImpact(before.descriptor.id);\n});\n\nvar isHomeOf = ((draggable, destination) => draggable.descriptor.droppableId === destination.descriptor.id);\n\nconst noDisplacedBy = {\n  point: origin,\n  value: 0\n};\nconst emptyGroups = {\n  invisible: {},\n  visible: {},\n  all: []\n};\nconst noImpact = {\n  displaced: emptyGroups,\n  displacedBy: noDisplacedBy,\n  at: null\n};\nvar noImpact$1 = noImpact;\n\nvar isWithin = ((lowerBound, upperBound) => value => lowerBound <= value && value <= upperBound);\n\nvar isPartiallyVisibleThroughFrame = (frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    const isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    if (isContained) {\n      return true;\n    }\n    const isPartiallyVisibleVertically = isWithinVertical(subject.top) || isWithinVertical(subject.bottom);\n    const isPartiallyVisibleHorizontally = isWithinHorizontal(subject.left) || isWithinHorizontal(subject.right);\n    const isPartiallyContained = isPartiallyVisibleVertically && isPartiallyVisibleHorizontally;\n    if (isPartiallyContained) {\n      return true;\n    }\n    const isBiggerVertically = subject.top < frame.top && subject.bottom > frame.bottom;\n    const isBiggerHorizontally = subject.left < frame.left && subject.right > frame.right;\n    const isTargetBiggerThanFrame = isBiggerVertically && isBiggerHorizontally;\n    if (isTargetBiggerThanFrame) {\n      return true;\n    }\n    const isTargetBiggerOnOneAxis = isBiggerVertically && isPartiallyVisibleHorizontally || isBiggerHorizontally && isPartiallyVisibleVertically;\n    return isTargetBiggerOnOneAxis;\n  };\n});\n\nvar isTotallyVisibleThroughFrame = (frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    const isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    return isContained;\n  };\n});\n\nconst vertical = {\n  direction: 'vertical',\n  line: 'y',\n  crossAxisLine: 'x',\n  start: 'top',\n  end: 'bottom',\n  size: 'height',\n  crossAxisStart: 'left',\n  crossAxisEnd: 'right',\n  crossAxisSize: 'width'\n};\nconst horizontal = {\n  direction: 'horizontal',\n  line: 'x',\n  crossAxisLine: 'y',\n  start: 'left',\n  end: 'right',\n  size: 'width',\n  crossAxisStart: 'top',\n  crossAxisEnd: 'bottom',\n  crossAxisSize: 'height'\n};\n\nvar isTotallyVisibleThroughFrameOnAxis = (axis => frame => {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return subject => {\n    if (axis === vertical) {\n      return isWithinVertical(subject.top) && isWithinVertical(subject.bottom);\n    }\n    return isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n  };\n});\n\nconst getDroppableDisplaced = (target, destination) => {\n  const displacement = destination.frame ? destination.frame.scroll.diff.displacement : origin;\n  return offsetByPosition(target, displacement);\n};\nconst isVisibleInDroppable = (target, destination, isVisibleThroughFrameFn) => {\n  if (!destination.subject.active) {\n    return false;\n  }\n  return isVisibleThroughFrameFn(destination.subject.active)(target);\n};\nconst isVisibleInViewport = (target, viewport, isVisibleThroughFrameFn) => isVisibleThroughFrameFn(viewport)(target);\nconst isVisible$1 = ({\n  target: toBeDisplaced,\n  destination,\n  viewport,\n  withDroppableDisplacement,\n  isVisibleThroughFrameFn\n}) => {\n  const displacedTarget = withDroppableDisplacement ? getDroppableDisplaced(toBeDisplaced, destination) : toBeDisplaced;\n  return isVisibleInDroppable(displacedTarget, destination, isVisibleThroughFrameFn) && isVisibleInViewport(displacedTarget, viewport, isVisibleThroughFrameFn);\n};\nconst isPartiallyVisible = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isPartiallyVisibleThroughFrame\n});\nconst isTotallyVisible = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isTotallyVisibleThroughFrame\n});\nconst isTotallyVisibleOnAxis = args => isVisible$1({\n  ...args,\n  isVisibleThroughFrameFn: isTotallyVisibleThroughFrameOnAxis(args.destination.axis)\n});\n\nconst getShouldAnimate = (id, last, forceShouldAnimate) => {\n  if (typeof forceShouldAnimate === 'boolean') {\n    return forceShouldAnimate;\n  }\n  if (!last) {\n    return true;\n  }\n  const {\n    invisible,\n    visible\n  } = last;\n  if (invisible[id]) {\n    return false;\n  }\n  const previous = visible[id];\n  return previous ? previous.shouldAnimate : true;\n};\nfunction getTarget(draggable, displacedBy) {\n  const marginBox = draggable.page.marginBox;\n  const expandBy = {\n    top: displacedBy.point.y,\n    right: 0,\n    bottom: 0,\n    left: displacedBy.point.x\n  };\n  return getRect(expand(marginBox, expandBy));\n}\nfunction getDisplacementGroups({\n  afterDragging,\n  destination,\n  displacedBy,\n  viewport,\n  forceShouldAnimate,\n  last\n}) {\n  return afterDragging.reduce(function process(groups, draggable) {\n    const target = getTarget(draggable, displacedBy);\n    const id = draggable.descriptor.id;\n    groups.all.push(id);\n    const isVisible = isPartiallyVisible({\n      target,\n      destination,\n      viewport,\n      withDroppableDisplacement: true\n    });\n    if (!isVisible) {\n      groups.invisible[draggable.descriptor.id] = true;\n      return groups;\n    }\n    const shouldAnimate = getShouldAnimate(id, last, forceShouldAnimate);\n    const displacement = {\n      draggableId: id,\n      shouldAnimate\n    };\n    groups.visible[id] = displacement;\n    return groups;\n  }, {\n    all: [],\n    visible: {},\n    invisible: {}\n  });\n}\n\nfunction getIndexOfLastItem(draggables, options) {\n  if (!draggables.length) {\n    return 0;\n  }\n  const indexOfLastItem = draggables[draggables.length - 1].descriptor.index;\n  return options.inHomeList ? indexOfLastItem : indexOfLastItem + 1;\n}\nfunction goAtEnd({\n  insideDestination,\n  inHomeList,\n  displacedBy,\n  destination\n}) {\n  const newIndex = getIndexOfLastItem(insideDestination, {\n    inHomeList\n  });\n  return {\n    displaced: emptyGroups,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: newIndex\n      }\n    }\n  };\n}\nfunction calculateReorderImpact({\n  draggable,\n  insideDestination,\n  destination,\n  viewport,\n  displacedBy,\n  last,\n  index,\n  forceShouldAnimate\n}) {\n  const inHomeList = isHomeOf(draggable, destination);\n  if (index == null) {\n    return goAtEnd({\n      insideDestination,\n      inHomeList,\n      displacedBy,\n      destination\n    });\n  }\n  const match = insideDestination.find(item => item.descriptor.index === index);\n  if (!match) {\n    return goAtEnd({\n      insideDestination,\n      inHomeList,\n      displacedBy,\n      destination\n    });\n  }\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const sliceFrom = insideDestination.indexOf(match);\n  const impacted = withoutDragging.slice(sliceFrom);\n  const displaced = getDisplacementGroups({\n    afterDragging: impacted,\n    destination,\n    displacedBy,\n    last,\n    viewport: viewport.frame,\n    forceShouldAnimate\n  });\n  return {\n    displaced,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index\n      }\n    }\n  };\n}\n\nfunction didStartAfterCritical(draggableId, afterCritical) {\n  return Boolean(afterCritical.effected[draggableId]);\n}\n\nvar fromCombine = (({\n  isMovingForward,\n  destination,\n  draggables,\n  combine,\n  afterCritical\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const combineId = combine.draggableId;\n  const combineWith = draggables[combineId];\n  const combineWithIndex = combineWith.descriptor.index;\n  const didCombineWithStartAfterCritical = didStartAfterCritical(combineId, afterCritical);\n  if (didCombineWithStartAfterCritical) {\n    if (isMovingForward) {\n      return combineWithIndex;\n    }\n    return combineWithIndex - 1;\n  }\n  if (isMovingForward) {\n    return combineWithIndex + 1;\n  }\n  return combineWithIndex;\n});\n\nvar fromReorder = (({\n  isMovingForward,\n  isInHomeList,\n  insideDestination,\n  location\n}) => {\n  if (!insideDestination.length) {\n    return null;\n  }\n  const currentIndex = location.index;\n  const proposedIndex = isMovingForward ? currentIndex + 1 : currentIndex - 1;\n  const firstIndex = insideDestination[0].descriptor.index;\n  const lastIndex = insideDestination[insideDestination.length - 1].descriptor.index;\n  const upperBound = isInHomeList ? lastIndex : lastIndex + 1;\n  if (proposedIndex < firstIndex) {\n    return null;\n  }\n  if (proposedIndex > upperBound) {\n    return null;\n  }\n  return proposedIndex;\n});\n\nvar moveToNextIndex = (({\n  isMovingForward,\n  isInHomeList,\n  draggable,\n  draggables,\n  destination,\n  insideDestination,\n  previousImpact,\n  viewport,\n  afterCritical\n}) => {\n  const wasAt = previousImpact.at;\n  !wasAt ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot move in direction without previous impact location') : invariant(false) : void 0;\n  if (wasAt.type === 'REORDER') {\n    const newIndex = fromReorder({\n      isMovingForward,\n      isInHomeList,\n      location: wasAt.destination,\n      insideDestination\n    });\n    if (newIndex == null) {\n      return null;\n    }\n    return calculateReorderImpact({\n      draggable,\n      insideDestination,\n      destination,\n      viewport,\n      last: previousImpact.displaced,\n      displacedBy: previousImpact.displacedBy,\n      index: newIndex\n    });\n  }\n  const newIndex = fromCombine({\n    isMovingForward,\n    destination,\n    displaced: previousImpact.displaced,\n    draggables,\n    combine: wasAt.combine,\n    afterCritical\n  });\n  if (newIndex == null) {\n    return null;\n  }\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    last: previousImpact.displaced,\n    displacedBy: previousImpact.displacedBy,\n    index: newIndex\n  });\n});\n\nvar getCombinedItemDisplacement = (({\n  displaced,\n  afterCritical,\n  combineWith,\n  displacedBy\n}) => {\n  const isDisplaced = Boolean(displaced.visible[combineWith] || displaced.invisible[combineWith]);\n  if (didStartAfterCritical(combineWith, afterCritical)) {\n    return isDisplaced ? origin : negate(displacedBy.point);\n  }\n  return isDisplaced ? displacedBy.point : origin;\n});\n\nvar whenCombining = (({\n  afterCritical,\n  impact,\n  draggables\n}) => {\n  const combine = tryGetCombine(impact);\n  !combine ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  const combineWith = combine.draggableId;\n  const center = draggables[combineWith].page.borderBox.center;\n  const displaceBy = getCombinedItemDisplacement({\n    displaced: impact.displaced,\n    afterCritical,\n    combineWith,\n    displacedBy: impact.displacedBy\n  });\n  return add(center, displaceBy);\n});\n\nconst distanceFromStartToBorderBoxCenter = (axis, box) => box.margin[axis.start] + box.borderBox[axis.size] / 2;\nconst distanceFromEndToBorderBoxCenter = (axis, box) => box.margin[axis.end] + box.borderBox[axis.size] / 2;\nconst getCrossAxisBorderBoxCenter = (axis, target, isMoving) => target[axis.crossAxisStart] + isMoving.margin[axis.crossAxisStart] + isMoving.borderBox[axis.crossAxisSize] / 2;\nconst goAfter = ({\n  axis,\n  moveRelativeTo,\n  isMoving\n}) => patch(axis.line, moveRelativeTo.marginBox[axis.end] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\nconst goBefore = ({\n  axis,\n  moveRelativeTo,\n  isMoving\n}) => patch(axis.line, moveRelativeTo.marginBox[axis.start] - distanceFromEndToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\nconst goIntoStart = ({\n  axis,\n  moveInto,\n  isMoving\n}) => patch(axis.line, moveInto.contentBox[axis.start] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveInto.contentBox, isMoving));\n\nvar whenReordering = (({\n  impact,\n  draggable,\n  draggables,\n  droppable,\n  afterCritical\n}) => {\n  const insideDestination = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  const draggablePage = draggable.page;\n  const axis = droppable.axis;\n  if (!insideDestination.length) {\n    return goIntoStart({\n      axis,\n      moveInto: droppable.page,\n      isMoving: draggablePage\n    });\n  }\n  const {\n    displaced,\n    displacedBy\n  } = impact;\n  const closestAfter = displaced.all[0];\n  if (closestAfter) {\n    const closest = draggables[closestAfter];\n    if (didStartAfterCritical(closestAfter, afterCritical)) {\n      return goBefore({\n        axis,\n        moveRelativeTo: closest.page,\n        isMoving: draggablePage\n      });\n    }\n    const withDisplacement = offset(closest.page, displacedBy.point);\n    return goBefore({\n      axis,\n      moveRelativeTo: withDisplacement,\n      isMoving: draggablePage\n    });\n  }\n  const last = insideDestination[insideDestination.length - 1];\n  if (last.descriptor.id === draggable.descriptor.id) {\n    return draggablePage.borderBox.center;\n  }\n  if (didStartAfterCritical(last.descriptor.id, afterCritical)) {\n    const page = offset(last.page, negate(afterCritical.displacedBy.point));\n    return goAfter({\n      axis,\n      moveRelativeTo: page,\n      isMoving: draggablePage\n    });\n  }\n  return goAfter({\n    axis,\n    moveRelativeTo: last.page,\n    isMoving: draggablePage\n  });\n});\n\nvar withDroppableDisplacement = ((droppable, point) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return point;\n  }\n  return add(point, frame.scroll.diff.displacement);\n});\n\nconst getResultWithoutDroppableDisplacement = ({\n  impact,\n  draggable,\n  droppable,\n  draggables,\n  afterCritical\n}) => {\n  const original = draggable.page.borderBox.center;\n  const at = impact.at;\n  if (!droppable) {\n    return original;\n  }\n  if (!at) {\n    return original;\n  }\n  if (at.type === 'REORDER') {\n    return whenReordering({\n      impact,\n      draggable,\n      draggables,\n      droppable,\n      afterCritical\n    });\n  }\n  return whenCombining({\n    impact,\n    draggables,\n    afterCritical\n  });\n};\nvar getPageBorderBoxCenterFromImpact = (args => {\n  const withoutDisplacement = getResultWithoutDroppableDisplacement(args);\n  const droppable = args.droppable;\n  const withDisplacement = droppable ? withDroppableDisplacement(droppable, withoutDisplacement) : withoutDisplacement;\n  return withDisplacement;\n});\n\nvar scrollViewport = ((viewport, newScroll) => {\n  const diff = subtract(newScroll, viewport.scroll.initial);\n  const displacement = negate(diff);\n  const frame = getRect({\n    top: newScroll.y,\n    bottom: newScroll.y + viewport.frame.height,\n    left: newScroll.x,\n    right: newScroll.x + viewport.frame.width\n  });\n  const updated = {\n    frame,\n    scroll: {\n      initial: viewport.scroll.initial,\n      max: viewport.scroll.max,\n      current: newScroll,\n      diff: {\n        value: diff,\n        displacement\n      }\n    }\n  };\n  return updated;\n});\n\nfunction getDraggables$1(ids, draggables) {\n  return ids.map(id => draggables[id]);\n}\nfunction tryGetVisible(id, groups) {\n  for (let i = 0; i < groups.length; i++) {\n    const displacement = groups[i].visible[id];\n    if (displacement) {\n      return displacement;\n    }\n  }\n  return null;\n}\nvar speculativelyIncrease = (({\n  impact,\n  viewport,\n  destination,\n  draggables,\n  maxScrollChange\n}) => {\n  const scrolledViewport = scrollViewport(viewport, add(viewport.scroll.current, maxScrollChange));\n  const scrolledDroppable = destination.frame ? scrollDroppable(destination, add(destination.frame.scroll.current, maxScrollChange)) : destination;\n  const last = impact.displaced;\n  const withViewportScroll = getDisplacementGroups({\n    afterDragging: getDraggables$1(last.all, draggables),\n    destination,\n    displacedBy: impact.displacedBy,\n    viewport: scrolledViewport.frame,\n    last,\n    forceShouldAnimate: false\n  });\n  const withDroppableScroll = getDisplacementGroups({\n    afterDragging: getDraggables$1(last.all, draggables),\n    destination: scrolledDroppable,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    last,\n    forceShouldAnimate: false\n  });\n  const invisible = {};\n  const visible = {};\n  const groups = [last, withViewportScroll, withDroppableScroll];\n  last.all.forEach(id => {\n    const displacement = tryGetVisible(id, groups);\n    if (displacement) {\n      visible[id] = displacement;\n      return;\n    }\n    invisible[id] = true;\n  });\n  const newImpact = {\n    ...impact,\n    displaced: {\n      all: last.all,\n      invisible,\n      visible\n    }\n  };\n  return newImpact;\n});\n\nvar withViewportDisplacement = ((viewport, point) => add(viewport.scroll.diff.displacement, point));\n\nvar getClientFromPageBorderBoxCenter = (({\n  pageBorderBoxCenter,\n  draggable,\n  viewport\n}) => {\n  const withoutPageScrollChange = withViewportDisplacement(viewport, pageBorderBoxCenter);\n  const offset = subtract(withoutPageScrollChange, draggable.page.borderBox.center);\n  return add(draggable.client.borderBox.center, offset);\n});\n\nvar isTotallyVisibleInNewLocation = (({\n  draggable,\n  destination,\n  newPageBorderBoxCenter,\n  viewport,\n  withDroppableDisplacement,\n  onlyOnMainAxis = false\n}) => {\n  const changeNeeded = subtract(newPageBorderBoxCenter, draggable.page.borderBox.center);\n  const shifted = offsetByPosition(draggable.page.borderBox, changeNeeded);\n  const args = {\n    target: shifted,\n    destination,\n    withDroppableDisplacement,\n    viewport\n  };\n  return onlyOnMainAxis ? isTotallyVisibleOnAxis(args) : isTotallyVisible(args);\n});\n\nvar moveToNextPlace = (({\n  isMovingForward,\n  draggable,\n  destination,\n  draggables,\n  previousImpact,\n  viewport,\n  previousPageBorderBoxCenter,\n  previousClientSelection,\n  afterCritical\n}) => {\n  if (!destination.isEnabled) {\n    return null;\n  }\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const isInHomeList = isHomeOf(draggable, destination);\n  const impact = moveToNextCombine({\n    isMovingForward,\n    draggable,\n    destination,\n    insideDestination,\n    previousImpact\n  }) || moveToNextIndex({\n    isMovingForward,\n    isInHomeList,\n    draggable,\n    draggables,\n    destination,\n    insideDestination,\n    previousImpact,\n    viewport,\n    afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    afterCritical\n  });\n  const isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n    draggable,\n    destination,\n    newPageBorderBoxCenter: pageBorderBoxCenter,\n    viewport: viewport.frame,\n    withDroppableDisplacement: false,\n    onlyOnMainAxis: true\n  });\n  if (isVisibleInNewLocation) {\n    const clientSelection = getClientFromPageBorderBoxCenter({\n      pageBorderBoxCenter,\n      draggable,\n      viewport\n    });\n    return {\n      clientSelection,\n      impact,\n      scrollJumpRequest: null\n    };\n  }\n  const distance = subtract(pageBorderBoxCenter, previousPageBorderBoxCenter);\n  const cautious = speculativelyIncrease({\n    impact,\n    viewport,\n    destination,\n    draggables,\n    maxScrollChange: distance\n  });\n  return {\n    clientSelection: previousClientSelection,\n    impact: cautious,\n    scrollJumpRequest: distance\n  };\n});\n\nconst getKnownActive = droppable => {\n  const rect = droppable.subject.active;\n  !rect ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get clipped area from droppable') : invariant(false) : void 0;\n  return rect;\n};\nvar getBestCrossAxisDroppable = (({\n  isMovingForward,\n  pageBorderBoxCenter,\n  source,\n  droppables,\n  viewport\n}) => {\n  const active = source.subject.active;\n  if (!active) {\n    return null;\n  }\n  const axis = source.axis;\n  const isBetweenSourceClipped = isWithin(active[axis.start], active[axis.end]);\n  const candidates = toDroppableList(droppables).filter(droppable => droppable !== source).filter(droppable => droppable.isEnabled).filter(droppable => Boolean(droppable.subject.active)).filter(droppable => isPartiallyVisibleThroughFrame(viewport.frame)(getKnownActive(droppable))).filter(droppable => {\n    const activeOfTarget = getKnownActive(droppable);\n    if (isMovingForward) {\n      return active[axis.crossAxisEnd] < activeOfTarget[axis.crossAxisEnd];\n    }\n    return activeOfTarget[axis.crossAxisStart] < active[axis.crossAxisStart];\n  }).filter(droppable => {\n    const activeOfTarget = getKnownActive(droppable);\n    const isBetweenDestinationClipped = isWithin(activeOfTarget[axis.start], activeOfTarget[axis.end]);\n    return isBetweenSourceClipped(activeOfTarget[axis.start]) || isBetweenSourceClipped(activeOfTarget[axis.end]) || isBetweenDestinationClipped(active[axis.start]) || isBetweenDestinationClipped(active[axis.end]);\n  }).sort((a, b) => {\n    const first = getKnownActive(a)[axis.crossAxisStart];\n    const second = getKnownActive(b)[axis.crossAxisStart];\n    if (isMovingForward) {\n      return first - second;\n    }\n    return second - first;\n  }).filter((droppable, index, array) => getKnownActive(droppable)[axis.crossAxisStart] === getKnownActive(array[0])[axis.crossAxisStart]);\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0];\n  }\n  const contains = candidates.filter(droppable => {\n    const isWithinDroppable = isWithin(getKnownActive(droppable)[axis.start], getKnownActive(droppable)[axis.end]);\n    return isWithinDroppable(pageBorderBoxCenter[axis.line]);\n  });\n  if (contains.length === 1) {\n    return contains[0];\n  }\n  if (contains.length > 1) {\n    return contains.sort((a, b) => getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start])[0];\n  }\n  return candidates.sort((a, b) => {\n    const first = closest$1(pageBorderBoxCenter, getCorners(getKnownActive(a)));\n    const second = closest$1(pageBorderBoxCenter, getCorners(getKnownActive(b)));\n    if (first !== second) {\n      return first - second;\n    }\n    return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n  })[0];\n});\n\nconst getCurrentPageBorderBoxCenter = (draggable, afterCritical) => {\n  const original = draggable.page.borderBox.center;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? subtract(original, afterCritical.displacedBy.point) : original;\n};\nconst getCurrentPageBorderBox = (draggable, afterCritical) => {\n  const original = draggable.page.borderBox;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? offsetByPosition(original, negate(afterCritical.displacedBy.point)) : original;\n};\n\nvar getClosestDraggable = (({\n  pageBorderBoxCenter,\n  viewport,\n  destination,\n  insideDestination,\n  afterCritical\n}) => {\n  const sorted = insideDestination.filter(draggable => isTotallyVisible({\n    target: getCurrentPageBorderBox(draggable, afterCritical),\n    destination,\n    viewport: viewport.frame,\n    withDroppableDisplacement: true\n  })).sort((a, b) => {\n    const distanceToA = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(a, afterCritical)));\n    const distanceToB = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(b, afterCritical)));\n    if (distanceToA < distanceToB) {\n      return -1;\n    }\n    if (distanceToB < distanceToA) {\n      return 1;\n    }\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return sorted[0] || null;\n});\n\nvar getDisplacedBy = memoizeOne(function getDisplacedBy(axis, displaceBy) {\n  const displacement = displaceBy[axis.line];\n  return {\n    value: displacement,\n    point: patch(axis.line, displacement)\n  };\n});\n\nconst getRequiredGrowthForPlaceholder = (droppable, placeholderSize, draggables) => {\n  const axis = droppable.axis;\n  if (droppable.descriptor.mode === 'virtual') {\n    return patch(axis.line, placeholderSize[axis.line]);\n  }\n  const availableSpace = droppable.subject.page.contentBox[axis.size];\n  const insideDroppable = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  const spaceUsed = insideDroppable.reduce((sum, dimension) => sum + dimension.client.marginBox[axis.size], 0);\n  const requiredSpace = spaceUsed + placeholderSize[axis.line];\n  const needsToGrowBy = requiredSpace - availableSpace;\n  if (needsToGrowBy <= 0) {\n    return null;\n  }\n  return patch(axis.line, needsToGrowBy);\n};\nconst withMaxScroll = (frame, max) => ({\n  ...frame,\n  scroll: {\n    ...frame.scroll,\n    max\n  }\n});\nconst addPlaceholder = (droppable, draggable, draggables) => {\n  const frame = droppable.frame;\n  !!isHomeOf(draggable, droppable) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should not add placeholder space to home list') : invariant(false) : void 0;\n  !!droppable.subject.withPlaceholder ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot add placeholder size to a subject when it already has one') : invariant(false) : void 0;\n  const placeholderSize = getDisplacedBy(droppable.axis, draggable.displaceBy).point;\n  const requiredGrowth = getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables);\n  const added = {\n    placeholderSize,\n    increasedBy: requiredGrowth,\n    oldFrameMaxScroll: droppable.frame ? droppable.frame.scroll.max : null\n  };\n  if (!frame) {\n    const subject = getSubject({\n      page: droppable.subject.page,\n      withPlaceholder: added,\n      axis: droppable.axis,\n      frame: droppable.frame\n    });\n    return {\n      ...droppable,\n      subject\n    };\n  }\n  const maxScroll = requiredGrowth ? add(frame.scroll.max, requiredGrowth) : frame.scroll.max;\n  const newFrame = withMaxScroll(frame, maxScroll);\n  const subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: added,\n    axis: droppable.axis,\n    frame: newFrame\n  });\n  return {\n    ...droppable,\n    subject,\n    frame: newFrame\n  };\n};\nconst removePlaceholder = droppable => {\n  const added = droppable.subject.withPlaceholder;\n  !added ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot remove placeholder form subject when there was none') : invariant(false) : void 0;\n  const frame = droppable.frame;\n  if (!frame) {\n    const subject = getSubject({\n      page: droppable.subject.page,\n      axis: droppable.axis,\n      frame: null,\n      withPlaceholder: null\n    });\n    return {\n      ...droppable,\n      subject\n    };\n  }\n  const oldMaxScroll = added.oldFrameMaxScroll;\n  !oldMaxScroll ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected droppable with frame to have old max frame scroll when removing placeholder') : invariant(false) : void 0;\n  const newFrame = withMaxScroll(frame, oldMaxScroll);\n  const subject = getSubject({\n    page: droppable.subject.page,\n    axis: droppable.axis,\n    frame: newFrame,\n    withPlaceholder: null\n  });\n  return {\n    ...droppable,\n    subject,\n    frame: newFrame\n  };\n};\n\nvar moveToNewDroppable = (({\n  previousPageBorderBoxCenter,\n  moveRelativeTo,\n  insideDestination,\n  draggable,\n  draggables,\n  destination,\n  viewport,\n  afterCritical\n}) => {\n  if (!moveRelativeTo) {\n    if (insideDestination.length) {\n      return null;\n    }\n    const proposed = {\n      displaced: emptyGroups,\n      displacedBy: noDisplacedBy,\n      at: {\n        type: 'REORDER',\n        destination: {\n          droppableId: destination.descriptor.id,\n          index: 0\n        }\n      }\n    };\n    const proposedPageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n      impact: proposed,\n      draggable,\n      droppable: destination,\n      draggables,\n      afterCritical\n    });\n    const withPlaceholder = isHomeOf(draggable, destination) ? destination : addPlaceholder(destination, draggable, draggables);\n    const isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n      draggable,\n      destination: withPlaceholder,\n      newPageBorderBoxCenter: proposedPageBorderBoxCenter,\n      viewport: viewport.frame,\n      withDroppableDisplacement: false,\n      onlyOnMainAxis: true\n    });\n    return isVisibleInNewLocation ? proposed : null;\n  }\n  const isGoingBeforeTarget = Boolean(previousPageBorderBoxCenter[destination.axis.line] <= moveRelativeTo.page.borderBox.center[destination.axis.line]);\n  const proposedIndex = (() => {\n    const relativeTo = moveRelativeTo.descriptor.index;\n    if (moveRelativeTo.descriptor.id === draggable.descriptor.id) {\n      return relativeTo;\n    }\n    if (isGoingBeforeTarget) {\n      return relativeTo;\n    }\n    return relativeTo + 1;\n  })();\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    displacedBy,\n    last: emptyGroups,\n    index: proposedIndex\n  });\n});\n\nvar moveCrossAxis = (({\n  isMovingForward,\n  previousPageBorderBoxCenter,\n  draggable,\n  isOver,\n  draggables,\n  droppables,\n  viewport,\n  afterCritical\n}) => {\n  const destination = getBestCrossAxisDroppable({\n    isMovingForward,\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    source: isOver,\n    droppables,\n    viewport\n  });\n  if (!destination) {\n    return null;\n  }\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const moveRelativeTo = getClosestDraggable({\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    viewport,\n    destination,\n    insideDestination,\n    afterCritical\n  });\n  const impact = moveToNewDroppable({\n    previousPageBorderBoxCenter,\n    destination,\n    draggable,\n    draggables,\n    moveRelativeTo,\n    insideDestination,\n    viewport,\n    afterCritical\n  });\n  if (!impact) {\n    return null;\n  }\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    afterCritical\n  });\n  const clientSelection = getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter,\n    draggable,\n    viewport\n  });\n  return {\n    clientSelection,\n    impact,\n    scrollJumpRequest: null\n  };\n});\n\nvar whatIsDraggedOver = (impact => {\n  const at = impact.at;\n  if (!at) {\n    return null;\n  }\n  if (at.type === 'REORDER') {\n    return at.destination.droppableId;\n  }\n  return at.combine.droppableId;\n});\n\nconst getDroppableOver$1 = (impact, droppables) => {\n  const id = whatIsDraggedOver(impact);\n  return id ? droppables[id] : null;\n};\nvar moveInDirection = (({\n  state,\n  type\n}) => {\n  const isActuallyOver = getDroppableOver$1(state.impact, state.dimensions.droppables);\n  const isMainAxisMovementAllowed = Boolean(isActuallyOver);\n  const home = state.dimensions.droppables[state.critical.droppable.id];\n  const isOver = isActuallyOver || home;\n  const direction = isOver.axis.direction;\n  const isMovingOnMainAxis = direction === 'vertical' && (type === 'MOVE_UP' || type === 'MOVE_DOWN') || direction === 'horizontal' && (type === 'MOVE_LEFT' || type === 'MOVE_RIGHT');\n  if (isMovingOnMainAxis && !isMainAxisMovementAllowed) {\n    return null;\n  }\n  const isMovingForward = type === 'MOVE_DOWN' || type === 'MOVE_RIGHT';\n  const draggable = state.dimensions.draggables[state.critical.draggable.id];\n  const previousPageBorderBoxCenter = state.current.page.borderBoxCenter;\n  const {\n    draggables,\n    droppables\n  } = state.dimensions;\n  return isMovingOnMainAxis ? moveToNextPlace({\n    isMovingForward,\n    previousPageBorderBoxCenter,\n    draggable,\n    destination: isOver,\n    draggables,\n    viewport: state.viewport,\n    previousClientSelection: state.current.client.selection,\n    previousImpact: state.impact,\n    afterCritical: state.afterCritical\n  }) : moveCrossAxis({\n    isMovingForward,\n    previousPageBorderBoxCenter,\n    draggable,\n    isOver,\n    draggables,\n    droppables,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n});\n\nfunction isMovementAllowed(state) {\n  return state.phase === 'DRAGGING' || state.phase === 'COLLECTING';\n}\n\nfunction isPositionInFrame(frame) {\n  const isWithinVertical = isWithin(frame.top, frame.bottom);\n  const isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function run(point) {\n    return isWithinVertical(point.y) && isWithinHorizontal(point.x);\n  };\n}\n\nfunction getHasOverlap(first, second) {\n  return first.left < second.right && first.right > second.left && first.top < second.bottom && first.bottom > second.top;\n}\nfunction getFurthestAway({\n  pageBorderBox,\n  draggable,\n  candidates\n}) {\n  const startCenter = draggable.page.borderBox.center;\n  const sorted = candidates.map(candidate => {\n    const axis = candidate.axis;\n    const target = patch(candidate.axis.line, pageBorderBox.center[axis.line], candidate.page.borderBox.center[axis.crossAxisLine]);\n    return {\n      id: candidate.descriptor.id,\n      distance: distance(startCenter, target)\n    };\n  }).sort((a, b) => b.distance - a.distance);\n  return sorted[0] ? sorted[0].id : null;\n}\nfunction getDroppableOver({\n  pageBorderBox,\n  draggable,\n  droppables\n}) {\n  const candidates = toDroppableList(droppables).filter(item => {\n    if (!item.isEnabled) {\n      return false;\n    }\n    const active = item.subject.active;\n    if (!active) {\n      return false;\n    }\n    if (!getHasOverlap(pageBorderBox, active)) {\n      return false;\n    }\n    if (isPositionInFrame(active)(pageBorderBox.center)) {\n      return true;\n    }\n    const axis = item.axis;\n    const childCenter = active.center[axis.crossAxisLine];\n    const crossAxisStart = pageBorderBox[axis.crossAxisStart];\n    const crossAxisEnd = pageBorderBox[axis.crossAxisEnd];\n    const isContained = isWithin(active[axis.crossAxisStart], active[axis.crossAxisEnd]);\n    const isStartContained = isContained(crossAxisStart);\n    const isEndContained = isContained(crossAxisEnd);\n    if (!isStartContained && !isEndContained) {\n      return true;\n    }\n    if (isStartContained) {\n      return crossAxisStart < childCenter;\n    }\n    return crossAxisEnd > childCenter;\n  });\n  if (!candidates.length) {\n    return null;\n  }\n  if (candidates.length === 1) {\n    return candidates[0].descriptor.id;\n  }\n  return getFurthestAway({\n    pageBorderBox,\n    draggable,\n    candidates\n  });\n}\n\nconst offsetRectByPosition = (rect, point) => getRect(offsetByPosition(rect, point));\n\nvar withDroppableScroll = ((droppable, area) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return area;\n  }\n  return offsetRectByPosition(area, frame.scroll.diff.value);\n});\n\nfunction getIsDisplaced({\n  displaced,\n  id\n}) {\n  return Boolean(displaced.visible[id] || displaced.invisible[id]);\n}\n\nfunction atIndex({\n  draggable,\n  closest,\n  inHomeList\n}) {\n  if (!closest) {\n    return null;\n  }\n  if (!inHomeList) {\n    return closest.descriptor.index;\n  }\n  if (closest.descriptor.index > draggable.descriptor.index) {\n    return closest.descriptor.index - 1;\n  }\n  return closest.descriptor.index;\n}\nvar getReorderImpact = (({\n  pageBorderBoxWithDroppableScroll: targetRect,\n  draggable,\n  destination,\n  insideDestination,\n  last,\n  viewport,\n  afterCritical\n}) => {\n  const axis = destination.axis;\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  const displacement = displacedBy.value;\n  const targetStart = targetRect[axis.start];\n  const targetEnd = targetRect[axis.end];\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const closest = withoutDragging.find(child => {\n    const id = child.descriptor.id;\n    const childCenter = child.page.borderBox.center[axis.line];\n    const didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    const isDisplaced = getIsDisplaced({\n      displaced: last,\n      id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd <= childCenter;\n      }\n      return targetStart < childCenter - displacement;\n    }\n    if (isDisplaced) {\n      return targetEnd <= childCenter + displacement;\n    }\n    return targetStart < childCenter;\n  }) || null;\n  const newIndex = atIndex({\n    draggable,\n    closest,\n    inHomeList: isHomeOf(draggable, destination)\n  });\n  return calculateReorderImpact({\n    draggable,\n    insideDestination,\n    destination,\n    viewport,\n    last,\n    displacedBy,\n    index: newIndex\n  });\n});\n\nconst combineThresholdDivisor = 4;\nvar getCombineImpact = (({\n  draggable,\n  pageBorderBoxWithDroppableScroll: targetRect,\n  previousImpact,\n  destination,\n  insideDestination,\n  afterCritical\n}) => {\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n  const axis = destination.axis;\n  const displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  const displacement = displacedBy.value;\n  const targetStart = targetRect[axis.start];\n  const targetEnd = targetRect[axis.end];\n  const withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  const combineWith = withoutDragging.find(child => {\n    const id = child.descriptor.id;\n    const childRect = child.page.borderBox;\n    const childSize = childRect[axis.size];\n    const threshold = childSize / combineThresholdDivisor;\n    const didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    const isDisplaced = getIsDisplaced({\n      displaced: previousImpact.displaced,\n      id\n    });\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd > childRect[axis.start] + threshold && targetEnd < childRect[axis.end] - threshold;\n      }\n      return targetStart > childRect[axis.start] - displacement + threshold && targetStart < childRect[axis.end] - displacement - threshold;\n    }\n    if (isDisplaced) {\n      return targetEnd > childRect[axis.start] + displacement + threshold && targetEnd < childRect[axis.end] + displacement - threshold;\n    }\n    return targetStart > childRect[axis.start] + threshold && targetStart < childRect[axis.end] - threshold;\n  });\n  if (!combineWith) {\n    return null;\n  }\n  const impact = {\n    displacedBy,\n    displaced: previousImpact.displaced,\n    at: {\n      type: 'COMBINE',\n      combine: {\n        draggableId: combineWith.descriptor.id,\n        droppableId: destination.descriptor.id\n      }\n    }\n  };\n  return impact;\n});\n\nvar getDragImpact = (({\n  pageOffset,\n  draggable,\n  draggables,\n  droppables,\n  previousImpact,\n  viewport,\n  afterCritical\n}) => {\n  const pageBorderBox = offsetRectByPosition(draggable.page.borderBox, pageOffset);\n  const destinationId = getDroppableOver({\n    pageBorderBox,\n    draggable,\n    droppables\n  });\n  if (!destinationId) {\n    return noImpact$1;\n  }\n  const destination = droppables[destinationId];\n  const insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  const pageBorderBoxWithDroppableScroll = withDroppableScroll(destination, pageBorderBox);\n  return getCombineImpact({\n    pageBorderBoxWithDroppableScroll,\n    draggable,\n    previousImpact,\n    destination,\n    insideDestination,\n    afterCritical\n  }) || getReorderImpact({\n    pageBorderBoxWithDroppableScroll,\n    draggable,\n    destination,\n    insideDestination,\n    last: previousImpact.displaced,\n    viewport,\n    afterCritical\n  });\n});\n\nvar patchDroppableMap = ((droppables, updated) => ({\n  ...droppables,\n  [updated.descriptor.id]: updated\n}));\n\nconst clearUnusedPlaceholder = ({\n  previousImpact,\n  impact,\n  droppables\n}) => {\n  const last = whatIsDraggedOver(previousImpact);\n  const now = whatIsDraggedOver(impact);\n  if (!last) {\n    return droppables;\n  }\n  if (last === now) {\n    return droppables;\n  }\n  const lastDroppable = droppables[last];\n  if (!lastDroppable.subject.withPlaceholder) {\n    return droppables;\n  }\n  const updated = removePlaceholder(lastDroppable);\n  return patchDroppableMap(droppables, updated);\n};\nvar recomputePlaceholders = (({\n  draggable,\n  draggables,\n  droppables,\n  previousImpact,\n  impact\n}) => {\n  const cleaned = clearUnusedPlaceholder({\n    previousImpact,\n    impact,\n    droppables\n  });\n  const isOver = whatIsDraggedOver(impact);\n  if (!isOver) {\n    return cleaned;\n  }\n  const droppable = droppables[isOver];\n  if (isHomeOf(draggable, droppable)) {\n    return cleaned;\n  }\n  if (droppable.subject.withPlaceholder) {\n    return cleaned;\n  }\n  const patched = addPlaceholder(droppable, draggable, draggables);\n  return patchDroppableMap(cleaned, patched);\n});\n\nvar update = (({\n  state,\n  clientSelection: forcedClientSelection,\n  dimensions: forcedDimensions,\n  viewport: forcedViewport,\n  impact: forcedImpact,\n  scrollJumpRequest\n}) => {\n  const viewport = forcedViewport || state.viewport;\n  const dimensions = forcedDimensions || state.dimensions;\n  const clientSelection = forcedClientSelection || state.current.client.selection;\n  const offset = subtract(clientSelection, state.initial.client.selection);\n  const client = {\n    offset,\n    selection: clientSelection,\n    borderBoxCenter: add(state.initial.client.borderBoxCenter, offset)\n  };\n  const page = {\n    selection: add(client.selection, viewport.scroll.current),\n    borderBoxCenter: add(client.borderBoxCenter, viewport.scroll.current),\n    offset: add(client.offset, viewport.scroll.diff.value)\n  };\n  const current = {\n    client,\n    page\n  };\n  if (state.phase === 'COLLECTING') {\n    return {\n      ...state,\n      dimensions,\n      viewport,\n      current\n    };\n  }\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const newImpact = forcedImpact || getDragImpact({\n    pageOffset: page.offset,\n    draggable,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: state.impact,\n    viewport,\n    afterCritical: state.afterCritical\n  });\n  const withUpdatedPlaceholders = recomputePlaceholders({\n    draggable,\n    impact: newImpact,\n    previousImpact: state.impact,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables\n  });\n  const result = {\n    ...state,\n    current,\n    dimensions: {\n      draggables: dimensions.draggables,\n      droppables: withUpdatedPlaceholders\n    },\n    impact: newImpact,\n    viewport,\n    scrollJumpRequest: scrollJumpRequest || null,\n    forceShouldAnimate: scrollJumpRequest ? false : null\n  };\n  return result;\n});\n\nfunction getDraggables(ids, draggables) {\n  return ids.map(id => draggables[id]);\n}\nvar recompute = (({\n  impact,\n  viewport,\n  draggables,\n  destination,\n  forceShouldAnimate\n}) => {\n  const last = impact.displaced;\n  const afterDragging = getDraggables(last.all, draggables);\n  const displaced = getDisplacementGroups({\n    afterDragging,\n    destination,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    forceShouldAnimate,\n    last\n  });\n  return {\n    ...impact,\n    displaced\n  };\n});\n\nvar getClientBorderBoxCenter = (({\n  impact,\n  draggable,\n  droppable,\n  draggables,\n  viewport,\n  afterCritical\n}) => {\n  const pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact,\n    draggable,\n    draggables,\n    droppable,\n    afterCritical\n  });\n  return getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter,\n    draggable,\n    viewport\n  });\n});\n\nvar refreshSnap = (({\n  state,\n  dimensions: forcedDimensions,\n  viewport: forcedViewport\n}) => {\n  !(state.movementMode === 'SNAP') ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  const needsVisibilityCheck = state.impact;\n  const viewport = forcedViewport || state.viewport;\n  const dimensions = forcedDimensions || state.dimensions;\n  const {\n    draggables,\n    droppables\n  } = dimensions;\n  const draggable = draggables[state.critical.draggable.id];\n  const isOver = whatIsDraggedOver(needsVisibilityCheck);\n  !isOver ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must be over a destination in SNAP movement mode') : invariant(false) : void 0;\n  const destination = droppables[isOver];\n  const impact = recompute({\n    impact: needsVisibilityCheck,\n    viewport,\n    destination,\n    draggables\n  });\n  const clientSelection = getClientBorderBoxCenter({\n    impact,\n    draggable,\n    droppable: destination,\n    draggables,\n    viewport,\n    afterCritical: state.afterCritical\n  });\n  return update({\n    impact,\n    clientSelection,\n    state,\n    dimensions,\n    viewport\n  });\n});\n\nvar getHomeLocation = (descriptor => ({\n  index: descriptor.index,\n  droppableId: descriptor.droppableId\n}));\n\nvar getLiftEffect = (({\n  draggable,\n  home,\n  draggables,\n  viewport\n}) => {\n  const displacedBy = getDisplacedBy(home.axis, draggable.displaceBy);\n  const insideHome = getDraggablesInsideDroppable(home.descriptor.id, draggables);\n  const rawIndex = insideHome.indexOf(draggable);\n  !(rawIndex !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected draggable to be inside home list') : invariant(false) : void 0;\n  const afterDragging = insideHome.slice(rawIndex + 1);\n  const effected = afterDragging.reduce((previous, item) => {\n    previous[item.descriptor.id] = true;\n    return previous;\n  }, {});\n  const afterCritical = {\n    inVirtualList: home.descriptor.mode === 'virtual',\n    displacedBy,\n    effected\n  };\n  const displaced = getDisplacementGroups({\n    afterDragging,\n    destination: home,\n    displacedBy,\n    last: null,\n    viewport: viewport.frame,\n    forceShouldAnimate: false\n  });\n  const impact = {\n    displaced,\n    displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: getHomeLocation(draggable.descriptor)\n    }\n  };\n  return {\n    impact,\n    afterCritical\n  };\n});\n\nvar patchDimensionMap = ((dimensions, updated) => ({\n  draggables: dimensions.draggables,\n  droppables: patchDroppableMap(dimensions.droppables, updated)\n}));\n\nconst start = key => {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\nconst finish = key => {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\n\nvar offsetDraggable = (({\n  draggable,\n  offset: offset$1,\n  initialWindowScroll\n}) => {\n  const client = offset(draggable.client, offset$1);\n  const page = withScroll(client, initialWindowScroll);\n  const moved = {\n    ...draggable,\n    placeholder: {\n      ...draggable.placeholder,\n      client\n    },\n    client,\n    page\n  };\n  return moved;\n});\n\nvar getFrame = (droppable => {\n  const frame = droppable.frame;\n  !frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected Droppable to have a frame') : invariant(false) : void 0;\n  return frame;\n});\n\nvar adjustAdditionsForScrollChanges = (({\n  additions,\n  updatedDroppables,\n  viewport\n}) => {\n  const windowScrollChange = viewport.scroll.diff.value;\n  return additions.map(draggable => {\n    const droppableId = draggable.descriptor.droppableId;\n    const modified = updatedDroppables[droppableId];\n    const frame = getFrame(modified);\n    const droppableScrollChange = frame.scroll.diff.value;\n    const totalChange = add(windowScrollChange, droppableScrollChange);\n    const moved = offsetDraggable({\n      draggable,\n      offset: totalChange,\n      initialWindowScroll: viewport.scroll.initial\n    });\n    return moved;\n  });\n});\n\nconst timingsKey = 'Processing dynamic changes';\nvar publishWhileDraggingInVirtual = (({\n  state,\n  published\n}) => {\n  start();\n  const withScrollChange = published.modified.map(update => {\n    const existing = state.dimensions.droppables[update.droppableId];\n    const scrolled = scrollDroppable(existing, update.scroll);\n    return scrolled;\n  });\n  const droppables = {\n    ...state.dimensions.droppables,\n    ...toDroppableMap(withScrollChange)\n  };\n  const updatedAdditions = toDraggableMap(adjustAdditionsForScrollChanges({\n    additions: published.additions,\n    updatedDroppables: droppables,\n    viewport: state.viewport\n  }));\n  const draggables = {\n    ...state.dimensions.draggables,\n    ...updatedAdditions\n  };\n  published.removals.forEach(id => {\n    delete draggables[id];\n  });\n  const dimensions = {\n    droppables,\n    draggables\n  };\n  const wasOverId = whatIsDraggedOver(state.impact);\n  const wasOver = wasOverId ? dimensions.droppables[wasOverId] : null;\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const home = dimensions.droppables[state.critical.droppable.id];\n  const {\n    impact: onLiftImpact,\n    afterCritical\n  } = getLiftEffect({\n    draggable,\n    home,\n    draggables,\n    viewport: state.viewport\n  });\n  const previousImpact = wasOver && wasOver.isCombineEnabled ? state.impact : onLiftImpact;\n  const impact = getDragImpact({\n    pageOffset: state.current.page.offset,\n    draggable: dimensions.draggables[state.critical.draggable.id],\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact,\n    viewport: state.viewport,\n    afterCritical\n  });\n  finish(timingsKey);\n  const draggingState = {\n    ...state,\n    phase: 'DRAGGING',\n    impact,\n    onLiftImpact,\n    dimensions,\n    afterCritical,\n    forceShouldAnimate: false\n  };\n  if (state.phase === 'COLLECTING') {\n    return draggingState;\n  }\n  const dropPending = {\n    ...draggingState,\n    phase: 'DROP_PENDING',\n    reason: state.reason,\n    isWaiting: false\n  };\n  return dropPending;\n});\n\nconst isSnapping = state => state.movementMode === 'SNAP';\nconst postDroppableChange = (state, updated, isEnabledChanging) => {\n  const dimensions = patchDimensionMap(state.dimensions, updated);\n  if (!isSnapping(state) || isEnabledChanging) {\n    return update({\n      state,\n      dimensions\n    });\n  }\n  return refreshSnap({\n    state,\n    dimensions\n  });\n};\nfunction removeScrollJumpRequest(state) {\n  if (state.isDragging && state.movementMode === 'SNAP') {\n    return {\n      ...state,\n      scrollJumpRequest: null\n    };\n  }\n  return state;\n}\nconst idle$2 = {\n  phase: 'IDLE',\n  completed: null,\n  shouldFlush: false\n};\nvar reducer = ((state = idle$2, action) => {\n  if (action.type === 'FLUSH') {\n    return {\n      ...idle$2,\n      shouldFlush: true\n    };\n  }\n  if (action.type === 'INITIAL_PUBLISH') {\n    !(state.phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'INITIAL_PUBLISH must come after a IDLE phase') : invariant(false) : void 0;\n    const {\n      critical,\n      clientSelection,\n      viewport,\n      dimensions,\n      movementMode\n    } = action.payload;\n    const draggable = dimensions.draggables[critical.draggable.id];\n    const home = dimensions.droppables[critical.droppable.id];\n    const client = {\n      selection: clientSelection,\n      borderBoxCenter: draggable.client.borderBox.center,\n      offset: origin\n    };\n    const initial = {\n      client,\n      page: {\n        selection: add(client.selection, viewport.scroll.initial),\n        borderBoxCenter: add(client.selection, viewport.scroll.initial),\n        offset: add(client.selection, viewport.scroll.diff.value)\n      }\n    };\n    const isWindowScrollAllowed = toDroppableList(dimensions.droppables).every(item => !item.isFixedOnPage);\n    const {\n      impact,\n      afterCritical\n    } = getLiftEffect({\n      draggable,\n      home,\n      draggables: dimensions.draggables,\n      viewport\n    });\n    const result = {\n      phase: 'DRAGGING',\n      isDragging: true,\n      critical,\n      movementMode,\n      dimensions,\n      initial,\n      current: initial,\n      isWindowScrollAllowed,\n      impact,\n      afterCritical,\n      onLiftImpact: impact,\n      viewport,\n      scrollJumpRequest: null,\n      forceShouldAnimate: null\n    };\n    return result;\n  }\n  if (action.type === 'COLLECTION_STARTING') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Collection cannot start from phase ${state.phase}`) : invariant(false) : void 0;\n    const result = {\n      ...state,\n      phase: 'COLLECTING'\n    };\n    return result;\n  }\n  if (action.type === 'PUBLISH_WHILE_DRAGGING') {\n    !(state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Unexpected ${action.type} received in phase ${state.phase}`) : invariant(false) : void 0;\n    return publishWhileDraggingInVirtual({\n      state,\n      published: action.payload\n    });\n  }\n  if (action.type === 'MOVE') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} not permitted in phase ${state.phase}`) : invariant(false) : void 0;\n    const {\n      client: clientSelection\n    } = action.payload;\n    if (isEqual$1(clientSelection, state.current.client.selection)) {\n      return state;\n    }\n    return update({\n      state,\n      clientSelection,\n      impact: isSnapping(state) ? state.impact : null\n    });\n  }\n  if (action.type === 'UPDATE_DROPPABLE_SCROLL') {\n    if (state.phase === 'DROP_PENDING') {\n      return removeScrollJumpRequest(state);\n    }\n    if (state.phase === 'COLLECTING') {\n      return removeScrollJumpRequest(state);\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} not permitted in phase ${state.phase}`) : invariant(false) : void 0;\n    const {\n      id,\n      newScroll\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    if (!target) {\n      return state;\n    }\n    const scrolled = scrollDroppable(target, newScroll);\n    return postDroppableChange(state, scrolled, false);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Attempting to move in an unsupported phase ${state.phase}`) : invariant(false) : void 0;\n    const {\n      id,\n      isEnabled\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    !target ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find Droppable[id: ${id}] to toggle its enabled state`) : invariant(false) : void 0;\n    !(target.isEnabled !== isEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Trying to set droppable isEnabled to ${String(isEnabled)}\n      but it is already ${String(target.isEnabled)}`) : invariant(false) : void 0;\n    const updated = {\n      ...target,\n      isEnabled\n    };\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Attempting to move in an unsupported phase ${state.phase}`) : invariant(false) : void 0;\n    const {\n      id,\n      isCombineEnabled\n    } = action.payload;\n    const target = state.dimensions.droppables[id];\n    !target ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find Droppable[id: ${id}] to toggle its isCombineEnabled state`) : invariant(false) : void 0;\n    !(target.isCombineEnabled !== isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Trying to set droppable isCombineEnabled to ${String(isCombineEnabled)}\n      but it is already ${String(target.isCombineEnabled)}`) : invariant(false) : void 0;\n    const updated = {\n      ...target,\n      isCombineEnabled\n    };\n    return postDroppableChange(state, updated, true);\n  }\n  if (action.type === 'MOVE_BY_WINDOW_SCROLL') {\n    if (state.phase === 'DROP_PENDING' || state.phase === 'DROP_ANIMATING') {\n      return state;\n    }\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot move by window in phase ${state.phase}`) : invariant(false) : void 0;\n    !state.isWindowScrollAllowed ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Window scrolling is currently not supported for fixed lists') : invariant(false) : void 0;\n    const newScroll = action.payload.newScroll;\n    if (isEqual$1(state.viewport.scroll.current, newScroll)) {\n      return removeScrollJumpRequest(state);\n    }\n    const viewport = scrollViewport(state.viewport, newScroll);\n    if (isSnapping(state)) {\n      return refreshSnap({\n        state,\n        viewport\n      });\n    }\n    return update({\n      state,\n      viewport\n    });\n  }\n  if (action.type === 'UPDATE_VIEWPORT_MAX_SCROLL') {\n    if (!isMovementAllowed(state)) {\n      return state;\n    }\n    const maxScroll = action.payload.maxScroll;\n    if (isEqual$1(maxScroll, state.viewport.scroll.max)) {\n      return state;\n    }\n    const withMaxScroll = {\n      ...state.viewport,\n      scroll: {\n        ...state.viewport.scroll,\n        max: maxScroll\n      }\n    };\n    return {\n      ...state,\n      viewport: withMaxScroll\n    };\n  }\n  if (action.type === 'MOVE_UP' || action.type === 'MOVE_DOWN' || action.type === 'MOVE_LEFT' || action.type === 'MOVE_RIGHT') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${action.type} received while not in DRAGGING phase`) : invariant(false) : void 0;\n    const result = moveInDirection({\n      state,\n      type: action.type\n    });\n    if (!result) {\n      return state;\n    }\n    return update({\n      state,\n      impact: result.impact,\n      clientSelection: result.clientSelection,\n      scrollJumpRequest: result.scrollJumpRequest\n    });\n  }\n  if (action.type === 'DROP_PENDING') {\n    const reason = action.payload.reason;\n    !(state.phase === 'COLLECTING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only move into the DROP_PENDING phase from the COLLECTING phase') : invariant(false) : void 0;\n    const newState = {\n      ...state,\n      phase: 'DROP_PENDING',\n      isWaiting: true,\n      reason\n    };\n    return newState;\n  }\n  if (action.type === 'DROP_ANIMATE') {\n    const {\n      completed,\n      dropDuration,\n      newHomeClientOffset\n    } = action.payload;\n    !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot animate drop from phase ${state.phase}`) : invariant(false) : void 0;\n    const result = {\n      phase: 'DROP_ANIMATING',\n      completed,\n      dropDuration,\n      newHomeClientOffset,\n      dimensions: state.dimensions\n    };\n    return result;\n  }\n  if (action.type === 'DROP_COMPLETE') {\n    const {\n      completed\n    } = action.payload;\n    return {\n      phase: 'IDLE',\n      completed,\n      shouldFlush: false\n    };\n  }\n  return state;\n});\n\nconst beforeInitialCapture = args => ({\n  type: 'BEFORE_INITIAL_CAPTURE',\n  payload: args\n});\nconst lift$1 = args => ({\n  type: 'LIFT',\n  payload: args\n});\nconst initialPublish = args => ({\n  type: 'INITIAL_PUBLISH',\n  payload: args\n});\nconst publishWhileDragging = args => ({\n  type: 'PUBLISH_WHILE_DRAGGING',\n  payload: args\n});\nconst collectionStarting = () => ({\n  type: 'COLLECTION_STARTING',\n  payload: null\n});\nconst updateDroppableScroll = args => ({\n  type: 'UPDATE_DROPPABLE_SCROLL',\n  payload: args\n});\nconst updateDroppableIsEnabled = args => ({\n  type: 'UPDATE_DROPPABLE_IS_ENABLED',\n  payload: args\n});\nconst updateDroppableIsCombineEnabled = args => ({\n  type: 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED',\n  payload: args\n});\nconst move = args => ({\n  type: 'MOVE',\n  payload: args\n});\nconst moveByWindowScroll = args => ({\n  type: 'MOVE_BY_WINDOW_SCROLL',\n  payload: args\n});\nconst updateViewportMaxScroll = args => ({\n  type: 'UPDATE_VIEWPORT_MAX_SCROLL',\n  payload: args\n});\nconst moveUp = () => ({\n  type: 'MOVE_UP',\n  payload: null\n});\nconst moveDown = () => ({\n  type: 'MOVE_DOWN',\n  payload: null\n});\nconst moveRight = () => ({\n  type: 'MOVE_RIGHT',\n  payload: null\n});\nconst moveLeft = () => ({\n  type: 'MOVE_LEFT',\n  payload: null\n});\nconst flush = () => ({\n  type: 'FLUSH',\n  payload: null\n});\nconst animateDrop = args => ({\n  type: 'DROP_ANIMATE',\n  payload: args\n});\nconst completeDrop = args => ({\n  type: 'DROP_COMPLETE',\n  payload: args\n});\nconst drop$1 = args => ({\n  type: 'DROP',\n  payload: args\n});\nconst dropPending = args => ({\n  type: 'DROP_PENDING',\n  payload: args\n});\nconst dropAnimationFinished = () => ({\n  type: 'DROP_ANIMATION_FINISHED',\n  payload: null\n});\n\nfunction checkIndexes(insideDestination) {\n  if (insideDestination.length <= 1) {\n    return;\n  }\n  const indexes = insideDestination.map(d => d.descriptor.index);\n  const errors = {};\n  for (let i = 1; i < indexes.length; i++) {\n    const current = indexes[i];\n    const previous = indexes[i - 1];\n    if (current !== previous + 1) {\n      errors[current] = true;\n    }\n  }\n  if (!Object.keys(errors).length) {\n    return;\n  }\n  const formatted = indexes.map(index => {\n    const hasError = Boolean(errors[index]);\n    return hasError ? `[🔥${index}]` : `${index}`;\n  }).join(', ');\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    Detected non-consecutive <Draggable /> indexes.\n\n    (This can cause unexpected bugs)\n\n    ${formatted}\n  `) : void 0;\n}\nfunction validateDimensions(critical, dimensions) {\n  if (process.env.NODE_ENV !== 'production') {\n    const insideDestination = getDraggablesInsideDroppable(critical.droppable.id, dimensions.draggables);\n    checkIndexes(insideDestination);\n  }\n}\n\nvar lift = (marshal => ({\n  getState,\n  dispatch\n}) => next => action => {\n  if (action.type !== 'LIFT') {\n    next(action);\n    return;\n  }\n  const {\n    id,\n    clientSelection,\n    movementMode\n  } = action.payload;\n  const initial = getState();\n  if (initial.phase === 'DROP_ANIMATING') {\n    dispatch(completeDrop({\n      completed: initial.completed\n    }));\n  }\n  !(getState().phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase to start a drag') : invariant(false) : void 0;\n  dispatch(flush());\n  dispatch(beforeInitialCapture({\n    draggableId: id,\n    movementMode\n  }));\n  const scrollOptions = {\n    shouldPublishImmediately: movementMode === 'SNAP'\n  };\n  const request = {\n    draggableId: id,\n    scrollOptions\n  };\n  const {\n    critical,\n    dimensions,\n    viewport\n  } = marshal.startPublishing(request);\n  validateDimensions(critical, dimensions);\n  dispatch(initialPublish({\n    critical,\n    dimensions,\n    clientSelection,\n    movementMode,\n    viewport\n  }));\n});\n\nvar style = (marshal => () => next => action => {\n  if (action.type === 'INITIAL_PUBLISH') {\n    marshal.dragging();\n  }\n  if (action.type === 'DROP_ANIMATE') {\n    marshal.dropping(action.payload.completed.result.reason);\n  }\n  if (action.type === 'FLUSH' || action.type === 'DROP_COMPLETE') {\n    marshal.resting();\n  }\n  next(action);\n});\n\nconst curves = {\n  outOfTheWay: 'cubic-bezier(0.2, 0, 0, 1)',\n  drop: 'cubic-bezier(.2,1,.1,1)'\n};\nconst combine = {\n  opacity: {\n    drop: 0,\n    combining: 0.7\n  },\n  scale: {\n    drop: 0.75\n  }\n};\nconst timings = {\n  outOfTheWay: 0.2,\n  minDropTime: 0.33,\n  maxDropTime: 0.55\n};\nconst outOfTheWayTiming = `${timings.outOfTheWay}s ${curves.outOfTheWay}`;\nconst transitions = {\n  fluid: `opacity ${outOfTheWayTiming}`,\n  snap: `transform ${outOfTheWayTiming}, opacity ${outOfTheWayTiming}`,\n  drop: duration => {\n    const timing = `${duration}s ${curves.drop}`;\n    return `transform ${timing}, opacity ${timing}`;\n  },\n  outOfTheWay: `transform ${outOfTheWayTiming}`,\n  placeholder: `height ${outOfTheWayTiming}, width ${outOfTheWayTiming}, margin ${outOfTheWayTiming}`\n};\nconst moveTo = offset => isEqual$1(offset, origin) ? undefined : `translate(${offset.x}px, ${offset.y}px)`;\nconst transforms = {\n  moveTo,\n  drop: (offset, isCombining) => {\n    const translate = moveTo(offset);\n    if (!translate) {\n      return undefined;\n    }\n    if (!isCombining) {\n      return translate;\n    }\n    return `${translate} scale(${combine.scale.drop})`;\n  }\n};\n\nconst {\n  minDropTime,\n  maxDropTime\n} = timings;\nconst dropTimeRange = maxDropTime - minDropTime;\nconst maxDropTimeAtDistance = 1500;\nconst cancelDropModifier = 0.6;\nvar getDropDuration = (({\n  current,\n  destination,\n  reason\n}) => {\n  const distance$1 = distance(current, destination);\n  if (distance$1 <= 0) {\n    return minDropTime;\n  }\n  if (distance$1 >= maxDropTimeAtDistance) {\n    return maxDropTime;\n  }\n  const percentage = distance$1 / maxDropTimeAtDistance;\n  const duration = minDropTime + dropTimeRange * percentage;\n  const withDuration = reason === 'CANCEL' ? duration * cancelDropModifier : duration;\n  return Number(withDuration.toFixed(2));\n});\n\nvar getNewHomeClientOffset = (({\n  impact,\n  draggable,\n  dimensions,\n  viewport,\n  afterCritical\n}) => {\n  const {\n    draggables,\n    droppables\n  } = dimensions;\n  const droppableId = whatIsDraggedOver(impact);\n  const destination = droppableId ? droppables[droppableId] : null;\n  const home = droppables[draggable.descriptor.droppableId];\n  const newClientCenter = getClientBorderBoxCenter({\n    impact,\n    draggable,\n    draggables,\n    afterCritical,\n    droppable: destination || home,\n    viewport\n  });\n  const offset = subtract(newClientCenter, draggable.client.borderBox.center);\n  return offset;\n});\n\nvar getDropImpact = (({\n  draggables,\n  reason,\n  lastImpact,\n  home,\n  viewport,\n  onLiftImpact\n}) => {\n  if (!lastImpact.at || reason !== 'DROP') {\n    const recomputedHomeImpact = recompute({\n      draggables,\n      impact: onLiftImpact,\n      destination: home,\n      viewport,\n      forceShouldAnimate: true\n    });\n    return {\n      impact: recomputedHomeImpact,\n      didDropInsideDroppable: false\n    };\n  }\n  if (lastImpact.at.type === 'REORDER') {\n    return {\n      impact: lastImpact,\n      didDropInsideDroppable: true\n    };\n  }\n  const withoutMovement = {\n    ...lastImpact,\n    displaced: emptyGroups\n  };\n  return {\n    impact: withoutMovement,\n    didDropInsideDroppable: true\n  };\n});\n\nconst dropMiddleware = ({\n  getState,\n  dispatch\n}) => next => action => {\n  if (action.type !== 'DROP') {\n    next(action);\n    return;\n  }\n  const state = getState();\n  const reason = action.payload.reason;\n  if (state.phase === 'COLLECTING') {\n    dispatch(dropPending({\n      reason\n    }));\n    return;\n  }\n  if (state.phase === 'IDLE') {\n    return;\n  }\n  const isWaitingForDrop = state.phase === 'DROP_PENDING' && state.isWaiting;\n  !!isWaitingForDrop ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A DROP action occurred while DROP_PENDING and still waiting') : invariant(false) : void 0;\n  !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot drop in phase: ${state.phase}`) : invariant(false) : void 0;\n  const critical = state.critical;\n  const dimensions = state.dimensions;\n  const draggable = dimensions.draggables[state.critical.draggable.id];\n  const {\n    impact,\n    didDropInsideDroppable\n  } = getDropImpact({\n    reason,\n    lastImpact: state.impact,\n    afterCritical: state.afterCritical,\n    onLiftImpact: state.onLiftImpact,\n    home: state.dimensions.droppables[state.critical.droppable.id],\n    viewport: state.viewport,\n    draggables: state.dimensions.draggables\n  });\n  const destination = didDropInsideDroppable ? tryGetDestination(impact) : null;\n  const combine = didDropInsideDroppable ? tryGetCombine(impact) : null;\n  const source = {\n    index: critical.draggable.index,\n    droppableId: critical.droppable.id\n  };\n  const result = {\n    draggableId: draggable.descriptor.id,\n    type: draggable.descriptor.type,\n    source,\n    reason,\n    mode: state.movementMode,\n    destination,\n    combine\n  };\n  const newHomeClientOffset = getNewHomeClientOffset({\n    impact,\n    draggable,\n    dimensions,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n  const completed = {\n    critical: state.critical,\n    afterCritical: state.afterCritical,\n    result,\n    impact\n  };\n  const isAnimationRequired = !isEqual$1(state.current.client.offset, newHomeClientOffset) || Boolean(result.combine);\n  if (!isAnimationRequired) {\n    dispatch(completeDrop({\n      completed\n    }));\n    return;\n  }\n  const dropDuration = getDropDuration({\n    current: state.current.client.offset,\n    destination: newHomeClientOffset,\n    reason\n  });\n  const args = {\n    newHomeClientOffset,\n    dropDuration,\n    completed\n  };\n  dispatch(animateDrop(args));\n};\nvar drop = dropMiddleware;\n\nvar getWindowScroll = (() => ({\n  x: window.pageXOffset,\n  y: window.pageYOffset\n}));\n\nfunction getWindowScrollBinding(update) {\n  return {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: event => {\n      if (event.target !== window && event.target !== window.document) {\n        return;\n      }\n      update();\n    }\n  };\n}\nfunction getScrollListener({\n  onWindowScroll\n}) {\n  function updateScroll() {\n    onWindowScroll(getWindowScroll());\n  }\n  const scheduled = rafSchd(updateScroll);\n  const binding = getWindowScrollBinding(scheduled);\n  let unbind = noop$2;\n  function isActive() {\n    return unbind !== noop$2;\n  }\n  function start() {\n    !!isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start scroll listener when already active') : invariant(false) : void 0;\n    unbind = bindEvents(window, [binding]);\n  }\n  function stop() {\n    !isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop scroll listener when not active') : invariant(false) : void 0;\n    scheduled.cancel();\n    unbind();\n    unbind = noop$2;\n  }\n  return {\n    start,\n    stop,\n    isActive\n  };\n}\n\nconst shouldEnd = action => action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATE' || action.type === 'FLUSH';\nconst scrollListener = store => {\n  const listener = getScrollListener({\n    onWindowScroll: newScroll => {\n      store.dispatch(moveByWindowScroll({\n        newScroll\n      }));\n    }\n  });\n  return next => action => {\n    if (!listener.isActive() && action.type === 'INITIAL_PUBLISH') {\n      listener.start();\n    }\n    if (listener.isActive() && shouldEnd(action)) {\n      listener.stop();\n    }\n    next(action);\n  };\n};\nvar scrollListener$1 = scrollListener;\n\nvar getExpiringAnnounce = (announce => {\n  let wasCalled = false;\n  let isExpired = false;\n  const timeoutId = setTimeout(() => {\n    isExpired = true;\n  });\n  const result = message => {\n    if (wasCalled) {\n      process.env.NODE_ENV !== \"production\" ? warning('Announcement already made. Not making a second announcement') : void 0;\n      return;\n    }\n    if (isExpired) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Announcements cannot be made asynchronously.\n        Default message has already been announced.\n      `) : void 0;\n      return;\n    }\n    wasCalled = true;\n    announce(message);\n    clearTimeout(timeoutId);\n  };\n  result.wasCalled = () => wasCalled;\n  return result;\n});\n\nvar getAsyncMarshal = (() => {\n  const entries = [];\n  const execute = timerId => {\n    const index = entries.findIndex(item => item.timerId === timerId);\n    !(index !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find timer') : invariant(false) : void 0;\n    const [entry] = entries.splice(index, 1);\n    entry.callback();\n  };\n  const add = fn => {\n    const timerId = setTimeout(() => execute(timerId));\n    const entry = {\n      timerId,\n      callback: fn\n    };\n    entries.push(entry);\n  };\n  const flush = () => {\n    if (!entries.length) {\n      return;\n    }\n    const shallow = [...entries];\n    entries.length = 0;\n    shallow.forEach(entry => {\n      clearTimeout(entry.timerId);\n      entry.callback();\n    });\n  };\n  return {\n    add,\n    flush\n  };\n});\n\nconst areLocationsEqual = (first, second) => {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.droppableId === second.droppableId && first.index === second.index;\n};\nconst isCombineEqual = (first, second) => {\n  if (first == null && second == null) {\n    return true;\n  }\n  if (first == null || second == null) {\n    return false;\n  }\n  return first.draggableId === second.draggableId && first.droppableId === second.droppableId;\n};\nconst isCriticalEqual = (first, second) => {\n  if (first === second) {\n    return true;\n  }\n  const isDraggableEqual = first.draggable.id === second.draggable.id && first.draggable.droppableId === second.draggable.droppableId && first.draggable.type === second.draggable.type && first.draggable.index === second.draggable.index;\n  const isDroppableEqual = first.droppable.id === second.droppable.id && first.droppable.type === second.droppable.type;\n  return isDraggableEqual && isDroppableEqual;\n};\n\nconst withTimings = (key, fn) => {\n  start();\n  fn();\n  finish();\n};\nconst getDragStart = (critical, mode) => ({\n  draggableId: critical.draggable.id,\n  type: critical.droppable.type,\n  source: {\n    droppableId: critical.droppable.id,\n    index: critical.draggable.index\n  },\n  mode\n});\nfunction execute(responder, data, announce, getDefaultMessage) {\n  if (!responder) {\n    announce(getDefaultMessage(data));\n    return;\n  }\n  const willExpire = getExpiringAnnounce(announce);\n  const provided = {\n    announce: willExpire\n  };\n  responder(data, provided);\n  if (!willExpire.wasCalled()) {\n    announce(getDefaultMessage(data));\n  }\n}\nvar getPublisher = ((getResponders, announce) => {\n  const asyncMarshal = getAsyncMarshal();\n  let dragging = null;\n  const beforeCapture = (draggableId, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeCapture as a drag start has already been published') : invariant(false) : void 0;\n    withTimings('onBeforeCapture', () => {\n      const fn = getResponders().onBeforeCapture;\n      if (fn) {\n        const before = {\n          draggableId,\n          mode\n        };\n        fn(before);\n      }\n    });\n  };\n  const beforeStart = (critical, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant(false) : void 0;\n    withTimings('onBeforeDragStart', () => {\n      const fn = getResponders().onBeforeDragStart;\n      if (fn) {\n        fn(getDragStart(critical, mode));\n      }\n    });\n  };\n  const start = (critical, mode) => {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant(false) : void 0;\n    const data = getDragStart(critical, mode);\n    dragging = {\n      mode,\n      lastCritical: critical,\n      lastLocation: data.source,\n      lastCombine: null\n    };\n    asyncMarshal.add(() => {\n      withTimings('onDragStart', () => execute(getResponders().onDragStart, data, announce, preset$1.onDragStart));\n    });\n  };\n  const update = (critical, impact) => {\n    const location = tryGetDestination(impact);\n    const combine = tryGetCombine(impact);\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragMove when onDragStart has not been called') : invariant(false) : void 0;\n    const hasCriticalChanged = !isCriticalEqual(critical, dragging.lastCritical);\n    if (hasCriticalChanged) {\n      dragging.lastCritical = critical;\n    }\n    const hasLocationChanged = !areLocationsEqual(dragging.lastLocation, location);\n    if (hasLocationChanged) {\n      dragging.lastLocation = location;\n    }\n    const hasGroupingChanged = !isCombineEqual(dragging.lastCombine, combine);\n    if (hasGroupingChanged) {\n      dragging.lastCombine = combine;\n    }\n    if (!hasCriticalChanged && !hasLocationChanged && !hasGroupingChanged) {\n      return;\n    }\n    const data = {\n      ...getDragStart(critical, dragging.mode),\n      combine,\n      destination: location\n    };\n    asyncMarshal.add(() => {\n      withTimings('onDragUpdate', () => execute(getResponders().onDragUpdate, data, announce, preset$1.onDragUpdate));\n    });\n  };\n  const flush = () => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only flush responders while dragging') : invariant(false) : void 0;\n    asyncMarshal.flush();\n  };\n  const drop = result => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragEnd when there is no matching onDragStart') : invariant(false) : void 0;\n    dragging = null;\n    withTimings('onDragEnd', () => execute(getResponders().onDragEnd, result, announce, preset$1.onDragEnd));\n  };\n  const abort = () => {\n    if (!dragging) {\n      return;\n    }\n    const result = {\n      ...getDragStart(dragging.lastCritical, dragging.mode),\n      combine: null,\n      destination: null,\n      reason: 'CANCEL'\n    };\n    drop(result);\n  };\n  return {\n    beforeCapture,\n    beforeStart,\n    start,\n    update,\n    flush,\n    drop,\n    abort\n  };\n});\n\nvar responders = ((getResponders, announce) => {\n  const publisher = getPublisher(getResponders, announce);\n  return store => next => action => {\n    if (action.type === 'BEFORE_INITIAL_CAPTURE') {\n      publisher.beforeCapture(action.payload.draggableId, action.payload.movementMode);\n      return;\n    }\n    if (action.type === 'INITIAL_PUBLISH') {\n      const critical = action.payload.critical;\n      publisher.beforeStart(critical, action.payload.movementMode);\n      next(action);\n      publisher.start(critical, action.payload.movementMode);\n      return;\n    }\n    if (action.type === 'DROP_COMPLETE') {\n      const result = action.payload.completed.result;\n      publisher.flush();\n      next(action);\n      publisher.drop(result);\n      return;\n    }\n    next(action);\n    if (action.type === 'FLUSH') {\n      publisher.abort();\n      return;\n    }\n    const state = store.getState();\n    if (state.phase === 'DRAGGING') {\n      publisher.update(state.critical, state.impact);\n    }\n  };\n});\n\nconst dropAnimationFinishMiddleware = store => next => action => {\n  if (action.type !== 'DROP_ANIMATION_FINISHED') {\n    next(action);\n    return;\n  }\n  const state = store.getState();\n  !(state.phase === 'DROP_ANIMATING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot finish a drop animating when no drop is occurring') : invariant(false) : void 0;\n  store.dispatch(completeDrop({\n    completed: state.completed\n  }));\n};\nvar dropAnimationFinish = dropAnimationFinishMiddleware;\n\nconst dropAnimationFlushOnScrollMiddleware = store => {\n  let unbind = null;\n  let frameId = null;\n  function clear() {\n    if (frameId) {\n      cancelAnimationFrame(frameId);\n      frameId = null;\n    }\n    if (unbind) {\n      unbind();\n      unbind = null;\n    }\n  }\n  return next => action => {\n    if (action.type === 'FLUSH' || action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATION_FINISHED') {\n      clear();\n    }\n    next(action);\n    if (action.type !== 'DROP_ANIMATE') {\n      return;\n    }\n    const binding = {\n      eventName: 'scroll',\n      options: {\n        capture: true,\n        passive: false,\n        once: true\n      },\n      fn: function flushDropAnimation() {\n        const state = store.getState();\n        if (state.phase === 'DROP_ANIMATING') {\n          store.dispatch(dropAnimationFinished());\n        }\n      }\n    };\n    frameId = requestAnimationFrame(() => {\n      frameId = null;\n      unbind = bindEvents(window, [binding]);\n    });\n  };\n};\nvar dropAnimationFlushOnScroll = dropAnimationFlushOnScrollMiddleware;\n\nvar dimensionMarshalStopper = (marshal => () => next => action => {\n  if (action.type === 'DROP_COMPLETE' || action.type === 'FLUSH' || action.type === 'DROP_ANIMATE') {\n    marshal.stopPublishing();\n  }\n  next(action);\n});\n\nvar focus = (marshal => {\n  let isWatching = false;\n  return () => next => action => {\n    if (action.type === 'INITIAL_PUBLISH') {\n      isWatching = true;\n      marshal.tryRecordFocus(action.payload.critical.draggable.id);\n      next(action);\n      marshal.tryRestoreFocusRecorded();\n      return;\n    }\n    next(action);\n    if (!isWatching) {\n      return;\n    }\n    if (action.type === 'FLUSH') {\n      isWatching = false;\n      marshal.tryRestoreFocusRecorded();\n      return;\n    }\n    if (action.type === 'DROP_COMPLETE') {\n      isWatching = false;\n      const result = action.payload.completed.result;\n      if (result.combine) {\n        marshal.tryShiftRecord(result.draggableId, result.combine.draggableId);\n      }\n      marshal.tryRestoreFocusRecorded();\n    }\n  };\n});\n\nconst shouldStop = action => action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATE' || action.type === 'FLUSH';\nvar autoScroll = (autoScroller => store => next => action => {\n  if (shouldStop(action)) {\n    autoScroller.stop();\n    next(action);\n    return;\n  }\n  if (action.type === 'INITIAL_PUBLISH') {\n    next(action);\n    const state = store.getState();\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected phase to be DRAGGING after INITIAL_PUBLISH') : invariant(false) : void 0;\n    autoScroller.start(state);\n    return;\n  }\n  next(action);\n  autoScroller.scroll(store.getState());\n});\n\nconst pendingDrop = store => next => action => {\n  next(action);\n  if (action.type !== 'PUBLISH_WHILE_DRAGGING') {\n    return;\n  }\n  const postActionState = store.getState();\n  if (postActionState.phase !== 'DROP_PENDING') {\n    return;\n  }\n  if (postActionState.isWaiting) {\n    return;\n  }\n  store.dispatch(drop$1({\n    reason: postActionState.reason\n  }));\n};\nvar pendingDrop$1 = pendingDrop;\n\nconst composeEnhancers = process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({\n  name: '@hello-pangea/dnd'\n}) : compose;\nvar createStore = (({\n  dimensionMarshal,\n  focusMarshal,\n  styleMarshal,\n  getResponders,\n  announce,\n  autoScroller\n}) => createStore$1(reducer, composeEnhancers(applyMiddleware(style(styleMarshal), dimensionMarshalStopper(dimensionMarshal), lift(dimensionMarshal), drop, dropAnimationFinish, dropAnimationFlushOnScroll, pendingDrop$1, autoScroll(autoScroller), scrollListener$1, focus(focusMarshal), responders(getResponders, announce)))));\n\nconst clean$1 = () => ({\n  additions: {},\n  removals: {},\n  modified: {}\n});\nfunction createPublisher({\n  registry,\n  callbacks\n}) {\n  let staging = clean$1();\n  let frameId = null;\n  const collect = () => {\n    if (frameId) {\n      return;\n    }\n    callbacks.collectionStarting();\n    frameId = requestAnimationFrame(() => {\n      frameId = null;\n      start();\n      const {\n        additions,\n        removals,\n        modified\n      } = staging;\n      const added = Object.keys(additions).map(id => registry.draggable.getById(id).getDimension(origin)).sort((a, b) => a.descriptor.index - b.descriptor.index);\n      const updated = Object.keys(modified).map(id => {\n        const entry = registry.droppable.getById(id);\n        const scroll = entry.callbacks.getScrollWhileDragging();\n        return {\n          droppableId: id,\n          scroll\n        };\n      });\n      const result = {\n        additions: added,\n        removals: Object.keys(removals),\n        modified: updated\n      };\n      staging = clean$1();\n      finish();\n      callbacks.publish(result);\n    });\n  };\n  const add = entry => {\n    const id = entry.descriptor.id;\n    staging.additions[id] = entry;\n    staging.modified[entry.descriptor.droppableId] = true;\n    if (staging.removals[id]) {\n      delete staging.removals[id];\n    }\n    collect();\n  };\n  const remove = entry => {\n    const descriptor = entry.descriptor;\n    staging.removals[descriptor.id] = true;\n    staging.modified[descriptor.droppableId] = true;\n    if (staging.additions[descriptor.id]) {\n      delete staging.additions[descriptor.id];\n    }\n    collect();\n  };\n  const stop = () => {\n    if (!frameId) {\n      return;\n    }\n    cancelAnimationFrame(frameId);\n    frameId = null;\n    staging = clean$1();\n  };\n  return {\n    add,\n    remove,\n    stop\n  };\n}\n\nvar getMaxScroll = (({\n  scrollHeight,\n  scrollWidth,\n  height,\n  width\n}) => {\n  const maxScroll = subtract({\n    x: scrollWidth,\n    y: scrollHeight\n  }, {\n    x: width,\n    y: height\n  });\n  const adjustedMaxScroll = {\n    x: Math.max(0, maxScroll.x),\n    y: Math.max(0, maxScroll.y)\n  };\n  return adjustedMaxScroll;\n});\n\nvar getDocumentElement = (() => {\n  const doc = document.documentElement;\n  !doc ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.documentElement') : invariant(false) : void 0;\n  return doc;\n});\n\nvar getMaxWindowScroll = (() => {\n  const doc = getDocumentElement();\n  const maxScroll = getMaxScroll({\n    scrollHeight: doc.scrollHeight,\n    scrollWidth: doc.scrollWidth,\n    width: doc.clientWidth,\n    height: doc.clientHeight\n  });\n  return maxScroll;\n});\n\nvar getViewport = (() => {\n  const scroll = getWindowScroll();\n  const maxScroll = getMaxWindowScroll();\n  const top = scroll.y;\n  const left = scroll.x;\n  const doc = getDocumentElement();\n  const width = doc.clientWidth;\n  const height = doc.clientHeight;\n  const right = left + width;\n  const bottom = top + height;\n  const frame = getRect({\n    top,\n    left,\n    right,\n    bottom\n  });\n  const viewport = {\n    frame,\n    scroll: {\n      initial: scroll,\n      current: scroll,\n      max: maxScroll,\n      diff: {\n        value: origin,\n        displacement: origin\n      }\n    }\n  };\n  return viewport;\n});\n\nvar getInitialPublish = (({\n  critical,\n  scrollOptions,\n  registry\n}) => {\n  start();\n  const viewport = getViewport();\n  const windowScroll = viewport.scroll.current;\n  const home = critical.droppable;\n  const droppables = registry.droppable.getAllByType(home.type).map(entry => entry.callbacks.getDimensionAndWatchScroll(windowScroll, scrollOptions));\n  const draggables = registry.draggable.getAllByType(critical.draggable.type).map(entry => entry.getDimension(windowScroll));\n  const dimensions = {\n    draggables: toDraggableMap(draggables),\n    droppables: toDroppableMap(droppables)\n  };\n  finish();\n  const result = {\n    dimensions,\n    critical,\n    viewport\n  };\n  return result;\n});\n\nfunction shouldPublishUpdate(registry, dragging, entry) {\n  if (entry.descriptor.id === dragging.id) {\n    return false;\n  }\n  if (entry.descriptor.type !== dragging.type) {\n    return false;\n  }\n  const home = registry.droppable.getById(entry.descriptor.droppableId);\n  if (home.descriptor.mode !== 'virtual') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      You are attempting to add or remove a Draggable [id: ${entry.descriptor.id}]\n      while a drag is occurring. This is only supported for virtual lists.\n\n      See https://github.com/hello-pangea/dnd/blob/main/docs/patterns/virtual-lists.md\n    `) : void 0;\n    return false;\n  }\n  return true;\n}\nvar createDimensionMarshal = ((registry, callbacks) => {\n  let collection = null;\n  const publisher = createPublisher({\n    callbacks: {\n      publish: callbacks.publishWhileDragging,\n      collectionStarting: callbacks.collectionStarting\n    },\n    registry\n  });\n  const updateDroppableIsEnabled = (id, isEnabled) => {\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update is enabled flag of Droppable ${id} as it is not registered`) : invariant(false) : void 0;\n    if (!collection) {\n      return;\n    }\n    callbacks.updateDroppableIsEnabled({\n      id,\n      isEnabled\n    });\n  };\n  const updateDroppableIsCombineEnabled = (id, isCombineEnabled) => {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update isCombineEnabled flag of Droppable ${id} as it is not registered`) : invariant(false) : void 0;\n    callbacks.updateDroppableIsCombineEnabled({\n      id,\n      isCombineEnabled\n    });\n  };\n  const updateDroppableScroll = (id, newScroll) => {\n    if (!collection) {\n      return;\n    }\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot update the scroll on Droppable ${id} as it is not registered`) : invariant(false) : void 0;\n    callbacks.updateDroppableScroll({\n      id,\n      newScroll\n    });\n  };\n  const scrollDroppable = (id, change) => {\n    if (!collection) {\n      return;\n    }\n    registry.droppable.getById(id).callbacks.scroll(change);\n  };\n  const stopPublishing = () => {\n    if (!collection) {\n      return;\n    }\n    publisher.stop();\n    const home = collection.critical.droppable;\n    registry.droppable.getAllByType(home.type).forEach(entry => entry.callbacks.dragStopped());\n    collection.unsubscribe();\n    collection = null;\n  };\n  const subscriber = event => {\n    !collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should only be subscribed when a collection is occurring') : invariant(false) : void 0;\n    const dragging = collection.critical.draggable;\n    if (event.type === 'ADDITION') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.add(event.value);\n      }\n    }\n    if (event.type === 'REMOVAL') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.remove(event.value);\n      }\n    }\n  };\n  const startPublishing = request => {\n    !!collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start capturing critical dimensions as there is already a collection') : invariant(false) : void 0;\n    const entry = registry.draggable.getById(request.draggableId);\n    const home = registry.droppable.getById(entry.descriptor.droppableId);\n    const critical = {\n      draggable: entry.descriptor,\n      droppable: home.descriptor\n    };\n    const unsubscribe = registry.subscribe(subscriber);\n    collection = {\n      critical,\n      unsubscribe\n    };\n    return getInitialPublish({\n      critical,\n      registry,\n      scrollOptions: request.scrollOptions\n    });\n  };\n  const marshal = {\n    updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled,\n    scrollDroppable,\n    updateDroppableScroll,\n    startPublishing,\n    stopPublishing\n  };\n  return marshal;\n});\n\nvar canStartDrag = ((state, id) => {\n  if (state.phase === 'IDLE') {\n    return true;\n  }\n  if (state.phase !== 'DROP_ANIMATING') {\n    return false;\n  }\n  if (state.completed.result.draggableId === id) {\n    return false;\n  }\n  return state.completed.result.reason === 'DROP';\n});\n\nvar scrollWindow = (change => {\n  window.scrollBy(change.x, change.y);\n});\n\nconst getScrollableDroppables = memoizeOne(droppables => toDroppableList(droppables).filter(droppable => {\n  if (!droppable.isEnabled) {\n    return false;\n  }\n  if (!droppable.frame) {\n    return false;\n  }\n  return true;\n}));\nconst getScrollableDroppableOver = (target, droppables) => {\n  const maybe = getScrollableDroppables(droppables).find(droppable => {\n    !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid result') : invariant(false) : void 0;\n    return isPositionInFrame(droppable.frame.pageMarginBox)(target);\n  }) || null;\n  return maybe;\n};\nvar getBestScrollableDroppable = (({\n  center,\n  destination,\n  droppables\n}) => {\n  if (destination) {\n    const dimension = droppables[destination];\n    if (!dimension.frame) {\n      return null;\n    }\n    return dimension;\n  }\n  const dimension = getScrollableDroppableOver(center, droppables);\n  return dimension;\n});\n\nconst defaultAutoScrollerOptions = {\n  startFromPercentage: 0.25,\n  maxScrollAtPercentage: 0.05,\n  maxPixelScroll: 28,\n  ease: percentage => percentage ** 2,\n  durationDampening: {\n    stopDampeningAt: 1200,\n    accelerateAt: 360\n  },\n  disabled: false\n};\n\nvar getDistanceThresholds = ((container, axis, getAutoScrollerOptions = () => defaultAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  const startScrollingFrom = container[axis.size] * autoScrollerOptions.startFromPercentage;\n  const maxScrollValueAt = container[axis.size] * autoScrollerOptions.maxScrollAtPercentage;\n  const thresholds = {\n    startScrollingFrom,\n    maxScrollValueAt\n  };\n  return thresholds;\n});\n\nvar getPercentage = (({\n  startOfRange,\n  endOfRange,\n  current\n}) => {\n  const range = endOfRange - startOfRange;\n  if (range === 0) {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Detected distance range of 0 in the fluid auto scroller\n      This is unexpected and would cause a divide by 0 issue.\n      Not allowing an auto scroll\n    `) : void 0;\n    return 0;\n  }\n  const currentInRange = current - startOfRange;\n  const percentage = currentInRange / range;\n  return percentage;\n});\n\nvar minScroll = 1;\n\nvar getValueFromDistance = ((distanceToEdge, thresholds, getAutoScrollerOptions = () => defaultAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  if (distanceToEdge > thresholds.startScrollingFrom) {\n    return 0;\n  }\n  if (distanceToEdge <= thresholds.maxScrollValueAt) {\n    return autoScrollerOptions.maxPixelScroll;\n  }\n  if (distanceToEdge === thresholds.startScrollingFrom) {\n    return minScroll;\n  }\n  const percentageFromMaxScrollValueAt = getPercentage({\n    startOfRange: thresholds.maxScrollValueAt,\n    endOfRange: thresholds.startScrollingFrom,\n    current: distanceToEdge\n  });\n  const percentageFromStartScrollingFrom = 1 - percentageFromMaxScrollValueAt;\n  const scroll = autoScrollerOptions.maxPixelScroll * autoScrollerOptions.ease(percentageFromStartScrollingFrom);\n  return Math.ceil(scroll);\n});\n\nvar dampenValueByTime = ((proposedScroll, dragStartTime, getAutoScrollerOptions) => {\n  const autoScrollerOptions = getAutoScrollerOptions();\n  const accelerateAt = autoScrollerOptions.durationDampening.accelerateAt;\n  const stopAt = autoScrollerOptions.durationDampening.stopDampeningAt;\n  const startOfRange = dragStartTime;\n  const endOfRange = stopAt;\n  const now = Date.now();\n  const runTime = now - startOfRange;\n  if (runTime >= stopAt) {\n    return proposedScroll;\n  }\n  if (runTime < accelerateAt) {\n    return minScroll;\n  }\n  const betweenAccelerateAtAndStopAtPercentage = getPercentage({\n    startOfRange: accelerateAt,\n    endOfRange,\n    current: runTime\n  });\n  const scroll = proposedScroll * autoScrollerOptions.ease(betweenAccelerateAtAndStopAtPercentage);\n  return Math.ceil(scroll);\n});\n\nvar getValue = (({\n  distanceToEdge,\n  thresholds,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const scroll = getValueFromDistance(distanceToEdge, thresholds, getAutoScrollerOptions);\n  if (scroll === 0) {\n    return 0;\n  }\n  if (!shouldUseTimeDampening) {\n    return scroll;\n  }\n  return Math.max(dampenValueByTime(scroll, dragStartTime, getAutoScrollerOptions), minScroll);\n});\n\nvar getScrollOnAxis = (({\n  container,\n  distanceToEdges,\n  dragStartTime,\n  axis,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const thresholds = getDistanceThresholds(container, axis, getAutoScrollerOptions);\n  const isCloserToEnd = distanceToEdges[axis.end] < distanceToEdges[axis.start];\n  if (isCloserToEnd) {\n    return getValue({\n      distanceToEdge: distanceToEdges[axis.end],\n      thresholds,\n      dragStartTime,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n  }\n  return -1 * getValue({\n    distanceToEdge: distanceToEdges[axis.start],\n    thresholds,\n    dragStartTime,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n});\n\nvar adjustForSizeLimits = (({\n  container,\n  subject,\n  proposedScroll\n}) => {\n  const isTooBigVertically = subject.height > container.height;\n  const isTooBigHorizontally = subject.width > container.width;\n  if (!isTooBigHorizontally && !isTooBigVertically) {\n    return proposedScroll;\n  }\n  if (isTooBigHorizontally && isTooBigVertically) {\n    return null;\n  }\n  return {\n    x: isTooBigHorizontally ? 0 : proposedScroll.x,\n    y: isTooBigVertically ? 0 : proposedScroll.y\n  };\n});\n\nconst clean = apply(value => value === 0 ? 0 : value);\nvar getScroll$1 = (({\n  dragStartTime,\n  container,\n  subject,\n  center,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const distanceToEdges = {\n    top: center.y - container.top,\n    right: container.right - center.x,\n    bottom: container.bottom - center.y,\n    left: center.x - container.left\n  };\n  const y = getScrollOnAxis({\n    container,\n    distanceToEdges,\n    dragStartTime,\n    axis: vertical,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  const x = getScrollOnAxis({\n    container,\n    distanceToEdges,\n    dragStartTime,\n    axis: horizontal,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  const required = clean({\n    x,\n    y\n  });\n  if (isEqual$1(required, origin)) {\n    return null;\n  }\n  const limited = adjustForSizeLimits({\n    container,\n    subject,\n    proposedScroll: required\n  });\n  if (!limited) {\n    return null;\n  }\n  return isEqual$1(limited, origin) ? null : limited;\n});\n\nconst smallestSigned = apply(value => {\n  if (value === 0) {\n    return 0;\n  }\n  return value > 0 ? 1 : -1;\n});\nconst getOverlap = (() => {\n  const getRemainder = (target, max) => {\n    if (target < 0) {\n      return target;\n    }\n    if (target > max) {\n      return target - max;\n    }\n    return 0;\n  };\n  return ({\n    current,\n    max,\n    change\n  }) => {\n    const targetScroll = add(current, change);\n    const overlap = {\n      x: getRemainder(targetScroll.x, max.x),\n      y: getRemainder(targetScroll.y, max.y)\n    };\n    if (isEqual$1(overlap, origin)) {\n      return null;\n    }\n    return overlap;\n  };\n})();\nconst canPartiallyScroll = ({\n  max: rawMax,\n  current,\n  change\n}) => {\n  const max = {\n    x: Math.max(current.x, rawMax.x),\n    y: Math.max(current.y, rawMax.y)\n  };\n  const smallestChange = smallestSigned(change);\n  const overlap = getOverlap({\n    max,\n    current,\n    change: smallestChange\n  });\n  if (!overlap) {\n    return true;\n  }\n  if (smallestChange.x !== 0 && overlap.x === 0) {\n    return true;\n  }\n  if (smallestChange.y !== 0 && overlap.y === 0) {\n    return true;\n  }\n  return false;\n};\nconst canScrollWindow = (viewport, change) => canPartiallyScroll({\n  current: viewport.scroll.current,\n  max: viewport.scroll.max,\n  change\n});\nconst getWindowOverlap = (viewport, change) => {\n  if (!canScrollWindow(viewport, change)) {\n    return null;\n  }\n  const max = viewport.scroll.max;\n  const current = viewport.scroll.current;\n  return getOverlap({\n    current,\n    max,\n    change\n  });\n};\nconst canScrollDroppable = (droppable, change) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return false;\n  }\n  return canPartiallyScroll({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change\n  });\n};\nconst getDroppableOverlap = (droppable, change) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  if (!canScrollDroppable(droppable, change)) {\n    return null;\n  }\n  return getOverlap({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change\n  });\n};\n\nvar getWindowScrollChange = (({\n  viewport,\n  subject,\n  center,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const scroll = getScroll$1({\n    dragStartTime,\n    container: viewport.frame,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  return scroll && canScrollWindow(viewport, scroll) ? scroll : null;\n});\n\nvar getDroppableScrollChange = (({\n  droppable,\n  subject,\n  center,\n  dragStartTime,\n  shouldUseTimeDampening,\n  getAutoScrollerOptions\n}) => {\n  const frame = droppable.frame;\n  if (!frame) {\n    return null;\n  }\n  const scroll = getScroll$1({\n    dragStartTime,\n    container: frame.pageMarginBox,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  return scroll && canScrollDroppable(droppable, scroll) ? scroll : null;\n});\n\nvar scroll = (({\n  state,\n  dragStartTime,\n  shouldUseTimeDampening,\n  scrollWindow,\n  scrollDroppable,\n  getAutoScrollerOptions\n}) => {\n  const center = state.current.page.borderBoxCenter;\n  const draggable = state.dimensions.draggables[state.critical.draggable.id];\n  const subject = draggable.page.marginBox;\n  if (state.isWindowScrollAllowed) {\n    const viewport = state.viewport;\n    const change = getWindowScrollChange({\n      dragStartTime,\n      viewport,\n      subject,\n      center,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n    if (change) {\n      scrollWindow(change);\n      return;\n    }\n  }\n  const droppable = getBestScrollableDroppable({\n    center,\n    destination: whatIsDraggedOver(state.impact),\n    droppables: state.dimensions.droppables\n  });\n  if (!droppable) {\n    return;\n  }\n  const change = getDroppableScrollChange({\n    dragStartTime,\n    droppable,\n    subject,\n    center,\n    shouldUseTimeDampening,\n    getAutoScrollerOptions\n  });\n  if (change) {\n    scrollDroppable(droppable.descriptor.id, change);\n  }\n});\n\nvar createFluidScroller = (({\n  scrollWindow,\n  scrollDroppable,\n  getAutoScrollerOptions = () => defaultAutoScrollerOptions\n}) => {\n  const scheduleWindowScroll = rafSchd(scrollWindow);\n  const scheduleDroppableScroll = rafSchd(scrollDroppable);\n  let dragging = null;\n  const tryScroll = state => {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fluid scroll if not dragging') : invariant(false) : void 0;\n    const {\n      shouldUseTimeDampening,\n      dragStartTime\n    } = dragging;\n    scroll({\n      state,\n      scrollWindow: scheduleWindowScroll,\n      scrollDroppable: scheduleDroppableScroll,\n      dragStartTime,\n      shouldUseTimeDampening,\n      getAutoScrollerOptions\n    });\n  };\n  const start$1 = state => {\n    start();\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start auto scrolling when already started') : invariant(false) : void 0;\n    const dragStartTime = Date.now();\n    let wasScrollNeeded = false;\n    const fakeScrollCallback = () => {\n      wasScrollNeeded = true;\n    };\n    scroll({\n      state,\n      dragStartTime: 0,\n      shouldUseTimeDampening: false,\n      scrollWindow: fakeScrollCallback,\n      scrollDroppable: fakeScrollCallback,\n      getAutoScrollerOptions\n    });\n    dragging = {\n      dragStartTime,\n      shouldUseTimeDampening: wasScrollNeeded\n    };\n    finish();\n    if (wasScrollNeeded) {\n      tryScroll(state);\n    }\n  };\n  const stop = () => {\n    if (!dragging) {\n      return;\n    }\n    scheduleWindowScroll.cancel();\n    scheduleDroppableScroll.cancel();\n    dragging = null;\n  };\n  return {\n    start: start$1,\n    stop,\n    scroll: tryScroll\n  };\n});\n\nvar createJumpScroller = (({\n  move,\n  scrollDroppable,\n  scrollWindow\n}) => {\n  const moveByOffset = (state, offset) => {\n    const client = add(state.current.client.selection, offset);\n    move({\n      client\n    });\n  };\n  const scrollDroppableAsMuchAsItCan = (droppable, change) => {\n    if (!canScrollDroppable(droppable, change)) {\n      return change;\n    }\n    const overlap = getDroppableOverlap(droppable, change);\n    if (!overlap) {\n      scrollDroppable(droppable.descriptor.id, change);\n      return null;\n    }\n    const whatTheDroppableCanScroll = subtract(change, overlap);\n    scrollDroppable(droppable.descriptor.id, whatTheDroppableCanScroll);\n    const remainder = subtract(change, whatTheDroppableCanScroll);\n    return remainder;\n  };\n  const scrollWindowAsMuchAsItCan = (isWindowScrollAllowed, viewport, change) => {\n    if (!isWindowScrollAllowed) {\n      return change;\n    }\n    if (!canScrollWindow(viewport, change)) {\n      return change;\n    }\n    const overlap = getWindowOverlap(viewport, change);\n    if (!overlap) {\n      scrollWindow(change);\n      return null;\n    }\n    const whatTheWindowCanScroll = subtract(change, overlap);\n    scrollWindow(whatTheWindowCanScroll);\n    const remainder = subtract(change, whatTheWindowCanScroll);\n    return remainder;\n  };\n  const jumpScroller = state => {\n    const request = state.scrollJumpRequest;\n    if (!request) {\n      return;\n    }\n    const destination = whatIsDraggedOver(state.impact);\n    !destination ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot perform a jump scroll when there is no destination') : invariant(false) : void 0;\n    const droppableRemainder = scrollDroppableAsMuchAsItCan(state.dimensions.droppables[destination], request);\n    if (!droppableRemainder) {\n      return;\n    }\n    const viewport = state.viewport;\n    const windowRemainder = scrollWindowAsMuchAsItCan(state.isWindowScrollAllowed, viewport, droppableRemainder);\n    if (!windowRemainder) {\n      return;\n    }\n    moveByOffset(state, windowRemainder);\n  };\n  return jumpScroller;\n});\n\nvar createAutoScroller = (({\n  scrollDroppable,\n  scrollWindow,\n  move,\n  getAutoScrollerOptions\n}) => {\n  const fluidScroller = createFluidScroller({\n    scrollWindow,\n    scrollDroppable,\n    getAutoScrollerOptions\n  });\n  const jumpScroll = createJumpScroller({\n    move,\n    scrollWindow,\n    scrollDroppable\n  });\n  const scroll = state => {\n    const autoScrollerOptions = getAutoScrollerOptions();\n    if (autoScrollerOptions.disabled || state.phase !== 'DRAGGING') {\n      return;\n    }\n    if (state.movementMode === 'FLUID') {\n      fluidScroller.scroll(state);\n      return;\n    }\n    if (!state.scrollJumpRequest) {\n      return;\n    }\n    jumpScroll(state);\n  };\n  const scroller = {\n    scroll,\n    start: fluidScroller.start,\n    stop: fluidScroller.stop\n  };\n  return scroller;\n});\n\nconst prefix = 'data-rfd';\nconst dragHandle = (() => {\n  const base = `${prefix}-drag-handle`;\n  return {\n    base,\n    draggableId: `${base}-draggable-id`,\n    contextId: `${base}-context-id`\n  };\n})();\nconst draggable = (() => {\n  const base = `${prefix}-draggable`;\n  return {\n    base,\n    contextId: `${base}-context-id`,\n    id: `${base}-id`\n  };\n})();\nconst droppable = (() => {\n  const base = `${prefix}-droppable`;\n  return {\n    base,\n    contextId: `${base}-context-id`,\n    id: `${base}-id`\n  };\n})();\nconst scrollContainer = {\n  contextId: `${prefix}-scroll-container-context-id`\n};\n\nconst makeGetSelector = context => attribute => `[${attribute}=\"${context}\"]`;\nconst getStyles = (rules, property) => rules.map(rule => {\n  const value = rule.styles[property];\n  if (!value) {\n    return '';\n  }\n  return `${rule.selector} { ${value} }`;\n}).join(' ');\nconst noPointerEvents = 'pointer-events: none;';\nvar getStyles$1 = (contextId => {\n  const getSelector = makeGetSelector(contextId);\n  const dragHandle$1 = (() => {\n    const grabCursor = `\n      cursor: -webkit-grab;\n      cursor: grab;\n    `;\n    return {\n      selector: getSelector(dragHandle.contextId),\n      styles: {\n        always: `\n          -webkit-touch-callout: none;\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\n          touch-action: manipulation;\n        `,\n        resting: grabCursor,\n        dragging: noPointerEvents,\n        dropAnimating: grabCursor\n      }\n    };\n  })();\n  const draggable$1 = (() => {\n    const transition = `\n      transition: ${transitions.outOfTheWay};\n    `;\n    return {\n      selector: getSelector(draggable.contextId),\n      styles: {\n        dragging: transition,\n        dropAnimating: transition,\n        userCancel: transition\n      }\n    };\n  })();\n  const droppable$1 = {\n    selector: getSelector(droppable.contextId),\n    styles: {\n      always: `overflow-anchor: none;`\n    }\n  };\n  const body = {\n    selector: 'body',\n    styles: {\n      dragging: `\n        cursor: grabbing;\n        cursor: -webkit-grabbing;\n        user-select: none;\n        -webkit-user-select: none;\n        -moz-user-select: none;\n        -ms-user-select: none;\n        overflow-anchor: none;\n      `\n    }\n  };\n  const rules = [draggable$1, dragHandle$1, droppable$1, body];\n  return {\n    always: getStyles(rules, 'always'),\n    resting: getStyles(rules, 'resting'),\n    dragging: getStyles(rules, 'dragging'),\n    dropAnimating: getStyles(rules, 'dropAnimating'),\n    userCancel: getStyles(rules, 'userCancel')\n  };\n});\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect$1 : useEffect;\nvar useLayoutEffect = useIsomorphicLayoutEffect;\n\nconst getHead = () => {\n  const head = document.querySelector('head');\n  !head ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find the head to append a style to') : invariant(false) : void 0;\n  return head;\n};\nconst createStyleEl = nonce => {\n  const el = document.createElement('style');\n  if (nonce) {\n    el.setAttribute('nonce', nonce);\n  }\n  el.type = 'text/css';\n  return el;\n};\nfunction useStyleMarshal(contextId, nonce) {\n  const styles = useMemo(() => getStyles$1(contextId), [contextId]);\n  const alwaysRef = useRef(null);\n  const dynamicRef = useRef(null);\n  const setDynamicStyle = useCallback(memoizeOne(proposed => {\n    const el = dynamicRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant(false) : void 0;\n    el.textContent = proposed;\n  }), []);\n  const setAlwaysStyle = useCallback(proposed => {\n    const el = alwaysRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant(false) : void 0;\n    el.textContent = proposed;\n  }, []);\n  useLayoutEffect(() => {\n    !(!alwaysRef.current && !dynamicRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'style elements already mounted') : invariant(false) : void 0;\n    const always = createStyleEl(nonce);\n    const dynamic = createStyleEl(nonce);\n    alwaysRef.current = always;\n    dynamicRef.current = dynamic;\n    always.setAttribute(`${prefix}-always`, contextId);\n    dynamic.setAttribute(`${prefix}-dynamic`, contextId);\n    getHead().appendChild(always);\n    getHead().appendChild(dynamic);\n    setAlwaysStyle(styles.always);\n    setDynamicStyle(styles.resting);\n    return () => {\n      const remove = ref => {\n        const current = ref.current;\n        !current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot unmount ref as it is not set') : invariant(false) : void 0;\n        getHead().removeChild(current);\n        ref.current = null;\n      };\n      remove(alwaysRef);\n      remove(dynamicRef);\n    };\n  }, [nonce, setAlwaysStyle, setDynamicStyle, styles.always, styles.resting, contextId]);\n  const dragging = useCallback(() => setDynamicStyle(styles.dragging), [setDynamicStyle, styles.dragging]);\n  const dropping = useCallback(reason => {\n    if (reason === 'DROP') {\n      setDynamicStyle(styles.dropAnimating);\n      return;\n    }\n    setDynamicStyle(styles.userCancel);\n  }, [setDynamicStyle, styles.dropAnimating, styles.userCancel]);\n  const resting = useCallback(() => {\n    if (!dynamicRef.current) {\n      return;\n    }\n    setDynamicStyle(styles.resting);\n  }, [setDynamicStyle, styles.resting]);\n  const marshal = useMemo(() => ({\n    dragging,\n    dropping,\n    resting\n  }), [dragging, dropping, resting]);\n  return marshal;\n}\n\nfunction querySelectorAll(parentNode, selector) {\n  return Array.from(parentNode.querySelectorAll(selector));\n}\n\nvar getWindowFromEl = (el => {\n  if (el && el.ownerDocument && el.ownerDocument.defaultView) {\n    return el.ownerDocument.defaultView;\n  }\n  return window;\n});\n\nfunction isHtmlElement(el) {\n  return el instanceof getWindowFromEl(el).HTMLElement;\n}\n\nfunction findDragHandle(contextId, draggableId) {\n  const selector = `[${dragHandle.contextId}=\"${contextId}\"]`;\n  const possible = querySelectorAll(document, selector);\n  if (!possible.length) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find any drag handles in the context \"${contextId}\"`) : void 0;\n    return null;\n  }\n  const handle = possible.find(el => {\n    return el.getAttribute(dragHandle.draggableId) === draggableId;\n  });\n  if (!handle) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find drag handle with id \"${draggableId}\" as no handle with a matching id was found`) : void 0;\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle needs to be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\n\nfunction useFocusMarshal(contextId) {\n  const entriesRef = useRef({});\n  const recordRef = useRef(null);\n  const restoreFocusFrameRef = useRef(null);\n  const isMountedRef = useRef(false);\n  const register = useCallback(function register(id, focus) {\n    const entry = {\n      id,\n      focus\n    };\n    entriesRef.current[id] = entry;\n    return function unregister() {\n      const entries = entriesRef.current;\n      const current = entries[id];\n      if (current !== entry) {\n        delete entries[id];\n      }\n    };\n  }, []);\n  const tryGiveFocus = useCallback(function tryGiveFocus(tryGiveFocusTo) {\n    const handle = findDragHandle(contextId, tryGiveFocusTo);\n    if (handle && handle !== document.activeElement) {\n      handle.focus();\n    }\n  }, [contextId]);\n  const tryShiftRecord = useCallback(function tryShiftRecord(previous, redirectTo) {\n    if (recordRef.current === previous) {\n      recordRef.current = redirectTo;\n    }\n  }, []);\n  const tryRestoreFocusRecorded = useCallback(function tryRestoreFocusRecorded() {\n    if (restoreFocusFrameRef.current) {\n      return;\n    }\n    if (!isMountedRef.current) {\n      return;\n    }\n    restoreFocusFrameRef.current = requestAnimationFrame(() => {\n      restoreFocusFrameRef.current = null;\n      const record = recordRef.current;\n      if (record) {\n        tryGiveFocus(record);\n      }\n    });\n  }, [tryGiveFocus]);\n  const tryRecordFocus = useCallback(function tryRecordFocus(id) {\n    recordRef.current = null;\n    const focused = document.activeElement;\n    if (!focused) {\n      return;\n    }\n    if (focused.getAttribute(dragHandle.draggableId) !== id) {\n      return;\n    }\n    recordRef.current = id;\n  }, []);\n  useLayoutEffect(() => {\n    isMountedRef.current = true;\n    return function clearFrameOnUnmount() {\n      isMountedRef.current = false;\n      const frameId = restoreFocusFrameRef.current;\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n    };\n  }, []);\n  const marshal = useMemo(() => ({\n    register,\n    tryRecordFocus,\n    tryRestoreFocusRecorded,\n    tryShiftRecord\n  }), [register, tryRecordFocus, tryRestoreFocusRecorded, tryShiftRecord]);\n  return marshal;\n}\n\nfunction createRegistry() {\n  const entries = {\n    draggables: {},\n    droppables: {}\n  };\n  const subscribers = [];\n  function subscribe(cb) {\n    subscribers.push(cb);\n    return function unsubscribe() {\n      const index = subscribers.indexOf(cb);\n      if (index === -1) {\n        return;\n      }\n      subscribers.splice(index, 1);\n    };\n  }\n  function notify(event) {\n    if (subscribers.length) {\n      subscribers.forEach(cb => cb(event));\n    }\n  }\n  function findDraggableById(id) {\n    return entries.draggables[id] || null;\n  }\n  function getDraggableById(id) {\n    const entry = findDraggableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find draggable entry with id [${id}]`) : invariant(false) : void 0;\n    return entry;\n  }\n  const draggableAPI = {\n    register: entry => {\n      entries.draggables[entry.descriptor.id] = entry;\n      notify({\n        type: 'ADDITION',\n        value: entry\n      });\n    },\n    update: (entry, last) => {\n      const current = entries.draggables[last.descriptor.id];\n      if (!current) {\n        return;\n      }\n      if (current.uniqueId !== entry.uniqueId) {\n        return;\n      }\n      delete entries.draggables[last.descriptor.id];\n      entries.draggables[entry.descriptor.id] = entry;\n    },\n    unregister: entry => {\n      const draggableId = entry.descriptor.id;\n      const current = findDraggableById(draggableId);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.draggables[draggableId];\n      if (entries.droppables[entry.descriptor.droppableId]) {\n        notify({\n          type: 'REMOVAL',\n          value: entry\n        });\n      }\n    },\n    getById: getDraggableById,\n    findById: findDraggableById,\n    exists: id => Boolean(findDraggableById(id)),\n    getAllByType: type => Object.values(entries.draggables).filter(entry => entry.descriptor.type === type)\n  };\n  function findDroppableById(id) {\n    return entries.droppables[id] || null;\n  }\n  function getDroppableById(id) {\n    const entry = findDroppableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot find droppable entry with id [${id}]`) : invariant(false) : void 0;\n    return entry;\n  }\n  const droppableAPI = {\n    register: entry => {\n      entries.droppables[entry.descriptor.id] = entry;\n    },\n    unregister: entry => {\n      const current = findDroppableById(entry.descriptor.id);\n      if (!current) {\n        return;\n      }\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n      delete entries.droppables[entry.descriptor.id];\n    },\n    getById: getDroppableById,\n    findById: findDroppableById,\n    exists: id => Boolean(findDroppableById(id)),\n    getAllByType: type => Object.values(entries.droppables).filter(entry => entry.descriptor.type === type)\n  };\n  function clean() {\n    entries.draggables = {};\n    entries.droppables = {};\n    subscribers.length = 0;\n  }\n  return {\n    draggable: draggableAPI,\n    droppable: droppableAPI,\n    subscribe,\n    clean\n  };\n}\n\nfunction useRegistry() {\n  const registry = useMemo(createRegistry, []);\n  useEffect(() => {\n    return function unmount() {\n      if (React.version.startsWith('16') || React.version.startsWith('17')) {\n        requestAnimationFrame(registry.clean);\n      } else {\n        registry.clean();\n      }\n    };\n  }, [registry]);\n  return registry;\n}\n\nvar StoreContext = React.createContext(null);\n\nvar getBodyElement = (() => {\n  const body = document.body;\n  !body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.body') : invariant(false) : void 0;\n  return body;\n});\n\nconst visuallyHidden = {\n  position: 'absolute',\n  width: '1px',\n  height: '1px',\n  margin: '-1px',\n  border: '0',\n  padding: '0',\n  overflow: 'hidden',\n  clip: 'rect(0 0 0 0)',\n  'clip-path': 'inset(100%)'\n};\nvar visuallyHidden$1 = visuallyHidden;\n\nconst getId = contextId => `rfd-announcement-${contextId}`;\nfunction useAnnouncer(contextId) {\n  const id = useMemo(() => getId(contextId), [contextId]);\n  const ref = useRef(null);\n  useEffect(function setup() {\n    const el = document.createElement('div');\n    ref.current = el;\n    el.id = id;\n    el.setAttribute('aria-live', 'assertive');\n    el.setAttribute('aria-atomic', 'true');\n    _extends(el.style, visuallyHidden$1);\n    getBodyElement().appendChild(el);\n    return function cleanup() {\n      setTimeout(function remove() {\n        const body = getBodyElement();\n        if (body.contains(el)) {\n          body.removeChild(el);\n        }\n        if (el === ref.current) {\n          ref.current = null;\n        }\n      });\n    };\n  }, [id]);\n  const announce = useCallback(message => {\n    const el = ref.current;\n    if (el) {\n      el.textContent = message;\n      return;\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      A screen reader message was trying to be announced but it was unable to do so.\n      This can occur if you unmount your <DragDropContext /> in your onDragEnd.\n      Consider calling provided.announce() before the unmount so that the instruction will\n      not be lost for users relying on a screen reader.\n\n      Message not passed to screen reader:\n\n      \"${message}\"\n    `) : void 0;\n  }, []);\n  return announce;\n}\n\nlet count$1 = 0;\nconst defaults = {\n  separator: '::'\n};\nfunction resetDeprecatedUniqueId() {\n  count$1 = 0;\n}\nfunction useDeprecatedUniqueId(prefix, options = defaults) {\n  return useMemo(() => `${prefix}${options.separator}${count$1++}`, [options.separator, prefix]);\n}\nfunction useUniqueId(prefix, options = defaults) {\n  const id = React.useId();\n  return useMemo(() => `${prefix}${options.separator}${id}`, [options.separator, prefix, id]);\n}\nvar useUniqueId$1 = 'useId' in React ? useUniqueId : useDeprecatedUniqueId;\n\nfunction getElementId({\n  contextId,\n  uniqueId\n}) {\n  return `rfd-hidden-text-${contextId}-${uniqueId}`;\n}\nfunction useHiddenTextElement({\n  contextId,\n  text\n}) {\n  const uniqueId = useUniqueId$1('hidden-text', {\n    separator: '-'\n  });\n  const id = useMemo(() => getElementId({\n    contextId,\n    uniqueId\n  }), [uniqueId, contextId]);\n  useEffect(function mount() {\n    const el = document.createElement('div');\n    el.id = id;\n    el.textContent = text;\n    el.style.display = 'none';\n    getBodyElement().appendChild(el);\n    return function unmount() {\n      const body = getBodyElement();\n      if (body.contains(el)) {\n        body.removeChild(el);\n      }\n    };\n  }, [id, text]);\n  return id;\n}\n\nvar AppContext = React.createContext(null);\n\nvar peerDependencies = {\n\treact: \"^16.8.5 || ^17.0.0 || ^18.0.0\",\n\t\"react-dom\": \"^16.8.5 || ^17.0.0 || ^18.0.0\"\n};\n\nconst semver = /(\\d+)\\.(\\d+)\\.(\\d+)/;\nconst getVersion = value => {\n  const result = semver.exec(value);\n  !(result != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Unable to parse React version ${value}`) : invariant(false) : void 0;\n  const major = Number(result[1]);\n  const minor = Number(result[2]);\n  const patch = Number(result[3]);\n  return {\n    major,\n    minor,\n    patch,\n    raw: value\n  };\n};\nconst isSatisfied = (expected, actual) => {\n  if (actual.major > expected.major) {\n    return true;\n  }\n  if (actual.major < expected.major) {\n    return false;\n  }\n  if (actual.minor > expected.minor) {\n    return true;\n  }\n  if (actual.minor < expected.minor) {\n    return false;\n  }\n  return actual.patch >= expected.patch;\n};\nvar checkReactVersion = ((peerDepValue, actualValue) => {\n  const peerDep = getVersion(peerDepValue);\n  const actual = getVersion(actualValue);\n  if (isSatisfied(peerDep, actual)) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    React version: [${actual.raw}]\n    does not satisfy expected peer dependency version: [${peerDep.raw}]\n\n    This can result in run time bugs, and even fatal crashes\n  `) : void 0;\n});\n\nconst suffix = `\n  We expect a html5 doctype: <!doctype html>\n  This is to ensure consistent browser layout and measurement\n\n  More information: https://github.com/hello-pangea/dnd/blob/main/docs/guides/doctype.md\n`;\nvar checkDoctype = (doc => {\n  const doctype = doc.doctype;\n  if (!doctype) {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      No <!doctype html> found.\n\n      ${suffix}\n    `) : void 0;\n    return;\n  }\n  if (doctype.name.toLowerCase() !== 'html') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Unexpected <!doctype> found: (${doctype.name})\n\n      ${suffix}\n    `) : void 0;\n  }\n  if (doctype.publicId !== '') {\n    process.env.NODE_ENV !== \"production\" ? warning(`\n      Unexpected <!doctype> publicId found: (${doctype.publicId})\n      A html5 doctype does not have a publicId\n\n      ${suffix}\n    `) : void 0;\n  }\n});\n\nfunction useDev(useHook) {\n  if (process.env.NODE_ENV !== 'production') {\n    useHook();\n  }\n}\n\nfunction useDevSetupWarning(fn, inputs) {\n  useDev(() => {\n    useEffect(() => {\n      try {\n        fn();\n      } catch (e) {\n        error(`\n          A setup problem was encountered.\n\n          > ${e.message}\n        `);\n      }\n    }, inputs);\n  });\n}\n\nfunction useStartupValidation() {\n  useDevSetupWarning(() => {\n    checkReactVersion(peerDependencies.react, React.version);\n    checkDoctype(document);\n  }, []);\n}\n\nfunction usePrevious(current) {\n  const ref = useRef(current);\n  useEffect(() => {\n    ref.current = current;\n  });\n  return ref;\n}\n\nfunction create() {\n  let lock = null;\n  function isClaimed() {\n    return Boolean(lock);\n  }\n  function isActive(value) {\n    return value === lock;\n  }\n  function claim(abandon) {\n    !!lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot claim lock as it is already claimed') : invariant(false) : void 0;\n    const newLock = {\n      abandon\n    };\n    lock = newLock;\n    return newLock;\n  }\n  function release() {\n    !lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot release lock when there is no lock') : invariant(false) : void 0;\n    lock = null;\n  }\n  function tryAbandon() {\n    if (lock) {\n      lock.abandon();\n      release();\n    }\n  }\n  return {\n    isClaimed,\n    isActive,\n    claim,\n    release,\n    tryAbandon\n  };\n}\n\nfunction isDragging(state) {\n  if (state.phase === 'IDLE' || state.phase === 'DROP_ANIMATING') {\n    return false;\n  }\n  return state.isDragging;\n}\n\nconst tab = 9;\nconst enter = 13;\nconst escape = 27;\nconst space = 32;\nconst pageUp = 33;\nconst pageDown = 34;\nconst end = 35;\nconst home = 36;\nconst arrowLeft = 37;\nconst arrowUp = 38;\nconst arrowRight = 39;\nconst arrowDown = 40;\n\nconst preventedKeys = {\n  [enter]: true,\n  [tab]: true\n};\nvar preventStandardKeyEvents = (event => {\n  if (preventedKeys[event.keyCode]) {\n    event.preventDefault();\n  }\n});\n\nconst supportedEventName = (() => {\n  const base = 'visibilitychange';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  const candidates = [base, `ms${base}`, `webkit${base}`, `moz${base}`, `o${base}`];\n  const supported = candidates.find(eventName => `on${eventName}` in document);\n  return supported || base;\n})();\nvar supportedPageVisibilityEventName = supportedEventName;\n\nconst primaryButton = 0;\nconst sloppyClickThreshold = 5;\nfunction isSloppyClickThresholdExceeded(original, current) {\n  return Math.abs(current.x - original.x) >= sloppyClickThreshold || Math.abs(current.y - original.y) >= sloppyClickThreshold;\n}\nconst idle$1 = {\n  type: 'IDLE'\n};\nfunction getCaptureBindings({\n  cancel,\n  completed,\n  getPhase,\n  setPhase\n}) {\n  return [{\n    eventName: 'mousemove',\n    fn: event => {\n      const {\n        button,\n        clientX,\n        clientY\n      } = event;\n      if (button !== primaryButton) {\n        return;\n      }\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      const phase = getPhase();\n      if (phase.type === 'DRAGGING') {\n        event.preventDefault();\n        phase.actions.move(point);\n        return;\n      }\n      !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot be IDLE') : invariant(false) : void 0;\n      const pending = phase.point;\n      if (!isSloppyClickThresholdExceeded(pending, point)) {\n        return;\n      }\n      event.preventDefault();\n      const actions = phase.actions.fluidLift(point);\n      setPhase({\n        type: 'DRAGGING',\n        actions\n      });\n    }\n  }, {\n    eventName: 'mouseup',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: event => {\n      if (getPhase().type === 'DRAGGING') {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type === 'PENDING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: () => {\n      if (getPhase().type === 'PENDING') {\n        cancel();\n      }\n    }\n  }, {\n    eventName: 'webkitmouseforcedown',\n    fn: event => {\n      const phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase') : invariant(false) : void 0;\n      if (phase.actions.shouldRespectForcePress()) {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedPageVisibilityEventName,\n    fn: cancel\n  }];\n}\nfunction useMouseSensor(api) {\n  const phaseRef = useRef(idle$1);\n  const unbindEventsRef = useRef(noop$2);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'mousedown',\n    fn: function onMouseDown(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.button !== primaryButton) {\n        return;\n      }\n      if (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const actions = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!actions) {\n        return;\n      }\n      event.preventDefault();\n      const point = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      unbindEventsRef.current();\n      startPendingDrag(actions, point);\n    }\n  }), [api]);\n  const preventForcePressBinding = useMemo(() => ({\n    eventName: 'webkitmouseforcewillbegin',\n    fn: event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      const id = api.findClosestDraggableId(event);\n      if (!id) {\n        return;\n      }\n      const options = api.findOptionsForDraggable(id);\n      if (!options) {\n        return;\n      }\n      if (options.shouldRespectForcePress) {\n        return;\n      }\n      if (!api.canGetLock(id)) {\n        return;\n      }\n      event.preventDefault();\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function listenForCapture() {\n    const options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [preventForcePressBinding, startCaptureBinding], options);\n  }, [preventForcePressBinding, startCaptureBinding]);\n  const stop = useCallback(() => {\n    const current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    phaseRef.current = idle$1;\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture]);\n  const cancel = useCallback(() => {\n    const phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  const bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    const bindings = getCaptureBindings({\n      cancel,\n      completed: stop,\n      getPhase: () => phaseRef.current,\n      setPhase: phase => {\n        phaseRef.current = phase;\n      }\n    });\n    unbindEventsRef.current = bindEvents(window, bindings, options);\n  }, [cancel, stop]);\n  const startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(phaseRef.current.type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant(false) : void 0;\n    phaseRef.current = {\n      type: 'PENDING',\n      point,\n      actions\n    };\n    bindCapturingEvents();\n  }, [bindCapturingEvents]);\n  useLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nfunction noop$1() {}\nconst scrollJumpKeys = {\n  [pageDown]: true,\n  [pageUp]: true,\n  [home]: true,\n  [end]: true\n};\nfunction getDraggingBindings(actions, stop) {\n  function cancel() {\n    stop();\n    actions.cancel();\n  }\n  function drop() {\n    stop();\n    actions.drop();\n  }\n  return [{\n    eventName: 'keydown',\n    fn: event => {\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n      if (event.keyCode === space) {\n        event.preventDefault();\n        drop();\n        return;\n      }\n      if (event.keyCode === arrowDown) {\n        event.preventDefault();\n        actions.moveDown();\n        return;\n      }\n      if (event.keyCode === arrowUp) {\n        event.preventDefault();\n        actions.moveUp();\n        return;\n      }\n      if (event.keyCode === arrowRight) {\n        event.preventDefault();\n        actions.moveRight();\n        return;\n      }\n      if (event.keyCode === arrowLeft) {\n        event.preventDefault();\n        actions.moveLeft();\n        return;\n      }\n      if (scrollJumpKeys[event.keyCode]) {\n        event.preventDefault();\n        return;\n      }\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: cancel\n  }, {\n    eventName: 'mouseup',\n    fn: cancel\n  }, {\n    eventName: 'click',\n    fn: cancel\n  }, {\n    eventName: 'touchstart',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'wheel',\n    fn: cancel,\n    options: {\n      passive: true\n    }\n  }, {\n    eventName: supportedPageVisibilityEventName,\n    fn: cancel\n  }];\n}\nfunction useKeyboardSensor(api) {\n  const unbindEventsRef = useRef(noop$1);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'keydown',\n    fn: function onKeyDown(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      if (event.keyCode !== space) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const preDrag = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!preDrag) {\n        return;\n      }\n      event.preventDefault();\n      let isCapturing = true;\n      const actions = preDrag.snapLift();\n      unbindEventsRef.current();\n      function stop() {\n        !isCapturing ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop capturing a keyboard drag when not capturing') : invariant(false) : void 0;\n        isCapturing = false;\n        unbindEventsRef.current();\n        listenForCapture();\n      }\n      unbindEventsRef.current = bindEvents(window, getDraggingBindings(actions, stop), {\n        capture: true,\n        passive: false\n      });\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function tryStartCapture() {\n    const options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  useLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nconst idle = {\n  type: 'IDLE'\n};\nconst timeForLongPress = 120;\nconst forcePressThreshold = 0.15;\nfunction getWindowBindings({\n  cancel,\n  getPhase\n}) {\n  return [{\n    eventName: 'orientationchange',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'contextmenu',\n    fn: event => {\n      event.preventDefault();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: event => {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      if (event.keyCode === escape) {\n        event.preventDefault();\n      }\n      cancel();\n    }\n  }, {\n    eventName: supportedPageVisibilityEventName,\n    fn: cancel\n  }];\n}\nfunction getHandleBindings({\n  cancel,\n  completed,\n  getPhase\n}) {\n  return [{\n    eventName: 'touchmove',\n    options: {\n      capture: false\n    },\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      phase.hasMoved = true;\n      const {\n        clientX,\n        clientY\n      } = event.touches[0];\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      event.preventDefault();\n      phase.actions.move(point);\n    }\n  }, {\n    eventName: 'touchend',\n    fn: event => {\n      const phase = getPhase();\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'touchcancel',\n    fn: event => {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n      cancel();\n    }\n  }, {\n    eventName: 'touchforcechange',\n    fn: event => {\n      const phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n      const touch = event.touches[0];\n      if (!touch) {\n        return;\n      }\n      const isForcePress = touch.force >= forcePressThreshold;\n      if (!isForcePress) {\n        return;\n      }\n      const shouldRespect = phase.actions.shouldRespectForcePress();\n      if (phase.type === 'PENDING') {\n        if (shouldRespect) {\n          cancel();\n        }\n        return;\n      }\n      if (shouldRespect) {\n        if (phase.hasMoved) {\n          event.preventDefault();\n          return;\n        }\n        cancel();\n        return;\n      }\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedPageVisibilityEventName,\n    fn: cancel\n  }];\n}\nfunction useTouchSensor(api) {\n  const phaseRef = useRef(idle);\n  const unbindEventsRef = useRef(noop$2);\n  const getPhase = useCallback(function getPhase() {\n    return phaseRef.current;\n  }, []);\n  const setPhase = useCallback(function setPhase(phase) {\n    phaseRef.current = phase;\n  }, []);\n  const startCaptureBinding = useMemo(() => ({\n    eventName: 'touchstart',\n    fn: function onTouchStart(event) {\n      if (event.defaultPrevented) {\n        return;\n      }\n      const draggableId = api.findClosestDraggableId(event);\n      if (!draggableId) {\n        return;\n      }\n      const actions = api.tryGetLock(draggableId, stop, {\n        sourceEvent: event\n      });\n      if (!actions) {\n        return;\n      }\n      const touch = event.touches[0];\n      const {\n        clientX,\n        clientY\n      } = touch;\n      const point = {\n        x: clientX,\n        y: clientY\n      };\n      unbindEventsRef.current();\n      startPendingDrag(actions, point);\n    }\n  }), [api]);\n  const listenForCapture = useCallback(function listenForCapture() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  const stop = useCallback(() => {\n    const current = phaseRef.current;\n    if (current.type === 'IDLE') {\n      return;\n    }\n    if (current.type === 'PENDING') {\n      clearTimeout(current.longPressTimerId);\n    }\n    setPhase(idle);\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture, setPhase]);\n  const cancel = useCallback(() => {\n    const phase = phaseRef.current;\n    stop();\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  const bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    const options = {\n      capture: true,\n      passive: false\n    };\n    const args = {\n      cancel,\n      completed: stop,\n      getPhase\n    };\n    const unbindTarget = bindEvents(window, getHandleBindings(args), options);\n    const unbindWindow = bindEvents(window, getWindowBindings(args), options);\n    unbindEventsRef.current = function unbindAll() {\n      unbindTarget();\n      unbindWindow();\n    };\n  }, [cancel, getPhase, stop]);\n  const startDragging = useCallback(function startDragging() {\n    const phase = getPhase();\n    !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot start dragging from phase ${phase.type}`) : invariant(false) : void 0;\n    const actions = phase.actions.fluidLift(phase.point);\n    setPhase({\n      type: 'DRAGGING',\n      actions,\n      hasMoved: false\n    });\n  }, [getPhase, setPhase]);\n  const startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(getPhase().type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant(false) : void 0;\n    const longPressTimerId = setTimeout(startDragging, timeForLongPress);\n    setPhase({\n      type: 'PENDING',\n      point,\n      actions,\n      longPressTimerId\n    });\n    bindCapturingEvents();\n  }, [bindCapturingEvents, getPhase, setPhase, startDragging]);\n  useLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n      const phase = getPhase();\n      if (phase.type === 'PENDING') {\n        clearTimeout(phase.longPressTimerId);\n        setPhase(idle);\n      }\n    };\n  }, [getPhase, listenForCapture, setPhase]);\n  useLayoutEffect(function webkitHack() {\n    const unbind = bindEvents(window, [{\n      eventName: 'touchmove',\n      fn: () => {},\n      options: {\n        capture: false,\n        passive: false\n      }\n    }]);\n    return unbind;\n  }, []);\n}\n\nfunction useValidateSensorHooks(sensorHooks) {\n  useDev(() => {\n    const previousRef = usePrevious(sensorHooks);\n    useDevSetupWarning(() => {\n      !(previousRef.current.length === sensorHooks.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot change the amount of sensor hooks after mounting') : invariant(false) : void 0;\n    });\n  });\n}\n\nconst interactiveTagNames = ['input', 'button', 'textarea', 'select', 'option', 'optgroup', 'video', 'audio'];\nfunction isAnInteractiveElement(parent, current) {\n  if (current == null) {\n    return false;\n  }\n  const hasAnInteractiveTag = interactiveTagNames.includes(current.tagName.toLowerCase());\n  if (hasAnInteractiveTag) {\n    return true;\n  }\n  const attribute = current.getAttribute('contenteditable');\n  if (attribute === 'true' || attribute === '') {\n    return true;\n  }\n  if (current === parent) {\n    return false;\n  }\n  return isAnInteractiveElement(parent, current.parentElement);\n}\nfunction isEventInInteractiveElement(draggable, event) {\n  const target = event.target;\n  if (!isHtmlElement(target)) {\n    return false;\n  }\n  return isAnInteractiveElement(draggable, target);\n}\n\nvar getBorderBoxCenterPosition = (el => getRect(el.getBoundingClientRect()).center);\n\nfunction isElement(el) {\n  return el instanceof getWindowFromEl(el).Element;\n}\n\nconst supportedMatchesName = (() => {\n  const base = 'matches';\n  if (typeof document === 'undefined') {\n    return base;\n  }\n  const candidates = [base, 'msMatchesSelector', 'webkitMatchesSelector'];\n  const value = candidates.find(name => name in Element.prototype);\n  return value || base;\n})();\nfunction closestPonyfill(el, selector) {\n  if (el == null) {\n    return null;\n  }\n  if (el[supportedMatchesName](selector)) {\n    return el;\n  }\n  return closestPonyfill(el.parentElement, selector);\n}\nfunction closest(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n  }\n  return closestPonyfill(el, selector);\n}\n\nfunction getSelector(contextId) {\n  return `[${dragHandle.contextId}=\"${contextId}\"]`;\n}\nfunction findClosestDragHandleFromEvent(contextId, event) {\n  const target = event.target;\n  if (!isElement(target)) {\n    process.env.NODE_ENV !== \"production\" ? warning('event.target must be a Element') : void 0;\n    return null;\n  }\n  const selector = getSelector(contextId);\n  const handle = closest(target, selector);\n  if (!handle) {\n    return null;\n  }\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle must be a HTMLElement') : void 0;\n    return null;\n  }\n  return handle;\n}\nfunction tryGetClosestDraggableIdFromEvent(contextId, event) {\n  const handle = findClosestDragHandleFromEvent(contextId, event);\n  if (!handle) {\n    return null;\n  }\n  return handle.getAttribute(dragHandle.draggableId);\n}\n\nfunction findDraggable(contextId, draggableId) {\n  const selector = `[${draggable.contextId}=\"${contextId}\"]`;\n  const possible = querySelectorAll(document, selector);\n  const draggable$1 = possible.find(el => {\n    return el.getAttribute(draggable.id) === draggableId;\n  });\n  if (!draggable$1) {\n    return null;\n  }\n  if (!isHtmlElement(draggable$1)) {\n    process.env.NODE_ENV !== \"production\" ? warning('Draggable element is not a HTMLElement') : void 0;\n    return null;\n  }\n  return draggable$1;\n}\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction isActive({\n  expected,\n  phase,\n  isLockActive,\n  shouldWarn\n}) {\n  if (!isLockActive()) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Cannot perform action.\n        The sensor no longer has an action lock.\n\n        Tips:\n\n        - Throw away your action handlers when forceStop() is called\n        - Check actions.isActive() if you really need to\n      `) : void 0;\n    }\n    return false;\n  }\n  if (expected !== phase) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(`\n        Cannot perform action.\n        The actions you used belong to an outdated phase\n\n        Current phase: ${expected}\n        You called an action from outdated phase: ${phase}\n\n        Tips:\n\n        - Do not use preDragActions actions after calling preDragActions.lift()\n      `) : void 0;\n    }\n    return false;\n  }\n  return true;\n}\nfunction canStart({\n  lockAPI,\n  store,\n  registry,\n  draggableId\n}) {\n  if (lockAPI.isClaimed()) {\n    return false;\n  }\n  const entry = registry.draggable.findById(draggableId);\n  if (!entry) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find draggable with id: ${draggableId}`) : void 0;\n    return false;\n  }\n  if (!entry.options.isEnabled) {\n    return false;\n  }\n  if (!canStartDrag(store.getState(), draggableId)) {\n    return false;\n  }\n  return true;\n}\nfunction tryStart({\n  lockAPI,\n  contextId,\n  store,\n  registry,\n  draggableId,\n  forceSensorStop,\n  sourceEvent\n}) {\n  const shouldStart = canStart({\n    lockAPI,\n    store,\n    registry,\n    draggableId\n  });\n  if (!shouldStart) {\n    return null;\n  }\n  const entry = registry.draggable.getById(draggableId);\n  const el = findDraggable(contextId, entry.descriptor.id);\n  if (!el) {\n    process.env.NODE_ENV !== \"production\" ? warning(`Unable to find draggable element with id: ${draggableId}`) : void 0;\n    return null;\n  }\n  if (sourceEvent && !entry.options.canDragInteractiveElements && isEventInInteractiveElement(el, sourceEvent)) {\n    return null;\n  }\n  const lock = lockAPI.claim(forceSensorStop || noop$2);\n  let phase = 'PRE_DRAG';\n  function getShouldRespectForcePress() {\n    return entry.options.shouldRespectForcePress;\n  }\n  function isLockActive() {\n    return lockAPI.isActive(lock);\n  }\n  function tryDispatch(expected, getAction) {\n    if (isActive({\n      expected,\n      phase,\n      isLockActive,\n      shouldWarn: true\n    })) {\n      store.dispatch(getAction());\n    }\n  }\n  const tryDispatchWhenDragging = tryDispatch.bind(null, 'DRAGGING');\n  function lift(args) {\n    function completed() {\n      lockAPI.release();\n      phase = 'COMPLETED';\n    }\n    if (phase !== 'PRE_DRAG') {\n      completed();\n      process.env.NODE_ENV !== \"production\" ? invariant(false, `Cannot lift in phase ${phase}`) : invariant(false) ;\n    }\n    store.dispatch(lift$1(args.liftActionArgs));\n    phase = 'DRAGGING';\n    function finish(reason, options = {\n      shouldBlockNextClick: false\n    }) {\n      args.cleanup();\n      if (options.shouldBlockNextClick) {\n        const unbind = bindEvents(window, [{\n          eventName: 'click',\n          fn: preventDefault,\n          options: {\n            once: true,\n            passive: false,\n            capture: true\n          }\n        }]);\n        setTimeout(unbind);\n      }\n      completed();\n      store.dispatch(drop$1({\n        reason\n      }));\n    }\n    return {\n      isActive: () => isActive({\n        expected: 'DRAGGING',\n        phase,\n        isLockActive,\n        shouldWarn: false\n      }),\n      shouldRespectForcePress: getShouldRespectForcePress,\n      drop: options => finish('DROP', options),\n      cancel: options => finish('CANCEL', options),\n      ...args.actions\n    };\n  }\n  function fluidLift(clientSelection) {\n    const move$1 = rafSchd(client => {\n      tryDispatchWhenDragging(() => move({\n        client\n      }));\n    });\n    const api = lift({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection,\n        movementMode: 'FLUID'\n      },\n      cleanup: () => move$1.cancel(),\n      actions: {\n        move: move$1\n      }\n    });\n    return {\n      ...api,\n      move: move$1\n    };\n  }\n  function snapLift() {\n    const actions = {\n      moveUp: () => tryDispatchWhenDragging(moveUp),\n      moveRight: () => tryDispatchWhenDragging(moveRight),\n      moveDown: () => tryDispatchWhenDragging(moveDown),\n      moveLeft: () => tryDispatchWhenDragging(moveLeft)\n    };\n    return lift({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: getBorderBoxCenterPosition(el),\n        movementMode: 'SNAP'\n      },\n      cleanup: noop$2,\n      actions\n    });\n  }\n  function abortPreDrag() {\n    const shouldRelease = isActive({\n      expected: 'PRE_DRAG',\n      phase,\n      isLockActive,\n      shouldWarn: true\n    });\n    if (shouldRelease) {\n      lockAPI.release();\n    }\n  }\n  const preDrag = {\n    isActive: () => isActive({\n      expected: 'PRE_DRAG',\n      phase,\n      isLockActive,\n      shouldWarn: false\n    }),\n    shouldRespectForcePress: getShouldRespectForcePress,\n    fluidLift,\n    snapLift,\n    abort: abortPreDrag\n  };\n  return preDrag;\n}\nconst defaultSensors = [useMouseSensor, useKeyboardSensor, useTouchSensor];\nfunction useSensorMarshal({\n  contextId,\n  store,\n  registry,\n  customSensors,\n  enableDefaultSensors\n}) {\n  const useSensors = [...(enableDefaultSensors ? defaultSensors : []), ...(customSensors || [])];\n  const lockAPI = useState(() => create())[0];\n  const tryAbandonLock = useCallback(function tryAbandonLock(previous, current) {\n    if (isDragging(previous) && !isDragging(current)) {\n      lockAPI.tryAbandon();\n    }\n  }, [lockAPI]);\n  useLayoutEffect(function listenToStore() {\n    let previous = store.getState();\n    const unsubscribe = store.subscribe(() => {\n      const current = store.getState();\n      tryAbandonLock(previous, current);\n      previous = current;\n    });\n    return unsubscribe;\n  }, [lockAPI, store, tryAbandonLock]);\n  useLayoutEffect(() => {\n    return lockAPI.tryAbandon;\n  }, [lockAPI.tryAbandon]);\n  const canGetLock = useCallback(draggableId => {\n    return canStart({\n      lockAPI,\n      registry,\n      store,\n      draggableId\n    });\n  }, [lockAPI, registry, store]);\n  const tryGetLock = useCallback((draggableId, forceStop, options) => tryStart({\n    lockAPI,\n    registry,\n    contextId,\n    store,\n    draggableId,\n    forceSensorStop: forceStop || null,\n    sourceEvent: options && options.sourceEvent ? options.sourceEvent : null\n  }), [contextId, lockAPI, registry, store]);\n  const findClosestDraggableId = useCallback(event => tryGetClosestDraggableIdFromEvent(contextId, event), [contextId]);\n  const findOptionsForDraggable = useCallback(id => {\n    const entry = registry.draggable.findById(id);\n    return entry ? entry.options : null;\n  }, [registry.draggable]);\n  const tryReleaseLock = useCallback(function tryReleaseLock() {\n    if (!lockAPI.isClaimed()) {\n      return;\n    }\n    lockAPI.tryAbandon();\n    if (store.getState().phase !== 'IDLE') {\n      store.dispatch(flush());\n    }\n  }, [lockAPI, store]);\n  const isLockClaimed = useCallback(() => lockAPI.isClaimed(), [lockAPI]);\n  const api = useMemo(() => ({\n    canGetLock,\n    tryGetLock,\n    findClosestDraggableId,\n    findOptionsForDraggable,\n    tryReleaseLock,\n    isLockClaimed\n  }), [canGetLock, tryGetLock, findClosestDraggableId, findOptionsForDraggable, tryReleaseLock, isLockClaimed]);\n  useValidateSensorHooks(useSensors);\n  for (let i = 0; i < useSensors.length; i++) {\n    useSensors[i](api);\n  }\n}\n\nconst createResponders = props => ({\n  onBeforeCapture: t => {\n    const onBeforeCapureCallback = () => {\n      if (props.onBeforeCapture) {\n        props.onBeforeCapture(t);\n      }\n    };\n    if (React.version.startsWith('16') || React.version.startsWith('17')) {\n      onBeforeCapureCallback();\n    } else {\n      flushSync(onBeforeCapureCallback);\n    }\n  },\n  onBeforeDragStart: props.onBeforeDragStart,\n  onDragStart: props.onDragStart,\n  onDragEnd: props.onDragEnd,\n  onDragUpdate: props.onDragUpdate\n});\nconst createAutoScrollerOptions = props => ({\n  ...defaultAutoScrollerOptions,\n  ...props.autoScrollerOptions,\n  durationDampening: {\n    ...defaultAutoScrollerOptions.durationDampening,\n    ...props.autoScrollerOptions\n  }\n});\nfunction getStore(lazyRef) {\n  !lazyRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find store from lazy ref') : invariant(false) : void 0;\n  return lazyRef.current;\n}\nfunction App(props) {\n  const {\n    contextId,\n    setCallbacks,\n    sensors,\n    nonce,\n    dragHandleUsageInstructions\n  } = props;\n  const lazyStoreRef = useRef(null);\n  useStartupValidation();\n  const lastPropsRef = usePrevious(props);\n  const getResponders = useCallback(() => {\n    return createResponders(lastPropsRef.current);\n  }, [lastPropsRef]);\n  const getAutoScrollerOptions = useCallback(() => {\n    return createAutoScrollerOptions(lastPropsRef.current);\n  }, [lastPropsRef]);\n  const announce = useAnnouncer(contextId);\n  const dragHandleUsageInstructionsId = useHiddenTextElement({\n    contextId,\n    text: dragHandleUsageInstructions\n  });\n  const styleMarshal = useStyleMarshal(contextId, nonce);\n  const lazyDispatch = useCallback(action => {\n    getStore(lazyStoreRef).dispatch(action);\n  }, []);\n  const marshalCallbacks = useMemo(() => bindActionCreators({\n    publishWhileDragging,\n    updateDroppableScroll,\n    updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled,\n    collectionStarting\n  }, lazyDispatch), [lazyDispatch]);\n  const registry = useRegistry();\n  const dimensionMarshal = useMemo(() => {\n    return createDimensionMarshal(registry, marshalCallbacks);\n  }, [registry, marshalCallbacks]);\n  const autoScroller = useMemo(() => createAutoScroller({\n    scrollWindow,\n    scrollDroppable: dimensionMarshal.scrollDroppable,\n    getAutoScrollerOptions,\n    ...bindActionCreators({\n      move\n    }, lazyDispatch)\n  }), [dimensionMarshal.scrollDroppable, lazyDispatch, getAutoScrollerOptions]);\n  const focusMarshal = useFocusMarshal(contextId);\n  const store = useMemo(() => createStore({\n    announce,\n    autoScroller,\n    dimensionMarshal,\n    focusMarshal,\n    getResponders,\n    styleMarshal\n  }), [announce, autoScroller, dimensionMarshal, focusMarshal, getResponders, styleMarshal]);\n  if (process.env.NODE_ENV !== 'production') {\n    if (lazyStoreRef.current && lazyStoreRef.current !== store) {\n      process.env.NODE_ENV !== \"production\" ? warning('unexpected store change') : void 0;\n    }\n  }\n  lazyStoreRef.current = store;\n  const tryResetStore = useCallback(() => {\n    const current = getStore(lazyStoreRef);\n    const state = current.getState();\n    if (state.phase !== 'IDLE') {\n      current.dispatch(flush());\n    }\n  }, []);\n  const isDragging = useCallback(() => {\n    const state = getStore(lazyStoreRef).getState();\n    if (state.phase === 'DROP_ANIMATING') {\n      return true;\n    }\n    if (state.phase === 'IDLE') {\n      return false;\n    }\n    return state.isDragging;\n  }, []);\n  const appCallbacks = useMemo(() => ({\n    isDragging,\n    tryAbort: tryResetStore\n  }), [isDragging, tryResetStore]);\n  setCallbacks(appCallbacks);\n  const getCanLift = useCallback(id => canStartDrag(getStore(lazyStoreRef).getState(), id), []);\n  const getIsMovementAllowed = useCallback(() => isMovementAllowed(getStore(lazyStoreRef).getState()), []);\n  const appContext = useMemo(() => ({\n    marshal: dimensionMarshal,\n    focus: focusMarshal,\n    contextId,\n    canLift: getCanLift,\n    isMovementAllowed: getIsMovementAllowed,\n    dragHandleUsageInstructionsId,\n    registry\n  }), [contextId, dimensionMarshal, dragHandleUsageInstructionsId, focusMarshal, getCanLift, getIsMovementAllowed, registry]);\n  useSensorMarshal({\n    contextId,\n    store,\n    registry,\n    customSensors: sensors || null,\n    enableDefaultSensors: props.enableDefaultSensors !== false\n  });\n  useEffect(() => {\n    return tryResetStore;\n  }, [tryResetStore]);\n  return React.createElement(AppContext.Provider, {\n    value: appContext\n  }, React.createElement(Provider, {\n    context: StoreContext,\n    store: store\n  }, props.children));\n}\n\nlet count = 0;\nfunction resetDeprecatedUniqueContextId() {\n  count = 0;\n}\nfunction useDeprecatedUniqueContextId() {\n  return useMemo(() => `${count++}`, []);\n}\nfunction useUniqueContextId() {\n  return React.useId();\n}\nvar useUniqueContextId$1 = 'useId' in React ? useUniqueContextId : useDeprecatedUniqueContextId;\n\nfunction resetServerContext() {\n  if ('useId' in React) {\n    process.env.NODE_ENV !== \"production\" ? warning(`It is not necessary to call resetServerContext when using React 18+`) : void 0;\n    return;\n  }\n  resetDeprecatedUniqueContextId();\n  resetDeprecatedUniqueId();\n}\nfunction DragDropContext(props) {\n  const contextId = useUniqueContextId$1();\n  const dragHandleUsageInstructions = props.dragHandleUsageInstructions || preset$1.dragHandleUsageInstructions;\n  return React.createElement(ErrorBoundary, null, setCallbacks => React.createElement(App, {\n    nonce: props.nonce,\n    contextId: contextId,\n    setCallbacks: setCallbacks,\n    dragHandleUsageInstructions: dragHandleUsageInstructions,\n    enableDefaultSensors: props.enableDefaultSensors,\n    sensors: props.sensors,\n    onBeforeCapture: props.onBeforeCapture,\n    onBeforeDragStart: props.onBeforeDragStart,\n    onDragStart: props.onDragStart,\n    onDragUpdate: props.onDragUpdate,\n    onDragEnd: props.onDragEnd,\n    autoScrollerOptions: props.autoScrollerOptions\n  }, props.children));\n}\n\nconst zIndexOptions = {\n  dragging: 5000,\n  dropAnimating: 4500\n};\nconst getDraggingTransition = (shouldAnimateDragMovement, dropping) => {\n  if (dropping) {\n    return transitions.drop(dropping.duration);\n  }\n  if (shouldAnimateDragMovement) {\n    return transitions.snap;\n  }\n  return transitions.fluid;\n};\nconst getDraggingOpacity = (isCombining, isDropAnimating) => {\n  if (!isCombining) {\n    return undefined;\n  }\n  return isDropAnimating ? combine.opacity.drop : combine.opacity.combining;\n};\nconst getShouldDraggingAnimate = dragging => {\n  if (dragging.forceShouldAnimate != null) {\n    return dragging.forceShouldAnimate;\n  }\n  return dragging.mode === 'SNAP';\n};\nfunction getDraggingStyle(dragging) {\n  const dimension = dragging.dimension;\n  const box = dimension.client;\n  const {\n    offset,\n    combineWith,\n    dropping\n  } = dragging;\n  const isCombining = Boolean(combineWith);\n  const shouldAnimate = getShouldDraggingAnimate(dragging);\n  const isDropAnimating = Boolean(dropping);\n  const transform = isDropAnimating ? transforms.drop(offset, isCombining) : transforms.moveTo(offset);\n  const style = {\n    position: 'fixed',\n    top: box.marginBox.top,\n    left: box.marginBox.left,\n    boxSizing: 'border-box',\n    width: box.borderBox.width,\n    height: box.borderBox.height,\n    transition: getDraggingTransition(shouldAnimate, dropping),\n    transform,\n    opacity: getDraggingOpacity(isCombining, isDropAnimating),\n    zIndex: isDropAnimating ? zIndexOptions.dropAnimating : zIndexOptions.dragging,\n    pointerEvents: 'none'\n  };\n  return style;\n}\nfunction getSecondaryStyle(secondary) {\n  return {\n    transform: transforms.moveTo(secondary.offset),\n    transition: secondary.shouldAnimateDisplacement ? undefined : 'none'\n  };\n}\nfunction getStyle$1(mapped) {\n  return mapped.type === 'DRAGGING' ? getDraggingStyle(mapped) : getSecondaryStyle(mapped);\n}\n\nfunction getDimension$1(descriptor, el, windowScroll = origin) {\n  const computedStyles = window.getComputedStyle(el);\n  const borderBox = el.getBoundingClientRect();\n  const client = calculateBox(borderBox, computedStyles);\n  const page = withScroll(client, windowScroll);\n  const placeholder = {\n    client,\n    tagName: el.tagName.toLowerCase(),\n    display: computedStyles.display\n  };\n  const displaceBy = {\n    x: client.marginBox.width,\n    y: client.marginBox.height\n  };\n  const dimension = {\n    descriptor,\n    placeholder,\n    displaceBy,\n    client,\n    page\n  };\n  return dimension;\n}\n\nfunction useDraggablePublisher(args) {\n  const uniqueId = useUniqueId$1('draggable');\n  const {\n    descriptor,\n    registry,\n    getDraggableRef,\n    canDragInteractiveElements,\n    shouldRespectForcePress,\n    isEnabled\n  } = args;\n  const options = useMemo(() => ({\n    canDragInteractiveElements,\n    shouldRespectForcePress,\n    isEnabled\n  }), [canDragInteractiveElements, isEnabled, shouldRespectForcePress]);\n  const getDimension = useCallback(windowScroll => {\n    const el = getDraggableRef();\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get dimension when no ref is set') : invariant(false) : void 0;\n    return getDimension$1(descriptor, el, windowScroll);\n  }, [descriptor, getDraggableRef]);\n  const entry = useMemo(() => ({\n    uniqueId,\n    descriptor,\n    options,\n    getDimension\n  }), [descriptor, getDimension, options, uniqueId]);\n  const publishedRef = useRef(entry);\n  const isFirstPublishRef = useRef(true);\n  useLayoutEffect(() => {\n    registry.draggable.register(publishedRef.current);\n    return () => registry.draggable.unregister(publishedRef.current);\n  }, [registry.draggable]);\n  useLayoutEffect(() => {\n    if (isFirstPublishRef.current) {\n      isFirstPublishRef.current = false;\n      return;\n    }\n    const last = publishedRef.current;\n    publishedRef.current = entry;\n    registry.draggable.update(entry, last);\n  }, [entry, registry.draggable]);\n}\n\nvar DroppableContext = React.createContext(null);\n\nfunction checkIsValidInnerRef(el) {\n  !(el && isHtmlElement(el)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `\n    provided.innerRef has not been provided with a HTMLElement.\n\n    You can find a guide on using the innerRef callback functions at:\n    https://github.com/hello-pangea/dnd/blob/main/docs/guides/using-inner-ref.md\n  `) : invariant(false) : void 0;\n}\n\nfunction useValidation$1(props, contextId, getRef) {\n  useDevSetupWarning(() => {\n    function prefix(id) {\n      return `Draggable[id: ${id}]: `;\n    }\n    const id = props.draggableId;\n    !id ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable requires a draggableId') : invariant(false) : void 0;\n    !(typeof id === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `Draggable requires a [string] draggableId.\n      Provided: [type: ${typeof id}] (value: ${id})`) : invariant(false) : void 0;\n    !Number.isInteger(props.index) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${prefix(id)} requires an integer index prop`) : invariant(false) : void 0;\n    if (props.mapped.type === 'DRAGGING') {\n      return;\n    }\n    checkIsValidInnerRef(getRef());\n    if (props.isEnabled) {\n      !findDragHandle(contextId, id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${prefix(id)} Unable to find drag handle`) : invariant(false) : void 0;\n    }\n  });\n}\nfunction useClonePropValidation(isClone) {\n  useDev(() => {\n    const initialRef = useRef(isClone);\n    useDevSetupWarning(() => {\n      !(isClone === initialRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable isClone prop value changed during component life') : invariant(false) : void 0;\n    }, [isClone]);\n  });\n}\n\nfunction useRequiredContext(Context) {\n  const result = useContext(Context);\n  !result ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find required context') : invariant(false) : void 0;\n  return result;\n}\n\nfunction preventHtml5Dnd(event) {\n  event.preventDefault();\n}\nconst Draggable = props => {\n  const ref = useRef(null);\n  const setRef = useCallback((el = null) => {\n    ref.current = el;\n  }, []);\n  const getRef = useCallback(() => ref.current, []);\n  const {\n    contextId,\n    dragHandleUsageInstructionsId,\n    registry\n  } = useRequiredContext(AppContext);\n  const {\n    type,\n    droppableId\n  } = useRequiredContext(DroppableContext);\n  const descriptor = useMemo(() => ({\n    id: props.draggableId,\n    index: props.index,\n    type,\n    droppableId\n  }), [props.draggableId, props.index, type, droppableId]);\n  const {\n    children,\n    draggableId,\n    isEnabled,\n    shouldRespectForcePress,\n    canDragInteractiveElements,\n    isClone,\n    mapped,\n    dropAnimationFinished: dropAnimationFinishedAction\n  } = props;\n  useValidation$1(props, contextId, getRef);\n  useClonePropValidation(isClone);\n  if (!isClone) {\n    const forPublisher = useMemo(() => ({\n      descriptor,\n      registry,\n      getDraggableRef: getRef,\n      canDragInteractiveElements,\n      shouldRespectForcePress,\n      isEnabled\n    }), [descriptor, registry, getRef, canDragInteractiveElements, shouldRespectForcePress, isEnabled]);\n    useDraggablePublisher(forPublisher);\n  }\n  const dragHandleProps = useMemo(() => isEnabled ? {\n    tabIndex: 0,\n    role: 'button',\n    'aria-describedby': dragHandleUsageInstructionsId,\n    'data-rfd-drag-handle-draggable-id': draggableId,\n    'data-rfd-drag-handle-context-id': contextId,\n    draggable: false,\n    onDragStart: preventHtml5Dnd\n  } : null, [contextId, dragHandleUsageInstructionsId, draggableId, isEnabled]);\n  const onMoveEnd = useCallback(event => {\n    if (mapped.type !== 'DRAGGING') {\n      return;\n    }\n    if (!mapped.dropping) {\n      return;\n    }\n    if (event.propertyName !== 'transform') {\n      return;\n    }\n    if (React.version.startsWith('16') || React.version.startsWith('17')) {\n      dropAnimationFinishedAction();\n    } else {\n      flushSync(dropAnimationFinishedAction);\n    }\n  }, [dropAnimationFinishedAction, mapped]);\n  const provided = useMemo(() => {\n    const style = getStyle$1(mapped);\n    const onTransitionEnd = mapped.type === 'DRAGGING' && mapped.dropping ? onMoveEnd : undefined;\n    const result = {\n      innerRef: setRef,\n      draggableProps: {\n        'data-rfd-draggable-context-id': contextId,\n        'data-rfd-draggable-id': draggableId,\n        style,\n        onTransitionEnd\n      },\n      dragHandleProps\n    };\n    return result;\n  }, [contextId, dragHandleProps, draggableId, mapped, onMoveEnd, setRef]);\n  const rubric = useMemo(() => ({\n    draggableId: descriptor.id,\n    type: descriptor.type,\n    source: {\n      index: descriptor.index,\n      droppableId: descriptor.droppableId\n    }\n  }), [descriptor.droppableId, descriptor.id, descriptor.index, descriptor.type]);\n  return React.createElement(React.Fragment, null, children(provided, mapped.snapshot, rubric));\n};\nvar Draggable$1 = Draggable;\n\nvar isStrictEqual = ((a, b) => a === b);\n\nvar whatIsDraggedOverFromResult = (result => {\n  const {\n    combine,\n    destination\n  } = result;\n  if (destination) {\n    return destination.droppableId;\n  }\n  if (combine) {\n    return combine.droppableId;\n  }\n  return null;\n});\n\nconst getCombineWithFromResult = result => {\n  return result.combine ? result.combine.draggableId : null;\n};\nconst getCombineWithFromImpact = impact => {\n  return impact.at && impact.at.type === 'COMBINE' ? impact.at.combine.draggableId : null;\n};\nfunction getDraggableSelector() {\n  const memoizedOffset = memoizeOne((x, y) => ({\n    x,\n    y\n  }));\n  const getMemoizedSnapshot = memoizeOne((mode, isClone, draggingOver = null, combineWith = null, dropping = null) => ({\n    isDragging: true,\n    isClone,\n    isDropAnimating: Boolean(dropping),\n    dropAnimation: dropping,\n    mode,\n    draggingOver,\n    combineWith,\n    combineTargetFor: null\n  }));\n  const getMemoizedProps = memoizeOne((offset, mode, dimension, isClone, draggingOver = null, combineWith = null, forceShouldAnimate = null) => ({\n    mapped: {\n      type: 'DRAGGING',\n      dropping: null,\n      draggingOver,\n      combineWith,\n      mode,\n      offset,\n      dimension,\n      forceShouldAnimate,\n      snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, null)\n    }\n  }));\n  const selector = (state, ownProps) => {\n    if (isDragging(state)) {\n      if (state.critical.draggable.id !== ownProps.draggableId) {\n        return null;\n      }\n      const offset = state.current.client.offset;\n      const dimension = state.dimensions.draggables[ownProps.draggableId];\n      const draggingOver = whatIsDraggedOver(state.impact);\n      const combineWith = getCombineWithFromImpact(state.impact);\n      const forceShouldAnimate = state.forceShouldAnimate;\n      return getMemoizedProps(memoizedOffset(offset.x, offset.y), state.movementMode, dimension, ownProps.isClone, draggingOver, combineWith, forceShouldAnimate);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (completed.result.draggableId !== ownProps.draggableId) {\n        return null;\n      }\n      const isClone = ownProps.isClone;\n      const dimension = state.dimensions.draggables[ownProps.draggableId];\n      const result = completed.result;\n      const mode = result.mode;\n      const draggingOver = whatIsDraggedOverFromResult(result);\n      const combineWith = getCombineWithFromResult(result);\n      const duration = state.dropDuration;\n      const dropping = {\n        duration,\n        curve: curves.drop,\n        moveTo: state.newHomeClientOffset,\n        opacity: combineWith ? combine.opacity.drop : null,\n        scale: combineWith ? combine.scale.drop : null\n      };\n      return {\n        mapped: {\n          type: 'DRAGGING',\n          offset: state.newHomeClientOffset,\n          dimension,\n          dropping,\n          draggingOver,\n          combineWith,\n          mode,\n          forceShouldAnimate: null,\n          snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, dropping)\n        }\n      };\n    }\n    return null;\n  };\n  return selector;\n}\nfunction getSecondarySnapshot(combineTargetFor = null) {\n  return {\n    isDragging: false,\n    isDropAnimating: false,\n    isClone: false,\n    dropAnimation: null,\n    mode: null,\n    draggingOver: null,\n    combineTargetFor,\n    combineWith: null\n  };\n}\nconst atRest = {\n  mapped: {\n    type: 'SECONDARY',\n    offset: origin,\n    combineTargetFor: null,\n    shouldAnimateDisplacement: true,\n    snapshot: getSecondarySnapshot(null)\n  }\n};\nfunction getSecondarySelector() {\n  const memoizedOffset = memoizeOne((x, y) => ({\n    x,\n    y\n  }));\n  const getMemoizedSnapshot = memoizeOne(getSecondarySnapshot);\n  const getMemoizedProps = memoizeOne((offset, combineTargetFor = null, shouldAnimateDisplacement) => ({\n    mapped: {\n      type: 'SECONDARY',\n      offset,\n      combineTargetFor,\n      shouldAnimateDisplacement,\n      snapshot: getMemoizedSnapshot(combineTargetFor)\n    }\n  }));\n  const getFallback = combineTargetFor => {\n    return combineTargetFor ? getMemoizedProps(origin, combineTargetFor, true) : null;\n  };\n  const getProps = (ownId, draggingId, impact, afterCritical) => {\n    const visualDisplacement = impact.displaced.visible[ownId];\n    const isAfterCriticalInVirtualList = Boolean(afterCritical.inVirtualList && afterCritical.effected[ownId]);\n    const combine = tryGetCombine(impact);\n    const combineTargetFor = combine && combine.draggableId === ownId ? draggingId : null;\n    if (!visualDisplacement) {\n      if (!isAfterCriticalInVirtualList) {\n        return getFallback(combineTargetFor);\n      }\n      if (impact.displaced.invisible[ownId]) {\n        return null;\n      }\n      const change = negate(afterCritical.displacedBy.point);\n      const offset = memoizedOffset(change.x, change.y);\n      return getMemoizedProps(offset, combineTargetFor, true);\n    }\n    if (isAfterCriticalInVirtualList) {\n      return getFallback(combineTargetFor);\n    }\n    const displaceBy = impact.displacedBy.point;\n    const offset = memoizedOffset(displaceBy.x, displaceBy.y);\n    return getMemoizedProps(offset, combineTargetFor, visualDisplacement.shouldAnimate);\n  };\n  const selector = (state, ownProps) => {\n    if (isDragging(state)) {\n      if (state.critical.draggable.id === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, state.critical.draggable.id, state.impact, state.afterCritical);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (completed.result.draggableId === ownProps.draggableId) {\n        return null;\n      }\n      return getProps(ownProps.draggableId, completed.result.draggableId, completed.impact, completed.afterCritical);\n    }\n    return null;\n  };\n  return selector;\n}\nconst makeMapStateToProps$1 = () => {\n  const draggingSelector = getDraggableSelector();\n  const secondarySelector = getSecondarySelector();\n  const selector = (state, ownProps) => draggingSelector(state, ownProps) || secondarySelector(state, ownProps) || atRest;\n  return selector;\n};\nconst mapDispatchToProps$1 = {\n  dropAnimationFinished: dropAnimationFinished\n};\nconst ConnectedDraggable = connect(makeMapStateToProps$1, mapDispatchToProps$1, null, {\n  context: StoreContext,\n  areStatePropsEqual: isStrictEqual\n})(Draggable$1);\nvar ConnectedDraggable$1 = ConnectedDraggable;\n\nfunction PrivateDraggable(props) {\n  const droppableContext = useRequiredContext(DroppableContext);\n  const isUsingCloneFor = droppableContext.isUsingCloneFor;\n  if (isUsingCloneFor === props.draggableId && !props.isClone) {\n    return null;\n  }\n  return React.createElement(ConnectedDraggable$1, props);\n}\nfunction PublicDraggable(props) {\n  const isEnabled = typeof props.isDragDisabled === 'boolean' ? !props.isDragDisabled : true;\n  const canDragInteractiveElements = Boolean(props.disableInteractiveElementBlocking);\n  const shouldRespectForcePress = Boolean(props.shouldRespectForcePress);\n  return React.createElement(PrivateDraggable, _extends({}, props, {\n    isClone: false,\n    isEnabled: isEnabled,\n    canDragInteractiveElements: canDragInteractiveElements,\n    shouldRespectForcePress: shouldRespectForcePress\n  }));\n}\n\nconst isEqual = base => value => base === value;\nconst isScroll = isEqual('scroll');\nconst isAuto = isEqual('auto');\nconst isVisible = isEqual('visible');\nconst isEither = (overflow, fn) => fn(overflow.overflowX) || fn(overflow.overflowY);\nconst isBoth = (overflow, fn) => fn(overflow.overflowX) && fn(overflow.overflowY);\nconst isElementScrollable = el => {\n  const style = window.getComputedStyle(el);\n  const overflow = {\n    overflowX: style.overflowX,\n    overflowY: style.overflowY\n  };\n  return isEither(overflow, isScroll) || isEither(overflow, isAuto);\n};\nconst isBodyScrollable = () => {\n  if (process.env.NODE_ENV === 'production') {\n    return false;\n  }\n  const body = getBodyElement();\n  const html = document.documentElement;\n  !html ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  if (!isElementScrollable(body)) {\n    return false;\n  }\n  const htmlStyle = window.getComputedStyle(html);\n  const htmlOverflow = {\n    overflowX: htmlStyle.overflowX,\n    overflowY: htmlStyle.overflowY\n  };\n  if (isBoth(htmlOverflow, isVisible)) {\n    return false;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    We have detected that your <body> element might be a scroll container.\n    We have found no reliable way of detecting whether the <body> element is a scroll container.\n    Under most circumstances a <body> scroll bar will be on the <html> element (document.documentElement)\n\n    Because we cannot determine if the <body> is a scroll container, and generally it is not one,\n    we will be treating the <body> as *not* a scroll container\n\n    More information: https://github.com/hello-pangea/dnd/blob/main/docs/guides/how-we-detect-scroll-containers.md\n  `) : void 0;\n  return false;\n};\nconst getClosestScrollable = el => {\n  if (el == null) {\n    return null;\n  }\n  if (el === document.body) {\n    return isBodyScrollable() ? el : null;\n  }\n  if (el === document.documentElement) {\n    return null;\n  }\n  if (!isElementScrollable(el)) {\n    return getClosestScrollable(el.parentElement);\n  }\n  return el;\n};\n\nvar checkForNestedScrollContainers = (scrollable => {\n  if (!scrollable) {\n    return;\n  }\n  const anotherScrollParent = getClosestScrollable(scrollable.parentElement);\n  if (!anotherScrollParent) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n    Droppable: unsupported nested scroll container detected.\n    A Droppable can only have one scroll parent (which can be itself)\n    Nested scroll containers are currently not supported.\n\n    We hope to support nested scroll containers soon: https://github.com/atlassian/react-beautiful-dnd/issues/131\n  `) : void 0;\n});\n\nvar getScroll = (el => ({\n  x: el.scrollLeft,\n  y: el.scrollTop\n}));\n\nconst getIsFixed = el => {\n  if (!el) {\n    return false;\n  }\n  const style = window.getComputedStyle(el);\n  if (style.position === 'fixed') {\n    return true;\n  }\n  return getIsFixed(el.parentElement);\n};\nvar getEnv = (start => {\n  const closestScrollable = getClosestScrollable(start);\n  const isFixedOnPage = getIsFixed(start);\n  return {\n    closestScrollable,\n    isFixedOnPage\n  };\n});\n\nvar getDroppableDimension = (({\n  descriptor,\n  isEnabled,\n  isCombineEnabled,\n  isFixedOnPage,\n  direction,\n  client,\n  page,\n  closest\n}) => {\n  const frame = (() => {\n    if (!closest) {\n      return null;\n    }\n    const {\n      scrollSize,\n      client: frameClient\n    } = closest;\n    const maxScroll = getMaxScroll({\n      scrollHeight: scrollSize.scrollHeight,\n      scrollWidth: scrollSize.scrollWidth,\n      height: frameClient.paddingBox.height,\n      width: frameClient.paddingBox.width\n    });\n    return {\n      pageMarginBox: closest.page.marginBox,\n      frameClient,\n      scrollSize,\n      shouldClipSubject: closest.shouldClipSubject,\n      scroll: {\n        initial: closest.scroll,\n        current: closest.scroll,\n        max: maxScroll,\n        diff: {\n          value: origin,\n          displacement: origin\n        }\n      }\n    };\n  })();\n  const axis = direction === 'vertical' ? vertical : horizontal;\n  const subject = getSubject({\n    page,\n    withPlaceholder: null,\n    axis,\n    frame\n  });\n  const dimension = {\n    descriptor,\n    isCombineEnabled,\n    isFixedOnPage,\n    axis,\n    isEnabled,\n    client,\n    page,\n    frame,\n    subject\n  };\n  return dimension;\n});\n\nconst getClient = (targetRef, closestScrollable) => {\n  const base = getBox(targetRef);\n  if (!closestScrollable) {\n    return base;\n  }\n  if (targetRef !== closestScrollable) {\n    return base;\n  }\n  const top = base.paddingBox.top - closestScrollable.scrollTop;\n  const left = base.paddingBox.left - closestScrollable.scrollLeft;\n  const bottom = top + closestScrollable.scrollHeight;\n  const right = left + closestScrollable.scrollWidth;\n  const paddingBox = {\n    top,\n    right,\n    bottom,\n    left\n  };\n  const borderBox = expand(paddingBox, base.border);\n  const client = createBox({\n    borderBox,\n    margin: base.margin,\n    border: base.border,\n    padding: base.padding\n  });\n  return client;\n};\nvar getDimension = (({\n  ref,\n  descriptor,\n  env,\n  windowScroll,\n  direction,\n  isDropDisabled,\n  isCombineEnabled,\n  shouldClipSubject\n}) => {\n  const closestScrollable = env.closestScrollable;\n  const client = getClient(ref, closestScrollable);\n  const page = withScroll(client, windowScroll);\n  const closest = (() => {\n    if (!closestScrollable) {\n      return null;\n    }\n    const frameClient = getBox(closestScrollable);\n    const scrollSize = {\n      scrollHeight: closestScrollable.scrollHeight,\n      scrollWidth: closestScrollable.scrollWidth\n    };\n    return {\n      client: frameClient,\n      page: withScroll(frameClient, windowScroll),\n      scroll: getScroll(closestScrollable),\n      scrollSize,\n      shouldClipSubject\n    };\n  })();\n  const dimension = getDroppableDimension({\n    descriptor,\n    isEnabled: !isDropDisabled,\n    isCombineEnabled,\n    isFixedOnPage: env.isFixedOnPage,\n    direction,\n    client,\n    page,\n    closest\n  });\n  return dimension;\n});\n\nconst immediate = {\n  passive: false\n};\nconst delayed = {\n  passive: true\n};\nvar getListenerOptions = (options => options.shouldPublishImmediately ? immediate : delayed);\n\nconst getClosestScrollableFromDrag = dragging => dragging && dragging.env.closestScrollable || null;\nfunction useDroppablePublisher(args) {\n  const whileDraggingRef = useRef(null);\n  const appContext = useRequiredContext(AppContext);\n  const uniqueId = useUniqueId$1('droppable');\n  const {\n    registry,\n    marshal\n  } = appContext;\n  const previousRef = usePrevious(args);\n  const descriptor = useMemo(() => ({\n    id: args.droppableId,\n    type: args.type,\n    mode: args.mode\n  }), [args.droppableId, args.mode, args.type]);\n  const publishedDescriptorRef = useRef(descriptor);\n  const memoizedUpdateScroll = useMemo(() => memoizeOne((x, y) => {\n    !whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only update scroll when dragging') : invariant(false) : void 0;\n    const scroll = {\n      x,\n      y\n    };\n    marshal.updateDroppableScroll(descriptor.id, scroll);\n  }), [descriptor.id, marshal]);\n  const getClosestScroll = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    if (!dragging || !dragging.env.closestScrollable) {\n      return origin;\n    }\n    return getScroll(dragging.env.closestScrollable);\n  }, []);\n  const updateScroll = useCallback(() => {\n    const scroll = getClosestScroll();\n    memoizedUpdateScroll(scroll.x, scroll.y);\n  }, [getClosestScroll, memoizedUpdateScroll]);\n  const scheduleScrollUpdate = useMemo(() => rafSchd(updateScroll), [updateScroll]);\n  const onClosestScroll = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find scroll options while scrolling') : invariant(false) : void 0;\n    const options = dragging.scrollOptions;\n    if (options.shouldPublishImmediately) {\n      updateScroll();\n      return;\n    }\n    scheduleScrollUpdate();\n  }, [scheduleScrollUpdate, updateScroll]);\n  const getDimensionAndWatchScroll = useCallback((windowScroll, options) => {\n    !!whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect a droppable while a drag is occurring') : invariant(false) : void 0;\n    const previous = previousRef.current;\n    const ref = previous.getDroppableRef();\n    !ref ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect without a droppable ref') : invariant(false) : void 0;\n    const env = getEnv(ref);\n    const dragging = {\n      ref,\n      descriptor,\n      env,\n      scrollOptions: options\n    };\n    whileDraggingRef.current = dragging;\n    const dimension = getDimension({\n      ref,\n      descriptor,\n      env,\n      windowScroll,\n      direction: previous.direction,\n      isDropDisabled: previous.isDropDisabled,\n      isCombineEnabled: previous.isCombineEnabled,\n      shouldClipSubject: !previous.ignoreContainerClipping\n    });\n    const scrollable = env.closestScrollable;\n    if (scrollable) {\n      scrollable.setAttribute(scrollContainer.contextId, appContext.contextId);\n      scrollable.addEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n      if (process.env.NODE_ENV !== 'production') {\n        checkForNestedScrollContainers(scrollable);\n      }\n    }\n    return dimension;\n  }, [appContext.contextId, descriptor, onClosestScroll, previousRef]);\n  const getScrollWhileDragging = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only recollect Droppable client for Droppables that have a scroll container') : invariant(false) : void 0;\n    return getScroll(closest);\n  }, []);\n  const dragStopped = useCallback(() => {\n    const dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop drag when no active drag') : invariant(false) : void 0;\n    const closest = getClosestScrollableFromDrag(dragging);\n    whileDraggingRef.current = null;\n    if (!closest) {\n      return;\n    }\n    scheduleScrollUpdate.cancel();\n    closest.removeAttribute(scrollContainer.contextId);\n    closest.removeEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n  }, [onClosestScroll, scheduleScrollUpdate]);\n  const scroll = useCallback(change => {\n    const dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll when there is no drag') : invariant(false) : void 0;\n    const closest = getClosestScrollableFromDrag(dragging);\n    !closest ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll a droppable with no closest scrollable') : invariant(false) : void 0;\n    closest.scrollTop += change.y;\n    closest.scrollLeft += change.x;\n  }, []);\n  const callbacks = useMemo(() => {\n    return {\n      getDimensionAndWatchScroll,\n      getScrollWhileDragging,\n      dragStopped,\n      scroll\n    };\n  }, [dragStopped, getDimensionAndWatchScroll, getScrollWhileDragging, scroll]);\n  const entry = useMemo(() => ({\n    uniqueId,\n    descriptor,\n    callbacks\n  }), [callbacks, descriptor, uniqueId]);\n  useLayoutEffect(() => {\n    publishedDescriptorRef.current = entry.descriptor;\n    registry.droppable.register(entry);\n    return () => {\n      if (whileDraggingRef.current) {\n        process.env.NODE_ENV !== \"production\" ? warning('Unsupported: changing the droppableId or type of a Droppable during a drag') : void 0;\n        dragStopped();\n      }\n      registry.droppable.unregister(entry);\n    };\n  }, [callbacks, descriptor, dragStopped, entry, marshal, registry.droppable]);\n  useLayoutEffect(() => {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsEnabled(publishedDescriptorRef.current.id, !args.isDropDisabled);\n  }, [args.isDropDisabled, marshal]);\n  useLayoutEffect(() => {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n    marshal.updateDroppableIsCombineEnabled(publishedDescriptorRef.current.id, args.isCombineEnabled);\n  }, [args.isCombineEnabled, marshal]);\n}\n\nfunction noop() {}\nconst empty = {\n  width: 0,\n  height: 0,\n  margin: noSpacing\n};\nconst getSize = ({\n  isAnimatingOpenOnMount,\n  placeholder,\n  animate\n}) => {\n  if (isAnimatingOpenOnMount) {\n    return empty;\n  }\n  if (animate === 'close') {\n    return empty;\n  }\n  return {\n    height: placeholder.client.borderBox.height,\n    width: placeholder.client.borderBox.width,\n    margin: placeholder.client.margin\n  };\n};\nconst getStyle = ({\n  isAnimatingOpenOnMount,\n  placeholder,\n  animate\n}) => {\n  const size = getSize({\n    isAnimatingOpenOnMount,\n    placeholder,\n    animate\n  });\n  return {\n    display: placeholder.display,\n    boxSizing: 'border-box',\n    width: size.width,\n    height: size.height,\n    marginTop: size.margin.top,\n    marginRight: size.margin.right,\n    marginBottom: size.margin.bottom,\n    marginLeft: size.margin.left,\n    flexShrink: '0',\n    flexGrow: '0',\n    pointerEvents: 'none',\n    transition: animate !== 'none' ? transitions.placeholder : null\n  };\n};\nconst Placeholder = props => {\n  const animateOpenTimerRef = useRef(null);\n  const tryClearAnimateOpenTimer = useCallback(() => {\n    if (!animateOpenTimerRef.current) {\n      return;\n    }\n    clearTimeout(animateOpenTimerRef.current);\n    animateOpenTimerRef.current = null;\n  }, []);\n  const {\n    animate,\n    onTransitionEnd,\n    onClose,\n    contextId\n  } = props;\n  const [isAnimatingOpenOnMount, setIsAnimatingOpenOnMount] = useState(props.animate === 'open');\n  useEffect(() => {\n    if (!isAnimatingOpenOnMount) {\n      return noop;\n    }\n    if (animate !== 'open') {\n      tryClearAnimateOpenTimer();\n      setIsAnimatingOpenOnMount(false);\n      return noop;\n    }\n    if (animateOpenTimerRef.current) {\n      return noop;\n    }\n    animateOpenTimerRef.current = setTimeout(() => {\n      animateOpenTimerRef.current = null;\n      setIsAnimatingOpenOnMount(false);\n    });\n    return tryClearAnimateOpenTimer;\n  }, [animate, isAnimatingOpenOnMount, tryClearAnimateOpenTimer]);\n  const onSizeChangeEnd = useCallback(event => {\n    if (event.propertyName !== 'height') {\n      return;\n    }\n    onTransitionEnd();\n    if (animate === 'close') {\n      onClose();\n    }\n  }, [animate, onClose, onTransitionEnd]);\n  const style = getStyle({\n    isAnimatingOpenOnMount,\n    animate: props.animate,\n    placeholder: props.placeholder\n  });\n  return React.createElement(props.placeholder.tagName, {\n    style,\n    'data-rfd-placeholder-context-id': contextId,\n    onTransitionEnd: onSizeChangeEnd,\n    ref: props.innerRef\n  });\n};\nvar Placeholder$1 = React.memo(Placeholder);\n\nfunction isBoolean(value) {\n  return typeof value === 'boolean';\n}\nfunction runChecks(args, checks) {\n  checks.forEach(check => check(args));\n}\nconst shared = [function required({\n  props\n}) {\n  !props.droppableId ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A Droppable requires a droppableId prop') : invariant(false) : void 0;\n  !(typeof props.droppableId === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, `A Droppable requires a [string] droppableId. Provided: [${typeof props.droppableId}]`) : invariant(false) : void 0;\n}, function boolean({\n  props\n}) {\n  !isBoolean(props.isDropDisabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isDropDisabled must be a boolean') : invariant(false) : void 0;\n  !isBoolean(props.isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isCombineEnabled must be a boolean') : invariant(false) : void 0;\n  !isBoolean(props.ignoreContainerClipping) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ignoreContainerClipping must be a boolean') : invariant(false) : void 0;\n}, function ref({\n  getDroppableRef\n}) {\n  checkIsValidInnerRef(getDroppableRef());\n}];\nconst standard = [function placeholder({\n  props,\n  getPlaceholderRef\n}) {\n  if (!props.placeholder) {\n    return;\n  }\n  const ref = getPlaceholderRef();\n  if (ref) {\n    return;\n  }\n  process.env.NODE_ENV !== \"production\" ? warning(`\n      Droppable setup issue [droppableId: \"${props.droppableId}\"]:\n      DroppableProvided > placeholder could not be found.\n\n      Please be sure to add the {provided.placeholder} React Node as a child of your Droppable.\n      More information: https://github.com/hello-pangea/dnd/blob/main/docs/api/droppable.md\n    `) : void 0;\n}];\nconst virtual = [function hasClone({\n  props\n}) {\n  !props.renderClone ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must provide a clone render function (renderClone) for virtual lists') : invariant(false) : void 0;\n}, function hasNoPlaceholder({\n  getPlaceholderRef\n}) {\n  !!getPlaceholderRef() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected virtual list to not have a placeholder') : invariant(false) : void 0;\n}];\nfunction useValidation(args) {\n  useDevSetupWarning(() => {\n    runChecks(args, shared);\n    if (args.props.mode === 'standard') {\n      runChecks(args, standard);\n    }\n    if (args.props.mode === 'virtual') {\n      runChecks(args, virtual);\n    }\n  });\n}\n\nclass AnimateInOut extends React.PureComponent {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      isVisible: Boolean(this.props.on),\n      data: this.props.on,\n      animate: this.props.shouldAnimate && this.props.on ? 'open' : 'none'\n    };\n    this.onClose = () => {\n      if (this.state.animate !== 'close') {\n        return;\n      }\n      this.setState({\n        isVisible: false\n      });\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (!props.shouldAnimate) {\n      return {\n        isVisible: Boolean(props.on),\n        data: props.on,\n        animate: 'none'\n      };\n    }\n    if (props.on) {\n      return {\n        isVisible: true,\n        data: props.on,\n        animate: 'open'\n      };\n    }\n    if (state.isVisible) {\n      return {\n        isVisible: true,\n        data: state.data,\n        animate: 'close'\n      };\n    }\n    return {\n      isVisible: false,\n      animate: 'close',\n      data: null\n    };\n  }\n  render() {\n    if (!this.state.isVisible) {\n      return null;\n    }\n    const provided = {\n      onClose: this.onClose,\n      data: this.state.data,\n      animate: this.state.animate\n    };\n    return this.props.children(provided);\n  }\n}\n\nconst Droppable = props => {\n  const appContext = useContext(AppContext);\n  !appContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find app context') : invariant(false) : void 0;\n  const {\n    contextId,\n    isMovementAllowed\n  } = appContext;\n  const droppableRef = useRef(null);\n  const placeholderRef = useRef(null);\n  const {\n    children,\n    droppableId,\n    type,\n    mode,\n    direction,\n    ignoreContainerClipping,\n    isDropDisabled,\n    isCombineEnabled,\n    snapshot,\n    useClone,\n    updateViewportMaxScroll,\n    getContainerForClone\n  } = props;\n  const getDroppableRef = useCallback(() => droppableRef.current, []);\n  const setDroppableRef = useCallback((value = null) => {\n    droppableRef.current = value;\n  }, []);\n  const getPlaceholderRef = useCallback(() => placeholderRef.current, []);\n  const setPlaceholderRef = useCallback((value = null) => {\n    placeholderRef.current = value;\n  }, []);\n  useValidation({\n    props,\n    getDroppableRef,\n    getPlaceholderRef\n  });\n  const onPlaceholderTransitionEnd = useCallback(() => {\n    if (isMovementAllowed()) {\n      updateViewportMaxScroll({\n        maxScroll: getMaxWindowScroll()\n      });\n    }\n  }, [isMovementAllowed, updateViewportMaxScroll]);\n  useDroppablePublisher({\n    droppableId,\n    type,\n    mode,\n    direction,\n    isDropDisabled,\n    isCombineEnabled,\n    ignoreContainerClipping,\n    getDroppableRef\n  });\n  const placeholder = useMemo(() => React.createElement(AnimateInOut, {\n    on: props.placeholder,\n    shouldAnimate: props.shouldAnimatePlaceholder\n  }, ({\n    onClose,\n    data,\n    animate\n  }) => React.createElement(Placeholder$1, {\n    placeholder: data,\n    onClose: onClose,\n    innerRef: setPlaceholderRef,\n    animate: animate,\n    contextId: contextId,\n    onTransitionEnd: onPlaceholderTransitionEnd\n  })), [contextId, onPlaceholderTransitionEnd, props.placeholder, props.shouldAnimatePlaceholder, setPlaceholderRef]);\n  const provided = useMemo(() => ({\n    innerRef: setDroppableRef,\n    placeholder,\n    droppableProps: {\n      'data-rfd-droppable-id': droppableId,\n      'data-rfd-droppable-context-id': contextId\n    }\n  }), [contextId, droppableId, placeholder, setDroppableRef]);\n  const isUsingCloneFor = useClone ? useClone.dragging.draggableId : null;\n  const droppableContext = useMemo(() => ({\n    droppableId,\n    type,\n    isUsingCloneFor\n  }), [droppableId, isUsingCloneFor, type]);\n  function getClone() {\n    if (!useClone) {\n      return null;\n    }\n    const {\n      dragging,\n      render\n    } = useClone;\n    const node = React.createElement(PrivateDraggable, {\n      draggableId: dragging.draggableId,\n      index: dragging.source.index,\n      isClone: true,\n      isEnabled: true,\n      shouldRespectForcePress: false,\n      canDragInteractiveElements: true\n    }, (draggableProvided, draggableSnapshot) => render(draggableProvided, draggableSnapshot, dragging));\n    return ReactDOM.createPortal(node, getContainerForClone());\n  }\n  return React.createElement(DroppableContext.Provider, {\n    value: droppableContext\n  }, children(provided, snapshot), getClone());\n};\nvar Droppable$1 = Droppable;\n\nfunction getBody() {\n  !document.body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'document.body is not ready') : invariant(false) : void 0;\n  return document.body;\n}\nconst defaultProps = {\n  mode: 'standard',\n  type: 'DEFAULT',\n  direction: 'vertical',\n  isDropDisabled: false,\n  isCombineEnabled: false,\n  ignoreContainerClipping: false,\n  renderClone: null,\n  getContainerForClone: getBody\n};\nconst attachDefaultPropsToOwnProps = ownProps => {\n  let mergedProps = {\n    ...ownProps\n  };\n  let defaultPropKey;\n  for (defaultPropKey in defaultProps) {\n    if (ownProps[defaultPropKey] === undefined) {\n      mergedProps = {\n        ...mergedProps,\n        [defaultPropKey]: defaultProps[defaultPropKey]\n      };\n    }\n  }\n  return mergedProps;\n};\nconst isMatchingType = (type, critical) => type === critical.droppable.type;\nconst getDraggable = (critical, dimensions) => dimensions.draggables[critical.draggable.id];\nconst makeMapStateToProps = () => {\n  const idleWithAnimation = {\n    placeholder: null,\n    shouldAnimatePlaceholder: true,\n    snapshot: {\n      isDraggingOver: false,\n      draggingOverWith: null,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: false\n    },\n    useClone: null\n  };\n  const idleWithoutAnimation = {\n    ...idleWithAnimation,\n    shouldAnimatePlaceholder: false\n  };\n  const getDraggableRubric = memoizeOne(descriptor => ({\n    draggableId: descriptor.id,\n    type: descriptor.type,\n    source: {\n      index: descriptor.index,\n      droppableId: descriptor.droppableId\n    }\n  }));\n  const getMapProps = memoizeOne((id, isEnabled, isDraggingOverForConsumer, isDraggingOverForImpact, dragging, renderClone) => {\n    const draggableId = dragging.descriptor.id;\n    const isHome = dragging.descriptor.droppableId === id;\n    if (isHome) {\n      const useClone = renderClone ? {\n        render: renderClone,\n        dragging: getDraggableRubric(dragging.descriptor)\n      } : null;\n      const snapshot = {\n        isDraggingOver: isDraggingOverForConsumer,\n        draggingOverWith: isDraggingOverForConsumer ? draggableId : null,\n        draggingFromThisWith: draggableId,\n        isUsingPlaceholder: true\n      };\n      return {\n        placeholder: dragging.placeholder,\n        shouldAnimatePlaceholder: false,\n        snapshot,\n        useClone\n      };\n    }\n    if (!isEnabled) {\n      return idleWithoutAnimation;\n    }\n    if (!isDraggingOverForImpact) {\n      return idleWithAnimation;\n    }\n    const snapshot = {\n      isDraggingOver: isDraggingOverForConsumer,\n      draggingOverWith: draggableId,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: true\n    };\n    return {\n      placeholder: dragging.placeholder,\n      shouldAnimatePlaceholder: true,\n      snapshot,\n      useClone: null\n    };\n  });\n  const selector = (state, ownProps) => {\n    const ownPropsWithDefaultProps = attachDefaultPropsToOwnProps(ownProps);\n    const id = ownPropsWithDefaultProps.droppableId;\n    const type = ownPropsWithDefaultProps.type;\n    const isEnabled = !ownPropsWithDefaultProps.isDropDisabled;\n    const renderClone = ownPropsWithDefaultProps.renderClone;\n    if (isDragging(state)) {\n      const critical = state.critical;\n      if (!isMatchingType(type, critical)) {\n        return idleWithoutAnimation;\n      }\n      const dragging = getDraggable(critical, state.dimensions);\n      const isDraggingOver = whatIsDraggedOver(state.impact) === id;\n      return getMapProps(id, isEnabled, isDraggingOver, isDraggingOver, dragging, renderClone);\n    }\n    if (state.phase === 'DROP_ANIMATING') {\n      const completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      const dragging = getDraggable(completed.critical, state.dimensions);\n      return getMapProps(id, isEnabled, whatIsDraggedOverFromResult(completed.result) === id, whatIsDraggedOver(completed.impact) === id, dragging, renderClone);\n    }\n    if (state.phase === 'IDLE' && state.completed && !state.shouldFlush) {\n      const completed = state.completed;\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n      const wasOver = whatIsDraggedOver(completed.impact) === id;\n      const wasCombining = Boolean(completed.impact.at && completed.impact.at.type === 'COMBINE');\n      const isHome = completed.critical.droppable.id === id;\n      if (wasOver) {\n        return wasCombining ? idleWithAnimation : idleWithoutAnimation;\n      }\n      if (isHome) {\n        return idleWithAnimation;\n      }\n      return idleWithoutAnimation;\n    }\n    return idleWithoutAnimation;\n  };\n  return selector;\n};\nconst mapDispatchToProps = {\n  updateViewportMaxScroll: updateViewportMaxScroll\n};\nconst ConnectedDroppable = connect(makeMapStateToProps, mapDispatchToProps, (stateProps, dispatchProps, ownProps) => {\n  return {\n    ...attachDefaultPropsToOwnProps(ownProps),\n    ...stateProps,\n    ...dispatchProps\n  };\n}, {\n  context: StoreContext,\n  areStatePropsEqual: isStrictEqual\n})(Droppable$1);\nvar ConnectedDroppable$1 = ConnectedDroppable;\n\nexport { DragDropContext, PublicDraggable as Draggable, ConnectedDroppable$1 as Droppable, resetServerContext, useKeyboardSensor, useMouseSensor, useTouchSensor };\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { PureComponent } from 'react';\nimport { flushSync } from 'react-dom';\nimport { Resizer } from './resizer';\nvar DEFAULT_SIZE = {\n    width: 'auto',\n    height: 'auto',\n};\nvar clamp = function (n, min, max) { return Math.max(Math.min(n, max), min); };\nvar snap = function (n, size, gridGap) {\n    var v = Math.round(n / size);\n    return v * size + gridGap * (v - 1);\n};\nvar hasDirection = function (dir, target) {\n    return new RegExp(dir, 'i').test(target);\n};\n// INFO: In case of window is a Proxy and does not porxy Events correctly, use isTouchEvent & isMouseEvent to distinguish event type instead of `instanceof`.\nvar isTouchEvent = function (event) {\n    return Boolean(event.touches && event.touches.length);\n};\nvar isMouseEvent = function (event) {\n    return Boolean((event.clientX || event.clientX === 0) &&\n        (event.clientY || event.clientY === 0));\n};\nvar findClosestSnap = function (n, snapArray, snapGap) {\n    if (snapGap === void 0) { snapGap = 0; }\n    var closestGapIndex = snapArray.reduce(function (prev, curr, index) { return (Math.abs(curr - n) < Math.abs(snapArray[prev] - n) ? index : prev); }, 0);\n    var gap = Math.abs(snapArray[closestGapIndex] - n);\n    return snapGap === 0 || gap < snapGap ? snapArray[closestGapIndex] : n;\n};\nvar getStringSize = function (n) {\n    n = n.toString();\n    if (n === 'auto') {\n        return n;\n    }\n    if (n.endsWith('px')) {\n        return n;\n    }\n    if (n.endsWith('%')) {\n        return n;\n    }\n    if (n.endsWith('vh')) {\n        return n;\n    }\n    if (n.endsWith('vw')) {\n        return n;\n    }\n    if (n.endsWith('vmax')) {\n        return n;\n    }\n    if (n.endsWith('vmin')) {\n        return n;\n    }\n    return \"\".concat(n, \"px\");\n};\nvar getPixelSize = function (size, parentSize, innerWidth, innerHeight) {\n    if (size && typeof size === 'string') {\n        if (size.endsWith('px')) {\n            return Number(size.replace('px', ''));\n        }\n        if (size.endsWith('%')) {\n            var ratio = Number(size.replace('%', '')) / 100;\n            return parentSize * ratio;\n        }\n        if (size.endsWith('vw')) {\n            var ratio = Number(size.replace('vw', '')) / 100;\n            return innerWidth * ratio;\n        }\n        if (size.endsWith('vh')) {\n            var ratio = Number(size.replace('vh', '')) / 100;\n            return innerHeight * ratio;\n        }\n    }\n    return size;\n};\nvar calculateNewMax = function (parentSize, innerWidth, innerHeight, maxWidth, maxHeight, minWidth, minHeight) {\n    maxWidth = getPixelSize(maxWidth, parentSize.width, innerWidth, innerHeight);\n    maxHeight = getPixelSize(maxHeight, parentSize.height, innerWidth, innerHeight);\n    minWidth = getPixelSize(minWidth, parentSize.width, innerWidth, innerHeight);\n    minHeight = getPixelSize(minHeight, parentSize.height, innerWidth, innerHeight);\n    return {\n        maxWidth: typeof maxWidth === 'undefined' ? undefined : Number(maxWidth),\n        maxHeight: typeof maxHeight === 'undefined' ? undefined : Number(maxHeight),\n        minWidth: typeof minWidth === 'undefined' ? undefined : Number(minWidth),\n        minHeight: typeof minHeight === 'undefined' ? undefined : Number(minHeight),\n    };\n};\n/**\n * transform T | [T, T] to [T, T]\n * @param val\n * @returns\n */\n// tslint:disable-next-line\nvar normalizeToPair = function (val) { return (Array.isArray(val) ? val : [val, val]); };\nvar definedProps = [\n    'as',\n    'ref',\n    'style',\n    'className',\n    'grid',\n    'gridGap',\n    'snap',\n    'bounds',\n    'boundsByDirection',\n    'size',\n    'defaultSize',\n    'minWidth',\n    'minHeight',\n    'maxWidth',\n    'maxHeight',\n    'lockAspectRatio',\n    'lockAspectRatioExtraWidth',\n    'lockAspectRatioExtraHeight',\n    'enable',\n    'handleStyles',\n    'handleClasses',\n    'handleWrapperStyle',\n    'handleWrapperClass',\n    'children',\n    'onResizeStart',\n    'onResize',\n    'onResizeStop',\n    'handleComponent',\n    'scale',\n    'resizeRatio',\n    'snapGap',\n];\n// HACK: This class is used to calculate % size.\nvar baseClassName = '__resizable_base__';\nvar Resizable = /** @class */ (function (_super) {\n    __extends(Resizable, _super);\n    function Resizable(props) {\n        var _a, _b, _c, _d;\n        var _this = _super.call(this, props) || this;\n        _this.ratio = 1;\n        _this.resizable = null;\n        // For parent boundary\n        _this.parentLeft = 0;\n        _this.parentTop = 0;\n        // For boundary\n        _this.resizableLeft = 0;\n        _this.resizableRight = 0;\n        _this.resizableTop = 0;\n        _this.resizableBottom = 0;\n        // For target boundary\n        _this.targetLeft = 0;\n        _this.targetTop = 0;\n        _this.delta = {\n            width: 0,\n            height: 0,\n        };\n        _this.appendBase = function () {\n            if (!_this.resizable || !_this.window) {\n                return null;\n            }\n            var parent = _this.parentNode;\n            if (!parent) {\n                return null;\n            }\n            var element = _this.window.document.createElement('div');\n            element.style.width = '100%';\n            element.style.height = '100%';\n            element.style.position = 'absolute';\n            element.style.transform = 'scale(0, 0)';\n            element.style.left = '0';\n            element.style.flex = '0 0 100%';\n            if (element.classList) {\n                element.classList.add(baseClassName);\n            }\n            else {\n                element.className += baseClassName;\n            }\n            parent.appendChild(element);\n            return element;\n        };\n        _this.removeBase = function (base) {\n            var parent = _this.parentNode;\n            if (!parent) {\n                return;\n            }\n            parent.removeChild(base);\n        };\n        _this.state = {\n            isResizing: false,\n            width: (_b = (_a = _this.propsSize) === null || _a === void 0 ? void 0 : _a.width) !== null && _b !== void 0 ? _b : 'auto',\n            height: (_d = (_c = _this.propsSize) === null || _c === void 0 ? void 0 : _c.height) !== null && _d !== void 0 ? _d : 'auto',\n            direction: 'right',\n            original: {\n                x: 0,\n                y: 0,\n                width: 0,\n                height: 0,\n            },\n            backgroundStyle: {\n                height: '100%',\n                width: '100%',\n                backgroundColor: 'rgba(0,0,0,0)',\n                cursor: 'auto',\n                opacity: 0,\n                position: 'fixed',\n                zIndex: 9999,\n                top: '0',\n                left: '0',\n                bottom: '0',\n                right: '0',\n            },\n            flexBasis: undefined,\n        };\n        _this.onResizeStart = _this.onResizeStart.bind(_this);\n        _this.onMouseMove = _this.onMouseMove.bind(_this);\n        _this.onMouseUp = _this.onMouseUp.bind(_this);\n        return _this;\n    }\n    Object.defineProperty(Resizable.prototype, \"parentNode\", {\n        get: function () {\n            if (!this.resizable) {\n                return null;\n            }\n            return this.resizable.parentNode;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"window\", {\n        get: function () {\n            if (!this.resizable) {\n                return null;\n            }\n            if (!this.resizable.ownerDocument) {\n                return null;\n            }\n            return this.resizable.ownerDocument.defaultView;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"propsSize\", {\n        get: function () {\n            return this.props.size || this.props.defaultSize || DEFAULT_SIZE;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"size\", {\n        get: function () {\n            var width = 0;\n            var height = 0;\n            if (this.resizable && this.window) {\n                var orgWidth = this.resizable.offsetWidth;\n                var orgHeight = this.resizable.offsetHeight;\n                // HACK: Set position `relative` to get parent size.\n                //       This is because when re-resizable set `absolute`, I can not get base width correctly.\n                var orgPosition = this.resizable.style.position;\n                if (orgPosition !== 'relative') {\n                    this.resizable.style.position = 'relative';\n                }\n                // INFO: Use original width or height if set auto.\n                width = this.resizable.style.width !== 'auto' ? this.resizable.offsetWidth : orgWidth;\n                height = this.resizable.style.height !== 'auto' ? this.resizable.offsetHeight : orgHeight;\n                // Restore original position\n                this.resizable.style.position = orgPosition;\n            }\n            return { width: width, height: height };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"sizeStyle\", {\n        get: function () {\n            var _this = this;\n            var size = this.props.size;\n            var getSize = function (key) {\n                var _a;\n                if (typeof _this.state[key] === 'undefined' || _this.state[key] === 'auto') {\n                    return 'auto';\n                }\n                if (_this.propsSize && _this.propsSize[key] && ((_a = _this.propsSize[key]) === null || _a === void 0 ? void 0 : _a.toString().endsWith('%'))) {\n                    if (_this.state[key].toString().endsWith('%')) {\n                        return _this.state[key].toString();\n                    }\n                    var parentSize = _this.getParentSize();\n                    var value = Number(_this.state[key].toString().replace('px', ''));\n                    var percent = (value / parentSize[key]) * 100;\n                    return \"\".concat(percent, \"%\");\n                }\n                return getStringSize(_this.state[key]);\n            };\n            var width = size && typeof size.width !== 'undefined' && !this.state.isResizing\n                ? getStringSize(size.width)\n                : getSize('width');\n            var height = size && typeof size.height !== 'undefined' && !this.state.isResizing\n                ? getStringSize(size.height)\n                : getSize('height');\n            return { width: width, height: height };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Resizable.prototype.getParentSize = function () {\n        if (!this.parentNode) {\n            if (!this.window) {\n                return { width: 0, height: 0 };\n            }\n            return { width: this.window.innerWidth, height: this.window.innerHeight };\n        }\n        var base = this.appendBase();\n        if (!base) {\n            return { width: 0, height: 0 };\n        }\n        // INFO: To calculate parent width with flex layout\n        var wrapChanged = false;\n        var wrap = this.parentNode.style.flexWrap;\n        if (wrap !== 'wrap') {\n            wrapChanged = true;\n            this.parentNode.style.flexWrap = 'wrap';\n            // HACK: Use relative to get parent padding size\n        }\n        base.style.position = 'relative';\n        base.style.minWidth = '100%';\n        base.style.minHeight = '100%';\n        var size = {\n            width: base.offsetWidth,\n            height: base.offsetHeight,\n        };\n        if (wrapChanged) {\n            this.parentNode.style.flexWrap = wrap;\n        }\n        this.removeBase(base);\n        return size;\n    };\n    Resizable.prototype.bindEvents = function () {\n        if (this.window) {\n            this.window.addEventListener('mouseup', this.onMouseUp);\n            this.window.addEventListener('mousemove', this.onMouseMove);\n            this.window.addEventListener('mouseleave', this.onMouseUp);\n            this.window.addEventListener('touchmove', this.onMouseMove, {\n                capture: true,\n                passive: false,\n            });\n            this.window.addEventListener('touchend', this.onMouseUp);\n        }\n    };\n    Resizable.prototype.unbindEvents = function () {\n        if (this.window) {\n            this.window.removeEventListener('mouseup', this.onMouseUp);\n            this.window.removeEventListener('mousemove', this.onMouseMove);\n            this.window.removeEventListener('mouseleave', this.onMouseUp);\n            this.window.removeEventListener('touchmove', this.onMouseMove, true);\n            this.window.removeEventListener('touchend', this.onMouseUp);\n        }\n    };\n    Resizable.prototype.componentDidMount = function () {\n        if (!this.resizable || !this.window) {\n            return;\n        }\n        var computedStyle = this.window.getComputedStyle(this.resizable);\n        this.setState({\n            width: this.state.width || this.size.width,\n            height: this.state.height || this.size.height,\n            flexBasis: computedStyle.flexBasis !== 'auto' ? computedStyle.flexBasis : undefined,\n        });\n    };\n    Resizable.prototype.componentWillUnmount = function () {\n        if (this.window) {\n            this.unbindEvents();\n        }\n    };\n    Resizable.prototype.createSizeForCssProperty = function (newSize, kind) {\n        var propsSize = this.propsSize && this.propsSize[kind];\n        return this.state[kind] === 'auto' &&\n            this.state.original[kind] === newSize &&\n            (typeof propsSize === 'undefined' || propsSize === 'auto')\n            ? 'auto'\n            : newSize;\n    };\n    Resizable.prototype.calculateNewMaxFromBoundary = function (maxWidth, maxHeight) {\n        var boundsByDirection = this.props.boundsByDirection;\n        var direction = this.state.direction;\n        var widthByDirection = boundsByDirection && hasDirection('left', direction);\n        var heightByDirection = boundsByDirection && hasDirection('top', direction);\n        var boundWidth;\n        var boundHeight;\n        if (this.props.bounds === 'parent') {\n            var parent_1 = this.parentNode;\n            if (parent_1) {\n                boundWidth = widthByDirection\n                    ? this.resizableRight - this.parentLeft\n                    : parent_1.offsetWidth + (this.parentLeft - this.resizableLeft);\n                boundHeight = heightByDirection\n                    ? this.resizableBottom - this.parentTop\n                    : parent_1.offsetHeight + (this.parentTop - this.resizableTop);\n            }\n        }\n        else if (this.props.bounds === 'window') {\n            if (this.window) {\n                boundWidth = widthByDirection ? this.resizableRight : this.window.innerWidth - this.resizableLeft;\n                boundHeight = heightByDirection ? this.resizableBottom : this.window.innerHeight - this.resizableTop;\n            }\n        }\n        else if (this.props.bounds) {\n            boundWidth = widthByDirection\n                ? this.resizableRight - this.targetLeft\n                : this.props.bounds.offsetWidth + (this.targetLeft - this.resizableLeft);\n            boundHeight = heightByDirection\n                ? this.resizableBottom - this.targetTop\n                : this.props.bounds.offsetHeight + (this.targetTop - this.resizableTop);\n        }\n        if (boundWidth && Number.isFinite(boundWidth)) {\n            maxWidth = maxWidth && maxWidth < boundWidth ? maxWidth : boundWidth;\n        }\n        if (boundHeight && Number.isFinite(boundHeight)) {\n            maxHeight = maxHeight && maxHeight < boundHeight ? maxHeight : boundHeight;\n        }\n        return { maxWidth: maxWidth, maxHeight: maxHeight };\n    };\n    Resizable.prototype.calculateNewSizeFromDirection = function (clientX, clientY) {\n        var scale = this.props.scale || 1;\n        var _a = normalizeToPair(this.props.resizeRatio || 1), resizeRatioX = _a[0], resizeRatioY = _a[1];\n        var _b = this.state, direction = _b.direction, original = _b.original;\n        var _c = this.props, lockAspectRatio = _c.lockAspectRatio, lockAspectRatioExtraHeight = _c.lockAspectRatioExtraHeight, lockAspectRatioExtraWidth = _c.lockAspectRatioExtraWidth;\n        var newWidth = original.width;\n        var newHeight = original.height;\n        var extraHeight = lockAspectRatioExtraHeight || 0;\n        var extraWidth = lockAspectRatioExtraWidth || 0;\n        if (hasDirection('right', direction)) {\n            newWidth = original.width + ((clientX - original.x) * resizeRatioX) / scale;\n            if (lockAspectRatio) {\n                newHeight = (newWidth - extraWidth) / this.ratio + extraHeight;\n            }\n        }\n        if (hasDirection('left', direction)) {\n            newWidth = original.width - ((clientX - original.x) * resizeRatioX) / scale;\n            if (lockAspectRatio) {\n                newHeight = (newWidth - extraWidth) / this.ratio + extraHeight;\n            }\n        }\n        if (hasDirection('bottom', direction)) {\n            newHeight = original.height + ((clientY - original.y) * resizeRatioY) / scale;\n            if (lockAspectRatio) {\n                newWidth = (newHeight - extraHeight) * this.ratio + extraWidth;\n            }\n        }\n        if (hasDirection('top', direction)) {\n            newHeight = original.height - ((clientY - original.y) * resizeRatioY) / scale;\n            if (lockAspectRatio) {\n                newWidth = (newHeight - extraHeight) * this.ratio + extraWidth;\n            }\n        }\n        return { newWidth: newWidth, newHeight: newHeight };\n    };\n    Resizable.prototype.calculateNewSizeFromAspectRatio = function (newWidth, newHeight, max, min) {\n        var _a = this.props, lockAspectRatio = _a.lockAspectRatio, lockAspectRatioExtraHeight = _a.lockAspectRatioExtraHeight, lockAspectRatioExtraWidth = _a.lockAspectRatioExtraWidth;\n        var computedMinWidth = typeof min.width === 'undefined' ? 10 : min.width;\n        var computedMaxWidth = typeof max.width === 'undefined' || max.width < 0 ? newWidth : max.width;\n        var computedMinHeight = typeof min.height === 'undefined' ? 10 : min.height;\n        var computedMaxHeight = typeof max.height === 'undefined' || max.height < 0 ? newHeight : max.height;\n        var extraHeight = lockAspectRatioExtraHeight || 0;\n        var extraWidth = lockAspectRatioExtraWidth || 0;\n        if (lockAspectRatio) {\n            var extraMinWidth = (computedMinHeight - extraHeight) * this.ratio + extraWidth;\n            var extraMaxWidth = (computedMaxHeight - extraHeight) * this.ratio + extraWidth;\n            var extraMinHeight = (computedMinWidth - extraWidth) / this.ratio + extraHeight;\n            var extraMaxHeight = (computedMaxWidth - extraWidth) / this.ratio + extraHeight;\n            var lockedMinWidth = Math.max(computedMinWidth, extraMinWidth);\n            var lockedMaxWidth = Math.min(computedMaxWidth, extraMaxWidth);\n            var lockedMinHeight = Math.max(computedMinHeight, extraMinHeight);\n            var lockedMaxHeight = Math.min(computedMaxHeight, extraMaxHeight);\n            newWidth = clamp(newWidth, lockedMinWidth, lockedMaxWidth);\n            newHeight = clamp(newHeight, lockedMinHeight, lockedMaxHeight);\n        }\n        else {\n            newWidth = clamp(newWidth, computedMinWidth, computedMaxWidth);\n            newHeight = clamp(newHeight, computedMinHeight, computedMaxHeight);\n        }\n        return { newWidth: newWidth, newHeight: newHeight };\n    };\n    Resizable.prototype.setBoundingClientRect = function () {\n        var adjustedScale = 1 / (this.props.scale || 1);\n        // For parent boundary\n        if (this.props.bounds === 'parent') {\n            var parent_2 = this.parentNode;\n            if (parent_2) {\n                var parentRect = parent_2.getBoundingClientRect();\n                this.parentLeft = parentRect.left * adjustedScale;\n                this.parentTop = parentRect.top * adjustedScale;\n            }\n        }\n        // For target(html element) boundary\n        if (this.props.bounds && typeof this.props.bounds !== 'string') {\n            var targetRect = this.props.bounds.getBoundingClientRect();\n            this.targetLeft = targetRect.left * adjustedScale;\n            this.targetTop = targetRect.top * adjustedScale;\n        }\n        // For boundary\n        if (this.resizable) {\n            var _a = this.resizable.getBoundingClientRect(), left = _a.left, top_1 = _a.top, right = _a.right, bottom = _a.bottom;\n            this.resizableLeft = left * adjustedScale;\n            this.resizableRight = right * adjustedScale;\n            this.resizableTop = top_1 * adjustedScale;\n            this.resizableBottom = bottom * adjustedScale;\n        }\n    };\n    Resizable.prototype.onResizeStart = function (event, direction) {\n        if (!this.resizable || !this.window) {\n            return;\n        }\n        var clientX = 0;\n        var clientY = 0;\n        if (event.nativeEvent && isMouseEvent(event.nativeEvent)) {\n            clientX = event.nativeEvent.clientX;\n            clientY = event.nativeEvent.clientY;\n        }\n        else if (event.nativeEvent && isTouchEvent(event.nativeEvent)) {\n            clientX = event.nativeEvent.touches[0].clientX;\n            clientY = event.nativeEvent.touches[0].clientY;\n        }\n        if (this.props.onResizeStart) {\n            if (this.resizable) {\n                var startResize = this.props.onResizeStart(event, direction, this.resizable);\n                if (startResize === false) {\n                    return;\n                }\n            }\n        }\n        // Fix #168\n        if (this.props.size) {\n            if (typeof this.props.size.height !== 'undefined' && this.props.size.height !== this.state.height) {\n                this.setState({ height: this.props.size.height });\n            }\n            if (typeof this.props.size.width !== 'undefined' && this.props.size.width !== this.state.width) {\n                this.setState({ width: this.props.size.width });\n            }\n        }\n        // For lockAspectRatio case\n        this.ratio =\n            typeof this.props.lockAspectRatio === 'number' ? this.props.lockAspectRatio : this.size.width / this.size.height;\n        var flexBasis;\n        var computedStyle = this.window.getComputedStyle(this.resizable);\n        if (computedStyle.flexBasis !== 'auto') {\n            var parent_3 = this.parentNode;\n            if (parent_3) {\n                var dir = this.window.getComputedStyle(parent_3).flexDirection;\n                this.flexDir = dir.startsWith('row') ? 'row' : 'column';\n                flexBasis = computedStyle.flexBasis;\n            }\n        }\n        // For boundary\n        this.setBoundingClientRect();\n        this.bindEvents();\n        var state = {\n            original: {\n                x: clientX,\n                y: clientY,\n                width: this.size.width,\n                height: this.size.height,\n            },\n            isResizing: true,\n            backgroundStyle: __assign(__assign({}, this.state.backgroundStyle), { cursor: this.window.getComputedStyle(event.target).cursor || 'auto' }),\n            direction: direction,\n            flexBasis: flexBasis,\n        };\n        this.setState(state);\n    };\n    Resizable.prototype.onMouseMove = function (event) {\n        var _this = this;\n        if (!this.state.isResizing || !this.resizable || !this.window) {\n            return;\n        }\n        if (this.window.TouchEvent && isTouchEvent(event)) {\n            try {\n                event.preventDefault();\n                event.stopPropagation();\n            }\n            catch (e) {\n                // Ignore on fail\n            }\n        }\n        var _a = this.props, maxWidth = _a.maxWidth, maxHeight = _a.maxHeight, minWidth = _a.minWidth, minHeight = _a.minHeight;\n        var clientX = isTouchEvent(event) ? event.touches[0].clientX : event.clientX;\n        var clientY = isTouchEvent(event) ? event.touches[0].clientY : event.clientY;\n        var _b = this.state, direction = _b.direction, original = _b.original, width = _b.width, height = _b.height;\n        var parentSize = this.getParentSize();\n        var max = calculateNewMax(parentSize, this.window.innerWidth, this.window.innerHeight, maxWidth, maxHeight, minWidth, minHeight);\n        maxWidth = max.maxWidth;\n        maxHeight = max.maxHeight;\n        minWidth = max.minWidth;\n        minHeight = max.minHeight;\n        // Calculate new size\n        var _c = this.calculateNewSizeFromDirection(clientX, clientY), newHeight = _c.newHeight, newWidth = _c.newWidth;\n        // Calculate max size from boundary settings\n        var boundaryMax = this.calculateNewMaxFromBoundary(maxWidth, maxHeight);\n        if (this.props.snap && this.props.snap.x) {\n            newWidth = findClosestSnap(newWidth, this.props.snap.x, this.props.snapGap);\n        }\n        if (this.props.snap && this.props.snap.y) {\n            newHeight = findClosestSnap(newHeight, this.props.snap.y, this.props.snapGap);\n        }\n        // Calculate new size from aspect ratio\n        var newSize = this.calculateNewSizeFromAspectRatio(newWidth, newHeight, { width: boundaryMax.maxWidth, height: boundaryMax.maxHeight }, { width: minWidth, height: minHeight });\n        newWidth = newSize.newWidth;\n        newHeight = newSize.newHeight;\n        if (this.props.grid) {\n            var newGridWidth = snap(newWidth, this.props.grid[0], this.props.gridGap ? this.props.gridGap[0] : 0);\n            var newGridHeight = snap(newHeight, this.props.grid[1], this.props.gridGap ? this.props.gridGap[1] : 0);\n            var gap = this.props.snapGap || 0;\n            var w = gap === 0 || Math.abs(newGridWidth - newWidth) <= gap ? newGridWidth : newWidth;\n            var h = gap === 0 || Math.abs(newGridHeight - newHeight) <= gap ? newGridHeight : newHeight;\n            newWidth = w;\n            newHeight = h;\n        }\n        var delta = {\n            width: newWidth - original.width,\n            height: newHeight - original.height,\n        };\n        this.delta = delta;\n        if (width && typeof width === 'string') {\n            if (width.endsWith('%')) {\n                var percent = (newWidth / parentSize.width) * 100;\n                newWidth = \"\".concat(percent, \"%\");\n            }\n            else if (width.endsWith('vw')) {\n                var vw = (newWidth / this.window.innerWidth) * 100;\n                newWidth = \"\".concat(vw, \"vw\");\n            }\n            else if (width.endsWith('vh')) {\n                var vh = (newWidth / this.window.innerHeight) * 100;\n                newWidth = \"\".concat(vh, \"vh\");\n            }\n        }\n        if (height && typeof height === 'string') {\n            if (height.endsWith('%')) {\n                var percent = (newHeight / parentSize.height) * 100;\n                newHeight = \"\".concat(percent, \"%\");\n            }\n            else if (height.endsWith('vw')) {\n                var vw = (newHeight / this.window.innerWidth) * 100;\n                newHeight = \"\".concat(vw, \"vw\");\n            }\n            else if (height.endsWith('vh')) {\n                var vh = (newHeight / this.window.innerHeight) * 100;\n                newHeight = \"\".concat(vh, \"vh\");\n            }\n        }\n        var newState = {\n            width: this.createSizeForCssProperty(newWidth, 'width'),\n            height: this.createSizeForCssProperty(newHeight, 'height'),\n        };\n        if (this.flexDir === 'row') {\n            newState.flexBasis = newState.width;\n        }\n        else if (this.flexDir === 'column') {\n            newState.flexBasis = newState.height;\n        }\n        var widthChanged = this.state.width !== newState.width;\n        var heightChanged = this.state.height !== newState.height;\n        var flexBaseChanged = this.state.flexBasis !== newState.flexBasis;\n        var changed = widthChanged || heightChanged || flexBaseChanged;\n        if (changed) {\n            // For v18, update state sync\n            flushSync(function () {\n                _this.setState(newState);\n            });\n        }\n        if (this.props.onResize) {\n            if (changed) {\n                this.props.onResize(event, direction, this.resizable, delta);\n            }\n        }\n    };\n    Resizable.prototype.onMouseUp = function (event) {\n        var _a, _b;\n        var _c = this.state, isResizing = _c.isResizing, direction = _c.direction, original = _c.original;\n        if (!isResizing || !this.resizable) {\n            return;\n        }\n        if (this.props.onResizeStop) {\n            this.props.onResizeStop(event, direction, this.resizable, this.delta);\n        }\n        if (this.props.size) {\n            this.setState({ width: (_a = this.props.size.width) !== null && _a !== void 0 ? _a : 'auto', height: (_b = this.props.size.height) !== null && _b !== void 0 ? _b : 'auto' });\n        }\n        this.unbindEvents();\n        this.setState({\n            isResizing: false,\n            backgroundStyle: __assign(__assign({}, this.state.backgroundStyle), { cursor: 'auto' }),\n        });\n    };\n    Resizable.prototype.updateSize = function (size) {\n        var _a, _b;\n        this.setState({ width: (_a = size.width) !== null && _a !== void 0 ? _a : 'auto', height: (_b = size.height) !== null && _b !== void 0 ? _b : 'auto' });\n    };\n    Resizable.prototype.renderResizer = function () {\n        var _this = this;\n        var _a = this.props, enable = _a.enable, handleStyles = _a.handleStyles, handleClasses = _a.handleClasses, handleWrapperStyle = _a.handleWrapperStyle, handleWrapperClass = _a.handleWrapperClass, handleComponent = _a.handleComponent;\n        if (!enable) {\n            return null;\n        }\n        var resizers = Object.keys(enable).map(function (dir) {\n            if (enable[dir] !== false) {\n                return (_jsx(Resizer, { direction: dir, onResizeStart: _this.onResizeStart, replaceStyles: handleStyles && handleStyles[dir], className: handleClasses && handleClasses[dir], children: handleComponent && handleComponent[dir] ? handleComponent[dir] : null }, dir));\n            }\n            return null;\n        });\n        // #93 Wrap the resize box in span (will not break 100% width/height)\n        return (_jsx(\"div\", { className: handleWrapperClass, style: handleWrapperStyle, children: resizers }));\n    };\n    Resizable.prototype.render = function () {\n        var _this = this;\n        var extendsProps = Object.keys(this.props).reduce(function (acc, key) {\n            if (definedProps.indexOf(key) !== -1) {\n                return acc;\n            }\n            acc[key] = _this.props[key];\n            return acc;\n        }, {});\n        var style = __assign(__assign(__assign({ position: 'relative', userSelect: this.state.isResizing ? 'none' : 'auto' }, this.props.style), this.sizeStyle), { maxWidth: this.props.maxWidth, maxHeight: this.props.maxHeight, minWidth: this.props.minWidth, minHeight: this.props.minHeight, boxSizing: 'border-box', flexShrink: 0 });\n        if (this.state.flexBasis) {\n            style.flexBasis = this.state.flexBasis;\n        }\n        var Wrapper = this.props.as || 'div';\n        return (_jsxs(Wrapper, __assign({ style: style, className: this.props.className }, extendsProps, { \n            // `ref` is after `extendsProps` to ensure this one wins over a version\n            // passed in\n            ref: function (c) {\n                if (c) {\n                    _this.resizable = c;\n                }\n            }, children: [this.state.isResizing && _jsx(\"div\", { style: this.state.backgroundStyle }), this.props.children, this.renderResizer()] })));\n    };\n    Resizable.defaultProps = {\n        as: 'div',\n        onResizeStart: function () { },\n        onResize: function () { },\n        onResizeStop: function () { },\n        enable: {\n            top: true,\n            right: true,\n            bottom: true,\n            left: true,\n            topRight: true,\n            bottomRight: true,\n            bottomLeft: true,\n            topLeft: true,\n        },\n        style: {},\n        grid: [1, 1],\n        gridGap: [0, 0],\n        lockAspectRatio: false,\n        lockAspectRatioExtraWidth: 0,\n        lockAspectRatioExtraHeight: 0,\n        scale: 1,\n        resizeRatio: 1,\n        snapGap: 0,\n    };\n    return Resizable;\n}(PureComponent));\nexport { Resizable };\n", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { memo, useCallback, useMemo } from 'react';\nvar rowSizeBase = {\n    width: '100%',\n    height: '10px',\n    top: '0px',\n    left: '0px',\n    cursor: 'row-resize',\n};\nvar colSizeBase = {\n    width: '10px',\n    height: '100%',\n    top: '0px',\n    left: '0px',\n    cursor: 'col-resize',\n};\nvar edgeBase = {\n    width: '20px',\n    height: '20px',\n    position: 'absolute',\n    zIndex: 1,\n};\nvar styles = {\n    top: __assign(__assign({}, rowSizeBase), { top: '-5px' }),\n    right: __assign(__assign({}, colSizeBase), { left: undefined, right: '-5px' }),\n    bottom: __assign(__assign({}, rowSizeBase), { top: undefined, bottom: '-5px' }),\n    left: __assign(__assign({}, colSizeBase), { left: '-5px' }),\n    topRight: __assign(__assign({}, edgeBase), { right: '-10px', top: '-10px', cursor: 'ne-resize' }),\n    bottomRight: __assign(__assign({}, edgeBase), { right: '-10px', bottom: '-10px', cursor: 'se-resize' }),\n    bottomLeft: __assign(__assign({}, edgeBase), { left: '-10px', bottom: '-10px', cursor: 'sw-resize' }),\n    topLeft: __assign(__assign({}, edgeBase), { left: '-10px', top: '-10px', cursor: 'nw-resize' }),\n};\nexport var Resizer = memo(function (props) {\n    var onResizeStart = props.onResizeStart, direction = props.direction, children = props.children, replaceStyles = props.replaceStyles, className = props.className;\n    var onMouseDown = useCallback(function (e) {\n        onResizeStart(e, direction);\n    }, [onResizeStart, direction]);\n    var onTouchStart = useCallback(function (e) {\n        onResizeStart(e, direction);\n    }, [onResizeStart, direction]);\n    var style = useMemo(function () {\n        return __assign(__assign({ position: 'absolute', userSelect: 'none' }, styles[direction]), (replaceStyles !== null && replaceStyles !== void 0 ? replaceStyles : {}));\n    }, [replaceStyles, direction]);\n    return (_jsx(\"div\", { className: className || undefined, style: style, onMouseDown: onMouseDown, onTouchStart: onTouchStart, children: children }));\n});\n", "(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"react\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"react\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ScrollSync\"] = factory(require(\"react\"));\n\telse\n\t\troot[\"ScrollSync\"] = factory(root[\"react\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_4__) {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// identity function for calling harmony imports with the correct context\n/******/ \t__webpack_require__.i = function(value) { return value; };\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 10);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\n// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * \n */\n\nfunction makeEmptyFunction(arg) {\n  return function () {\n    return arg;\n  };\n}\n\n/**\n * This function accepts and discards inputs; it has no side effects. This is\n * primarily useful idiomatically for overridable function endpoints which\n * always need to be callable, since JS lacks a null-call idiom ala Cocoa.\n */\nvar emptyFunction = function emptyFunction() {};\n\nemptyFunction.thatReturns = makeEmptyFunction;\nemptyFunction.thatReturnsFalse = makeEmptyFunction(false);\nemptyFunction.thatReturnsTrue = makeEmptyFunction(true);\nemptyFunction.thatReturnsNull = makeEmptyFunction(null);\nemptyFunction.thatReturnsThis = function () {\n  return this;\n};\nemptyFunction.thatReturnsArgument = function (arg) {\n  return arg;\n};\n\nmodule.exports = emptyFunction;\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n/* WEBPACK VAR INJECTION */(function(process) {/**\n * Copyright (c) 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n */\n\n\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar validateFormat = function validateFormat(format) {};\n\nif (process.env.NODE_ENV !== 'production') {\n  validateFormat = function validateFormat(format) {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  };\n}\n\nfunction invariant(condition, format, a, b, c, d, e, f) {\n  validateFormat(format);\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(format.replace(/%s/g, function () {\n        return args[argIndex++];\n      }));\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n}\n\nmodule.exports = invariant;\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(0)))\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\n\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE_4__;\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _react = __webpack_require__(4);\n\nvar _react2 = _interopRequireDefault(_react);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar ScrollSyncContext = _react2.default.createContext({\n  registerPane: function registerPane() {},\n  unregisterPane: function unregisterPane() {}\n});\n\nexports.default = ScrollSyncContext;\nmodule.exports = exports['default'];\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n/* WEBPACK VAR INJECTION */(function(process) {/**\n * Copyright 2014-2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n */\n\n\n\nvar emptyFunction = __webpack_require__(1);\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar warning = emptyFunction;\n\nif (process.env.NODE_ENV !== 'production') {\n  (function () {\n    var printWarning = function printWarning(format) {\n      for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      var argIndex = 0;\n      var message = 'Warning: ' + format.replace(/%s/g, function () {\n        return args[argIndex++];\n      });\n      if (typeof console !== 'undefined') {\n        console.error(message);\n      }\n      try {\n        // --- Welcome to debugging React ---\n        // This error was thrown as a convenience so that you can use this stack\n        // to find the callsite that caused this warning to fire.\n        throw new Error(message);\n      } catch (x) {}\n    };\n\n    warning = function warning(condition, format) {\n      if (format === undefined) {\n        throw new Error('`warning(condition, format, ...args)` requires a warning ' + 'message argument');\n      }\n\n      if (format.indexOf('Failed Composite propType: ') === 0) {\n        return; // Ignore CompositeComponent proptype check.\n      }\n\n      if (!condition) {\n        for (var _len2 = arguments.length, args = Array(_len2 > 2 ? _len2 - 2 : 0), _key2 = 2; _key2 < _len2; _key2++) {\n          args[_key2 - 2] = arguments[_key2];\n        }\n\n        printWarning.apply(undefined, [format].concat(args));\n      }\n    };\n  })();\n}\n\nmodule.exports = warning;\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(0)))\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/* WEBPACK VAR INJECTION */(function(process) {/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var REACT_ELEMENT_TYPE = (typeof Symbol === 'function' &&\n    Symbol.for &&\n    Symbol.for('react.element')) ||\n    0xeac7;\n\n  var isValidElement = function(object) {\n    return typeof object === 'object' &&\n      object !== null &&\n      object.$$typeof === REACT_ELEMENT_TYPE;\n  };\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = __webpack_require__(13)(isValidElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = __webpack_require__(12)();\n}\n\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(0)))\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _react = __webpack_require__(4);\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _propTypes = __webpack_require__(7);\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _ScrollSyncContext = __webpack_require__(5);\n\nvar _ScrollSyncContext2 = _interopRequireDefault(_ScrollSyncContext);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n/**\n * ScrollSync provider component\n *\n */\n\nvar ScrollSync = function (_Component) {\n  _inherits(ScrollSync, _Component);\n\n  function ScrollSync() {\n    var _ref;\n\n    var _temp, _this, _ret;\n\n    _classCallCheck(this, ScrollSync);\n\n    for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return _ret = (_temp = (_this = _possibleConstructorReturn(this, (_ref = ScrollSync.__proto__ || Object.getPrototypeOf(ScrollSync)).call.apply(_ref, [this].concat(args))), _this), _this.panes = {}, _this.registerPane = function (node, groups) {\n      groups.forEach(function (group) {\n        if (!_this.panes[group]) {\n          _this.panes[group] = [];\n        }\n\n        if (!_this.findPane(node, group)) {\n          if (_this.panes[group].length > 0) {\n            _this.syncScrollPosition(_this.panes[group][0], node);\n          }\n          _this.panes[group].push(node);\n        }\n      });\n      _this.addEvents(node, groups);\n    }, _this.unregisterPane = function (node, groups) {\n      groups.forEach(function (group) {\n        if (_this.findPane(node, group)) {\n          _this.removeEvents(node);\n          _this.panes[group].splice(_this.panes[group].indexOf(node), 1);\n        }\n      });\n    }, _this.addEvents = function (node, groups) {\n      /* For some reason element.addEventListener doesnt work with document.body */\n      node.onscroll = _this.handlePaneScroll.bind(_this, node, groups); // eslint-disable-line\n    }, _this.removeEvents = function (node) {\n      /* For some reason element.removeEventListener doesnt work with document.body */\n      node.onscroll = null; // eslint-disable-line\n    }, _this.findPane = function (node, group) {\n      if (!_this.panes[group]) {\n        return false;\n      }\n\n      return _this.panes[group].find(function (pane) {\n        return pane === node;\n      });\n    }, _this.handlePaneScroll = function (node, groups) {\n      if (!_this.props.enabled) {\n        return;\n      }\n\n      window.requestAnimationFrame(function () {\n        _this.syncScrollPositions(node, groups);\n      });\n    }, _this.syncScrollPositions = function (scrolledPane, groups) {\n      groups.forEach(function (group) {\n        _this.panes[group].forEach(function (pane) {\n          /* For all panes beside the currently scrolling one */\n          if (scrolledPane !== pane) {\n            /* Remove event listeners from the node that we'll manipulate */\n            _this.removeEvents(pane, group);\n            _this.syncScrollPosition(scrolledPane, pane);\n            /* Re-attach event listeners after we're done scrolling */\n            window.requestAnimationFrame(function () {\n              var paneGroups = Object.keys(_this.panes).filter(function (paneGroup) {\n                return _this.panes[paneGroup].includes(pane);\n              });\n              _this.addEvents(pane, paneGroups);\n            });\n          }\n        });\n      });\n      if (_this.props.onSync) _this.props.onSync(scrolledPane);\n    }, _temp), _possibleConstructorReturn(_this, _ret);\n  }\n\n  _createClass(ScrollSync, [{\n    key: 'getContextValue',\n    value: function getContextValue() {\n      return {\n        registerPane: this.registerPane,\n        unregisterPane: this.unregisterPane\n      };\n    }\n  }, {\n    key: 'syncScrollPosition',\n    value: function syncScrollPosition(scrolledPane, pane) {\n      var scrollTop = scrolledPane.scrollTop,\n          scrollHeight = scrolledPane.scrollHeight,\n          clientHeight = scrolledPane.clientHeight,\n          scrollLeft = scrolledPane.scrollLeft,\n          scrollWidth = scrolledPane.scrollWidth,\n          clientWidth = scrolledPane.clientWidth;\n\n\n      var scrollTopOffset = scrollHeight - clientHeight;\n      var scrollLeftOffset = scrollWidth - clientWidth;\n\n      var _props = this.props,\n          proportional = _props.proportional,\n          vertical = _props.vertical,\n          horizontal = _props.horizontal;\n\n      /* Calculate the actual pane height */\n\n      var paneHeight = pane.scrollHeight - clientHeight;\n      var paneWidth = pane.scrollWidth - clientWidth;\n      /* Adjust the scrollTop position of it accordingly */\n      if (vertical && scrollTopOffset > 0) {\n        pane.scrollTop = proportional ? paneHeight * scrollTop / scrollTopOffset : scrollTop; // eslint-disable-line\n      }\n      if (horizontal && scrollLeftOffset > 0) {\n        pane.scrollLeft = proportional ? paneWidth * scrollLeft / scrollLeftOffset : scrollLeft; // eslint-disable-line\n      }\n    }\n  }, {\n    key: 'render',\n    value: function render() {\n      return _react2.default.createElement(\n        _ScrollSyncContext2.default.Provider,\n        { value: this.getContextValue() },\n        _react2.default.Children.only(this.props.children)\n      );\n    }\n  }]);\n\n  return ScrollSync;\n}(_react.Component);\n\nScrollSync.propTypes = {\n  /**\n   * Callback to be invoked any time synchronization happens\n   *\n   * @param {Element} el The element that has received the scroll event\n   */\n  onSync: _propTypes2.default.func,\n  children: _propTypes2.default.element.isRequired,\n  proportional: _propTypes2.default.bool,\n  vertical: _propTypes2.default.bool,\n  horizontal: _propTypes2.default.bool,\n  enabled: _propTypes2.default.bool\n};\nScrollSync.defaultProps = {\n  proportional: true,\n  vertical: true,\n  horizontal: true,\n  enabled: true\n};\nexports.default = ScrollSync;\nmodule.exports = exports['default'];\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _react = __webpack_require__(4);\n\nvar _propTypes = __webpack_require__(7);\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _ScrollSyncContext = __webpack_require__(5);\n\nvar _ScrollSyncContext2 = _interopRequireDefault(_ScrollSyncContext);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } /* eslint react/no-find-dom-node: 0 */\n\n/**\n * ScrollSyncPane Component\n *\n * Wrap your content in it to keep its scroll position in sync with other panes\n *\n * @example ./example.md\n */\n\nvar ScrollSyncPane = function (_Component) {\n  _inherits(ScrollSyncPane, _Component);\n\n  function ScrollSyncPane(props) {\n    _classCallCheck(this, ScrollSyncPane);\n\n    var _this = _possibleConstructorReturn(this, (ScrollSyncPane.__proto__ || Object.getPrototypeOf(ScrollSyncPane)).call(this, props));\n\n    _this.toArray = function (groups) {\n      return [].concat(groups);\n    };\n\n    _this.updateNode = function () {\n      if (_this.props.attachTo) {\n        _this.node = _this.props.attachTo.current;\n      } else {\n        _this.node = _this.childRef.current;\n      }\n    };\n\n    _this.childRef = props.innerRef ? props.innerRef : (0, _react.createRef)();\n    return _this;\n  }\n\n  _createClass(ScrollSyncPane, [{\n    key: 'componentDidMount',\n    value: function componentDidMount() {\n      if (this.props.enabled) {\n        this.updateNode();\n        if (this.node) {\n          this.context.registerPane(this.node, this.toArray(this.props.group));\n        }\n      }\n    }\n  }, {\n    key: 'componentDidUpdate',\n    value: function componentDidUpdate(prevProps) {\n      if (this.props.attachTo !== prevProps.attachTo) {\n        if (this.node) {\n          this.context.unregisterPane(this.node, this.toArray(prevProps.group));\n        }\n        this.updateNode();\n        if (this.node) {\n          this.context.registerPane(this.node, this.toArray(prevProps.group));\n        }\n      }\n      if (this.node && this.props.enabled !== prevProps.enabled) {\n        if (this.props.enabled) {\n          this.context.registerPane(this.node, this.toArray(prevProps.group));\n        } else {\n          this.context.unregisterPane(this.node, this.toArray(prevProps.group));\n        }\n      }\n      if (this.node && this.props.enabled && this.props.group !== prevProps.group) {\n        this.context.unregisterPane(this.node, this.toArray(prevProps.group));\n        this.context.registerPane(this.node, this.toArray(this.props.group));\n      }\n    }\n  }, {\n    key: 'componentWillUnmount',\n    value: function componentWillUnmount() {\n      if (this.node && this.props.enabled) {\n        this.context.unregisterPane(this.node, this.toArray(this.props.group));\n      }\n    }\n  }, {\n    key: 'render',\n    value: function render() {\n      if (this.props.attachTo) {\n        return this.props.children;\n      }\n      return (0, _react.cloneElement)(_react.Children.only(this.props.children), {\n        ref: this.childRef\n      });\n    }\n  }]);\n\n  return ScrollSyncPane;\n}(_react.Component);\n\nScrollSyncPane.contextType = _ScrollSyncContext2.default;\nScrollSyncPane.propTypes = {\n  children: _propTypes2.default.node.isRequired,\n  attachTo: _propTypes2.default.oneOfType([_propTypes2.default.func, _propTypes2.default.shape({ current: _propTypes2.default.any })]),\n  group: _propTypes2.default.oneOfType([_propTypes2.default.string, _propTypes2.default.arrayOf(_propTypes2.default.string)]),\n  enabled: _propTypes2.default.bool,\n  innerRef: _propTypes2.default.oneOfType([// Either a function\n  _propTypes2.default.func, _propTypes2.default.shape({ current: _propTypes2.default.any })])\n};\nScrollSyncPane.defaultProps = {\n  group: 'default',\n  enabled: true\n};\nexports.default = ScrollSyncPane;\nmodule.exports = exports['default'];\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _ScrollSync = __webpack_require__(8);\n\nObject.defineProperty(exports, 'ScrollSync', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_ScrollSync).default;\n  }\n});\n\nvar _ScrollSyncPane = __webpack_require__(9);\n\nObject.defineProperty(exports, 'ScrollSyncPane', {\n  enumerable: true,\n  get: function get() {\n    return _interopRequireDefault(_ScrollSyncPane).default;\n  }\n});\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n/* WEBPACK VAR INJECTION */(function(process) {/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\n\n\nif (process.env.NODE_ENV !== 'production') {\n  var invariant = __webpack_require__(2);\n  var warning = __webpack_require__(6);\n  var ReactPropTypesSecret = __webpack_require__(3);\n  var loggedTypeFailures = {};\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (typeSpecs.hasOwnProperty(typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          invariant(typeof typeSpecs[typeSpecName] === 'function', '%s: %s type `%s` is invalid; it must be a function, usually from ' + 'React.PropTypes.', componentName || 'React class', location, typeSpecName);\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        warning(!error || error instanceof Error, '%s: type specification of %s `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error);\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          warning(false, 'Failed %s type: %s%s', location, error.message, stack != null ? stack : '');\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = checkPropTypes;\n\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(0)))\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\n\n\nvar emptyFunction = __webpack_require__(1);\nvar invariant = __webpack_require__(2);\nvar ReactPropTypesSecret = __webpack_require__(3);\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    invariant(\n      false,\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim\n  };\n\n  ReactPropTypes.checkPropTypes = emptyFunction;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n/* WEBPACK VAR INJECTION */(function(process) {/**\n * Copyright 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\n\n\nvar emptyFunction = __webpack_require__(1);\nvar invariant = __webpack_require__(2);\nvar warning = __webpack_require__(6);\n\nvar ReactPropTypesSecret = __webpack_require__(3);\nvar checkPropTypes = __webpack_require__(11);\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message) {\n    this.message = message;\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          invariant(\n            false,\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            warning(\n              false,\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `%s` prop on `%s`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.',\n              propFullName,\n              componentName\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunction.thatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      process.env.NODE_ENV !== 'production' ? warning(false, 'Invalid argument supplied to oneOf, expected an instance of array.') : void 0;\n      return emptyFunction.thatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues);\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + propValue + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (propValue.hasOwnProperty(key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? warning(false, 'Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunction.thatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        warning(\n          false,\n          'Invalid argument supplid to oneOfType. Expected an array of check functions, but ' +\n          'received %s at index %s.',\n          getPostfixForTypeWarning(checker),\n          i\n        );\n        return emptyFunction.thatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        if (checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret) == null) {\n          return null;\n        }\n      }\n\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (!checker) {\n          continue;\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n\n/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(0)))\n\n/***/ })\n/******/ ]);\n});\n//# sourceMappingURL=index.js.map", "import {useEffect as $Vsl8o$useEffect} from \"react\";\n\n\nfunction $9daab02d461809db$var$hasResizeObserver() {\n    return typeof window.ResizeObserver !== 'undefined';\n}\nfunction $9daab02d461809db$export$683480f191c0e3ea(options) {\n    const { ref: ref, box: box, onResize: onResize } = options;\n    (0, $Vsl8o$useEffect)(()=>{\n        let element = ref === null || ref === void 0 ? void 0 : ref.current;\n        if (!element) return;\n        if (!$9daab02d461809db$var$hasResizeObserver()) {\n            window.addEventListener('resize', onResize, false);\n            return ()=>{\n                window.removeEventListener('resize', onResize, false);\n            };\n        } else {\n            const resizeObserverInstance = new window.ResizeObserver((entries)=>{\n                if (!entries.length) return;\n                onResize();\n            });\n            resizeObserverInstance.observe(element, {\n                box: box\n            });\n            return ()=>{\n                if (element) resizeObserverInstance.unobserve(element);\n            };\n        }\n    }, [\n        onResize,\n        ref,\n        box\n    ]);\n}\n\n\nexport {$9daab02d461809db$export$683480f191c0e3ea as useResizeObserver};\n//# sourceMappingURL=useResizeObserver.module.js.map\n"], "names": ["useMemoOne", "getResult", "inputs", "initial", "useState", "result", "isFirstRun", "useRef", "committed", "cache", "current", "Boolean", "newInputs", "lastInputs", "length", "i", "areInputsEqual", "useEffect", "useMemo", "useCallback", "callback", "prefix", "getRect", "_ref", "top", "right", "bottom", "left", "width", "height", "x", "y", "center", "expand", "target", "expandBy", "shrink", "shrinkBy", "noSpacing", "createBox", "_ref2", "borderBox", "_ref2$margin", "margin", "_ref2$border", "border", "_ref2$padding", "padding", "marginBox", "paddingBox", "contentBox", "parse", "raw", "value", "slice", "Number", "isNaN", "condition", "Error", "invariant", "offset", "original", "change", "shiftBy", "shifted", "withScroll", "scroll", "window", "pageXOffset", "pageYOffset", "calculateBox", "styles", "marginTop", "marginRight", "marginBottom", "marginLeft", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "borderLeftWidth", "getBox", "el", "getBoundingClientRect", "getComputedStyle", "safeIsNaN", "isEqual", "first", "second", "memoizeOne", "resultFn", "memoized", "newArgs", "_i", "arguments", "lastThis", "this", "lastArgs", "lastResult", "apply", "clear", "fn", "frameId", "wrapperFn", "_len", "args", "Array", "_key", "requestAnimationFrame", "cancel", "cancelAnimationFrame", "_extends", "Object", "assign", "bind", "n", "e", "t", "r", "hasOwnProperty", "call", "log", "type", "message", "noop$2", "bindEvents", "bindings", "sharedOptions", "unbindings", "map", "binding", "options", "shared", "fromBinding", "getOptions", "addEventListener", "eventName", "removeEventListener", "for<PERSON>ach", "unbind", "prefix$1", "RbdInvariant", "prototype", "toString", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "super", "callbacks", "onWindowError", "event", "getCallbacks", "isDragging", "tryAbort", "error", "preventDefault", "setCallbacks", "componentDidMount", "componentDidCatch", "err", "setState", "componentWillUnmount", "render", "props", "children", "position", "index", "withLocation", "source", "destination", "isInHomeList", "droppableId", "startPosition", "endPosition", "<PERSON><PERSON><PERSON><PERSON>", "id", "combine", "draggableId", "returnedToStart", "preset", "dragHandleUsageInstructions", "onDragStart", "start", "onDragUpdate", "update", "location", "onDragEnd", "reason", "preset$1", "origin", "add", "point1", "point2", "subtract", "isEqual$1", "negate", "point", "patch", "line", "otherValue", "distance", "Math", "sqrt", "closest$1", "points", "min", "offsetByPosition", "spacing", "getCorners", "clip", "frame", "shouldClipSubject", "subject", "max", "executeClip", "pageMarginBox", "getSubject", "page", "withPlaceholder", "axis", "scrolled", "diff", "displacement", "scroll$1", "increased", "increasedBy", "end", "increase", "active", "scrollDroppable", "droppable", "newScroll", "scrollable", "scrollDiff", "scrollDisplacement", "toDroppableMap", "droppables", "reduce", "previous", "descriptor", "toDraggableMap", "draggables", "toDroppableList", "values", "toDraggableList", "getDraggablesInsideDroppable", "filter", "draggable", "sort", "a", "b", "tryGetDestination", "impact", "at", "tryGetCombine", "removeDraggableFromList", "remove", "list", "item", "isHomeOf", "noDisplacedBy", "emptyGroups", "invisible", "visible", "all", "noImpact$1", "displaced", "displacedBy", "<PERSON><PERSON><PERSON><PERSON>", "lowerBound", "upperBound", "isPartiallyVisibleThroughFrame", "isWithinVertical", "isWithinHorizontal", "isPartiallyVisibleVertically", "isPartiallyVisibleHorizontally", "isBiggerVertically", "isBiggerHorizontally", "isTotallyVisibleThroughFrame", "vertical", "direction", "crossAxisLine", "size", "crossAxisStart", "crossAxisEnd", "crossAxisSize", "horizontal", "isVisible$1", "toBeDisplaced", "viewport", "withDroppableDisplacement", "isVisibleThroughFrameFn", "<PERSON><PERSON><PERSON><PERSON>", "getDroppableDisplaced", "isVisibleInDroppable", "isVisibleInViewport", "isTotallyVisible", "getDisplacementGroups", "afterDragging", "forceShouldAnimate", "last", "groups", "get<PERSON><PERSON><PERSON>", "push", "shouldAnimate", "getShouldAnimate", "goAtEnd", "insideDestination", "inHomeList", "newIndex", "indexOfLastItem", "getIndexOfLastItem", "calculateReorderImpact", "match", "find", "withoutDragging", "sliceFrom", "indexOf", "didStartAfterCritical", "afterCritical", "effected", "moveToNextIndex", "isMovingForward", "previousImpact", "wasAt", "currentIndex", "proposedIndex", "firstIndex", "lastIndex", "fromReorder", "isCombineEnabled", "combineId", "combineWithIndex", "fromCombine", "whenCombining", "combineWith", "displaceBy", "isDisplaced", "getCombinedItemDisplacement", "distanceFromStartToBorderBoxCenter", "box", "getCrossAxisBorderBoxCenter", "isMoving", "goAfter", "moveRelativeTo", "goBefore", "distanceFromEndToBorderBoxCenter", "whenReordering", "draggablePage", "moveInto", "goIntoStart", "closestAfter", "closest", "withDisplacement", "getPageBorderBoxCenterFromImpact", "withoutDisplacement", "getResultWithoutDroppableDisplacement", "scrollViewport", "getDraggables$1", "ids", "getClientFromPageBorderBoxCenter", "pageBorderBoxCenter", "withoutPageScrollChange", "withViewportDisplacement", "client", "isTotallyVisibleInNewLocation", "newPageBorderBoxCenter", "onlyOnMainAxis", "changeNeeded", "isTotallyVisibleOnAxis", "moveToNextPlace", "previousPageBorderBoxCenter", "previousClientSelection", "isEnabled", "getImpact", "closestId", "withoutDraggable", "indexOfClosest", "findIndex", "d", "moveToNextCombine", "clientSelection", "scrollJumpRequest", "cautious", "maxScroll<PERSON>hange", "scrolledViewport", "scrolledDroppable", "withViewportScroll", "withDroppableScroll", "tryGetVisible", "speculativelyIncrease", "getKnownActive", "rect", "getCurrentPageBorderBoxCenter", "getCurrentPageBorderBox", "getDisplacedBy", "withMaxScroll", "addPlaceholder", "placeholderSize", "requiredGrowth", "mode", "availableSpace", "needsToGrowBy", "sum", "dimension", "getRequiredGrowthForPlaceholder", "added", "oldFrameMaxScroll", "maxScroll", "newFrame", "moveCrossAxis", "isOver", "isBetweenSourceClipped", "candidates", "activeOfTarget", "isBetweenDestinationClipped", "array", "contains", "isWithinDroppable", "getBestCrossAxisDroppable", "sorted", "distanceToA", "distanceToB", "getClosestDraggable", "proposed", "proposedPageBorderBoxCenter", "isGoingBeforeTarget", "relativeTo", "moveToNewDroppable", "whatIsDraggedOver", "moveInDirection", "state", "isActuallyOver", "getDroppableOver$1", "dimensions", "isMainAxisMovementAllowed", "home", "critical", "isMovingOnMainAxis", "borderBoxCenter", "selection", "isMovementAllowed", "phase", "isPositionInFrame", "getDroppableOver", "pageBorderBox", "childCenter", "isContained", "isStartContained", "isEndContained", "startCenter", "candidate", "getFurthestAway", "offsetRectByPosition", "getIsDisplaced", "getDragImpact", "pageOffset", "destinationId", "pageBorderBoxWithDroppableScroll", "area", "targetRect", "targetStart", "targetEnd", "child", "childRect", "threshold", "didStartAfterCritical$1", "getCombineImpact", "atIndex", "getReorderImpact", "patchDroppableMap", "updated", "clearUnusedPlaceholder", "now", "lastDroppable", "oldMaxScroll", "removePlaceholder", "forcedClientSelection", "forcedDimensions", "forcedViewport", "forcedImpact", "newImpact", "withUpdatedPlaceholders", "cleaned", "patched", "recomputePlaceholders", "recompute", "getDraggables", "getClientBorderBoxCenter", "refreshSnap", "movementMode", "needsVisibilityCheck", "getLiftEffect", "insideHome", "rawIndex", "inVirtualList", "key", "finish", "adjustAdditionsForScrollChanges", "additions", "updatedDroppables", "windowScrollChange", "getFrame", "droppableScrollChange", "moved", "offset$1", "initialWindowScroll", "placeholder", "offsetDraggable", "isSnapping", "postDroppableChange", "isEnabledChanging", "patchDimensionMap", "removeScrollJumpRequest", "idle$2", "completed", "<PERSON><PERSON><PERSON><PERSON>", "reducer", "action", "payload", "isWindowScrollAllowed", "every", "isFixedOnPage", "onLiftImpact", "published", "withScrollChange", "modified", "existing", "updatedAdditions", "removals", "wasOverId", "wasOver", "draggingState", "isWaiting", "publishWhileDraggingInVirtual", "dropDuration", "newHomeClientOffset", "lift$1", "publishWhileDragging", "collectionStarting", "updateDroppableScroll", "updateDroppableIsEnabled", "updateDroppableIsCombineEnabled", "move", "moveUp", "moveDown", "moveRight", "moveLeft", "flush", "completeDrop", "drop$1", "dropAnimationFinished", "curves", "drop", "combining", "timings", "outOfTheWay", "minDropTime", "maxDropTime", "outOfTheWayTiming", "transitions", "fluid", "snap", "duration", "timing", "moveTo", "undefined", "transforms", "isCombining", "translate", "dropTimeRange", "getState", "dispatch", "next", "dropPending", "didDropInsideDroppable", "lastImpact", "getDropImpact", "newClientCenter", "getNewHomeClientOffset", "distance$1", "toFixed", "getDropDuration", "animateDrop", "getScrollListener", "onWindowScroll", "scheduled", "passive", "capture", "document", "getWindowScrollBinding", "isActive", "stop", "scrollListener$1", "store", "listener", "shouldEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entries", "timerId", "setTimeout", "entry", "splice", "execute", "shallow", "clearTimeout", "with<PERSON><PERSON><PERSON>", "getDragStart", "responder", "data", "announce", "getDefaultMessage", "willExpire", "wasCalled", "isExpired", "timeoutId", "getExpiringAnnounce", "responders", "getResponders", "publisher", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragging", "beforeCapture", "onBeforeCapture", "beforeStart", "onBeforeDragStart", "lastCritical", "lastLocation", "lastCombine", "hasCriticalChanged", "isDraggableEqual", "isDroppableEqual", "isCriticalEqual", "hasLocationChanged", "hasGroupingChanged", "isCombineEqual", "abort", "getPublisher", "dropAnimationFinish", "dropAnimationFlushOnScroll", "once", "pendingDrop$1", "postActionState", "composeEnhancers", "compose", "createStore", "dimension<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "style<PERSON><PERSON><PERSON>", "autoScroller", "applyMiddleware", "marshal", "dropping", "resting", "stopPublishing", "dimensionMarshalStopper", "request", "scrollOptions", "shouldPublishImmediately", "startPublishing", "lift", "shouldStop", "autoScroll", "isWatching", "tryRecordFoc<PERSON>", "tryRestoreFocusRecorded", "tryShiftRecord", "getMaxScroll", "scrollHeight", "scrollWidth", "getDocumentElement", "doc", "documentElement", "getMaxWindowScroll", "clientWidth", "clientHeight", "getInitialPublish", "registry", "getViewport", "windowScroll", "getAllByType", "getDimensionAndWatchScroll", "getDimension", "shouldPublishUpdate", "getById", "createDimensionMarshal", "collection", "staging", "collect", "keys", "getScrollWhileDragging", "publish", "createPublisher", "subscriber", "exists", "unsubscribe", "subscribe", "dragStopped", "canStartDrag", "scrollWindow", "scrollBy", "getScrollableDroppables", "getBestScrollableDroppable", "maybe", "getScrollableDroppableOver", "defaultAutoScrollerOptions", "startFromPercentage", "maxScrollAtPercentage", "maxPixelScroll", "ease", "percentage", "durationDampening", "stopDampeningAt", "accelerateAt", "disabled", "getPercentage", "startOfRange", "endOfRange", "range", "getValue", "distanceToEdge", "thresholds", "dragStartTime", "shouldUseTimeDampening", "getAutoScrollerOptions", "autoScrollerOptions", "startScrollingFrom", "maxScrollValueAt", "percentageFromStartScrollingFrom", "ceil", "getValueFromDistance", "proposedScroll", "stopAt", "runTime", "Date", "betweenAccelerateAtAndStopAtPercentage", "dampenValueByTime", "getScrollOnAxis", "container", "distanceToEdges", "getDistanceThresholds", "clean", "getScroll$1", "required", "limited", "isTooBigVertically", "isTooBigHorizontally", "adjustForSizeLimits", "smallestSigned", "getOverlap", "get<PERSON><PERSON><PERSON>", "targetScroll", "overlap", "canPartiallyScroll", "rawMax", "smallestChange", "canScrollWindow", "canScrollDroppable", "getWindowScrollChange", "getDroppableScrollChange", "createJumpScroller", "scrollDroppableAsMuchAsItCan", "getDroppableOverlap", "whatTheDroppableCanScroll", "scrollWindowAsMuchAsItCan", "getWindowOverlap", "whatTheWindowCanScroll", "droppableRemainder", "windowRemainder", "moveByOffset", "createAutoScroller", "fluidScroller", "scheduleWindowScroll", "scheduleDroppableScroll", "tryScroll", "wasScrollNeeded", "fakeScrollCallback", "createFluidScroller", "jumpScroll", "dragHandle", "base", "contextId", "scrollContainer", "getStyles", "rules", "property", "rule", "selector", "join", "getStyles$1", "getSelector", "context", "attribute", "dragHandle$1", "grabCursor", "always", "dropAnimating", "transition", "userCancel", "useLayoutEffect", "createElement", "getHead", "head", "querySelector", "createStyleEl", "nonce", "setAttribute", "querySelectorAll", "parentNode", "from", "getWindowFromEl", "ownerDocument", "defaultView", "isHtmlElement", "HTMLElement", "findDragHandle", "possible", "handle", "getAttribute", "createRegistry", "subscribers", "notify", "cb", "findDraggableById", "findDroppableById", "register", "uniqueId", "unregister", "findById", "StoreContext", "getBodyElement", "body", "visuallyHidden$1", "overflow", "getId", "count$1", "defaults", "separator", "useUniqueId$1", "AppContext", "useDev", "useHook", "useDevSetupWarning", "usePrevious", "ref", "tab", "enter", "pageUp", "pageDown", "<PERSON><PERSON><PERSON><PERSON>", "preventStandardKeyEvents", "keyCode", "supportedPageVisibilityEventName", "idle$1", "getCaptureBindings", "getPhase", "setPhase", "button", "clientX", "clientY", "actions", "pending", "abs", "fluidLift", "shouldBlockNextClick", "shouldRespectForcePress", "noop$1", "scrollJumpKeys", "getDraggingBindings", "idle", "interactiveTagNames", "isAnInteractiveElement", "parent", "includes", "tagName", "toLowerCase", "parentElement", "isEventInInteractiveElement", "getBorderBoxCenterPosition", "supportedMatchesName", "name", "Element", "closestPonyfill", "findClosestDragHandleFromEvent", "expected", "isLockActive", "<PERSON><PERSON><PERSON><PERSON>", "canStart", "lockAPI", "isClaimed", "tryStart", "forceSensorStop", "sourceEvent", "draggable$1", "findDraggable", "canDragInteractiveElements", "lock", "claim", "getShouldRespectForcePress", "tryDispatchWhenDragging", "getAction", "release", "cleanup", "liftActionArgs", "move$1", "snapLift", "defaultSensors", "api", "phaseRef", "unbindEventsRef", "startCaptureBinding", "defaultPrevented", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "findClosestDraggableId", "tryGetLock", "startPendingDrag", "preventForcePressBinding", "findOptionsForDraggable", "canGetLock", "listenForCapture", "bindCapturingEvents", "preDrag", "isCapturing", "touch", "touches", "longPressTimerId", "unbindTarget", "hasMoved", "force", "shouldRespect", "getHandleBindings", "unbind<PERSON><PERSON>ow", "getWindowBindings", "startDragging", "useSensorMarshal", "customSensors", "enableDefaultSensors", "useSensors", "abandon", "newLock", "tryAbandon", "create", "tryAbandonLock", "forceStop", "tryGetClosestDraggableIdFromEvent", "tryReleaseLock", "isLockClaimed", "createResponders", "onBeforeCapureCallback", "startsWith", "flushSync", "createAutoScrollerOptions", "getStore", "lazyRef", "App", "sensors", "lazyStoreRef", "lastPropsRef", "style", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "useAnnouncer", "dragHandleUsageInstructionsId", "text", "getElementId", "display", "useHiddenTextElement", "alwaysRef", "dynamicRef", "setDynamicStyle", "setAlwaysStyle", "dynamic", "useStyleMarshal", "lazyDispatch", "marshalCallbacks", "bindActionCreators", "useRegistry", "entriesRef", "recordRef", "restoreFocusFrameRef", "isMountedRef", "focus", "tryGiveFocus", "tryGiveFocusTo", "activeElement", "redirectTo", "record", "focused", "useFocusMarshal", "tryResetStore", "getCanLift", "getIsMovementAllowed", "appContext", "canLift", "Provider", "count", "useUniqueContextId$1", "DragDropContext", "zIndexOptions", "getDraggingTransition", "shouldAnimateDragMovement", "getDraggingOpacity", "isDropAnimating", "getStyle$1", "mapped", "getShouldDraggingAnimate", "transform", "boxSizing", "opacity", "zIndex", "pointerEvents", "getDraggingStyle", "secondary", "shouldAnimateDisplacement", "useDraggablePublisher", "getDraggableRef", "computedStyles", "getDimension$1", "publishedRef", "isFirstPublishRef", "DroppableContext", "useRequiredContext", "Context", "useContext", "preventHtml5Dnd", "Draggable$1", "setRef", "getRef", "isClone", "dropAnimationFinishedAction", "dragHandleProps", "tabIndex", "role", "onMoveEnd", "propertyName", "provided", "onTransitionEnd", "innerRef", "draggableProps", "rubric", "snapshot", "isStrictEqual", "whatIsDraggedOverFromResult", "getSecondarySnapshot", "combineTargetFor", "dropAnimation", "draggingOver", "atRest", "mapDispatchToProps$1", "ConnectedDraggable", "connect", "draggingSelector", "memoizedOffset", "getMemoizedSnapshot", "getMemoizedProps", "ownProps", "getCombineWithFromResult", "curve", "scale", "getDraggableSelector", "secondarySelector", "get<PERSON>allback", "getProps", "ownId", "draggingId", "visualDisplacement", "isAfterCriticalInVirtualList", "getSecondarySelector", "areStatePropsEqual", "ConnectedDraggable$1", "PrivateDraggable", "isUsingCloneFor", "PublicDraggable", "isDragDisabled", "disableInteractiveElementBlocking", "isScroll", "isAuto", "is<PERSON><PERSON><PERSON>", "overflowX", "overflowY", "isElementScrollable", "getClosestScrollable", "getScroll", "scrollLeft", "scrollTop", "getIsFixed", "env", "isDropDisabled", "closestScrollable", "targetRef", "getClient", "frameClient", "scrollSize", "getDroppableDimension", "immediate", "delayed", "getListenerOptions", "getClosestScrollableFromDrag", "useDroppablePublisher", "whileDraggingRef", "previousRef", "publishedDescriptorRef", "memoizedUpdateScroll", "getClosestScroll", "updateScroll", "scheduleScrollUpdate", "onClosestScroll", "getDroppableRef", "getEnv", "ignoreContainerClipping", "removeAttribute", "noop", "empty", "getStyle", "isAnimatingOpenOnMount", "animate", "getSize", "flexShrink", "flexGrow", "Placeholder$1", "animateOpenTimerRef", "tryClearAnimateOpenTimer", "onClose", "setIsAnimatingOpenOnMount", "onSizeChangeEnd", "AnimateInOut", "isVisible", "on", "getDerivedStateFromProps", "Droppable$1", "droppableRef", "placeholder<PERSON><PERSON>", "useClone", "updateViewportMaxScroll", "getContainerForClone", "setDroppableRef", "setPlaceholderRef", "onPlaceholderTransitionEnd", "shouldAnimatePlaceholder", "droppableProps", "droppableContext", "node", "draggableProvided", "draggableSnapshot", "getClone", "defaultProps", "renderClone", "attachDefaultPropsToOwnProps", "defaultPropKey", "mergedProps", "isMatchingType", "getDraggable", "mapDispatchToProps", "ConnectedDroppable$1", "idleWithAnimation", "isDraggingOver", "draggingOverWith", "draggingFromThisWith", "isUsingPlaceholder", "idleWithoutAnimation", "getDraggableRubric", "getMapProps", "isDraggingOverForConsumer", "isDraggingOverForImpact", "ownPropsWithDefaultProps", "was<PERSON><PERSON><PERSON>", "isHome", "stateProps", "dispatchProps", "extendStatics", "__assign", "s", "p", "rowSizeBase", "cursor", "colSizeBase", "edgeBase", "topRight", "bottomRight", "bottomLeft", "topLeft", "Resizer", "memo", "onResizeStart", "replaceStyles", "className", "onMouseDown", "onTouchStart", "userSelect", "__extends", "setPrototypeOf", "__proto__", "TypeError", "String", "__", "DEFAULT_SIZE", "clamp", "gridGap", "v", "round", "hasDirection", "dir", "RegExp", "test", "isTouchEvent", "findClosestSnap", "snapArray", "snapGap", "closestGapIndex", "prev", "curr", "gap", "getStringSize", "endsWith", "concat", "getPixelSize", "parentSize", "innerWidth", "innerHeight", "replace", "definedProps", "baseClassName", "Resizable", "_super", "_a", "_b", "_c", "_d", "_this", "ratio", "resizable", "parentLeft", "parentTop", "resizableLeft", "resizableRight", "resizableTop", "resizableBottom", "targetLeft", "targetTop", "delta", "appendBase", "element", "flex", "classList", "removeBase", "isResizing", "propsSize", "backgroundStyle", "backgroundColor", "flexBasis", "onMouseMove", "onMouseUp", "defineProperty", "get", "enumerable", "configurable", "defaultSize", "orgWidth", "offsetWidth", "orgHeight", "offsetHeight", "orgPosition", "getParentSize", "percent", "wrapChanged", "wrap", "flexWrap", "min<PERSON><PERSON><PERSON>", "minHeight", "unbindEvents", "computedStyle", "createSizeForCssProperty", "newSize", "kind", "calculateNewMaxFromBoundary", "max<PERSON><PERSON><PERSON>", "maxHeight", "boundWidth", "boundHeight", "boundsByDirection", "widthByDirection", "heightByDirection", "bounds", "parent_1", "isFinite", "calculateNewSizeFromDirection", "val", "resizeRatio", "isArray", "resizeRatioX", "resizeRatioY", "lockAspectRatio", "lockAspectRatioExtraHeight", "lockAspectRatioExtraWidth", "newWidth", "newHeight", "extraHeight", "extraWidth", "calculateNewSizeFromAspectRatio", "computedMinWidth", "computedMaxWidth", "computedMinHeight", "computedMaxHeight", "extraMinWidth", "extraMaxWidth", "extraMinHeight", "extraMaxHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lockedMaxWidth", "lockedMinHeight", "lockedMaxHeight", "setBoundingClientRect", "adjustedScale", "parent_2", "parentRect", "top_1", "nativeEvent", "isMouseEvent", "parent_3", "flexDirection", "flexDir", "TouchEvent", "stopPropagation", "calculateNewMax", "boundaryMax", "grid", "newGridWidth", "newGridHeight", "vw", "vh", "newState", "widthChanged", "heightChanged", "flexBaseChanged", "changed", "onResize", "onResizeStop", "updateSize", "renderResizer", "enable", "handleStyles", "handleClasses", "handleWrapperStyle", "handleWrapperClass", "handleComponent", "resizers", "extendsProps", "acc", "sizeStyle", "Wrapper", "as", "c", "PureComponent", "factory", "__WEBPACK_EXTERNAL_MODULE_4__", "modules", "installedModules", "moduleId", "exports", "module", "l", "m", "getter", "o", "__esModule", "object", "cachedSetTimeout", "cachedClearTimeout", "process", "defaultSetTimout", "defaultClearTimeout", "runTimeout", "fun", "currentQueue", "queue", "draining", "queueIndex", "cleanUpNextTick", "drainQueue", "timeout", "len", "run", "marker", "runClearTimeout", "<PERSON><PERSON>", "nextTick", "title", "browser", "argv", "version", "versions", "addListener", "off", "removeListener", "removeAllListeners", "emit", "prependListener", "prependOnceListener", "listeners", "cwd", "chdir", "umask", "__webpack_require__", "makeEmptyFunction", "arg", "emptyFunction", "thatReturns", "thatReturnsFalse", "thatReturnsTrue", "thatReturnsNull", "thatReturnsThis", "thatReturnsArgument", "validateFormat", "format", "NODE_ENV", "f", "argIndex", "framesToPop", "obj", "_react", "ScrollSyncContext", "default", "createContext", "registerPane", "unregisterPane", "printWarning", "warning", "console", "_len2", "_key2", "REACT_ELEMENT_TYPE", "Symbol", "for", "$$typeof", "_createClass", "defineProperties", "writable", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_react2", "_interopRequireDefault", "_propTypes2", "_ScrollSyncContext2", "_possibleConstructorReturn", "self", "ReferenceError", "ScrollSync", "_Component", "_temp", "instance", "_classCallCheck", "getPrototypeOf", "panes", "group", "findPane", "syncScrollPosition", "addEvents", "removeEvents", "onscroll", "handlePaneScroll", "pane", "enabled", "syncScrollPositions", "scrolledPane", "paneGroups", "paneGroup", "onSync", "subClass", "superClass", "_inherits", "scrollTopOffset", "scrollLeftOffset", "_props", "proportional", "paneHeight", "paneWidth", "getContextValue", "Children", "only", "Component", "propTypes", "func", "isRequired", "bool", "ScrollSyncPane", "toArray", "updateNode", "attachTo", "childRef", "createRef", "prevProps", "cloneElement", "contextType", "oneOfType", "shape", "any", "string", "arrayOf", "_ScrollSync", "_ScrollSyncPane", "ReactPropTypesSecret", "loggedTypeFailures", "typeSpecs", "componentName", "getStack", "typeSpecName", "ex", "stack", "shim", "propName", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "getShim", "ReactPropTypes", "number", "symbol", "instanceOf", "objectOf", "oneOf", "checkPropTypes", "PropTypes", "isValidElement", "throwOnDirectAccess", "ITERATOR_SYMBOL", "iterator", "ANONYMOUS", "createPrimitiveTypeChecker", "createChainableTypeChecker", "typeC<PERSON>cker", "PropTypeError", "propValue", "getPropType", "expectedClass", "expectedClassName", "isNode", "propType", "expectedV<PERSON>ues", "is", "JSON", "stringify", "arrayOfTypeCheckers", "checker", "getPostfixForTypeWarning", "shapeTypes", "validate", "manualPropTypeCallCache", "manualPropTypeWarningCount", "checkType", "cache<PERSON>ey", "chainedCheckType", "expectedType", "getPreciseType", "iteratorFn", "maybeIterable", "getIteratorFn", "step", "done", "isSymbol", "$9daab02d461809db$export$683480f191c0e3ea", "ResizeObserver", "resizeObserverInstance", "observe", "unobserve"], "sourceRoot": ""}