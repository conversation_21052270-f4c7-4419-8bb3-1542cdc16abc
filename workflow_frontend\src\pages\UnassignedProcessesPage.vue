<template>
  <q-page>
    <div class="row" style="justify-content: space-between; align-items: center; padding-bottom: 30px; padding-top: 30px;">
      <div style="font-size: 30px; font-weight: 500;">Weitere Prozesse</div>
    </div>
    <div class="q-py-md">
      Hier werden Prozesse/Jobs gelistet, welche keinem Asservat und/oder Fall zugewiesen werden konnten. <br>
      Die Liste beschränkt sich auf Prozesse, die innerhalb der letzten {{daysLimit}} Tage aktualisiert wurden.
    </div>
    <q-card class="q-pa-md">
      <div class="row tableTopBtnRow no-wrap">
        <q-btn icon="refresh" label="Aktualisieren" @click="fetchUnassignedProcesses" />
      </div>

      <q-table
        :rows="filteredProcesses"
        row-key="process_id"
        :columns="processColumns"
        no-data-label="Keine Prozesse mit diesen Kriterien vorhanden."
        dense flat bordered
        :rows-per-page-options="[0, 20, 50, 100]"
      >
        <!-- Custom Header Slot -->
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th v-for="col in props.cols" :key="col.name" :props="props" style="font-size: 13px">
              {{ col.label }}
            </q-th>
          </q-tr>
          <q-tr>
            <q-th v-for="col in props.cols" :key="col.name">
              <q-input v-if="filterDict[col.name] !== undefined"
                       class="tableHeaderSearchInput" debounce="300" borderless
                       v-model="filterDict[col.name]" placeholder="Suchen"
                       @update:model-value="filterProcesses">
                <template v-slot:append>
                  <q-icon name="search"/>
                </template>
              </q-input>
            </q-th>
          </q-tr>
        </template>

        <!-- Case Name Column (Highlight if missing) -->
        <template v-slot:body-cell-case_name="props">
          <q-td>
            <div v-if="props.row.case_name">
              {{ props.row.case_name }}
              <q-tooltip>Fall ID: {{ props.row.case_id }}</q-tooltip>
            </div>
            <q-badge v-else color="red-4">Kein Fall zugewiesen</q-badge>
          </q-td>
        </template>

        <!-- Evidence Item Name Column (Highlight if missing) -->
        <template v-slot:body-cell-evidence_item_name="props">
          <q-td>
            <div v-if="props.row.evidence_item_name">
              {{ props.row.evidence_item_name }}
              <q-tooltip>Asservat ID: {{ props.row.evidence_item_id }}</q-tooltip>
            </div>
            <q-badge v-else color="red-4">Kein Asservat zugewiesen</q-badge>
          </q-td>
        </template>

        <template v-slot:body-cell-step_id="props">
          <q-td>
            <div>{{ getForensicStepDisplayString(props.row.step_id) }}</div>
          </q-td>
        </template>

        <template v-slot:body-cell-status="props">
          <q-td>
            <div>
              <q-badge
                  :label="getProcessStatusDisplayString(props.row.status)"
                  :color="getProcessStatusColor(props.row.status)">
                <q-tooltip anchor="center end" self="center left">
                  Prozess ID: {{ props.row.process_id }}
                </q-tooltip>
              </q-badge>
            </div>
          </q-td>
        </template>

      </q-table>
    </q-card>
  </q-page>
</template>

<script setup>
import {ref, onMounted, onBeforeMount, computed} from "vue";
import { Notify } from "quasar";
import {
  getProcessStatusColor,
  getProcessStatusDisplayString,
} from "src/utils/processes_utils.js";

const workflowApiUrl = computed(() => {
  let url = new URL(window.location.toString());
  url.port = '8000';
  url.hash = ''
  url.pathname = '/'
  // remove ending '/' (appending path segments later would fail otherwise)
  return url.toString().replace(/\/$/, '');
});

const processes = ref([]);
const filteredProcesses = ref([]);

const processColumns = [
  // { name: "process_id", label: "Prozess-ID", field: "process_id", align: "left", sortable: true },
  { name: "case_name", label: "Fallname", field: "case_name", align: "left", sortable: true },
  { name: "evidence_item_name", label: "Asservatname", field: "evidence_item_name", align: "left", sortable: true },
  { name: "step_id", label: "Schritt", field: "step_id", align: "left", sortable: true },
  { name: "status", label: "Status", field: "status", align: "left", sortable: true },
  { name: "created_at", label: "Erstellt am", field: "created_at", align: "left", sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
  { name: "started_running_at", label: "Start am", field: "started_running_at", align: "left", sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
  { name: "ended_at", label: "Ende am", field: "ended_at", align: "left", sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
  { name: "updated_at", label: "Letzte Aktualisierung am", field: "updated_at", align: "left", sortable: true,
    format: (val, row) => val != null ? new Date(val).toLocaleString('de-DE') : null},
];

const filterDict = ref({
  evidence_item_name: "",
  case_name: "",
  step_id: "",
  status: "",
});

const daysLimit = 14; //in days
const fetchUnassignedProcesses = async () => {
  try {
    let url = workflowApiUrl.value + "/processes/unassigned"
    // add parameter to limit processes to within the last 14 days
    const d = new Date()
    d.setDate(d.getDate() - daysLimit)
    url += `?date_threshold=${d.toISOString()}`

    console.log("GET " + url);
    const response = await fetch(url, { method: 'GET' });
    if (!response.ok) throw new Error('Fehler beim Abrufen der Prozesse');

    const data = await response.json();
    processes.value = data;
    filteredProcesses.value = [...processes.value];
  } catch (error) {
    console.error("Fehler beim Abrufen unzugewiesener Prozesse:", error);
    Notify.create({ message: "Fehler beim Abrufen der Prozesse", type: "negative", timeout: 5000 });
  }
};

// Filter processes
const filterProcesses = () => {
  filteredProcesses.value = processes.value.filter((item) =>
    Object.keys(filterDict.value).every((key) => {

      let fieldValue = "";
      if (item[key] !== undefined && item[key] !== null) {
        if (key === "status") {
          fieldValue = getProcessStatusDisplayString(item[key]).toString().toLowerCase();
        } else if (key === "step_id") {
          fieldValue = getForensicStepDisplayString(item[key]).toString().toLowerCase();
        } else {
          fieldValue = item[key].toString().toLowerCase();
        }
      }
      return fieldValue.includes(filterDict.value[key].toLowerCase());
    })
  );
};

function getForensicStepDisplayString(stepId){
  return forensicStepsDict[stepId]["display_name"];
}

const forensicStepsDict = ref({});
async function fetchForensicSteps() {
  try {
    // fetch evidence items
    const forensicStepsUrl = `${workflowApiUrl.value}/forensic-steps`;
    console.log("GET " + forensicStepsUrl);
    const responseForensicSteps = await fetch(forensicStepsUrl, {method: 'GET'});
    if (!responseForensicSteps.ok) throw new Error('Failed to fetch forensic steps.');


    const forensicStepsList = await responseForensicSteps.json();
    forensicStepsList.forEach(step => {
      forensicStepsDict[step['id']] = step;
    });

  } catch (err) {
    console.log(err)
    Notify.create({message: 'Fehler beim Abrufen der Schritte im ITF-Workflow', type: 'negative', timeout: 10000});
  }
}

onBeforeMount(async () => {
  // fetch step names (used as column headers)
  await fetchForensicSteps();
})

onMounted(fetchUnassignedProcesses);
</script>

<style scoped>
.q-table {
  margin-top: 20px;
  width: 100%;
}

.q-btn {
  text-transform: none;
}

.tableHeaderSearchInput :deep(.q-field__control), :deep(.q-field__append) {
  height: 22px;
  padding: 0px;
  font-size: 13px;
}

.tableTopBtnRow {
  margin: 0px 0px 15px 0px;

  .q-btn {
    float: right;
    width: auto;
    height: 38px;
    background-color: #be1717;
    color: white;
    margin-right: 5px;
  }
}
</style>
