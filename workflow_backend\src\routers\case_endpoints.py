import logging
from datetime import datetime
from typing import Optional

from fastapi import Query, HTTPException, APIRouter

from workflow_backend.src.database.database_helpers import run_query
from workflow_backend.src.database.get_case_progress import get_case_progress_from_database
from workflow_backend.src.models.cases_models import CreateCasePayload

case_router = APIRouter(prefix="/cases", tags=["cases"])


@case_router.get("")
async def get_cases(case_name: Optional[str] = Query(None, description="Filter for the given case name"),
                    reference_number: Optional[str] = Query(None, description="Filter for the given reference number (Aktenzeichen or Geschäftszeichen)"),
                    client: Optional[str] = Query(None, description="Filter for the given client (Auftraggeber)"),
                    ):
    """ Get all cases. Optionally, filter by case name, reference number or client."""
    where_clause_list = []
    if reference_number is not None:
        where_clause_list.append("reference_number = :reference_number")
    if case_name is not None:
        where_clause_list.append("case_name = :case_name")
    if client is not None:
        where_clause_list.append("client = :client")
    if len(where_clause_list) > 0:
        where_clause = "WHERE " + " AND ". join(where_clause_list)
    else:
        where_clause = ""
    result = run_query(f"SELECT * FROM cases {where_clause} ORDER BY id",
                       {"reference_number": reference_number, "case_name": case_name, "client": client})
    return result


@case_router.get("/{case_id}")
async def get_case(case_id: int):
    """
    Get case with the given id.
    """
    # TODO: prevent sql injection
    query = "SELECT * FROM cases WHERE id = :case_id;"
    result = run_query(query, {"case_id": case_id})
    if result is None or len(result) == 0:
        raise HTTPException(status_code=404, detail=f"Case with id {case_id} not found.")
    return result[0]


@case_router.post("")
async def add_case(case: CreateCasePayload):
    """
    Add a new case.
    """
    intake = datetime.fromtimestamp(case.intake_time_in_ms/1000)
    logging.info(f"Inserting new case ({case}) ...")
    query = """INSERT INTO cases (case_name, reference_number, client, intake_at) 
                VALUES (:case_name, :reference_number, :client, :intake_at) 
                RETURNING *;"""
    result = run_query(query, {
        "case_name": case.case_name,
        "reference_number": case.reference_number,
        "client": case.client,
        "intake_at": intake,
    })
    return {"added_case": result}


@case_router.delete("/{case_id}")
async def delete_case(case_id: int):
    """
    Delete a case together with all associated evidence items and processes.
    """
    logging.info(f"Deleting case with id {case_id} ...")
    query = "DELETE FROM cases WHERE id = :case_id RETURNING *;"
    result = run_query(query, {"case_id": case_id})
    return {"deleted_case": result}


@case_router.get("/{case_id}/progress")
async def get_case_progress(case_id: int):
    """
    Get case progress
    (dict with 'relative_progress' and 'progress_int_percent', which is the progress in percent rounded to 0 decimal places).
    """
    result = get_case_progress_from_database(case_id)
    if result is None or len(result) == 0 or result[0]['relative_progress'] is None:
        raise HTTPException(status_code=404, detail=f"No progress for case {case_id}.")
    return {"relative_progress": result[0]['relative_progress'],
            "progress_int_percent": round(result[0]['relative_progress'] * 100)}
