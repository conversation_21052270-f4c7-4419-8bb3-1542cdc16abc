{"version": 3, "file": "64.js", "mappings": "kKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,6CACxB,iBAAkB,iCAClB,yBAA0B,wCAE3B,yBAA0B,CACzB,sBAAuB,2CACvB,mBAAoB,yDAErB,gBAAiB,CAChBC,QAAS,+DACTC,SAAU,+CACVC,MAAO,cAER,wBAAyB,CACxB,wBAAyB,iBACzB,sBAAuB,mBACvB,sBAAuB,iBAExB,iBAAkB,CACjB,iBAAkB,sBAClB,cAAe,aACf,uBAAwB,iBAEzB,iBAAkB,CACjB,eAAgB,kBAChB,aAAc,qBAEf,oCAAqC,CACpC,eAAgB,YAChB,iBAAkB,sCAEnBC,SAAU,CACT,gCAAiC,oCAElC,YAAa,CACZD,MAAO,CACNA,MAAO,cAGT,2BAA4B,CAC3BE,QAAS,SAEV,qBAAsB,CACrB,uBAAwB,8BACxB,sCAAuC,mCAExC,yBAA0B,CACzB,2DAA4D,iIAC5D,kBAAmB,yDAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,WAEnB,oBAAqB,CACpB,uBAAwB,mBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,mCAEpC,wBAAyB,CACxB,wBAAyB,kBACzB,mBAAoB,mBAErB,yBAA0B,CACzB,2BAA4B,iBAC5B,aAAc,CACb,2BAA4B,iBAE7B,qBAAsB,gBACtB,sBAAuB,iBACvB,eAAgB,CACf,2BAA4B,qBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,iBAGb,8CAA+C,CAC9C,mBAAoB,WACpBC,QAAS,gGACT,gDAAiD,iEAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,iCACzB,uBAAwB,8BACxB,gCAAiC,sCACjC,oDAAqD,kGACrD,0BAA2B,8BAC3B,uBAAwB,wCACxB,mBAAoB,8BACpB,mDAAoD,6DACpD,uBAAwB,gCACxB,kDAAmD,iFACnD,iCAAkC,gDAClC,oCAAqC,qCAIxC,6BAA8B,CAC7B,+BAAgC,4BAChC,6BAA8B,0BAE/B,oBAAqB,CACpB,2BAA4B,oBAE7B,8BAA+B,CAC9B,kBAAmB,0BAEpB,2BAA4B,CAC3BC,MAAO,aAER,yBAA0B,CACzB,mBAAoB,wBAErB,4BAA6B,CAC5B,6CAA8C,gEAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,WAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,UAGR,uBAAwB,CACvB,0BAA2B,mBAE5B,wBAAyB,CACxB,2BAA4B,sB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/ru-RU/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Редактировать фильтр с ключом {{keyLabel}}\",\n\t\t\t\"managed-filter\": \"фильтр, управляемый {{origin}}\",\n\t\t\t\"remove-filter-with-key\": \"Удалить фильтр с ключом {{keyLabel}}\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Удалить значение фильтра ({{itemLabel}})\",\n\t\t\t\"use-custom-value\": \"Использовать пользовательское значение: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Если вы попали сюда по ссылке, возможна ошибка в приложении.\",\n\t\t\tsubTitle: \"URL-адрес не соответствует ни одной странице\",\n\t\t\ttitle: \"Не найдена\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"Свернуть сцену\",\n\t\t\t\"expand-button-label\": \"Развернуть сцену\",\n\t\t\t\"remove-button-label\": \"Удалить сцену\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Сведения об объекте\",\n\t\t\t\"scene-graph\": \"Граф сцены\",\n\t\t\t\"title-scene-debugger\": \"Отладчик сцен\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Свернуть строку\",\n\t\t\t\"expand-row\": \"Развернуть строку\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Сравнение\",\n\t\t\t\"button-tooltip\": \"Включить сравнение временных рамок\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Виджет изменения размера панелей\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Заголовок\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Обзор\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Загрузка панели плагинов...\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"Плагин не имеет свойства панели\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Отображение слишком большого количества рядов на одной панели может повлиять на производительность и затруднить чтение данных.\",\n\t\t\t\"warning-message\": \"Макс. количество отображаемых рядов: {{seriesLimit}}\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Удалить\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Отмена запроса\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Оператор редактирования фильтра\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Добавить фильтр\",\n\t\t\t\"title-add-filter\": \"Добавить фильтр\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Удалить фильтр\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Выбрать метку\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Выбрать метку\",\n\t\t\t\"title-remove-filter\": \"Удалить фильтр\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Выбрать значение\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"по умолчанию\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"очистить\",\n\t\t\ttooltip: \"Применяется по умолчанию на этом дашборде. При редактировании переносится на другие дашборды.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Восстановить критерий группирования, заданный этим дашбордом.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Значения, разделенные запятыми\",\n\t\t\t\t\t\"double-quoted-values\": \"Значения в двойных кавычках\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Форматируйте дату разными способами\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Форматируйте многозначные переменные с использованием синтаксиса glob, например {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"HTML-экранирование значений\",\n\t\t\t\t\t\"json-stringify-value\": \"Значение преобразования JSON в строку\",\n\t\t\t\t\t\"keep-value-as-is\": \"Сохраните значение как есть\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Несколько значений форматируются как «переменная=значение»\",\n\t\t\t\t\t\"single-quoted-values\": \"Значения в одинарных кавычках\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Функция удобна при URL-экранировании значений с учетом символов синтаксиса URI\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Функция удобна при URL-экранировании значений\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Значения разделяются символом |\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Группировать по селектору\",\n\t\t\t\"placeholder-group-by-label\": \"Группировать по меткам\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Выбрать значение\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Загрузка параметров...\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Применить\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Параметры не найдены\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Ошибка при получении меток. Нажмите, чтобы повторить попытку\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Привет\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Текст\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Ввести значение\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Выбрать значение\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}