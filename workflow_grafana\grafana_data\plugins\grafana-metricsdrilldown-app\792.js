"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[792],{1792:(e,t,n)=>{n.d(t,{A:()=>D});var r=n(5959),o=e=>"string"!=typeof e?{}:e.split(/ ?; ?/).reduce((e,t)=>{const[n,r]=t.split(/ ?: ?/).map((e,t)=>0===t?e.replace(/\s+/g,""):e.trim());if(n&&r){const t=n.replace(/(\w)-(\w)/g,(e,t,n)=>`${t}${n.toUpperCase()}`);let o=r.trim();Number.isNaN(Number(r))||(o=Number(r)),e[n.startsWith("-")?n:t]=o}return e},{});var i=["br","col","colgroup","dl","hr","iframe","img","input","link","menuitem","meta","ol","param","select","table","tbody","tfoot","thead","tr","ul","wbr"],a={"accept-charset":"acceptCharset",acceptcharset:"acceptCharset",accesskey:"accessKey",allowfullscreen:"allowFullScreen",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autosave:"autoSave",cellpadding:"cellPadding",cellspacing:"cellSpacing",charset:"charSet",class:"className",classid:"classID",classname:"className",colspan:"colSpan",contenteditable:"contentEditable",contextmenu:"contextMenu",controlslist:"controlsList",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",datetime:"dateTime",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",enctype:"encType",for:"htmlFor",formmethod:"formMethod",formaction:"formAction",formenctype:"formEncType",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder",hreflang:"hrefLang",htmlfor:"htmlFor",httpequiv:"httpEquiv","http-equiv":"httpEquiv",icon:"icon",innerhtml:"innerHTML",inputmode:"inputMode",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",keyparams:"keyParams",keytype:"keyType",marginwidth:"marginWidth",marginheight:"marginHeight",maxlength:"maxLength",mediagroup:"mediaGroup",minlength:"minLength",nomodule:"noModule",novalidate:"noValidate",playsinline:"playsInline",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",rowspan:"rowSpan",spellcheck:"spellCheck",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",tabindex:"tabIndex",typemustmatch:"typeMustMatch",usemap:"useMap",accentheight:"accentHeight","accent-height":"accentHeight",alignmentbaseline:"alignmentBaseline","alignment-baseline":"alignmentBaseline",allowreorder:"allowReorder",arabicform:"arabicForm","arabic-form":"arabicForm",attributename:"attributeName",attributetype:"attributeType",autoreverse:"autoReverse",basefrequency:"baseFrequency",baselineshift:"baselineShift","baseline-shift":"baselineShift",baseprofile:"baseProfile",calcmode:"calcMode",capheight:"capHeight","cap-height":"capHeight",clippath:"clipPath","clip-path":"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","clip-rule":"clipRule",colorinterpolation:"colorInterpolation","color-interpolation":"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters","color-interpolation-filters":"colorInterpolationFilters",colorprofile:"colorProfile","color-profile":"colorProfile",colorrendering:"colorRendering","color-rendering":"colorRendering",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",diffuseconstant:"diffuseConstant",dominantbaseline:"dominantBaseline","dominant-baseline":"dominantBaseline",edgemode:"edgeMode",enablebackground:"enableBackground","enable-background":"enableBackground",externalresourcesrequired:"externalResourcesRequired",fillopacity:"fillOpacity","fill-opacity":"fillOpacity",fillrule:"fillRule","fill-rule":"fillRule",filterres:"filterRes",filterunits:"filterUnits",floodopacity:"floodOpacity","flood-opacity":"floodOpacity",floodcolor:"floodColor","flood-color":"floodColor",fontfamily:"fontFamily","font-family":"fontFamily",fontsize:"fontSize","font-size":"fontSize",fontsizeadjust:"fontSizeAdjust","font-size-adjust":"fontSizeAdjust",fontstretch:"fontStretch","font-stretch":"fontStretch",fontstyle:"fontStyle","font-style":"fontStyle",fontvariant:"fontVariant","font-variant":"fontVariant",fontweight:"fontWeight","font-weight":"fontWeight",glyphname:"glyphName","glyph-name":"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal","glyph-orientation-horizontal":"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical","glyph-orientation-vertical":"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",horizadvx:"horizAdvX","horiz-adv-x":"horizAdvX",horizoriginx:"horizOriginX","horiz-origin-x":"horizOriginX",imagerendering:"imageRendering","image-rendering":"imageRendering",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",letterspacing:"letterSpacing","letter-spacing":"letterSpacing",lightingcolor:"lightingColor","lighting-color":"lightingColor",limitingconeangle:"limitingConeAngle",markerend:"markerEnd","marker-end":"markerEnd",markerheight:"markerHeight",markermid:"markerMid","marker-mid":"markerMid",markerstart:"markerStart","marker-start":"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",overlineposition:"overlinePosition","overline-position":"overlinePosition",overlinethickness:"overlineThickness","overline-thickness":"overlineThickness",paintorder:"paintOrder","paint-order":"paintOrder","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointerevents:"pointerEvents","pointer-events":"pointerEvents",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",renderingintent:"renderingIntent","rendering-intent":"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",shaperendering:"shapeRendering","shape-rendering":"shapeRendering",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",stopcolor:"stopColor","stop-color":"stopColor",stopopacity:"stopOpacity","stop-opacity":"stopOpacity",strikethroughposition:"strikethroughPosition","strikethrough-position":"strikethroughPosition",strikethroughthickness:"strikethroughThickness","strikethrough-thickness":"strikethroughThickness",strokedasharray:"strokeDasharray","stroke-dasharray":"strokeDasharray",strokedashoffset:"strokeDashoffset","stroke-dashoffset":"strokeDashoffset",strokelinecap:"strokeLinecap","stroke-linecap":"strokeLinecap",strokelinejoin:"strokeLinejoin","stroke-linejoin":"strokeLinejoin",strokemiterlimit:"strokeMiterlimit","stroke-miterlimit":"strokeMiterlimit",strokewidth:"strokeWidth","stroke-width":"strokeWidth",strokeopacity:"strokeOpacity","stroke-opacity":"strokeOpacity",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textanchor:"textAnchor","text-anchor":"textAnchor",textdecoration:"textDecoration","text-decoration":"textDecoration",textlength:"textLength",textrendering:"textRendering","text-rendering":"textRendering",underlineposition:"underlinePosition","underline-position":"underlinePosition",underlinethickness:"underlineThickness","underline-thickness":"underlineThickness",unicodebidi:"unicodeBidi","unicode-bidi":"unicodeBidi",unicoderange:"unicodeRange","unicode-range":"unicodeRange",unitsperem:"unitsPerEm","units-per-em":"unitsPerEm",unselectable:"unselectable",valphabetic:"vAlphabetic","v-alphabetic":"vAlphabetic",vectoreffect:"vectorEffect","vector-effect":"vectorEffect",vertadvy:"vertAdvY","vert-adv-y":"vertAdvY",vertoriginx:"vertOriginX","vert-origin-x":"vertOriginX",vertoriginy:"vertOriginY","vert-origin-y":"vertOriginY",vhanging:"vHanging","v-hanging":"vHanging",videographic:"vIdeographic","v-ideographic":"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",vmathematical:"vMathematical","v-mathematical":"vMathematical",wordspacing:"wordSpacing","word-spacing":"wordSpacing",writingmode:"writingMode","writing-mode":"writingMode",xchannelselector:"xChannelSelector",xheight:"xHeight","x-height":"xHeight",xlinkactuate:"xlinkActuate","xlink:actuate":"xlinkActuate",xlinkarcrole:"xlinkArcrole","xlink:arcrole":"xlinkArcrole",xlinkhref:"xlinkHref","xlink:href":"xlinkHref",xlinkrole:"xlinkRole","xlink:role":"xlinkRole",xlinkshow:"xlinkShow","xlink:show":"xlinkShow",xlinktitle:"xlinkTitle","xlink:title":"xlinkTitle",xlinktype:"xlinkType","xlink:type":"xlinkType",xmlbase:"xmlBase","xml:base":"xmlBase",xmllang:"xmlLang","xml:lang":"xmlLang","xml:space":"xmlSpace",xmlnsxlink:"xmlnsXlink","xmlns:xlink":"xmlnsXlink",xmlspace:"xmlSpace",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan",onblur:"onBlur",onchange:"onChange",onclick:"onClick",oncontextmenu:"onContextMenu",ondoubleclick:"onDoubleClick",ondrag:"onDrag",ondragend:"onDragEnd",ondragenter:"onDragEnter",ondragexit:"onDragExit",ondragleave:"onDragLeave",ondragover:"onDragOver",ondragstart:"onDragStart",ondrop:"onDrop",onerror:"onError",onfocus:"onFocus",oninput:"onInput",oninvalid:"onInvalid",onkeydown:"onKeyDown",onkeypress:"onKeyPress",onkeyup:"onKeyUp",onload:"onLoad",onmousedown:"onMouseDown",onmouseenter:"onMouseEnter",onmouseleave:"onMouseLeave",onmousemove:"onMouseMove",onmouseout:"onMouseOut",onmouseover:"onMouseOver",onmouseup:"onMouseUp",onscroll:"onScroll",onsubmit:"onSubmit",ontouchcancel:"onTouchCancel",ontouchend:"onTouchEnd",ontouchmove:"onTouchMove",ontouchstart:"onTouchStart",onwheel:"onWheel"};function s(e,t){const{key:n,level:s,...l}=t;switch(e.nodeType){case 1:return r.createElement(function(e){if(/[a-z]+[A-Z]+[a-z]+/.test(e))return e;return e.toLowerCase()}(e.nodeName),function(e,t){const n={key:t};if(e instanceof Element){const t=e.getAttribute("class");t&&(n.className=t),[...e.attributes].forEach(e=>{switch(e.name){case"class":break;case"style":n[e.name]=o(e.value);break;case"allowfullscreen":case"allowpaymentrequest":case"async":case"autofocus":case"autoplay":case"checked":case"controls":case"default":case"defer":case"disabled":case"formnovalidate":case"hidden":case"ismap":case"itemscope":case"loop":case"multiple":case"muted":case"nomodule":case"novalidate":case"open":case"readonly":case"required":case"reversed":case"selected":case"typemustmatch":n[a[e.name]||e.name]=!0;break;default:n[a[e.name]||e.name]=e.value}})}return n}(e,n),c(e.childNodes,s,l));case 3:{const t=e.nodeValue?.toString()??"";if(!l.allowWhiteSpaces&&/^\s+$/.test(t)&&!/[\u00A0\u202F]/.test(t))return null;if(!e.parentNode)return t;const n=e.parentNode.nodeName.toLowerCase();return i.includes(n)?(/\S/.test(t)&&console.warn(`A textNode is not allowed inside '${n}'. Your text "${t}" will be ignored`),null):t}case 8:default:return null;case 11:return c(e.childNodes,s,t)}}function c(e,t,n){const r=[...e].map((e,r)=>u(e,{...n,index:r,level:t+1})).filter(Boolean);return r.length?r:null}function l(e,t={}){return"string"==typeof e?function(e,t={}){if(!e||"string"!=typeof e)return null;const{includeAllNodes:n=!1,nodeOnly:r=!1,selector:o="body > *",type:i="text/html"}=t;try{const a=(new DOMParser).parseFromString(e,i);if(n){const{childNodes:e}=a.body;return r?e:[...e].map(e=>u(e,t))}const s=a.querySelector(o)||a.body.childNodes[0];if(!(s instanceof Node))throw new TypeError("Error parsing input");return r?s:u(s,t)}catch(e){0}return null}(e,t):e instanceof Node?u(e,t):null}function u(e,t={}){if(!(e&&e instanceof Node))return null;const{actions:n=[],index:r=0,level:o=0,randomKey:i}=t;let a=e,c=`${o}-${r}`;const l=[];return i&&0===o&&(c=`${function(e=6){const t="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";let n="";for(let r=e;r>0;--r)n+=t[Math.round(61*Math.random())];return n}()}-${c}`),Array.isArray(n)&&n.forEach(t=>{t.condition(a,c,o)&&("function"==typeof t.pre&&(a=t.pre(a,c,o),a instanceof Node||(a=e)),"function"==typeof t.post&&l.push(t.post(a,c,o)))}),l.length?l:s(a,{key:c,level:o,...t})}var h=Object.defineProperty,d=(e,t,n)=>((e,t,n)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n)(e,"symbol"!=typeof t?t+"":t,n),p="react-inlinesvg",f={IDLE:"idle",LOADING:"loading",LOADED:"loaded",FAILED:"failed",READY:"ready",UNSUPPORTED:"unsupported"};function m(e){return e[Math.floor(Math.random()*e.length)]}function g(){return!("undefined"==typeof window||!window.document?.createElement)}function y(){return function(){if(!document)return!1;const e=document.createElement("div");e.innerHTML="<svg />";const t=e.firstChild;return!!t&&"http://www.w3.org/2000/svg"===t.namespaceURI}()&&"undefined"!=typeof window&&null!==window}async function k(e,t){const n=await fetch(e,t),r=n.headers.get("content-type"),[o]=(r??"").split(/ ?; ?/);if(n.status>299)throw new Error("Not found");if(!["image/svg+xml","text/plain"].some(e=>o.includes(e)))throw new Error(`Content type isn't valid: ${o}`);return n.text()}function v(e=1){return new Promise(t=>{setTimeout(t,1e3*e)})}var w,x=class{constructor(){d(this,"cacheApi"),d(this,"cacheStore"),d(this,"subscribers",[]),d(this,"isReady",!1),this.cacheStore=new Map;let e=p,t=!1;g()&&(e=window.REACT_INLINESVG_CACHE_NAME??p,t=!!window.REACT_INLINESVG_PERSISTENT_CACHE&&"caches"in window),t?caches.open(e).then(e=>{this.cacheApi=e}).catch(e=>{console.error(`Failed to open cache: ${e.message}`),this.cacheApi=void 0}).finally(()=>{this.isReady=!0;const e=[...this.subscribers];this.subscribers.length=0,e.forEach(e=>{try{e()}catch(e){console.error(`Error in CacheStore subscriber callback: ${e.message}`)}})}):this.isReady=!0}onReady(e){this.isReady?e():this.subscribers.push(e)}async get(e,t){return await(this.cacheApi?this.fetchAndAddToPersistentCache(e,t):this.fetchAndAddToInternalCache(e,t)),this.cacheStore.get(e)?.content??""}set(e,t){this.cacheStore.set(e,t)}isCached(e){return this.cacheStore.get(e)?.status===f.LOADED}async fetchAndAddToInternalCache(e,t){const n=this.cacheStore.get(e);if(n?.status!==f.LOADING){if(!n?.content){this.cacheStore.set(e,{content:"",status:f.LOADING});try{const n=await k(e,t);this.cacheStore.set(e,{content:n,status:f.LOADED})}catch(t){throw this.cacheStore.set(e,{content:"",status:f.FAILED}),t}}}else await this.handleLoading(e,async()=>{this.cacheStore.set(e,{content:"",status:f.IDLE}),await this.fetchAndAddToInternalCache(e,t)})}async fetchAndAddToPersistentCache(e,t){const n=this.cacheStore.get(e);if(n?.status===f.LOADED)return;if(n?.status===f.LOADING)return void await this.handleLoading(e,async()=>{this.cacheStore.set(e,{content:"",status:f.IDLE}),await this.fetchAndAddToPersistentCache(e,t)});this.cacheStore.set(e,{content:"",status:f.LOADING});const r=await(this.cacheApi?.match(e));if(r){const t=await r.text();return void this.cacheStore.set(e,{content:t,status:f.LOADED})}try{await(this.cacheApi?.add(new Request(e,t)));const n=await(this.cacheApi?.match(e)),r=await(n?.text())??"";this.cacheStore.set(e,{content:r,status:f.LOADED})}catch(t){throw this.cacheStore.set(e,{content:"",status:f.FAILED}),t}}async handleLoading(e,t){for(let t=0;t<10;t++){if(this.cacheStore.get(e)?.status!==f.LOADING)return;await v(.1)}await t()}keys(){return[...this.cacheStore.keys()]}data(){return[...this.cacheStore.entries()].map(([e,t])=>({[e]:t}))}async delete(e){this.cacheApi&&await this.cacheApi.delete(e),this.cacheStore.delete(e)}async clear(){if(this.cacheApi){const e=await this.cacheApi.keys();await Promise.allSettled(e.map(e=>this.cacheApi.delete(e)))}this.cacheStore.clear()}};function b(e){const t=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{t.current=e}),t.current}function E(e){const{baseURL:t,content:n,description:r,handleError:o,hash:i,preProcessor:a,title:s,uniquifyIDs:c=!1}=e;try{const e=function(e,t){if(t)return t(e);return e}(n,a),o=l(e,{nodeOnly:!0});if(!(o&&o instanceof SVGSVGElement))throw new Error("Could not convert the src to a DOM Node");const u=A(o,{baseURL:t,hash:i,uniquifyIDs:c});if(r){const e=u.querySelector("desc");e?.parentNode&&e.parentNode.removeChild(e);const t=document.createElementNS("http://www.w3.org/2000/svg","desc");t.innerHTML=r,u.prepend(t)}if(void 0!==s){const e=u.querySelector("title");if(e?.parentNode&&e.parentNode.removeChild(e),s){const e=document.createElementNS("http://www.w3.org/2000/svg","title");e.innerHTML=s,u.prepend(e)}}return u}catch(e){return o(e)}}function A(e,t){const{baseURL:n="",hash:r,uniquifyIDs:o}=t,i=["id","href","xlink:href","xlink:role","xlink:arcrole"],a=["href","xlink:href"];return o?([...e.children].forEach(e=>{if(e.attributes?.length){const t=Object.values(e.attributes).map(e=>{const t=e,o=/url\((.*?)\)/.exec(e.value);return o?.[1]&&(t.value=e.value.replace(o[0],`url(${n}${o[1]}__${r})`)),t});i.forEach(e=>{const n=t.find(t=>t.name===e);var o,i;n&&(o=e,i=n.value,!a.includes(o)||!i||i.includes("#"))&&(n.value=`${n.value}__${r}`)})}return e.children.length?A(e,t):e}),e):e}function S(e){const{cacheRequests:t=!0,children:n=null,description:o,fetchOptions:i,innerRef:a,loader:s=null,onError:c,onLoad:u,src:h,title:d,uniqueHash:p}=e,[v,x]=(0,r.useReducer)((e,t)=>({...e,...t}),{content:"",element:null,isCached:t&&w.isCached(e.src),status:f.IDLE}),{content:A,element:S,isCached:D,status:C}=v,L=b(e),O=b(v),R=(0,r.useRef)(p??function(e){const t="abcdefghijklmnopqrstuvwxyz",n=`${t}${t.toUpperCase()}1234567890`;let r="";for(let t=0;t<e;t++)r+=m(n);return r}(8)),N=(0,r.useRef)(!1),T=(0,r.useRef)(!1),I=(0,r.useCallback)(e=>{N.current&&(x({status:"Browser does not support SVG"===e.message?f.UNSUPPORTED:f.FAILED}),c?.(e))},[c]),M=(0,r.useCallback)((e,t=!1)=>{N.current&&x({content:e,isCached:t,status:f.LOADED})},[]),P=(0,r.useCallback)(async()=>{const e=await k(h,i);M(e)},[i,M,h]),z=(0,r.useCallback)(()=>{try{const t=l(E({...e,handleError:I,hash:R.current,content:A}));if(!t||!(0,r.isValidElement)(t))throw new Error("Could not convert the src to a React element");x({element:t,status:f.READY})}catch(e){I(e)}},[A,I,e]),q=(0,r.useCallback)(async()=>{const e=/^data:image\/svg[^,]*?(;base64)?,(.*)/u.exec(h);let n;if(e?n=e[1]?window.atob(e[2]):decodeURIComponent(e[2]):h.includes("<svg")&&(n=h),n)M(n);else try{if(t){const e=await w.get(h,i);M(e,!0)}else await P()}catch(e){I(e)}},[t,P,i,I,M,h]),U=(0,r.useCallback)(async()=>{N.current&&x({content:"",element:null,isCached:!1,status:f.LOADING})},[]);(0,r.useEffect)(()=>{if(N.current=!0,g()&&!T.current){try{if(C===f.IDLE){if(!y())throw new Error("Browser does not support SVG");if(!h)throw new Error("Missing src");U()}}catch(e){I(e)}return T.current=!0,()=>{N.current=!1}}},[]),(0,r.useEffect)(()=>{if(g()&&L&&L.src!==h){if(!h)return void I(new Error("Missing src"));U()}},[I,U,L,h]),(0,r.useEffect)(()=>{C===f.LOADED&&z()},[C,z]),(0,r.useEffect)(()=>{g()&&L&&L.src===h&&(L.title===d&&L.description===o||z())},[o,z,L,h,d]),(0,r.useEffect)(()=>{if(O)switch(C){case f.LOADING:O.status!==f.LOADING&&q();break;case f.LOADED:O.status!==f.LOADED&&z();break;case f.READY:O.status!==f.READY&&u?.(h,D)}},[q,z,D,u,O,h,C]);const H=function(e,...t){const n={};for(const r in e)({}).hasOwnProperty.call(e,r)&&(t.includes(r)||(n[r]=e[r]));return n}(e,"baseURL","cacheRequests","children","description","fetchOptions","innerRef","loader","onError","onLoad","preProcessor","src","title","uniqueHash","uniquifyIDs");return g()?S?(0,r.cloneElement)(S,{ref:a,...H}):[f.UNSUPPORTED,f.FAILED].includes(C)?n:s:s}function D(e){w||(w=new x);const{loader:t}=e,[n,o]=(0,r.useState)(w.isReady);return(0,r.useEffect)(()=>{n||w.onReady(()=>{o(!0)})},[n]),n?r.createElement(S,{...e}):t}}}]);
//# sourceMappingURL=792.js.map?_cache=79c48dbde1c722d712fa