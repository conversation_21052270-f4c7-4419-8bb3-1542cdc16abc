from yoyo import step

__depends__ = {"001_initial_setup"}

steps = [
    # Create `process_logs` table if it doesn't exist
    step(
        """
        CREATE TABLE IF NOT EXISTS process_logs (
            id SERIAL PRIMARY KEY,
            process_id INT REFERENCES processes(id) ON DELETE CASCADE,
            message TEXT,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        """,
        "DROP TABLE IF EXISTS process_logs CASCADE;"
    ),
]
