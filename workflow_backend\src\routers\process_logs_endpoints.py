from fastapi import Query, APIRouter

from workflow_backend.src.database.database_helpers import run_query


process_logs_router = APIRouter(prefix="/process_logs", tags=["process_logs"])



@process_logs_router.get("")
async def get_process_logs(process_id: int):
    """ Get all logs for the given process. """
    query = "SELECT loglevel, message, created_at FROM process_logs WHERE process_id = :process_id;"
    result = run_query(query, {"process_id": process_id})
    return result


@process_logs_router.post("")
async def add_process_log(process_id: int, message: str, loglevel=Query(None, enum=["ERROR", "WARNING", "INFO", "DEBUG"])):
    query = """INSERT INTO process_logs (process_id, message, created_at, loglevel) 
                VALUES (:process_id, :message, NOW(), :loglevel)
                RETURNING *;"""
    result = run_query(query, {
        "process_id": process_id,
        "message": message,
        "loglevel": loglevel,
    })

    return result


