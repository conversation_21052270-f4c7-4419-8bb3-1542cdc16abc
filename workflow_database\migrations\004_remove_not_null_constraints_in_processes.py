from yoyo import step

__depends__ = {"003_rename_column_output_data"}

steps = [
    # Remove NOT NULL constraint for field "evidence_item_id" in table `processes`
    step(
        """
        ALTER TABLE processes
        ALTER COLUMN evidence_item_id DROP NOT NULL
        """,
        """
        ALTER TABLE processes
        ALTER COLUMN evidence_item_id SET NOT NULL
        """
    ),

    # Remove NOT NULL constraint for field "case_id" in table `evidence_items`
    step(
        """
        ALTER TABLE evidence_items
        ALTER COLUMN case_id DROP NOT NULL
        """,
        """
        ALTER TABLE evidence_items
        ALTER COLUMN case_id SET NOT NULL
        """
    ),
]

