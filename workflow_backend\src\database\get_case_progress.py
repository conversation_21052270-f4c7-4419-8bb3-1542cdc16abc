from workflow_backend.src.Define import ProcessStatus, get_case_progress_score, CaseProgressScore
from workflow_backend.src.database.database_helpers import run_query


def get_case_progress_from_database(case_id):
    count_forensic_steps = run_query("SELECT count(id) as count FROM forensic_steps", {"case_id": case_id})[0]["count"]
    # calculate maximum reachable progress score for ONE evidence item (= #forensic steps * max(CaseProgressSCore))
    # important: convert to float because important in sql statement for multiplication to work properly
    max_progress_score_per_evidence_item = float(count_forensic_steps * max(list(CaseProgressScore)))

    query = f"""with processes_of_this_case as (SELECT processes.*
                                               FROM processes
                                                        JOIN evidence_items ON processes.evidence_item_id = evidence_items.id
                                                        JOIN cases on evidence_items.case_id = cases.id
                                               WHERE cases.id = :case_id),
                    max_reachable_progress_points_in_this_case
                        as (SELECT count(DISTINCT (evidence_item_id)) * {max_progress_score_per_evidence_item} as max_progress_score
                            FROM processes_of_this_case),
                    progress_per_step as (SELECT step_id,
                                                 MAX(CASE status
                                                         WHEN 0 THEN {get_case_progress_score(ProcessStatus.QUEUED)}
                                                         WHEN 1 THEN {get_case_progress_score(ProcessStatus.RUNNING)}
                                                         WHEN 2 THEN {get_case_progress_score(ProcessStatus.FINISHED)}
                                                         WHEN 3 THEN {get_case_progress_score(ProcessStatus.FAILED)}
                                                         WHEN 4 THEN {get_case_progress_score(ProcessStatus.CANCELED)}
                                                         WHEN 5 THEN {get_case_progress_score(ProcessStatus.ASSIGNED)}
                                                     END) highest_progress
                                          FROM processes_of_this_case
                                          GROUP BY step_id, evidence_item_id)

               SELECT GREATEST(SUM(highest_progress) /
                               (SELECT max_progress_score FROM max_reachable_progress_points_in_this_case),
                               0) as relative_progress
               FROM progress_per_step;
            """
    result = run_query(query, {"case_id": case_id})
    return result
