"use strict";(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[708],{8327:(e,t,a)=>{a.r(t),a.d(t,{default:()=>v,updatePlugin:()=>f});var n=a(5959),r=a.n(n),i=a(2007),l=a(8531),o=a(6089),c=a(1269);function s(e,t,a,n,r,i,l){try{var o=e[i](l),c=o.value}catch(e){return void a(e)}o.done?t(c):Promise.resolve(c).then(n,r)}function p(e){return function(){var t=this,a=arguments;return new Promise(function(n,r){var i=e.apply(t,a);function l(e){s(i,n,r,l,o,"next",e)}function o(e){s(i,n,r,l,o,"throw",e)}l(void 0)})}}function u(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function d(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{},n=Object.keys(a);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(a).filter(function(e){return Object.getOwnPropertyDescriptor(a,e).enumerable}))),n.forEach(function(t){u(e,t,a[t])})}return e}function m(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t.push.apply(t,a)}return t}(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}),e}const y=e=>({colorWeak:o.css`
    color: ${e.colors.text.secondary};
  `,marginTop:o.css`
    margin-top: ${e.spacing(3)};
  `,marginTopXl:o.css`
    margin-top: ${e.spacing(6)};
  `}),b=(e,t)=>p(function*(){try{yield f(e,t),l.locationService.reload()}catch(e){console.error("Error while updating the plugin",e)}})(),g={container:"data-testid ac-container",apiKey:"data-testid ac-api-key",apiUrl:"data-testid ac-api-url",submit:"data-testid ac-submit-form"},f=(e,t)=>p(function*(){const a=(0,l.getBackendSrv)().fetch({url:`/api/plugins/${e}/settings`,method:"POST",data:t});return(yield(0,c.lastValueFrom)(a)).data})(),v=({plugin:e})=>{const t=(0,i.useStyles2)(y),{enabled:a,pinned:l,jsonData:o}=e.meta,[c,s]=(0,n.useState)({apiUrl:(null==o?void 0:o.apiUrl)||"",apiKey:"",isApiKeySet:Boolean(null==o?void 0:o.isApiKeySet)});return r().createElement("div",{"data-testid":g.container},r().createElement(i.FieldSet,{label:"Enable / Disable"},!a&&r().createElement(r().Fragment,null,r().createElement("div",{className:t.colorWeak},"The plugin is currently not enabled."),r().createElement(i.Button,{className:t.marginTop,variant:"primary",onClick:()=>b(e.meta.id,{enabled:!0,pinned:!0,jsonData:o})},"Enable plugin")),a&&r().createElement(r().Fragment,null,r().createElement("div",{className:t.colorWeak},"The plugin is currently enabled."),r().createElement(i.Button,{className:t.marginTop,variant:"destructive",onClick:()=>b(e.meta.id,{enabled:!1,pinned:!1,jsonData:o})},"Disable plugin"))),r().createElement(i.FieldSet,{label:"API Settings",className:t.marginTopXl},r().createElement(i.Field,{label:"API Key",description:"A secret key for authenticating to our custom API"},r().createElement(i.SecretInput,{width:60,"data-testid":g.apiKey,id:"api-key",value:null==c?void 0:c.apiKey,isConfigured:c.isApiKeySet,placeholder:"Your secret API key",onChange:e=>{s(m(d({},c),{apiKey:e.target.value.trim()}))},onReset:()=>s(m(d({},c),{apiKey:"",isApiKeySet:!1}))})),r().createElement(i.Field,{label:"API Url",description:"",className:t.marginTop},r().createElement(i.Input,{width:60,id:"api-url","data-testid":g.apiUrl,label:"API Url",value:null==c?void 0:c.apiUrl,placeholder:"E.g.: http://mywebsite.com/api/v1",onChange:e=>{s(m(d({},c),{apiUrl:e.target.value.trim()}))}})),r().createElement("div",{className:t.marginTop},r().createElement(i.Button,{type:"submit","data-testid":g.submit,onClick:()=>b(e.meta.id,{enabled:a,pinned:l,jsonData:{apiUrl:c.apiUrl,isApiKeySet:!0},secureJsonData:c.isApiKeySet?void 0:{apiKey:c.apiKey}}),disabled:Boolean(!c.apiUrl||!c.isApiKeySet&&!c.apiKey)},"Save API settings"))))}}}]);
//# sourceMappingURL=708.js.map