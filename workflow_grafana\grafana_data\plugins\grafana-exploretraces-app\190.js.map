{"version": 3, "file": "190.js", "mappings": "mKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,0CACxB,iBAAkB,kCAClB,yBAA0B,6CAE3B,yBAA0B,CACzB,sBAAuB,2CACvB,mBAAoB,2CAErB,gBAAiB,CAChBC,QAAS,gGACTC,SAAU,wCACVC,MAAO,uBAER,wBAAyB,CACxB,wBAAyB,kBACzB,sBAAuB,kBACvB,sBAAuB,mBAExB,iBAAkB,CACjB,iBAAkB,sBAClB,cAAe,uBACf,uBAAwB,wBAEzB,iBAAkB,CACjB,eAAgB,gBAChB,aAAc,iBAEf,oCAAqC,CACpC,eAAgB,cAChB,iBAAkB,iDAEnBC,SAAU,CACT,gCAAiC,wCAElC,YAAa,CACZD,MAAO,CACNA,MAAO,WAGT,2BAA4B,CAC3BE,QAAS,YAEV,qBAAsB,CACrB,uBAAwB,+BACxB,sCAAuC,2DAExC,yBAA0B,CACzB,2DAA4D,oHAC5D,kBAAmB,4CAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,YAEnB,oBAAqB,CACpB,uBAAwB,sBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,6BAEpC,wBAAyB,CACxB,wBAAyB,gBACzB,mBAAoB,iBAErB,yBAA0B,CACzB,2BAA4B,kBAC5B,aAAc,CACb,2BAA4B,wBAE7B,qBAAsB,uBACtB,sBAAuB,kBACvB,eAAgB,CACf,2BAA4B,sBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,mBAGb,8CAA+C,CAC9C,mBAAoB,SACpBC,QAAS,qGACT,gDAAiD,4DAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,8BACzB,uBAAwB,gCACxB,gCAAiC,+CACjC,oDAAqD,sGACrD,0BAA2B,yBAC3B,uBAAwB,0BACxB,mBAAoB,6BACpB,mDAAoD,yDACpD,uBAAwB,iCACxB,kDAAmD,yEACnD,iCAAkC,kCAClC,oCAAqC,mDAIxC,6BAA8B,CAC7B,+BAAgC,uBAChC,6BAA8B,wBAE/B,oBAAqB,CACpB,2BAA4B,qBAE7B,8BAA+B,CAC9B,kBAAmB,wBAEpB,2BAA4B,CAC3BC,MAAO,WAER,yBAA0B,CACzB,mBAAoB,iCAErB,4BAA6B,CAC5B,6CAA8C,2FAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,SAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,UAGR,uBAAwB,CACvB,0BAA2B,oBAE5B,wBAAyB,CACxB,2BAA4B,uB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/es-ES/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Editar filtro con la clave {{keyLabel}}\",\n\t\t\t\"managed-filter\": \"Filtro gestionado de {{origin}}\",\n\t\t\t\"remove-filter-with-key\": \"Eliminar filtro con la clave {{keyLabel}}\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Eliminar valor del filtro: {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Usar valor personalizado: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Si ha llegado hasta aquí mediante un enlace, es posible que haya un error en esta aplicación.\",\n\t\t\tsubTitle: \"La URL no coincide con ninguna página\",\n\t\t\ttitle: \"No se ha encontrado\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"Contraer escena\",\n\t\t\t\"expand-button-label\": \"Expandir escena\",\n\t\t\t\"remove-button-label\": \"Eliminar escena\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Detalles del objeto\",\n\t\t\t\"scene-graph\": \"Gráfico de la escena\",\n\t\t\t\"title-scene-debugger\": \"Depurador de escenas\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Contraer fila\",\n\t\t\t\"expand-row\": \"Expandir fila\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Comparación\",\n\t\t\t\"button-tooltip\": \"Habilitar comparación de intervalos de tiempo\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Widget de cambio de tamaño del panel\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Título\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Explorar\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Cargando panel de plugins...\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"El plugin del panel no tiene ningún componente de panel\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Representar demasiadas series en un solo panel puede afectar al rendimiento y dificultar la lectura de los datos.\",\n\t\t\t\"warning-message\": \"Mostrando solo {{seriesLimit}} serie(s)\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Eliminar\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Cancelar consulta\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Editar operador de filtro\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Añadir filtro\",\n\t\t\t\"title-add-filter\": \"Añadir filtro\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Eliminar filtro\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Seleccionar etiqueta\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Seleccionar etiqueta\",\n\t\t\t\"title-remove-filter\": \"Eliminar filtro\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Seleccionar valor\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"predeterminada\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"borrar\",\n\t\t\ttooltip: \"Aplicado de forma predeterminada en este dashboard. Si se edita, se transfiere a otros dashboards.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Restaura la función groupby definida por este dashboard.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Valores separados por comas\",\n\t\t\t\t\t\"double-quoted-values\": \"Valores entre comillas dobles\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Dar formato a la fecha de diferentes maneras\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Dar formato a las variables de múltiples valores con la sintaxis glob, por ejemplo, {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"Escape HTML de valores\",\n\t\t\t\t\t\"json-stringify-value\": \"Valor de JSON stringify\",\n\t\t\t\t\t\"keep-value-as-is\": \"Mantener el valor tal cual\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Los valores múltiples tienen el formato variable=valor\",\n\t\t\t\t\t\"single-quoted-values\": \"Valores entre comillas simples\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Útil para valores de escape URL, utilizando caracteres de sintaxis URI\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Útil para valores de escape URL\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Los valores están separados por el carácter |\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Agrupar por selector\",\n\t\t\t\"placeholder-group-by-label\": \"Agrupar por etiqueta\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Seleccionar valor\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Cargando opciones...\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Aplicar\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"No se han encontrado opciones\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Se ha producido un error al recuperar las etiquetas. Haga clic para volver a intentarlo\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Hola\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Texto\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Introducir valor\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Seleccionar valor\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}