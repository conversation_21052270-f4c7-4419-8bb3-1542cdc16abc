{"version": 3, "file": "452.js?_cache=2b21d327e72e37b1c8bb", "mappings": "gRAmBA,SAASA,EAAYC,GACnB,MAAoB,iBAATA,GAAqBC,EAAAA,SAASC,aAAaF,GAE7CA,EAGFC,EAAAA,SAASE,WAAW,IAAIC,KAAKJ,GAAO,CAAEK,SAAS,IAAUC,aAClE,CAEA,MAgCA,EAhCuB,EAAGC,QAAOC,eAAcC,aAAYC,iBACzD,MAAOC,IAASC,EAAAA,EAAAA,MACV,OAAEC,EAAM,OAAEC,IAAWC,EAAAA,EAAAA,IAAiBR,GACtCS,EAAOjB,EAAYS,GACnBS,EAAKlB,EAAYU,GACjBS,GAAQC,EAAAA,EAAAA,IAAgB,CAC5BN,SACAO,UAAWV,EAAWW,IACtBC,eAAgBR,EAAOS,IAAI,EAAGC,QAAOC,KAAIC,YAAa,CACpDC,IAAKH,EACLI,SAAUH,EACVC,WAEFG,WAAY,IAAIC,EAAAA,GAAe,CAAEd,OAAMC,OACvCc,UAAU,IAGNC,GAAUC,EAAAA,EAAAA,SAAO,GAQvB,OAPAC,EAAAA,EAAAA,WAAU,KACHF,EAAQG,UACXH,EAAQG,SAAU,GAClBC,EAAAA,EAAAA,GAAqB,2BAA4B,CAAEC,UAAW,sBAE/D,IAGD,kBAACC,MAAAA,CAAIC,cAAY,8CACd5B,EAAQ,kBAAC6B,EAAAA,EAASA,CAAC7B,MAAOA,IAAY,kBAAC8B,EAAAA,EAAOA,CAACvB,MAAOA,K", "sources": ["webpack://grafana-metricsdrilldown-app/./exposedComponents/LabelBreakdown/LabelBreakdown.tsx"], "sourcesContent": ["import { dateMath, type DataSourceApi } from '@grafana/data';\nimport { SceneTimeRange } from '@grafana/scenes';\nimport React, { useEffect, useRef } from 'react';\n\nimport { ErrorView } from 'App/ErrorView';\nimport { <PERSON><PERSON> } from 'App/Routes';\nimport { useCatchExceptions } from 'App/useCatchExceptions';\nimport { reportExploreMetrics } from 'interactions';\nimport { newMetricsTrail } from 'utils';\n\nimport { parsePromQLQuery } from '../../extensions/links';\n\nexport interface LabelBreakdownProps {\n  query: string;\n  initialStart: string | number;\n  initialEnd: string | number;\n  dataSource: DataSourceApi;\n}\n\nfunction toSceneTime(time: string | number): string {\n  if (typeof time === 'string' && dateMath.isMathString(time)) {\n    // 'now', 'now-1h', etc.\n    return time;\n  }\n\n  return dateMath.toDateTime(new Date(time), { roundUp: false })!.toISOString();\n}\n\nconst LabelBreakdown = ({ query, initialStart, initialEnd, dataSource }: LabelBreakdownProps) => {\n  const [error] = useCatchExceptions();\n  const { metric, labels } = parsePromQLQuery(query);\n  const from = toSceneTime(initialStart);\n  const to = toSceneTime(initialEnd);\n  const trail = newMetricsTrail({\n    metric,\n    initialDS: dataSource.uid,\n    initialFilters: labels.map(({ label, op, value }) => ({\n      key: label,\n      operator: op,\n      value,\n    })),\n    $timeRange: new SceneTimeRange({ from, to }),\n    embedded: true,\n  });\n\n  const initRef = useRef(false);\n  useEffect(() => {\n    if (!initRef.current) {\n      initRef.current = true;\n      reportExploreMetrics('exposed_component_viewed', { component: 'label_breakdown' });\n    }\n  }, []);\n\n  return (\n    <div data-testid=\"metrics-drilldown-embedded-label-breakdown\">\n      {error ? <ErrorView error={error} /> : <Wingman trail={trail} />}\n    </div>\n  );\n};\n\nexport default LabelBreakdown;\n"], "names": ["toSceneTime", "time", "dateMath", "isMathString", "toDateTime", "Date", "roundUp", "toISOString", "query", "initialStart", "initialEnd", "dataSource", "error", "useCatchExceptions", "metric", "labels", "parsePromQLQuery", "from", "to", "trail", "newMetricsTrail", "initialDS", "uid", "initialFilters", "map", "label", "op", "value", "key", "operator", "$timeRange", "SceneTimeRange", "embedded", "initRef", "useRef", "useEffect", "current", "reportExploreMetrics", "component", "div", "data-testid", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Wingman"], "sourceRoot": ""}