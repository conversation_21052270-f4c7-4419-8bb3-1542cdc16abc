"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[546],{8546:(e,a,r)=>{r.r(a),r.d(a,{default:()=>i});var t=r(5959),o=r.n(t),l=r(7781),u=r(8531),n=r(2007),s=r(7389),c=r(6854);const p={[l.AbstractLabelOperator.Equal]:c.KQ.Equal,[l.AbstractLabelOperator.NotEqual]:c.KQ.NotEqual,[l.AbstractLabelOperator.EqualRegEx]:c.KQ.RegexEqual,[l.AbstractLabelOperator.NotEqualRegEx]:c.KQ.RegexNotEqual};function i({datasourceUid:e,from:a,renderButton:r,returnToPreviousSource:c,streamSelectors:i,to:b}){const E=(0,u.useReturnToPrevious)(),f=(0,t.useMemo)(()=>{const r=i[0];if(!r||(null==r?void 0:r.operator)!==l.AbstractLabelOperator.Equal)return null;const t=(0,s.uu)(r.value);let o=new URLSearchParams;return e&&(o=(0,s.xh)(s.I8.DatasourceId,e,o)),a&&(o=(0,s.xh)(s.I8.TimeRangeFrom,a,o)),b&&(o=(0,s.xh)(s.I8.TimeRangeTo,b,o)),i.forEach(e=>{o=(0,s.zH)(s.I8.Labels,`${e.name}|${p[e.operator]}|${(0,s.XH)((0,s.vh)(e.value))},${(0,s.XH)((0,s.rx)(e.value))}`,o)}),(0,s.Rk)(`/explore/${r.name}/${t}/logs`,o)},[e,a,b,i]);return f?r?r({href:f}):o().createElement(n.LinkButton,{variant:"secondary",href:f,onClick:()=>E(c||"previous")},"Open in Logs Drilldown"):null}}}]);
//# sourceMappingURL=546.js.map?_cache=8e0beb2f22d2cbf6b122