<template>
  <div class="iframe-container">
    <iframe :src="imageStationUrl" frameborder="0"></iframe>
  </div>
</template>

<script setup>

import {computed} from "vue";

const imageStationUrl = computed(() => {
  let url = new URL(window.location.toString());
  // if we are inside the mobile rack network, we need to use a different url (the url of the VM)
  if(url.toString().includes('itf-workflow-01.digifors.mobile')){
    url = new URL('http://imagestation02.digifors.mobile')
  }
  url.port = '8089';
  url.hash = ''
  url.pathname = '/'
  console.log('ImageStation URL: ' + url)
  return url.toString();
});
</script>

<style scoped>
.iframe-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.iframe-container iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
