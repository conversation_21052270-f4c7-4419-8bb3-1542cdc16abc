{"version": 3, "file": "module.js", "mappings": ";6MACIA,EADAC,ECAAC,EACAC,E,sECEJ,IACE,KAAiB,QACb,QAAkBC,MAAM,EAAG,QAAkBC,YAAY,KAAO,GAChE,0C,2CCAN,MAAMC,GAA4BC,EAAAA,EAAAA,MAAK,IAAM,+BACvCC,GAA0BD,EAAAA,EAAAA,MAAK,IAAM,iF,wUCM3C,MAAME,GAAMF,EAAAA,EAAAA,MAAK,kBACf,MAAM,cAAEG,SAAwB,yEAExBC,QAASC,SAAwB,yEACjCD,QAASE,SAA0B,+BACnCF,QAASG,SAAsB,8BAQvC,OANAF,IAEIF,YACIK,QAAQC,IAAI,CAACH,IAAmBC,OAGjC,4BACT,EAdiB,IAgBXG,GAAYV,EAAAA,EAAAA,MAAK,kBACrB,aAAa,gFACf,EAFuB,IAIVW,GAAS,IAAIC,EAAAA,WAAgBC,YAAYX,GAAKY,cAAc,CACvEC,KAAML,EACNM,KAAM,MACNC,GAAI,gBACJC,MAAO,kBAGT,IAAK,MAAMC,KAAcC,EAAAA,GACvBT,EAAOU,QAAQF,GAGjBR,EAAOW,gBAAgB,CACrBC,UDpCK,SAA4CC,GACjD,OACE,kBAACC,EAAAA,SAAQA,CACPC,SACE,kBAACC,EAAAA,WAAUA,CAACC,QAAQ,YAAYC,UAAAA,GAAS,2BAK3C,kBAAC9B,EAA8ByB,GAGrC,ECyBEM,YAAa,6DACbb,GAAI,yDACJC,MAAO,kCAGTP,EAAOW,gBAAgB,CACrBC,UD7BK,SAA0CC,GAC/C,OACE,kBAACC,EAAAA,SAAQA,CAACC,SAAU,kBAACK,MAAAA,KAAI,8BACvB,kBAAC9B,EAA4BuB,GAGnC,ECwBEM,YAAa,mGACbb,GAAI,uDACJC,MAAO,6B,0CCvDF,MAAMc,EAAiB,WACjBC,EAAuB,gB,qDCA7B,eAAKC,G,+EAAAA,C,CAAL,C,IAOA,WAAKC,G,mHAAAA,C,CAAL,C,IASA,WAAKC,G,uCAAAA,C,CAAL,C,sOCoBP,MAAMC,EAAe,yBACfnB,EAAQ,WAAWmB,IACnBP,EAAc,6BAA6BO,SAGpCC,EAAkB,CAC7BC,oBAAqB,4CAKVnB,EAA2B,CACtC,CACEoB,QAAS,CACPC,EAAAA,sBAAsBC,mBACtBD,EAAAA,sBAAsBE,qBACtB,0DAEFzB,QACAY,cACAd,KAjBS,UAkBT4B,KAAMC,IACNC,UAgHJ,SAA8DC,G,IAMlBC,EAL1C,IAAKD,EACH,OAEF,MAAMC,EAAYD,EAAQP,QAAQS,KAAMC,I,IAAWA,E,MAA4B,UAAX,QAAjBA,EAAAA,EAAOC,kBAAPD,IAAAA,OAAAA,EAAAA,EAAmBE,QAChEC,GAAcC,EAAAA,EAAAA,kBACdC,EAAgBF,EAAYG,QAAQR,SAAqB,QAArBA,EAAAA,EAAWG,kBAAXH,IAAAA,OAAAA,EAAAA,EAAuBS,IAAKV,EAAQW,YAE9E,IAAKV,IAAcO,EACjB,OAGF,MAAMI,EAAON,EAAYG,QAAQR,EAAUW,KAAMZ,EAAQW,WAAYE,IAC/D,OAAEC,EAAM,aAAEC,EAAY,YAAEC,EAAW,eAAEC,IAAmBC,EAAAA,EAAAA,IAAoBN,EAAMZ,EAASC,GAC3FkB,EAAgBJ,EAAab,KAAMkB,IAAaC,EAAAA,EAAAA,IAAoBD,EAASE,WAGnF,IAAKH,EACH,OAIF,MACMI,EAAaC,EADGL,EAAcM,MAAMC,MAAM,KAAK,IAErD,IAAIC,EAAYR,EAAcS,MAAQC,EAAAA,GAAe,UAAYV,EAAcS,IAE/Eb,EAAae,KAAMC,GAAOA,EAAEH,MAAQD,GAAa,EAAI,GAErD,IAAIK,EAASC,EAAgBC,EAAcC,aAAc3B,EAAe,IAAI4B,iBAC5EJ,EAASC,EAAgBC,EAAcG,cAAerC,EAAQsC,UAAUC,KAAKC,UAAUC,WAAYT,GACnGA,EAASC,EAAgBC,EAAcQ,YAAa1C,EAAQsC,UAAUK,GAAGH,UAAUC,WAAYT,GAC/FA,EAASY,EAA6B7B,EAAciB,GAEhDhB,IACFgB,EA/DJ,SAAgChB,EAA+BgB,GAC7D,IAAK,MAAMa,KAAc7B,EACvBgB,EAASc,EACPZ,EAAca,YACd,GAAGF,EAAWjB,OAAOoB,EAAoBH,EAAWvB,aAAa0B,EAC/DC,EAAgBJ,EAAWpB,UAE7BO,GAGJ,OAAOA,CACT,CAoDakB,CAAuBlC,EAAagB,KAE3ClB,aAAAA,EAAAA,EAAQqC,UACVnB,EApHJ,SAAsClB,EAAuBkB,GAC3D,IAAK,MAAMoB,KAAStC,EAClB,GAAIsC,EAAM/C,OAASgD,EAAAA,EAAUC,mBAEzBtB,EADEoB,EAAMxB,MAAQ2B,EAAAA,GACPT,EACPZ,EAAcsB,OACd,GAAGJ,EAAMxB,OAAOwB,EAAM9B,YAAY0B,EAAoBC,EAAgBG,EAAM3B,UAC5EO,GAGOc,EACPZ,EAAcuB,SACd,GAAGL,EAAMxB,OAAOwB,EAAM9B,YAAY0B,EAChCU,EAAqBN,EAAM3B,WACxBuB,EAAoBW,EAAmBP,EAAM3B,UAClDO,OAGC,CACL,MAAM4B,EAA8B,CAClCnC,MAAO2B,EAAM3B,MACboC,OAAQT,EAAMS,QAGVC,EAAuB,GAAGV,EAAMxB,OAAOwB,EAAM9B,YAAY0B,EAC7DU,EAAqBK,KAAKC,UAAUJ,QACjCK,EAA0BL,EAAWnC,SAE1CO,EAASc,EAAmBZ,EAAcgC,OAAQJ,EAAsB9B,EAC1E,CAEF,OAAOA,CACT,CAoFamC,CAA6BrD,EAAQkB,KAE5Cf,aAAAA,EAAAA,EAAgBkC,UAClBnB,EAxDG,SAAkCf,EAAqCe,GAC5E,MAAMoC,EAA6B,GAEnC,IAAK,MAAMhB,KAASnC,EAClBmD,EAASC,KAAK,CACZhE,KAAM+C,EAAM9B,WAAagD,EAAAA,GAAgBC,MAAQ,UAAY,UAC7DC,QAASvB,EAAgBG,EAAM3B,SAInC,IAAIgD,GAAiBC,EAAAA,EAAAA,GAAqBN,GAG1C,OADApC,EAASc,EAAmBZ,EAAcyC,SAAUZ,KAAKC,UAAUI,GAAWpC,GACvEc,EAAmBZ,EAAc0C,iBAAkBH,EAAgBzC,EAC5E,CA0Ca6C,CAAyB5D,EAAgBe,IAGpD,MAAO,CACLnC,KAAMC,EAAa,YAAY6B,KAAaJ,SAAmBS,GAEnE,IA1JA,SAASiB,EAAgBxB,GACvB,OAAKA,GACIqD,EAAAA,EAGX,CAGO,SAASnB,EAAmBlC,GACjC,OAAOA,aAAAA,EAAAA,EAAOhB,QAAQ,QAAS,KACjC,CAEO,SAASiD,EAAqBjC,GACnC,OAAKA,GAKEsD,EAAAA,EAAAA,IAA8BpB,EAAmBlC,IAJ/CqD,EAAAA,EAKX,CAEO,SAASb,EAA0BxC,GACxC,OAAKA,EAIEuB,EAAoBW,EAAmBlC,IAHrCqD,EAAAA,EAIX,CAoCA,SAASlC,EAA6B7B,EAAoCiB,GACxE,IAAK,MAAMgD,KAAejE,EAAc,CAEtC,GAAIiE,EAAY3E,OAASgD,EAAAA,EAAU4B,QACjC,SAGF,MAAMC,EAA6B,GAAGF,EAAYpD,OAAOoD,EAAY1D,YAAY0B,EAC/EU,EAAqBsB,EAAYvD,WAC9BuB,EAAoBW,EAAmBqB,EAAYvD,UAExDO,EAASc,EAAmBZ,EAAciD,OAAQD,EAA4BlD,EAChF,CACA,OAAOA,CACT,CA+EO,SAASlC,EAAaD,EAAO,WAAYuF,GAC9C,MAAO,MAAMC,EAAAA,KAAgBxF,IAAOuF,EAAY,IAAIA,EAAU3C,aAAe,IAC/E,CAEO,MAAMP,EAAgB,CAC3BC,aAAc,OAAOmD,EAAAA,KACrBjD,cAAe,OACfK,YAAa,KACbyC,OAAQ,OAAOI,EAAAA,KACfrB,OAAQ,OAAOsB,EAAAA,KACf/B,SAAU,OAAOgC,EAAAA,KACjBjC,OAAQ,OAAOkC,EAAAA,KACf3C,YAAa,OAAO4C,EAAAA,KACpBhB,SAAUiB,EAAAA,GACVhB,iBAAkB,OAAOgB,EAAAA,MAIpB,SAAS3D,EAAgBL,EAAuBH,EAAeoE,G,IAC3BA,EAAzC,MAAMC,EAAe,IAAI1D,gBAAsC,QAAtByD,EAAAA,aAAAA,EAAAA,EAAcpD,kBAAdoD,IAAAA,EAAAA,EAA4BE,EAAAA,gBAAgBC,aAGrF,OAFAF,EAAaG,IAAIrE,EAAKH,GAEfqE,CACT,CAEO,SAAShD,EACdlB,EACAH,EACAoE,GAEA,MAAMK,EAAWH,EAAAA,gBAAgBI,c,IACQN,EAAzC,MAAMC,EAAe,IAAI1D,gBAAsC,QAAtByD,EAAAA,aAAAA,EAAAA,EAAcpD,kBAAdoD,IAAAA,EAAAA,EAA4BK,EAASE,QAG9E,OAFAN,EAAaO,OAAOzE,EAAKH,GAElBqE,CACT,CAEO,SAAStE,EAAa8E,GAC3B,OACEC,EAAAA,EAAAA,IAAgCD,GAE7B7F,QAAQ,MAAO,KACfA,QAAQ,MAAO,IAEtB,CAqBO,SAASuC,EAAoBvB,GAClC,OAnBF,SAAkCA,GAChC,OAAIA,QACK,GAIF,KAAK+E,OAAO/F,SAASgB,EAAO,UACrC,CAYSgF,CAVF,SAAiChF,GACtC,OAAIA,QACK,GAIO,MAAM+E,OAAO/F,SAASgB,EAAO,UAC/C,CAGkCiF,CAAwBjF,GAC1D,CAGO,SAASZ,EAAqBY,EAA2BkF,GAE9D,IAAKA,EAASC,QAAUD,EAASE,WAC/B,OAAOpF,EAGT,GAAqB,iBAAVA,EACT,OAAOqF,EAAAA,EAAAA,GAAgCrF,GAIzC,OADsBsF,EAAAA,EAAAA,KAAUtF,EAAOuF,EAAAA,GAClBC,KAAK,IAC5B,C,iBCnSO,SAASH,EAAgCvF,GAC9C,OAAOA,EAAWd,QAAQ,MAAO,QAAQA,QAAQ,MAAO,OAAOA,QAAQ,KAAM,MAC/E,CAGO,SAASuG,EAA0BvF,GACxC,MAAqB,iBAAVA,EACFA,EAAMhB,QAAQ,MAAO,YAAYA,QAAQ,qBAAsB,UAEjEgB,CACT,C,0DCnBO,eAAK4B,G,2DAAAA,C,CAAL,C,oNCKA,eAAK6D,G,0EAAAA,C,CAAL,C,IAOA,WAAKC,G,mBAAAA,C,CAAL,C,IAIA,WAAKC,G,+CAAAA,C,CAAL,C,IAMA,MAAMC,E,kUAAW,IAAKH,EAAkBE,GA4BxC,eAAKE,G,2EAAAA,C,CAAL,C,IAOA,WAAKhD,G,yCAAAA,C,CAAL,C,IAKA,WAAKiD,G,2EAAAA,C,CAAL,C,ugBCxDP,MAAMC,EAAiB,CACrBC,IAAKpC,EAAAA,GACLqC,Q,UAGWC,EAAS,CACpBC,MAAO,CAACC,EAAsB7H,KAC5B,MAAM8H,EAAM,KAAKN,EAAmBxH,GACpC+H,QAAQH,MAAMC,EAAKC,GACnBE,EAAeH,EAAKC,IAEtBG,KAAM,CAACC,EAAalI,KAClB,MAAM8H,EAAM,KAAKN,EAAmBxH,GAEpCmI,EAAgBD,EAAKJ,IAEvBM,KAAM,CAACF,EAAalI,KAClB,MAAM8H,EAAM,KAAKN,EAAmBxH,GACpC+H,QAAQK,KAAKF,EAAKJ,GAClBO,EAAgBH,EAAKJ,KAInBK,EAAkB,CAACD,EAAalI,KACpC,KACEsI,EAAAA,EAAAA,SAAQJ,EAAKlI,EACf,CAAE,MAAOuI,GACPR,QAAQK,KAAK,4BACf,GAGIC,EAAkB,CAACH,EAAalI,KACpC,KACEwI,EAAAA,EAAAA,YAAWN,EAAKlI,EAClB,CAAE,MAAOuI,GACPR,QAAQK,KAAK,8BAA+B,CAAEpI,UAASkI,OACzD,GAgCF,MAAMF,EAAiB,CAACH,EAAmCY,KACzD,IAAIzI,EAAUyI,EACd,KA3BF,SAAmCZ,EAA2B7H,GAC5D,GAAmB,iBAAR6H,GAA4B,OAARA,KACzBa,EAAAA,EAAAA,IAASb,IACXc,OAAOC,KAAKf,GAAKgB,QAASjH,IACxB,MAAMH,EAAQoG,EAAIjG,GACG,iBAAVH,GAAuC,kBAAVA,GAAwC,iBAAVA,IACpEzB,EAAQ4B,GAAOH,EAAMgB,cAKvBqG,EAAQjB,IACV,GAAwB,iBAAbA,EAAIkB,MAAkC,OAAblB,EAAIkB,KACtC,IACE/I,EAAQ+I,KAAOhF,KAAKC,UAAU6D,EAAIkB,KACpC,CAAE,MAAOR,GAET,KAC6B,iBAAbV,EAAIkB,MAAyC,kBAAblB,EAAIkB,MAA0C,iBAAblB,EAAIkB,OACrF/I,EAAQ+I,KAAOlB,EAAIkB,KAAKtG,WAIhC,CAKIuG,CAA0BnB,EAAK7H,GAE3B6H,aAAeoB,OACjBC,EAAAA,EAAAA,UAASrB,EAAK7H,GACU,iBAAR6H,GAChBqB,EAAAA,EAAAA,UAAS,IAAID,MAAMpB,GAAM7H,GAChB6H,GAAsB,iBAARA,EACnB7H,EAAQkI,KACVgB,EAAAA,EAAAA,UAAS,IAAID,MAAMjJ,EAAQkI,KAAMlI,IAEjCkJ,EAAAA,EAAAA,UAAS,IAAID,MAAM,gBAAiBjJ,IAGtCkJ,EAAAA,EAAAA,UAAS,IAAID,MAAM,iBAAkBjJ,EAEzC,CAAE,MAAOuI,GACPR,QAAQH,MAAM,4BAA6B,CAAE5H,UAAS6H,OACxD,GAGIiB,EAAWrH,GACR,SAAUA,C,yNClDZ,MAAM0H,EAaX,eAAOC,CAASC,GACd,OAAO,IAAIF,EAAaE,EAAK9G,KAAM8G,EAAK1G,GAAI0G,EAAMA,EAAKhJ,KACzD,CAEAiJ,QAAAA,CAASC,GACP,OAAOC,KAAKjH,MAAQgH,EAAShH,MAAQiH,KAAK7G,IAAM4G,EAAS5G,EAC3D,CAEA8G,aAAAA,CAAcC,GACZ,OAAOA,EAAMC,UAAUH,KAAKjH,KAAMiH,KAAK7G,GACzC,CAjBA,WAAAiH,CAAYrH,EAAcI,EAAYkH,EAAyBxJ,GAL/DkC,EAAAA,KAAAA,YAAAA,GACAI,EAAAA,KAAAA,UAAAA,GACAtC,EAAAA,KAAAA,YAAAA,GACAwJ,EAAAA,KAAAA,kBAAAA,GAGEL,KAAKjH,KAAOA,EACZiH,KAAK7G,GAAKA,EACV6G,KAAKnJ,KAAOA,EACZmJ,KAAKK,WAAaA,CACpB,EAeK,SAASC,EAAkBJ,EAAeK,GAC/C,MAAMC,EAAsB,GAS5B,OARmBnG,EAAAA,GAAOoG,MAAMP,GAC3BQ,QAAQ,CACXC,MAAQd,UACYe,IAAdL,GAA2BA,EAAUM,SAAShB,EAAKhJ,KAAKnC,MAC1D8L,EAAM3F,KAAKgF,EAAKA,SAIfW,CACT,CAEA,SAASM,EAA4BjB,EAAkBhJ,GACrD,GAAIgJ,EAAKhJ,KAAKnC,KAAOmC,EACnB,MAAO,CAAC8I,EAAaC,SAASC,IAGhC,MAAMkB,EAA4B,GAClC,IAAIC,EAAM,EACNC,EAAQpB,EAAKqB,WAAWF,GAC5B,KAAOC,GACLF,EAAUlG,QAAQiG,EAA4BG,EAAOpK,IACrDmK,EAAMC,EAAM9H,GACZ8H,EAAQpB,EAAKqB,WAAWF,GAE1B,OAAOD,CACT,CAoCA,SAASI,EACPC,EACAC,EACA7J,EACA8J,EACAxJ,GAEA,MAAMyJ,EAAkBzJ,IAAagG,EAAAA,GAAa0D,OAAS1J,IAAagG,EAAAA,GAAa2D,cAC/EC,EAAoBN,EAAgBP,SAAS,SAAWU,EAI9D,GAAoB,MAAhBF,GAAuBE,EAAiB,CAE1C,MAAMI,EAAsB,IAAIC,OAAO,OAAQ,KAC/CR,EAAkBA,EAAgBnK,QAAQ0K,EAAqB,KACjE,MAAO,GAAoB,MAAhBN,EAAqB,CAE9B,MAAMQ,EAA2B,IAAID,OAAO,QAAU,KACtDR,EAAkBA,EAAgBnK,QAAQ4K,EAA0B,KACpE,MAAMF,EAAsB,IAAIC,OAAO,OAAQ,KAC/CR,EAAkBA,EAAgBnK,QAAQ0K,EAAqB,KACjE,CAeA,OAbID,IAEFN,EAAkBA,EAAgBnK,QAAQ,OAAQ,KAGpDO,EAAYqD,KAAK,CACfzC,IAAKsJ,EACD3D,EAAAA,GAAwB+D,gBAAgB7I,WACxC8E,EAAAA,GAAwBgE,cAAc9I,WAAa,IAAMqI,EAAMrI,WACnEnB,SAAUA,EACVG,MAAOmJ,IAGFA,CACT,CAEA,SAASY,EAAoBZ,EAAyB3J,EAAqCK,GACzF,MAAM+J,EAA2B,IAAID,OAAO,MAAO,KACnDR,EAAkBA,EAAgBnK,QAAQ4K,EAA0B,KACpEpK,EAAeoD,KAAK,CAClB/C,WACAG,MAAOmJ,GAEX,CAoDA,SAASa,EAAwBC,GAC/B,OAAIpB,EAA4BoB,EAASC,EAAAA,IAAKxI,OACrCyI,EAAAA,GAAeC,IACbvB,EAA4BoB,EAASI,EAAAA,IAAK3I,OAC5CyI,EAAAA,GAAeG,GACbzB,EAA4BoB,EAASM,EAAAA,IAAK7I,OAC5CyI,EAAAA,GAAeK,IACb3B,EAA4BoB,EAASQ,EAAAA,IAAK/I,OAC5CyI,EAAAA,GAAeO,QAGxBpE,QAAQK,KAAK,2BAGf,CAEA,SAASgE,EAAuBV,GAC9B,OAAIpB,EAA4BoB,EAASW,EAAAA,IAAIlJ,OACpCyI,EAAAA,GAAeU,MACbhC,EAA4BoB,EAASa,EAAAA,IAAKpJ,OAC5CyI,EAAAA,GAAeY,SACblC,EAA4BoB,EAASe,EAAAA,IAAItJ,OAC3CyI,EAAAA,GAAec,WACbpC,EAA4BoB,EAASiB,EAAAA,IAAKxJ,OAC5CyI,EAAAA,GAAegB,mBADjB,CAKT,CA2FO,SAAS1L,EACdwI,EACA1J,EACAC,GAOA,MAAM4M,EAA+B,GAC/B7L,EAAgC,GAChCC,EAAsC,GACtCH,EAAwB,GACxBM,EAAW0I,EAAkBJ,EAAO,CAACoD,EAAAA,KAE3C,GAAwB,IAApB1L,EAAS+B,OACX,MAAO,CAAEpC,aAAc8L,GAUzB,OAvRF,SAA2BnD,EAAemD,GAExC,MAAME,EAAajD,EAAkBJ,EAAO,CAACsD,EAAAA,KAC7C,IAAK,MAAMtB,KAAWqB,EAAY,CAChC,MAAME,EAAqB3C,EAA4BoB,EAASwB,EAAAA,IAChE,IAAKD,GAAoD,IAA9BA,EAAmB9J,OAC5C,SAGF,MAAMgK,EAAgB7C,EAA4BoB,EAAS0B,EAAAA,IACrD9L,EAAWoI,EAAMC,UAAUsD,EAAmB,GAAGtK,GAAIwK,EAAc,GAAG5K,MACtEX,EAAMqL,EAAmB,GAAGxD,cAAcC,GAC1CjI,EAAQ0L,EAAcE,IAAK9D,GAAaG,EAAMC,UAAUJ,EAAShH,KAAO,EAAGgH,EAAS5G,GAAK,IAAI,GAGhGf,GACAH,IACAH,IAAasK,EAAAA,GAAeY,UAC3BlL,IAAasK,EAAAA,GAAeU,OAC5BhL,IAAasK,EAAAA,GAAec,YAC5BpL,IAAasK,EAAAA,GAAegB,gBAKhCC,EAAOxI,KAAK,CACVzC,MACAN,WACAjB,KAAMgD,EAAAA,EAAU4B,QAChBxD,SAEJ,CACF,CAmPE6L,CAFsBhD,EAA4BlJ,EAAS,GAAI0L,EAAAA,IAAU,GAAGrD,cAAcC,GAEzDmD,GAhMnC,SAA0BnD,EAAe1I,EAA+BC,GACtE,MAAMsM,EAAiBzD,EAAkBJ,EAAO,CAAC8D,EAAAA,KACjD,IAAK,MAAO1C,EAAOY,KAAY6B,EAAeE,UAAW,CACvD,MAAMC,EAAQpD,EAA4BoB,EAASiC,EAAAA,IAC7CC,EAAatD,EAA4BoB,EAASmC,EAAAA,IAClDC,EAAWxD,EAA4BoB,EAASa,EAAAA,IAChDwB,EAAiBzD,EAA4BoB,EAASiB,EAAAA,IACtDqB,EAAiB1D,EAA4BoB,EAASuC,EAAAA,IACtDC,EAAiB5D,EAA4BoB,EAASyC,EAAAA,IAEtDC,EAAuBC,EAAyB3C,GAEtD,IAAK,MAAM4C,KAAuBF,EAAsB,CACtD,MAAMvD,EAAcnB,EAAMC,WAAU2E,aAAAA,EAAAA,EAAqB/L,MAAO,EAAG+L,aAAAA,EAAAA,EAAqB/L,MAGxF,IAAIqI,EAAkBlB,EAAMC,WAAU2E,aAAAA,EAAAA,EAAqB/L,MAAO,GAAG+L,aAAAA,EAAAA,EAAqB3L,IAAK,GAE/F,GAAIiI,EAAgBzH,OAAQ,CAC1B,IAAI7B,EACJ,GAAIoM,EAAMvK,OACR7B,EAAWgG,EAAAA,GAAa/C,WACnB,GAAIuJ,EAAS3K,OAClB7B,EAAWgG,EAAAA,GAAaiH,mBACnB,GAAIR,EAAe5K,OACxB7B,EAAWgG,EAAAA,GAAa2D,mBACnB,GAAI2C,EAAWzK,OACpB7B,EAAWgG,EAAAA,GAAa0D,WACnB,GAAIgD,EAAe7K,OACxB7B,EAAWgD,EAAAA,GAAgBC,UACtB,KAAI2J,EAAe/K,OAEnB,CACL4E,QAAQK,KAAK,sBAAuB,CAClCsB,MAAOA,EAAMC,UAAU+B,EAAQnJ,KAAMmJ,EAAQ/I,MAG/C,QACF,CAPErB,EAAWgD,EAAAA,GAAgBiK,aAO7B,CAEMjN,IAAagD,EAAAA,GAAgBC,OAASjD,IAAagD,EAAAA,GAAgBiK,cACvE5D,EAAuBC,EAAiBC,EAAa7J,EAAa8J,EAAOxJ,GAEzEkK,EAAoBZ,EAAiB3J,EAAgBK,EAEzD,CACF,CACF,CACF,CAiJEkN,CAAiB9E,EAAO1I,EAAaC,GAjHvC,SACEyI,EACA5I,EACAd,EACAC,G,IAEkBD,EAAlB,MAAMyO,EAAYzO,SAAa,QAAbA,EAAAA,EAAS+I,YAAT/I,IAAAA,OAAAA,EAAAA,EAAe0O,OAAOxO,KAAMyO,GAAUA,EAAMC,SAAU3O,aAAAA,EAAAA,EAAW2O,QAE7EC,EAAY/E,EAAkBJ,EAAO,CAACoF,EAAAA,KAC5C,IAAK,MAAMpD,KAAWmD,EAAW,C,IAsBbE,EArBlB,MACMC,EADW7F,EAAaC,SAASsC,GACXjC,cAAcC,GAI1C,GAHqBgC,EAAQuD,SAASH,EAAAA,IAIpC,SAIF,GAAmC,cAA/BE,EAAWrF,UAAU,EAAG,GAC1B,SAKF,MAAMuF,EAAepF,EAAkBJ,EAAMC,UAAU,EAAG+B,EAAQrC,KAAK1G,IAAK,CAACwM,EAAAA,KACvEC,EAAatF,EAAkBJ,EAAMC,UAAU,EAAG+B,EAAQrC,KAAK1G,IAAK,CAAC0M,EAAAA,KAIrEC,EAA4B,QAAhBP,EADIzE,EAA4BoB,EAASwB,EAAAA,IAC3B,UAAd6B,IAAAA,OAAAA,EAAAA,EAAkBtF,cAAcC,GAG5C6F,EAAmBjF,EAA4BoB,EAAS0B,EAAAA,IACxDoC,EAAmBlF,EAA4BoB,EAAS+D,EAAAA,IACxDC,EAAkBpF,EAA4BoB,EAASiE,EAAAA,IACvDC,EAAqBtF,EAA4BoB,EAASmE,EAAAA,IAEhE,IAAIjM,EAAoBtC,EAmBpBwO,EAlBJ,GAAIP,EAAiBpM,OACnB7B,EAAW8K,EAAuBV,GAElC9H,EAAa8F,EAAMC,UAAU4F,EAAiB,GAAGhN,KAAO,EAAGgN,EAAiB,GAAG5M,GAAK,QAC/E,GAAI6M,EAAiBrM,OAC1BS,EAAa4L,EAAiB,GAAG/F,cAAcC,GAC/CpI,EAAWmK,EAAwBC,QAC9B,GAAIkE,EAAmBzM,OAC5B7B,EAAWmK,EAAwBC,GACnC9H,EAAagM,EAAmB,GAAGnG,cAAcC,OAC5C,KAAIgG,EAAgBvM,OAIzB,SAHA7B,EAAWmK,EAAwBC,GACnC9H,EAAa8L,EAAgB,GAAGjG,cAAcC,EAGhD,C,IAOcqG,EAGd,GANItB,IAGFqB,EAA6CrB,QAAjCsB,GAAAA,EAAAA,EAAAA,GAAsBT,EAAWb,UAAjCsB,IAAAA,EAAAA,OAA+C3F,GAGzD9I,EAAU,CACZ,IAAIuC,EACAqL,EAAa/L,QAAUiM,EAAWjM,OACpCU,EAAS,QACAqL,EAAa/L,OACtBU,EAAS,SACAuL,EAAWjM,OACpBU,EAAS,OAGTiM,EAAYzM,EAAAA,EAAUC,mBAGxBxC,EAAOuD,KAAK,CACVzC,IAAK0N,EACLhO,SAAUA,EACVuC,SACAxD,KAAMyP,QAAAA,EAAazM,EAAAA,EAAU2M,OAC7BvO,MAAOmC,GAEX,CACF,CACF,CA2BEqM,CAAYvG,EAAO5I,EAAQd,EAASC,GAE7B,CAAEa,SAAQC,aAAc8L,EAAQ7L,cAAaC,iBACtD,CAqBO,MAAMiP,EAAU,EAChB,SAASC,EAAazG,GAC3B,OAA2C,IArBtC,SAAyBA,EAAe0G,GAC7C,IAAIC,GAAkB,EAUtB,OATaxM,EAAAA,GAAOoG,MAAMP,GACrBQ,QAAQ,CACXC,MAAO,EAAG9J,WACR,GAAIA,EAAKnC,KAAOkS,EAEd,OADAC,GAAkB,GACX,KAINA,CACT,CASSA,CAAgB3G,EAAOwG,EAChC,CAEA,SAAS7B,EAAyBxB,GAChC,MAAM7C,EAAsB,GAC5B,IAAIX,EAA0BwD,EAC9B,EAAG,CACD,MAAMyD,EAASjH,EAAK4F,SAAS7B,EAAAA,IACzBkD,IAAWjH,EAAK4F,SAAS5H,EAAAA,KAC3B2C,EAAM3F,KAAKiM,GAEbjH,EAAOA,EAAK4F,SAASsB,EAAAA,GACvB,OAAiB,MAARlH,GAET,OAAOW,CACT,C,wDC/ZO,WAAKwG,G,+DAAAA,C,CAAL,C,IA6BA,SAAST,EAAsBU,EAAkB9B,EAAkB7D,EAAQ,G,IAC9D6D,EAAlB,MAAM+B,EAAwD,QAA5C/B,EAAAA,EAAM7N,OAAOZ,KAAMkD,GAAyB,eAAfA,EAAMuN,aAAnChC,IAAAA,OAAAA,EAAAA,EAA2DiC,OAAO9F,GACpF,IAAK4F,EACH,OAAO,KAET,OAAQA,EAAUD,IAChB,IAAK,IACH,OAAOpN,EAAAA,EAAU4B,QACnB,IAAK,IACH,OAAO5B,EAAAA,EAAUC,mBACnB,IAAK,IACH,OAAOD,EAAAA,EAAU2M,OACnB,QACE,OAAO,KAEb,C,wOCzCA,MAAMa,EAASC,GAAyC,iBAANA,GAAwB,OAANA,EAE7D,SAASC,EAA+BhI,EAAciI,GAC3D,OAAOA,KAAQjI,CACjB,CAEA,MAAMkI,EAAYC,GAA6B,iBAANA,GAAkBA,GAAM,GAEpDxI,EAAYyI,GAAgE,iBAARA,EAE1E,SAASC,EAAiBrP,GAC/B,IAAIsP,EAAoB,GACxB,GAAIC,MAAMC,QAAQxP,GAChB,IAAK,IAAIyP,EAAI,EAAGA,EAAIzP,EAAEoB,OAAQqO,IAC5BH,EAAQhN,KAAK4M,EAASlP,EAAEyP,KAG5B,OAAOH,CACT,CAEO,SAASI,EAAuBX,GACrC,MAAMY,EAAWb,EAAMC,IAAMC,EAAQD,EAAG,QAAUC,EAAQD,EAAG,OAASA,EAEtE,GAAIY,EAAU,CACZ,MAAMC,EAA8B,iBAAjBD,EAASC,KAAoBD,EAASC,IACnDzT,EAA4B,iBAAhBwT,EAASxT,IAAmBwT,EAASxT,GACvD,IAAW,IAAPA,IAAwB,IAARyT,EAClB,MAAO,CAAEzT,KAAIyT,MAEjB,CAEA,OAAO,CACT,CAEO,SAASC,EAA4Bd,GAC1C,MAAoB,iBAANA,IAAyB,SAANA,GAAsB,UAANA,GAAuB,SAANA,IAAiBA,CACrF,CACO,SAASe,EAAoBf,GAClC,MAAiB,iBAANA,GAAkBA,IAAMgB,EAAAA,cAAcC,UAAUtP,WAClDqP,EAAAA,cAAcC,UAGN,iBAANjB,GAAkBA,IAAMgB,EAAAA,cAAcE,WAAWvP,YACnDqP,EAAAA,cAAcE,UAIzB,CAEO,SAASC,EAAiBnB,GAC/B,MAAMY,EAAWb,EAAMC,IAAMC,EAAQD,EAAG,UAAYC,EAAQD,EAAG,WAAaA,EAE5E,GAAIY,EAAU,CACZ,MAAM7N,EACuB,iBAApB6N,EAAS7N,SACK,WAApB6N,EAAS7N,QACY,SAApB6N,EAAS7N,QACW,UAApB6N,EAAS7N,QACW,uBAApB6N,EAAS7N,SACX6N,EAAS7N,OACLpC,EAAkC,iBAAnBiQ,EAASjQ,OAAsBiQ,EAASjQ,MAE7D,IAAe,IAAXoC,IAA8B,IAAVpC,EACtB,MAAO,CAAEoC,SAAQpC,QAErB,CAEA,OAAO,CACT,CAEO,SAASyQ,EAAyBpB,GACvC,MAAMY,EAAWb,EAAMC,IAAMpI,EAASoI,IAAMA,EAE5C,GAAIY,EAAU,CACZ,MAAM9I,EAAOD,OAAOC,KAAK8I,GACnBS,EAAuC,CAAC,EAC9C,IAAK,IAAIX,EAAI,EAAGA,EAAI5I,EAAKzF,OAAQqO,IAAK,CACpC,MAAM5P,EAAMgH,EAAK4I,GACX/P,EAAQiQ,EAAS9I,EAAK4I,IACP,iBAAV/P,IACT0Q,EAAavQ,GAAOH,EAExB,CAEA,OAAO0Q,CACT,CAEA,OAAO,CACT,CAEO,SAASC,EAAgBC,GAC9B,MAAMC,EAAQzB,EAAMwB,IAAiBtB,EAAQsB,EAAc,OAAStB,EAAQsB,EAAc,SAAWA,EACrG,GAAIC,EAAO,CACT,MAAM3P,EAAKsO,EAASqB,EAAM3P,IACpBJ,EAAO0O,EAASqB,EAAM/P,MAC5B,GAAII,GAAMJ,EACR,MAAO,CAAEA,OAAMI,KAEnB,CAGF,CAEO,SAAS4P,EAAmBhK,GACjC,MAAML,EAAM2I,EAAMtI,IAAMwI,EAAQxI,EAAG,UAAY0I,EAAS1I,EAAEX,OAC1D,GAAIM,EACF,OAAOA,CAGX,CAEO,SAASsK,EAAqBC,GACnC,OAAQA,GACN,KAAKvL,EAAAA,GAAcoF,MACnB,KAAKpF,EAAAA,GAAcsF,SACnB,KAAKtF,EAAAA,GAAcwF,WACnB,KAAKxF,EAAAA,GAAc0F,cACnB,KAAKxF,EAAAA,GAAgB+E,GACrB,KAAK/E,EAAAA,GAAgB6E,IACrB,KAAK7E,EAAAA,GAAgB2E,GACrB,KAAK3E,EAAAA,GAAgByE,IACnB,OAAO4G,EACT,QACE,MAAM,IAAIC,EAAe,wBAE/B,CAYO,SAASC,EAAsBC,GACpC,OAAOC,EAAeD,IAAUE,EAAgBF,EAClD,CAEO,SAASE,EAAgBF,GAC9B,OAAQA,IAAUvT,EAAAA,GAAW+D,OAASwP,IAAUvT,EAAAA,GAAW0T,QAAUH,CACvE,CAEO,SAASC,EAAeD,GAI7B,MAHqB,iBAAVA,IACTA,EAAQA,EAAMI,gBAGbJ,IAAUxT,EAAAA,GAAU0B,QACnB8R,IAAUxT,EAAAA,GAAU6T,QACpBL,IAAUxT,EAAAA,GAAU8T,MACpBN,IAAUxT,EAAAA,GAAUgF,WACtBwO,CAEJ,CAEO,SAASO,EAAqCrN,GACnD,OAAOwL,MAAMC,QAAQzL,EAAa5G,EAAAA,KAChC4G,EAAa5G,EAAAA,GAAsB,IACc,iBAA1C4G,EAAa5G,EAAAA,GAAsB,GACxC4G,EAAa5G,EAAAA,GAAsB,GACW,iBAAvC4G,EAAa5G,EAAAA,IAAsC4G,EAAa5G,EAAAA,EAC7E,CAEO,SAASkU,EAA+BtN,GAC7C,OAAO6M,EACLrB,MAAMC,QAAQzL,EAAa7G,EAAAA,IAAmB6G,EAAa7G,EAAAA,GAAgB,GAAK6G,EAAa7G,EAAAA,GAEjG,CAEO,SAASoU,EAAkCC,GAChD,GAAIzC,EAAMyC,IAAYvC,EAAQuC,EAAS,SAAWvC,EAAQuC,EAAS,QAAS,CAG1E,MAAO,CACLC,KAHWtC,EAASqC,EAAQC,MAI5B5C,KAHWM,EAASqC,EAAQ3C,MAKhC,CACA,OAAO,CACT,CAEO,MAAM+B,UAAuBzJ,O,qFC/L7B,MAAM5H,EAAuBoR,GAC3BA,IAAOpL,EAAAA,GAASiF,OAASmG,IAAOpL,EAAAA,GAASqF,WAErC8G,EAAuBf,GAC3BA,IAAOpL,EAAAA,GAASmF,UAAYiG,IAAOpL,EAAAA,GAASuF,cAExC6G,EAAmBhB,GACvBA,IAAOpL,EAAAA,GAASqF,YAAc+F,IAAOpL,EAAAA,GAASuF,cAE1C8G,EAAqBjB,GACzBkB,EAAAA,GAAqBtJ,SAASoI,E,+FCVhC,SAASmB,EAAuBnB,GACrC,GAAIA,IAAOpL,EAAAA,GAASmF,SAClB,MAAO,YAET,GAAIiG,IAAOpL,EAAAA,GAASuF,cAClB,MAAO,uBAET,GAAI6F,IAAOpL,EAAAA,GAASiF,MAClB,MAAO,SAET,GAAImG,IAAOpL,EAAAA,GAASqF,WAClB,MAAO,gBAET,GAAI+F,IAAOpL,EAAAA,GAAS0E,GAClB,MAAO,YAET,GAAI0G,IAAOpL,EAAAA,GAAS8E,GAClB,MAAO,eAET,GAAIsG,IAAOpL,EAAAA,GAAS4E,IAClB,MAAO,2BAET,GAAIwG,IAAOpL,EAAAA,GAASwE,IAClB,MAAO,wBAGT,MAAMjE,EAAQ,IAAIqB,MAAM,qBAExB,MADAtB,EAAAA,EAAOC,MAAMA,EAAO,CAAEM,IAAK,mBAAoB5G,SAAUmR,IACnD7K,CACR,CC3BO,MAAMiM,EAAY,CAACxM,EAAAA,GAASiF,MAAOjF,EAAAA,GAASmF,SAAUnF,EAAAA,GAASqF,WAAYrF,EAAAA,GAASuF,eAAeS,IAExG,CAAC5L,EAAOqJ,EAAOgJ,KACR,CACL/U,YAAa6U,EAAuBnS,GACpCsR,MAAOtR,EACPA,WAISsS,EAAmB,CAAC1M,EAAAA,GAASiF,MAAOjF,EAAAA,GAASqF,YAAYW,IAA8B5L,IAAW,CAC7G1C,YAAa6U,EAAuBnS,GACpCsR,MAAOtR,EACPA,WAGWkS,EAAuB,CAACtM,EAAAA,GAAS8E,GAAI9E,EAAAA,GAAS4E,IAAK5E,EAAAA,GAAS0E,GAAI1E,EAAAA,GAASwE,KAEzEmI,EAAmBL,EAAqBtG,IAA8B5L,IAAW,CAC5F1C,YAAa6U,EAAuBnS,GACpCsR,MAAOtR,EACPA,WAGWwS,EAAyC,CACpD,CAAElB,MAAO,QAAStR,MAAO6F,EAAAA,GAAa/C,OACtC,CAAEwO,MAAO,gBAAiBtR,MAAO6F,EAAAA,GAAaiH,eAC9C,CAAEwE,MAAO,QAAStR,MAAO6F,EAAAA,GAAa0D,OACtC,CAAE+H,MAAO,gBAAiBtR,MAAO6F,EAAAA,GAAa2D,e,gDC7BzC,SAASvG,EAAqBN,GACnC,MACM8P,EADkB9P,EAASyI,OAAQrI,GAA6B,YAAjBA,EAAQnE,MAE1DgN,IAAK8G,GAAM,QAAOrN,EAAAA,EAAAA,GAAgCqN,EAAE3P,aACpDyC,KAAK,KACLmN,OAEGC,EAAkBjQ,EAASyI,OAAQrI,GAA6B,YAAjBA,EAAQnE,MAC7D,IAAIiU,EAAsB,GAU1B,OATID,EAAgBlR,OAAS,IAEzBmR,EAD6B,IAA3BD,EAAgBlR,OACI,QAAO2D,EAAAA,EAAAA,GAAgCuN,EAAgB,GAAG7P,YAE1D,MAAM6P,EACzBhH,IAAK8G,GAAM,KAAIrN,EAAAA,EAAAA,GAAgCqN,EAAE3P,aACjDyC,KAAK,WAGL,GAAGiN,KAAuBI,IAAsBF,MACzD,C,2dCSO,MAAM7O,EAAa,UACbgP,EAAkB,aAClBC,EAAqB,kBACrBC,EAA0B,qBAC1BjP,EAAa,SACbkP,EAAkB,YAClBC,EAAsB,mBACtBC,EAAwB,qBACxBC,EAA0B,aAC1BpP,EAAe,WACfqP,EAAoB,cACpBlP,EAAe,WACfmP,EAAoB,cACpBrP,EAAa,SACbsP,EAAkB,YAClBC,EAAqB,UACrBC,EAAqB,UACrBC,EAA0B,aAC1BC,EAA2B,uBAE3BC,EAAoB,gBACpBC,EAAyB,mBACzBhQ,EAAiB,KACjBiQ,EAAsB,QACtBC,EAAkB,aAClBC,EAAuB,gBAEvBC,EAAkB,aAClBC,EAAuB,gBAIvBC,EAAoB,UAAUH,iDAC9BI,EAAmB,UAAUJ,wCAE7BK,EAAmB,WAEnBC,EAAkB,aAClBC,EAAuB,gBAIvBC,EAAkB,eAGlBtQ,EAAmB,cACnBuQ,EAAwB,iBACxBC,EAA2B,IAAI5B,MAAoBS,KAAmBF,KAAqBC,KAAqBmB,YAAgCT,kDAAqEf,KAAmBiB,IAExOS,EAA6B,IAAI7B,MAAoBS,KAAmBF,KAAqBC,KAAqBmB,KAAyBN,KAAqBlB,IAChK2B,EAA0C,IAAI9B,MAAoBS,KAAmBJ,KAAyBG,KAAqBmB,KAnB/F,UAAUT,oDAmBmJd,IAEjM2B,EAA8B,IAAI/B,MAAoBI,KAAuBG,KAAqBC,KAAqBmB,KAAyBF,KAAwBtB,IACxK6B,EAAgC,IAAIhC,MAAoBO,KAAqBC,KAAqBiB,IAClGQ,EAAkC,GAAGjC,KAAmBS,KAAmBF,KAAqBC,KAAqBmB,KAAyBxB,IAC9I+B,EAAiB,CAAE/V,IAAK6U,GACxBmB,EAAqB,SACrBnT,EAAuB,iBACvB1B,EAAe,eACf8U,EAAmB,UACnBC,EAAyB,yBAEzB9R,EAAuB,KAIvB+R,EAAgC,UACtC,SAAStQ,EAAgC9E,EAAQ,IACtD,OAAIA,EAAMqV,WAAWD,GACZpV,EAAMkI,UAAUkN,EAA8B1T,QAEhD1B,CACT,CACO,SAASsV,EAA4BtV,EAAQ,IAClD,OAAOA,EAAMqV,WAAWD,EAC1B,CACO,SAAS9R,GAA8BtD,EAAQ,IACpD,OAAOoV,EAAgCpV,CACzC,C,WC9GAuV,EAAOC,QAAUC,C,WCAjBF,EAAOC,QAAUE,C,WCAjBH,EAAOC,QAAUG,C,WCAjBJ,EAAOC,QAAUI,C,WCAjBL,EAAOC,QAAUK,C,WCAjBN,EAAOC,QAAUM,C,WCAjBP,EAAOC,QAAUO,C,WCAjBR,EAAOC,QAAUQ,C,UCAjBT,EAAOC,QAAUS,C,WCAjBV,EAAOC,QAAUU,C,WCAjBX,EAAOC,QAAUW,C,WCAjBZ,EAAOC,QAAUY,C,wSCGjB,MAAMC,EAAsB,KAC5B,IAAIC,EAAa,EACjB,MAAMC,EACF,WAAApO,CAAYrH,EAAMI,GACd6G,KAAKjH,KAAOA,EACZiH,KAAK7G,GAAKA,CACd,EAOJ,MAAMsV,EAIF,WAAArO,CAAYsO,EAAS,CAAC,GAClB1O,KAAKtL,GAAK6Z,IACVvO,KAAK2O,UAAYD,EAAOC,QACxB3O,KAAK4O,YAAcF,EAAOE,aAAe,MACrC,MAAM,IAAInP,MAAM,uDACnB,EACL,CAUA,GAAAoP,CAAI9T,GACA,GAAIiF,KAAK2O,QACL,MAAM,IAAIG,WAAW,0CAGzB,MAFoB,mBAAT/T,IACPA,EAAQgU,EAAShU,MAAMA,IACnBlE,IACJ,IAAImY,EAASjU,EAAMlE,GACnB,YAAkB+J,IAAXoO,EAAuB,KAAO,CAAChP,KAAMgP,GAEpD,EAQJP,EAASQ,SAAW,IAAIR,EAAS,CAAEG,YAAaM,GAAOA,EAAIhX,MAAM,OAMjEuW,EAASU,SAAW,IAAIV,EAAS,CAAEG,YAAaM,GAAOA,EAAIhX,MAAM,OAMjEuW,EAASW,MAAQ,IAAIX,EAAS,CAAEG,YAAaM,GAAOA,EAAIhX,MAAM,OAY9DuW,EAASY,QAAU,IAAIZ,EAAS,CAAEG,YAAa3W,IACvC,GAAIA,GAAkB,OAATA,GAA2B,OAATA,GAA2B,QAATA,EAC7C,MAAM,IAAI6W,WAAW,8BAAgC7W,GACzD,OAAOA,GAAS,UAOxBwW,EAASa,YAAc,IAAIb,EAAS,CAAEE,SAAS,IAO/CF,EAASc,UAAY,IAAId,EAAS,CAAEE,SAAS,IAM7CF,EAASe,QAAU,IAAIf,EAAS,CAAEE,SAAS,IAM3C,MAAMc,EACF,WAAArP,CAIAsP,EAUAC,EAIAtV,GACI2F,KAAK0P,KAAOA,EACZ1P,KAAK2P,QAAUA,EACf3P,KAAK3F,OAASA,CAClB,CAIA,UAAOuV,CAAIF,GACP,OAAOA,GAAQA,EAAKza,OAASya,EAAKza,MAAMwZ,EAASe,QAAQ9a,GAC7D,EAEJ,MAAMmb,EAAU1Q,OAAO2Q,OAAO,MAI9B,MAAMf,EAIF,WAAA3O,CAOA+G,EAIAlS,EAKAP,EAIAqb,EAAQ,GACJ/P,KAAKmH,KAAOA,EACZnH,KAAK/K,MAAQA,EACb+K,KAAKtL,GAAKA,EACVsL,KAAK+P,MAAQA,CACjB,CAIA,aAAOC,CAAOC,GACV,IAAIhb,EAAQgb,EAAKhb,OAASgb,EAAKhb,MAAM0E,OAASwF,OAAO2Q,OAAO,MAAQD,EAChEE,GAASE,EAAKC,IAAM,EAAuB,IAAMD,EAAKE,QAAU,EAA2B,IAC1FF,EAAK7R,MAAQ,EAAyB,IAAmB,MAAb6R,EAAK9I,KAAe,EAA6B,GAC9FtQ,EAAO,IAAIkY,EAASkB,EAAK9I,MAAQ,GAAIlS,EAAOgb,EAAKvb,GAAIqb,GACzD,GAAIE,EAAKhb,MACL,IAAK,IAAImb,KAAOH,EAAKhb,MAGjB,GAFK6S,MAAMC,QAAQqI,KACfA,EAAMA,EAAIvZ,IACVuZ,EAAK,CACL,GAAIA,EAAI,GAAGzB,QACP,MAAM,IAAIG,WAAW,8CACzB7Z,EAAMmb,EAAI,GAAG1b,IAAM0b,EAAI,EAC3B,CAER,OAAOvZ,CACX,CAKA,IAAA2Q,CAAKA,GAAQ,OAAOxH,KAAK/K,MAAMuS,EAAK9S,GAAK,CAIzC,SAAI2b,GAAU,OAAqB,EAAbrQ,KAAK+P,OAAgC,CAAG,CAI9D,aAAIO,GAAc,OAAqB,EAAbtQ,KAAK+P,OAAoC,CAAG,CAItE,WAAIQ,GAAY,OAAqB,EAAbvQ,KAAK+P,OAAkC,CAAG,CAKlE,eAAIS,GAAgB,OAAqB,EAAbxQ,KAAK+P,OAAsC,CAAG,CAK1E,EAAAU,CAAGtJ,GACC,GAAmB,iBAARA,EAAkB,CACzB,GAAInH,KAAKmH,MAAQA,EACb,OAAO,EACX,IAAIiI,EAAQpP,KAAKwH,KAAKiH,EAASW,OAC/B,QAAOA,GAAQA,EAAMsB,QAAQvJ,IAAS,CAC1C,CACA,OAAOnH,KAAKtL,IAAMyS,CACtB,CASA,YAAOpM,CAAM8I,GACT,IAAI8M,EAASxR,OAAO2Q,OAAO,MAC3B,IAAK,IAAItI,KAAQ3D,EACb,IAAK,IAAIsD,KAAQK,EAAKtP,MAAM,KACxByY,EAAOxJ,GAAQtD,EAAI2D,GAC3B,OAAQ3H,IACJ,IAAK,IAAI+Q,EAAS/Q,EAAK2H,KAAKiH,EAASW,OAAQpH,GAAK,EAAGA,GAAK4I,EAASA,EAAOjX,OAAS,GAAIqO,IAAK,CACxF,IAAI6I,EAAQF,EAAO3I,EAAI,EAAInI,EAAKsH,KAAOyJ,EAAO5I,IAC9C,GAAI6I,EACA,OAAOA,CACf,EAER,EAKJ9B,EAAS+B,KAAO,IAAI/B,EAAS,GAAI5P,OAAO2Q,OAAO,MAAO,EAAG,GAUzD,MAAMiB,EAKF,WAAA3Q,CAIA4Q,GACIhR,KAAKgR,MAAQA,EACb,IAAK,IAAIhJ,EAAI,EAAGA,EAAIgJ,EAAMrX,OAAQqO,IAC9B,GAAIgJ,EAAMhJ,GAAGtT,IAAMsT,EACf,MAAM,IAAI8G,WAAW,8EACjC,CAMA,MAAAmC,IAAUhc,GACN,IAAIic,EAAW,GACf,IAAK,IAAIra,KAAQmJ,KAAKgR,MAAO,CACzB,IAAIG,EAAW,KACf,IAAK,IAAIC,KAAUnc,EAAO,CACtB,IAAI4Z,EAAMuC,EAAOva,GACbgY,IACKsC,IACDA,EAAWhS,OAAOkS,OAAO,CAAC,EAAGxa,EAAK5B,QACtCkc,EAAStC,EAAI,GAAGna,IAAMma,EAAI,GAElC,CACAqC,EAASrW,KAAKsW,EAAW,IAAIpC,EAASlY,EAAKsQ,KAAMgK,EAAUta,EAAKnC,GAAImC,EAAKkZ,OAASlZ,EACtF,CACA,OAAO,IAAIka,EAAQG,EACvB,EAEJ,MAAMI,EAAa,IAAIC,QAAWC,EAAkB,IAAID,QAKxD,IAAIE,GACJ,SAAWA,GAMPA,EAASA,EAAyB,eAAI,GAAK,iBAM3CA,EAASA,EAA2B,iBAAI,GAAK,mBAM7CA,EAASA,EAAuB,aAAI,GAAK,eAOzCA,EAASA,EAAyB,eAAI,GAAK,gBAC9C,CA1BD,CA0BGA,IAAaA,EAAW,CAAC,IAiB5B,MAAMC,EAIF,WAAAtR,CAIAvJ,EAIA8a,EAKA5Q,EAIApH,EAIA1E,GASI,GARA+K,KAAKnJ,KAAOA,EACZmJ,KAAK2R,SAAWA,EAChB3R,KAAKe,UAAYA,EACjBf,KAAKrG,OAASA,EAIdqG,KAAK/K,MAAQ,KACTA,GAASA,EAAM0E,OAAQ,CACvBqG,KAAK/K,MAAQkK,OAAO2Q,OAAO,MAC3B,IAAK,IAAKtI,EAAMvP,KAAUhD,EACtB+K,KAAK/K,MAAqB,iBAARuS,EAAmBA,EAAOA,EAAK9S,IAAMuD,CAC/D,CACJ,CAIA,QAAAgB,GACI,IAAIuW,EAAUC,EAAYG,IAAI5P,MAC9B,GAAIwP,IAAYA,EAAQG,QACpB,OAAOH,EAAQE,KAAKzW,WACxB,IAAI0Y,EAAW,GACf,IAAK,IAAIC,KAAM5R,KAAK2R,SAAU,CAC1B,IAAIzC,EAAM0C,EAAG3Y,WACTiW,IACIyC,IACAA,GAAY,KAChBA,GAAYzC,EAEpB,CACA,OAAQlP,KAAKnJ,KAAKsQ,MACb,KAAK0K,KAAK7R,KAAKnJ,KAAKsQ,QAAUnH,KAAKnJ,KAAK0Z,QAAUhW,KAAKC,UAAUwF,KAAKnJ,KAAKsQ,MAAQnH,KAAKnJ,KAAKsQ,OACzFwK,EAAShY,OAAS,IAAMgY,EAAW,IAAM,IAFzBA,CAG7B,CAMA,MAAAG,CAAOC,EAAO,GACV,OAAO,IAAIC,EAAWhS,KAAKiS,QAASF,EACxC,CAMA,QAAAG,CAASlR,EAAKmR,EAAO,EAAGJ,EAAO,GAC3B,IAAIK,EAAQd,EAAW1B,IAAI5P,OAASA,KAAKiS,QACrCH,EAAS,IAAIE,EAAWI,GAG5B,OAFAN,EAAOO,OAAOrR,EAAKmR,GACnBb,EAAW7U,IAAIuD,KAAM8R,EAAOQ,OACrBR,CACX,CAKA,WAAIG,GACA,OAAO,IAAIM,EAASvS,KAAM,EAAG,EAAG,KACpC,CAYA,OAAAwS,CAAQxR,EAAKmR,EAAO,GAChB,IAAItS,EAAO4S,EAAYnB,EAAW1B,IAAI5P,OAASA,KAAKiS,QAASjR,EAAKmR,GAAM,GAExE,OADAb,EAAW7U,IAAIuD,KAAMH,GACdA,CACX,CAQA,YAAA6S,CAAa1R,EAAKmR,EAAO,GACrB,IAAItS,EAAO4S,EAAYjB,EAAgB5B,IAAI5P,OAASA,KAAKiS,QAASjR,EAAKmR,GAAM,GAE7E,OADAX,EAAgB/U,IAAIuD,KAAMH,GACnBA,CACX,CAQA,YAAA8S,CAAa3R,EAAKmR,EAAO,GACrB,OAwcR,SAAuBzC,EAAM1O,EAAKmR,GAC9B,IAAIS,EAAQlD,EAAKgD,aAAa1R,EAAKmR,GAAOU,EAAS,KACnD,IAAK,IAAIC,EAAOF,aAAiBL,EAAWK,EAAQA,EAAMpc,QAAQuc,OAAQD,EAAMA,EAAOA,EAAKC,OACxF,GAAID,EAAKxR,MAAQ,EAAG,CAChB,IAAIyR,EAASD,EAAKC,QACjBF,IAAWA,EAAS,CAACD,KAAS/X,KAAKkY,EAAOP,QAAQxR,EAAKmR,IACxDW,EAAOC,CACX,KACK,CACD,IAAIC,EAAQvD,EAAYG,IAAIkD,EAAKpD,MAEjC,GAAIsD,GAASA,EAAMrD,SAAWqD,EAAMrD,QAAQ,GAAG5W,MAAQiI,GAAOgS,EAAMrD,QAAQqD,EAAMrD,QAAQhW,OAAS,GAAGR,IAAM6H,EAAK,CAC7G,IAAIiS,EAAO,IAAIV,EAASS,EAAMtD,KAAMsD,EAAMrD,QAAQ,GAAG5W,KAAO+Z,EAAK/Z,MAAO,EAAG+Z,IAC1ED,IAAWA,EAAS,CAACD,KAAS/X,KAAK4X,EAAYQ,EAAMjS,EAAKmR,GAAM,GACrE,CACJ,CAEJ,OAAOU,EAASK,EAAUL,GAAUD,CACxC,CA1deO,CAAcnT,KAAMgB,EAAKmR,EACpC,CAQA,OAAAzR,CAAQuP,GACJ,IAAI,MAAEtP,EAAK,MAAEyS,EAAK,KAAEra,EAAO,EAAC,GAAEI,EAAK6G,KAAKrG,QAAWsW,EAC/C8B,EAAO9B,EAAK8B,MAAQ,EAAGsB,GAAQtB,EAAON,EAAS6B,kBAAoB,EACvE,IAAK,IAAIC,EAAIvT,KAAK8R,OAAOC,EAAON,EAAS6B,oBAAqB,CAC1D,IAAIE,GAAU,EACd,GAAID,EAAExa,MAAQI,GAAMoa,EAAEpa,IAAMJ,KAAUsa,GAAQE,EAAE1c,KAAK2Z,cAA4B,IAAb7P,EAAM4S,IAAe,CACrF,GAAIA,EAAEE,aACF,SACJD,GAAU,CACd,CACA,KACQA,GAAWJ,IAAUC,IAASE,EAAE1c,KAAK2Z,cACrC4C,EAAMG,IACNA,EAAEG,eAHD,CAKL,IAAKH,EAAER,SACH,OACJS,GAAU,CACd,CACJ,CACJ,CAKA,IAAAhM,CAAKA,GACD,OAAQA,EAAKmH,QAAiC3O,KAAK/K,MAAQ+K,KAAK/K,MAAMuS,EAAK9S,SAAMkM,EAA1DZ,KAAKnJ,KAAK2Q,KAAKA,EAC1C,CAMA,cAAImM,GACA,IAAI3E,EAAS,GACb,GAAIhP,KAAK/K,MACL,IAAK,IAAIP,KAAMsL,KAAK/K,MAChB+Z,EAAOnU,KAAK,EAAEnG,EAAIsL,KAAK/K,MAAMP,KACrC,OAAOsa,CACX,CAMA,OAAA4E,CAAQlF,EAAS,CAAC,GACd,OAAO1O,KAAK2R,SAAShY,QAAU,EAA+BqG,KAC1D6T,EAAa9E,EAAS+B,KAAM9Q,KAAK2R,SAAU3R,KAAKe,UAAW,EAAGf,KAAK2R,SAAShY,OAAQ,EAAGqG,KAAKrG,OAAQ,CAACgY,EAAU5Q,EAAWpH,IAAW,IAAI+X,EAAK1R,KAAKnJ,KAAM8a,EAAU5Q,EAAWpH,EAAQqG,KAAK2T,YAAajF,EAAOoF,UAAY,EAAEnC,EAAU5Q,EAAWpH,IAAW,IAAI+X,EAAK3C,EAAS+B,KAAMa,EAAU5Q,EAAWpH,IAClT,CAKA,YAAOoa,CAAMxU,GAAQ,OA4tBzB,SAAmBA,GACf,IAAIyU,EACJ,IAAI,OAAEC,EAAM,QAAEC,EAAO,gBAAEC,EAAkB7F,EAAmB,OAAE8F,EAAS,GAAE,cAAEC,EAAgBH,EAAQlD,MAAMrX,QAAW4F,EAChHuS,EAAShK,MAAMC,QAAQkM,GAAU,IAAIK,EAAiBL,EAAQA,EAAOta,QAAUsa,EAC/EjD,EAAQkD,EAAQlD,MAChB1B,EAAc,EAAGC,EAAY,EACjC,SAASgF,EAASC,EAAaC,EAAQ9C,EAAU5Q,EAAW2T,EAAUC,GAClE,IAAI,GAAEjgB,EAAE,MAAEkgB,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAC3BiD,EAAmBxF,EAAWyF,EAAiB1F,EACnD,KAAOwF,EAAO,GAAG,CAEb,GADAhD,EAAOmD,QACM,GAATH,EAAsC,CACtC,IAAIjV,EAAOuU,EAAO1f,GAGlB,OAFAid,EAAS9W,KAAKgF,QACdkB,EAAUlG,KAAK+Z,EAAQJ,EAE3B,CACK,IAAa,GAATM,EAEL,YADAxF,EAAc5a,GAGb,IAAa,GAATogB,EAEL,YADAvF,EAAY7a,GAIZ,MAAM,IAAIoa,WAAW,6BAA6BgG,IAE1D,CACA,IAAsBjV,EAAMoU,EAAxBpd,EAAOma,EAAMtc,GACbwgB,EAAWN,EAAQJ,EACvB,GAAIK,EAAMD,GAAST,IAAoBF,EAASkB,EAAerD,EAAO9Q,IAAMyT,EAAQC,IAAY,CAE5F,IAAInV,EAAO,IAAI6V,YAAYnB,EAAOa,KAAOb,EAAOoB,MAC5CC,EAASxD,EAAO9Q,IAAMiT,EAAOa,KAAMxT,EAAQ/B,EAAK5F,OACpD,KAAOmY,EAAO9Q,IAAMsU,GAChBhU,EAAQiU,EAAatB,EAAOW,MAAOrV,EAAM+B,GAC7CzB,EAAO,IAAI2V,EAAWjW,EAAMsV,EAAMZ,EAAOW,MAAOV,GAChDgB,EAAWjB,EAAOW,MAAQJ,CAC9B,KACK,CACD,IAAIc,EAASxD,EAAO9Q,IAAM8T,EAC1BhD,EAAOmD,OACP,IAAIQ,EAAgB,GAAIC,EAAiB,GACrCC,EAAgBjhB,GAAM2f,EAAgB3f,GAAM,EAC5CkhB,EAAY,EAAGC,EAAUhB,EAC7B,KAAO/C,EAAO9Q,IAAMsU,GACZK,GAAiB,GAAK7D,EAAOpd,IAAMihB,GAAiB7D,EAAOgD,MAAQ,GAC/DhD,EAAO+C,KAAOgB,EAAU1B,IACxB2B,EAAeL,EAAeC,EAAgBd,EAAOgB,EAAW9D,EAAO+C,IAAKgB,EAASF,EAAeZ,EAAkBC,GACtHY,EAAYH,EAAc9b,OAC1Bkc,EAAU/D,EAAO+C,KAErB/C,EAAOmD,QAEFN,EAAQ,KACboB,EAAanB,EAAOU,EAAQG,EAAeC,GAG3CnB,EAASK,EAAOU,EAAQG,EAAeC,EAAgBC,EAAehB,EAAQ,GAOtF,GAJIgB,GAAiB,GAAKC,EAAY,GAAKA,EAAYH,EAAc9b,QACjEmc,EAAeL,EAAeC,EAAgBd,EAAOgB,EAAWhB,EAAOiB,EAASF,EAAeZ,EAAkBC,GACrHS,EAAcO,UACdN,EAAeM,UACXL,GAAiB,GAAKC,EAAY,EAAG,CACrC,IAAIK,EAAOC,EAAarf,EAAMme,GAC9BnV,EAAOgU,EAAahd,EAAM4e,EAAeC,EAAgB,EAAGD,EAAc9b,OAAQ,EAAGkb,EAAMD,EAAOqB,EAAMA,EAC5G,MAEIpW,EAAOiU,EAASjd,EAAM4e,EAAeC,EAAgBb,EAAMD,EAAOG,EAAmBF,EAAKG,EAElG,CACArD,EAAS9W,KAAKgF,GACdkB,EAAUlG,KAAKqa,EACnB,CACA,SAASa,EAAavB,EAAaC,EAAQ9C,EAAU5Q,GACjD,IAAIP,EAAQ,GACR2V,EAAY,EAAGC,GAAU,EAC7B,KAAOtE,EAAO9Q,IAAMyT,GAAQ,CACxB,IAAI,GAAE/f,EAAE,MAAEkgB,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAC/B,GAAIgD,EAAO,EACPhD,EAAOmD,WAEN,IAAImB,GAAU,GAAKxB,EAAQwB,EAC5B,MAGIA,EAAS,IACTA,EAASvB,EAAMV,GACnB3T,EAAM3F,KAAKnG,EAAIkgB,EAAOC,GACtBsB,IACArE,EAAOmD,MACX,CACJ,CACA,GAAIkB,EAAW,CACX,IAAIlC,EAAS,IAAImB,YAAwB,EAAZe,GACzBvB,EAAQpU,EAAMA,EAAM7G,OAAS,GACjC,IAAK,IAAIqO,EAAIxH,EAAM7G,OAAS,EAAG0c,EAAI,EAAGrO,GAAK,EAAGA,GAAK,EAC/CiM,EAAOoC,KAAO7V,EAAMwH,GACpBiM,EAAOoC,KAAO7V,EAAMwH,EAAI,GAAK4M,EAC7BX,EAAOoC,KAAO7V,EAAMwH,EAAI,GAAK4M,EAC7BX,EAAOoC,KAAOA,EAElB1E,EAAS9W,KAAK,IAAI2a,EAAWvB,EAAQzT,EAAM,GAAKoU,EAAOV,IACvDnT,EAAUlG,KAAK+Z,EAAQJ,EAC3B,CACJ,CACA,SAAS0B,EAAarf,EAAMyY,GACxB,MAAO,CAACqC,EAAU5Q,EAAWpH,KACzB,IAAgD2c,EAAMC,EAAlDhH,EAAY,EAAGiH,EAAQ7E,EAAShY,OAAS,EAC7C,GAAI6c,GAAS,IAAMF,EAAO3E,EAAS6E,cAAmB9E,EAAM,CACxD,IAAK8E,GAASF,EAAKzf,MAAQA,GAAQyf,EAAK3c,QAAUA,EAC9C,OAAO2c,GACPC,EAAgBD,EAAK9O,KAAKiH,EAASc,cACnCA,EAAYxO,EAAUyV,GAASF,EAAK3c,OAAS4c,EACrD,CACA,OAAOzC,EAASjd,EAAM8a,EAAU5Q,EAAWpH,EAAQ4V,EAAWD,GAEtE,CACA,SAASwG,EAAenE,EAAU5Q,EAAW0V,EAAMzO,EAAGjP,EAAMI,EAAItC,EAAM0Y,EAAWD,GAC7E,IAAImG,EAAgB,GAAIC,EAAiB,GACzC,KAAO/D,EAAShY,OAASqO,GACrByN,EAAc5a,KAAK8W,EAAS+E,OAC5BhB,EAAe7a,KAAKkG,EAAU2V,MAAQD,EAAO1d,GAEjD4Y,EAAS9W,KAAKiZ,EAASI,EAAQlD,MAAMna,GAAO4e,EAAeC,EAAgBvc,EAAKJ,EAAMwW,EAAYpW,EAAImW,IACtGvO,EAAUlG,KAAK9B,EAAO0d,EAC1B,CACA,SAAS3C,EAASjd,EAAM8a,EAAU5Q,EAAWpH,EAAQ4V,EAAWD,EAAara,GACzE,GAAIqa,EAAa,CACb,IAAIqH,EAAO,CAAClI,EAASa,YAAaA,GAClCra,EAAQA,EAAQ,CAAC0hB,GAAMC,OAAO3hB,GAAS,CAAC0hB,EAC5C,CACA,GAAIpH,EAAY,GAAI,CAChB,IAAIoH,EAAO,CAAClI,EAASc,UAAWA,GAChCta,EAAQA,EAAQ,CAAC0hB,GAAMC,OAAO3hB,GAAS,CAAC0hB,EAC5C,CACA,OAAO,IAAIjF,EAAK7a,EAAM8a,EAAU5Q,EAAWpH,EAAQ1E,EACvD,CACA,SAASkgB,EAAe0B,EAASnC,GAO7B,IAAIoC,EAAOhF,EAAOgF,OACdhC,EAAO,EAAGF,EAAQ,EAAGS,EAAO,EAAG0B,EAAWD,EAAKjC,IAAMV,EACrDnF,EAAS,CAAE8F,KAAM,EAAGF,MAAO,EAAGS,KAAM,GACxCvC,EAAM,IAAK,IAAI2B,EAASqC,EAAK9V,IAAM6V,EAASC,EAAK9V,IAAMyT,GAAS,CAC5D,IAAIuC,EAAWF,EAAKhC,KAEpB,GAAIgC,EAAKpiB,IAAMggB,GAAYsC,GAAY,EAAG,CAGtChI,EAAO8F,KAAOA,EACd9F,EAAO4F,MAAQA,EACf5F,EAAOqG,KAAOA,EACdA,GAAQ,EACRP,GAAQ,EACRgC,EAAK7B,OACL,QACJ,CACA,IAAIC,EAAW4B,EAAK9V,IAAMgW,EAC1B,GAAIA,EAAW,GAAK9B,EAAWT,GAAUqC,EAAKlC,MAAQmC,EAClD,MACJ,IAAIE,EAAeH,EAAKpiB,IAAM2f,EAAgB,EAAI,EAC9C6C,EAAYJ,EAAKlC,MAErB,IADAkC,EAAK7B,OACE6B,EAAK9V,IAAMkU,GAAU,CACxB,GAAI4B,EAAKhC,KAAO,EAAG,CACf,IAAkB,GAAdgC,EAAKhC,KAGL,MAAMhC,EAFNmE,GAAgB,CAGxB,MACSH,EAAKpiB,IAAM2f,IAChB4C,GAAgB,GAEpBH,EAAK7B,MACT,CACAL,EAAQsC,EACRpC,GAAQkC,EACR3B,GAAQ4B,CACZ,CAMA,OALIvC,EAAW,GAAKI,GAAQ+B,KACxB7H,EAAO8F,KAAOA,EACd9F,EAAO4F,MAAQA,EACf5F,EAAOqG,KAAOA,GAEXrG,EAAO8F,KAAO,EAAI9F,OAASpO,CACtC,CACA,SAAS2U,EAAa4B,EAAalD,EAAQ3S,GACvC,IAAI,GAAE5M,EAAE,MAAEkgB,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAE/B,GADAA,EAAOmD,OACHH,GAAQ,GAAKpgB,EAAK2f,EAAe,CACjC,IAAI+C,EAAa9V,EACjB,GAAIwT,EAAO,EAAG,CACV,IAAIQ,EAASxD,EAAO9Q,KAAO8T,EAAO,GAClC,KAAOhD,EAAO9Q,IAAMsU,GAChBhU,EAAQiU,EAAa4B,EAAalD,EAAQ3S,EAClD,CACA2S,IAAS3S,GAAS8V,EAClBnD,IAAS3S,GAASuT,EAAMsC,EACxBlD,IAAS3S,GAASsT,EAAQuC,EAC1BlD,IAAS3S,GAAS5M,CACtB,MACkB,GAATogB,EACLxF,EAAc5a,GAEA,GAATogB,IACLvF,EAAY7a,GAEhB,OAAO4M,CACX,CACA,IAAIqQ,EAAW,GAAI5Q,EAAY,GAC/B,KAAO+Q,EAAO9Q,IAAM,GAChBuT,EAAShV,EAAKqV,OAAS,EAAGrV,EAAK4X,aAAe,EAAGxF,EAAU5Q,GAAY,EAAG,GAC9E,IAAIpH,EAAgC,QAAtBqa,EAAKzU,EAAK5F,cAA2B,IAAPqa,EAAgBA,EAAMrC,EAAShY,OAASoH,EAAU,GAAK4Q,EAAS,GAAGhY,OAAS,EACxH,OAAO,IAAI+X,EAAKV,EAAMzR,EAAK8X,OAAQ1F,EAASqE,UAAWjV,EAAUiV,UAAWrc,EAChF,CA17BgC2d,CAAU/X,EAAO,EAKjDmS,EAAK6F,MAAQ,IAAI7F,EAAK3C,EAAS+B,KAAM,GAAI,GAAI,GAC7C,MAAMwD,EACF,WAAAlU,CAAY6T,EAAQ3S,GAChBtB,KAAKiU,OAASA,EACdjU,KAAKsB,MAAQA,CACjB,CACA,MAAI5M,GAAO,OAAOsL,KAAKiU,OAAOjU,KAAKsB,MAAQ,EAAI,CAC/C,SAAIsT,GAAU,OAAO5U,KAAKiU,OAAOjU,KAAKsB,MAAQ,EAAI,CAClD,OAAIuT,GAAQ,OAAO7U,KAAKiU,OAAOjU,KAAKsB,MAAQ,EAAI,CAChD,QAAIwT,GAAS,OAAO9U,KAAKiU,OAAOjU,KAAKsB,MAAQ,EAAI,CACjD,OAAIN,GAAQ,OAAOhB,KAAKsB,KAAO,CAC/B,IAAA2T,GAASjV,KAAKsB,OAAS,CAAG,CAC1B,IAAAwV,GAAS,OAAO,IAAIxC,EAAiBtU,KAAKiU,OAAQjU,KAAKsB,MAAQ,EAQnE,MAAMkU,EAIF,WAAApV,CAIA6T,EAIAta,EAIA8C,GACIuD,KAAKiU,OAASA,EACdjU,KAAKrG,OAASA,EACdqG,KAAKvD,IAAMA,CACf,CAIA,QAAI5F,GAAS,OAAOkY,EAAS+B,IAAM,CAInC,QAAA7X,GACI,IAAI+V,EAAS,GACb,IAAK,IAAI1N,EAAQ,EAAGA,EAAQtB,KAAKiU,OAAOta,QACpCqV,EAAOnU,KAAKmF,KAAKwX,YAAYlW,IAC7BA,EAAQtB,KAAKiU,OAAO3S,EAAQ,GAEhC,OAAO0N,EAAOvR,KAAK,IACvB,CAIA,WAAA+Z,CAAYlW,GACR,IAAI5M,EAAKsL,KAAKiU,OAAO3S,GAAQmW,EAAWzX,KAAKiU,OAAO3S,EAAQ,GACxDzK,EAAOmJ,KAAKvD,IAAIuU,MAAMtc,GAAKsa,EAASnY,EAAKsQ,KAI7C,GAHI,KAAK0K,KAAK7C,KAAYnY,EAAK0Z,UAC3BvB,EAASzU,KAAKC,UAAUwU,IAExByI,IADJnW,GAAS,GAEL,OAAO0N,EACX,IAAI2C,EAAW,GACf,KAAOrQ,EAAQmW,GACX9F,EAAS9W,KAAKmF,KAAKwX,YAAYlW,IAC/BA,EAAQtB,KAAKiU,OAAO3S,EAAQ,GAEhC,OAAO0N,EAAS,IAAM2C,EAASlU,KAAK,KAAO,GAC/C,CAIA,SAAAia,CAAUN,EAAYK,EAAUE,EAAK3W,EAAKmR,GACtC,IAAI,OAAE8B,GAAWjU,KAAM4X,GAAQ,EAC/B,IAAK,IAAI5P,EAAIoP,EAAYpP,GAAKyP,KACtBI,EAAU1F,EAAMnR,EAAKiT,EAAOjM,EAAI,GAAIiM,EAAOjM,EAAI,MAC/C4P,EAAO5P,EACH2P,EAAM,IAHsB3P,EAAIiM,EAAOjM,EAAI,IAOvD,OAAO4P,CACX,CAIA,KAAAtkB,CAAMwkB,EAAQC,EAAMhf,GAChB,IAAIif,EAAIhY,KAAKiU,OACTgE,EAAO,IAAI7C,YAAY2C,EAAOD,GAASI,EAAM,EACjD,IAAK,IAAIlQ,EAAI8P,EAAQzB,EAAI,EAAGrO,EAAI+P,GAAO,CACnCE,EAAK5B,KAAO2B,EAAEhQ,KACdiQ,EAAK5B,KAAO2B,EAAEhQ,KAAOjP,EACrB,IAAII,EAAK8e,EAAK5B,KAAO2B,EAAEhQ,KAAOjP,EAC9Bkf,EAAK5B,KAAO2B,EAAEhQ,KAAO8P,EACrBI,EAAMC,KAAKC,IAAIF,EAAK/e,EACxB,CACA,OAAO,IAAIqc,EAAWyC,EAAMC,EAAKlY,KAAKvD,IAC1C,EAEJ,SAASob,EAAU1F,EAAMnR,EAAKjI,EAAMI,GAChC,OAAQgZ,GACJ,KAAM,EAAqB,OAAOpZ,EAAOiI,EACzC,KAAM,EAAyB,OAAO7H,GAAM6H,GAAOjI,EAAOiI,EAC1D,KAAK,EAAqB,OAAOjI,EAAOiI,GAAO7H,EAAK6H,EACpD,KAAK,EAAwB,OAAOjI,GAAQiI,GAAO7H,EAAK6H,EACxD,KAAK,EAAoB,OAAO7H,EAAK6H,EACrC,KAAK,EAAuB,OAAO,EAE3C,CACA,SAASyR,EAAY5S,EAAMmB,EAAKmR,EAAMkG,GAGlC,IAFA,IAAIrE,EAEGnU,EAAK9G,MAAQ8G,EAAK1G,KACpBgZ,EAAO,EAAItS,EAAK9G,MAAQiI,EAAMnB,EAAK9G,KAAOiI,KAC1CmR,GAAQ,EAAItS,EAAK1G,IAAM6H,EAAMnB,EAAK1G,GAAK6H,IAAM,CAC9C,IAAI+R,GAAUsF,GAAYxY,aAAgB0S,GAAY1S,EAAKyB,MAAQ,EAAI,KAAOzB,EAAKkT,OACnF,IAAKA,EACD,OAAOlT,EACXA,EAAOkT,CACX,CACA,IAAIhB,EAAOsG,EAAW,EAAI5G,EAAS6G,eAEnC,GAAID,EACA,IAAK,IAAIvF,EAAOjT,EAAMkT,EAASD,EAAKC,OAAQA,EAAQD,EAAOC,EAAQA,EAASD,EAAKC,OACzED,aAAgBP,GAAYO,EAAKxR,MAAQ,IAA+C,QAAxC0S,EAAKjB,EAAOpS,MAAMK,EAAKmR,EAAMJ,UAA0B,IAAPiC,OAAgB,EAASA,EAAGjb,OAAS+Z,EAAK/Z,OAC1I8G,EAAOkT,GAEnB,OAAS,CACL,IAAIH,EAAQ/S,EAAKc,MAAMK,EAAKmR,EAAMJ,GAClC,IAAKa,EACD,OAAO/S,EACXA,EAAO+S,CACX,CACJ,CACA,MAAM2F,EACF,MAAAzG,CAAOC,EAAO,GAAK,OAAO,IAAIC,EAAWhS,KAAM+R,EAAO,CACtD,QAAAtM,CAAS5O,EAAM2hB,EAAS,KAAMC,EAAQ,MAClC,IAAIC,EAAIC,EAAY3Y,KAAMnJ,EAAM2hB,EAAQC,GACxC,OAAOC,EAAE/e,OAAS+e,EAAE,GAAK,IAC7B,CACA,WAAAC,CAAY9hB,EAAM2hB,EAAS,KAAMC,EAAQ,MACrC,OAAOE,EAAY3Y,KAAMnJ,EAAM2hB,EAAQC,EAC3C,CACA,OAAAjG,CAAQxR,EAAKmR,EAAO,GAChB,OAAOM,EAAYzS,KAAMgB,EAAKmR,GAAM,EACxC,CACA,YAAAO,CAAa1R,EAAKmR,EAAO,GACrB,OAAOM,EAAYzS,KAAMgB,EAAKmR,GAAM,EACxC,CACA,YAAAyG,CAAapiB,GACT,OAAOqiB,EAAiB7Y,KAAK+S,OAAQvc,EACzC,CACA,0BAAAsiB,CAA2B9X,GACvB,IAAI8R,EAAO9S,KAAK+Y,YAAY/X,GAAMnB,EAAOG,KACzC,KAAO8S,GAAM,CACT,IAAIwD,EAAOxD,EAAKkG,UAChB,IAAK1C,GAAQA,EAAKnd,IAAM2Z,EAAK3Z,GACzB,MACAmd,EAAKzf,KAAK0Z,SAAW+F,EAAKvd,MAAQud,EAAKnd,IACvC0G,EAAOiT,EACPA,EAAOwD,EAAK2C,aAGZnG,EAAOwD,CAEf,CACA,OAAOzW,CACX,CACA,QAAIA,GAAS,OAAOG,IAAM,CAC1B,QAAIiV,GAAS,OAAOjV,KAAK+S,MAAQ,EAErC,MAAMR,UAAiBgG,EACnB,WAAAnY,CAAYkS,EAAOvZ,EAEnBuI,EAAO4X,GACHC,QACAnZ,KAAKsS,MAAQA,EACbtS,KAAKjH,KAAOA,EACZiH,KAAKsB,MAAQA,EACbtB,KAAKkZ,QAAUA,CACnB,CACA,QAAIriB,GAAS,OAAOmJ,KAAKsS,MAAMzb,IAAM,CACrC,QAAIsQ,GAAS,OAAOnH,KAAKsS,MAAMzb,KAAKsQ,IAAM,CAC1C,MAAIhO,GAAO,OAAO6G,KAAKjH,KAAOiH,KAAKsS,MAAM3Y,MAAQ,CACjD,SAAAyf,CAAUpR,EAAG2P,EAAK3W,EAAKmR,EAAMJ,EAAO,GAChC,IAAK,IAAIgB,EAAS/S,OAAQ,CACtB,IAAK,IAAI,SAAE2R,EAAQ,UAAE5Q,GAAcgS,EAAOT,MAAOvT,EAAI4Y,EAAM,EAAIhG,EAAShY,QAAU,EAAGqO,GAAKjJ,EAAGiJ,GAAK2P,EAAK,CACnG,IAAI1C,EAAOtD,EAAS3J,GAAI4M,EAAQ7T,EAAUiH,GAAK+K,EAAOha,KACtD,GAAK8e,EAAU1F,EAAMnR,EAAK4T,EAAOA,EAAQK,EAAKtb,QAE9C,GAAIsb,aAAgBO,EAAY,CAC5B,GAAIzD,EAAON,EAAS4H,eAChB,SACJ,IAAI/X,EAAQ2T,EAAKyC,UAAU,EAAGzC,EAAKhB,OAAOta,OAAQge,EAAK3W,EAAM4T,EAAOzC,GACpE,GAAI7Q,GAAS,EACT,OAAO,IAAIgY,EAAW,IAAIC,EAAcxG,EAAQkC,EAAMjN,EAAG4M,GAAQ,KAAMtT,EAC/E,MACK,GAAKyQ,EAAON,EAAS6B,mBAAuB2B,EAAKpe,KAAK2Z,aAAegJ,EAASvE,GAAQ,CACvF,IAAIzF,EACJ,KAAMuC,EAAON,EAASgI,gBAAkBjK,EAAUC,EAAYG,IAAIqF,MAAWzF,EAAQG,QACjF,OAAO,IAAI4C,EAAS/C,EAAQE,KAAMkF,EAAO5M,EAAG+K,GAChD,IAAIH,EAAQ,IAAIL,EAAS0C,EAAML,EAAO5M,EAAG+K,GACzC,OAAQhB,EAAON,EAAS6B,mBAAsBV,EAAM/b,KAAK2Z,YAAcoC,EACjEA,EAAMwG,UAAUzB,EAAM,EAAI1C,EAAKtD,SAAShY,OAAS,EAAI,EAAGge,EAAK3W,EAAKmR,EAC5E,CACJ,CACA,GAAKJ,EAAON,EAAS6B,mBAAsBP,EAAOlc,KAAK2Z,YACnD,OAAO,KAMX,GAJIxI,EADA+K,EAAOzR,OAAS,EACZyR,EAAOzR,MAAQqW,EAEfA,EAAM,GAAK,EAAI5E,EAAOmG,QAAQ5G,MAAMX,SAAShY,OACrDoZ,EAASA,EAAOmG,SACXnG,EACD,OAAO,IACf,CACJ,CACA,cAAIU,GAAe,OAAOzT,KAAKoZ,UAAU,EAAG,EAAG,EAAG,EAAwB,CAC1E,aAAIJ,GAAc,OAAOhZ,KAAKoZ,UAAUpZ,KAAKsS,MAAMX,SAAShY,OAAS,GAAI,EAAG,EAAG,EAAwB,CACvG,UAAAuH,CAAWF,GAAO,OAAOhB,KAAKoZ,UAAU,EAAG,EAAGpY,EAAK,EAAqB,CACxE,WAAA+X,CAAY/X,GAAO,OAAOhB,KAAKoZ,UAAUpZ,KAAKsS,MAAMX,SAAShY,OAAS,GAAI,EAAGqH,GAAM,EAAsB,CACzG,KAAAL,CAAMK,EAAKmR,EAAMJ,EAAO,GACpB,IAAIvC,EACJ,KAAMuC,EAAON,EAAS6G,kBAAoB9I,EAAUC,EAAYG,IAAI5P,KAAKsS,SAAW9C,EAAQG,QAAS,CACjG,IAAI+J,EAAO1Y,EAAMhB,KAAKjH,KACtB,IAAK,IAAI,KAAEA,EAAI,GAAEI,KAAQqW,EAAQG,QAC7B,IAAKwC,EAAO,EAAIpZ,GAAQ2gB,EAAO3gB,EAAO2gB,KACjCvH,EAAO,EAAIhZ,GAAMugB,EAAOvgB,EAAKugB,GAC9B,OAAO,IAAInH,EAAS/C,EAAQE,KAAMF,EAAQG,QAAQ,GAAG5W,KAAOiH,KAAKjH,MAAO,EAAGiH,KAEvF,CACA,OAAOA,KAAKoZ,UAAU,EAAG,EAAGpY,EAAKmR,EAAMJ,EAC3C,CACA,qBAAA4H,GACI,IAAIC,EAAM5Z,KACV,KAAO4Z,EAAI/iB,KAAK2Z,aAAeoJ,EAAIV,SAC/BU,EAAMA,EAAIV,QACd,OAAOU,CACX,CACA,UAAI7G,GACA,OAAO/S,KAAKkZ,QAAUlZ,KAAKkZ,QAAQS,wBAA0B,IACjE,CACA,eAAIjG,GACA,OAAO1T,KAAKkZ,SAAWlZ,KAAKsB,OAAS,EAAItB,KAAKkZ,QAAQE,UAAUpZ,KAAKsB,MAAQ,EAAG,EAAG,EAAG,GAAyB,IACnH,CACA,eAAI2X,GACA,OAAOjZ,KAAKkZ,SAAWlZ,KAAKsB,OAAS,EAAItB,KAAKkZ,QAAQE,UAAUpZ,KAAKsB,MAAQ,GAAI,EAAG,EAAG,GAAyB,IACpH,CACA,QAAIoO,GAAS,OAAO1P,KAAKsS,KAAO,CAChC,MAAAuH,GAAW,OAAO7Z,KAAKsS,KAAO,CAI9B,QAAArZ,GAAa,OAAO+G,KAAKsS,MAAMrZ,UAAY,EAE/C,SAAS0f,EAAY9Y,EAAMhJ,EAAM2hB,EAAQC,GACrC,IAAIqB,EAAMja,EAAKiS,SAAU9C,EAAS,GAClC,IAAK8K,EAAIrG,aACL,OAAOzE,EACX,GAAc,MAAVwJ,EACA,IAAK,IAAI3H,GAAQ,GAAQA,GAErB,GADAA,EAAQiJ,EAAIjjB,KAAK4Z,GAAG+H,IACfsB,EAAIpG,cACL,OAAO1E,EAEnB,OAAS,CACL,GAAa,MAATyJ,GAAiBqB,EAAIjjB,KAAK4Z,GAAGgI,GAC7B,OAAOzJ,EAGX,GAFI8K,EAAIjjB,KAAK4Z,GAAG5Z,IACZmY,EAAOnU,KAAKif,EAAIja,OACfia,EAAIpG,cACL,OAAgB,MAAT+E,EAAgBzJ,EAAS,EACxC,CACJ,CACA,SAAS6J,EAAiBhZ,EAAMrJ,EAASwR,EAAIxR,EAAQmD,OAAS,GAC1D,IAAK,IAAIgR,EAAI9K,EAAMmI,GAAK,EAAG2C,EAAIA,EAAEoI,OAAQ,CACrC,IAAKpI,EACD,OAAO,EACX,IAAKA,EAAE9T,KAAK2Z,YAAa,CACrB,GAAIha,EAAQwR,IAAMxR,EAAQwR,IAAM2C,EAAExD,KAC9B,OAAO,EACXa,GACJ,CACJ,CACA,OAAO,CACX,CACA,MAAMuR,EACF,WAAAnZ,CAAY2S,EAAQkB,EAAQ3S,EAAOsT,GAC/B5U,KAAK+S,OAASA,EACd/S,KAAKiU,OAASA,EACdjU,KAAKsB,MAAQA,EACbtB,KAAK4U,MAAQA,CACjB,EAEJ,MAAM0E,UAAmBf,EACrB,QAAIpR,GAAS,OAAOnH,KAAKnJ,KAAKsQ,IAAM,CACpC,QAAIpO,GAAS,OAAOiH,KAAKxJ,QAAQoe,MAAQ5U,KAAKxJ,QAAQyd,OAAOA,OAAOjU,KAAKsB,MAAQ,EAAI,CACrF,MAAInI,GAAO,OAAO6G,KAAKxJ,QAAQoe,MAAQ5U,KAAKxJ,QAAQyd,OAAOA,OAAOjU,KAAKsB,MAAQ,EAAI,CACnF,WAAAlB,CAAY5J,EAAS0iB,EAAS5X,GAC1B6X,QACAnZ,KAAKxJ,QAAUA,EACfwJ,KAAKkZ,QAAUA,EACflZ,KAAKsB,MAAQA,EACbtB,KAAKnJ,KAAOL,EAAQyd,OAAOxX,IAAIuU,MAAMxa,EAAQyd,OAAOA,OAAO3S,GAC/D,CACA,KAAAL,CAAM0W,EAAK3W,EAAKmR,GACZ,IAAI,OAAE8B,GAAWjU,KAAKxJ,QAClB8K,EAAQ2S,EAAOyD,UAAU1X,KAAKsB,MAAQ,EAAG2S,EAAOA,OAAOjU,KAAKsB,MAAQ,GAAIqW,EAAK3W,EAAMhB,KAAKxJ,QAAQoe,MAAOzC,GAC3G,OAAO7Q,EAAQ,EAAI,KAAO,IAAIgY,EAAWtZ,KAAKxJ,QAASwJ,KAAMsB,EACjE,CACA,cAAImS,GAAe,OAAOzT,KAAKiB,MAAM,EAAG,EAAG,EAAwB,CACnE,aAAI+X,GAAc,OAAOhZ,KAAKiB,OAAO,EAAG,EAAG,EAAwB,CACnE,UAAAC,CAAWF,GAAO,OAAOhB,KAAKiB,MAAM,EAAGD,EAAK,EAAqB,CACjE,WAAA+X,CAAY/X,GAAO,OAAOhB,KAAKiB,OAAO,EAAGD,GAAM,EAAsB,CACrE,KAAAL,CAAMK,EAAKmR,EAAMJ,EAAO,GACpB,GAAIA,EAAON,EAAS4H,eAChB,OAAO,KACX,IAAI,OAAEpF,GAAWjU,KAAKxJ,QAClB8K,EAAQ2S,EAAOyD,UAAU1X,KAAKsB,MAAQ,EAAG2S,EAAOA,OAAOjU,KAAKsB,MAAQ,GAAI6Q,EAAO,EAAI,GAAK,EAAGnR,EAAMhB,KAAKxJ,QAAQoe,MAAOzC,GACzH,OAAO7Q,EAAQ,EAAI,KAAO,IAAIgY,EAAWtZ,KAAKxJ,QAASwJ,KAAMsB,EACjE,CACA,UAAIyR,GACA,OAAO/S,KAAKkZ,SAAWlZ,KAAKxJ,QAAQuc,OAAO4G,uBAC/C,CACA,eAAAI,CAAgBpC,GACZ,OAAO3X,KAAKkZ,QAAU,KAAOlZ,KAAKxJ,QAAQuc,OAAOqG,UAAUpZ,KAAKxJ,QAAQ8K,MAAQqW,EAAKA,EAAK,EAAG,EACjG,CACA,eAAIjE,GACA,IAAI,OAAEO,GAAWjU,KAAKxJ,QAClBiiB,EAAQxE,EAAOA,OAAOjU,KAAKsB,MAAQ,GACvC,OAAImX,GAASzY,KAAKkZ,QAAUjF,EAAOA,OAAOjU,KAAKkZ,QAAQ5X,MAAQ,GAAK2S,EAAOA,OAAOta,QACvE,IAAI2f,EAAWtZ,KAAKxJ,QAASwJ,KAAKkZ,QAAST,GAC/CzY,KAAK+Z,gBAAgB,EAChC,CACA,eAAId,GACA,IAAI,OAAEhF,GAAWjU,KAAKxJ,QAClBge,EAAcxU,KAAKkZ,QAAUlZ,KAAKkZ,QAAQ5X,MAAQ,EAAI,EAC1D,OAAItB,KAAKsB,OAASkT,EACPxU,KAAK+Z,iBAAiB,GAC1B,IAAIT,EAAWtZ,KAAKxJ,QAASwJ,KAAKkZ,QAASjF,EAAOyD,UAAUlD,EAAaxU,KAAKsB,OAAQ,EAAG,EAAG,GACvG,CACA,QAAIoO,GAAS,OAAO,IAAM,CAC1B,MAAAmK,GACI,IAAIlI,EAAW,GAAI5Q,EAAY,IAC3B,OAAEkT,GAAWjU,KAAKxJ,QAClBshB,EAAS9X,KAAKsB,MAAQ,EAAGyW,EAAO9D,EAAOA,OAAOjU,KAAKsB,MAAQ,GAC/D,GAAIyW,EAAOD,EAAQ,CACf,IAAI/e,EAAOkb,EAAOA,OAAOjU,KAAKsB,MAAQ,GACtCqQ,EAAS9W,KAAKoZ,EAAO3gB,MAAMwkB,EAAQC,EAAMhf,IACzCgI,EAAUlG,KAAK,EACnB,CACA,OAAO,IAAI6W,EAAK1R,KAAKnJ,KAAM8a,EAAU5Q,EAAWf,KAAK7G,GAAK6G,KAAKjH,KACnE,CAIA,QAAAE,GAAa,OAAO+G,KAAKxJ,QAAQyd,OAAOuD,YAAYxX,KAAKsB,MAAQ,EAErE,SAAS4R,EAAU8G,GACf,IAAKA,EAAMrgB,OACP,OAAO,KACX,IAAIie,EAAO,EAAGqC,EAASD,EAAM,GAC7B,IAAK,IAAIhS,EAAI,EAAGA,EAAIgS,EAAMrgB,OAAQqO,IAAK,CACnC,IAAInI,EAAOma,EAAMhS,IACbnI,EAAK9G,KAAOkhB,EAAOlhB,MAAQ8G,EAAK1G,GAAK8gB,EAAO9gB,MAC5C8gB,EAASpa,EACT+X,EAAO5P,EAEf,CACA,IAAIiN,EAAOgF,aAAkB1H,GAAY0H,EAAO3Y,MAAQ,EAAI,KAAO2Y,EAAOlH,OACtEmH,EAAWF,EAAM1mB,QAKrB,OAJI2hB,EACAiF,EAAStC,GAAQ3C,EAEjBiF,EAASC,OAAOvC,EAAM,GACnB,IAAIwC,EAAcF,EAAUD,EACvC,CACA,MAAMG,EACF,WAAAha,CAAY4Z,EAAOna,GACfG,KAAKga,MAAQA,EACbha,KAAKH,KAAOA,CAChB,CACA,QAAIoV,GAAS,OAAO/B,EAAUlT,KAAKga,MAAQ,EAyB/C,MAAMhI,EAIF,QAAI7K,GAAS,OAAOnH,KAAKnJ,KAAKsQ,IAAM,CAIpC,WAAA/G,CAAYP,EAIZkS,EAAO,GAYH,GAXA/R,KAAK+R,KAAOA,EAIZ/R,KAAKiU,OAAS,KACdjU,KAAKqa,MAAQ,GAIbra,KAAKsB,MAAQ,EACbtB,KAAKsa,WAAa,KACdza,aAAgB0S,EAChBvS,KAAKua,UAAU1a,OAEd,CACDG,KAAKsS,MAAQzS,EAAKrJ,QAAQuc,OAC1B/S,KAAKiU,OAASpU,EAAKrJ,QACnB,IAAK,IAAIgkB,EAAI3a,EAAKqZ,QAASsB,EAAGA,EAAIA,EAAEtB,QAChClZ,KAAKqa,MAAMI,QAAQD,EAAElZ,OACzBtB,KAAKsa,WAAaza,EAClBG,KAAK0a,SAAS7a,EAAKyB,MACvB,CACJ,CACA,SAAAiZ,CAAU1a,GACN,QAAKA,IAELG,KAAKsS,MAAQzS,EACbG,KAAKnJ,KAAOgJ,EAAKhJ,KACjBmJ,KAAKjH,KAAO8G,EAAK9G,KACjBiH,KAAK7G,GAAK0G,EAAK1G,IACR,EACX,CACA,QAAAuhB,CAASpZ,EAAOzK,GACZmJ,KAAKsB,MAAQA,EACb,IAAI,MAAEsT,EAAK,OAAEX,GAAWjU,KAAKiU,OAI7B,OAHAjU,KAAKnJ,KAAOA,GAAQod,EAAOxX,IAAIuU,MAAMiD,EAAOA,OAAO3S,IACnDtB,KAAKjH,KAAO6b,EAAQX,EAAOA,OAAO3S,EAAQ,GAC1CtB,KAAK7G,GAAKyb,EAAQX,EAAOA,OAAO3S,EAAQ,IACjC,CACX,CAIA,KAAAqZ,CAAM9a,GACF,QAAKA,IAEDA,aAAgB0S,GAChBvS,KAAKiU,OAAS,KACPjU,KAAKua,UAAU1a,KAE1BG,KAAKiU,OAASpU,EAAKrJ,QACZwJ,KAAK0a,SAAS7a,EAAKyB,MAAOzB,EAAKhJ,OAC1C,CAIA,QAAAoC,GACI,OAAO+G,KAAKiU,OAASjU,KAAKiU,OAAOA,OAAOuD,YAAYxX,KAAKsB,OAAStB,KAAKsS,MAAMrZ,UACjF,CAIA,UAAA2hB,CAAWjD,EAAK3W,EAAKmR,GACjB,IAAKnS,KAAKiU,OACN,OAAOjU,KAAK2a,MAAM3a,KAAKsS,MAAM8G,UAAUzB,EAAM,EAAI3X,KAAKsS,MAAMA,MAAMX,SAAShY,OAAS,EAAI,EAAGge,EAAK3W,EAAKmR,EAAMnS,KAAK+R,OACpH,IAAI,OAAEkC,GAAWjU,KAAKiU,OAClB3S,EAAQ2S,EAAOyD,UAAU1X,KAAKsB,MAAQ,EAAG2S,EAAOA,OAAOjU,KAAKsB,MAAQ,GAAIqW,EAAK3W,EAAMhB,KAAKiU,OAAOW,MAAOzC,GAC1G,QAAI7Q,EAAQ,KAEZtB,KAAKqa,MAAMxf,KAAKmF,KAAKsB,OACdtB,KAAK0a,SAASpZ,GACzB,CAKA,UAAAmS,GAAe,OAAOzT,KAAK4a,WAAW,EAAG,EAAG,EAAwB,CAIpE,SAAA5B,GAAc,OAAOhZ,KAAK4a,YAAY,EAAG,EAAG,EAAwB,CAIpE,UAAA1Z,CAAWF,GAAO,OAAOhB,KAAK4a,WAAW,EAAG5Z,EAAK,EAAqB,CAItE,WAAA+X,CAAY/X,GAAO,OAAOhB,KAAK4a,YAAY,EAAG5Z,GAAM,EAAsB,CAQ1E,KAAAL,CAAMK,EAAKmR,EAAMJ,EAAO/R,KAAK+R,MACzB,OAAK/R,KAAKiU,SAEHlC,EAAON,EAAS4H,iBAAyBrZ,KAAK4a,WAAW,EAAG5Z,EAAKmR,GAD7DnS,KAAK2a,MAAM3a,KAAKsS,MAAM3R,MAAMK,EAAKmR,EAAMJ,GAEtD,CAIA,MAAAgB,GACI,IAAK/S,KAAKiU,OACN,OAAOjU,KAAKua,UAAWva,KAAK+R,KAAON,EAAS6B,iBAAoBtT,KAAKsS,MAAM4G,QAAUlZ,KAAKsS,MAAMS,QACpG,GAAI/S,KAAKqa,MAAM1gB,OACX,OAAOqG,KAAK0a,SAAS1a,KAAKqa,MAAM3D,OACpC,IAAI3D,EAAU/S,KAAK+R,KAAON,EAAS6B,iBAAoBtT,KAAKiU,OAAOlB,OAAS/S,KAAKiU,OAAOlB,OAAO4G,wBAE/F,OADA3Z,KAAKiU,OAAS,KACPjU,KAAKua,UAAUxH,EAC1B,CAIA,OAAA8H,CAAQlD,GACJ,IAAK3X,KAAKiU,OACN,QAAQjU,KAAKsS,MAAM4G,SACblZ,KAAK2a,MAAM3a,KAAKsS,MAAMhR,MAAQ,EAAI,KAC9BtB,KAAKsS,MAAM4G,QAAQE,UAAUpZ,KAAKsS,MAAMhR,MAAQqW,EAAKA,EAAK,EAAG,EAAuB3X,KAAK+R,OACvG,IAAI,OAAEkC,GAAWjU,KAAKiU,OAAQ6G,EAAI9a,KAAKqa,MAAM1gB,OAAS,EACtD,GAAIge,EAAM,EAAG,CACT,IAAInD,EAAcsG,EAAI,EAAI,EAAI9a,KAAKqa,MAAMS,GAAK,EAC9C,GAAI9a,KAAKsB,OAASkT,EACd,OAAOxU,KAAK0a,SAASzG,EAAOyD,UAAUlD,EAAaxU,KAAKsB,OAAQ,EAAG,EAAG,GAC9E,KACK,CACD,IAAImX,EAAQxE,EAAOA,OAAOjU,KAAKsB,MAAQ,GACvC,GAAImX,GAASqC,EAAI,EAAI7G,EAAOA,OAAOta,OAASsa,EAAOA,OAAOjU,KAAKqa,MAAMS,GAAK,IACtE,OAAO9a,KAAK0a,SAASjC,EAC7B,CACA,OAAOqC,EAAI,GAAI9a,KAAK2a,MAAM3a,KAAKiU,OAAOlB,OAAOqG,UAAUpZ,KAAKiU,OAAO3S,MAAQqW,EAAKA,EAAK,EAAG,EAAuB3X,KAAK+R,MACxH,CAIA,WAAA2B,GAAgB,OAAO1T,KAAK6a,QAAQ,EAAI,CAIxC,WAAA5B,GAAgB,OAAOjZ,KAAK6a,SAAS,EAAI,CACzC,UAAAE,CAAWpD,GACP,IAAIrW,EAAOyR,GAAQ,OAAEkB,GAAWjU,KAChC,GAAIiU,EAAQ,CACR,GAAI0D,EAAM,GACN,GAAI3X,KAAKsB,MAAQ2S,EAAOA,OAAOA,OAAOta,OAClC,OAAO,OAGX,IAAK,IAAIqO,EAAI,EAAGA,EAAIhI,KAAKsB,MAAO0G,IAC5B,GAAIiM,EAAOA,OAAOA,OAAOjM,EAAI,GAAKhI,KAAKsB,MACnC,OAAO,IAEhBA,QAAOyR,UAAWkB,EACzB,OAEO3S,QAAO4X,QAASnG,GAAW/S,KAAKsS,OAEvC,KAAOS,IAAUzR,QAAO4X,QAASnG,GAAWA,GACxC,GAAIzR,GAAS,EACT,IAAK,IAAI0G,EAAI1G,EAAQqW,EAAK5Y,EAAI4Y,EAAM,GAAK,EAAI5E,EAAOT,MAAMX,SAAShY,OAAQqO,GAAKjJ,EAAGiJ,GAAK2P,EAAK,CACzF,IAAI1W,EAAQ8R,EAAOT,MAAMX,SAAS3J,GAClC,GAAKhI,KAAK+R,KAAON,EAAS6B,kBACtBrS,aAAiBuU,IAChBvU,EAAMpK,KAAK2Z,aACZgJ,EAASvY,GACT,OAAO,CACf,CAER,OAAO,CACX,CACA,IAAA+Z,CAAKrD,EAAKhX,GACN,GAAIA,GAASX,KAAK4a,WAAWjD,EAAK,EAAG,GACjC,OAAO,EACX,OAAS,CACL,GAAI3X,KAAK6a,QAAQlD,GACb,OAAO,EACX,GAAI3X,KAAK+a,WAAWpD,KAAS3X,KAAK+S,SAC9B,OAAO,CACf,CACJ,CAQA,IAAAkC,CAAKtU,GAAQ,GAAQ,OAAOX,KAAKgb,KAAK,EAAGra,EAAQ,CAOjD,IAAAsa,CAAKta,GAAQ,GAAQ,OAAOX,KAAKgb,MAAM,EAAGra,EAAQ,CAMlD,MAAA0R,CAAOrR,EAAKmR,EAAO,GAEf,MAAOnS,KAAKjH,MAAQiH,KAAK7G,KACpBgZ,EAAO,EAAInS,KAAKjH,MAAQiI,EAAMhB,KAAKjH,KAAOiI,KAC1CmR,GAAQ,EAAInS,KAAK7G,IAAM6H,EAAMhB,KAAK7G,GAAK6H,KACnChB,KAAK+S,WAGd,KAAO/S,KAAK4a,WAAW,EAAG5Z,EAAKmR,KAC/B,OAAOnS,IACX,CAKA,QAAIH,GACA,IAAKG,KAAKiU,OACN,OAAOjU,KAAKsS,MAChB,IAAI4I,EAAQlb,KAAKsa,WAAYtL,EAAS,KAAM2F,EAAQ,EACpD,GAAIuG,GAASA,EAAM1kB,SAAWwJ,KAAKiU,OAC/BnB,EAAM,IAAK,IAAIxR,EAAQtB,KAAKsB,MAAOwZ,EAAI9a,KAAKqa,MAAM1gB,OAAQmhB,GAAK,GAAI,CAC/D,IAAK,IAAIvH,EAAI2H,EAAO3H,EAAGA,EAAIA,EAAE2F,QACzB,GAAI3F,EAAEjS,OAASA,EAAO,CAClB,GAAIA,GAAStB,KAAKsB,MACd,OAAOiS,EACXvE,EAASuE,EACToB,EAAQmG,EAAI,EACZ,MAAMhI,CACV,CACJxR,EAAQtB,KAAKqa,QAAQS,EACzB,CAEJ,IAAK,IAAI9S,EAAI2M,EAAO3M,EAAIhI,KAAKqa,MAAM1gB,OAAQqO,IACvCgH,EAAS,IAAIsK,EAAWtZ,KAAKiU,OAAQjF,EAAQhP,KAAKqa,MAAMrS,IAC5D,OAAOhI,KAAKsa,WAAa,IAAIhB,EAAWtZ,KAAKiU,OAAQjF,EAAQhP,KAAKsB,MACtE,CAMA,QAAIoO,GACA,OAAO1P,KAAKiU,OAAS,KAAOjU,KAAKsS,MAAMA,KAC3C,CAOA,OAAA5R,CAAQC,EAAOyS,GACX,IAAK,IAAIuB,EAAQ,IAAK,CAClB,IAAIwG,GAAY,EAChB,GAAInb,KAAKnJ,KAAK2Z,cAA+B,IAAhB7P,EAAMX,MAAiB,CAChD,GAAIA,KAAKyT,aAAc,CACnBkB,IACA,QACJ,CACK3U,KAAKnJ,KAAK2Z,cACX2K,GAAY,EACpB,CACA,OAAS,CAIL,GAHIA,GAAa/H,GACbA,EAAMpT,MACVmb,EAAYnb,KAAKnJ,KAAK2Z,aACjBmE,EACD,OACJ,GAAI3U,KAAK0T,cACL,MACJ1T,KAAK+S,SACL4B,IACAwG,GAAY,CAChB,CACJ,CACJ,CAMA,YAAAvC,CAAapiB,GACT,IAAKwJ,KAAKiU,OACN,OAAO4E,EAAiB7Y,KAAKH,KAAKkT,OAAQvc,GAC9C,IAAI,OAAEyd,GAAWjU,KAAKiU,QAAQ,MAAEjD,GAAUiD,EAAOxX,IACjD,IAAK,IAAIuL,EAAIxR,EAAQmD,OAAS,EAAGmhB,EAAI9a,KAAKqa,MAAM1gB,OAAS,EAAGqO,GAAK,EAAG8S,IAAK,CACrE,GAAIA,EAAI,EACJ,OAAOjC,EAAiB7Y,KAAKsS,MAAO9b,EAASwR,GACjD,IAAInR,EAAOma,EAAMiD,EAAOA,OAAOjU,KAAKqa,MAAMS,KAC1C,IAAKjkB,EAAK2Z,YAAa,CACnB,GAAIha,EAAQwR,IAAMxR,EAAQwR,IAAMnR,EAAKsQ,KACjC,OAAO,EACXa,GACJ,CACJ,CACA,OAAO,CACX,EAEJ,SAASwR,EAAS9J,GACd,OAAOA,EAAKiC,SAASyJ,KAAKxJ,GAAMA,aAAc4D,IAAe5D,EAAG/a,KAAK2Z,aAAegJ,EAAS5H,GACjG,CAgOA,MAAMyJ,EAAgB,IAAI9J,QAC1B,SAASyF,EAASsE,EAAazb,GAC3B,IAAKyb,EAAY9K,aAAe3Q,aAAgB2V,GAAc3V,EAAKhJ,MAAQykB,EACvE,OAAO,EACX,IAAIxG,EAAOuG,EAAczL,IAAI/P,GAC7B,GAAY,MAARiV,EAAc,CACdA,EAAO,EACP,IAAK,IAAI7T,KAASpB,EAAK8R,SAAU,CAC7B,GAAI1Q,EAAMpK,MAAQykB,KAAiBra,aAAiByQ,GAAO,CACvDoD,EAAO,EACP,KACJ,CACAA,GAAQkC,EAASsE,EAAara,EAClC,CACAoa,EAAc5e,IAAIoD,EAAMiV,EAC5B,CACA,OAAOA,CACX,CACA,SAASjB,EAETyH,EAEA3J,EAAU5Q,EAEVhI,EAAMI,EAENyb,EAEAjb,EAEA4hB,EAEAC,GACI,IAAIC,EAAQ,EACZ,IAAK,IAAIzT,EAAIjP,EAAMiP,EAAI7O,EAAI6O,IACvByT,GAASzE,EAASsE,EAAa3J,EAAS3J,IAC5C,IAAI0T,EAAWvD,KAAKwD,KAAc,IAARF,EAAe,GACrChG,EAAgB,GAAIC,EAAiB,GA2BzC,OA1BA,SAASkG,EAAOjK,EAAU5Q,EAAWhI,EAAMI,EAAI0iB,GAC3C,IAAK,IAAI7T,EAAIjP,EAAMiP,EAAI7O,GAAK,CACxB,IAAI2iB,EAAY9T,EAAG+T,EAAahb,EAAUiH,GAAIgU,EAAYhF,EAASsE,EAAa3J,EAAS3J,IAEzF,IADAA,IACOA,EAAI7O,EAAI6O,IAAK,CAChB,IAAIiU,EAAWjF,EAASsE,EAAa3J,EAAS3J,IAC9C,GAAIgU,EAAYC,GAAYP,EACxB,MACJM,GAAaC,CACjB,CACA,GAAIjU,GAAK8T,EAAY,EAAG,CACpB,GAAIE,EAAYN,EAAU,CACtB,IAAIQ,EAAOvK,EAASmK,GACpBF,EAAOM,EAAKvK,SAAUuK,EAAKnb,UAAW,EAAGmb,EAAKvK,SAAShY,OAAQoH,EAAU+a,GAAaD,GACtF,QACJ,CACApG,EAAc5a,KAAK8W,EAASmK,GAChC,KACK,CACD,IAAIniB,EAASoH,EAAUiH,EAAI,GAAK2J,EAAS3J,EAAI,GAAGrO,OAASoiB,EACzDtG,EAAc5a,KAAKgZ,EAAayH,EAAa3J,EAAU5Q,EAAW+a,EAAW9T,EAAG+T,EAAYpiB,EAAQ,KAAM6hB,GAC9G,CACA9F,EAAe7a,KAAKkhB,EAAaF,EAASjH,EAC9C,CACJ,CACAgH,CAAOjK,EAAU5Q,EAAWhI,EAAMI,EAAI,IAC9BoiB,GAASC,GAAQ/F,EAAeC,EAAgB/b,EAC5D,CAkKA,MAAMwiB,EAWF,UAAAC,CAAWhT,EAAOiT,EAAWC,GAIzB,MAHoB,iBAATlT,IACPA,EAAQ,IAAImT,EAAYnT,IAC5BkT,EAAUA,EAAwCA,EAAO3iB,OAAS2iB,EAAOzY,IAAI6U,GAAK,IAAIlK,EAAMkK,EAAE3f,KAAM2f,EAAEvf,KAAO,CAAC,IAAIqV,EAAM,EAAG,IAAxG,CAAC,IAAIA,EAAM,EAAGpF,EAAMzP,SAChCqG,KAAKwc,YAAYpT,EAAOiT,GAAa,GAAIC,EACpD,CAIA,KAAA7b,CAAM2I,EAAOiT,EAAWC,GACpB,IAAI7b,EAAQT,KAAKoc,WAAWhT,EAAOiT,EAAWC,GAC9C,OAAS,CACL,IAAIG,EAAOhc,EAAMic,UACjB,GAAID,EACA,OAAOA,CACf,CACJ,EAEJ,MAAMF,EACF,WAAAnc,CAAY0G,GACR9G,KAAK8G,OAASA,CAClB,CACA,UAAInN,GAAW,OAAOqG,KAAK8G,OAAOnN,MAAQ,CAC1C,KAAAgjB,CAAM5jB,GAAQ,OAAOiH,KAAK8G,OAAOxT,MAAMyF,EAAO,CAC9C,cAAI6jB,GAAe,OAAO,CAAO,CACjC,IAAAC,CAAK9jB,EAAMI,GAAM,OAAO6G,KAAK8G,OAAOxT,MAAMyF,EAAMI,EAAK,EAuCpC,IAAIsV,EAAS,CAAEE,SAAS,ICrvD7C,MAAMmO,EAIF,WAAA1c,CAIAuK,EAKA0P,EAIA0C,EAQAC,EAIAhc,EAMAic,EAOAhJ,EASAiJ,EAIAC,EAIA5N,EAAY,EAQZwD,GACI/S,KAAK2K,EAAIA,EACT3K,KAAKqa,MAAQA,EACbra,KAAK+c,MAAQA,EACb/c,KAAKgd,UAAYA,EACjBhd,KAAKgB,IAAMA,EACXhB,KAAKid,MAAQA,EACbjd,KAAKiU,OAASA,EACdjU,KAAKkd,WAAaA,EAClBld,KAAKmd,WAAaA,EAClBnd,KAAKuP,UAAYA,EACjBvP,KAAK+S,OAASA,CAClB,CAIA,QAAA9Z,GACI,MAAO,IAAI+G,KAAKqa,MAAMhX,OAAO,CAAC+Z,EAAGpV,IAAMA,EAAI,GAAK,GAAG4O,OAAO5W,KAAK+c,WAAW/c,KAAKgB,MAAMhB,KAAKid,MAAQ,IAAMjd,KAAKid,MAAQ,IACzH,CAKA,YAAOrI,CAAMjK,EAAGoS,EAAO/b,EAAM,GACzB,IAAIqc,EAAK1S,EAAEtQ,OAAO7D,QAClB,OAAO,IAAIsmB,EAAMnS,EAAG,GAAIoS,EAAO/b,EAAKA,EAAK,EAAG,GAAI,EAAGqc,EAAK,IAAIC,EAAaD,EAAIA,EAAGzI,OAAS,KAAM,EAAG,KACtG,CAOA,WAAIpe,GAAY,OAAOwJ,KAAKmd,WAAand,KAAKmd,WAAW3mB,QAAU,IAAM,CAMzE,SAAA+mB,CAAUR,EAAOnI,GACb5U,KAAKqa,MAAMxf,KAAKmF,KAAK+c,MAAOnI,EAAO5U,KAAKkd,WAAald,KAAKiU,OAAOta,QACjEqG,KAAK+c,MAAQA,CACjB,CAKA,MAAAS,CAAOC,GACH,IAAIzJ,EACJ,IAAIW,EAAQ8I,GAAU,GAAkC5mB,EAAgB,MAAT4mB,GAC3D,OAAEpjB,GAAW2F,KAAK2K,EAClB+S,EAAkB1d,KAAKgd,UAAYhd,KAAKgB,IAAM,GAC9C0c,GACA1d,KAAK2d,aAAa3d,KAAKgB,KAC3B,IAAI4c,EAAQvjB,EAAOwjB,kBAAkBhnB,GAGrC,GAFI+mB,IACA5d,KAAKid,OAASW,GACL,GAATjJ,EAOA,OANA3U,KAAKud,UAAUljB,EAAOyjB,QAAQ9d,KAAK+c,MAAOlmB,GAAM,GAAOmJ,KAAKgd,WAGxDnmB,EAAOwD,EAAO0jB,eACd/d,KAAKge,UAAUnnB,EAAMmJ,KAAKgd,UAAWhd,KAAKgd,UAAWU,EAAkB,EAAI,GAAG,QAClF1d,KAAKie,cAAcpnB,EAAMmJ,KAAKgd,WAQlC,IAAIvG,EAAOzW,KAAKqa,MAAM1gB,OAAwB,GAAbgb,EAAQ,IAAoB,OAAT8I,EAAwC,EAAI,GAC5F7I,EAAQ6B,EAAOzW,KAAKqa,MAAM5D,EAAO,GAAKzW,KAAK2K,EAAE2R,OAAO,GAAGvjB,KAAM+b,EAAO9U,KAAKgd,UAAYpI,EAIrFE,GAAQ,OAAqF,QAA5Cd,EAAKhU,KAAK2K,EAAEtQ,OAAO6Z,QAAQlD,MAAMna,UAA0B,IAAPmd,OAAgB,EAASA,EAAGxD,eAC7HoE,GAAS5U,KAAK2K,EAAEuT,uBAChBle,KAAK2K,EAAEwT,oBACPne,KAAK2K,EAAEyT,qBAAuBtJ,GAEzB9U,KAAK2K,EAAEyT,qBAAuBtJ,IACnC9U,KAAK2K,EAAEwT,kBAAoB,EAC3Bne,KAAK2K,EAAEuT,sBAAwBtJ,EAC/B5U,KAAK2K,EAAEyT,qBAAuBtJ,IAGtC,IAAIoI,EAAazG,EAAOzW,KAAKqa,MAAM5D,EAAO,GAAK,EAAG4H,EAAQre,KAAKkd,WAAald,KAAKiU,OAAOta,OAASujB,EAEjG,GAAIrmB,EAAOwD,EAAO0jB,eAA2B,OAATN,EAA0C,CAC1E,IAAIzc,EAAM3G,EAAOikB,UAAUte,KAAK+c,MAAO,GAA6B/c,KAAKgB,IAAMhB,KAAKgd,UACpFhd,KAAKge,UAAUnnB,EAAM+d,EAAO5T,EAAKqd,EAAQ,GAAG,EAChD,CACA,GAAa,OAATZ,EACAzd,KAAK+c,MAAQ/c,KAAKqa,MAAM5D,OAEvB,CACD,IAAI8H,EAAcve,KAAKqa,MAAM5D,EAAO,GACpCzW,KAAK+c,MAAQ1iB,EAAOyjB,QAAQS,EAAa1nB,GAAM,EACnD,CACA,KAAOmJ,KAAKqa,MAAM1gB,OAAS8c,GACvBzW,KAAKqa,MAAM3D,MACf1W,KAAKie,cAAcpnB,EAAM+d,EAC7B,CAKA,SAAAoJ,CAAUQ,EAAM5J,EAAOC,EAAKC,EAAO,EAAG2J,GAAW,GAC7C,GAAY,GAARD,KACExe,KAAKqa,MAAM1gB,QAAUqG,KAAKqa,MAAMra,KAAKqa,MAAM1gB,OAAS,GAAKqG,KAAKiU,OAAOta,OAASqG,KAAKkd,YAAa,CAElG,IAAIpD,EAAM9Z,KAAMkQ,EAAMlQ,KAAKiU,OAAOta,OAKlC,GAJW,GAAPuW,GAAY4J,EAAI/G,SAChB7C,EAAM4J,EAAIoD,WAAapD,EAAI/G,OAAOmK,WAClCpD,EAAMA,EAAI/G,QAEV7C,EAAM,GAA4B,GAAvB4J,EAAI7F,OAAO/D,EAAM,IAA0B4J,EAAI7F,OAAO/D,EAAM,IAAM,EAAG,CAChF,GAAI0E,GAASC,EACT,OACJ,GAAIiF,EAAI7F,OAAO/D,EAAM,IAAM0E,EAEvB,YADAkF,EAAI7F,OAAO/D,EAAM,GAAK2E,EAG9B,CACJ,CACA,GAAK4J,GAAYze,KAAKgB,KAAO6T,EAGxB,CACD,IAAIvT,EAAQtB,KAAKiU,OAAOta,OACxB,GAAI2H,EAAQ,GAA+B,GAA1BtB,KAAKiU,OAAO3S,EAAQ,GAAwB,CACzD,IAAIod,GAAW,EACf,IAAK,IAAI5L,EAAOxR,EAAOwR,EAAO,GAAK9S,KAAKiU,OAAOnB,EAAO,GAAK+B,EAAK/B,GAAQ,EACpE,GAAI9S,KAAKiU,OAAOnB,EAAO,IAAM,EAAG,CAC5B4L,GAAW,EACX,KACJ,CAEJ,GAAIA,EACA,KAAOpd,EAAQ,GAAKtB,KAAKiU,OAAO3S,EAAQ,GAAKuT,GAEzC7U,KAAKiU,OAAO3S,GAAStB,KAAKiU,OAAO3S,EAAQ,GACzCtB,KAAKiU,OAAO3S,EAAQ,GAAKtB,KAAKiU,OAAO3S,EAAQ,GAC7CtB,KAAKiU,OAAO3S,EAAQ,GAAKtB,KAAKiU,OAAO3S,EAAQ,GAC7CtB,KAAKiU,OAAO3S,EAAQ,GAAKtB,KAAKiU,OAAO3S,EAAQ,GAC7CA,GAAS,EACLwT,EAAO,IACPA,GAAQ,EAExB,CACA9U,KAAKiU,OAAO3S,GAASkd,EACrBxe,KAAKiU,OAAO3S,EAAQ,GAAKsT,EACzB5U,KAAKiU,OAAO3S,EAAQ,GAAKuT,EACzB7U,KAAKiU,OAAO3S,EAAQ,GAAKwT,CAC7B,MA5BI9U,KAAKiU,OAAOpZ,KAAK2jB,EAAM5J,EAAOC,EAAKC,EA6B3C,CAKA,KAAA6J,CAAMlB,EAAQ5mB,EAAM+d,EAAOC,GACvB,GAAa,OAAT4I,EACAzd,KAAKud,UAAmB,MAATE,EAAuCzd,KAAKgB,UAE1D,GAAc,OAATyc,EAaNzd,KAAKgB,IAAM6T,EACX7U,KAAK4e,aAAa/nB,EAAM+d,GACpB/d,GAAQmJ,KAAK2K,EAAEtQ,OAAOwkB,SACtB7e,KAAKiU,OAAOpZ,KAAKhE,EAAM+d,EAAOC,EAAK,OAhBY,CACnD,IAAIiK,EAAYrB,GAAQ,OAAEpjB,GAAW2F,KAAK2K,GACtCkK,EAAM7U,KAAKgB,KAAOnK,GAAQwD,EAAOwkB,WACjC7e,KAAKgB,IAAM6T,EACNxa,EAAOikB,UAAUQ,EAAW,KAC7B9e,KAAKgd,UAAYnI,IAEzB7U,KAAKud,UAAUuB,EAAWlK,GAC1B5U,KAAK4e,aAAa/nB,EAAM+d,GACpB/d,GAAQwD,EAAOwkB,SACf7e,KAAKiU,OAAOpZ,KAAKhE,EAAM+d,EAAOC,EAAK,EAC3C,CAOJ,CAKA,KAAAkK,CAAMtB,EAAQxI,EAAM+J,EAAWC,GACd,MAATxB,EACAzd,KAAKwd,OAAOC,GAEZzd,KAAK2e,MAAMlB,EAAQxI,EAAM+J,EAAWC,EAC5C,CAKA,OAAAC,CAAQjnB,EAAOgd,GACX,IAAI3T,EAAQtB,KAAK2K,EAAEyJ,OAAOza,OAAS,GAC/B2H,EAAQ,GAAKtB,KAAK2K,EAAEyJ,OAAO9S,IAAUrJ,KACrC+H,KAAK2K,EAAEyJ,OAAOvZ,KAAK5C,GACnBqJ,KAEJ,IAAIsT,EAAQ5U,KAAKgB,IACjBhB,KAAKgd,UAAYhd,KAAKgB,IAAM4T,EAAQ3c,EAAM0B,OAC1CqG,KAAKud,UAAUtI,EAAML,GACrB5U,KAAKiU,OAAOpZ,KAAKyG,EAAOsT,EAAO5U,KAAKgd,WAAY,GAC5Chd,KAAKmd,YACLnd,KAAKmf,cAAcnf,KAAKmd,WAAWiC,QAAQC,MAAMrf,KAAKmd,WAAW3mB,QAASyB,EAAO+H,KAAMA,KAAK2K,EAAE2U,OAAOC,MAAMvf,KAAKgB,IAAM/I,EAAM0B,SACpI,CAOA,KAAAzB,GACI,IAAI6a,EAAS/S,KACTwf,EAAMzM,EAAOkB,OAAOta,OAKxB,KAAO6lB,EAAM,GAAKzM,EAAOkB,OAAOuL,EAAM,GAAKzM,EAAOiK,WAC9CwC,GAAO,EACX,IAAIvL,EAASlB,EAAOkB,OAAO3gB,MAAMksB,GAAM/I,EAAO1D,EAAOmK,WAAasC,EAElE,KAAOzM,GAAU0D,GAAQ1D,EAAOmK,YAC5BnK,EAASA,EAAOA,OACpB,OAAO,IAAI+J,EAAM9c,KAAK2K,EAAG3K,KAAKqa,MAAM/mB,QAAS0M,KAAK+c,MAAO/c,KAAKgd,UAAWhd,KAAKgB,IAAKhB,KAAKid,MAAOhJ,EAAQwC,EAAMzW,KAAKmd,WAAYnd,KAAKuP,UAAWwD,EAClJ,CAKA,eAAA0M,CAAgBxK,EAAMgK,GAClB,IAAIS,EAASzK,GAAQjV,KAAK2K,EAAEtQ,OAAOwkB,QAC/Ba,GACA1f,KAAKge,UAAU/I,EAAMjV,KAAKgB,IAAKie,EAAS,GAC5Cjf,KAAKge,UAAU,EAAkBhe,KAAKgB,IAAKie,EAASS,EAAS,EAAI,GACjE1f,KAAKgB,IAAMhB,KAAKgd,UAAYiC,EAC5Bjf,KAAKid,OAAS,GAClB,CAOA,QAAA0C,CAASnB,GACL,IAAK,IAAIoB,EAAM,IAAIC,EAAe7f,QAAS,CACvC,IAAIyd,EAASzd,KAAK2K,EAAEtQ,OAAOylB,UAAUF,EAAI7C,MAAO,IAAqC/c,KAAK2K,EAAEtQ,OAAO0lB,UAAUH,EAAI7C,MAAOyB,GACxH,GAAc,GAAVf,EACA,OAAO,EACX,KAAc,MAATA,GACD,OAAO,EACXmC,EAAIpC,OAAOC,EACf,CACJ,CAMA,eAAAuC,CAAgB/K,GACZ,GAAIjV,KAAKqa,MAAM1gB,QAAU,IACrB,MAAO,GACX,IAAIsmB,EAAajgB,KAAK2K,EAAEtQ,OAAO4lB,WAAWjgB,KAAK+c,OAC/C,GAAIkD,EAAWtmB,OAAS,GAAgCqG,KAAKqa,MAAM1gB,QAAU,IAA0C,CACnH,IAAIumB,EAAO,GACX,IAAK,IAAWxY,EAAPM,EAAI,EAAMA,EAAIiY,EAAWtmB,OAAQqO,GAAK,GACtCN,EAAIuY,EAAWjY,EAAI,KAAOhI,KAAK+c,OAAS/c,KAAK2K,EAAEtQ,OAAO0lB,UAAUrY,EAAGuN,IACpEiL,EAAKrlB,KAAKolB,EAAWjY,GAAIN,GAEjC,GAAI1H,KAAKqa,MAAM1gB,OAAS,IACpB,IAAK,IAAIqO,EAAI,EAAGkY,EAAKvmB,OAAS,GAAgCqO,EAAIiY,EAAWtmB,OAAQqO,GAAK,EAAG,CACzF,IAAIN,EAAIuY,EAAWjY,EAAI,GAClBkY,EAAK9E,KAAK,CAAC+E,EAAGnY,IAAW,EAAJA,GAAUmY,GAAKzY,IACrCwY,EAAKrlB,KAAKolB,EAAWjY,GAAIN,EACjC,CACJuY,EAAaC,CACjB,CACA,IAAIlR,EAAS,GACb,IAAK,IAAIhH,EAAI,EAAGA,EAAIiY,EAAWtmB,QAAUqV,EAAOrV,OAAS,EAAyBqO,GAAK,EAAG,CACtF,IAAIN,EAAIuY,EAAWjY,EAAI,GACvB,GAAIN,GAAK1H,KAAK+c,MACV,SACJ,IAAI1C,EAAQra,KAAK9H,QACjBmiB,EAAMkD,UAAU7V,EAAG1H,KAAKgB,KACxBqZ,EAAM2D,UAAU,EAAkB3D,EAAMrZ,IAAKqZ,EAAMrZ,IAAK,GAAG,GAC3DqZ,EAAMuE,aAAaqB,EAAWjY,GAAIhI,KAAKgB,KACvCqZ,EAAM2C,UAAYhd,KAAKgB,IACvBqZ,EAAM4C,OAAS,IACfjO,EAAOnU,KAAKwf,EAChB,CACA,OAAOrL,CACX,CAMA,WAAAoR,GACI,IAAI,OAAE/lB,GAAW2F,KAAK2K,EAClB6S,EAASnjB,EAAOylB,UAAU9f,KAAK+c,MAAO,GAC1C,KAAc,MAATS,GACD,OAAO,EACX,IAAKnjB,EAAOgmB,YAAYrgB,KAAK+c,MAAOS,GAAS,CACzC,IAAI7I,EAAQ6I,GAAU,GAAkCgB,EAAgB,MAAThB,EAC3D7mB,EAASqJ,KAAKqa,MAAM1gB,OAAiB,EAARgb,EACjC,GAAIhe,EAAS,GAAK0D,EAAOyjB,QAAQ9d,KAAKqa,MAAM1jB,GAAS6nB,GAAM,GAAS,EAAG,CACnE,IAAI8B,EAAStgB,KAAKugB,sBAClB,GAAc,MAAVD,EACA,OAAO,EACX9C,EAAS8C,CACb,CACAtgB,KAAKge,UAAU,EAAkBhe,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxDhB,KAAKid,OAAS,GAClB,CAGA,OAFAjd,KAAKgd,UAAYhd,KAAKgB,IACtBhB,KAAKwd,OAAOA,IACL,CACX,CAMA,mBAAA+C,GACI,IAAI,OAAElmB,GAAW2F,KAAK2K,EAAG6V,EAAO,GAC5BC,EAAU,CAAC1D,EAAOpI,KAClB,IAAI6L,EAAK3f,SAASkc,GAGlB,OADAyD,EAAK3lB,KAAKkiB,GACH1iB,EAAOqmB,WAAW3D,EAAQU,IAC7B,GAAa,OAATA,QACC,GAAa,MAATA,EAAwC,CAC7C,IAAIkD,GAAUlD,GAAU,IAAoC9I,EAC5D,GAAIgM,EAAS,EAAG,CACZ,IAAInC,EAAgB,MAATf,EAAuC9mB,EAASqJ,KAAKqa,MAAM1gB,OAAkB,EAATgnB,EAC/E,GAAIhqB,GAAU,GAAK0D,EAAOyjB,QAAQ9d,KAAKqa,MAAM1jB,GAAS6nB,GAAM,IAAU,EAClE,OAAQmC,GAAU,GAAoC,MAAgCnC,CAC9F,CACJ,KACK,CACD,IAAI3N,EAAQ4P,EAAQhD,EAAQ9I,EAAQ,GACpC,GAAa,MAAT9D,EACA,OAAOA,CACf,KAGR,OAAO4P,EAAQzgB,KAAK+c,MAAO,EAC/B,CAIA,QAAA6D,GACI,MAAQ5gB,KAAK2K,EAAEtQ,OAAOikB,UAAUte,KAAK+c,MAAO,IACxC,IAAK/c,KAAKogB,cAAe,CACrBpgB,KAAKge,UAAU,EAAkBhe,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxD,KACJ,CAEJ,OAAOhB,IACX,CAMA,WAAI6gB,GACA,GAAyB,GAArB7gB,KAAKqa,MAAM1gB,OACX,OAAO,EACX,IAAI,OAAEU,GAAW2F,KAAK2K,EACtB,OAAgF,OAAzEtQ,EAAOkF,KAAKlF,EAAOylB,UAAU9f,KAAK+c,MAAO,MAC3C1iB,EAAOylB,UAAU9f,KAAK+c,MAAO,EACtC,CAMA,OAAA+D,GACI9gB,KAAKge,UAAU,EAAkBhe,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxDhB,KAAK+c,MAAQ/c,KAAKqa,MAAM,GACxBra,KAAKqa,MAAM1gB,OAAS,CACxB,CAIA,SAAAonB,CAAUC,GACN,GAAIhhB,KAAK+c,OAASiE,EAAMjE,OAAS/c,KAAKqa,MAAM1gB,QAAUqnB,EAAM3G,MAAM1gB,OAC9D,OAAO,EACX,IAAK,IAAIqO,EAAI,EAAGA,EAAIhI,KAAKqa,MAAM1gB,OAAQqO,GAAK,EACxC,GAAIhI,KAAKqa,MAAMrS,IAAMgZ,EAAM3G,MAAMrS,GAC7B,OAAO,EACf,OAAO,CACX,CAIA,UAAI3N,GAAW,OAAO2F,KAAK2K,EAAEtQ,MAAQ,CAKrC,cAAA4mB,CAAeC,GAAa,OAAOlhB,KAAK2K,EAAEtQ,OAAO8mB,QAAQpR,MAAMmR,EAAY,CAC3E,YAAAtC,CAAaJ,EAAM5J,GACX5U,KAAKmd,YACLnd,KAAKmf,cAAcnf,KAAKmd,WAAWiC,QAAQT,MAAM3e,KAAKmd,WAAW3mB,QAASgoB,EAAMxe,KAAMA,KAAK2K,EAAE2U,OAAOC,MAAM3K,IAClH,CACA,aAAAqJ,CAAcO,EAAM5J,GACZ5U,KAAKmd,YACLnd,KAAKmf,cAAcnf,KAAKmd,WAAWiC,QAAQ5B,OAAOxd,KAAKmd,WAAW3mB,QAASgoB,EAAMxe,KAAMA,KAAK2K,EAAE2U,OAAOC,MAAM3K,IACnH,CAIA,WAAAwM,GACI,IAAI9K,EAAOtW,KAAKiU,OAAOta,OAAS,GAC5B2c,EAAO,IAA2B,GAAtBtW,KAAKiU,OAAOqC,KACxBtW,KAAKiU,OAAOpZ,KAAKmF,KAAKmd,WAAWkE,KAAMrhB,KAAKgB,IAAKhB,KAAKgB,KAAM,EACpE,CAIA,aAAAsgB,GACI,IAAIhL,EAAOtW,KAAKiU,OAAOta,OAAS,GAC5B2c,EAAO,IAA2B,GAAtBtW,KAAKiU,OAAOqC,KACxBtW,KAAKiU,OAAOpZ,KAAKmF,KAAKuP,UAAWvP,KAAKgB,IAAKhB,KAAKgB,KAAM,EAC9D,CACA,aAAAme,CAAc3oB,GACV,GAAIA,GAAWwJ,KAAKmd,WAAW3mB,QAAS,CACpC,IAAI+qB,EAAQ,IAAIjE,EAAatd,KAAKmd,WAAWiC,QAAS5oB,GAClD+qB,EAAMF,MAAQrhB,KAAKmd,WAAWkE,MAC9BrhB,KAAKohB,cACTphB,KAAKmd,WAAaoE,CACtB,CACJ,CAIA,YAAA5D,CAAapO,GACLA,EAAYvP,KAAKuP,YACjBvP,KAAKshB,gBACLthB,KAAKuP,UAAYA,EAEzB,CAIA,KAAAiS,GACQxhB,KAAKmd,YAAcnd,KAAKmd,WAAWiC,QAAQqC,QAC3CzhB,KAAKohB,cACLphB,KAAKuP,UAAY,GACjBvP,KAAKshB,eACb,EAEJ,MAAMhE,EACF,WAAAld,CAAYgf,EAAS5oB,GACjBwJ,KAAKof,QAAUA,EACfpf,KAAKxJ,QAAUA,EACfwJ,KAAKqhB,KAAOjC,EAAQqC,OAASrC,EAAQiC,KAAK7qB,GAAW,CACzD,EAIJ,MAAMqpB,EACF,WAAAzf,CAAYwU,GACR5U,KAAK4U,MAAQA,EACb5U,KAAK+c,MAAQnI,EAAMmI,MACnB/c,KAAKqa,MAAQzF,EAAMyF,MACnBra,KAAKyW,KAAOzW,KAAKqa,MAAM1gB,MAC3B,CACA,MAAA6jB,CAAOC,GACH,IAAIe,EAAgB,MAATf,EAAuC9I,EAAQ8I,GAAU,GACvD,GAAT9I,GACI3U,KAAKqa,OAASra,KAAK4U,MAAMyF,QACzBra,KAAKqa,MAAQra,KAAKqa,MAAM/mB,SAC5B0M,KAAKqa,MAAMxf,KAAKmF,KAAK+c,MAAO,EAAG,GAC/B/c,KAAKyW,MAAQ,GAGbzW,KAAKyW,MAAsB,GAAb9B,EAAQ,GAE1B,IAAI+M,EAAO1hB,KAAK4U,MAAMjK,EAAEtQ,OAAOyjB,QAAQ9d,KAAKqa,MAAMra,KAAKyW,KAAO,GAAI+H,GAAM,GACxExe,KAAK+c,MAAQ2E,CACjB,EAIJ,MAAMC,EACF,WAAAvhB,CAAYia,EAAOrZ,EAAKM,GACpBtB,KAAKqa,MAAQA,EACbra,KAAKgB,IAAMA,EACXhB,KAAKsB,MAAQA,EACbtB,KAAKiU,OAASoG,EAAMpG,OACF,GAAdjU,KAAKsB,OACLtB,KAAK4hB,WACb,CACA,aAAO9R,CAAOuK,EAAOrZ,EAAMqZ,EAAM6C,WAAa7C,EAAMpG,OAAOta,QACvD,OAAO,IAAIgoB,EAAkBtH,EAAOrZ,EAAKA,EAAMqZ,EAAM6C,WACzD,CACA,SAAA0E,GACI,IAAI3M,EAAOjV,KAAKqa,MAAMtH,OACV,MAARkC,IACAjV,KAAKsB,MAAQtB,KAAKqa,MAAM6C,WAAajI,EAAKiI,WAC1Cld,KAAKqa,MAAQpF,EACbjV,KAAKiU,OAASgB,EAAKhB,OAE3B,CACA,MAAIvf,GAAO,OAAOsL,KAAKiU,OAAOjU,KAAKsB,MAAQ,EAAI,CAC/C,SAAIsT,GAAU,OAAO5U,KAAKiU,OAAOjU,KAAKsB,MAAQ,EAAI,CAClD,OAAIuT,GAAQ,OAAO7U,KAAKiU,OAAOjU,KAAKsB,MAAQ,EAAI,CAChD,QAAIwT,GAAS,OAAO9U,KAAKiU,OAAOjU,KAAKsB,MAAQ,EAAI,CACjD,IAAA2T,GACIjV,KAAKsB,OAAS,EACdtB,KAAKgB,KAAO,EACM,GAAdhB,KAAKsB,OACLtB,KAAK4hB,WACb,CACA,IAAA9K,GACI,OAAO,IAAI6K,EAAkB3hB,KAAKqa,MAAOra,KAAKgB,IAAKhB,KAAKsB,MAC5D,EAKJ,SAASugB,EAAYzY,EAAO0Y,EAAO1M,aAC/B,GAAoB,iBAAThM,EACP,OAAOA,EACX,IAAIkB,EAAQ,KACZ,IAAK,IAAItJ,EAAM,EAAG+gB,EAAM,EAAG/gB,EAAMoI,EAAMzP,QAAS,CAC5C,IAAI1B,EAAQ,EACZ,OAAS,CACL,IAAIgd,EAAO7L,EAAM4Y,WAAWhhB,KAAQihB,GAAO,EAC3C,GAAY,KAARhN,EAAqC,CACrChd,EAAQ,MACR,KACJ,CACIgd,GAAQ,IACRA,IACAA,GAAQ,IACRA,IACJ,IAAIiN,EAAQjN,EAAO,GAMnB,GALIiN,GAAS,KACTA,GAAS,GACTD,GAAO,GAEXhqB,GAASiqB,EACLD,EACA,MACJhqB,GAAS,EACb,CACIqS,EACAA,EAAMyX,KAAS9pB,EAEfqS,EAAQ,IAAIwX,EAAK7pB,EACzB,CACA,OAAOqS,CACX,CAEA,MAAM6X,EACF,WAAA/hB,GACIJ,KAAK4U,OAAS,EACd5U,KAAK/H,OAAS,EACd+H,KAAK6U,KAAO,EACZ7U,KAAKoiB,UAAY,EACjBpiB,KAAKuP,UAAY,EACjBvP,KAAKqiB,KAAO,EACZriB,KAAKxJ,QAAU,CACnB,EAEJ,MAAM8rB,EAAY,IAAIH,EAOtB,MAAMI,EAIF,WAAAniB,CAIAgJ,EAIAkT,GACItc,KAAKoJ,MAAQA,EACbpJ,KAAKsc,OAASA,EAIdtc,KAAK2c,MAAQ,GAIb3c,KAAKwiB,SAAW,EAIhBxiB,KAAKyiB,OAAS,GACdziB,KAAK0iB,UAAY,EAKjB1iB,KAAKiV,MAAQ,EAIbjV,KAAK2iB,MAAQL,EACbtiB,KAAK4iB,WAAa,EAClB5iB,KAAKgB,IAAMhB,KAAK6iB,SAAWvG,EAAO,GAAGvjB,KACrCiH,KAAK8I,MAAQwT,EAAO,GACpBtc,KAAK6U,IAAMyH,EAAOA,EAAO3iB,OAAS,GAAGR,GACrC6G,KAAK8iB,UACT,CAIA,aAAAC,CAAclH,EAAQmH,GAClB,IAAIla,EAAQ9I,KAAK8I,MAAOxH,EAAQtB,KAAK4iB,WACjC5hB,EAAMhB,KAAKgB,IAAM6a,EACrB,KAAO7a,EAAM8H,EAAM/P,MAAM,CACrB,IAAKuI,EACD,OAAO,KACX,IAAI2T,EAAOjV,KAAKsc,SAAShb,GACzBN,GAAO8H,EAAM/P,KAAOkc,EAAK9b,GACzB2P,EAAQmM,CACZ,CACA,KAAO+N,EAAQ,EAAIhiB,EAAM8H,EAAM3P,GAAK6H,GAAO8H,EAAM3P,IAAI,CACjD,GAAImI,GAAStB,KAAKsc,OAAO3iB,OAAS,EAC9B,OAAO,KACX,IAAIsb,EAAOjV,KAAKsc,SAAShb,GACzBN,GAAOiU,EAAKlc,KAAO+P,EAAM3P,GACzB2P,EAAQmM,CACZ,CACA,OAAOjU,CACX,CAIA,OAAAiiB,CAAQjiB,GACJ,GAAIA,GAAOhB,KAAK8I,MAAM/P,MAAQiI,EAAMhB,KAAK8I,MAAM3P,GAC3C,OAAO6H,EACX,IAAK,IAAI8H,KAAS9I,KAAKsc,OACnB,GAAIxT,EAAM3P,GAAK6H,EACX,OAAOmX,KAAKC,IAAIpX,EAAK8H,EAAM/P,MACnC,OAAOiH,KAAK6U,GAChB,CAYA,IAAAqO,CAAKrH,GACD,IAAkC7a,EAAKgO,EAAnCmU,EAAMnjB,KAAKwiB,SAAW3G,EAC1B,GAAIsH,GAAO,GAAKA,EAAMnjB,KAAK2c,MAAMhjB,OAC7BqH,EAAMhB,KAAKgB,IAAM6a,EACjB7M,EAAShP,KAAK2c,MAAMqF,WAAWmB,OAE9B,CACD,IAAIC,EAAWpjB,KAAK+iB,cAAclH,EAAQ,GAC1C,GAAgB,MAAZuH,EACA,OAAQ,EAEZ,GADApiB,EAAMoiB,EACFpiB,GAAOhB,KAAK0iB,WAAa1hB,EAAMhB,KAAK0iB,UAAY1iB,KAAKyiB,OAAO9oB,OAC5DqV,EAAShP,KAAKyiB,OAAOT,WAAWhhB,EAAMhB,KAAK0iB,eAE1C,CACD,IAAI1a,EAAIhI,KAAK4iB,WAAY9Z,EAAQ9I,KAAK8I,MACtC,KAAOA,EAAM3P,IAAM6H,GACf8H,EAAQ9I,KAAKsc,SAAStU,GAC1BhI,KAAKyiB,OAASziB,KAAKoJ,MAAMuT,MAAM3c,KAAK0iB,UAAY1hB,GAC5CA,EAAMhB,KAAKyiB,OAAO9oB,OAASmP,EAAM3P,KACjC6G,KAAKyiB,OAASziB,KAAKyiB,OAAOnvB,MAAM,EAAGwV,EAAM3P,GAAK6H,IAClDgO,EAAShP,KAAKyiB,OAAOT,WAAW,EACpC,CACJ,CAGA,OAFIhhB,GAAOhB,KAAK2iB,MAAMpT,YAClBvP,KAAK2iB,MAAMpT,UAAYvO,EAAM,GAC1BgO,CACX,CAMA,WAAAqU,CAAYV,EAAOW,EAAY,GAC3B,IAAIzO,EAAMyO,EAAYtjB,KAAK+iB,cAAcO,GAAY,GAAKtjB,KAAKgB,IAC/D,GAAW,MAAP6T,GAAeA,EAAM7U,KAAK2iB,MAAM/N,MAChC,MAAM,IAAI9F,WAAW,2BACzB9O,KAAK2iB,MAAM1qB,MAAQ0qB,EACnB3iB,KAAK2iB,MAAM9N,IAAMA,CACrB,CAIA,aAAA0O,CAAcZ,EAAOrN,GACjBtV,KAAK2iB,MAAM1qB,MAAQ0qB,EACnB3iB,KAAK2iB,MAAM9N,IAAMS,CACrB,CACA,QAAAkO,GACI,GAAIxjB,KAAKgB,KAAOhB,KAAK0iB,WAAa1iB,KAAKgB,IAAMhB,KAAK0iB,UAAY1iB,KAAKyiB,OAAO9oB,OAAQ,CAC9E,IAAI,MAAEgjB,EAAK,SAAEkG,GAAa7iB,KAC1BA,KAAK2c,MAAQ3c,KAAKyiB,OAClBziB,KAAK6iB,SAAW7iB,KAAK0iB,UACrB1iB,KAAKyiB,OAAS9F,EACd3c,KAAK0iB,UAAYG,EACjB7iB,KAAKwiB,SAAWxiB,KAAKgB,IAAMhB,KAAK6iB,QACpC,KACK,CACD7iB,KAAKyiB,OAASziB,KAAK2c,MACnB3c,KAAK0iB,UAAY1iB,KAAK6iB,SACtB,IAAIY,EAAYzjB,KAAKoJ,MAAMuT,MAAM3c,KAAKgB,KAClC6T,EAAM7U,KAAKgB,IAAMyiB,EAAU9pB,OAC/BqG,KAAK2c,MAAQ9H,EAAM7U,KAAK8I,MAAM3P,GAAKsqB,EAAUnwB,MAAM,EAAG0M,KAAK8I,MAAM3P,GAAK6G,KAAKgB,KAAOyiB,EAClFzjB,KAAK6iB,SAAW7iB,KAAKgB,IACrBhB,KAAKwiB,SAAW,CACpB,CACJ,CACA,QAAAM,GACI,OAAI9iB,KAAKwiB,UAAYxiB,KAAK2c,MAAMhjB,SAC5BqG,KAAKwjB,WACDxjB,KAAKwiB,UAAYxiB,KAAK2c,MAAMhjB,QACrBqG,KAAKiV,MAAQ,EAErBjV,KAAKiV,KAAOjV,KAAK2c,MAAMqF,WAAWhiB,KAAKwiB,SAClD,CAKA,OAAA9F,CAAQlC,EAAI,GAER,IADAxa,KAAKwiB,UAAYhI,EACVxa,KAAKgB,IAAMwZ,GAAKxa,KAAK8I,MAAM3P,IAAI,CAClC,GAAI6G,KAAK4iB,YAAc5iB,KAAKsc,OAAO3iB,OAAS,EACxC,OAAOqG,KAAK0jB,UAChBlJ,GAAKxa,KAAK8I,MAAM3P,GAAK6G,KAAKgB,IAC1BhB,KAAK8I,MAAQ9I,KAAKsc,SAAStc,KAAK4iB,YAChC5iB,KAAKgB,IAAMhB,KAAK8I,MAAM/P,IAC1B,CAIA,OAHAiH,KAAKgB,KAAOwZ,EACRxa,KAAKgB,KAAOhB,KAAK2iB,MAAMpT,YACvBvP,KAAK2iB,MAAMpT,UAAYvP,KAAKgB,IAAM,GAC/BhB,KAAK8iB,UAChB,CACA,OAAAY,GAII,OAHA1jB,KAAKgB,IAAMhB,KAAK6iB,SAAW7iB,KAAK6U,IAChC7U,KAAK8I,MAAQ9I,KAAKsc,OAAOtc,KAAK4iB,WAAa5iB,KAAKsc,OAAO3iB,OAAS,GAChEqG,KAAK2c,MAAQ,GACN3c,KAAKiV,MAAQ,CACxB,CAIA,KAAAsK,CAAMve,EAAK2hB,GAUP,GATIA,GACA3iB,KAAK2iB,MAAQA,EACbA,EAAM/N,MAAQ5T,EACd2hB,EAAMpT,UAAYvO,EAAM,EACxB2hB,EAAM1qB,MAAQ0qB,EAAMP,UAAY,GAGhCpiB,KAAK2iB,MAAQL,EAEbtiB,KAAKgB,KAAOA,EAAK,CAEjB,GADAhB,KAAKgB,IAAMA,EACPA,GAAOhB,KAAK6U,IAEZ,OADA7U,KAAK0jB,UACE1jB,KAEX,KAAOgB,EAAMhB,KAAK8I,MAAM/P,MACpBiH,KAAK8I,MAAQ9I,KAAKsc,SAAStc,KAAK4iB,YACpC,KAAO5hB,GAAOhB,KAAK8I,MAAM3P,IACrB6G,KAAK8I,MAAQ9I,KAAKsc,SAAStc,KAAK4iB,YAChC5hB,GAAOhB,KAAK6iB,UAAY7hB,EAAMhB,KAAK6iB,SAAW7iB,KAAK2c,MAAMhjB,OACzDqG,KAAKwiB,SAAWxhB,EAAMhB,KAAK6iB,UAG3B7iB,KAAK2c,MAAQ,GACb3c,KAAKwiB,SAAW,GAEpBxiB,KAAK8iB,UACT,CACA,OAAO9iB,IACX,CAIA,IAAA6c,CAAK9jB,EAAMI,GACP,GAAIJ,GAAQiH,KAAK6iB,UAAY1pB,GAAM6G,KAAK6iB,SAAW7iB,KAAK2c,MAAMhjB,OAC1D,OAAOqG,KAAK2c,MAAMrpB,MAAMyF,EAAOiH,KAAK6iB,SAAU1pB,EAAK6G,KAAK6iB,UAC5D,GAAI9pB,GAAQiH,KAAK0iB,WAAavpB,GAAM6G,KAAK0iB,UAAY1iB,KAAKyiB,OAAO9oB,OAC7D,OAAOqG,KAAKyiB,OAAOnvB,MAAMyF,EAAOiH,KAAK0iB,UAAWvpB,EAAK6G,KAAK0iB,WAC9D,GAAI3pB,GAAQiH,KAAK8I,MAAM/P,MAAQI,GAAM6G,KAAK8I,MAAM3P,GAC5C,OAAO6G,KAAKoJ,MAAMyT,KAAK9jB,EAAMI,GACjC,IAAI6V,EAAS,GACb,IAAK,IAAI0J,KAAK1Y,KAAKsc,OAAQ,CACvB,GAAI5D,EAAE3f,MAAQI,EACV,MACAuf,EAAEvf,GAAKJ,IACPiW,GAAUhP,KAAKoJ,MAAMyT,KAAK1E,KAAKC,IAAIM,EAAE3f,KAAMA,GAAOof,KAAKwL,IAAIjL,EAAEvf,GAAIA,IACzE,CACA,OAAO6V,CACX,EAKJ,MAAM4U,EACF,WAAAxjB,CAAYb,EAAM7K,GACdsL,KAAKT,KAAOA,EACZS,KAAKtL,GAAKA,CACd,CACA,KAAAiuB,CAAMvZ,EAAOiR,GACT,IAAI,OAAEhgB,GAAWggB,EAAM1P,EACvBkZ,EAAU7jB,KAAKT,KAAM6J,EAAOiR,EAAOra,KAAKtL,GAAI2F,EAAOkF,KAAMlF,EAAOypB,eACpE,EAEJF,EAAWG,UAAUC,WAAaJ,EAAWG,UAAU5uB,SAAWyuB,EAAWG,UAAU9S,QAAS,EA+BzD2S,EAAWG,UAAU5uB,SAAWyuB,EAAWG,UAAU9S,QAAS,EA4CrG,SAAS4S,EAAUtkB,EAAM6J,EAAOiR,EAAOjL,EAAO6U,EAAWC,GACrD,IAAInH,EAAQ,EAAGoH,EAAY,GAAK/U,GAAO,QAAE+R,GAAY9G,EAAM1P,EAAEtQ,OAC7DyY,EAAM,KAC+B,IAA5BqR,EAAY5kB,EAAKwd,KADX,CAGX,IAAIqH,EAAS7kB,EAAKwd,EAAQ,GAI1B,IAAK,IAAI/U,EAAI+U,EAAQ,EAAG/U,EAAIoc,EAAQpc,GAAK,EACrC,IAAKzI,EAAKyI,EAAI,GAAKmc,GAAa,EAAG,CAC/B,IAAI3F,EAAOjf,EAAKyI,GAChB,GAAImZ,EAAQkD,OAAO7F,MACQ,GAAtBpV,EAAMuZ,MAAM1qB,OAAemR,EAAMuZ,MAAM1qB,OAASumB,GAC7C8F,EAAU9F,EAAMpV,EAAMuZ,MAAM1qB,MAAOgsB,EAAWC,IAAc,CAChE9a,EAAMia,YAAY7E,GAClB,KACJ,CACJ,CACJ,IAAIvJ,EAAO7L,EAAM6L,KAAMsP,EAAM,EAAGC,EAAOjlB,EAAKwd,EAAQ,GAEpD,KAAI3T,EAAM6L,KAAO,GAAKuP,EAAOD,GAAsC,OAA/BhlB,EAAK6kB,EAAgB,EAAPI,EAAW,IAA7D,CAKA,KAAOD,EAAMC,GAAO,CAChB,IAAIC,EAAOF,EAAMC,GAAS,EACtBljB,EAAQ8iB,EAASK,GAAOA,GAAO,GAC/B1rB,EAAOwG,EAAK+B,GAAQnI,EAAKoG,EAAK+B,EAAQ,IAAM,MAChD,GAAI2T,EAAOlc,EACPyrB,EAAOC,MACN,MAAIxP,GAAQ9b,GAEZ,CACD4jB,EAAQxd,EAAK+B,EAAQ,GACrB8H,EAAMsT,UACN,SAAS5J,CACb,CALIyR,EAAME,EAAM,CAKhB,CACJ,CACA,KAhBA,CAFI1H,EAAQxd,EAAK6kB,EAAgB,EAAPI,EAAW,EAmBzC,CACJ,CACA,SAASE,EAAWnlB,EAAMqV,EAAO4J,GAC7B,IAAK,IAAevJ,EAAXjN,EAAI4M,EAAiC,QAAnBK,EAAO1V,EAAKyI,IAA4BA,IAC/D,GAAIiN,GAAQuJ,EACR,OAAOxW,EAAI4M,EACnB,OAAQ,CACZ,CACA,SAAS0P,EAAU3B,EAAO1H,EAAM0J,EAAWC,GACvC,IAAIC,EAAQH,EAAWC,EAAWC,EAAa3J,GAC/C,OAAO4J,EAAQ,GAAKH,EAAWC,EAAWC,EAAajC,GAASkC,CACpE,CAGA,MAAMC,EAA4B,oBAAXC,SAA0BA,QAAQC,KAAO,YAAYnT,KAAKkT,QAAQC,IAAIC,KAC7F,IAAIC,EAAW,KACf,SAASC,EAAMzV,EAAM1O,EAAKmR,GACtB,IAAIL,EAASpC,EAAKoC,OAAOL,EAAS6B,kBAElC,IADAxB,EAAOO,OAAOrR,KAEV,KAAMmR,EAAO,EAAIL,EAAOiH,YAAY/X,GAAO8Q,EAAO5Q,WAAWF,IACzD,OAAS,CACL,IAAKmR,EAAO,EAAIL,EAAO3Y,GAAK6H,EAAM8Q,EAAO/Y,KAAOiI,KAAS8Q,EAAOjb,KAAK0Z,QACjE,OAAO4B,EAAO,EAAIgG,KAAKC,IAAI,EAAGD,KAAKwL,IAAI7R,EAAO3Y,GAAK,EAAG6H,EAAM,KACtDmX,KAAKwL,IAAIjU,EAAK/V,OAAQwe,KAAKC,IAAItG,EAAO/Y,KAAO,EAAGiI,EAAM,KAChE,GAAImR,EAAO,EAAIL,EAAOmH,cAAgBnH,EAAO4B,cACzC,MACJ,IAAK5B,EAAOiB,SACR,OAAOZ,EAAO,EAAI,EAAIzC,EAAK/V,MACnC,CAEZ,CACA,MAAM,EACF,WAAAyG,CAAYic,EAAWnI,GACnBlU,KAAKqc,UAAYA,EACjBrc,KAAKkU,QAAUA,EACflU,KAAKgI,EAAI,EACThI,KAAKolB,SAAW,KAChBplB,KAAKqlB,UAAY,EACjBrlB,KAAKslB,QAAU,EACftlB,KAAKulB,MAAQ,GACbvlB,KAAK4U,MAAQ,GACb5U,KAAKsB,MAAQ,GACbtB,KAAKwlB,cACT,CACA,YAAAA,GACI,IAAIC,EAAKzlB,KAAKolB,SAAWplB,KAAKgI,GAAKhI,KAAKqc,UAAU1iB,OAAS,KAAOqG,KAAKqc,UAAUrc,KAAKgI,KACtF,GAAIyd,EAAI,CAGJ,IAFAzlB,KAAKqlB,SAAWI,EAAGC,UAAYP,EAAMM,EAAG/V,KAAM+V,EAAG1sB,KAAO0sB,EAAG5J,OAAQ,GAAK4J,EAAG5J,OAAS4J,EAAG1sB,KACvFiH,KAAKslB,OAASG,EAAGE,QAAUR,EAAMM,EAAG/V,KAAM+V,EAAGtsB,GAAKssB,EAAG5J,QAAS,GAAK4J,EAAG5J,OAAS4J,EAAGtsB,GAC3E6G,KAAKulB,MAAM5rB,QACdqG,KAAKulB,MAAM7O,MACX1W,KAAK4U,MAAM8B,MACX1W,KAAKsB,MAAMoV,MAEf1W,KAAKulB,MAAM1qB,KAAK4qB,EAAG/V,MACnB1P,KAAK4U,MAAM/Z,MAAM4qB,EAAG5J,QACpB7b,KAAKsB,MAAMzG,KAAK,GAChBmF,KAAKgf,UAAYhf,KAAKqlB,QAC1B,MAEIrlB,KAAKgf,UAAY,GAEzB,CAEA,MAAA4G,CAAO5kB,GACH,GAAIA,EAAMhB,KAAKgf,UACX,OAAO,KACX,KAAOhf,KAAKolB,UAAYplB,KAAKslB,QAAUtkB,GACnChB,KAAKwlB,eACT,IAAKxlB,KAAKolB,SACN,OAAO,KACX,OAAS,CACL,IAAI9O,EAAOtW,KAAKulB,MAAM5rB,OAAS,EAC/B,GAAI2c,EAAO,EAEP,OADAtW,KAAKwlB,eACE,KAEX,IAAItV,EAAMlQ,KAAKulB,MAAMjP,GAAOhV,EAAQtB,KAAKsB,MAAMgV,GAC/C,GAAIhV,GAAS4O,EAAIyB,SAAShY,OAAQ,CAC9BqG,KAAKulB,MAAM7O,MACX1W,KAAK4U,MAAM8B,MACX1W,KAAKsB,MAAMoV,MACX,QACJ,CACA,IAAIzB,EAAO/E,EAAIyB,SAASrQ,GACpBsT,EAAQ5U,KAAK4U,MAAM0B,GAAQpG,EAAInP,UAAUO,GAC7C,GAAIsT,EAAQ5T,EAER,OADAhB,KAAKgf,UAAYpK,EACV,KAEX,GAAIK,aAAgBvD,EAAM,CACtB,GAAIkD,GAAS5T,EAAK,CACd,GAAI4T,EAAQ5U,KAAKqlB,SACb,OAAO,KACX,IAAIxQ,EAAMD,EAAQK,EAAKtb,OACvB,GAAIkb,GAAO7U,KAAKslB,OAAQ,CACpB,IAAI/V,EAAY0F,EAAKzN,KAAKiH,EAASc,WACnC,IAAKA,GAAasF,EAAMtF,EAAYvP,KAAKolB,SAASjsB,GAC9C,OAAO8b,CACf,CACJ,CACAjV,KAAKsB,MAAMgV,KACP1B,EAAQK,EAAKtb,QAAUwe,KAAKC,IAAIpY,KAAKqlB,SAAUrkB,KAC/ChB,KAAKulB,MAAM1qB,KAAKoa,GAChBjV,KAAK4U,MAAM/Z,KAAK+Z,GAChB5U,KAAKsB,MAAMzG,KAAK,GAExB,MAEImF,KAAKsB,MAAMgV,KACXtW,KAAKgf,UAAYpK,EAAQK,EAAKtb,MAEtC,CACJ,EAEJ,MAAMksB,EACF,WAAAzlB,CAAY/F,EAAQilB,GAChBtf,KAAKsf,OAASA,EACdtf,KAAK8lB,OAAS,GACd9lB,KAAK+lB,UAAY,KACjB/lB,KAAKgmB,QAAU,GACfhmB,KAAK8lB,OAASzrB,EAAO4rB,WAAWpiB,IAAIuZ,GAAK,IAAI+E,EACjD,CACA,UAAA+D,CAAW7L,GACP,IAAI8L,EAAc,EACdC,EAAO,MACP,OAAE/rB,GAAWggB,EAAM1P,GAAG,WAAEsb,GAAe5rB,EACvCgoB,EAAOhoB,EAAOylB,UAAUzF,EAAM0C,MAAO,GACrCvmB,EAAU6jB,EAAM8C,WAAa9C,EAAM8C,WAAWkE,KAAO,EACrD9R,EAAY,EAChB,IAAK,IAAIvH,EAAI,EAAGA,EAAIie,EAAWtsB,OAAQqO,IAAK,CACxC,KAAM,GAAKA,EAAKqa,GACZ,SACJ,IAAIgE,EAAYJ,EAAWje,GAAI2a,EAAQ3iB,KAAK8lB,OAAO9d,GACnD,KAAIoe,GAASC,EAAUlxB,aAEnBkxB,EAAUrC,YAAcrB,EAAM/N,OAASyF,EAAMrZ,KAAO2hB,EAAMN,MAAQA,GAAQM,EAAMnsB,SAAWA,KAC3FwJ,KAAKsmB,kBAAkB3D,EAAO0D,EAAWhM,GACzCsI,EAAMN,KAAOA,EACbM,EAAMnsB,QAAUA,GAEhBmsB,EAAMpT,UAAYoT,EAAM9N,IAAM,KAC9BtF,EAAY4I,KAAKC,IAAIuK,EAAMpT,UAAWA,IACvB,GAAfoT,EAAM1qB,OAA2B,CACjC,IAAImf,EAAa+O,EAIjB,GAHIxD,EAAMP,UAAY,IAClB+D,EAAcnmB,KAAKumB,WAAWlM,EAAOsI,EAAMP,SAAUO,EAAM9N,IAAKsR,IACpEA,EAAcnmB,KAAKumB,WAAWlM,EAAOsI,EAAM1qB,MAAO0qB,EAAM9N,IAAKsR,IACxDE,EAAUpV,SACXmV,EAAOzD,EACHwD,EAAc/O,GACd,KAEZ,CACJ,CACA,KAAOpX,KAAKgmB,QAAQrsB,OAASwsB,GACzBnmB,KAAKgmB,QAAQtP,MAUjB,OATInH,GACA8K,EAAMsD,aAAapO,GAClB6W,GAAQ/L,EAAMrZ,KAAOhB,KAAKsf,OAAOzK,MAClCuR,EAAO,IAAIjE,EACXiE,EAAKnuB,MAAQoiB,EAAM1P,EAAEtQ,OAAOmsB,QAC5BJ,EAAKxR,MAAQwR,EAAKvR,IAAMwF,EAAMrZ,IAC9BmlB,EAAcnmB,KAAKumB,WAAWlM,EAAO+L,EAAKnuB,MAAOmuB,EAAKvR,IAAKsR,IAE/DnmB,KAAK+lB,UAAYK,EACVpmB,KAAKgmB,OAChB,CACA,YAAAS,CAAapM,GACT,GAAIra,KAAK+lB,UACL,OAAO/lB,KAAK+lB,UAChB,IAAIK,EAAO,IAAIjE,GAAa,IAAEnhB,EAAG,EAAE2J,GAAM0P,EAIzC,OAHA+L,EAAKxR,MAAQ5T,EACbolB,EAAKvR,IAAMsD,KAAKwL,IAAI3iB,EAAM,EAAG2J,EAAE2U,OAAOzK,KACtCuR,EAAKnuB,MAAQ+I,GAAO2J,EAAE2U,OAAOzK,IAAMlK,EAAEtQ,OAAOmsB,QAAU,EAC/CJ,CACX,CACA,iBAAAE,CAAkB3D,EAAO0D,EAAWhM,GAChC,IAAIzF,EAAQ5U,KAAKsf,OAAO2D,QAAQ5I,EAAMrZ,KAEtC,GADAqlB,EAAU1D,MAAM3iB,KAAKsf,OAAOC,MAAM3K,EAAO+N,GAAQtI,GAC7CsI,EAAM1qB,OAAS,EAAG,CAClB,IAAI,OAAEoC,GAAWggB,EAAM1P,EACvB,IAAK,IAAI3C,EAAI,EAAGA,EAAI3N,EAAOqsB,YAAY/sB,OAAQqO,IAC3C,GAAI3N,EAAOqsB,YAAY1e,IAAM2a,EAAM1qB,MAAO,CACtC,IAAI+W,EAAS3U,EAAOssB,aAAa3e,GAAGhI,KAAKsf,OAAOzC,KAAK8F,EAAM/N,MAAO+N,EAAM9N,KAAMwF,GAC9E,GAAIrL,GAAU,GAAKqL,EAAM1P,EAAEtQ,OAAO8mB,QAAQkD,OAAOrV,GAAU,GAAI,CAC7C,EAATA,EAGD2T,EAAMP,SAAWpT,GAAU,EAF3B2T,EAAM1qB,MAAQ+W,GAAU,EAG5B,KACJ,CACJ,CACR,MAEI2T,EAAM1qB,MAAQ,EACd0qB,EAAM9N,IAAM7U,KAAKsf,OAAO2D,QAAQrO,EAAQ,EAEhD,CACA,SAAAgS,CAAUnJ,EAAQkF,EAAO9N,EAAKvT,GAE1B,IAAK,IAAI0G,EAAI,EAAGA,EAAI1G,EAAO0G,GAAK,EAC5B,GAAIhI,KAAKgmB,QAAQhe,IAAMyV,EACnB,OAAOnc,EAIf,OAHAtB,KAAKgmB,QAAQ1kB,KAAWmc,EACxBzd,KAAKgmB,QAAQ1kB,KAAWqhB,EACxB3iB,KAAKgmB,QAAQ1kB,KAAWuT,EACjBvT,CACX,CACA,UAAAilB,CAAWlM,EAAOsI,EAAO9N,EAAKvT,GAC1B,IAAI,MAAEyb,GAAU1C,GAAO,OAAEhgB,GAAWggB,EAAM1P,GAAG,KAAEpL,GAASlF,EACxD,IAAK,IAAIoC,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAAIuL,EAAI3N,EAAOylB,UAAU/C,EAAOtgB,EAAM,EAA0B,IAA8BuL,GAAK,EAAG,CACvG,GAAe,OAAXzI,EAAKyI,GAA2B,CAChC,GAAmB,GAAfzI,EAAKyI,EAAI,GAGR,CACY,GAAT1G,GAA6B,GAAf/B,EAAKyI,EAAI,KACvB1G,EAAQtB,KAAK4mB,UAAUjQ,GAAKpX,EAAMyI,EAAI,GAAI2a,EAAO9N,EAAKvT,IAC1D,KACJ,CANI0G,EAAI2O,GAAKpX,EAAMyI,EAAI,EAO3B,CACIzI,EAAKyI,IAAM2a,IACXrhB,EAAQtB,KAAK4mB,UAAUjQ,GAAKpX,EAAMyI,EAAI,GAAI2a,EAAO9N,EAAKvT,GAC9D,CAEJ,OAAOA,CACX,EAEJ,MAAMulB,EACF,WAAAzmB,CAAY/F,EAAQ+O,EAAOiT,EAAWC,GAClCtc,KAAK3F,OAASA,EACd2F,KAAKoJ,MAAQA,EACbpJ,KAAKsc,OAASA,EACdtc,KAAK8mB,WAAa,EAClB9mB,KAAK+mB,YAAc,KACnB/mB,KAAKgnB,YAAc,EACnBhnB,KAAKoU,OAAS,GACdpU,KAAKinB,UAAY,KACjBjnB,KAAKke,uBAAyB,EAC9Ble,KAAKoe,qBAAuB,EAC5Bpe,KAAKme,kBAAoB,EACzBne,KAAKsf,OAAS,IAAIiD,EAAYnZ,EAAOkT,GACrCtc,KAAK8lB,OAAS,IAAID,EAAWxrB,EAAQ2F,KAAKsf,QAC1Ctf,KAAKknB,QAAU7sB,EAAO6V,IAAI,GAC1B,IAAI,KAAEnX,GAASujB,EAAO,GACtBtc,KAAKmnB,OAAS,CAACrK,EAAMlI,MAAM5U,KAAM3F,EAAO6V,IAAI,GAAInX,IAChDiH,KAAKqc,UAAYA,EAAU1iB,QAAUqG,KAAKsf,OAAOzK,IAAM9b,EAA6B,EAAtBsB,EAAO+sB,aAC/D,IAAI,EAAe/K,EAAWhiB,EAAO6Z,SAAW,IAC1D,CACA,aAAImT,GACA,OAAOrnB,KAAKgnB,WAChB,CAOA,OAAAtK,GACI,IAGI4K,EAASC,EAHTJ,EAASnnB,KAAKmnB,OAAQnmB,EAAMhB,KAAKgnB,YAEjCQ,EAAYxnB,KAAKmnB,OAAS,GAS9B,GAAInnB,KAAKme,kBAAoB,KAAmE,GAAjBgJ,EAAOxtB,OAAa,CAC/F,IAAK+N,GAAKyf,EACV,KAAOzf,EAAE0Y,eAAiB1Y,EAAE2S,MAAM1gB,QAAU+N,EAAE2S,MAAM3S,EAAE2S,MAAM1gB,OAAS,IAAMqG,KAAKke,wBAChFle,KAAKme,kBAAoBne,KAAKoe,qBAAuB,CACzD,CAIA,IAAK,IAAIpW,EAAI,EAAGA,EAAImf,EAAOxtB,OAAQqO,IAAK,CACpC,IAAIqS,EAAQ8M,EAAOnf,GACnB,OAAS,CAEL,GADAhI,KAAK8lB,OAAOC,UAAY,KACpB1L,EAAMrZ,IAAMA,EACZwmB,EAAU3sB,KAAKwf,OAEd,IAAIra,KAAKynB,aAAapN,EAAOmN,EAAWL,GACzC,SAEC,CACIG,IACDA,EAAU,GACVC,EAAgB,IAEpBD,EAAQzsB,KAAKwf,GACb,IAAIqN,EAAM1nB,KAAK8lB,OAAOW,aAAapM,GACnCkN,EAAc1sB,KAAK6sB,EAAIzvB,MAAOyvB,EAAI7S,IACtC,EACA,KACJ,CACJ,CACA,IAAK2S,EAAU7tB,OAAQ,CACnB,IAAIguB,EAAWL,GAuhB3B,SAAsBH,GAClB,IAAIjH,EAAO,KACX,IAAK,IAAI7F,KAAS8M,EAAQ,CACtB,IAAIG,EAAUjN,EAAM1P,EAAEsc,WACjB5M,EAAMrZ,KAAOqZ,EAAM1P,EAAE2U,OAAOzK,KAAkB,MAAXyS,GAAmBjN,EAAMrZ,IAAMsmB,IACnEjN,EAAM1P,EAAEtQ,OAAOikB,UAAUjE,EAAM0C,MAAO,MACpCmD,GAAQA,EAAKjD,MAAQ5C,EAAM4C,SAC7BiD,EAAO7F,EACf,CACA,OAAO6F,CACX,CAjiBsC0H,CAAaN,GACvC,GAAIK,EAGA,OAAO3nB,KAAK6nB,YAAYF,GAE5B,GAAI3nB,KAAK3F,OAAOonB,OAGZ,MAAM,IAAIqG,YAAY,eAAiB9mB,GAEtChB,KAAK8mB,aACN9mB,KAAK8mB,WAAa,EAC1B,CACA,GAAI9mB,KAAK8mB,YAAcQ,EAAS,CAC5B,IAAIK,EAA6B,MAAlB3nB,KAAKinB,WAAqBK,EAAQ,GAAGtmB,IAAMhB,KAAKinB,UAAYK,EAAQ,GAC7EtnB,KAAK+nB,YAAYT,EAASC,EAAeC,GAC/C,GAAIG,EAGA,OAAO3nB,KAAK6nB,YAAYF,EAAS/G,WAEzC,CACA,GAAI5gB,KAAK8mB,WAAY,CACjB,IAAIkB,EAAkC,GAAnBhoB,KAAK8mB,WAAkB,EAAsB,EAAlB9mB,KAAK8mB,WACnD,GAAIU,EAAU7tB,OAASquB,EAEnB,IADAR,EAAUlvB,KAAK,CAACC,EAAGyf,IAAMA,EAAEiF,MAAQ1kB,EAAE0kB,OAC9BuK,EAAU7tB,OAASquB,GACtBR,EAAU9Q,MAEd8Q,EAAUpM,KAAK1T,GAAKA,EAAEsV,UAAYhc,IAClChB,KAAK8mB,YACb,MACK,GAAIU,EAAU7tB,OAAS,EAAG,CAI3BsuB,EAAO,IAAK,IAAIjgB,EAAI,EAAGA,EAAIwf,EAAU7tB,OAAS,EAAGqO,IAAK,CAClD,IAAIqS,EAAQmN,EAAUxf,GACtB,IAAK,IAAIqO,EAAIrO,EAAI,EAAGqO,EAAImR,EAAU7tB,OAAQ0c,IAAK,CAC3C,IAAI2K,EAAQwG,EAAUnR,GACtB,GAAIgE,EAAM0G,UAAUC,IAChB3G,EAAMpG,OAAOta,OAAS,KAAsCqnB,EAAM/M,OAAOta,OAAS,IAAoC,CACtH,MAAM0gB,EAAM4C,MAAQ+D,EAAM/D,OAAW5C,EAAMpG,OAAOta,OAASqnB,EAAM/M,OAAOta,QAAW,GAG9E,CACD6tB,EAAUrN,OAAOnS,IAAK,GACtB,SAASigB,CACb,CALIT,EAAUrN,OAAO9D,IAAK,EAM9B,CACJ,CACJ,CACImR,EAAU7tB,OAAS,IACnB6tB,EAAUrN,OAAO,GAA4BqN,EAAU7tB,OAAS,GACxE,CACAqG,KAAKgnB,YAAcQ,EAAU,GAAGxmB,IAChC,IAAK,IAAIgH,EAAI,EAAGA,EAAIwf,EAAU7tB,OAAQqO,IAC9Bwf,EAAUxf,GAAGhH,IAAMhB,KAAKgnB,cACxBhnB,KAAKgnB,YAAcQ,EAAUxf,GAAGhH,KACxC,OAAO,IACX,CACA,MAAAoV,CAAOpV,GACH,GAAsB,MAAlBhB,KAAKinB,WAAqBjnB,KAAKinB,UAAYjmB,EAC3C,MAAM,IAAI8N,WAAW,gCACzB9O,KAAKinB,UAAYjmB,CACrB,CAKA,YAAAymB,CAAapN,EAAO8M,EAAQjvB,GACxB,IAAI0c,EAAQyF,EAAMrZ,KAAK,OAAE3G,GAAW2F,KACzB8kB,GAAU9kB,KAAKkoB,QAAQ7N,GAClC,GAAsB,MAAlBra,KAAKinB,WAAqBrS,EAAQ5U,KAAKinB,UACvC,OAAO5M,EAAM+F,cAAgB/F,EAAQ,KACzC,GAAIra,KAAKqc,UAAW,CAChB,IAAI8L,EAAW9N,EAAM8C,YAAc9C,EAAM8C,WAAWiC,QAAQqC,OAAQ2G,EAASD,EAAW9N,EAAM8C,WAAWkE,KAAO,EAChH,IAAK,IAAIgH,EAASroB,KAAKqc,UAAUuJ,OAAOhR,GAAQyT,GAAS,CACrD,IAAIttB,EAAQiF,KAAK3F,OAAO6Z,QAAQlD,MAAMqX,EAAOxxB,KAAKnC,KAAO2zB,EAAOxxB,KAAOwD,EAAOyjB,QAAQzD,EAAM0C,MAAOsL,EAAOxxB,KAAKnC,KAAO,EACtH,GAAIqG,GAAS,GAAKstB,EAAO1uB,UAAYwuB,IAAaE,EAAO7gB,KAAKiH,EAASa,cAAgB,IAAM8Y,GAIzF,OAHA/N,EAAM6E,QAAQmJ,EAAQttB,IAGf,EAEX,KAAMstB,aAAkB3W,IAAmC,GAA1B2W,EAAO1W,SAAShY,QAAe0uB,EAAOtnB,UAAU,GAAK,EAClF,MACJ,IAAI6R,EAAQyV,EAAO1W,SAAS,GAC5B,KAAIiB,aAAiBlB,GAA+B,GAAvB2W,EAAOtnB,UAAU,IAG1C,MAFAsnB,EAASzV,CAGjB,CACJ,CACA,IAAI0V,EAAgBjuB,EAAOylB,UAAUzF,EAAM0C,MAAO,GAClD,GAAIuL,EAAgB,EAIhB,OAHAjO,EAAMmD,OAAO8K,IAGN,EAEX,GAAIjO,EAAMA,MAAM1gB,QAAU,KACtB,KAAO0gB,EAAMA,MAAM1gB,OAAS,KAAwB0gB,EAAM+F,gBAE9D,IAAI4F,EAAUhmB,KAAK8lB,OAAOI,WAAW7L,GACrC,IAAK,IAAIrS,EAAI,EAAGA,EAAIge,EAAQrsB,QAAS,CACjC,IAAI8jB,EAASuI,EAAQhe,KAAMwW,EAAOwH,EAAQhe,KAAM6M,EAAMmR,EAAQhe,KAC1DsO,EAAOtO,GAAKge,EAAQrsB,SAAWzB,EAC/BqwB,EAAajS,EAAO+D,EAAQA,EAAMniB,QAClCkuB,EAAOpmB,KAAK8lB,OAAOC,UAKvB,GAJAwC,EAAWxJ,MAAMtB,EAAQe,EAAM4H,EAAOA,EAAKxR,MAAQ2T,EAAWvnB,IAAK6T,GAI/DyB,EACA,OAAO,EACFiS,EAAWvnB,IAAM4T,EACtBuS,EAAOtsB,KAAK0tB,GAEZrwB,EAAM2C,KAAK0tB,EACnB,CACA,OAAO,CACX,CAIA,YAAAC,CAAanO,EAAOmN,GAChB,IAAIxmB,EAAMqZ,EAAMrZ,IAChB,OAAS,CACL,IAAKhB,KAAKynB,aAAapN,EAAO,KAAM,MAChC,OAAO,EACX,GAAIA,EAAMrZ,IAAMA,EAEZ,OADAynB,EAAepO,EAAOmN,IACf,CAEf,CACJ,CACA,WAAAO,CAAYZ,EAAQrB,EAAQ0B,GACxB,IAAIG,EAAW,KAAMe,GAAY,EACjC,IAAK,IAAI1gB,EAAI,EAAGA,EAAImf,EAAOxtB,OAAQqO,IAAK,CACpC,IAAIqS,EAAQ8M,EAAOnf,GAAI2a,EAAQmD,EAAO9d,GAAK,GAAI2gB,EAAW7C,EAAkB,GAAV9d,GAAK,IACnEyO,EAAOqO,EAAU9kB,KAAKkoB,QAAQ7N,GAAS,OAAS,GACpD,GAAIA,EAAMwG,QAAS,CACf,GAAI6H,EACA,SAMJ,GALAA,GAAY,EACZrO,EAAMyG,UAGK9gB,KAAKwoB,aAAanO,EAAOmN,GAEhC,QACR,CACA,IAAIoB,EAAQvO,EAAMniB,QAAS2wB,EAAYpS,EACvC,IAAK,IAAIJ,EAAI,EAAGuS,EAAMxI,eAAiB/J,EAAI,GAA+BA,IAAK,CAI3E,GADWrW,KAAKwoB,aAAaI,EAAOpB,GAEhC,MACA1C,IACA+D,EAAY7oB,KAAKkoB,QAAQU,GAAS,OAC1C,CACA,IAAK,IAAIE,KAAUzO,EAAM2F,gBAAgB2C,GAGrC3iB,KAAKwoB,aAAaM,EAAQtB,GAE1BxnB,KAAKsf,OAAOzK,IAAMwF,EAAMrZ,KACpB2nB,GAAYtO,EAAMrZ,MAClB2nB,IACAhG,EAAQ,GAEZtI,EAAMoF,gBAAgBkD,EAAOgG,GAG7BF,EAAepO,EAAOmN,MAEhBG,GAAYA,EAAS1K,MAAQ5C,EAAM4C,SACzC0K,EAAWtN,EAEnB,CACA,OAAOsN,CACX,CAEA,WAAAE,CAAYxN,GAER,OADAA,EAAMmH,QACC9P,EAAKqC,MAAM,CAAEE,OAAQ0N,EAAkB7R,OAAOuK,GACjDnG,QAASlU,KAAK3F,OAAO6Z,QACrBmD,MAAOrX,KAAKknB,QACZ/S,gBAAiBnU,KAAK3F,OAAO+sB,aAC7BhT,OAAQpU,KAAKoU,OACbQ,MAAO5U,KAAKsc,OAAO,GAAGvjB,KACtBY,OAAQ0gB,EAAMrZ,IAAMhB,KAAKsc,OAAO,GAAGvjB,KACnCsb,cAAerU,KAAK3F,OAAO0jB,eACnC,CACA,OAAAmK,CAAQ7N,GACJ,IAAI3lB,GAAMwwB,IAAaA,EAAW,IAAI3T,UAAU3B,IAAIyK,GAGpD,OAFK3lB,GACDwwB,EAASzoB,IAAI4d,EAAO3lB,EAAKkP,OAAOmlB,cAAc/oB,KAAK+mB,gBAChDryB,EAAK2lB,CAChB,EAEJ,SAASoO,EAAepO,EAAOmN,GAC3B,IAAK,IAAIxf,EAAI,EAAGA,EAAIwf,EAAU7tB,OAAQqO,IAAK,CACvC,IAAIgZ,EAAQwG,EAAUxf,GACtB,GAAIgZ,EAAMhgB,KAAOqZ,EAAMrZ,KAAOggB,EAAMD,UAAU1G,GAG1C,YAFImN,EAAUxf,GAAGiV,MAAQ5C,EAAM4C,QAC3BuK,EAAUxf,GAAKqS,GAG3B,CACAmN,EAAU3sB,KAAKwf,EACnB,CACA,MAAM2O,EACF,WAAA5oB,CAAYgR,EAAQrB,EAAOza,GACvB0K,KAAKoR,OAASA,EACdpR,KAAK+P,MAAQA,EACb/P,KAAK1K,SAAWA,CACpB,CACA,MAAA+uB,CAAO7F,GAAQ,OAAQxe,KAAK1K,UAAmC,GAAvB0K,KAAK1K,SAASkpB,EAAY,EAiCtE,MAAMyK,WAAiB9M,EAInB,WAAA/b,CAAY6P,GAMR,GALAkJ,QAIAnZ,KAAKkpB,SAAW,GACI,IAAhBjZ,EAAK/R,QACL,MAAM,IAAI4Q,WAAW,mBAAmBmB,EAAK/R,+CACjD,IAAIirB,EAAYlZ,EAAKkZ,UAAUjxB,MAAM,KACrC8H,KAAK+d,cAAgBoL,EAAUxvB,OAC/B,IAAK,IAAIqO,EAAI,EAAGA,EAAIiI,EAAKmZ,gBAAiBphB,IACtCmhB,EAAUtuB,KAAK,IACnB,IAAIwuB,EAAWlqB,OAAOC,KAAK6Q,EAAKqZ,UAAUzlB,IAAI6U,GAAKzI,EAAKqZ,SAAS5Q,GAAG,IAChE6Q,EAAY,GAChB,IAAK,IAAIvhB,EAAI,EAAGA,EAAImhB,EAAUxvB,OAAQqO,IAClCuhB,EAAU1uB,KAAK,IACnB,SAAS2uB,EAAQC,EAAQjiB,EAAMvP,GAC3BsxB,EAAUE,GAAQ5uB,KAAK,CAAC2M,EAAMA,EAAKoH,YAAYhL,OAAO3L,KAC1D,CACA,GAAIgY,EAAKsZ,UACL,IAAK,IAAIG,KAAYzZ,EAAKsZ,UAAW,CACjC,IAAI/hB,EAAOkiB,EAAS,GACD,iBAARliB,IACPA,EAAOiH,EAASjH,IACpB,IAAK,IAAIQ,EAAI,EAAGA,EAAI0hB,EAAS/vB,QAAS,CAClC,IAAIsb,EAAOyU,EAAS1hB,KACpB,GAAIiN,GAAQ,EACRuU,EAAQvU,EAAMzN,EAAMkiB,EAAS1hB,UAE5B,CACD,IAAI/P,EAAQyxB,EAAS1hB,GAAKiN,GAC1B,IAAK,IAAIoB,GAAKpB,EAAMoB,EAAI,EAAGA,IACvBmT,EAAQE,EAAS1hB,KAAMR,EAAMvP,GACjC+P,GACJ,CACJ,CACJ,CACJhI,KAAKkU,QAAU,IAAInD,EAAQoY,EAAUtlB,IAAI,CAACsD,EAAMa,IAAM+G,EAASiB,OAAO,CAClE7I,KAAMa,GAAKhI,KAAK+d,mBAAgBnd,EAAYuG,EAC5CzS,GAAIsT,EACJ/S,MAAOs0B,EAAUvhB,GACjBkI,IAAKmZ,EAAS3Y,QAAQ1I,IAAM,EAC5B5J,MAAY,GAAL4J,EACPmI,QAASF,EAAK0Z,cAAgB1Z,EAAK0Z,aAAajZ,QAAQ1I,IAAM,MAE9DiI,EAAK2Z,cACL5pB,KAAKkU,QAAUlU,KAAKkU,QAAQjD,UAAUhB,EAAK2Z,cAC/C5pB,KAAKyhB,QAAS,EACdzhB,KAAKonB,aAAe9Y,EACpB,IAAIub,EAAahI,EAAY5R,EAAK6Z,WAClC9pB,KAAKxJ,QAAUyZ,EAAKzZ,QACpBwJ,KAAK+pB,iBAAmB9Z,EAAKyW,aAAe,GAC5C1mB,KAAK0mB,YAAc,IAAItR,YAAYpV,KAAK+pB,iBAAiBpwB,QACzD,IAAK,IAAIqO,EAAI,EAAGA,EAAIhI,KAAK+pB,iBAAiBpwB,OAAQqO,IAC9ChI,KAAK0mB,YAAY1e,GAAKhI,KAAK+pB,iBAAiB/hB,GAAGwW,KACnDxe,KAAK2mB,aAAe3mB,KAAK+pB,iBAAiBlmB,IAAImmB,IAC9ChqB,KAAKiqB,OAASpI,EAAY5R,EAAKga,OAAQC,aACvClqB,KAAKT,KAAOsiB,EAAY5R,EAAKka,WAC7BnqB,KAAK0hB,KAAOG,EAAY5R,EAAKyR,MAC7B1hB,KAAKoqB,QAAUna,EAAKma,QACpBpqB,KAAKimB,WAAahW,EAAKgW,WAAWpiB,IAAI5L,GAAyB,iBAATA,EAAoB,IAAI2rB,EAAWiG,EAAY5xB,GAASA,GAC9G+H,KAAKspB,SAAWrZ,EAAKqZ,SACrBtpB,KAAKqqB,SAAWpa,EAAKoa,UAAY,CAAC,EAClCrqB,KAAKsqB,mBAAqBra,EAAKqa,oBAAsB,KACrDtqB,KAAK8jB,eAAiB7T,EAAKsa,UAC3BvqB,KAAKwqB,UAAYva,EAAKua,WAAa,KACnCxqB,KAAK6e,QAAU7e,KAAKkU,QAAQlD,MAAMrX,OAAS,EAC3CqG,KAAKmhB,QAAUnhB,KAAKyqB,eACpBzqB,KAAKkQ,IAAMlQ,KAAKspB,SAASnqB,OAAOC,KAAKY,KAAKspB,UAAU,GACxD,CACA,WAAA9M,CAAYpT,EAAOiT,EAAWC,GAC1B,IAAI7b,EAAQ,IAAIomB,EAAM7mB,KAAMoJ,EAAOiT,EAAWC,GAC9C,IAAK,IAAIoO,KAAK1qB,KAAKkpB,SACfzoB,EAAQiqB,EAAEjqB,EAAO2I,EAAOiT,EAAWC,GACvC,OAAO7b,CACX,CAIA,OAAAqd,CAAQf,EAAOyB,EAAMmM,GAAQ,GACzB,IAAIC,EAAQ5qB,KAAK0hB,KACjB,GAAIlD,GAAQoM,EAAM,GACd,OAAQ,EACZ,IAAK,IAAI5pB,EAAM4pB,EAAMpM,EAAO,KAAM,CAC9B,IAAIqM,EAAWD,EAAM5pB,KAAQsV,EAAkB,EAAXuU,EAChCl0B,EAASi0B,EAAM5pB,KACnB,GAAIsV,GAAQqU,EACR,OAAOh0B,EACX,IAAK,IAAIke,EAAM7T,GAAO6pB,GAAY,GAAI7pB,EAAM6T,EAAK7T,IAC7C,GAAI4pB,EAAM5pB,IAAQ+b,EACd,OAAOpmB,EACf,GAAI2f,EACA,OAAQ,CAChB,CACJ,CAIA,SAAAyJ,CAAUhD,EAAO+N,GACb,IAAIvrB,EAAOS,KAAKT,KAChB,IAAK,IAAI9C,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAA2FwY,EAAvFjN,EAAIhI,KAAK8f,UAAU/C,EAAOtgB,EAAM,EAA0B,IAAoCuL,GAAK,EAAG,CAC3G,GAAwB,QAAnBiN,EAAO1V,EAAKyI,IAA4B,CACzC,GAAmB,GAAfzI,EAAKyI,EAAI,GAER,IAAmB,GAAfzI,EAAKyI,EAAI,GACd,OAAO2O,GAAKpX,EAAMyI,EAAI,GAEtB,KAAK,CAJLiN,EAAO1V,EAAKyI,EAAI2O,GAAKpX,EAAMyI,EAAI,GAKvC,CACA,GAAIiN,GAAQ6V,GAAoB,GAAR7V,EACpB,OAAO0B,GAAKpX,EAAMyI,EAAI,EAC9B,CAEJ,OAAO,CACX,CAIA,SAAA8X,CAAU/C,EAAOgO,GACb,OAAO/qB,KAAKiqB,OAAgB,EAARlN,EAAmCgO,EAC3D,CAIA,SAAAzM,CAAUvB,EAAOiO,GACb,OAAQhrB,KAAK8f,UAAU/C,EAAO,GAA4BiO,GAAQ,CACtE,CAIA,WAAA3K,CAAYtD,EAAOU,GACf,QAASzd,KAAK0gB,WAAW3D,EAAOxkB,GAAKA,GAAKklB,GAAgB,KAC9D,CAIA,UAAAiD,CAAW3D,EAAOU,GACd,IAAIwN,EAAQjrB,KAAK8f,UAAU/C,EAAO,GAC9B/N,EAASic,EAAQxN,EAAOwN,QAASrqB,EACrC,IAAK,IAAIoH,EAAIhI,KAAK8f,UAAU/C,EAAO,GAAuC,MAAV/N,EAAgBhH,GAAK,EAAG,CACpF,GAAoB,OAAhBhI,KAAKT,KAAKyI,GAA2B,CACrC,GAAwB,GAApBhI,KAAKT,KAAKyI,EAAI,GAGd,MAFAA,EAAI2O,GAAK3W,KAAKT,KAAMyI,EAAI,EAGhC,CACAgH,EAASyO,EAAO9G,GAAK3W,KAAKT,KAAMyI,EAAI,GACxC,CACA,OAAOgH,CACX,CAKA,UAAAiR,CAAWlD,GACP,IAAI/N,EAAS,GACb,IAAK,IAAIhH,EAAIhI,KAAK8f,UAAU/C,EAAO,IAA8B/U,GAAK,EAAG,CACrE,GAAoB,OAAhBhI,KAAKT,KAAKyI,GAA2B,CACrC,GAAwB,GAApBhI,KAAKT,KAAKyI,EAAI,GAGd,MAFAA,EAAI2O,GAAK3W,KAAKT,KAAMyI,EAAI,EAGhC,CACA,KAAwB,EAAnBhI,KAAKT,KAAKyI,EAAI,IAAkD,CACjE,IAAI/P,EAAQ+H,KAAKT,KAAKyI,EAAI,GACrBgH,EAAOoM,KAAK,CAAC+E,EAAGnY,IAAW,EAAJA,GAAUmY,GAAKloB,IACvC+W,EAAOnU,KAAKmF,KAAKT,KAAKyI,GAAI/P,EAClC,CACJ,CACA,OAAO+W,CACX,CAMA,SAAAzY,CAAUmY,GAGN,IAAIuJ,EAAO9Y,OAAOkS,OAAOlS,OAAO2Q,OAAOmZ,GAASlF,WAAY/jB,MAG5D,GAFI0O,EAAOzZ,QACPgjB,EAAK/D,QAAUlU,KAAKkU,QAAQjD,UAAUvC,EAAOzZ,QAC7CyZ,EAAOwB,IAAK,CACZ,IAAIzR,EAAOuB,KAAKspB,SAAS5a,EAAOwB,KAChC,IAAKzR,EACD,MAAM,IAAIqQ,WAAW,yBAAyBJ,EAAOwB,OACzD+H,EAAK/H,IAAMzR,CACf,CA2BA,OA1BIiQ,EAAOuX,aACPhO,EAAKgO,WAAajmB,KAAKimB,WAAWpiB,IAAIqnB,IAClC,IAAIra,EAAQnC,EAAOuX,WAAWvvB,KAAKgiB,GAAKA,EAAE3f,MAAQmyB,GAClD,OAAOra,EAAQA,EAAM1X,GAAK+xB,KAE9Bxc,EAAOiY,eACP1O,EAAK0O,aAAe3mB,KAAK2mB,aAAarzB,QACtC2kB,EAAK8R,iBAAmB/pB,KAAK+pB,iBAAiBlmB,IAAI,CAAC6D,EAAGM,KAClD,IAAI6I,EAAQnC,EAAOiY,aAAajwB,KAAKgiB,GAAKA,EAAE3f,MAAQ2O,EAAEyjB,UACtD,IAAKta,EACD,OAAOnJ,EACX,IAAIuI,EAAO9Q,OAAOkS,OAAOlS,OAAOkS,OAAO,CAAC,EAAG3J,GAAI,CAAEyjB,SAAUta,EAAM1X,KAEjE,OADA8e,EAAK0O,aAAa3e,GAAKgiB,GAAe/Z,GAC/BA,KAGXvB,EAAO0c,iBACPnT,EAAKzhB,QAAUkY,EAAO0c,gBACtB1c,EAAOyS,UACPlJ,EAAKkJ,QAAUnhB,KAAKyqB,aAAa/b,EAAOyS,UACvB,MAAjBzS,EAAO+S,SACPxJ,EAAKwJ,OAAS/S,EAAO+S,QACrB/S,EAAO2c,OACPpT,EAAKiR,SAAWjR,EAAKiR,SAAStS,OAAOlI,EAAO2c,OACrB,MAAvB3c,EAAO0Y,eACPnP,EAAKmP,aAAe1Y,EAAO0Y,cACxBnP,CACX,CAKA,WAAAqT,GACI,OAAOtrB,KAAKkpB,SAASvvB,OAAS,CAClC,CAOA,OAAA4xB,CAAQ/M,GACJ,OAAOxe,KAAKwqB,UAAYxqB,KAAKwqB,UAAUhM,GAAQ5a,OAAO4a,GAAQxe,KAAK6e,SAAW7e,KAAKkU,QAAQlD,MAAMwN,GAAMrX,MAAQqX,EACnH,CAKA,WAAIgI,GAAY,OAAOxmB,KAAK6e,QAAU,CAAG,CAIzC,WAAI5M,GAAY,OAAOjS,KAAKkU,QAAQlD,MAAMhR,KAAKkQ,IAAI,GAAK,CAIxD,iBAAA2N,CAAkBW,GACd,IAAIgN,EAAOxrB,KAAKsqB,mBAChB,OAAe,MAARkB,EAAe,EAAIA,EAAKhN,IAAS,CAC5C,CAIA,YAAAiM,CAAatJ,GACT,IAAI/Z,EAASjI,OAAOC,KAAKY,KAAKqqB,UAAWta,EAAQ3I,EAAOvD,IAAI,KAAM,GAClE,GAAIsd,EACA,IAAK,IAAIsK,KAAQtK,EAAQjpB,MAAM,KAAM,CACjC,IAAIxD,EAAK0S,EAAOsJ,QAAQ+a,GACpB/2B,GAAM,IACNqb,EAAMrb,IAAM,EACpB,CACJ,IAAIY,EAAW,KACf,IAAK,IAAI0S,EAAI,EAAGA,EAAIZ,EAAOzN,OAAQqO,IAC/B,IAAK+H,EAAM/H,GACP,IAAK,IAAkCtT,EAA9B2hB,EAAIrW,KAAKqqB,SAASjjB,EAAOY,IAAkC,QAAxBtT,EAAKsL,KAAKT,KAAK8W,QACtD/gB,IAAaA,EAAW,IAAIo2B,WAAW1rB,KAAKoqB,QAAU,KAAK11B,GAAM,EAE9E,OAAO,IAAIs0B,EAAQ7H,EAASpR,EAAOza,EACvC,CAKA,kBAAOsZ,CAAYqB,GACf,OAAO,IAAIgZ,GAAShZ,EACxB,EAEJ,SAAS0G,GAAKpX,EAAMigB,GAAO,OAAOjgB,EAAKigB,GAAQjgB,EAAKigB,EAAM,IAAM,EAAK,CAYrE,SAASwK,GAAe/Z,GACpB,GAAIA,EAAKkb,SAAU,CACf,IAAI9I,EAAOpS,EAAKgB,OAAS,EAA4B,EACrD,MAAO,CAAChZ,EAAOoiB,IAAWpK,EAAKkb,SAASlzB,EAAOoiB,IAAU,EAAKgI,CAClE,CACA,OAAOpS,EAAKL,GAChB,CCr1DA,MAoCM+b,GAAgB,CACpBC,KArCa,EAsCbC,OArCW,EAsCXC,OArCW,EAsCX9wB,QArCY,EAsCZ+wB,OArCW,EAsCXC,aApCgB,EAqChBC,YApCe,EAqCfC,cApCiB,EAqCjBC,OApCW,GAqCXtQ,OApCW,GAqCXuQ,KApCS,GAqCTC,GApCO,GAqCPC,SApCa,GAqCbC,WApCc,GAqCdC,YApCe,GAqCfC,OA/CW,EAgDXC,WArCe,GAsCfC,KArCS,GAsCTC,KArCS,IA4CLC,GAA0B,CAC9BC,GA5CO,GA6CPC,QA5CY,GA6CZC,IA5CQ,GA6CRC,GA5CO,GA6CPC,OA5CW,GA6CXC,IA5CQ,GA6CRC,IA5CQ,GA6CR/O,MA5CU,GA6CVjG,IA5CQ,GA6CRuL,IA5CQ,GA6CR0J,OA5CW,GA6CXC,OA5CW,GA6CXC,QA5CY,GA6CZC,KA5CS,GA6CTl1B,KA5CS,GA6CTm1B,UA5Cc,IAoDVC,GAAkB,CAACC,UAAU,KAAKC,GAAG,IAAKC,gBAAgB,IAAKC,KAAK,IAAKC,aAAa,IAAKC,gBAAgB,IAAKC,WAAW,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,iBAAiB,IAAKC,iBAAiB,IAAKC,mBAAmB,IAAKC,gBAAgB,IAAKC,eAAe,IAAKC,iBAAiB,IAAKC,MAAM,IAAKC,SAAS,IAAKC,iBAAiB,KACzXz0B,GAAS4uB,GAASra,YAAY,CAClC1Q,QAAS,GACT+rB,OAAQ,ygGACRE,UAAW,+9KACXzI,KAAM,wvCACNyH,UAAW,k4CACXiB,QAAS,IACTT,aAAc,CAAC,EAAE,IACjBP,gBAAiB,EACjBU,UAAW,4tEACX7D,WAAY,CAAC,EAAG,GAChBqD,SAAU,CAAC,MAAQ,CAAC,EAAE,KACtB5C,YAAa,CAAC,CAAClI,KAAM,GAAI5O,IAAK,CAAC3X,EAAOoiB,IAzCX,CAACpiB,GACrB0zB,GAAc1zB,EAAMuR,iBAAmB,EAwCGulB,CAAqB92B,IAAU,GAAI,CAACumB,KAAM,GAAI5O,IAAK,CAAC3X,EAAOoiB,IAlBrF,CAACpiB,GACjB40B,GAAwB50B,EAAMuR,iBAAmB,EAiB+DwlB,CAAiB/2B,IAAU,EAAK,GAAG,CAACumB,KAAM,GAAI5O,IAAK3X,GAASy1B,GAAgBz1B,KAAW,IAC9MsyB,UAAW,IAGP1kB,GAAO,EACXF,GAAS,EAsCTrC,GAAW,GAEXE,GAAU,GACVE,GAAa,GACbb,GAAK,GACL,GAAS,GACTE,GAAM,GACNE,GAAK,GACLE,GAAM,GAINa,GAAa,GAEbG,GAAY,GACZE,GAAY,GACZI,GAAc,GACdE,GAAM,GACN9G,GAAW,GAEXkJ,GAAW,GAUXzB,GAAc,GAId5C,GAAM,GACN2D,GAAW,GACX7D,GAAM,GACNF,GAAM,GACNH,GAAM,GAGNgE,GAAQ,GAGR,GAAS,GAcT8oB,GAAa,E,sEC1MXC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBxuB,IAAjByuB,EACH,OAAOA,EAAa5hB,QAGrB,IAAID,EAAS0hB,EAAyBE,GAAY,CACjD16B,GAAI06B,EACJE,QAAQ,EACR7hB,QAAS,CAAC,GAUX,OANA8hB,EAAoBH,GAAUI,KAAKhiB,EAAOC,QAASD,EAAQA,EAAOC,QAAS0hB,GAG3E3hB,EAAO8hB,QAAS,EAGT9hB,EAAOC,OACf,C,OAGA0hB,EAAoBM,EAAIF,EC3BxBJ,EAAoB3U,EAAKhN,IACxB,IAAIkiB,EAASliB,GAAUA,EAAOmiB,WAC7B,IAAOniB,EAAiB,QACxB,IAAM,EAEP,OADA2hB,EAAoBrU,EAAE4U,EAAQ,CAAEn3B,EAAGm3B,IAC5BA,GpCNJv8B,EAAWgM,OAAOywB,eAAkBjoB,GAASxI,OAAOywB,eAAejoB,GAASA,GAASA,EAAa,UAQtGwnB,EAAoBjE,EAAI,SAASjzB,EAAO8Z,GAEvC,GADU,EAAPA,IAAU9Z,EAAQ+H,KAAK/H,IAChB,EAAP8Z,EAAU,OAAO9Z,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAP8Z,GAAa9Z,EAAM03B,WAAY,OAAO13B,EAC1C,GAAW,GAAP8Z,GAAoC,mBAAf9Z,EAAM43B,KAAqB,OAAO53B,CAC5D,CACA,IAAI63B,EAAK3wB,OAAO2Q,OAAO,MACvBqf,EAAoBzW,EAAEoX,GACtB,IAAIC,EAAM,CAAC,EACX78B,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAI68B,EAAiB,EAAPje,GAAY9Z,EAAyB,iBAAX+3B,KAAyB98B,EAAewd,QAAQsf,GAAUA,EAAU78B,EAAS68B,GACxH7wB,OAAO8wB,oBAAoBD,GAAS3wB,QAASjH,GAAS23B,EAAI33B,GAAO,IAAOH,EAAMG,IAI/E,OAFA23B,EAAa,QAAI,IAAM,EACvBZ,EAAoBrU,EAAEgV,EAAIC,GACnBD,CACR,EqCxBAX,EAAoBrU,EAAI,CAACrN,EAASyiB,KACjC,IAAI,IAAI93B,KAAO83B,EACXf,EAAoB7nB,EAAE4oB,EAAY93B,KAAS+2B,EAAoB7nB,EAAEmG,EAASrV,IAC5E+G,OAAOgxB,eAAe1iB,EAASrV,EAAK,CAAEg4B,YAAY,EAAMxgB,IAAKsgB,EAAW93B,MCJ3E+2B,EAAoBkB,EAAI,CAAC,EAGzBlB,EAAoBpwB,EAAKuxB,GACjBr8B,QAAQC,IAAIiL,OAAOC,KAAK+vB,EAAoBkB,GAAG7S,OAAO,CAAC+S,EAAUn4B,KACvE+2B,EAAoBkB,EAAEj4B,GAAKk4B,EAASC,GAC7BA,GACL,KCNJpB,EAAoBqB,EAAKF,GAEZA,EAAU,cAAgB,CAAC,GAAK,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,wBAAwBA,GCH3dnB,EAAoBsB,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO1wB,MAAQ,IAAI2wB,SAAS,cAAb,EAChB,CAAE,MAAO5xB,GACR,GAAsB,iBAAX6xB,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBzB,EAAoB7nB,EAAI,CAACK,EAAKH,IAAUrI,OAAO4kB,UAAU8M,eAAerB,KAAK7nB,EAAKH,GxCA9EpU,EAAa,CAAC,EACdC,EAAoB,2BAExB87B,EAAoB2B,EAAI,CAACC,EAAKtU,EAAMrkB,EAAKk4B,KACxC,GAAGl9B,EAAW29B,GAAQ39B,EAAW29B,GAAKl2B,KAAK4hB,OAA3C,CACA,IAAIuU,EAAQC,EACZ,QAAWrwB,IAARxI,EAEF,IADA,IAAI84B,EAAUC,SAASC,qBAAqB,UACpCppB,EAAI,EAAGA,EAAIkpB,EAAQv3B,OAAQqO,IAAK,CACvC,IAAIN,EAAIwpB,EAAQlpB,GAChB,GAAGN,EAAE2pB,aAAa,QAAUN,GAAOrpB,EAAE2pB,aAAa,iBAAmBh+B,EAAoB+E,EAAK,CAAE44B,EAAStpB,EAAG,KAAO,CACpH,CAEGspB,IACHC,GAAa,GACbD,EAASG,SAASG,cAAc,WAEzBC,QAAU,QACjBP,EAAOQ,QAAU,IACbrC,EAAoBsC,IACvBT,EAAOU,aAAa,QAASvC,EAAoBsC,IAElDT,EAAOU,aAAa,eAAgBr+B,EAAoB+E,GAExD44B,EAAO5gB,IAAM2gB,EAC4C,IAArDC,EAAO5gB,IAAIM,QAAQkgB,OAAOl0B,SAASi1B,OAAS,OAC/CX,EAAOY,YAAc,aAEtBZ,EAAOa,UAAY1C,EAAoB2C,UAAUxB,GACjDU,EAAOY,YAAc,aAEtBx+B,EAAW29B,GAAO,CAACtU,GACnB,IAAIsV,EAAmB,CAAC9W,EAAM+W,KAE7BhB,EAAOiB,QAAUjB,EAAOkB,OAAS,KACjCC,aAAaX,GACb,IAAIY,EAAUh/B,EAAW29B,GAIzB,UAHO39B,EAAW29B,GAClBC,EAAOqB,YAAcrB,EAAOqB,WAAWC,YAAYtB,GACnDoB,GAAWA,EAAQ/yB,QAASkzB,GAAQA,EAAGP,IACpC/W,EAAM,OAAOA,EAAK+W,IAElBR,EAAUgB,WAAWT,EAAiBU,KAAK,UAAM7xB,EAAW,CAAE/J,KAAM,UAAWF,OAAQq6B,IAAW,MACtGA,EAAOiB,QAAUF,EAAiBU,KAAK,KAAMzB,EAAOiB,SACpDjB,EAAOkB,OAASH,EAAiBU,KAAK,KAAMzB,EAAOkB,QACnDjB,GAAcE,SAASuB,KAAKC,YAAY3B,EAzCkB,GyCH3D7B,EAAoBzW,EAAKjL,IACH,oBAAXzQ,QAA0BA,OAAO41B,aAC1CzzB,OAAOgxB,eAAe1iB,EAASzQ,OAAO41B,YAAa,CAAE36B,MAAO,WAE7DkH,OAAOgxB,eAAe1iB,EAAS,aAAc,CAAExV,OAAO,KCLvDk3B,EAAoB0D,IAAOrlB,IAC1BA,EAAOslB,MAAQ,GACVtlB,EAAOmE,WAAUnE,EAAOmE,SAAW,IACjCnE,GCHR2hB,EAAoBxkB,EAAI,0CCCxBwkB,EAAoB2C,UAAY,CAAC,GAAK,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,uD,MCD92B3C,EAAoBnX,EAAImZ,SAAS4B,SAAWC,KAAKt2B,SAASqN,KAK1D,IAAIkpB,EAAkB,CACrB,IAAK,GAGN9D,EAAoBkB,EAAEha,EAAI,CAACia,EAASC,KAElC,IAAI2C,EAAqB/D,EAAoB7nB,EAAE2rB,EAAiB3C,GAAW2C,EAAgB3C,QAAW1vB,EACtG,GAA0B,IAAvBsyB,EAGF,GAAGA,EACF3C,EAAS11B,KAAKq4B,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIl/B,QAAQ,CAACue,EAAS4gB,IAAYF,EAAqBD,EAAgB3C,GAAW,CAAC9d,EAAS4gB,IAC1G7C,EAAS11B,KAAKq4B,EAAmB,GAAKC,GAGtC,IAAIpC,EAAM5B,EAAoBxkB,EAAIwkB,EAAoBqB,EAAEF,GAEpDlyB,EAAQ,IAAIqB,MAgBhB0vB,EAAoB2B,EAAEC,EAfFiB,IACnB,GAAG7C,EAAoB7nB,EAAE2rB,EAAiB3C,KAEf,KAD1B4C,EAAqBD,EAAgB3C,MACR2C,EAAgB3C,QAAW1vB,GACrDsyB,GAAoB,CACtB,IAAIG,EAAYrB,IAAyB,SAAfA,EAAMn7B,KAAkB,UAAYm7B,EAAMn7B,MAChEy8B,EAAUtB,GAASA,EAAMr7B,QAAUq7B,EAAMr7B,OAAOyZ,IACpDhS,EAAMm1B,QAAU,iBAAmBjD,EAAU,cAAgB+C,EAAY,KAAOC,EAAU,IAC1Fl1B,EAAM+I,KAAO,iBACb/I,EAAMvH,KAAOw8B,EACbj1B,EAAMo1B,QAAUF,EAChBJ,EAAmB,GAAG90B,EACvB,GAGuC,SAAWkyB,EAASA,EAE/D,GAeH,IAAImD,EAAuB,CAACC,EAA4Bn0B,KACvD,IAGI6vB,EAAUkB,GAHTqD,EAAUC,EAAaC,GAAWt0B,EAGhByI,EAAI,EAC3B,GAAG2rB,EAASvY,KAAM1mB,GAAgC,IAAxBu+B,EAAgBv+B,IAAa,CACtD,IAAI06B,KAAYwE,EACZzE,EAAoB7nB,EAAEssB,EAAaxE,KACrCD,EAAoBM,EAAEL,GAAYwE,EAAYxE,IAGhD,GAAGyE,EAAsBA,EAAQ1E,EAClC,CAEA,IADGuE,GAA4BA,EAA2Bn0B,GACrDyI,EAAI2rB,EAASh6B,OAAQqO,IACzBsoB,EAAUqD,EAAS3rB,GAChBmnB,EAAoB7nB,EAAE2rB,EAAiB3C,IAAY2C,EAAgB3C,IACrE2C,EAAgB3C,GAAS,KAE1B2C,EAAgB3C,GAAW,GAKzBwD,EAAqBd,KAA0C,oCAAIA,KAA0C,qCAAK,GACtHc,EAAmBz0B,QAAQo0B,EAAqBhB,KAAK,KAAM,IAC3DqB,EAAmBj5B,KAAO44B,EAAqBhB,KAAK,KAAMqB,EAAmBj5B,KAAK43B,KAAKqB,G,KClF7D3E,EAAoB,K", "sources": ["webpack://grafana-lokiexplore-app/webpack/runtime/create fake namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/load script", "webpack://grafana-lokiexplore-app/./node_modules/grafana-public-path.js", "webpack://grafana-lokiexplore-app/./services/extensions/exposedComponents.tsx", "webpack://grafana-lokiexplore-app/./module.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/ServiceSceneConstants.ts", "webpack://grafana-lokiexplore-app/./services/enums.ts", "webpack://grafana-lokiexplore-app/./services/extensions/links.ts", "webpack://grafana-lokiexplore-app/./services/extensions/scenesMethods.ts", "webpack://grafana-lokiexplore-app/./services/fieldsTypes.ts", "webpack://grafana-lokiexplore-app/./services/filterTypes.ts", "webpack://grafana-lokiexplore-app/./services/logger.ts", "webpack://grafana-lokiexplore-app/./services/logqlMatchers.ts", "webpack://grafana-lokiexplore-app/./services/lokiQuery.ts", "webpack://grafana-lokiexplore-app/./services/narrowing.ts", "webpack://grafana-lokiexplore-app/./services/operatorHelpers.ts", "webpack://grafana-lokiexplore-app/./services/getOperatorDescription.ts", "webpack://grafana-lokiexplore-app/./services/operators.ts", "webpack://grafana-lokiexplore-app/./services/renderPatternFilters.ts", "webpack://grafana-lokiexplore-app/./services/variables.ts", "webpack://grafana-lokiexplore-app/external amd \"@emotion/css\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/data\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/runtime\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/ui\"", "webpack://grafana-lokiexplore-app/external amd \"lodash\"", "webpack://grafana-lokiexplore-app/external amd \"module\"", "webpack://grafana-lokiexplore-app/external amd \"react\"", "webpack://grafana-lokiexplore-app/external amd \"react-dom\"", "webpack://grafana-lokiexplore-app/external amd \"react-redux\"", "webpack://grafana-lokiexplore-app/external amd \"react-router\"", "webpack://grafana-lokiexplore-app/external amd \"redux\"", "webpack://grafana-lokiexplore-app/external amd \"rxjs\"", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/common/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/lr/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@grafana/lezer-logql/index.es.js", "webpack://grafana-lokiexplore-app/webpack/bootstrap", "webpack://grafana-lokiexplore-app/webpack/runtime/compat get default export", "webpack://grafana-lokiexplore-app/webpack/runtime/define property getters", "webpack://grafana-lokiexplore-app/webpack/runtime/ensure chunk", "webpack://grafana-lokiexplore-app/webpack/runtime/get javascript chunk filename", "webpack://grafana-lokiexplore-app/webpack/runtime/global", "webpack://grafana-lokiexplore-app/webpack/runtime/hasOwnProperty shorthand", "webpack://grafana-lokiexplore-app/webpack/runtime/make namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/node module decorator", "webpack://grafana-lokiexplore-app/webpack/runtime/publicPath", "webpack://grafana-lokiexplore-app/webpack/runtime/compat", "webpack://grafana-lokiexplore-app/webpack/runtime/jsonp chunk loading", "webpack://grafana-lokiexplore-app/webpack/startup"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; typeof current == 'object' && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"grafana-lokiexplore-app:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t\tif (script.src.indexOf(window.location.origin + '/') !== 0) {\n\t\t\tscript.crossOrigin = \"anonymous\";\n\t\t}\n\t\tscript.integrity = __webpack_require__.sriHashes[chunkId];\n\t\tscript.crossOrigin = \"anonymous\";\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "\nimport amdMetaModule from 'amd-module';\n\n__webpack_public_path__ =\n  amdMetaModule && amdMetaModule.uri\n    ? amdMetaModule.uri.slice(0, amdMetaModule.uri.lastIndexOf('/') + 1)\n    : 'public/plugins/grafana-lokiexplore-app/';\n", "import React, { lazy, Suspense } from 'react';\n\nimport { LinkButton } from '@grafana/ui';\n\nimport { EmbeddedLogsExplorationProps } from 'Components/EmbeddedLogsExploration/types';\nimport { OpenInLogsDrilldownButtonProps } from 'Components/OpenInLogsDrilldownButton/types';\nconst OpenInLogsDrilldownButton = lazy(() => import('Components/OpenInLogsDrilldownButton/OpenInLogsDrilldownButton'));\nconst EmbeddedLogsExploration = lazy(() => import('Components/EmbeddedLogsExploration/EmbeddedLogs'));\n\nexport function SuspendedOpenInLogsDrilldownButton(props: OpenInLogsDrilldownButtonProps) {\n  return (\n    <Suspense\n      fallback={\n        <LinkButton variant=\"secondary\" disabled>\n          Open in Logs Drilldown\n        </LinkButton>\n      }\n    >\n      <OpenInLogsDrilldownButton {...props} />\n    </Suspense>\n  );\n}\n\nexport function SuspendedEmbeddedLogsExploration(props: EmbeddedLogsExplorationProps) {\n  return (\n    <Suspense fallback={<div>Loading Logs Drilldown...</div>}>\n      <EmbeddedLogsExploration {...props} />\n    </Suspense>\n  );\n}\n", "import { lazy } from 'react';\n\nimport { AppPlugin } from '@grafana/data';\n\nimport {\n  SuspendedEmbeddedLogsExploration,\n  SuspendedOpenInLogsDrilldownButton,\n} from 'services/extensions/exposedComponents';\nimport { linkConfigs } from 'services/extensions/links';\n\n// Anything imported in this file is included in the main bundle which is pre-loaded in Grafana\n// Don't add imports to this file without lazy loading\n// Link extensions are the exception as they must be included in the main bundle in order to work in core Grafana\nconst App = lazy(async () => {\n  const { wasmSupported } = await import('services/sorting');\n\n  const { default: initRuntimeDs } = await import('services/datasource');\n  const { default: initChangepoint } = await import('@bsull/augurs/changepoint');\n  const { default: initOutlier } = await import('@bsull/augurs/outlier');\n\n  initRuntimeDs();\n\n  if (wasmSupported()) {\n    await Promise.all([initChangepoint(), initOutlier()]);\n  }\n\n  return import('Components/App');\n});\n\nconst AppConfig = lazy(async () => {\n  return await import('./Components/AppConfig/AppConfig');\n});\n\nexport const plugin = new AppPlugin<{}>().setRootPage(App).addConfigPage({\n  body: AppConfig,\n  icon: 'cog',\n  id: 'configuration',\n  title: 'Configuration',\n});\n\nfor (const linkConfig of linkConfigs) {\n  plugin.addLink(linkConfig);\n}\n\nplugin.exposeComponent({\n  component: SuspendedOpenInLogsDrilldownButton,\n  description: 'A button that opens a logs view in the Logs Drilldown app.',\n  id: `grafana-lokiexplore-app/open-in-explore-logs-button/v1`,\n  title: 'Open in Logs Drilldown button',\n});\n\nplugin.exposeComponent({\n  component: SuspendedEmbeddedLogsExploration,\n  description: 'A component that renders a logs exploration view that can be embedded in other parts of Grafana.',\n  id: `grafana-lokiexplore-app/embedded-logs-exploration/v1`,\n  title: 'Embedded Logs Exploration',\n});\n", "export const pageSlugUrlKey = 'pageSlug';\nexport const drilldownLabelUrlKey = 'drillDownLabel';\n", "// Circular dependencies can cause enums to return as undefined in jest tests, moving enums here\nexport enum TabNames {\n  logs = 'Logs',\n  labels = 'Labels',\n  fields = 'Fields',\n  patterns = 'Patterns',\n}\n\nexport enum PageSlugs {\n  explore = 'explore',\n  logs = 'logs',\n  labels = 'labels',\n  patterns = 'patterns',\n  fields = 'fields',\n  embed = 'embed',\n}\n\nexport enum ValueSlugs {\n  field = 'field',\n  label = 'label',\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { map as lodashMap } from 'lodash';\n\nimport {\n  CustomVariableModel,\n  PluginExtensionAddedLinkConfig,\n  PluginExtensionPanelContext,\n  PluginExtensionPoints,\n  QueryVariableModel,\n} from '@grafana/data';\nimport { getTemplateSrv, locationService } from '@grafana/runtime';\n\nimport pluginJson from '../../plugin.json';\nimport { LabelType } from '../fieldsTypes';\nimport { FieldFilter, IndexedLabelFilter, LineFilterType, PatternFilterOp, PatternFilterType } from '../filterTypes';\nimport { getMatcherFromQuery } from '../logqlMatchers';\nimport { LokiQuery } from '../lokiQuery';\nimport { isOperatorInclusive } from '../operatorHelpers';\nimport { renderPatternFilters } from '../renderPatternFilters';\nimport { escapeLabelValueInExactSelector, lokiSpecialRegexEscape } from './scenesMethods';\nimport {\n  addAdHocFilterUserInputPrefix,\n  AdHocFieldValue,\n  AppliedPattern,\n  EMPTY_VARIABLE_VALUE,\n  LEVEL_VARIABLE_VALUE,\n  SERVICE_NAME,\n  stripAdHocFilterUserInputPrefix,\n  VAR_DATASOURCE,\n  VAR_FIELDS,\n  VAR_LABELS,\n  VAR_LEVELS,\n  VAR_LINE_FILTERS,\n  VAR_METADATA,\n  VAR_PATTERNS,\n} from 'services/variables';\n\nconst PRODUCT_NAME = 'Grafana Logs Drilldown';\nconst title = `Open in ${PRODUCT_NAME}`;\nconst description = `Open current query in the ${PRODUCT_NAME} view`;\nconst icon = 'gf-logs';\n\nexport const ExtensionPoints = {\n  MetricInvestigation: 'grafana-lokiexplore-app/investigation/v1',\n} as const;\n\nexport type LinkConfigs = Array<PluginExtensionAddedLinkConfig<PluginExtensionPanelContext>>;\n\nexport const linkConfigs: LinkConfigs = [\n  {\n    targets: [\n      PluginExtensionPoints.DashboardPanelMenu,\n      PluginExtensionPoints.ExploreToolbarAction,\n      'grafana-metricsdrilldown-app/open-in-logs-drilldown/v1',\n    ],\n    title,\n    description,\n    icon,\n    path: createAppUrl(),\n    configure: contextToLink,\n  },\n];\n\nfunction stringifyValues(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n  return value;\n}\n\n// Why are there twice as many escape chars in the url as expected?\nexport function replaceEscapeChars(value?: string): string | undefined {\n  return value?.replace(/\\\\\\\\/g, '\\\\');\n}\n\nexport function stringifyAdHocValues(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n\n  // All label values from explore are already escaped, so we mark them as custom values to prevent them from getting escaped again when rendering the LogQL\n  return addAdHocFilterUserInputPrefix(replaceEscapeChars(value));\n}\n\nexport function stringifyAdHocValueLabels(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n\n  return escapeURLDelimiters(replaceEscapeChars(value));\n}\n\nfunction setUrlParamsFromFieldFilters(fields: FieldFilter[], params: URLSearchParams) {\n  for (const field of fields) {\n    if (field.type === LabelType.StructuredMetadata) {\n      if (field.key === LEVEL_VARIABLE_VALUE) {\n        params = appendUrlParameter(\n          UrlParameters.Levels,\n          `${field.key}|${field.operator}|${escapeURLDelimiters(stringifyValues(field.value))}`,\n          params\n        );\n      } else {\n        params = appendUrlParameter(\n          UrlParameters.Metadata,\n          `${field.key}|${field.operator}|${escapeURLDelimiters(\n            stringifyAdHocValues(field.value)\n          )},${escapeURLDelimiters(replaceEscapeChars(field.value))}`,\n          params\n        );\n      }\n    } else {\n      const fieldValue: AdHocFieldValue = {\n        value: field.value,\n        parser: field.parser,\n      };\n\n      const adHocFilterURLString = `${field.key}|${field.operator}|${escapeURLDelimiters(\n        stringifyAdHocValues(JSON.stringify(fieldValue))\n      )},${stringifyAdHocValueLabels(fieldValue.value)}`;\n\n      params = appendUrlParameter(UrlParameters.Fields, adHocFilterURLString, params);\n    }\n  }\n  return params;\n}\n\nfunction setUrlParamsFromLabelFilters(labelFilters: IndexedLabelFilter[], params: URLSearchParams) {\n  for (const labelFilter of labelFilters) {\n    // skip non-indexed filters for now\n    if (labelFilter.type !== LabelType.Indexed) {\n      continue;\n    }\n\n    const labelsAdHocFilterURLString = `${labelFilter.key}|${labelFilter.operator}|${escapeURLDelimiters(\n      stringifyAdHocValues(labelFilter.value)\n    )},${escapeURLDelimiters(replaceEscapeChars(labelFilter.value))}`;\n\n    params = appendUrlParameter(UrlParameters.Labels, labelsAdHocFilterURLString, params);\n  }\n  return params;\n}\n\nfunction setLineFilterUrlParams(lineFilters: LineFilterType[], params: URLSearchParams) {\n  for (const lineFilter of lineFilters) {\n    params = appendUrlParameter(\n      UrlParameters.LineFilters,\n      `${lineFilter.key}|${escapeURLDelimiters(lineFilter.operator)}|${escapeURLDelimiters(\n        stringifyValues(lineFilter.value)\n      )}`,\n      params\n    );\n  }\n  return params;\n}\n\nexport function setUrlParamsFromPatterns(patternFilters: PatternFilterType[], params: URLSearchParams) {\n  const patterns: AppliedPattern[] = [];\n\n  for (const field of patternFilters) {\n    patterns.push({\n      type: field.operator === PatternFilterOp.match ? 'include' : 'exclude',\n      pattern: stringifyValues(field.value),\n    });\n  }\n\n  let patternsString = renderPatternFilters(patterns);\n\n  params = appendUrlParameter(UrlParameters.Patterns, JSON.stringify(patterns), params);\n  return appendUrlParameter(UrlParameters.PatternsVariable, patternsString, params);\n}\n\nfunction contextToLink<T extends PluginExtensionPanelContext>(context?: T) {\n  if (!context) {\n    return undefined;\n  }\n  const lokiQuery = context.targets.find((target) => target.datasource?.type === 'loki') as LokiQuery | undefined;\n  const templateSrv = getTemplateSrv();\n  const dataSourceUid = templateSrv.replace(lokiQuery?.datasource?.uid, context.scopedVars);\n\n  if (!lokiQuery || !dataSourceUid) {\n    return undefined;\n  }\n\n  const expr = templateSrv.replace(lokiQuery.expr, context.scopedVars, interpolateQueryExpr);\n  const { fields, labelFilters, lineFilters, patternFilters } = getMatcherFromQuery(expr, context, lokiQuery);\n  const labelSelector = labelFilters.find((selector) => isOperatorInclusive(selector.operator));\n\n  // Require at least one inclusive operator to run a valid Loki query\n  if (!labelSelector) {\n    return undefined;\n  }\n\n  // If there are a bunch of values for the same field, the value slug can get really long, let's just use the first one in the URL\n  const urlLabelValue = labelSelector.value.split('|')[0];\n  const labelValue = replaceSlash(urlLabelValue);\n  let labelName = labelSelector.key === SERVICE_NAME ? 'service' : labelSelector.key;\n  // sort `primary label` first\n  labelFilters.sort((a) => (a.key === labelName ? -1 : 1));\n\n  let params = setUrlParameter(UrlParameters.DatasourceId, dataSourceUid, new URLSearchParams());\n  params = setUrlParameter(UrlParameters.TimeRangeFrom, context.timeRange.from.valueOf().toString(), params);\n  params = setUrlParameter(UrlParameters.TimeRangeTo, context.timeRange.to.valueOf().toString(), params);\n  params = setUrlParamsFromLabelFilters(labelFilters, params);\n\n  if (lineFilters) {\n    params = setLineFilterUrlParams(lineFilters, params);\n  }\n  if (fields?.length) {\n    params = setUrlParamsFromFieldFilters(fields, params);\n  }\n  if (patternFilters?.length) {\n    params = setUrlParamsFromPatterns(patternFilters, params);\n  }\n\n  return {\n    path: createAppUrl(`/explore/${labelName}/${labelValue}/logs`, params),\n  };\n}\n\nexport function createAppUrl(path = '/explore', urlParams?: URLSearchParams): string {\n  return `/a/${pluginJson.id}${path}${urlParams ? `?${urlParams.toString()}` : ''}`;\n}\n\nexport const UrlParameters = {\n  DatasourceId: `var-${VAR_DATASOURCE}`,\n  TimeRangeFrom: 'from',\n  TimeRangeTo: 'to',\n  Labels: `var-${VAR_LABELS}`,\n  Fields: `var-${VAR_FIELDS}`,\n  Metadata: `var-${VAR_METADATA}`,\n  Levels: `var-${VAR_LEVELS}`,\n  LineFilters: `var-${VAR_LINE_FILTERS}`,\n  Patterns: VAR_PATTERNS,\n  PatternsVariable: `var-${VAR_PATTERNS}`,\n} as const;\nexport type UrlParameterType = (typeof UrlParameters)[keyof typeof UrlParameters];\n\nexport function setUrlParameter(key: UrlParameterType, value: string, initalParams?: URLSearchParams): URLSearchParams {\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? locationService.getSearch());\n  searchParams.set(key, value);\n\n  return searchParams;\n}\n\nexport function appendUrlParameter(\n  key: UrlParameterType,\n  value: string,\n  initalParams?: URLSearchParams\n): URLSearchParams {\n  const location = locationService.getLocation();\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? location.search);\n  searchParams.append(key, value);\n\n  return searchParams;\n}\n\nexport function replaceSlash(parameter: string): string {\n  return (\n    stripAdHocFilterUserInputPrefix(parameter)\n      // back-slash is converted to forward-slash in the URL, replace that char\n      .replace(/\\//g, '-')\n      .replace(/\\\\/g, '-')\n  );\n}\n\n// Manually copied over from @grafana/scenes so we don't need to import scenes to build links\nfunction escapeUrlCommaDelimiters(value: string | undefined): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  // Replace the comma due to using it as a value/label separator\n  return /,/g[Symbol.replace](value, '__gfc__');\n}\n\nexport function escapeUrlPipeDelimiters(value: string | undefined): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  // Replace the pipe due to using it as a filter separator\n  return (value = /\\|/g[Symbol.replace](value, '__gfp__'));\n}\n\nexport function escapeURLDelimiters(value: string | undefined): string {\n  return escapeUrlCommaDelimiters(escapeUrlPipeDelimiters(value));\n}\n\n// Copied from interpolateQueryExpr in loki datasource, as we can't return a promise in the link extension config we can't fetch the datasource from the datasource srv, so we're forced to duplicate this method\nexport function interpolateQueryExpr(value: string | unknown[], variable: QueryVariableModel | CustomVariableModel) {\n  // if no multi or include all do not regexEscape\n  if (!variable.multi && !variable.includeAll) {\n    return value;\n  }\n\n  if (typeof value === 'string') {\n    return escapeLabelValueInExactSelector(value);\n  }\n\n  const escapedValues = lodashMap(value, lokiSpecialRegexEscape);\n  return escapedValues.join('|');\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\n/**\n * Methods copied from scenes that we want in the module (to generate links which cannot be lazy loaded), without including all of scenes.\n * See https://github.com/grafana/scenes/issues/1046\n */\n// based on the openmetrics-documentation, the 3 symbols we have to handle are:\n// - \\n ... the newline character\n// - \\  ... the backslash character\n// - \"  ... the double-quote character\nexport function escapeLabelValueInExactSelector(labelValue: string): string {\n  return labelValue.replace(/\\\\/g, '\\\\\\\\').replace(/\\n/g, '\\\\n').replace(/\"/g, '\\\\\"');\n}\n\n// Pulled from loki datasource\nexport function lokiSpecialRegexEscape<T>(value: T) {\n  if (typeof value === 'string') {\n    return value.replace(/\\\\/g, '\\\\\\\\\\\\\\\\').replace(/[$^*{}\\[\\]+?.()|]/g, '\\\\\\\\$&');\n  }\n  return value;\n}\n", "// copied from public/app/plugins/datasource/loki/types.ts\nexport enum LabelType {\n  Indexed = 'I',\n  StructuredMetadata = 'S',\n  Parsed = 'P',\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport { LabelType } from './fieldsTypes';\nimport { ParserType } from './variables';\n\nexport type FilterOpType = LabelFilterOp | NumericFilterOp;\nexport enum LabelFilterOp {\n  Equal = '=',\n  NotEqual = '!=',\n  RegexEqual = '=~',\n  RegexNotEqual = '!~',\n}\n// Line filter doesn't have an operator, so we add an empty space to keep it in URL state\nexport enum LineFormatFilterOp {\n  Empty = ' ',\n}\n\nexport enum NumericFilterOp {\n  gt = '>',\n  lt = '<',\n  gte = '>=',\n  lte = '<=',\n}\nexport const FilterOp = { ...LabelFilterOp, ...NumericFilterOp };\n\nexport type IndexedLabelFilter = {\n  key: string;\n  operator: FilterOpType;\n  type?: LabelType;\n  value: string;\n};\n\nexport type FieldFilter = {\n  key: string;\n  operator: FilterOpType;\n  parser?: ParserType;\n  type?: LabelType;\n  value: string;\n};\n\nexport type LineFilterType = {\n  key: string;\n  operator: LineFilterOp;\n  value: string;\n};\n\nexport type PatternFilterType = {\n  operator: PatternFilterOp;\n  value: string;\n};\n\nexport enum LineFilterOp {\n  match = '|=',\n  negativeMatch = `!=`,\n  regex = '|~',\n  negativeRegex = `!~`,\n}\n\nexport enum PatternFilterOp {\n  match = '|>',\n  negativeMatch = '!>',\n}\n\nexport enum LineFilterCaseSensitive {\n  caseSensitive = 'caseSensitive',\n  caseInsensitive = 'caseInsensitive',\n}\n", "import { LogContext } from '@grafana/faro-web-sdk';\nimport { FetchError, logError, logInfo, logWarning } from '@grafana/runtime';\n\nimport packageJson from '../../package.json';\nimport pluginJson from '../plugin.json';\nimport { isRecord } from './narrowing';\n\nconst defaultContext = {\n  app: pluginJson.id,\n  version: packageJson.version,\n};\n\nexport const logger = {\n  error: (err: Error | unknown, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.error(err, ctx);\n    attemptFaroErr(err, ctx);\n  },\n  info: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.log(msg, ctx);\n    attemptFaroInfo(msg, ctx);\n  },\n  warn: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.warn(msg, ctx);\n    attemptFaroWarn(msg, ctx);\n  },\n};\n\nconst attemptFaroInfo = (msg: string, context?: LogContext) => {\n  try {\n    logInfo(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro event!');\n  }\n};\n\nconst attemptFaroWarn = (msg: string, context?: LogContext) => {\n  try {\n    logWarning(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro warning!', { context, msg });\n  }\n};\n/**\n * Checks unknown error for properties from Records like FetchError and adds them to the context\n * @param err\n * @param context\n */\nfunction populateFetchErrorContext(err: unknown | FetchError, context: LogContext) {\n  if (typeof err === 'object' && err !== null) {\n    if (isRecord(err)) {\n      Object.keys(err).forEach((key: string) => {\n        const value = err[key];\n        if (typeof value === 'string' || typeof value === 'boolean' || typeof value === 'number') {\n          context[key] = value.toString();\n        }\n      });\n    }\n\n    if (hasData(err)) {\n      if (typeof err.data === 'object' && err.data !== null) {\n        try {\n          context.data = JSON.stringify(err.data);\n        } catch (e) {\n          // do nothing\n        }\n      } else if (typeof err.data === 'string' || typeof err.data === 'boolean' || typeof err.data === 'number') {\n        context.data = err.data.toString();\n      }\n    }\n  }\n}\n\nconst attemptFaroErr = (err: Error | FetchError | unknown, context2: LogContext) => {\n  let context = context2;\n  try {\n    populateFetchErrorContext(err, context);\n\n    if (err instanceof Error) {\n      logError(err, context);\n    } else if (typeof err === 'string') {\n      logError(new Error(err), context);\n    } else if (err && typeof err === 'object') {\n      if (context.msg) {\n        logError(new Error(context.msg), context);\n      } else {\n        logError(new Error('error object'), context);\n      }\n    } else {\n      logError(new Error('unknown error'), context);\n    }\n  } catch (e) {\n    console.error('Failed to log faro error!', { context, err });\n  }\n};\n\nconst hasData = (value: object): value is { data: unknown } => {\n  return 'data' in value;\n};\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport { NodeType, SyntaxNode, Tree } from '@lezer/common';\n\nimport { PluginExtensionPanelContext } from '@grafana/data';\nimport {\n  Bytes,\n  Duration,\n  Eq,\n  FilterOp,\n  Gte,\n  Gtr,\n  Identifier,\n  <PERSON>son,\n  LabelFilter,\n  LineFilter,\n  Logfmt,\n  Lss,\n  Lte,\n  Matcher,\n  Neq,\n  Npa,\n  Nre,\n  Number,\n  OrFilter,\n  parser,\n  PipeExact,\n  PipeMatch,\n  PipePattern,\n  Re,\n  Selector,\n  String,\n} from '@grafana/lezer-logql';\n\nimport { LabelType } from './fieldsTypes';\nimport {\n  FieldFilter,\n  FilterOp as FilterOperator,\n  FilterOpType,\n  IndexedLabelFilter,\n  LineFilterCaseSensitive,\n  LineFilterOp,\n  LineFilterType,\n  PatternFilterOp,\n  PatternFilterType,\n} from './filterTypes';\nimport { getLabelType<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Query } from './lokiQuery';\nimport { ParserType } from './variables';\n\nexport class NodePosition {\n  from: number;\n  to: number;\n  type?: NodeType;\n  syntaxNode?: SyntaxNode;\n\n  constructor(from: number, to: number, syntaxNode?: SyntaxNode, type?: NodeType) {\n    this.from = from;\n    this.to = to;\n    this.type = type;\n    this.syntaxNode = syntaxNode;\n  }\n\n  static fromNode(node: SyntaxNode): NodePosition {\n    return new NodePosition(node.from, node.to, node, node.type);\n  }\n\n  contains(position: NodePosition): boolean {\n    return this.from <= position.from && this.to >= position.to;\n  }\n\n  getExpression(query: string): string {\n    return query.substring(this.from, this.to);\n  }\n}\n\nexport function getNodesFromQuery(query: string, nodeTypes?: number[]): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  const tree: Tree = parser.parse(query);\n  tree.iterate({\n    enter: (node): false | void => {\n      if (nodeTypes === undefined || nodeTypes.includes(node.type.id)) {\n        nodes.push(node.node);\n      }\n    },\n  });\n  return nodes;\n}\n\nfunction getAllPositionsInNodeByType(node: SyntaxNode, type: number): NodePosition[] {\n  if (node.type.id === type) {\n    return [NodePosition.fromNode(node)];\n  }\n\n  const positions: NodePosition[] = [];\n  let pos = 0;\n  let child = node.childAfter(pos);\n  while (child) {\n    positions.push(...getAllPositionsInNodeByType(child, type));\n    pos = child.to;\n    child = node.childAfter(pos);\n  }\n  return positions;\n}\n\nfunction parseLabelFilters(query: string, filter: IndexedLabelFilter[]) {\n  // `Matcher` will select field filters as well as indexed label filters\n  const allMatcher = getNodesFromQuery(query, [Matcher]);\n  for (const matcher of allMatcher) {\n    const identifierPosition = getAllPositionsInNodeByType(matcher, Identifier);\n    if (!identifierPosition || identifierPosition.length === 0) {\n      continue;\n    }\n\n    const valuePosition = getAllPositionsInNodeByType(matcher, String);\n    const operator = query.substring(identifierPosition[0].to, valuePosition[0].from);\n    const key = identifierPosition[0].getExpression(query);\n    const value = valuePosition.map((position) => query.substring(position.from + 1, position.to - 1))[0];\n\n    if (\n      !key ||\n      !value ||\n      (operator !== FilterOperator.NotEqual &&\n        operator !== FilterOperator.Equal &&\n        operator !== FilterOperator.RegexEqual &&\n        operator !== FilterOperator.RegexNotEqual)\n    ) {\n      continue;\n    }\n\n    filter.push({\n      key,\n      operator,\n      type: LabelType.Indexed,\n      value,\n    });\n  }\n}\n\nfunction parseNonPatternFilters(\n  lineFilterValue: string,\n  quoteString: string,\n  lineFilters: LineFilterType[],\n  index: number,\n  operator: LineFilterOp\n) {\n  const isRegexSelector = operator === LineFilterOp.regex || operator === LineFilterOp.negativeRegex;\n  const isCaseInsensitive = lineFilterValue.includes('(?i)') && isRegexSelector;\n\n  // If quoteString is `, we shouldn't need to un-escape anything\n  // But if the quoteString is \", we'll need to remove double escape chars, as these values are re-escaped when building the query expression (but not stored in the value/url)\n  if (quoteString === '\"' && isRegexSelector) {\n    // replace \\\\ with \\\n    const replaceDoubleEscape = new RegExp(/\\\\\\\\/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleEscape, '\\\\');\n  } else if (quoteString === '\"') {\n    // replace \\\\\\\" => \"\n    const replaceDoubleQuoteEscape = new RegExp(`\\\\\\\\\\\"`, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleQuoteEscape, '\"');\n    const replaceDoubleEscape = new RegExp(/\\\\\\\\/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleEscape, '\\\\');\n  }\n\n  if (isCaseInsensitive) {\n    // If `(?i)` exists in a regex it would need to be escaped to match log lines containing `(?i)`, so it should be safe to replace all instances of `(?i)` in the line filter?\n    lineFilterValue = lineFilterValue.replace('(?i)', '');\n  }\n\n  lineFilters.push({\n    key: isCaseInsensitive\n      ? LineFilterCaseSensitive.caseInsensitive.toString()\n      : LineFilterCaseSensitive.caseSensitive.toString() + ',' + index.toString(),\n    operator: operator,\n    value: lineFilterValue,\n  });\n\n  return lineFilterValue;\n}\n\nfunction parsePatternFilters(lineFilterValue: string, patternFilters: PatternFilterType[], operator: PatternFilterOp) {\n  const replaceDoubleQuoteEscape = new RegExp(/\\\\\"/, 'g');\n  lineFilterValue = lineFilterValue.replace(replaceDoubleQuoteEscape, '\"');\n  patternFilters.push({\n    operator,\n    value: lineFilterValue,\n  });\n}\n\nfunction parseLineFilters(query: string, lineFilters: LineFilterType[], patternFilters: PatternFilterType[]) {\n  const allLineFilters = getNodesFromQuery(query, [LineFilter]);\n  for (const [index, matcher] of allLineFilters.entries()) {\n    const equal = getAllPositionsInNodeByType(matcher, PipeExact);\n    const pipeRegExp = getAllPositionsInNodeByType(matcher, PipeMatch);\n    const notEqual = getAllPositionsInNodeByType(matcher, Neq);\n    const notEqualRegExp = getAllPositionsInNodeByType(matcher, Nre);\n    const patternInclude = getAllPositionsInNodeByType(matcher, PipePattern);\n    const patternExclude = getAllPositionsInNodeByType(matcher, Npa);\n\n    const lineFilterValueNodes = getStringsFromLineFilter(matcher);\n\n    for (const lineFilterValueNode of lineFilterValueNodes) {\n      const quoteString = query.substring(lineFilterValueNode?.from + 1, lineFilterValueNode?.from);\n\n      // Remove quotes\n      let lineFilterValue = query.substring(lineFilterValueNode?.from + 1, lineFilterValueNode?.to - 1);\n\n      if (lineFilterValue.length) {\n        let operator;\n        if (equal.length) {\n          operator = LineFilterOp.match;\n        } else if (notEqual.length) {\n          operator = LineFilterOp.negativeMatch;\n        } else if (notEqualRegExp.length) {\n          operator = LineFilterOp.negativeRegex;\n        } else if (pipeRegExp.length) {\n          operator = LineFilterOp.regex;\n        } else if (patternInclude.length) {\n          operator = PatternFilterOp.match;\n        } else if (patternExclude.length) {\n          operator = PatternFilterOp.negativeMatch;\n        } else {\n          console.warn('unknown line filter', {\n            query: query.substring(matcher.from, matcher.to),\n          });\n\n          continue;\n        }\n\n        if (!(operator === PatternFilterOp.match || operator === PatternFilterOp.negativeMatch)) {\n          parseNonPatternFilters(lineFilterValue, quoteString, lineFilters, index, operator);\n        } else {\n          parsePatternFilters(lineFilterValue, patternFilters, operator);\n        }\n      }\n    }\n  }\n}\n\nfunction getNumericFieldOperator(matcher: SyntaxNode) {\n  if (getAllPositionsInNodeByType(matcher, Lte).length) {\n    return FilterOperator.lte;\n  } else if (getAllPositionsInNodeByType(matcher, Lss).length) {\n    return FilterOperator.lt;\n  } else if (getAllPositionsInNodeByType(matcher, Gte).length) {\n    return FilterOperator.gte;\n  } else if (getAllPositionsInNodeByType(matcher, Gtr).length) {\n    return FilterOperator.gt;\n  }\n\n  console.warn('unknown numeric operator');\n\n  return undefined;\n}\n\nfunction getStringFieldOperator(matcher: SyntaxNode) {\n  if (getAllPositionsInNodeByType(matcher, Eq).length) {\n    return FilterOperator.Equal; // =\n  } else if (getAllPositionsInNodeByType(matcher, Neq).length) {\n    return FilterOperator.NotEqual; // !=\n  } else if (getAllPositionsInNodeByType(matcher, Re).length) {\n    return FilterOperator.RegexEqual; // =~\n  } else if (getAllPositionsInNodeByType(matcher, Nre).length) {\n    return FilterOperator.RegexNotEqual; // !~\n  }\n\n  return undefined;\n}\n\nfunction parseFields(\n  query: string,\n  fields: FieldFilter[],\n  context?: PluginExtensionPanelContext,\n  lokiQuery?: LokiQuery\n) {\n  const dataFrame = context?.data?.series.find((frame) => frame.refId === lokiQuery?.refId);\n  // We do not currently support \"or\" in Grafana Logs Drilldown, so grab the left hand side LabelFilter leaf nodes as this will be the first filter expression in a given pipeline stage\n  const allFields = getNodesFromQuery(query, [LabelFilter]);\n  for (const matcher of allFields) {\n    const position = NodePosition.fromNode(matcher);\n    const expression = position.getExpression(query);\n    const isParentNode = matcher.getChild(LabelFilter);\n\n    // If the Label filter contains other Label Filter nodes, we want to skip this node so we only add the leaf LabelFilter nodes\n    if (isParentNode) {\n      continue;\n    }\n\n    // Skip error expression, it will get added automatically when Grafana Logs Drilldown adds a parser\n    if (expression.substring(0, 9) === `__error__`) {\n      continue;\n    }\n\n    // @todo we need to use detected_fields API to get the \"right\" parser for a specific field\n    // Currently we just check to see if there is a parser before the current node, this means that queries that are placing metadata filters after the parser will query the metadata field as a parsed field, which will lead to degraded performance\n    const logFmtParser = getNodesFromQuery(query.substring(0, matcher.node.to), [Logfmt]);\n    const jsonParser = getNodesFromQuery(query.substring(0, matcher.node.to), [Json]);\n\n    // field filter key\n    const fieldNameNode = getAllPositionsInNodeByType(matcher, Identifier);\n    const fieldName = fieldNameNode[0]?.getExpression(query);\n\n    // field filter value\n    const fieldStringValue = getAllPositionsInNodeByType(matcher, String);\n    const fieldNumberValue = getAllPositionsInNodeByType(matcher, Number);\n    const fieldBytesValue = getAllPositionsInNodeByType(matcher, Bytes);\n    const fieldDurationValue = getAllPositionsInNodeByType(matcher, Duration);\n\n    let fieldValue: string, operator: FilterOpType | undefined;\n    if (fieldStringValue.length) {\n      operator = getStringFieldOperator(matcher);\n      // Strip out quotes\n      fieldValue = query.substring(fieldStringValue[0].from + 1, fieldStringValue[0].to - 1);\n    } else if (fieldNumberValue.length) {\n      fieldValue = fieldNumberValue[0].getExpression(query);\n      operator = getNumericFieldOperator(matcher);\n    } else if (fieldDurationValue.length) {\n      operator = getNumericFieldOperator(matcher);\n      fieldValue = fieldDurationValue[0].getExpression(query);\n    } else if (fieldBytesValue.length) {\n      operator = getNumericFieldOperator(matcher);\n      fieldValue = fieldBytesValue[0].getExpression(query);\n    } else {\n      continue;\n    }\n\n    // Label type\n    let labelType: LabelType | undefined;\n    if (dataFrame) {\n      // @todo if the field label is not in the first line, we'll always add this filter as a field filter\n      // Also negative filters that exclude all values of a field will always fail to get a label type for that exclusion filter?\n      labelType = getLabelTypeFromFrame(fieldName, dataFrame) ?? undefined;\n    }\n\n    if (operator) {\n      let parser: ParserType | undefined;\n      if (logFmtParser.length && jsonParser.length) {\n        parser = 'mixed';\n      } else if (logFmtParser.length) {\n        parser = 'logfmt';\n      } else if (jsonParser.length) {\n        parser = 'json';\n      } else {\n        // If there is no parser in the query, the field would have to be metadata or an invalid query?\n        labelType = LabelType.StructuredMetadata;\n      }\n\n      fields.push({\n        key: fieldName,\n        operator: operator,\n        parser,\n        type: labelType ?? LabelType.Parsed,\n        value: fieldValue,\n      });\n    }\n  }\n}\n\nexport function getMatcherFromQuery(\n  query: string,\n  context?: PluginExtensionPanelContext,\n  lokiQuery?: LokiQuery\n): {\n  fields?: FieldFilter[];\n  labelFilters: IndexedLabelFilter[];\n  lineFilters?: LineFilterType[];\n  patternFilters?: PatternFilterType[];\n} {\n  const filter: IndexedLabelFilter[] = [];\n  const lineFilters: LineFilterType[] = [];\n  const patternFilters: PatternFilterType[] = [];\n  const fields: FieldFilter[] = [];\n  const selector = getNodesFromQuery(query, [Selector]);\n\n  if (selector.length === 0) {\n    return { labelFilters: filter };\n  }\n\n  // Get the stream selector portion of the query\n  const selectorQuery = getAllPositionsInNodeByType(selector[0], Selector)[0].getExpression(query);\n\n  parseLabelFilters(selectorQuery, filter);\n  parseLineFilters(query, lineFilters, patternFilters);\n  parseFields(query, fields, context, lokiQuery);\n\n  return { fields, labelFilters: filter, lineFilters, patternFilters };\n}\n\nexport function isQueryWithNode(query: string, nodeType: number): boolean {\n  let isQueryWithNode = false;\n  const tree = parser.parse(query);\n  tree.iterate({\n    enter: ({ type }): false | void => {\n      if (type.id === nodeType) {\n        isQueryWithNode = true;\n        return false;\n      }\n    },\n  });\n  return isQueryWithNode;\n}\n\n/**\n * Parses the query and looks for error nodes. If there is at least one, it returns true.\n * Grafana variables are considered errors, so if you need to validate a query\n * with variables you should interpolate it first.\n */\nexport const ErrorId = 0;\nexport function isValidQuery(query: string): boolean {\n  return isQueryWithNode(query, ErrorId) === false;\n}\n\nfunction getStringsFromLineFilter(filter: SyntaxNode): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  let node: SyntaxNode | null = filter;\n  do {\n    const string = node.getChild(String);\n    if (string && !node.getChild(FilterOp)) {\n      nodes.push(string);\n    }\n    node = node.getChild(OrFilter);\n  } while (node != null);\n\n  return nodes;\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { DataFrame, DataSourceJsonData, ScopedVars, TimeRange } from '@grafana/data';\nimport { DataSourceWithBackend } from '@grafana/runtime';\nimport { DataSourceRef } from '@grafana/schema';\n\nimport { LabelType } from './fieldsTypes';\n\nexport enum LokiQueryDirection {\n  Backward = 'backward',\n  Forward = 'forward',\n  Scan = 'scan',\n}\n\nexport type LokiQuery = {\n  datasource?: DataSourceRef;\n  direction?: LokiQueryDirection;\n  editorMode?: string;\n  expr: string;\n  legendFormat?: string;\n  maxLines?: number;\n  queryType?: LokiQueryType;\n  refId: string;\n  step?: string;\n  supportingQueryType?: string;\n};\n\nexport type LokiQueryType = 'instant' | 'range' | 'stream' | string;\n\nexport type LokiDatasource = DataSourceWithBackend<LokiQuery, DataSourceJsonData> & {\n  maxLines?: number;\n} & {\n  getTimeRangeParams: (timeRange: TimeRange) => { end: number; start: number };\n  // @todo delete after min supported grafana is upgraded to >=11.6\n  interpolateString?: (string: string, scopedVars?: ScopedVars) => string;\n};\n\nexport function getLabelTypeFromFrame(labelKey: string, frame: DataFrame, index = 0): null | LabelType {\n  const typeField = frame.fields.find((field) => field.name === 'labelTypes')?.values[index];\n  if (!typeField) {\n    return null;\n  }\n  switch (typeField[labelKey]) {\n    case 'I':\n      return LabelType.Indexed;\n    case 'S':\n      return LabelType.StructuredMetadata;\n    case 'P':\n      return LabelType.Parsed;\n    default:\n      return null;\n  }\n}\n", "import { LogsSortOrder, RawTimeRange, UrlQueryMap } from '@grafana/data';\n\nimport { drilldownLabelUrlKey, pageSlugUrlKey } from '../Components/ServiceScene/ServiceSceneConstants';\nimport { SelectedTableRow } from '../Components/Table/LogLineCellComponent';\nimport { JSONDerivedFieldLink } from './derivedFields';\nimport { PageSlugs, TabNames, ValueSlugs } from './enums';\nimport { LabelFilterOp, NumericFilterOp } from './filterTypes';\nimport { LogsVisualizationType } from './store';\nimport { FieldValue, ParserType } from './variables';\n\nconst isObj = (o: unknown): o is object => typeof o === 'object' && o !== null;\n\nexport function hasProp<K extends PropertyKey>(data: object, prop: K): data is Record<K, unknown> {\n  return prop in data;\n}\n\nconst isString = (s: unknown) => (typeof s === 'string' && s) || '';\n\nexport const isRecord = (obj: unknown): obj is Record<string, unknown> => typeof obj === 'object';\n\nexport function unknownToStrings(a: unknown): string[] {\n  let strings: string[] = [];\n  if (Array.isArray(a)) {\n    for (let i = 0; i < a.length; i++) {\n      strings.push(isString(a[i]));\n    }\n  }\n  return strings;\n}\n\nexport function narrowSelectedTableRow(o: unknown): SelectedTableRow | false {\n  const narrowed = isObj(o) && hasProp(o, 'row') && hasProp(o, 'id') && o;\n\n  if (narrowed) {\n    const row = typeof narrowed.row === 'number' && narrowed.row;\n    const id = typeof narrowed.id === 'string' && narrowed.id;\n    if (id !== false && row !== false) {\n      return { id, row };\n    }\n  }\n\n  return false;\n}\n\nexport function narrowLogsVisualizationType(o: unknown): LogsVisualizationType | false {\n  return typeof o === 'string' && (o === 'logs' || o === 'table' || o === 'json') && o;\n}\nexport function narrowLogsSortOrder(o: unknown): LogsSortOrder | false {\n  if (typeof o === 'string' && o === LogsSortOrder.Ascending.toString()) {\n    return LogsSortOrder.Ascending;\n  }\n\n  if (typeof o === 'string' && o === LogsSortOrder.Descending.toString()) {\n    return LogsSortOrder.Descending;\n  }\n\n  return false;\n}\n\nexport function narrowFieldValue(o: unknown): FieldValue | false {\n  const narrowed = isObj(o) && hasProp(o, 'value') && hasProp(o, 'parser') && o;\n\n  if (narrowed) {\n    const parser: ParserType | false =\n      typeof narrowed.parser === 'string' &&\n      (narrowed.parser === 'logfmt' ||\n        narrowed.parser === 'json' ||\n        narrowed.parser === 'mixed' ||\n        narrowed.parser === 'structuredMetadata') &&\n      narrowed.parser;\n    const value = typeof narrowed.value === 'string' && narrowed.value;\n\n    if (parser !== false && value !== false) {\n      return { parser, value };\n    }\n  }\n\n  return false;\n}\n\nexport function narrowRecordStringNumber(o: unknown): Record<string, number> | false {\n  const narrowed = isObj(o) && isRecord(o) && o;\n\n  if (narrowed) {\n    const keys = Object.keys(narrowed);\n    const returnRecord: Record<string, number> = {};\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const value = narrowed[keys[i]];\n      if (typeof value === 'number') {\n        returnRecord[key] = value;\n      }\n    }\n\n    return returnRecord;\n  }\n\n  return false;\n}\n\nexport function narrowTimeRange(unknownRange: unknown): RawTimeRange | undefined {\n  const range = isObj(unknownRange) && hasProp(unknownRange, 'to') && hasProp(unknownRange, 'from') && unknownRange;\n  if (range) {\n    const to = isString(range.to);\n    const from = isString(range.from);\n    if (to && from) {\n      return { from, to };\n    }\n  }\n\n  return undefined;\n}\n\nexport function narrowErrorMessage(e: unknown): string | undefined {\n  const msg = isObj(e) && hasProp(e, 'error') && isString(e.error);\n  if (msg) {\n    return msg;\n  }\n  return undefined;\n}\n\nexport function narrowFilterOperator(op: string): LabelFilterOp | NumericFilterOp {\n  switch (op) {\n    case LabelFilterOp.Equal:\n    case LabelFilterOp.NotEqual:\n    case LabelFilterOp.RegexEqual:\n    case LabelFilterOp.RegexNotEqual:\n    case NumericFilterOp.gt:\n    case NumericFilterOp.gte:\n    case NumericFilterOp.lt:\n    case NumericFilterOp.lte:\n      return op;\n    default:\n      throw new NarrowingError('operator is invalid!');\n  }\n}\n\nexport function narrowTabName(input: unknown): TabNames | false {\n  return (\n    (input === TabNames.fields ||\n      input === TabNames.labels ||\n      input === TabNames.logs ||\n      input === TabNames.patterns) &&\n    input\n  );\n}\n\nexport function narrowPageOrValueSlug(input: unknown): ValueSlugs | PageSlugs | false {\n  return narrowPageSlug(input) || narrowValueSlug(input);\n}\n\nexport function narrowValueSlug(input: unknown): ValueSlugs | false {\n  return (input === ValueSlugs.field || input === ValueSlugs.label) && input;\n}\n\nexport function narrowPageSlug(input: unknown): PageSlugs | false {\n  if (typeof input === 'string') {\n    input = input.toLowerCase();\n  }\n  return (\n    (input === PageSlugs.fields ||\n      input === PageSlugs.labels ||\n      input === PageSlugs.logs ||\n      input === PageSlugs.patterns) &&\n    input\n  );\n}\n\nexport function narrowDrilldownLabelFromSearchParams(searchParams: UrlQueryMap) {\n  return Array.isArray(searchParams[drilldownLabelUrlKey]) &&\n    searchParams[drilldownLabelUrlKey][0] &&\n    typeof searchParams[drilldownLabelUrlKey][0] === 'string'\n    ? searchParams[drilldownLabelUrlKey][0]\n    : typeof searchParams[drilldownLabelUrlKey] === 'string' && searchParams[drilldownLabelUrlKey];\n}\n\nexport function narrowPageSlugFromSearchParams(searchParams: UrlQueryMap) {\n  return narrowPageOrValueSlug(\n    Array.isArray(searchParams[pageSlugUrlKey]) ? searchParams[pageSlugUrlKey][0] : searchParams[pageSlugUrlKey]\n  );\n}\n\nexport function narrowJsonDerivedFieldLinkPayload(payload: unknown): JSONDerivedFieldLink | false {\n  if (isObj(payload) && hasProp(payload, 'href') && hasProp(payload, 'name')) {\n    const href = isString(payload.href);\n    const name = isString(payload.name);\n    return {\n      href,\n      name,\n    };\n  }\n  return false;\n}\n\nexport class NarrowingError extends Error {}\n", "import { FilterOp, FilterOpType, NumericFilterOp } from './filterTypes';\nimport { numericOperatorArray } from './operators';\n\nexport const isOperatorInclusive = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.Equal || op === FilterOp.RegexEqual;\n};\nexport const isOperatorExclusive = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.NotEqual || op === FilterOp.RegexNotEqual;\n};\nexport const isOperatorRegex = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.RegexEqual || op === FilterOp.RegexNotEqual;\n};\nexport const isOperatorNumeric = (op: string | NumericFilterOp): boolean => {\n  return numericOperatorArray.includes(op);\n};\n", "import { FilterOp, FilterOpType } from './filterTypes';\nimport { logger } from './logger';\n\nexport function getOperatorDescription(op: FilterOpType): string {\n  if (op === FilterOp.NotEqual) {\n    return 'Not equal';\n  }\n  if (op === FilterOp.RegexNotEqual) {\n    return 'Does not match regex';\n  }\n  if (op === FilterOp.Equal) {\n    return 'Equals';\n  }\n  if (op === FilterOp.RegexEqual) {\n    return 'Matches regex';\n  }\n  if (op === FilterOp.lt) {\n    return 'Less than';\n  }\n  if (op === FilterOp.gt) {\n    return 'Greater than';\n  }\n  if (op === FilterOp.gte) {\n    return 'Greater than or equal to';\n  }\n  if (op === FilterOp.lte) {\n    return 'Less than or equal to';\n  }\n\n  const error = new Error('Invalid operator!');\n  logger.error(error, { msg: 'Invalid operator', operator: op });\n  throw error;\n}\n", "import { SelectableValue } from '@grafana/data';\n\nimport { FilterOp, LineFilterOp } from './filterTypes';\nimport { getOperatorDescription } from './getOperatorDescription';\n\nexport const operators = [FilterOp.Equal, FilterOp.NotEqual, FilterOp.RegexEqual, FilterOp.RegexNotEqual].map<\n  SelectableValue<string>\n>((value, index, array) => {\n  return {\n    description: getOperatorDescription(value),\n    label: value,\n    value,\n  };\n});\n\nexport const includeOperators = [FilterOp.Equal, FilterOp.RegexEqual].map<SelectableValue<string>>((value) => ({\n  description: getOperatorDescription(value),\n  label: value,\n  value,\n}));\n\nexport const numericOperatorArray = [FilterOp.gt, FilterOp.gte, FilterOp.lt, FilterOp.lte];\n\nexport const numericOperators = numericOperatorArray.map<SelectableValue<string>>((value) => ({\n  description: getOperatorDescription(value),\n  label: value,\n  value,\n}));\n\nexport const lineFilterOperators: SelectableValue[] = [\n  { label: 'match', value: LineFilterOp.match },\n  { label: 'negativeMatch', value: LineFilterOp.negativeMatch },\n  { label: 'regex', value: LineFilterOp.regex },\n  { label: 'negativeRegex', value: LineFilterOp.negativeRegex },\n];\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain many imports to keep that bundle size small. Don't add imports to this file!\nimport { escapeLabelValueInExactSelector } from './extensions/scenesMethods';\nimport { AppliedPattern } from './variables';\n\nexport function renderPatternFilters(patterns: AppliedPattern[]) {\n  const excludePatterns = patterns.filter((pattern) => pattern.type === 'exclude');\n  const excludePatternsLine = excludePatterns\n    .map((p) => `!> \"${escapeLabelValueInExactSelector(p.pattern)}\"`)\n    .join(' ')\n    .trim();\n\n  const includePatterns = patterns.filter((pattern) => pattern.type === 'include');\n  let includePatternsLine = '';\n  if (includePatterns.length > 0) {\n    if (includePatterns.length === 1) {\n      includePatternsLine = `|> \"${escapeLabelValueInExactSelector(includePatterns[0].pattern)}\"`;\n    } else {\n      includePatternsLine = `|> ${includePatterns\n        .map((p) => `\"${escapeLabelValueInExactSelector(p.pattern)}\"`)\n        .join(' or ')}`;\n    }\n  }\n  return `${excludePatternsLine} ${includePatternsLine}`.trim();\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\nimport { AdHocFilterWithLabels } from '@grafana/scenes';\n\nexport interface FieldValue {\n  parser: ParserType;\n  value: string;\n}\n\nexport interface AdHocFieldValue {\n  parser?: ParserType;\n  value?: string;\n}\nexport interface AppliedPattern {\n  pattern: string;\n  type: 'exclude' | 'include';\n}\n\nexport type ParserType = 'json' | 'logfmt' | 'mixed' | 'structuredMetadata';\nexport type DetectedFieldType = 'boolean' | 'bytes' | 'duration' | 'float' | 'int' | 'string';\nexport type AdHocFilterWithLabelsMeta = { parser?: ParserType; type?: DetectedFieldType };\nexport type AdHocFiltersWithLabelsAndMeta = AdHocFilterWithLabels<AdHocFilterWithLabelsMeta>;\n\nexport type LogsQueryOptions = {\n  fieldExpressionToAdd?: string;\n  fieldType?: DetectedFieldType;\n  jsonParserPropToAdd?: string;\n  labelExpressionToAdd?: string;\n  parser?: ParserType;\n  structuredMetadataToAdd?: string;\n};\n\nexport const VAR_LABELS = 'filters';\nexport const VAR_LABELS_EXPR = '${filters}';\nexport const VAR_LABELS_REPLICA = 'filters_replica';\nexport const VAR_LABELS_REPLICA_EXPR = '${filters_replica}';\nexport const VAR_FIELDS = 'fields';\nexport const VAR_FIELDS_EXPR = '${fields}';\nexport const PENDING_FIELDS_EXPR = '${pendingFields}';\nexport const PENDING_METADATA_EXPR = '${pendingMetadata}';\nexport const VAR_FIELDS_AND_METADATA = 'all-fields';\nexport const VAR_METADATA = 'metadata';\nexport const VAR_METADATA_EXPR = '${metadata}';\nexport const VAR_PATTERNS = 'patterns';\nexport const VAR_PATTERNS_EXPR = '${patterns}';\nexport const VAR_LEVELS = 'levels';\nexport const VAR_LEVELS_EXPR = '${levels}';\nexport const VAR_FIELD_GROUP_BY = 'fieldBy';\nexport const VAR_LABEL_GROUP_BY = 'labelBy';\nexport const VAR_LABEL_GROUP_BY_EXPR = '${labelBy}';\nexport const VAR_PRIMARY_LABEL_SEARCH = 'primary_label_search';\nexport const VAR_PRIMARY_LABEL_SEARCH_EXPR = '${primary_label_search}';\nexport const VAR_PRIMARY_LABEL = 'primary_label';\nexport const VAR_PRIMARY_LABEL_EXPR = '${primary_label}';\nexport const VAR_DATASOURCE = 'ds';\nexport const VAR_DATASOURCE_EXPR = '${ds}';\nexport const VAR_JSON_FIELDS = 'jsonFields';\nexport const VAR_JSON_FIELDS_EXPR = '${jsonFields}';\n\nexport const VAR_LINE_FORMAT = 'lineFormat';\nexport const VAR_LINE_FORMAT_EXPR = '${lineFormat}';\n\nexport const DETECTED_FIELDS_MIXED_FORMAT_EXPR_NO_JSON_FIELDS = `| json | logfmt | drop __error__, __error_details__`;\nexport const DETECTED_FIELDS_MIXED_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | logfmt | drop __error__, __error_details__`;\nexport const MIXED_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | logfmt | drop __error__, __error_details__`;\nexport const JSON_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | drop __error__, __error_details__`;\n// export const JSON_FORMAT_EXPR_NO_JSON_FIELDS = `| json | drop __error__, __error_details__`;\nexport const LOGS_FORMAT_EXPR = `| logfmt`;\n// This variable is hardcoded to the value of MIXED_FORMAT_EXPR. This is a hack to get logs context working, we don't want to use a variable for a value that doesn't change and cannot be updated by the user.\nexport const VAR_LOGS_FORMAT = 'logsFormat';\nexport const VAR_LOGS_FORMAT_EXPR = '${logsFormat}';\n// The deprecated line filter (custom variable)\nexport const VAR_LINE_FILTER_DEPRECATED = 'lineFilter';\n// The new single value line filter (ad-hoc variable), results are added to VAR_LINE_FILTER_AD_HOC when \"submitted\"\nexport const VAR_LINE_FILTER = 'lineFilterV2';\nexport const VAR_LINE_FILTER_EXPR = '${lineFilterV2}';\n// The new multi value line filter (ad-hoc variable)\nexport const VAR_LINE_FILTERS = 'lineFilters';\nexport const VAR_LINE_FILTERS_EXPR = '${lineFilters}';\nexport const LOG_STREAM_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} | json ${VAR_JSON_FIELDS_EXPR} | logfmt | drop __error__, __error_details__ ${VAR_FIELDS_EXPR} ${VAR_LINE_FORMAT_EXPR}`;\n// Same as the LOG_STREAM_SELECTOR_EXPR, but without the fields as they will need to be built manually to exclude the current filter value\nexport const DETECTED_FIELD_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${MIXED_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const DETECTED_FIELD_AND_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${PENDING_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${DETECTED_FIELDS_MIXED_FORMAT_EXPR} ${PENDING_FIELDS_EXPR}`;\nexport const DETECTED_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${PENDING_FIELDS_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const DETECTED_LEVELS_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${PENDING_FIELDS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const PATTERNS_SAMPLE_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LOGS_FORMAT_EXPR}`;\nexport const PRETTY_LOG_STREAM_SELECTOR_EXPR = `${VAR_LABELS_EXPR} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const EXPLORATION_DS = { uid: VAR_DATASOURCE_EXPR };\nexport const ALL_VARIABLE_VALUE = '$__all';\nexport const LEVEL_VARIABLE_VALUE = 'detected_level';\nexport const SERVICE_NAME = 'service_name';\nexport const SERVICE_UI_LABEL = 'service';\nexport const VAR_AGGREGATED_METRICS = 'var_aggregated_metrics';\nexport const VAR_AGGREGATED_METRICS_EXPR = '${var_aggregated_metrics}';\nexport const EMPTY_VARIABLE_VALUE = '\"\"';\n\n// Delimiter used at the start of a label value to denote user input that should not be escaped\n// @todo we need ad-hoc-filter meta that is persisted in the URL so we can clean this up.\nexport const USER_INPUT_ADHOC_VALUE_PREFIX = '__CVΩ__';\nexport function stripAdHocFilterUserInputPrefix(value = '') {\n  if (value.startsWith(USER_INPUT_ADHOC_VALUE_PREFIX)) {\n    return value.substring(USER_INPUT_ADHOC_VALUE_PREFIX.length);\n  }\n  return value;\n}\nexport function isAdHocFilterValueUserInput(value = '') {\n  return value.startsWith(USER_INPUT_ADHOC_VALUE_PREFIX);\n}\nexport function addAdHocFilterUserInputPrefix(value = '') {\n  return USER_INPUT_ADHOC_VALUE_PREFIX + value;\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__6089__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7781__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8531__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__2007__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3241__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1308__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5959__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8398__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__200__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1159__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7694__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1269__;", "/**\nThe default maximum length of a `TreeBuffer` node.\n*/\nconst DefaultBufferLength = 1024;\nlet nextPropID = 0;\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n/**\nEach [node type](#common.NodeType) or [individual tree](#common.Tree)\ncan have metadata associated with it in props. Instances of this\nclass represent prop names.\n*/\nclass NodeProp {\n    /**\n    Create a new node prop type.\n    */\n    constructor(config = {}) {\n        this.id = nextPropID++;\n        this.perNode = !!config.perNode;\n        this.deserialize = config.deserialize || (() => {\n            throw new Error(\"This node type doesn't define a deserialize function\");\n        });\n    }\n    /**\n    This is meant to be used with\n    [`NodeSet.extend`](#common.NodeSet.extend) or\n    [`LRParser.configure`](#lr.ParserConfig.props) to compute\n    prop values for each node type in the set. Takes a [match\n    object](#common.NodeType^match) or function that returns undefined\n    if the node type doesn't get this prop, and the prop's value if\n    it does.\n    */\n    add(match) {\n        if (this.perNode)\n            throw new RangeError(\"Can't add per-node props to node types\");\n        if (typeof match != \"function\")\n            match = NodeType.match(match);\n        return (type) => {\n            let result = match(type);\n            return result === undefined ? null : [this, result];\n        };\n    }\n}\n/**\nProp that is used to describe matching delimiters. For opening\ndelimiters, this holds an array of node names (written as a\nspace-separated string when declaring this prop in a grammar)\nfor the node types of closing delimiters that match it.\n*/\nNodeProp.closedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nThe inverse of [`closedBy`](#common.NodeProp^closedBy). This is\nattached to closing delimiters, holding an array of node names\nof types of matching opening delimiters.\n*/\nNodeProp.openedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nUsed to assign node types to groups (for example, all node\ntypes that represent an expression could be tagged with an\n`\"Expression\"` group).\n*/\nNodeProp.group = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nAttached to nodes to indicate these should be\n[displayed](https://codemirror.net/docs/ref/#language.syntaxTree)\nin a bidirectional text isolate, so that direction-neutral\ncharacters on their sides don't incorrectly get associated with\nsurrounding text. You'll generally want to set this for nodes\nthat contain arbitrary text, like strings and comments, and for\nnodes that appear _inside_ arbitrary text, like HTML tags. When\nnot given a value, in a grammar declaration, defaults to\n`\"auto\"`.\n*/\nNodeProp.isolate = new NodeProp({ deserialize: value => {\n        if (value && value != \"rtl\" && value != \"ltr\" && value != \"auto\")\n            throw new RangeError(\"Invalid value for isolate: \" + value);\n        return value || \"auto\";\n    } });\n/**\nThe hash of the [context](#lr.ContextTracker.constructor)\nthat the node was parsed in, if any. Used to limit reuse of\ncontextual nodes.\n*/\nNodeProp.contextHash = new NodeProp({ perNode: true });\n/**\nThe distance beyond the end of the node that the tokenizer\nlooked ahead for any of the tokens inside the node. (The LR\nparser only stores this when it is larger than 25, for\nefficiency reasons.)\n*/\nNodeProp.lookAhead = new NodeProp({ perNode: true });\n/**\nThis per-node prop is used to replace a given node, or part of a\nnode, with another tree. This is useful to include trees from\ndifferent languages in mixed-language parsers.\n*/\nNodeProp.mounted = new NodeProp({ perNode: true });\n/**\nA mounted tree, which can be [stored](#common.NodeProp^mounted) on\na tree node to indicate that parts of its content are\nrepresented by another tree.\n*/\nclass MountedTree {\n    constructor(\n    /**\n    The inner tree.\n    */\n    tree, \n    /**\n    If this is null, this tree replaces the entire node (it will\n    be included in the regular iteration instead of its host\n    node). If not, only the given ranges are considered to be\n    covered by this tree. This is used for trees that are mixed in\n    a way that isn't strictly hierarchical. Such mounted trees are\n    only entered by [`resolveInner`](#common.Tree.resolveInner)\n    and [`enter`](#common.SyntaxNode.enter).\n    */\n    overlay, \n    /**\n    The parser used to create this subtree.\n    */\n    parser) {\n        this.tree = tree;\n        this.overlay = overlay;\n        this.parser = parser;\n    }\n    /**\n    @internal\n    */\n    static get(tree) {\n        return tree && tree.props && tree.props[NodeProp.mounted.id];\n    }\n}\nconst noProps = Object.create(null);\n/**\nEach node in a syntax tree has a node type associated with it.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the node type. Not necessarily unique, but if the\n    grammar was written properly, different node types with the\n    same name within a node set should play the same semantic\n    role.\n    */\n    name, \n    /**\n    @internal\n    */\n    props, \n    /**\n    The id of this node in its set. Corresponds to the term ids\n    used in the parser.\n    */\n    id, \n    /**\n    @internal\n    */\n    flags = 0) {\n        this.name = name;\n        this.props = props;\n        this.id = id;\n        this.flags = flags;\n    }\n    /**\n    Define a node type.\n    */\n    static define(spec) {\n        let props = spec.props && spec.props.length ? Object.create(null) : noProps;\n        let flags = (spec.top ? 1 /* NodeFlag.Top */ : 0) | (spec.skipped ? 2 /* NodeFlag.Skipped */ : 0) |\n            (spec.error ? 4 /* NodeFlag.Error */ : 0) | (spec.name == null ? 8 /* NodeFlag.Anonymous */ : 0);\n        let type = new NodeType(spec.name || \"\", props, spec.id, flags);\n        if (spec.props)\n            for (let src of spec.props) {\n                if (!Array.isArray(src))\n                    src = src(type);\n                if (src) {\n                    if (src[0].perNode)\n                        throw new RangeError(\"Can't store a per-node prop on a node type\");\n                    props[src[0].id] = src[1];\n                }\n            }\n        return type;\n    }\n    /**\n    Retrieves a node prop for this type. Will return `undefined` if\n    the prop isn't present on this node.\n    */\n    prop(prop) { return this.props[prop.id]; }\n    /**\n    True when this is the top node of a grammar.\n    */\n    get isTop() { return (this.flags & 1 /* NodeFlag.Top */) > 0; }\n    /**\n    True when this node is produced by a skip rule.\n    */\n    get isSkipped() { return (this.flags & 2 /* NodeFlag.Skipped */) > 0; }\n    /**\n    Indicates whether this is an error node.\n    */\n    get isError() { return (this.flags & 4 /* NodeFlag.Error */) > 0; }\n    /**\n    When true, this node type doesn't correspond to a user-declared\n    named node, for example because it is used to cache repetition.\n    */\n    get isAnonymous() { return (this.flags & 8 /* NodeFlag.Anonymous */) > 0; }\n    /**\n    Returns true when this node's name or one of its\n    [groups](#common.NodeProp^group) matches the given string.\n    */\n    is(name) {\n        if (typeof name == 'string') {\n            if (this.name == name)\n                return true;\n            let group = this.prop(NodeProp.group);\n            return group ? group.indexOf(name) > -1 : false;\n        }\n        return this.id == name;\n    }\n    /**\n    Create a function from node types to arbitrary values by\n    specifying an object whose property names are node or\n    [group](#common.NodeProp^group) names. Often useful with\n    [`NodeProp.add`](#common.NodeProp.add). You can put multiple\n    names, separated by spaces, in a single property name to map\n    multiple node names to a single value.\n    */\n    static match(map) {\n        let direct = Object.create(null);\n        for (let prop in map)\n            for (let name of prop.split(\" \"))\n                direct[name] = map[prop];\n        return (node) => {\n            for (let groups = node.prop(NodeProp.group), i = -1; i < (groups ? groups.length : 0); i++) {\n                let found = direct[i < 0 ? node.name : groups[i]];\n                if (found)\n                    return found;\n            }\n        };\n    }\n}\n/**\nAn empty dummy node type to use when no actual type is available.\n*/\nNodeType.none = new NodeType(\"\", Object.create(null), 0, 8 /* NodeFlag.Anonymous */);\n/**\nA node set holds a collection of node types. It is used to\ncompactly represent trees by storing their type ids, rather than a\nfull pointer to the type object, in a numeric array. Each parser\n[has](#lr.LRParser.nodeSet) a node set, and [tree\nbuffers](#common.TreeBuffer) can only store collections of nodes\nfrom the same set. A set can have a maximum of 2**16 (65536) node\ntypes in it, so that the ids fit into 16-bit typed array slots.\n*/\nclass NodeSet {\n    /**\n    Create a set with the given types. The `id` property of each\n    type should correspond to its position within the array.\n    */\n    constructor(\n    /**\n    The node types in this set, by id.\n    */\n    types) {\n        this.types = types;\n        for (let i = 0; i < types.length; i++)\n            if (types[i].id != i)\n                throw new RangeError(\"Node type ids should correspond to array positions when creating a node set\");\n    }\n    /**\n    Create a copy of this set with some node properties added. The\n    arguments to this method can be created with\n    [`NodeProp.add`](#common.NodeProp.add).\n    */\n    extend(...props) {\n        let newTypes = [];\n        for (let type of this.types) {\n            let newProps = null;\n            for (let source of props) {\n                let add = source(type);\n                if (add) {\n                    if (!newProps)\n                        newProps = Object.assign({}, type.props);\n                    newProps[add[0].id] = add[1];\n                }\n            }\n            newTypes.push(newProps ? new NodeType(type.name, newProps, type.id, type.flags) : type);\n        }\n        return new NodeSet(newTypes);\n    }\n}\nconst CachedNode = new WeakMap(), CachedInnerNode = new WeakMap();\n/**\nOptions that control iteration. Can be combined with the `|`\noperator to enable multiple ones.\n*/\nvar IterMode;\n(function (IterMode) {\n    /**\n    When enabled, iteration will only visit [`Tree`](#common.Tree)\n    objects, not nodes packed into\n    [`TreeBuffer`](#common.TreeBuffer)s.\n    */\n    IterMode[IterMode[\"ExcludeBuffers\"] = 1] = \"ExcludeBuffers\";\n    /**\n    Enable this to make iteration include anonymous nodes (such as\n    the nodes that wrap repeated grammar constructs into a balanced\n    tree).\n    */\n    IterMode[IterMode[\"IncludeAnonymous\"] = 2] = \"IncludeAnonymous\";\n    /**\n    By default, regular [mounted](#common.NodeProp^mounted) nodes\n    replace their base node in iteration. Enable this to ignore them\n    instead.\n    */\n    IterMode[IterMode[\"IgnoreMounts\"] = 4] = \"IgnoreMounts\";\n    /**\n    This option only applies in\n    [`enter`](#common.SyntaxNode.enter)-style methods. It tells the\n    library to not enter mounted overlays if one covers the given\n    position.\n    */\n    IterMode[IterMode[\"IgnoreOverlays\"] = 8] = \"IgnoreOverlays\";\n})(IterMode || (IterMode = {}));\n/**\nA piece of syntax tree. There are two ways to approach these\ntrees: the way they are actually stored in memory, and the\nconvenient way.\n\nSyntax trees are stored as a tree of `Tree` and `TreeBuffer`\nobjects. By packing detail information into `TreeBuffer` leaf\nnodes, the representation is made a lot more memory-efficient.\n\nHowever, when you want to actually work with tree nodes, this\nrepresentation is very awkward, so most client code will want to\nuse the [`TreeCursor`](#common.TreeCursor) or\n[`SyntaxNode`](#common.SyntaxNode) interface instead, which provides\na view on some part of this data structure, and can be used to\nmove around to adjacent nodes.\n*/\nclass Tree {\n    /**\n    Construct a new tree. See also [`Tree.build`](#common.Tree^build).\n    */\n    constructor(\n    /**\n    The type of the top node.\n    */\n    type, \n    /**\n    This node's child nodes.\n    */\n    children, \n    /**\n    The positions (offsets relative to the start of this tree) of\n    the children.\n    */\n    positions, \n    /**\n    The total length of this tree\n    */\n    length, \n    /**\n    Per-node [node props](#common.NodeProp) to associate with this node.\n    */\n    props) {\n        this.type = type;\n        this.children = children;\n        this.positions = positions;\n        this.length = length;\n        /**\n        @internal\n        */\n        this.props = null;\n        if (props && props.length) {\n            this.props = Object.create(null);\n            for (let [prop, value] of props)\n                this.props[typeof prop == \"number\" ? prop : prop.id] = value;\n        }\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let mounted = MountedTree.get(this);\n        if (mounted && !mounted.overlay)\n            return mounted.tree.toString();\n        let children = \"\";\n        for (let ch of this.children) {\n            let str = ch.toString();\n            if (str) {\n                if (children)\n                    children += \",\";\n                children += str;\n            }\n        }\n        return !this.type.name ? children :\n            (/\\W/.test(this.type.name) && !this.type.isError ? JSON.stringify(this.type.name) : this.type.name) +\n                (children.length ? \"(\" + children + \")\" : \"\");\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) positioned at the top of\n    the tree. Mode can be used to [control](#common.IterMode) which\n    nodes the cursor visits.\n    */\n    cursor(mode = 0) {\n        return new TreeCursor(this.topNode, mode);\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) pointing into this tree\n    at the given position and side (see\n    [`moveTo`](#common.TreeCursor.moveTo).\n    */\n    cursorAt(pos, side = 0, mode = 0) {\n        let scope = CachedNode.get(this) || this.topNode;\n        let cursor = new TreeCursor(scope);\n        cursor.moveTo(pos, side);\n        CachedNode.set(this, cursor._tree);\n        return cursor;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) object for the top of the\n    tree.\n    */\n    get topNode() {\n        return new TreeNode(this, 0, 0, null);\n    }\n    /**\n    Get the [syntax node](#common.SyntaxNode) at the given position.\n    If `side` is -1, this will move into nodes that end at the\n    position. If 1, it'll move into nodes that start at the\n    position. With 0, it'll only enter nodes that cover the position\n    from both sides.\n    \n    Note that this will not enter\n    [overlays](#common.MountedTree.overlay), and you often want\n    [`resolveInner`](#common.Tree.resolveInner) instead.\n    */\n    resolve(pos, side = 0) {\n        let node = resolveNode(CachedNode.get(this) || this.topNode, pos, side, false);\n        CachedNode.set(this, node);\n        return node;\n    }\n    /**\n    Like [`resolve`](#common.Tree.resolve), but will enter\n    [overlaid](#common.MountedTree.overlay) nodes, producing a syntax node\n    pointing into the innermost overlaid tree at the given position\n    (with parent links going through all parent structure, including\n    the host trees).\n    */\n    resolveInner(pos, side = 0) {\n        let node = resolveNode(CachedInnerNode.get(this) || this.topNode, pos, side, true);\n        CachedInnerNode.set(this, node);\n        return node;\n    }\n    /**\n    In some situations, it can be useful to iterate through all\n    nodes around a position, including those in overlays that don't\n    directly cover the position. This method gives you an iterator\n    that will produce all nodes, from small to big, around the given\n    position.\n    */\n    resolveStack(pos, side = 0) {\n        return stackIterator(this, pos, side);\n    }\n    /**\n    Iterate over the tree and its children, calling `enter` for any\n    node that touches the `from`/`to` region (if given) before\n    running over such a node's children, and `leave` (if given) when\n    leaving the node. When `enter` returns `false`, that node will\n    not have its children iterated over (or `leave` called).\n    */\n    iterate(spec) {\n        let { enter, leave, from = 0, to = this.length } = spec;\n        let mode = spec.mode || 0, anon = (mode & IterMode.IncludeAnonymous) > 0;\n        for (let c = this.cursor(mode | IterMode.IncludeAnonymous);;) {\n            let entered = false;\n            if (c.from <= to && c.to >= from && (!anon && c.type.isAnonymous || enter(c) !== false)) {\n                if (c.firstChild())\n                    continue;\n                entered = true;\n            }\n            for (;;) {\n                if (entered && leave && (anon || !c.type.isAnonymous))\n                    leave(c);\n                if (c.nextSibling())\n                    break;\n                if (!c.parent())\n                    return;\n                entered = true;\n            }\n        }\n    }\n    /**\n    Get the value of the given [node prop](#common.NodeProp) for this\n    node. Works with both per-node and per-type props.\n    */\n    prop(prop) {\n        return !prop.perNode ? this.type.prop(prop) : this.props ? this.props[prop.id] : undefined;\n    }\n    /**\n    Returns the node's [per-node props](#common.NodeProp.perNode) in a\n    format that can be passed to the [`Tree`](#common.Tree)\n    constructor.\n    */\n    get propValues() {\n        let result = [];\n        if (this.props)\n            for (let id in this.props)\n                result.push([+id, this.props[id]]);\n        return result;\n    }\n    /**\n    Balance the direct children of this tree, producing a copy of\n    which may have children grouped into subtrees with type\n    [`NodeType.none`](#common.NodeType^none).\n    */\n    balance(config = {}) {\n        return this.children.length <= 8 /* Balance.BranchFactor */ ? this :\n            balanceRange(NodeType.none, this.children, this.positions, 0, this.children.length, 0, this.length, (children, positions, length) => new Tree(this.type, children, positions, length, this.propValues), config.makeTree || ((children, positions, length) => new Tree(NodeType.none, children, positions, length)));\n    }\n    /**\n    Build a tree from a postfix-ordered buffer of node information,\n    or a cursor over such a buffer.\n    */\n    static build(data) { return buildTree(data); }\n}\n/**\nThe empty tree\n*/\nTree.empty = new Tree(NodeType.none, [], [], 0);\nclass FlatBufferCursor {\n    constructor(buffer, index) {\n        this.buffer = buffer;\n        this.index = index;\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    get pos() { return this.index; }\n    next() { this.index -= 4; }\n    fork() { return new FlatBufferCursor(this.buffer, this.index); }\n}\n/**\nTree buffers contain (type, start, end, endIndex) quads for each\nnode. In such a buffer, nodes are stored in prefix order (parents\nbefore children, with the endIndex of the parent indicating which\nchildren belong to it).\n*/\nclass TreeBuffer {\n    /**\n    Create a tree buffer.\n    */\n    constructor(\n    /**\n    The buffer's content.\n    */\n    buffer, \n    /**\n    The total length of the group of nodes in the buffer.\n    */\n    length, \n    /**\n    The node set used in this buffer.\n    */\n    set) {\n        this.buffer = buffer;\n        this.length = length;\n        this.set = set;\n    }\n    /**\n    @internal\n    */\n    get type() { return NodeType.none; }\n    /**\n    @internal\n    */\n    toString() {\n        let result = [];\n        for (let index = 0; index < this.buffer.length;) {\n            result.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result.join(\",\");\n    }\n    /**\n    @internal\n    */\n    childString(index) {\n        let id = this.buffer[index], endIndex = this.buffer[index + 3];\n        let type = this.set.types[id], result = type.name;\n        if (/\\W/.test(result) && !type.isError)\n            result = JSON.stringify(result);\n        index += 4;\n        if (endIndex == index)\n            return result;\n        let children = [];\n        while (index < endIndex) {\n            children.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result + \"(\" + children.join(\",\") + \")\";\n    }\n    /**\n    @internal\n    */\n    findChild(startIndex, endIndex, dir, pos, side) {\n        let { buffer } = this, pick = -1;\n        for (let i = startIndex; i != endIndex; i = buffer[i + 3]) {\n            if (checkSide(side, pos, buffer[i + 1], buffer[i + 2])) {\n                pick = i;\n                if (dir > 0)\n                    break;\n            }\n        }\n        return pick;\n    }\n    /**\n    @internal\n    */\n    slice(startI, endI, from) {\n        let b = this.buffer;\n        let copy = new Uint16Array(endI - startI), len = 0;\n        for (let i = startI, j = 0; i < endI;) {\n            copy[j++] = b[i++];\n            copy[j++] = b[i++] - from;\n            let to = copy[j++] = b[i++] - from;\n            copy[j++] = b[i++] - startI;\n            len = Math.max(len, to);\n        }\n        return new TreeBuffer(copy, len, this.set);\n    }\n}\nfunction checkSide(side, pos, from, to) {\n    switch (side) {\n        case -2 /* Side.Before */: return from < pos;\n        case -1 /* Side.AtOrBefore */: return to >= pos && from < pos;\n        case 0 /* Side.Around */: return from < pos && to > pos;\n        case 1 /* Side.AtOrAfter */: return from <= pos && to > pos;\n        case 2 /* Side.After */: return to > pos;\n        case 4 /* Side.DontCare */: return true;\n    }\n}\nfunction resolveNode(node, pos, side, overlays) {\n    var _a;\n    // Move up to a node that actually holds the position, if possible\n    while (node.from == node.to ||\n        (side < 1 ? node.from >= pos : node.from > pos) ||\n        (side > -1 ? node.to <= pos : node.to < pos)) {\n        let parent = !overlays && node instanceof TreeNode && node.index < 0 ? null : node.parent;\n        if (!parent)\n            return node;\n        node = parent;\n    }\n    let mode = overlays ? 0 : IterMode.IgnoreOverlays;\n    // Must go up out of overlays when those do not overlap with pos\n    if (overlays)\n        for (let scan = node, parent = scan.parent; parent; scan = parent, parent = scan.parent) {\n            if (scan instanceof TreeNode && scan.index < 0 && ((_a = parent.enter(pos, side, mode)) === null || _a === void 0 ? void 0 : _a.from) != scan.from)\n                node = parent;\n        }\n    for (;;) {\n        let inner = node.enter(pos, side, mode);\n        if (!inner)\n            return node;\n        node = inner;\n    }\n}\nclass BaseNode {\n    cursor(mode = 0) { return new TreeCursor(this, mode); }\n    getChild(type, before = null, after = null) {\n        let r = getChildren(this, type, before, after);\n        return r.length ? r[0] : null;\n    }\n    getChildren(type, before = null, after = null) {\n        return getChildren(this, type, before, after);\n    }\n    resolve(pos, side = 0) {\n        return resolveNode(this, pos, side, false);\n    }\n    resolveInner(pos, side = 0) {\n        return resolveNode(this, pos, side, true);\n    }\n    matchContext(context) {\n        return matchNodeContext(this.parent, context);\n    }\n    enterUnfinishedNodesBefore(pos) {\n        let scan = this.childBefore(pos), node = this;\n        while (scan) {\n            let last = scan.lastChild;\n            if (!last || last.to != scan.to)\n                break;\n            if (last.type.isError && last.from == last.to) {\n                node = scan;\n                scan = last.prevSibling;\n            }\n            else {\n                scan = last;\n            }\n        }\n        return node;\n    }\n    get node() { return this; }\n    get next() { return this.parent; }\n}\nclass TreeNode extends BaseNode {\n    constructor(_tree, from, \n    // Index in parent node, set to -1 if the node is not a direct child of _parent.node (overlay)\n    index, _parent) {\n        super();\n        this._tree = _tree;\n        this.from = from;\n        this.index = index;\n        this._parent = _parent;\n    }\n    get type() { return this._tree.type; }\n    get name() { return this._tree.type.name; }\n    get to() { return this.from + this._tree.length; }\n    nextChild(i, dir, pos, side, mode = 0) {\n        for (let parent = this;;) {\n            for (let { children, positions } = parent._tree, e = dir > 0 ? children.length : -1; i != e; i += dir) {\n                let next = children[i], start = positions[i] + parent.from;\n                if (!checkSide(side, pos, start, start + next.length))\n                    continue;\n                if (next instanceof TreeBuffer) {\n                    if (mode & IterMode.ExcludeBuffers)\n                        continue;\n                    let index = next.findChild(0, next.buffer.length, dir, pos - start, side);\n                    if (index > -1)\n                        return new BufferNode(new BufferContext(parent, next, i, start), null, index);\n                }\n                else if ((mode & IterMode.IncludeAnonymous) || (!next.type.isAnonymous || hasChild(next))) {\n                    let mounted;\n                    if (!(mode & IterMode.IgnoreMounts) && (mounted = MountedTree.get(next)) && !mounted.overlay)\n                        return new TreeNode(mounted.tree, start, i, parent);\n                    let inner = new TreeNode(next, start, i, parent);\n                    return (mode & IterMode.IncludeAnonymous) || !inner.type.isAnonymous ? inner\n                        : inner.nextChild(dir < 0 ? next.children.length - 1 : 0, dir, pos, side);\n                }\n            }\n            if ((mode & IterMode.IncludeAnonymous) || !parent.type.isAnonymous)\n                return null;\n            if (parent.index >= 0)\n                i = parent.index + dir;\n            else\n                i = dir < 0 ? -1 : parent._parent._tree.children.length;\n            parent = parent._parent;\n            if (!parent)\n                return null;\n        }\n    }\n    get firstChild() { return this.nextChild(0, 1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.nextChild(this._tree.children.length - 1, -1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.nextChild(0, 1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.nextChild(this._tree.children.length - 1, -1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        let mounted;\n        if (!(mode & IterMode.IgnoreOverlays) && (mounted = MountedTree.get(this._tree)) && mounted.overlay) {\n            let rPos = pos - this.from;\n            for (let { from, to } of mounted.overlay) {\n                if ((side > 0 ? from <= rPos : from < rPos) &&\n                    (side < 0 ? to >= rPos : to > rPos))\n                    return new TreeNode(mounted.tree, mounted.overlay[0].from + this.from, -1, this);\n            }\n        }\n        return this.nextChild(0, 1, pos, side, mode);\n    }\n    nextSignificantParent() {\n        let val = this;\n        while (val.type.isAnonymous && val._parent)\n            val = val._parent;\n        return val;\n    }\n    get parent() {\n        return this._parent ? this._parent.nextSignificantParent() : null;\n    }\n    get nextSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index + 1, 1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get prevSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index - 1, -1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get tree() { return this._tree; }\n    toTree() { return this._tree; }\n    /**\n    @internal\n    */\n    toString() { return this._tree.toString(); }\n}\nfunction getChildren(node, type, before, after) {\n    let cur = node.cursor(), result = [];\n    if (!cur.firstChild())\n        return result;\n    if (before != null)\n        for (let found = false; !found;) {\n            found = cur.type.is(before);\n            if (!cur.nextSibling())\n                return result;\n        }\n    for (;;) {\n        if (after != null && cur.type.is(after))\n            return result;\n        if (cur.type.is(type))\n            result.push(cur.node);\n        if (!cur.nextSibling())\n            return after == null ? result : [];\n    }\n}\nfunction matchNodeContext(node, context, i = context.length - 1) {\n    for (let p = node; i >= 0; p = p.parent) {\n        if (!p)\n            return false;\n        if (!p.type.isAnonymous) {\n            if (context[i] && context[i] != p.name)\n                return false;\n            i--;\n        }\n    }\n    return true;\n}\nclass BufferContext {\n    constructor(parent, buffer, index, start) {\n        this.parent = parent;\n        this.buffer = buffer;\n        this.index = index;\n        this.start = start;\n    }\n}\nclass BufferNode extends BaseNode {\n    get name() { return this.type.name; }\n    get from() { return this.context.start + this.context.buffer.buffer[this.index + 1]; }\n    get to() { return this.context.start + this.context.buffer.buffer[this.index + 2]; }\n    constructor(context, _parent, index) {\n        super();\n        this.context = context;\n        this._parent = _parent;\n        this.index = index;\n        this.type = context.buffer.set.types[context.buffer.buffer[index]];\n    }\n    child(dir, pos, side) {\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get firstChild() { return this.child(1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.child(-1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.child(1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.child(-1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        if (mode & IterMode.ExcludeBuffers)\n            return null;\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], side > 0 ? 1 : -1, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get parent() {\n        return this._parent || this.context.parent.nextSignificantParent();\n    }\n    externalSibling(dir) {\n        return this._parent ? null : this.context.parent.nextChild(this.context.index + dir, dir, 0, 4 /* Side.DontCare */);\n    }\n    get nextSibling() {\n        let { buffer } = this.context;\n        let after = buffer.buffer[this.index + 3];\n        if (after < (this._parent ? buffer.buffer[this._parent.index + 3] : buffer.buffer.length))\n            return new BufferNode(this.context, this._parent, after);\n        return this.externalSibling(1);\n    }\n    get prevSibling() {\n        let { buffer } = this.context;\n        let parentStart = this._parent ? this._parent.index + 4 : 0;\n        if (this.index == parentStart)\n            return this.externalSibling(-1);\n        return new BufferNode(this.context, this._parent, buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n    }\n    get tree() { return null; }\n    toTree() {\n        let children = [], positions = [];\n        let { buffer } = this.context;\n        let startI = this.index + 4, endI = buffer.buffer[this.index + 3];\n        if (endI > startI) {\n            let from = buffer.buffer[this.index + 1];\n            children.push(buffer.slice(startI, endI, from));\n            positions.push(0);\n        }\n        return new Tree(this.type, children, positions, this.to - this.from);\n    }\n    /**\n    @internal\n    */\n    toString() { return this.context.buffer.childString(this.index); }\n}\nfunction iterStack(heads) {\n    if (!heads.length)\n        return null;\n    let pick = 0, picked = heads[0];\n    for (let i = 1; i < heads.length; i++) {\n        let node = heads[i];\n        if (node.from > picked.from || node.to < picked.to) {\n            picked = node;\n            pick = i;\n        }\n    }\n    let next = picked instanceof TreeNode && picked.index < 0 ? null : picked.parent;\n    let newHeads = heads.slice();\n    if (next)\n        newHeads[pick] = next;\n    else\n        newHeads.splice(pick, 1);\n    return new StackIterator(newHeads, picked);\n}\nclass StackIterator {\n    constructor(heads, node) {\n        this.heads = heads;\n        this.node = node;\n    }\n    get next() { return iterStack(this.heads); }\n}\nfunction stackIterator(tree, pos, side) {\n    let inner = tree.resolveInner(pos, side), layers = null;\n    for (let scan = inner instanceof TreeNode ? inner : inner.context.parent; scan; scan = scan.parent) {\n        if (scan.index < 0) { // This is an overlay root\n            let parent = scan.parent;\n            (layers || (layers = [inner])).push(parent.resolve(pos, side));\n            scan = parent;\n        }\n        else {\n            let mount = MountedTree.get(scan.tree);\n            // Relevant overlay branching off\n            if (mount && mount.overlay && mount.overlay[0].from <= pos && mount.overlay[mount.overlay.length - 1].to >= pos) {\n                let root = new TreeNode(mount.tree, mount.overlay[0].from + scan.from, -1, scan);\n                (layers || (layers = [inner])).push(resolveNode(root, pos, side, false));\n            }\n        }\n    }\n    return layers ? iterStack(layers) : inner;\n}\n/**\nA tree cursor object focuses on a given node in a syntax tree, and\nallows you to move to adjacent nodes.\n*/\nclass TreeCursor {\n    /**\n    Shorthand for `.type.name`.\n    */\n    get name() { return this.type.name; }\n    /**\n    @internal\n    */\n    constructor(node, \n    /**\n    @internal\n    */\n    mode = 0) {\n        this.mode = mode;\n        /**\n        @internal\n        */\n        this.buffer = null;\n        this.stack = [];\n        /**\n        @internal\n        */\n        this.index = 0;\n        this.bufferNode = null;\n        if (node instanceof TreeNode) {\n            this.yieldNode(node);\n        }\n        else {\n            this._tree = node.context.parent;\n            this.buffer = node.context;\n            for (let n = node._parent; n; n = n._parent)\n                this.stack.unshift(n.index);\n            this.bufferNode = node;\n            this.yieldBuf(node.index);\n        }\n    }\n    yieldNode(node) {\n        if (!node)\n            return false;\n        this._tree = node;\n        this.type = node.type;\n        this.from = node.from;\n        this.to = node.to;\n        return true;\n    }\n    yieldBuf(index, type) {\n        this.index = index;\n        let { start, buffer } = this.buffer;\n        this.type = type || buffer.set.types[buffer.buffer[index]];\n        this.from = start + buffer.buffer[index + 1];\n        this.to = start + buffer.buffer[index + 2];\n        return true;\n    }\n    /**\n    @internal\n    */\n    yield(node) {\n        if (!node)\n            return false;\n        if (node instanceof TreeNode) {\n            this.buffer = null;\n            return this.yieldNode(node);\n        }\n        this.buffer = node.context;\n        return this.yieldBuf(node.index, node.type);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.buffer ? this.buffer.buffer.childString(this.index) : this._tree.toString();\n    }\n    /**\n    @internal\n    */\n    enterChild(dir, pos, side) {\n        if (!this.buffer)\n            return this.yield(this._tree.nextChild(dir < 0 ? this._tree._tree.children.length - 1 : 0, dir, pos, side, this.mode));\n        let { buffer } = this.buffer;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.buffer.start, side);\n        if (index < 0)\n            return false;\n        this.stack.push(this.index);\n        return this.yieldBuf(index);\n    }\n    /**\n    Move the cursor to this node's first child. When this returns\n    false, the node has no child, and the cursor has not been moved.\n    */\n    firstChild() { return this.enterChild(1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to this node's last child.\n    */\n    lastChild() { return this.enterChild(-1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to the first child that ends after `pos`.\n    */\n    childAfter(pos) { return this.enterChild(1, pos, 2 /* Side.After */); }\n    /**\n    Move to the last child that starts before `pos`.\n    */\n    childBefore(pos) { return this.enterChild(-1, pos, -2 /* Side.Before */); }\n    /**\n    Move the cursor to the child around `pos`. If side is -1 the\n    child may end at that position, when 1 it may start there. This\n    will also enter [overlaid](#common.MountedTree.overlay)\n    [mounted](#common.NodeProp^mounted) trees unless `overlays` is\n    set to false.\n    */\n    enter(pos, side, mode = this.mode) {\n        if (!this.buffer)\n            return this.yield(this._tree.enter(pos, side, mode));\n        return mode & IterMode.ExcludeBuffers ? false : this.enterChild(1, pos, side);\n    }\n    /**\n    Move to the node's parent node, if this isn't the top node.\n    */\n    parent() {\n        if (!this.buffer)\n            return this.yieldNode((this.mode & IterMode.IncludeAnonymous) ? this._tree._parent : this._tree.parent);\n        if (this.stack.length)\n            return this.yieldBuf(this.stack.pop());\n        let parent = (this.mode & IterMode.IncludeAnonymous) ? this.buffer.parent : this.buffer.parent.nextSignificantParent();\n        this.buffer = null;\n        return this.yieldNode(parent);\n    }\n    /**\n    @internal\n    */\n    sibling(dir) {\n        if (!this.buffer)\n            return !this._tree._parent ? false\n                : this.yield(this._tree.index < 0 ? null\n                    : this._tree._parent.nextChild(this._tree.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode));\n        let { buffer } = this.buffer, d = this.stack.length - 1;\n        if (dir < 0) {\n            let parentStart = d < 0 ? 0 : this.stack[d] + 4;\n            if (this.index != parentStart)\n                return this.yieldBuf(buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n        }\n        else {\n            let after = buffer.buffer[this.index + 3];\n            if (after < (d < 0 ? buffer.buffer.length : buffer.buffer[this.stack[d] + 3]))\n                return this.yieldBuf(after);\n        }\n        return d < 0 ? this.yield(this.buffer.parent.nextChild(this.buffer.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode)) : false;\n    }\n    /**\n    Move to this node's next sibling, if any.\n    */\n    nextSibling() { return this.sibling(1); }\n    /**\n    Move to this node's previous sibling, if any.\n    */\n    prevSibling() { return this.sibling(-1); }\n    atLastNode(dir) {\n        let index, parent, { buffer } = this;\n        if (buffer) {\n            if (dir > 0) {\n                if (this.index < buffer.buffer.buffer.length)\n                    return false;\n            }\n            else {\n                for (let i = 0; i < this.index; i++)\n                    if (buffer.buffer.buffer[i + 3] < this.index)\n                        return false;\n            }\n            ({ index, parent } = buffer);\n        }\n        else {\n            ({ index, _parent: parent } = this._tree);\n        }\n        for (; parent; { index, _parent: parent } = parent) {\n            if (index > -1)\n                for (let i = index + dir, e = dir < 0 ? -1 : parent._tree.children.length; i != e; i += dir) {\n                    let child = parent._tree.children[i];\n                    if ((this.mode & IterMode.IncludeAnonymous) ||\n                        child instanceof TreeBuffer ||\n                        !child.type.isAnonymous ||\n                        hasChild(child))\n                        return false;\n                }\n        }\n        return true;\n    }\n    move(dir, enter) {\n        if (enter && this.enterChild(dir, 0, 4 /* Side.DontCare */))\n            return true;\n        for (;;) {\n            if (this.sibling(dir))\n                return true;\n            if (this.atLastNode(dir) || !this.parent())\n                return false;\n        }\n    }\n    /**\n    Move to the next node in a\n    [pre-order](https://en.wikipedia.org/wiki/Tree_traversal#Pre-order,_NLR)\n    traversal, going from a node to its first child or, if the\n    current node is empty or `enter` is false, its next sibling or\n    the next sibling of the first parent node that has one.\n    */\n    next(enter = true) { return this.move(1, enter); }\n    /**\n    Move to the next node in a last-to-first pre-order traversal. A\n    node is followed by its last child or, if it has none, its\n    previous sibling or the previous sibling of the first parent\n    node that has one.\n    */\n    prev(enter = true) { return this.move(-1, enter); }\n    /**\n    Move the cursor to the innermost node that covers `pos`. If\n    `side` is -1, it will enter nodes that end at `pos`. If it is 1,\n    it will enter nodes that start at `pos`.\n    */\n    moveTo(pos, side = 0) {\n        // Move up to a node that actually holds the position, if possible\n        while (this.from == this.to ||\n            (side < 1 ? this.from >= pos : this.from > pos) ||\n            (side > -1 ? this.to <= pos : this.to < pos))\n            if (!this.parent())\n                break;\n        // Then scan down into child nodes as far as possible\n        while (this.enterChild(1, pos, side)) { }\n        return this;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) at the cursor's current\n    position.\n    */\n    get node() {\n        if (!this.buffer)\n            return this._tree;\n        let cache = this.bufferNode, result = null, depth = 0;\n        if (cache && cache.context == this.buffer) {\n            scan: for (let index = this.index, d = this.stack.length; d >= 0;) {\n                for (let c = cache; c; c = c._parent)\n                    if (c.index == index) {\n                        if (index == this.index)\n                            return c;\n                        result = c;\n                        depth = d + 1;\n                        break scan;\n                    }\n                index = this.stack[--d];\n            }\n        }\n        for (let i = depth; i < this.stack.length; i++)\n            result = new BufferNode(this.buffer, result, this.stack[i]);\n        return this.bufferNode = new BufferNode(this.buffer, result, this.index);\n    }\n    /**\n    Get the [tree](#common.Tree) that represents the current node, if\n    any. Will return null when the node is in a [tree\n    buffer](#common.TreeBuffer).\n    */\n    get tree() {\n        return this.buffer ? null : this._tree._tree;\n    }\n    /**\n    Iterate over the current node and all its descendants, calling\n    `enter` when entering a node and `leave`, if given, when leaving\n    one. When `enter` returns `false`, any children of that node are\n    skipped, and `leave` isn't called for it.\n    */\n    iterate(enter, leave) {\n        for (let depth = 0;;) {\n            let mustLeave = false;\n            if (this.type.isAnonymous || enter(this) !== false) {\n                if (this.firstChild()) {\n                    depth++;\n                    continue;\n                }\n                if (!this.type.isAnonymous)\n                    mustLeave = true;\n            }\n            for (;;) {\n                if (mustLeave && leave)\n                    leave(this);\n                mustLeave = this.type.isAnonymous;\n                if (!depth)\n                    return;\n                if (this.nextSibling())\n                    break;\n                this.parent();\n                depth--;\n                mustLeave = true;\n            }\n        }\n    }\n    /**\n    Test whether the current node matches a given context—a sequence\n    of direct parent node names. Empty strings in the context array\n    are treated as wildcards.\n    */\n    matchContext(context) {\n        if (!this.buffer)\n            return matchNodeContext(this.node.parent, context);\n        let { buffer } = this.buffer, { types } = buffer.set;\n        for (let i = context.length - 1, d = this.stack.length - 1; i >= 0; d--) {\n            if (d < 0)\n                return matchNodeContext(this._tree, context, i);\n            let type = types[buffer.buffer[this.stack[d]]];\n            if (!type.isAnonymous) {\n                if (context[i] && context[i] != type.name)\n                    return false;\n                i--;\n            }\n        }\n        return true;\n    }\n}\nfunction hasChild(tree) {\n    return tree.children.some(ch => ch instanceof TreeBuffer || !ch.type.isAnonymous || hasChild(ch));\n}\nfunction buildTree(data) {\n    var _a;\n    let { buffer, nodeSet, maxBufferLength = DefaultBufferLength, reused = [], minRepeatType = nodeSet.types.length } = data;\n    let cursor = Array.isArray(buffer) ? new FlatBufferCursor(buffer, buffer.length) : buffer;\n    let types = nodeSet.types;\n    let contextHash = 0, lookAhead = 0;\n    function takeNode(parentStart, minPos, children, positions, inRepeat, depth) {\n        let { id, start, end, size } = cursor;\n        let lookAheadAtStart = lookAhead, contextAtStart = contextHash;\n        while (size < 0) {\n            cursor.next();\n            if (size == -1 /* SpecialRecord.Reuse */) {\n                let node = reused[id];\n                children.push(node);\n                positions.push(start - parentStart);\n                return;\n            }\n            else if (size == -3 /* SpecialRecord.ContextChange */) { // Context change\n                contextHash = id;\n                return;\n            }\n            else if (size == -4 /* SpecialRecord.LookAhead */) {\n                lookAhead = id;\n                return;\n            }\n            else {\n                throw new RangeError(`Unrecognized record size: ${size}`);\n            }\n        }\n        let type = types[id], node, buffer;\n        let startPos = start - parentStart;\n        if (end - start <= maxBufferLength && (buffer = findBufferSize(cursor.pos - minPos, inRepeat))) {\n            // Small enough for a buffer, and no reused nodes inside\n            let data = new Uint16Array(buffer.size - buffer.skip);\n            let endPos = cursor.pos - buffer.size, index = data.length;\n            while (cursor.pos > endPos)\n                index = copyToBuffer(buffer.start, data, index);\n            node = new TreeBuffer(data, end - buffer.start, nodeSet);\n            startPos = buffer.start - parentStart;\n        }\n        else { // Make it a node\n            let endPos = cursor.pos - size;\n            cursor.next();\n            let localChildren = [], localPositions = [];\n            let localInRepeat = id >= minRepeatType ? id : -1;\n            let lastGroup = 0, lastEnd = end;\n            while (cursor.pos > endPos) {\n                if (localInRepeat >= 0 && cursor.id == localInRepeat && cursor.size >= 0) {\n                    if (cursor.end <= lastEnd - maxBufferLength) {\n                        makeRepeatLeaf(localChildren, localPositions, start, lastGroup, cursor.end, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n                        lastGroup = localChildren.length;\n                        lastEnd = cursor.end;\n                    }\n                    cursor.next();\n                }\n                else if (depth > 2500 /* CutOff.Depth */) {\n                    takeFlatNode(start, endPos, localChildren, localPositions);\n                }\n                else {\n                    takeNode(start, endPos, localChildren, localPositions, localInRepeat, depth + 1);\n                }\n            }\n            if (localInRepeat >= 0 && lastGroup > 0 && lastGroup < localChildren.length)\n                makeRepeatLeaf(localChildren, localPositions, start, lastGroup, start, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n            localChildren.reverse();\n            localPositions.reverse();\n            if (localInRepeat > -1 && lastGroup > 0) {\n                let make = makeBalanced(type, contextAtStart);\n                node = balanceRange(type, localChildren, localPositions, 0, localChildren.length, 0, end - start, make, make);\n            }\n            else {\n                node = makeTree(type, localChildren, localPositions, end - start, lookAheadAtStart - end, contextAtStart);\n            }\n        }\n        children.push(node);\n        positions.push(startPos);\n    }\n    function takeFlatNode(parentStart, minPos, children, positions) {\n        let nodes = []; // Temporary, inverted array of leaf nodes found, with absolute positions\n        let nodeCount = 0, stopAt = -1;\n        while (cursor.pos > minPos) {\n            let { id, start, end, size } = cursor;\n            if (size > 4) { // Not a leaf\n                cursor.next();\n            }\n            else if (stopAt > -1 && start < stopAt) {\n                break;\n            }\n            else {\n                if (stopAt < 0)\n                    stopAt = end - maxBufferLength;\n                nodes.push(id, start, end);\n                nodeCount++;\n                cursor.next();\n            }\n        }\n        if (nodeCount) {\n            let buffer = new Uint16Array(nodeCount * 4);\n            let start = nodes[nodes.length - 2];\n            for (let i = nodes.length - 3, j = 0; i >= 0; i -= 3) {\n                buffer[j++] = nodes[i];\n                buffer[j++] = nodes[i + 1] - start;\n                buffer[j++] = nodes[i + 2] - start;\n                buffer[j++] = j;\n            }\n            children.push(new TreeBuffer(buffer, nodes[2] - start, nodeSet));\n            positions.push(start - parentStart);\n        }\n    }\n    function makeBalanced(type, contextHash) {\n        return (children, positions, length) => {\n            let lookAhead = 0, lastI = children.length - 1, last, lookAheadProp;\n            if (lastI >= 0 && (last = children[lastI]) instanceof Tree) {\n                if (!lastI && last.type == type && last.length == length)\n                    return last;\n                if (lookAheadProp = last.prop(NodeProp.lookAhead))\n                    lookAhead = positions[lastI] + last.length + lookAheadProp;\n            }\n            return makeTree(type, children, positions, length, lookAhead, contextHash);\n        };\n    }\n    function makeRepeatLeaf(children, positions, base, i, from, to, type, lookAhead, contextHash) {\n        let localChildren = [], localPositions = [];\n        while (children.length > i) {\n            localChildren.push(children.pop());\n            localPositions.push(positions.pop() + base - from);\n        }\n        children.push(makeTree(nodeSet.types[type], localChildren, localPositions, to - from, lookAhead - to, contextHash));\n        positions.push(from - base);\n    }\n    function makeTree(type, children, positions, length, lookAhead, contextHash, props) {\n        if (contextHash) {\n            let pair = [NodeProp.contextHash, contextHash];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        if (lookAhead > 25) {\n            let pair = [NodeProp.lookAhead, lookAhead];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        return new Tree(type, children, positions, length, props);\n    }\n    function findBufferSize(maxSize, inRepeat) {\n        // Scan through the buffer to find previous siblings that fit\n        // together in a TreeBuffer, and don't contain any reused nodes\n        // (which can't be stored in a buffer).\n        // If `inRepeat` is > -1, ignore node boundaries of that type for\n        // nesting, but make sure the end falls either at the start\n        // (`maxSize`) or before such a node.\n        let fork = cursor.fork();\n        let size = 0, start = 0, skip = 0, minStart = fork.end - maxBufferLength;\n        let result = { size: 0, start: 0, skip: 0 };\n        scan: for (let minPos = fork.pos - maxSize; fork.pos > minPos;) {\n            let nodeSize = fork.size;\n            // Pretend nested repeat nodes of the same type don't exist\n            if (fork.id == inRepeat && nodeSize >= 0) {\n                // Except that we store the current state as a valid return\n                // value.\n                result.size = size;\n                result.start = start;\n                result.skip = skip;\n                skip += 4;\n                size += 4;\n                fork.next();\n                continue;\n            }\n            let startPos = fork.pos - nodeSize;\n            if (nodeSize < 0 || startPos < minPos || fork.start < minStart)\n                break;\n            let localSkipped = fork.id >= minRepeatType ? 4 : 0;\n            let nodeStart = fork.start;\n            fork.next();\n            while (fork.pos > startPos) {\n                if (fork.size < 0) {\n                    if (fork.size == -3 /* SpecialRecord.ContextChange */)\n                        localSkipped += 4;\n                    else\n                        break scan;\n                }\n                else if (fork.id >= minRepeatType) {\n                    localSkipped += 4;\n                }\n                fork.next();\n            }\n            start = nodeStart;\n            size += nodeSize;\n            skip += localSkipped;\n        }\n        if (inRepeat < 0 || size == maxSize) {\n            result.size = size;\n            result.start = start;\n            result.skip = skip;\n        }\n        return result.size > 4 ? result : undefined;\n    }\n    function copyToBuffer(bufferStart, buffer, index) {\n        let { id, start, end, size } = cursor;\n        cursor.next();\n        if (size >= 0 && id < minRepeatType) {\n            let startIndex = index;\n            if (size > 4) {\n                let endPos = cursor.pos - (size - 4);\n                while (cursor.pos > endPos)\n                    index = copyToBuffer(bufferStart, buffer, index);\n            }\n            buffer[--index] = startIndex;\n            buffer[--index] = end - bufferStart;\n            buffer[--index] = start - bufferStart;\n            buffer[--index] = id;\n        }\n        else if (size == -3 /* SpecialRecord.ContextChange */) {\n            contextHash = id;\n        }\n        else if (size == -4 /* SpecialRecord.LookAhead */) {\n            lookAhead = id;\n        }\n        return index;\n    }\n    let children = [], positions = [];\n    while (cursor.pos > 0)\n        takeNode(data.start || 0, data.bufferStart || 0, children, positions, -1, 0);\n    let length = (_a = data.length) !== null && _a !== void 0 ? _a : (children.length ? positions[0] + children[0].length : 0);\n    return new Tree(types[data.topID], children.reverse(), positions.reverse(), length);\n}\nconst nodeSizeCache = new WeakMap;\nfunction nodeSize(balanceType, node) {\n    if (!balanceType.isAnonymous || node instanceof TreeBuffer || node.type != balanceType)\n        return 1;\n    let size = nodeSizeCache.get(node);\n    if (size == null) {\n        size = 1;\n        for (let child of node.children) {\n            if (child.type != balanceType || !(child instanceof Tree)) {\n                size = 1;\n                break;\n            }\n            size += nodeSize(balanceType, child);\n        }\n        nodeSizeCache.set(node, size);\n    }\n    return size;\n}\nfunction balanceRange(\n// The type the balanced tree's inner nodes.\nbalanceType, \n// The direct children and their positions\nchildren, positions, \n// The index range in children/positions to use\nfrom, to, \n// The start position of the nodes, relative to their parent.\nstart, \n// Length of the outer node\nlength, \n// Function to build the top node of the balanced tree\nmkTop, \n// Function to build internal nodes for the balanced tree\nmkTree) {\n    let total = 0;\n    for (let i = from; i < to; i++)\n        total += nodeSize(balanceType, children[i]);\n    let maxChild = Math.ceil((total * 1.5) / 8 /* Balance.BranchFactor */);\n    let localChildren = [], localPositions = [];\n    function divide(children, positions, from, to, offset) {\n        for (let i = from; i < to;) {\n            let groupFrom = i, groupStart = positions[i], groupSize = nodeSize(balanceType, children[i]);\n            i++;\n            for (; i < to; i++) {\n                let nextSize = nodeSize(balanceType, children[i]);\n                if (groupSize + nextSize >= maxChild)\n                    break;\n                groupSize += nextSize;\n            }\n            if (i == groupFrom + 1) {\n                if (groupSize > maxChild) {\n                    let only = children[groupFrom]; // Only trees can have a size > 1\n                    divide(only.children, only.positions, 0, only.children.length, positions[groupFrom] + offset);\n                    continue;\n                }\n                localChildren.push(children[groupFrom]);\n            }\n            else {\n                let length = positions[i - 1] + children[i - 1].length - groupStart;\n                localChildren.push(balanceRange(balanceType, children, positions, groupFrom, i, groupStart, length, null, mkTree));\n            }\n            localPositions.push(groupStart + offset - start);\n        }\n    }\n    divide(children, positions, from, to, 0);\n    return (mkTop || mkTree)(localChildren, localPositions, length);\n}\n/**\nProvides a way to associate values with pieces of trees. As long\nas that part of the tree is reused, the associated values can be\nretrieved from an updated tree.\n*/\nclass NodeWeakMap {\n    constructor() {\n        this.map = new WeakMap();\n    }\n    setBuffer(buffer, index, value) {\n        let inner = this.map.get(buffer);\n        if (!inner)\n            this.map.set(buffer, inner = new Map);\n        inner.set(index, value);\n    }\n    getBuffer(buffer, index) {\n        let inner = this.map.get(buffer);\n        return inner && inner.get(index);\n    }\n    /**\n    Set the value for this syntax node.\n    */\n    set(node, value) {\n        if (node instanceof BufferNode)\n            this.setBuffer(node.context.buffer, node.index, value);\n        else if (node instanceof TreeNode)\n            this.map.set(node.tree, value);\n    }\n    /**\n    Retrieve value for this syntax node, if it exists in the map.\n    */\n    get(node) {\n        return node instanceof BufferNode ? this.getBuffer(node.context.buffer, node.index)\n            : node instanceof TreeNode ? this.map.get(node.tree) : undefined;\n    }\n    /**\n    Set the value for the node that a cursor currently points to.\n    */\n    cursorSet(cursor, value) {\n        if (cursor.buffer)\n            this.setBuffer(cursor.buffer.buffer, cursor.index, value);\n        else\n            this.map.set(cursor.tree, value);\n    }\n    /**\n    Retrieve the value for the node that a cursor currently points\n    to.\n    */\n    cursorGet(cursor) {\n        return cursor.buffer ? this.getBuffer(cursor.buffer.buffer, cursor.index) : this.map.get(cursor.tree);\n    }\n}\n\n/**\nTree fragments are used during [incremental\nparsing](#common.Parser.startParse) to track parts of old trees\nthat can be reused in a new parse. An array of fragments is used\nto track regions of an old tree whose nodes might be reused in new\nparses. Use the static\n[`applyChanges`](#common.TreeFragment^applyChanges) method to\nupdate fragments for document changes.\n*/\nclass TreeFragment {\n    /**\n    Construct a tree fragment. You'll usually want to use\n    [`addTree`](#common.TreeFragment^addTree) and\n    [`applyChanges`](#common.TreeFragment^applyChanges) instead of\n    calling this directly.\n    */\n    constructor(\n    /**\n    The start of the unchanged range pointed to by this fragment.\n    This refers to an offset in the _updated_ document (as opposed\n    to the original tree).\n    */\n    from, \n    /**\n    The end of the unchanged range.\n    */\n    to, \n    /**\n    The tree that this fragment is based on.\n    */\n    tree, \n    /**\n    The offset between the fragment's tree and the document that\n    this fragment can be used against. Add this when going from\n    document to tree positions, subtract it to go from tree to\n    document positions.\n    */\n    offset, openStart = false, openEnd = false) {\n        this.from = from;\n        this.to = to;\n        this.tree = tree;\n        this.offset = offset;\n        this.open = (openStart ? 1 /* Open.Start */ : 0) | (openEnd ? 2 /* Open.End */ : 0);\n    }\n    /**\n    Whether the start of the fragment represents the start of a\n    parse, or the end of a change. (In the second case, it may not\n    be safe to reuse some nodes at the start, depending on the\n    parsing algorithm.)\n    */\n    get openStart() { return (this.open & 1 /* Open.Start */) > 0; }\n    /**\n    Whether the end of the fragment represents the end of a\n    full-document parse, or the start of a change.\n    */\n    get openEnd() { return (this.open & 2 /* Open.End */) > 0; }\n    /**\n    Create a set of fragments from a freshly parsed tree, or update\n    an existing set of fragments by replacing the ones that overlap\n    with a tree with content from the new tree. When `partial` is\n    true, the parse is treated as incomplete, and the resulting\n    fragment has [`openEnd`](#common.TreeFragment.openEnd) set to\n    true.\n    */\n    static addTree(tree, fragments = [], partial = false) {\n        let result = [new TreeFragment(0, tree.length, tree, 0, false, partial)];\n        for (let f of fragments)\n            if (f.to > tree.length)\n                result.push(f);\n        return result;\n    }\n    /**\n    Apply a set of edits to an array of fragments, removing or\n    splitting fragments as necessary to remove edited ranges, and\n    adjusting offsets for fragments that moved.\n    */\n    static applyChanges(fragments, changes, minGap = 128) {\n        if (!changes.length)\n            return fragments;\n        let result = [];\n        let fI = 1, nextF = fragments.length ? fragments[0] : null;\n        for (let cI = 0, pos = 0, off = 0;; cI++) {\n            let nextC = cI < changes.length ? changes[cI] : null;\n            let nextPos = nextC ? nextC.fromA : 1e9;\n            if (nextPos - pos >= minGap)\n                while (nextF && nextF.from < nextPos) {\n                    let cut = nextF;\n                    if (pos >= cut.from || nextPos <= cut.to || off) {\n                        let fFrom = Math.max(cut.from, pos) - off, fTo = Math.min(cut.to, nextPos) - off;\n                        cut = fFrom >= fTo ? null : new TreeFragment(fFrom, fTo, cut.tree, cut.offset + off, cI > 0, !!nextC);\n                    }\n                    if (cut)\n                        result.push(cut);\n                    if (nextF.to > nextPos)\n                        break;\n                    nextF = fI < fragments.length ? fragments[fI++] : null;\n                }\n            if (!nextC)\n                break;\n            pos = nextC.toA;\n            off = nextC.toA - nextC.toB;\n        }\n        return result;\n    }\n}\n/**\nA superclass that parsers should extend.\n*/\nclass Parser {\n    /**\n    Start a parse, returning a [partial parse](#common.PartialParse)\n    object. [`fragments`](#common.TreeFragment) can be passed in to\n    make the parse incremental.\n    \n    By default, the entire input is parsed. You can pass `ranges`,\n    which should be a sorted array of non-empty, non-overlapping\n    ranges, to parse only those ranges. The tree returned in that\n    case will start at `ranges[0].from`.\n    */\n    startParse(input, fragments, ranges) {\n        if (typeof input == \"string\")\n            input = new StringInput(input);\n        ranges = !ranges ? [new Range(0, input.length)] : ranges.length ? ranges.map(r => new Range(r.from, r.to)) : [new Range(0, 0)];\n        return this.createParse(input, fragments || [], ranges);\n    }\n    /**\n    Run a full parse, returning the resulting tree.\n    */\n    parse(input, fragments, ranges) {\n        let parse = this.startParse(input, fragments, ranges);\n        for (;;) {\n            let done = parse.advance();\n            if (done)\n                return done;\n        }\n    }\n}\nclass StringInput {\n    constructor(string) {\n        this.string = string;\n    }\n    get length() { return this.string.length; }\n    chunk(from) { return this.string.slice(from); }\n    get lineChunks() { return false; }\n    read(from, to) { return this.string.slice(from, to); }\n}\n\n/**\nCreate a parse wrapper that, after the inner parse completes,\nscans its tree for mixed language regions with the `nest`\nfunction, runs the resulting [inner parses](#common.NestedParse),\nand then [mounts](#common.NodeProp^mounted) their results onto the\ntree.\n*/\nfunction parseMixed(nest) {\n    return (parse, input, fragments, ranges) => new MixedParse(parse, nest, input, fragments, ranges);\n}\nclass InnerParse {\n    constructor(parser, parse, overlay, target, from) {\n        this.parser = parser;\n        this.parse = parse;\n        this.overlay = overlay;\n        this.target = target;\n        this.from = from;\n    }\n}\nfunction checkRanges(ranges) {\n    if (!ranges.length || ranges.some(r => r.from >= r.to))\n        throw new RangeError(\"Invalid inner parse ranges given: \" + JSON.stringify(ranges));\n}\nclass ActiveOverlay {\n    constructor(parser, predicate, mounts, index, start, target, prev) {\n        this.parser = parser;\n        this.predicate = predicate;\n        this.mounts = mounts;\n        this.index = index;\n        this.start = start;\n        this.target = target;\n        this.prev = prev;\n        this.depth = 0;\n        this.ranges = [];\n    }\n}\nconst stoppedInner = new NodeProp({ perNode: true });\nclass MixedParse {\n    constructor(base, nest, input, fragments, ranges) {\n        this.nest = nest;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.inner = [];\n        this.innerDone = 0;\n        this.baseTree = null;\n        this.stoppedAt = null;\n        this.baseParse = base;\n    }\n    advance() {\n        if (this.baseParse) {\n            let done = this.baseParse.advance();\n            if (!done)\n                return null;\n            this.baseParse = null;\n            this.baseTree = done;\n            this.startInner();\n            if (this.stoppedAt != null)\n                for (let inner of this.inner)\n                    inner.parse.stopAt(this.stoppedAt);\n        }\n        if (this.innerDone == this.inner.length) {\n            let result = this.baseTree;\n            if (this.stoppedAt != null)\n                result = new Tree(result.type, result.children, result.positions, result.length, result.propValues.concat([[stoppedInner, this.stoppedAt]]));\n            return result;\n        }\n        let inner = this.inner[this.innerDone], done = inner.parse.advance();\n        if (done) {\n            this.innerDone++;\n            // This is a somewhat dodgy but super helpful hack where we\n            // patch up nodes created by the inner parse (and thus\n            // presumably not aliased anywhere else) to hold the information\n            // about the inner parse.\n            let props = Object.assign(Object.create(null), inner.target.props);\n            props[NodeProp.mounted.id] = new MountedTree(done, inner.overlay, inner.parser);\n            inner.target.props = props;\n        }\n        return null;\n    }\n    get parsedPos() {\n        if (this.baseParse)\n            return 0;\n        let pos = this.input.length;\n        for (let i = this.innerDone; i < this.inner.length; i++) {\n            if (this.inner[i].from < pos)\n                pos = Math.min(pos, this.inner[i].parse.parsedPos);\n        }\n        return pos;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n        if (this.baseParse)\n            this.baseParse.stopAt(pos);\n        else\n            for (let i = this.innerDone; i < this.inner.length; i++)\n                this.inner[i].parse.stopAt(pos);\n    }\n    startInner() {\n        let fragmentCursor = new FragmentCursor(this.fragments);\n        let overlay = null;\n        let covered = null;\n        let cursor = new TreeCursor(new TreeNode(this.baseTree, this.ranges[0].from, 0, null), IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n        scan: for (let nest, isCovered;;) {\n            let enter = true, range;\n            if (this.stoppedAt != null && cursor.from >= this.stoppedAt) {\n                enter = false;\n            }\n            else if (fragmentCursor.hasNode(cursor)) {\n                if (overlay) {\n                    let match = overlay.mounts.find(m => m.frag.from <= cursor.from && m.frag.to >= cursor.to && m.mount.overlay);\n                    if (match)\n                        for (let r of match.mount.overlay) {\n                            let from = r.from + match.pos, to = r.to + match.pos;\n                            if (from >= cursor.from && to <= cursor.to && !overlay.ranges.some(r => r.from < to && r.to > from))\n                                overlay.ranges.push({ from, to });\n                        }\n                }\n                enter = false;\n            }\n            else if (covered && (isCovered = checkCover(covered.ranges, cursor.from, cursor.to))) {\n                enter = isCovered != 2 /* Cover.Full */;\n            }\n            else if (!cursor.type.isAnonymous && (nest = this.nest(cursor, this.input)) &&\n                (cursor.from < cursor.to || !nest.overlay)) {\n                if (!cursor.tree)\n                    materialize(cursor);\n                let oldMounts = fragmentCursor.findMounts(cursor.from, nest.parser);\n                if (typeof nest.overlay == \"function\") {\n                    overlay = new ActiveOverlay(nest.parser, nest.overlay, oldMounts, this.inner.length, cursor.from, cursor.tree, overlay);\n                }\n                else {\n                    let ranges = punchRanges(this.ranges, nest.overlay ||\n                        (cursor.from < cursor.to ? [new Range(cursor.from, cursor.to)] : []));\n                    if (ranges.length)\n                        checkRanges(ranges);\n                    if (ranges.length || !nest.overlay)\n                        this.inner.push(new InnerParse(nest.parser, ranges.length ? nest.parser.startParse(this.input, enterFragments(oldMounts, ranges), ranges)\n                            : nest.parser.startParse(\"\"), nest.overlay ? nest.overlay.map(r => new Range(r.from - cursor.from, r.to - cursor.from)) : null, cursor.tree, ranges.length ? ranges[0].from : cursor.from));\n                    if (!nest.overlay)\n                        enter = false;\n                    else if (ranges.length)\n                        covered = { ranges, depth: 0, prev: covered };\n                }\n            }\n            else if (overlay && (range = overlay.predicate(cursor))) {\n                if (range === true)\n                    range = new Range(cursor.from, cursor.to);\n                if (range.from < range.to) {\n                    let last = overlay.ranges.length - 1;\n                    if (last >= 0 && overlay.ranges[last].to == range.from)\n                        overlay.ranges[last] = { from: overlay.ranges[last].from, to: range.to };\n                    else\n                        overlay.ranges.push(range);\n                }\n            }\n            if (enter && cursor.firstChild()) {\n                if (overlay)\n                    overlay.depth++;\n                if (covered)\n                    covered.depth++;\n            }\n            else {\n                for (;;) {\n                    if (cursor.nextSibling())\n                        break;\n                    if (!cursor.parent())\n                        break scan;\n                    if (overlay && !--overlay.depth) {\n                        let ranges = punchRanges(this.ranges, overlay.ranges);\n                        if (ranges.length) {\n                            checkRanges(ranges);\n                            this.inner.splice(overlay.index, 0, new InnerParse(overlay.parser, overlay.parser.startParse(this.input, enterFragments(overlay.mounts, ranges), ranges), overlay.ranges.map(r => new Range(r.from - overlay.start, r.to - overlay.start)), overlay.target, ranges[0].from));\n                        }\n                        overlay = overlay.prev;\n                    }\n                    if (covered && !--covered.depth)\n                        covered = covered.prev;\n                }\n            }\n        }\n    }\n}\nfunction checkCover(covered, from, to) {\n    for (let range of covered) {\n        if (range.from >= to)\n            break;\n        if (range.to > from)\n            return range.from <= from && range.to >= to ? 2 /* Cover.Full */ : 1 /* Cover.Partial */;\n    }\n    return 0 /* Cover.None */;\n}\n// Take a piece of buffer and convert it into a stand-alone\n// TreeBuffer.\nfunction sliceBuf(buf, startI, endI, nodes, positions, off) {\n    if (startI < endI) {\n        let from = buf.buffer[startI + 1];\n        nodes.push(buf.slice(startI, endI, from));\n        positions.push(from - off);\n    }\n}\n// This function takes a node that's in a buffer, and converts it, and\n// its parent buffer nodes, into a Tree. This is again acting on the\n// assumption that the trees and buffers have been constructed by the\n// parse that was ran via the mix parser, and thus aren't shared with\n// any other code, making violations of the immutability safe.\nfunction materialize(cursor) {\n    let { node } = cursor, stack = [];\n    let buffer = node.context.buffer;\n    // Scan up to the nearest tree\n    do {\n        stack.push(cursor.index);\n        cursor.parent();\n    } while (!cursor.tree);\n    // Find the index of the buffer in that tree\n    let base = cursor.tree, i = base.children.indexOf(buffer);\n    let buf = base.children[i], b = buf.buffer, newStack = [i];\n    // Split a level in the buffer, putting the nodes before and after\n    // the child that contains `node` into new buffers.\n    function split(startI, endI, type, innerOffset, length, stackPos) {\n        let targetI = stack[stackPos];\n        let children = [], positions = [];\n        sliceBuf(buf, startI, targetI, children, positions, innerOffset);\n        let from = b[targetI + 1], to = b[targetI + 2];\n        newStack.push(children.length);\n        let child = stackPos\n            ? split(targetI + 4, b[targetI + 3], buf.set.types[b[targetI]], from, to - from, stackPos - 1)\n            : node.toTree();\n        children.push(child);\n        positions.push(from - innerOffset);\n        sliceBuf(buf, b[targetI + 3], endI, children, positions, innerOffset);\n        return new Tree(type, children, positions, length);\n    }\n    base.children[i] = split(0, b.length, NodeType.none, 0, buf.length, stack.length - 1);\n    // Move the cursor back to the target node\n    for (let index of newStack) {\n        let tree = cursor.tree.children[index], pos = cursor.tree.positions[index];\n        cursor.yield(new TreeNode(tree, pos + cursor.from, index, cursor._tree));\n    }\n}\nclass StructureCursor {\n    constructor(root, offset) {\n        this.offset = offset;\n        this.done = false;\n        this.cursor = root.cursor(IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n    }\n    // Move to the first node (in pre-order) that starts at or after `pos`.\n    moveTo(pos) {\n        let { cursor } = this, p = pos - this.offset;\n        while (!this.done && cursor.from < p) {\n            if (cursor.to >= pos && cursor.enter(p, 1, IterMode.IgnoreOverlays | IterMode.ExcludeBuffers)) ;\n            else if (!cursor.next(false))\n                this.done = true;\n        }\n    }\n    hasNode(cursor) {\n        this.moveTo(cursor.from);\n        if (!this.done && this.cursor.from + this.offset == cursor.from && this.cursor.tree) {\n            for (let tree = this.cursor.tree;;) {\n                if (tree == cursor.tree)\n                    return true;\n                if (tree.children.length && tree.positions[0] == 0 && tree.children[0] instanceof Tree)\n                    tree = tree.children[0];\n                else\n                    break;\n            }\n        }\n        return false;\n    }\n}\nclass FragmentCursor {\n    constructor(fragments) {\n        var _a;\n        this.fragments = fragments;\n        this.curTo = 0;\n        this.fragI = 0;\n        if (fragments.length) {\n            let first = this.curFrag = fragments[0];\n            this.curTo = (_a = first.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : first.to;\n            this.inner = new StructureCursor(first.tree, -first.offset);\n        }\n        else {\n            this.curFrag = this.inner = null;\n        }\n    }\n    hasNode(node) {\n        while (this.curFrag && node.from >= this.curTo)\n            this.nextFrag();\n        return this.curFrag && this.curFrag.from <= node.from && this.curTo >= node.to && this.inner.hasNode(node);\n    }\n    nextFrag() {\n        var _a;\n        this.fragI++;\n        if (this.fragI == this.fragments.length) {\n            this.curFrag = this.inner = null;\n        }\n        else {\n            let frag = this.curFrag = this.fragments[this.fragI];\n            this.curTo = (_a = frag.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : frag.to;\n            this.inner = new StructureCursor(frag.tree, -frag.offset);\n        }\n    }\n    findMounts(pos, parser) {\n        var _a;\n        let result = [];\n        if (this.inner) {\n            this.inner.cursor.moveTo(pos, 1);\n            for (let pos = this.inner.cursor.node; pos; pos = pos.parent) {\n                let mount = (_a = pos.tree) === null || _a === void 0 ? void 0 : _a.prop(NodeProp.mounted);\n                if (mount && mount.parser == parser) {\n                    for (let i = this.fragI; i < this.fragments.length; i++) {\n                        let frag = this.fragments[i];\n                        if (frag.from >= pos.to)\n                            break;\n                        if (frag.tree == this.curFrag.tree)\n                            result.push({\n                                frag,\n                                pos: pos.from - frag.offset,\n                                mount\n                            });\n                    }\n                }\n            }\n        }\n        return result;\n    }\n}\nfunction punchRanges(outer, ranges) {\n    let copy = null, current = ranges;\n    for (let i = 1, j = 0; i < outer.length; i++) {\n        let gapFrom = outer[i - 1].to, gapTo = outer[i].from;\n        for (; j < current.length; j++) {\n            let r = current[j];\n            if (r.from >= gapTo)\n                break;\n            if (r.to <= gapFrom)\n                continue;\n            if (!copy)\n                current = copy = ranges.slice();\n            if (r.from < gapFrom) {\n                copy[j] = new Range(r.from, gapFrom);\n                if (r.to > gapTo)\n                    copy.splice(j + 1, 0, new Range(gapTo, r.to));\n            }\n            else if (r.to > gapTo) {\n                copy[j--] = new Range(gapTo, r.to);\n            }\n            else {\n                copy.splice(j--, 1);\n            }\n        }\n    }\n    return current;\n}\nfunction findCoverChanges(a, b, from, to) {\n    let iA = 0, iB = 0, inA = false, inB = false, pos = -1e9;\n    let result = [];\n    for (;;) {\n        let nextA = iA == a.length ? 1e9 : inA ? a[iA].to : a[iA].from;\n        let nextB = iB == b.length ? 1e9 : inB ? b[iB].to : b[iB].from;\n        if (inA != inB) {\n            let start = Math.max(pos, from), end = Math.min(nextA, nextB, to);\n            if (start < end)\n                result.push(new Range(start, end));\n        }\n        pos = Math.min(nextA, nextB);\n        if (pos == 1e9)\n            break;\n        if (nextA == pos) {\n            if (!inA)\n                inA = true;\n            else {\n                inA = false;\n                iA++;\n            }\n        }\n        if (nextB == pos) {\n            if (!inB)\n                inB = true;\n            else {\n                inB = false;\n                iB++;\n            }\n        }\n    }\n    return result;\n}\n// Given a number of fragments for the outer tree, and a set of ranges\n// to parse, find fragments for inner trees mounted around those\n// ranges, if any.\nfunction enterFragments(mounts, ranges) {\n    let result = [];\n    for (let { pos, mount, frag } of mounts) {\n        let startPos = pos + (mount.overlay ? mount.overlay[0].from : 0), endPos = startPos + mount.tree.length;\n        let from = Math.max(frag.from, startPos), to = Math.min(frag.to, endPos);\n        if (mount.overlay) {\n            let overlay = mount.overlay.map(r => new Range(r.from + pos, r.to + pos));\n            let changes = findCoverChanges(ranges, overlay, from, to);\n            for (let i = 0, pos = from;; i++) {\n                let last = i == changes.length, end = last ? to : changes[i].from;\n                if (end > pos)\n                    result.push(new TreeFragment(pos, end, mount.tree, -startPos, frag.from >= pos || frag.openStart, frag.to <= end || frag.openEnd));\n                if (last)\n                    break;\n                pos = changes[i].to;\n            }\n        }\n        else {\n            result.push(new TreeFragment(from, to, mount.tree, -startPos, frag.from >= startPos || frag.openStart, frag.to <= endPos || frag.openEnd));\n        }\n    }\n    return result;\n}\n\nexport { DefaultBufferLength, IterMode, MountedTree, NodeProp, NodeSet, NodeType, NodeWeakMap, Parser, Tree, TreeBuffer, TreeCursor, TreeFragment, parseMixed };\n", "import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let lookaheadRecord = this.reducePos < this.pos - 25 /* Lookahead.Margin */;\n        if (lookaheadRecord)\n            this.setLookAhead(this.pos);\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, lookaheadRecord ? 8 : 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, mustSink = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!mustSink || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */) {\n                let mustMove = false;\n                for (let scan = index; scan > 0 && this.buffer[scan - 2] > end; scan -= 4) {\n                    if (this.buffer[scan - 1] >= 0) {\n                        mustMove = true;\n                        break;\n                    }\n                }\n                if (mustMove)\n                    while (index > 0 && this.buffer[index - 2] > end) {\n                        // Move this record forward\n                        this.buffer[index] = this.buffer[index - 4];\n                        this.buffer[index + 1] = this.buffer[index - 3];\n                        this.buffer[index + 2] = this.buffer[index - 2];\n                        this.buffer[index + 3] = this.buffer[index - 1];\n                        index -= 4;\n                        if (size > 4)\n                            size -= 4;\n                    }\n            }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    /**\n    Accept a token ending at a specific given position.\n    */\n    acceptTokenTo(token, endPos) {\n        this.token.value = token;\n        this.token.end = endPos;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n", "import { L<PERSON>arser } from '@lezer/lr';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json$1 = 1,\n  Logfmt$1 = 2,\n  Unpack$1 = 3,\n  Pattern$1 = 4,\n  Regexp$1 = 5,\n  Unwrap$1 = 6,\n  LabelFormat$1 = 7,\n  LineFormat$1 = 8,\n  LabelReplace$1 = 9,\n  Vector$1 = 10,\n  Offset$1 = 11,\n  Bool$1 = 12,\n  On$1 = 13,\n  Ignoring$1 = 14,\n  GroupLeft$1 = 15,\n  GroupRight$1 = 16,\n  Decolorize$1 = 17,\n  Drop$1 = 18,\n  Keep$1 = 19,\n  By$1 = 20,\n  Without$1 = 21,\n  And$1 = 22,\n  Or$1 = 23,\n  Unless$1 = 24,\n  Sum$1 = 25,\n  Avg$1 = 26,\n  Count$1 = 27,\n  Max$1 = 28,\n  Min$1 = 29,\n  Stddev$1 = 30,\n  Stdvar$1 = 31,\n  Bottomk$1 = 32,\n  Topk$1 = 33,\n  Sort$1 = 34,\n  Sort_Desc$1 = 35;\n\nconst keywordTokens = {\n  json: Json$1,\n  logfmt: Logfmt$1,\n  unpack: Unpack$1,\n  pattern: Pattern$1,\n  regexp: Regexp$1,\n  label_format: LabelFormat$1,\n  line_format: LineFormat$1,\n  label_replace: LabelReplace$1,\n  vector: Vector$1,\n  offset: Offset$1,\n  bool: Bool$1,\n  on: On$1,\n  ignoring: Ignoring$1,\n  group_left: GroupLeft$1,\n  group_right: GroupRight$1,\n  unwrap: Unwrap$1,\n  decolorize: Decolorize$1,\n  drop: Drop$1,\n  keep: Keep$1,\n};\n\nconst specializeIdentifier = (value) => {\n  return keywordTokens[value.toLowerCase()] || -1;\n};\n\nconst contextualKeywordTokens = {\n  by: By$1,\n  without: Without$1,\n  and: And$1,\n  or: Or$1,\n  unless: Unless$1,\n  sum: Sum$1,\n  avg: Avg$1,\n  count: Count$1,\n  max: Max$1,\n  min: Min$1,\n  stddev: Stddev$1,\n  stdvar: Stdvar$1,\n  bottomk: Bottomk$1,\n  topk: Topk$1,\n  sort: Sort$1,\n  sort_desc: Sort_Desc$1,\n};\n\nconst extendIdentifier = (value) => {\n  return contextualKeywordTokens[value.toLowerCase()] || -1;\n};\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_Identifier = {__proto__:null,ip:295, count_over_time:301, rate:303, rate_counter:305, bytes_over_time:307, bytes_rate:309, avg_over_time:311, sum_over_time:313, min_over_time:315, max_over_time:317, stddev_over_time:319, stdvar_over_time:321, quantile_over_time:323, first_over_time:325, last_over_time:327, absent_over_time:329, bytes:335, duration:337, duration_seconds:339};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"EtOYQPOOO#cQPO'#DUOOQO'#ER'#ERO#hQPO'#ERO$}QPO'#DTOYQPO'#DTOOQO'#Ed'#EdO%[QPO'#EcOOQO'#FP'#FPO%aQPO'#FOQ%lQPOOO&mQPO'#F]O&rQPO'#F^OOQO'#Eb'#EbOOQO'#DS'#DSOOQO'#Ee'#EeOOQO'#Ef'#EfOOQO'#Eg'#EgOOQO'#Eh'#EhOOQO'#Ei'#EiOOQO'#Ej'#EjOOQO'#Ek'#EkOOQO'#El'#ElOOQO'#Em'#EmOOQO'#En'#EnOOQO'#Eo'#EoOOQO'#Ep'#EpOOQO'#Eq'#EqOOQO'#Er'#ErOOQO'#Es'#EsO&wQPO'#DWOOQO'#DV'#DVO'VQPO,59pOOQO,5:m,5:mOOQO'#Dc'#DcO'_QPO'#DbO'gQPO'#DaO)lQPO'#D`O*{QPO'#D`OOQO'#D_'#D_O+sQPO,59oO-}QPO,59oO.UQPO,5:|O.]QPO,5:}O.hQPO'#E|O0sQPO,5;jO0zQPO,5;jO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lOYQPO,5;wO3cQPO,5;xO3hQPO,59rO#cQPO,59qOOQO1G/[1G/[OOQO'#Dh'#DhO3mQPO,59|O5^QPO,59|OOQO'#Di'#DiO5cQPO,59{OOQO,59{,59{O5kQPO'#DWO6YQPO'#DlO8PQPO'#DoO9sQPO'#DoOOQO'#Do'#DoOOQO'#Dv'#DvOOQO'#Dt'#DtO+kQPO'#DtO9xQPO,59zO;iQPO'#EVO;nQPO'#EWOOQO'#EZ'#EZO;sQPO'#E[O;xQPO'#E_OOQO,59z,59zOOQO,59y,59yOOQO1G/Z1G/ZOOQO1G0h1G0hO;}QPO'#EtO.`QPO'#EtO<XQPO1G0iO<^QPO1G0iO<cQPO,5;hO=oQPO1G1UO=vQPO1G1UO=}QPO1G1UO>UQPO'#FSO@dQPO'#FRO@nQPO'#FROYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO@xQPO1G1cOAPQPO1G1dOOQO1G/^1G/^OOQO1G/]1G/]O5cQPO1G/hOAUQPO1G/hOAZQPO'#DjOBzQPO'#DjOOQO1G/g1G/gOCbQPO,59rOCPQPO,5:cOOQO'#Dm'#DmOClQPO,5:WOEcQPO'#DrOOQO'#Dq'#DqOGVQPO,5:_OHvQPO,5:[OOQO,5:Z,5:ZOJgQPO,5:`O+kQPO,5:`O+kQPO,5:`OOQO,5:q,5:qOJuQPO'#EYOOQO'#EX'#EXOJzQPO,5:rOLkQPO'#E^OOQO'#E^'#E^OOQO'#E]'#E]ONbQPO,5:vO!!RQPO'#EaOOQO'#Ea'#EaOOQO'#E`'#E`O!#xQPO,5:yO!%iQPO'#D`O;}QPO,5;`O!%pQPO'#EuO!%uQPO,5;`O!%}QPO,5;`O!&[QPO,5;`O!&iQPO,5;`O!&nQPO7+&TO.`QPO7+&TOOQO'#E}'#E}O!(OQPO1G1SOOQO1G1S1G1SOYQPO7+&pO!(WQPO7+&pO!)hQPO7+&pO!)oQPO7+&pO!)vQQO'#FTOOQO,5;n,5;nO!,UQPO,5;mO!,]QPO,5;mO!-nQPO7+&rO!-uQPO7+&rOOQO7+&r7+&rO!.SQPO7+&rO!.ZQPO7+&rO!/`QPO7+&rO!/pQPO7+&}OOQO7+'O7+'OOOQO7+%S7+%SO!/uQPO7+%SO5cQPO,5:UO!/zQPO,5:UO!0PQPO1G/{OOQO1G/}1G/}OOQO1G0U1G0UOOQO1G0W1G0WOOQO,5:X,5:XO!0UQPO1G/yO!1uQPO,5:^O!1zQPO,5:]OOQO1G/z1G/zO!2PQPO1G/zO!3pQPO,5:tO;nQPO,5:sO;sQPO,5:wO;xQPO,5:zO!3xQPO,5;cO!%uQPO1G0zO!4WQPO1G0zO!4`QPO,5;aO+kQPO,5;cO!4eQPO1G0zO!4oQPO'#EvO!4tQPO1G0zO!4eQPO1G0zO!4|QPO1G0zO!5ZQPO1G0zO!%xQPO1G0zOOQO1G0z1G0zOOQO<<Io<<IoO!5fQPO<<IoO!5kQPO,5;iOOQO7+&n7+&nO!5pQPO<<J[OOQO<<J[<<J[OYQPO<<J[OOQO'#FV'#FVO!5wQPO,5;oOOQO'#FU'#FUOOQO,5;o,5;oOOQO1G1X1G1XO!6PQPO1G1XO!8YQPO<<JiOOQO<<Hn<<HnOOQO1G/p1G/pO!8_QPO1G/pO!8dQPO7+%gOOQO1G/x1G/xOOQO1G/w1G/wOOQO1G0`1G0`OOQO1G0_1G0_OOQO1G0c1G0cOOQO1G0f1G0fOOQO'#Ex'#ExOOQO1G0}1G0}O!8iQPO1G0}OOQO'#Ey'#EyOOQO'#Ez'#EzOOQO'#E{'#E{OOQO7+&f7+&fOOQO1G0{1G0{O!8nQPO1G0}O!9SQPO7+&fOOQO,5;b,5;bO!9[QPO7+&fO!%xQPO7+&fO!9fQPO7+&fO!9qQPOAN?ZOOQO1G1T1G1TO!;RQPOAN?vO!<cQPOAN?vO!<jQQO1G1ZOOQO1G1Z1G1ZOOQO7+&s7+&sO!<rQPOAN@TOOQO7+%[7+%[O!<wQPO<<IRO!<|QPO7+&iO!=RQPO<<JQO!=ZQPO<<JQO!=cQPO'#EwO!=hQPO<<JQOOQOG24uG24uOOQOG25bG25bOOQO1G1[1G1[OOQO7+&u7+&uO!=pQPOG25oOOQOAN>mAN>mO!=uQPO<<JTOOQOAN?lAN?lO!=zQPOAN?lO!>SQPOLD+ZOOQOAN?oAN?oOOQO,5:r,5:rO!>XQPO!$'NuO!>^QPO!)9DaO!>cQPO!.K9{OOQO!4//g!4//gO;nQPO'#EWO!>hQPO'#D`O!?`QPO,59oO!@fQPO'#DTOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO!AqQPO7+&rO!AxQPO7+&rO!BVQPO7+&rO!C_QPO7+&rO!CfQPO7+&rO!B^QPO'#FQ\",\n  stateData: \"!Cs~O$TOStOS~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!vQO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O{nO~O!vqO~O!OrO!QrO!WrO!XrO!YrO!ZrOfwXgwXhwX!lwX!nwX!owX!pwX!qwX!wwX!xwX#{wX#|wX#}wX$OwX~O!_vO$RwX$ZwX~P#mO$Y{O~Od|Oe|O$Y}O~Of!QOg!POh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{!SO#|!SO#}!SO$O!TO~O$Y!VO~O$Y!WO~O|!XO!O!XO!P!XO!Q!XO~O$V!YO$W!ZO~O}!]O$X!_O~Og!`Of!TXh!TX!O!TX!Q!TX!W!TX!X!TX!Y!TX!Z!TX!_!TX!l!TX!n!TX!o!TX!p!TX!q!TX!w!TX!x!TX#{!TX#|!TX#}!TX$O!TX$R!TX$Z!TX$k!TX$V!TX~O!OrO!QrO!WrO!XrO!YrO!ZrO~Of!SXg!SXh!SX!_!SX!l!SX!n!SX!o!SX!p!SX!q!SX!w!SX!x!SX#{!SX#|!SX#}!SX$O!SX$R!SX$Z!SX$k!SX$V!SX~P)WOP!dOQ!cOR!fOS!eOT!eOV!lOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_vOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Rwa$Zwa~P)WOfvXgvXhvX!OvX!lvX!nvX!ovX!pvX!qvX!wvX!xvX#{vX#|vX#}vX$OvX~O$Z!rO~P,|O$Z!sO~P,|O!v!wO$UPO$Y!uO~O$Y!xO~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O!v!yO~P.mO$Y!{O~O[#OO]!|O^!|OX#uPY#uPi#uPj#uPk#uPl#uPm#uPn#uPo#uPp#uPq#uPr#uPs#uP!v#uP!w#uP!x#uP$U#uP$Y#uP$[#uP$]#uP$^#uP$_#uP$`#uP$a#uP$b#uP$c#uP$d#uP$e#uP$f#uP$g#uP$h#uP$i#uP$j#uP~O!v#WO~O}#XO~Og#ZOf!Uah!Ua!O!Ua!Q!Ua!W!Ua!X!Ua!Y!Ua!Z!Ua!_!Ua!l!Ua!n!Ua!o!Ua!p!Ua!q!Ua!w!Ua!x!Ua#{!Ua#|!Ua#}!Ua$O!Ua$R!Ua$Z!Ua$k!Ua$V!Ua~O$Y#[O~O}#]O$X!_O~O|#`O!O#`O!P!XO!Q!XO!l#aO!n#aO!o#aO!p#aO!q#aO~O{#dO!b#bOf!`Xg!`Xh!`X!O!`X!Q!`X!W!`X!X!`X!Y!`X!Z!`X!_!`X!l!`X!n!`X!o!`X!p!`X!q!`X!w!`X!x!`X#{!`X#|!`X#}!`X$O!`X$R!`X$Z!`X$k!`X$V!`X~O{#dOf!cXg!cXh!cX!O!cX!Q!cX!W!cX!X!cX!Y!cX!Z!cX!_!cX!l!cX!n!cX!o!cX!p!cX!q!cX!w!cX!x!cX#{!cX#|!cX#}!cX$O!cX$R!cX$Z!cX$k!cX$V!cX~O}#hO~Of#jOg#kO$V#jOh!Sa!O!Sa!Q!Sa!W!Sa!X!Sa!Y!Sa!Z!Sa!_!Sa!l!Sa!n!Sa!o!Sa!p!Sa!q!Sa!w!Sa!x!Sa#{!Sa#|!Sa#}!Sa$O!Sa$R!Sa$Z!Sa$k!Sa~O}#lO~O{#mO~O{#pO~O{#tO~O!_#xO$k#zO~P)WO$Z$PO~O$V$QO~O{$RO$Z$TO~Of!uXg!uXh!uX!O!uX!l!uX!n!uX!o!uX!p!uX!q!uX!w!uX!x!uX#{!uX#|!uX#}!uX$O!uX$Z!uX~O$V$UO~P<kO$Z$VO~P,|O!v$WO~P.mO$Y$YO~OX#uXY#uXi#uXj#uXk#uXl#uXm#uXn#uXo#uXp#uXq#uXr#uXs#uX!v#uX!w#uX!x#uX$U#uX$Y#uX$[#uX$]#uX$^#uX$_#uX$`#uX$a#uX$b#uX$c#uX$d#uX$e#uX$f#uX$g#uX$h#uX$i#uX$j#uX~O_$[O`$[O~P>ZO]!|O^!|O~P>ZO$V$dO~P,|O$Z$eO~O}$gO~Og$hOf!^Xh!^X!O!^X!Q!^X!W!^X!X!^X!Y!^X!Z!^X!_!^X!l!^X!n!^X!o!^X!p!^X!q!^X!w!^X!x!^X#{!^X#|!^X#}!^X$O!^X$R!^X$Z!^X$k!^X$V!^X~O$Y$iO~O!m$kO!s$lO!vQO!wRO!xRO~O}#XO$X!_O~PCPO{#dO!b$nOf!`ag!`ah!`a!O!`a!Q!`a!W!`a!X!`a!Y!`a!Z!`a!_!`a!l!`a!n!`a!o!`a!p!`a!q!`a!w!`a!x!`a#{!`a#|!`a#}!`a$O!`a$R!`a$Z!`a$k!`a$V!`a~O|$pOf!fXg!fXh!fX!O!fX!Q!fX!W!fX!X!fX!Y!fX!Z!fX!_!fX!l!fX!n!fX!o!fX!p!fX!q!fX!w!fX!x!fX#{!fX#|!fX#}!fX$O!fX$R!fX$V!fX$Z!fX$k!fX~O$V$qOf!gag!gah!ga!O!ga!Q!ga!W!ga!X!ga!Y!ga!Z!ga!_!ga!l!ga!n!ga!o!ga!p!ga!q!ga!w!ga!x!ga#{!ga#|!ga#}!ga$O!ga$R!ga$Z!ga$k!ga~O$V$qOf!dag!dah!da!O!da!Q!da!W!da!X!da!Y!da!Z!da!_!da!l!da!n!da!o!da!p!da!q!da!w!da!x!da#{!da#|!da#}!da$O!da$R!da$Z!da$k!da~Of#jOg#kO$V#jO$Z$rO~O|$tO~O$V$uOf!zag!zah!za!O!za!Q!za!W!za!X!za!Y!za!Z!za!_!za!l!za!n!za!o!za!p!za!q!za!w!za!x!za#{!za#|!za#}!za$O!za$R!za$Z!za$k!za~O|!XO!O!XO!P!XO!Q!XOf#QXg#QXh#QX!W#QX!X#QX!Y#QX!Z#QX!_#QX!l#QX!n#QX!o#QX!p#QX!q#QX!w#QX!x#QX#{#QX#|#QX#}#QX$O#QX$R#QX$V#QX$Z#QX$k#QX~O$V$vOf#Oag#Oah#Oa!O#Oa!Q#Oa!W#Oa!X#Oa!Y#Oa!Z#Oa!_#Oa!l#Oa!n#Oa!o#Oa!p#Oa!q#Oa!w#Oa!x#Oa#{#Oa#|#Oa#}#Oa$O#Oa$R#Oa$Z#Oa$k#Oa~O|!XO!O!XO!P!XO!Q!XOf#TXg#TXh#TX!W#TX!X#TX!Y#TX!Z#TX!_#TX!l#TX!n#TX!o#TX!p#TX!q#TX!w#TX!x#TX#{#TX#|#TX#}#TX$O#TX$R#TX$V#TX$Z#TX$k#TX~O$V$wOf#Rag#Rah#Ra!O#Ra!Q#Ra!W#Ra!X#Ra!Y#Ra!Z#Ra!_#Ra!l#Ra!n#Ra!o#Ra!p#Ra!q#Ra!w#Ra!x#Ra#{#Ra#|#Ra#}#Ra$O#Ra$R#Ra$Z#Ra$k#Ra~OU$xO~P*{O!m${O~O!_$|O$k#zO~OZ%OO!_#xO$Z#ha~P)WO!_#xO$Z%TO$k#zO~P)WO$Z%UO~Od|Oe|Of#Vqg#Vqh#Vq!O#Vq!l#Vq!n#Vq!o#Vq!p#Vq!q#Vq!w#Vq!x#Vq#{#Vq#|#Vq#}#Vq$O#Vq$R#Vq$Z#Vq$V#Vq~O$V%XO$Z%YO~Od|Oe|Of#rqg#rqh#rq!O#rq!l#rq!n#rq!o#rq!p#rq!q#rq!w#rq!x#rq#{#rq#|#rq#}#rq$O#rq$R#rq$Z#rq$V#rq~O$V%]O~P<kO$Z%[O~P,|O#z%^O$Z%aO~OX#uaY#uai#uaj#uak#ual#uam#uan#uao#uap#uaq#uar#uas#ua!v#ua!w#ua!x#ua$U#ua$[#ua$]#ua$^#ua$_#ua$`#ua$a#ua$b#ua$c#ua$d#ua$e#ua$f#ua$g#ua$h#ua$i#ua$j#ua~O$Y$YO~P!*OO_%cO`%cO$Y#ua~P!*OOf!QOh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{#tq#|#tq#}#tq$O#tq$R#tq$Z#tq~Og#tq~P!,jOf#tqg#tqh#tq~P!,pOg!PO~P!,jO$R#tq$Z#tq~P%lOf#tqg#tqh#tq!O#tq!l#tq!n#tq!o#tq!p#tq!q#tq#{#tq#|#tq#}#tq$O#tq~O!w!RO!x!RO$R#tq$Z#tq~P!.eO}%dO~O$Z%eO~O}%gO~O$Y%hO~O$V$qOf!gig!gih!gi!O!gi!Q!gi!W!gi!X!gi!Y!gi!Z!gi!_!gi!l!gi!n!gi!o!gi!p!gi!q!gi!w!gi!x!gi#{!gi#|!gi#}!gi$O!gi$R!gi$Z!gi$k!gi~O}%iO~O{#dO~Of#jO$V#jOg!hih!hi!O!hi!Q!hi!W!hi!X!hi!Y!hi!Z!hi!_!hi!l!hi!n!hi!o!hi!p!hi!q!hi!w!hi!x!hi#{!hi#|!hi#}!hi$O!hi$R!hi$Z!hi$k!hi~O{%kO}%kO~O{%pO$m%rO$n%sO$o%tO~OZ%OO$Z#hi~O$l%vO~O!_#xO$Z#hi~P)WO!m%yO~O!_$|O$Z#hi~O!_#xO$Z%{O$k#zO~P)WO!_$|O$Z%{O$k#zO~O$Z%}O~O{&OO~O$Z&PO~P,|O$V&RO$Z&SO~O$Y$YOX#uiY#uii#uij#uik#uil#uim#uin#uio#uip#uiq#uir#uis#ui!v#ui!w#ui!x#ui$U#ui$[#ui$]#ui$^#ui$_#ui$`#ui$a#ui$b#ui$c#ui$d#ui$e#ui$f#ui$g#ui$h#ui$i#ui$j#ui~O$V&UO~O$Z&VO~O}&WO~O$Y&XO~Of#jOg#kO$V#jO!_#ki$k#ki$Z#ki~O!_$|O$Z#hq~O!_#xO$Z#hq~P)WOZ%OO!_&[O$Z#hq~Od|Oe|Of#V!Rg#V!Rh#V!R!O#V!R!l#V!R!n#V!R!o#V!R!p#V!R!q#V!R!w#V!R!x#V!R#{#V!R#|#V!R#}#V!R$O#V!R$R#V!R$Z#V!R$V#V!R~Od|Oe|Of#r!Rg#r!Rh#r!R!O#r!R!l#r!R!n#r!R!o#r!R!p#r!R!q#r!R!w#r!R!x#r!R#{#r!R#|#r!R#}#r!R$O#r!R$R#r!R$Z#r!R$V#r!R~O$Z&_O~P,|O#z%^O$Z&aO~O}&bO~O$Z&cO~O{&dO~O!_$|O$Z#hy~OZ%OO$Z#hy~OU$xO~O!_&[O$Z#hy~O$V&gO~O$Z&hO~O!_$|O$Z#h!R~O}&jO~O$V&kO~O}&lO~O$Z&mO~OP!dOQ!cOR!fOS!eOT!eOV&nOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_&oOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Vwa~P)WO!_&oO$VwX~P#mOf&yOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{#tq#|#tq#}#tq$O#tq$V#tq~Og#tq~P!@pOf#tqg#tqh#tq~P!@vOg&xO~P!@pOf&yOg&xOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{&{O#|&{O#}&{O$O&|O~O$V#tq~P!B^O!w&zO!x&zO$V#tq~P!.eO\",\n  goto: \"1l$RPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP$S%R%j&Y&]PPPPPP&t'W'h'v(XPPPP(h(p(yP)S)XP)S)S)[)e)S)m*O*O*XPPPPPP*XP*O*bPPP)S)S*{+R)S)S+Y+])S+c+f+l,_,t-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-p-y.^.j/S/V/V/V/Y/i,_/l,_0R0w1Y1c1fPPPPP,_,_[YOT}!{$U%]Q$^#PQ$_#QS$`#R&tQ$a#SQ$b#TQ$c#UQ'O&rQ'P&sQ'Q&uQ'R&vQ'S&wR'T!Vt^O}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wRyTjSOT}!V!{#P#Q#R#S#T#U$U%]S!t{$QQ#}!u]&q&r&s&t&u&v&wRpPQoP^!hv!i#j#k#x$|&oQ#Y!YS#q!n$vT#u!o$wQxSQ#y!tQ$}#|Q%R#}Q%z%QR&p&q[wS!t#|#}%Q&q]!qx#y$}%R%z&piuSx!t#y#|#}$}%Q%R%z&p&qhtSx!t#y#|#}$}%Q%R%z&p&qR!auksSux!t#y#|#}$}%Q%R%z&p&qQ!^sV#^!`#Z$hW![s!`#Z$hR$j#`Q#_!`Q$f#ZR%f$hV!pv#x&oR#c!cQ#f!cQ#g!dR$o#cU#e!c!d#cR%j$qU!jv#x&oQ#i!iQ$r#jQ$s#kR%w$|_!hv!i#j#k#x$|&o_!gv!i#j#k#x$|&ov]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wT$m#`#aQ#o!lR&i&nS#n!l&nR%l$uR#s!nQ#r!nR%m$vR#w!oQ#v!oR%n$wj^O#P#Q#R#S#T#U&r&s&t&u&v&wQzTQ!z}Q#V!VQ$X!{Q%Z$UR&Q%]w]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwVOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwUOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ!v{Q$O!uR%W$QS#|!t#}W$z#y#{%R%SQ%u$yQ%|%TR&Z%{Q%Q#|Q%u$zQ&]%|R&e&ZQ#{!tS$y#y%RQ%P#|Q%S#}S%x$}%QS&Y%z%|R&f&]R%q$xR%o$xQ!OXQ%V$PQ%[$VQ&^%}R&_&PR$S!xwXOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ#P!PQ#Q!QQ#R!RQ#S!SQ#T!TQ#U!UQ&r&xQ&s&yQ&t&zQ&u&{Q&v&|R&w&}h!}!P!Q!R!S!T!U&x&y&z&{&|&}R$]#OQ$Z!|Q%b$[R&T%cR%_$YQ%`$YR&`&R\",\n  nodeNames: \"⚠ Json Logfmt Unpack Pattern Regexp Unwrap LabelFormat LineFormat LabelReplace Vector Offset Bool On Ignoring GroupLeft GroupRight Decolorize Drop Keep By Without And Or Unless Sum Avg Count Max Min Stddev Stdvar Bottomk Topk Sort Sort_Desc LineComment LogQL Expr LogExpr Selector Matchers Matcher Identifier Eq String Neq Re Nre PipelineExpr PipelineStage LineFilters LineFilter Filter PipeExact PipeMatch PipePattern Npa FilterOp Ip OrFilter Pipe LogfmtParser LogfmtParserFlags ParserFlag LabelParser JsonExpressionParser LabelExtractionExpressionList LabelExtractionExpression LogfmtExpressionParser LabelFilter IpLabelFilter UnitFilter DurationFilter Gtr Duration Gte Lss Lte Eql BytesFilter Bytes NumberFilter LiteralExpr Number Add Sub LineFormatExpr LabelFormatExpr LabelsFormat LabelFormatMatcher DecolorizeExpr DropLabelsExpr DropLabels DropLabel KeepLabelsExpr KeepLabels KeepLabel MetricExpr RangeAggregationExpr RangeOp CountOverTime Rate RateCounter BytesOverTime BytesRate AvgOverTime SumOverTime MinOverTime MaxOverTime StddevOverTime StdvarOverTime QuantileOverTime FirstOverTime LastOverTime AbsentOverTime LogRangeExpr Range OffsetExpr UnwrapExpr ConvOp BytesConv DurationConv DurationSecondsConv Grouping Labels VectorAggregationExpr VectorOp BinOpExpr BinOpModifier OnOrIgnoringModifier GroupingLabels GroupingLabelList GroupingLabel LabelName Mul Div Mod Pow LabelReplaceExpr VectorExpr\",\n  maxTerm: 169,\n  skippedNodes: [0,36],\n  repeatNodeCount: 0,\n  tokenData: \"<n~RvX^#ipq#iqr$^rs$yst%kuv%vxy%{yz&Qz{&V{|&[|}&a}!O&f!O!P2v!P!Q3v!Q!R3{!R![7^![!]9]!^!_9q!_!`:O!`!a:e!c!}:r!}#O;Y#P#Q;_#Q#R;d#R#S:r#S#T;i#T#o:r#o#p;u#p#q;z#q#r<i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~#nY$T~X^#ipq#i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~$aR!_!`$j!`!a$o#r#s$t~$oO!O~~$tO!Z~~$yO!Q~~$|UOY$yZr$yrs%`s#O$y#O#P%e#P~$y~%eO}~~%hPO~$y~%pQt~OY%kZ~%k~%{O#}~~&QO$Y~~&VO$Z~~&[O#{~~&aO!w~~&fO$V~~&kQ!x~}!O&q!Q![(w~&tQ#_#`&z#g#h(X~&}P#X#Y'Q~'TP#X#Y'W~'ZP#d#e'^~'aP}!O'd~'gP#X#Y'j~'mP#a#b'p~'sP#d#e'v~'yP#h#i'|~(PP#m#n(S~(XO!b~~([P#h#i(_~(bP#f#g(e~(hP#]#^(k~(nP#V#W(q~(tP#h#i(S~(zZ!O!P)m!Q![(w#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#m#n1{${$|.^~)pP!Q![)s~)vV!Q![)s#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~*bP!m~!Q![*e~*hV!O!P*}!Q![*e#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~+QP!Q![+T~+WU!Q![+T#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~+oQ!m~!Q![+u#g#h-Q~+xV!O!P,_!Q![+u#a#b,z#b#c.R#g#h/X#i#j.^${$|.^~,bP!Q![,e~,hU!Q![,e#a#b,z#b#c.R#g#h/X#i#j.^${$|.^~,}P#g#h-Q~-VP!m~!Q![-Y~-]T!O!P-l!Q![-Y#b#c.R#i#j.^${$|.^~-oP!Q![-r~-uS!Q![-r#b#c.R#i#j.^${$|.^~.UP#g#h.X~.^O!m~~.aP#g#h.d~.iP!m~!Q![.l~.oR!O!P.x!Q![.l#b#c.R~.{P!Q![/O~/RQ!Q![/O#b#c.R~/^P!m~!Q![/a~/dU!O!P/v!Q![/a#a#b,z#b#c.R#i#j.^${$|.^~/yP!Q![/|~0PT!Q![/|#a#b,z#b#c.R#i#j.^${$|.^~0eP!m~!Q![0h~0kW!O!P)m!Q![0h#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~1YP!m~!Q![1]~1`X!O!P)m!Q![1]#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~2QP!m~!Q![2T~2WY!O!P)m!Q![2T#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T${$|.^~2yP!Q![2|~3RR!v~!Q![2|!g!h3[#X#Y3[~3_R{|3h}!O3h!Q![3n~3kP!Q![3n~3sP!v~!Q![3n~3{O#|~~4Qe!v~!O!P5c!Q![7^!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#W#X0`#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#l#m8q#m#n1{${$|.^~5hR!v~!Q![5q!g!h3[#X#Y3[~5v`!v~!Q![5q!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~6}O!s~~7QQ!d!e6x#]#^7W~7ZP!d!e6x~7cd!v~!O!P5c!Q![7^!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#W#X0`#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#m#n1{${$|.^~8tR!Q![8}!c!i8}#T#Z8}~9SR!v~!Q![8}!c!i8}#T#Z8}P9bT{P!Q![9]![!]9]!c!}9]#R#S9]#T#o9]~9vP!o~!_!`9y~:OO!p~~:TQ|~!_!`:Z#r#s:`~:`O!q~~:eO!P~~:jP!l~!_!`:m~:rO!n~R:yT{P#zQ!Q![:r![!]9]!c!}:r#R#S:r#T#o:r~;_O$k~~;dO$l~~;iO$O~~;lRO#S;i#S#T%`#T~;i~;zO$U~~<PR!_~!_!`<Y!`!a<_#r#s<d~<_O!W~~<dO!Y~~<iO!X~~<nO$W~\",\n  tokenizers: [0, 1],\n  topRules: {\"LogQL\":[0,37]},\n  specialized: [{term: 43, get: (value, stack) => (specializeIdentifier(value) << 1)},{term: 43, get: (value, stack) => (extendIdentifier(value) << 1) | 1},{term: 43, get: value => spec_Identifier[value] || -1}],\n  tokenPrec: 0\n});\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json = 1,\n  Logfmt = 2,\n  Unpack = 3,\n  Pattern = 4,\n  Regexp = 5,\n  Unwrap = 6,\n  LabelFormat = 7,\n  LineFormat = 8,\n  LabelReplace = 9,\n  Vector = 10,\n  Offset = 11,\n  Bool = 12,\n  On = 13,\n  Ignoring = 14,\n  GroupLeft = 15,\n  GroupRight = 16,\n  Decolorize = 17,\n  Drop = 18,\n  Keep = 19,\n  By = 20,\n  Without = 21,\n  And = 22,\n  Or = 23,\n  Unless = 24,\n  Sum = 25,\n  Avg = 26,\n  Count = 27,\n  Max = 28,\n  Min = 29,\n  Stddev = 30,\n  Stdvar = 31,\n  Bottomk = 32,\n  Topk = 33,\n  Sort = 34,\n  Sort_Desc = 35,\n  LineComment = 36,\n  LogQL = 37,\n  Expr = 38,\n  LogExpr = 39,\n  Selector = 40,\n  Matchers = 41,\n  Matcher = 42,\n  Identifier = 43,\n  Eq = 44,\n  String = 45,\n  Neq = 46,\n  Re = 47,\n  Nre = 48,\n  PipelineExpr = 49,\n  PipelineStage = 50,\n  LineFilters = 51,\n  LineFilter = 52,\n  Filter = 53,\n  PipeExact = 54,\n  PipeMatch = 55,\n  PipePattern = 56,\n  Npa = 57,\n  FilterOp = 58,\n  Ip = 59,\n  OrFilter = 60,\n  Pipe = 61,\n  LogfmtParser = 62,\n  LogfmtParserFlags = 63,\n  ParserFlag = 64,\n  LabelParser = 65,\n  JsonExpressionParser = 66,\n  LabelExtractionExpressionList = 67,\n  LabelExtractionExpression = 68,\n  LogfmtExpressionParser = 69,\n  LabelFilter = 70,\n  IpLabelFilter = 71,\n  UnitFilter = 72,\n  DurationFilter = 73,\n  Gtr = 74,\n  Duration = 75,\n  Gte = 76,\n  Lss = 77,\n  Lte = 78,\n  Eql = 79,\n  BytesFilter = 80,\n  Bytes = 81,\n  NumberFilter = 82,\n  LiteralExpr = 83,\n  Number = 84,\n  Add = 85,\n  Sub = 86,\n  LineFormatExpr = 87,\n  LabelFormatExpr = 88,\n  LabelsFormat = 89,\n  LabelFormatMatcher = 90,\n  DecolorizeExpr = 91,\n  DropLabelsExpr = 92,\n  DropLabels = 93,\n  DropLabel = 94,\n  KeepLabelsExpr = 95,\n  KeepLabels = 96,\n  KeepLabel = 97,\n  MetricExpr = 98,\n  RangeAggregationExpr = 99,\n  RangeOp = 100,\n  CountOverTime = 101,\n  Rate = 102,\n  RateCounter = 103,\n  BytesOverTime = 104,\n  BytesRate = 105,\n  AvgOverTime = 106,\n  SumOverTime = 107,\n  MinOverTime = 108,\n  MaxOverTime = 109,\n  StddevOverTime = 110,\n  StdvarOverTime = 111,\n  QuantileOverTime = 112,\n  FirstOverTime = 113,\n  LastOverTime = 114,\n  AbsentOverTime = 115,\n  LogRangeExpr = 116,\n  Range = 117,\n  OffsetExpr = 118,\n  UnwrapExpr = 119,\n  ConvOp = 120,\n  BytesConv = 121,\n  DurationConv = 122,\n  DurationSecondsConv = 123,\n  Grouping = 124,\n  Labels = 125,\n  VectorAggregationExpr = 126,\n  VectorOp = 127,\n  BinOpExpr = 128,\n  BinOpModifier = 129,\n  OnOrIgnoringModifier = 130,\n  GroupingLabels = 131,\n  GroupingLabelList = 132,\n  GroupingLabel = 133,\n  LabelName = 134,\n  Mul = 135,\n  Div = 136,\n  Mod = 137,\n  Pow = 138,\n  LabelReplaceExpr = 139,\n  VectorExpr = 140;\n\nexport { AbsentOverTime, Add, And, Avg, AvgOverTime, BinOpExpr, BinOpModifier, Bool, Bottomk, By, Bytes, BytesConv, BytesFilter, BytesOverTime, BytesRate, ConvOp, Count, CountOverTime, Decolorize, DecolorizeExpr, Div, Drop, DropLabel, DropLabels, DropLabelsExpr, Duration, DurationConv, DurationFilter, DurationSecondsConv, Eq, Eql, Expr, Filter, FilterOp, FirstOverTime, GroupLeft, GroupRight, Grouping, GroupingLabel, GroupingLabelList, GroupingLabels, Gte, Gtr, Identifier, Ignoring, Ip, IpLabelFilter, Json, JsonExpressionParser, Keep, KeepLabel, KeepLabels, KeepLabelsExpr, LabelExtractionExpression, LabelExtractionExpressionList, LabelFilter, LabelFormat, LabelFormatExpr, LabelFormatMatcher, LabelName, LabelParser, LabelReplace, LabelReplaceExpr, Labels, LabelsFormat, LastOverTime, LineComment, LineFilter, LineFilters, LineFormat, LineFormatExpr, LiteralExpr, LogExpr, LogQL, LogRangeExpr, Logfmt, LogfmtExpressionParser, LogfmtParser, LogfmtParserFlags, Lss, Lte, Matcher, Matchers, Max, MaxOverTime, MetricExpr, Min, MinOverTime, Mod, Mul, Neq, Npa, Nre, Number, NumberFilter, Offset, OffsetExpr, On, OnOrIgnoringModifier, Or, OrFilter, ParserFlag, Pattern, Pipe, PipeExact, PipeMatch, PipePattern, PipelineExpr, PipelineStage, Pow, QuantileOverTime, Range, RangeAggregationExpr, RangeOp, Rate, RateCounter, Re, Regexp, Selector, Sort, Sort_Desc, Stddev, StddevOverTime, Stdvar, StdvarOverTime, String, Sub, Sum, SumOverTime, Topk, UnitFilter, Unless, Unpack, Unwrap, UnwrapExpr, Vector, VectorAggregationExpr, VectorExpr, VectorOp, Without, parser };\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".js?_cache=\" + {\"82\":\"938c92610b5cdf2d00ef\",\"328\":\"2a581908eae56bffcaae\",\"470\":\"29dd26bce42815d67980\",\"546\":\"8e0beb2f22d2cbf6b122\",\"557\":\"a3b175be8d0fd60ff808\",\"675\":\"0a1079bbbee9699b0bea\",\"677\":\"4abf8fc642aec3399b73\",\"767\":\"ca8114e32d5cb06e2cdd\",\"826\":\"91e39090c5611938563c\",\"854\":\"9da793b3efc18875808d\",\"864\":\"c7042e4fc7e1fc7aad94\",\"905\":\"1ba01bd632316ea2c77c\",\"906\":\"b3faa479d78c2dc07403\",\"919\":\"728718e594379dd03c81\",\"944\":\"c9770b9500ce5eb4bbe7\"}[chunkId] + \"\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"public/plugins/grafana-lokiexplore-app/\";", "\n__webpack_require__.sriHashes = {\"82\":\"*-*-*-CHUNK-SRI-HASH-EQQjepNKyKKHggNWDI+9nHc8T6ovg=\",\"328\":\"*-*-*-CHUNK-SRI-HASH-HP0RIrpXqzYxxeeTwj5+o/GRsLRxE=\",\"470\":\"*-*-*-CHUNK-SRI-HASH-DLC92l8qdycD4Ua87OV1W1EmkNCnU=\",\"546\":\"*-*-*-CHUNK-SRI-HASH-SGxEKWjXIi8J7KnveGHhrAGvn0JRw=\",\"557\":\"*-*-*-CHUNK-SRI-HASH-Gw/2LmWGy8RTlrbYN6/fH4yP2IvE4=\",\"675\":\"*-*-*-CHUNK-SRI-HASH-jqzP2PT4qXnc3eE0MBMM0EZfK6/XQ=\",\"677\":\"*-*-*-CHUNK-SRI-HASH-5AAWXVXE9/tolnBeqrK3+9cHOkFO8=\",\"767\":\"*-*-*-CHUNK-SRI-HASH-jEdaJjIgLKHmuiiv6R1fFNJ1L0eGE=\",\"826\":\"*-*-*-CHUNK-SRI-HASH-TD9ZyoD08pRdcHFno3E1sZN/8NxwE=\",\"854\":\"*-*-*-CHUNK-SRI-HASH-CO8yIHolx/3g3nglKHNgio0QWCR/E=\",\"864\":\"*-*-*-CHUNK-SRI-HASH-yWxbkjxSn+1LO1sy5eo44f5jeHdaE=\",\"905\":\"*-*-*-CHUNK-SRI-HASH-HhCHUmeskf7B5oHAvR9wcq2Ft6pYU=\",\"906\":\"*-*-*-CHUNK-SRI-HASH-XoyJpYxqaHodAXTB6hiTmznYmkdwA=\",\"919\":\"*-*-*-CHUNK-SRI-HASH-E0zoKbm1MOaKexO/750XsMkg7RxhQ=\",\"944\":\"*-*-*-CHUNK-SRI-HASH-9Nvrh568fe5XpRS5Cb19/W6QCSzAA=\"};", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkgrafana_lokiexplore_app\"] = self[\"webpackChunkgrafana_lokiexplore_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(6709);\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "slice", "lastIndexOf", "OpenInLogsDrilldownButton", "lazy", "EmbeddedLogsExploration", "App", "wasmSupported", "default", "initRuntimeDs", "initChangepoint", "initOutlier", "Promise", "all", "AppConfig", "plugin", "AppPlugin", "setRootPage", "addConfigPage", "body", "icon", "id", "title", "linkConfig", "linkConfigs", "addLink", "exposeComponent", "component", "props", "Suspense", "fallback", "LinkButton", "variant", "disabled", "description", "div", "pageSlugUrlKey", "drilldownLabelUrlKey", "TabNames", "PageSlugs", "ValueSlugs", "PRODUCT_NAME", "ExtensionPoints", "MetricInvestigation", "targets", "PluginExtensionPoints", "DashboardPanelMenu", "ExploreToolbarAction", "path", "createAppUrl", "configure", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "target", "datasource", "type", "templateSrv", "getTemplateSrv", "dataSourceUid", "replace", "uid", "scopedVars", "expr", "interpolateQueryExpr", "fields", "labelFilters", "lineFilters", "patternFilters", "getMatcherFromQuery", "labelSelector", "selector", "isOperatorInclusive", "operator", "labelValue", "replaceSlash", "value", "split", "labelName", "key", "SERVICE_NAME", "sort", "a", "params", "setUrlParameter", "UrlParameters", "DatasourceId", "URLSearchParams", "TimeRangeFrom", "timeRange", "from", "valueOf", "toString", "TimeRangeTo", "to", "setUrlParamsFromLabelFilters", "lineFilter", "appendUrlParameter", "LineFilters", "escapeURLDelimiters", "stringifyValues", "setLineFilterUrlParams", "length", "field", "LabelType", "StructuredMetadata", "LEVEL_VARIABLE_VALUE", "Levels", "<PERSON><PERSON><PERSON>", "stringifyAdHocValues", "replaceEscapeChars", "fieldValue", "parser", "adHocFilterURLString", "JSON", "stringify", "stringifyAdHocValueLabels", "Fields", "setUrlParamsFromFieldFilters", "patterns", "push", "PatternFilterOp", "match", "pattern", "patternsString", "renderPatternFilters", "Patterns", "PatternsVariable", "setUrlParamsFromPatterns", "EMPTY_VARIABLE_VALUE", "addAdHocFilterUserInputPrefix", "labelFilter", "Indexed", "labelsAdHocFilterURLString", "Labels", "urlParams", "pluginJson", "VAR_DATASOURCE", "VAR_LABELS", "VAR_FIELDS", "VAR_METADATA", "VAR_LEVELS", "VAR_LINE_FILTERS", "VAR_PATTERNS", "initalParams", "searchParams", "locationService", "getSearch", "set", "location", "getLocation", "search", "append", "parameter", "stripAdHocFilterUserInputPrefix", "Symbol", "escapeUrlCommaDelimiters", "escapeUrlPipeDelimiters", "variable", "multi", "includeAll", "escapeLabelValueInExactSelector", "lodashMap", "lokiSpecialRegexEscape", "join", "LabelFilterOp", "LineFormatFilterOp", "NumericFilterOp", "FilterOp", "LineFilterOp", "LineFilterCaseSensitive", "defaultContext", "app", "version", "logger", "error", "err", "ctx", "console", "attemptFaroErr", "info", "msg", "attemptFaroInfo", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logInfo", "e", "logWarning", "context2", "isRecord", "Object", "keys", "for<PERSON>ach", "hasData", "data", "populateFetchErrorContext", "Error", "logError", "NodePosition", "fromNode", "node", "contains", "position", "this", "getExpression", "query", "substring", "constructor", "syntaxNode", "getNodesFromQuery", "nodeTypes", "nodes", "parse", "iterate", "enter", "undefined", "includes", "getAllPositionsInNodeByType", "positions", "pos", "child", "childAfter", "parseNonPatternFilters", "lineFilterValue", "quoteString", "index", "isRegexSelector", "regex", "negativeRegex", "isCaseInsensitive", "replaceDoubleEscape", "RegExp", "replaceDoubleQuoteEscape", "caseInsensitive", "caseSensitive", "parsePatternFilters", "getNumericFieldOperator", "matcher", "<PERSON><PERSON>", "FilterOperator", "lte", "Lss", "lt", "Gte", "gte", "Gtr", "gt", "getStringFieldOperator", "Eq", "Equal", "Neq", "NotEqual", "Re", "RegexEqual", "Nre", "RegexNotEqual", "filter", "Selector", "allMatcher", "Matcher", "identifierPosition", "Identifier", "valuePosition", "String", "map", "parseLabelFilters", "allLineFilters", "LineFilter", "entries", "equal", "PipeExact", "pipeRegExp", "PipeMatch", "notEqual", "notEqualRegExp", "patternInclude", "PipePattern", "patternExclude", "Npa", "lineFilterValueNodes", "getStringsFromLineFilter", "lineFilterValueNode", "negativeMatch", "parseLineFilters", "dataFrame", "series", "frame", "refId", "allFields", "LabelFilter", "fieldNameNode", "expression", "<PERSON><PERSON><PERSON><PERSON>", "logFmtParser", "Logfmt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Json", "fieldName", "fieldStringValue", "fieldNumberValue", "Number", "fieldBytesValue", "Bytes", "fieldDurationValue", "Duration", "labelType", "getLabelTypeFromFrame", "Parsed", "parseFields", "ErrorId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "isQueryWithNode", "string", "<PERSON><PERSON><PERSON><PERSON>", "LokiQueryDirection", "labelKey", "typeField", "name", "values", "isObj", "o", "hasProp", "prop", "isString", "s", "obj", "unknownToStrings", "strings", "Array", "isArray", "i", "narrowSelectedTableRow", "narrowed", "row", "narrowLogsVisualizationType", "narrowLogsSortOrder", "LogsSortOrder", "Ascending", "Descending", "narrowFieldValue", "narrowRecordStringNumber", "returnRecord", "narrowTimeRange", "<PERSON><PERSON><PERSON><PERSON>", "range", "narrowErrorMessage", "narrowFilterOperator", "op", "NarrowingError", "narrowPageOrValueSlug", "input", "narrowPageSlug", "narrowValueSlug", "label", "toLowerCase", "labels", "logs", "narrowDrilldownLabelFromSearchParams", "narrowPageSlugFromSearchParams", "narrowJsonDerivedFieldLinkPayload", "payload", "href", "isOperatorExclusive", "isOperatorRegex", "isOperatorNumeric", "numericOperatorArray", "getOperatorDescription", "operators", "array", "includeOperators", "numericOperators", "lineFilterOperators", "excludePatternsLine", "p", "trim", "includePatterns", "includePatternsLine", "VAR_LABELS_EXPR", "VAR_LABELS_REPLICA", "VAR_LABELS_REPLICA_EXPR", "VAR_FIELDS_EXPR", "PENDING_FIELDS_EXPR", "PENDING_METADATA_EXPR", "VAR_FIELDS_AND_METADATA", "VAR_METADATA_EXPR", "VAR_PATTERNS_EXPR", "VAR_LEVELS_EXPR", "VAR_FIELD_GROUP_BY", "VAR_LABEL_GROUP_BY", "VAR_LABEL_GROUP_BY_EXPR", "VAR_PRIMARY_LABEL_SEARCH", "VAR_PRIMARY_LABEL", "VAR_PRIMARY_LABEL_EXPR", "VAR_DATASOURCE_EXPR", "VAR_JSON_FIELDS", "VAR_JSON_FIELDS_EXPR", "VAR_LINE_FORMAT", "VAR_LINE_FORMAT_EXPR", "MIXED_FORMAT_EXPR", "JSON_FORMAT_EXPR", "LOGS_FORMAT_EXPR", "VAR_LOGS_FORMAT", "VAR_LOGS_FORMAT_EXPR", "VAR_LINE_FILTER", "VAR_LINE_FILTERS_EXPR", "LOG_STREAM_SELECTOR_EXPR", "DETECTED_FIELD_VALUES_EXPR", "DETECTED_FIELD_AND_METADATA_VALUES_EXPR", "DETECTED_LEVELS_VALUES_EXPR", "PATTERNS_SAMPLE_SELECTOR_EXPR", "PRETTY_LOG_STREAM_SELECTOR_EXPR", "EXPLORATION_DS", "ALL_VARIABLE_VALUE", "SERVICE_UI_LABEL", "VAR_AGGREGATED_METRICS", "USER_INPUT_ADHOC_VALUE_PREFIX", "startsWith", "isAdHocFilterValueUserInput", "module", "exports", "__WEBPACK_EXTERNAL_MODULE__6089__", "__WEBPACK_EXTERNAL_MODULE__7781__", "__WEBPACK_EXTERNAL_MODULE__8531__", "__WEBPACK_EXTERNAL_MODULE__2007__", "__WEBPACK_EXTERNAL_MODULE__3241__", "__WEBPACK_EXTERNAL_MODULE__1308__", "__WEBPACK_EXTERNAL_MODULE__5959__", "__WEBPACK_EXTERNAL_MODULE__8398__", "__WEBPACK_EXTERNAL_MODULE__200__", "__WEBPACK_EXTERNAL_MODULE__1159__", "__WEBPACK_EXTERNAL_MODULE__7694__", "__WEBPACK_EXTERNAL_MODULE__1269__", "De<PERSON>ult<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "nextPropID", "Range", "NodeProp", "config", "perNode", "deserialize", "add", "RangeError", "NodeType", "result", "closedBy", "str", "openedBy", "group", "isolate", "contextHash", "lookAhead", "mounted", "MountedTree", "tree", "overlay", "get", "noProps", "create", "flags", "define", "spec", "top", "skipped", "src", "isTop", "isSkipped", "isError", "isAnonymous", "is", "indexOf", "direct", "groups", "found", "none", "NodeSet", "types", "extend", "newTypes", "newProps", "source", "assign", "CachedNode", "WeakMap", "CachedInnerNode", "IterMode", "Tree", "children", "ch", "test", "cursor", "mode", "TreeCursor", "topNode", "cursorAt", "side", "scope", "moveTo", "_tree", "TreeNode", "resolve", "resolveNode", "resolveInner", "resolveStack", "inner", "layers", "scan", "parent", "mount", "root", "iterStack", "stackIterator", "leave", "anon", "IncludeAnonymous", "c", "entered", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "balance", "balanceRange", "makeTree", "build", "_a", "buffer", "nodeSet", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reused", "minRepeatType", "FlatBufferCursor", "takeNode", "parentStart", "minPos", "inRepeat", "depth", "start", "end", "size", "lookAheadAtStart", "contextAtStart", "next", "startPos", "findBufferSize", "Uint16Array", "skip", "endPos", "copyToBuffer", "<PERSON><PERSON><PERSON><PERSON>", "localChildren", "localPositions", "localInRepeat", "lastGroup", "lastEnd", "makeRepeatLeaf", "takeFlatNode", "reverse", "make", "makeBalanced", "nodeCount", "stopAt", "j", "last", "lookAheadProp", "lastI", "base", "pop", "pair", "concat", "maxSize", "fork", "minStart", "nodeSize", "localSkipped", "nodeStart", "bufferStart", "startIndex", "topID", "buildTree", "empty", "childString", "endIndex", "<PERSON><PERSON><PERSON><PERSON>", "dir", "pick", "checkSide", "startI", "endI", "b", "copy", "len", "Math", "max", "overlays", "IgnoreOverlays", "BaseNode", "before", "after", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchContext", "matchNodeContext", "enterUnfinishedNodesBefore", "childBefore", "<PERSON><PERSON><PERSON><PERSON>", "prevSibling", "_parent", "super", "<PERSON><PERSON><PERSON><PERSON>", "ExcludeBuffers", "BufferNode", "BufferContext", "<PERSON><PERSON><PERSON><PERSON>", "IgnoreMounts", "rPos", "nextSignificantParent", "val", "toTree", "cur", "externalSibling", "heads", "picked", "newHeads", "splice", "StackIterator", "stack", "bufferNode", "yieldNode", "n", "unshift", "yieldBuf", "yield", "enterChild", "sibling", "d", "atLastNode", "move", "prev", "cache", "mustLeave", "some", "nodeSizeCache", "balanceType", "mkTop", "mkTree", "total", "max<PERSON><PERSON><PERSON>", "ceil", "divide", "offset", "groupFrom", "groupStart", "groupSize", "nextSize", "only", "<PERSON><PERSON><PERSON>", "startParse", "fragments", "ranges", "StringInput", "createParse", "done", "advance", "chunk", "lineChunks", "read", "<PERSON><PERSON>", "state", "reducePos", "score", "bufferBase", "cur<PERSON><PERSON><PERSON><PERSON>", "_", "cx", "StackContext", "pushState", "reduce", "action", "lookaheadRecord", "setLookAhead", "dPrec", "dynamicPrecedence", "getGoto", "minRepeatTerm", "storeNode", "reduceContext", "lastBigReductionStart", "bigReductionCount", "lastBigReductionSize", "count", "stateFlag", "baseStateID", "term", "mustSink", "mustMove", "shift", "shiftContext", "maxNode", "nextState", "apply", "nextStart", "nextEnd", "useNode", "updateContext", "tracker", "reuse", "stream", "reset", "off", "recoverByDelete", "isNode", "canShift", "sim", "SimulatedStack", "stateSlot", "hasAction", "recoverByInsert", "nextStates", "best", "v", "forceReduce", "validAction", "backup", "findForcedReduction", "seen", "explore", "allActions", "r<PERSON><PERSON><PERSON>", "forceAll", "deadEnd", "restart", "sameState", "other", "dialectEnabled", "dialectID", "dialect", "emitContext", "hash", "emitLookAhead", "newCx", "close", "strict", "goto", "StackBufferCursor", "maybeNext", "decodeArray", "Type", "out", "charCodeAt", "stop", "digit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extended", "mask", "nullToken", "InputStream", "chunkOff", "chunk2", "chunk2Pos", "token", "rangeIndex", "chunkPos", "readNext", "resolveOffset", "assoc", "clipPos", "peek", "idx", "resolved", "acceptToken", "endOffset", "acceptTokenTo", "getChunk", "nextChunk", "setDone", "min", "TokenGroup", "readToken", "tokenPrecTable", "prototype", "contextual", "precTable", "precOffset", "groupMask", "accEnd", "allows", "overrides", "low", "high", "mid", "findOffset", "tableData", "tableOffset", "iPrev", "verbose", "process", "env", "LOG", "stackIDs", "cutAt", "fragment", "safeFrom", "safeTo", "trees", "nextFragment", "fr", "openStart", "openEnd", "nodeAt", "TokenCache", "tokens", "mainToken", "actions", "tokenizers", "getActions", "actionIndex", "main", "tokenizer", "updateCachedToken", "addActions", "eofTerm", "getMainToken", "specialized", "specializers", "putAction", "Parse", "recovering", "nextStackID", "minStackPos", "stoppedAt", "topTerm", "stacks", "bufferLength", "parsedPos", "stopped", "stoppedTokens", "newStacks", "advanceStack", "tok", "finished", "findFinished", "stackToTree", "SyntaxError", "runRecovery", "maxRemaining", "outer", "stackID", "strictCx", "cxHash", "cached", "defaultReduce", "localStack", "advanceFully", "pushStackDedup", "restarted", "tokenEnd", "force", "forceBase", "insert", "fromCodePoint", "Dialect", "<PERSON><PERSON><PERSON><PERSON>", "wrappers", "nodeNames", "repeatNodeCount", "topTerms", "topRules", "nodeProps", "setProp", "nodeID", "propSpec", "skippedNodes", "propSources", "tokenArray", "tokenData", "specializerSpecs", "getSpecializer", "states", "Uint32Array", "stateData", "maxTerm", "dialects", "dynamicPrecedences", "tokenPrec", "termNames", "parseDialect", "w", "loose", "table", "groupTag", "terminal", "slot", "flag", "deflt", "t", "external", "contextTracker", "wrap", "hasWrappers", "getName", "prec", "part", "Uint8Array", "keywordTokens", "json", "logfmt", "unpack", "regexp", "label_format", "line_format", "label_replace", "vector", "bool", "on", "ignoring", "group_left", "group_right", "unwrap", "decolorize", "drop", "keep", "contextualKeywordTokens", "by", "without", "and", "or", "unless", "sum", "avg", "stddev", "stdvar", "bottomk", "topk", "sort_desc", "spec_Identifier", "__proto__", "ip", "count_over_time", "rate", "rate_counter", "bytes_over_time", "bytes_rate", "avg_over_time", "sum_over_time", "min_over_time", "max_over_time", "stddev_over_time", "stdvar_over_time", "quantile_over_time", "first_over_time", "last_over_time", "absent_over_time", "bytes", "duration", "duration_seconds", "specializeIdentifier", "extendIdentifier", "MetricExpr", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "loaded", "__webpack_modules__", "call", "m", "getter", "__esModule", "getPrototypeOf", "then", "ns", "def", "current", "getOwnPropertyNames", "definition", "defineProperty", "enumerable", "f", "chunkId", "promises", "u", "g", "globalThis", "Function", "window", "hasOwnProperty", "l", "url", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "origin", "crossOrigin", "integrity", "sri<PERSON><PERSON><PERSON>", "onScriptComplete", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "fn", "setTimeout", "bind", "head", "append<PERSON><PERSON><PERSON>", "toStringTag", "nmd", "paths", "baseURI", "self", "installedChunks", "installedChunkData", "promise", "reject", "errorType", "realSrc", "message", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "chunkIds", "moreModules", "runtime", "chunkLoadingGlobal"], "sourceRoot": ""}