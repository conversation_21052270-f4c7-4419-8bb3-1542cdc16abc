import logging
from typing import Optional

from fastapi import APIRouter, Query, HTTPException

from workflow_backend.src.Define import ProcessStatus, Step
from workflow_backend.src.database.database_helpers import run_query, add_new_process_to_database
from workflow_backend.src.models.evidence_items_models import EvidenceItem
from workflow_backend.src.models.processes_models import AddProcessPayload

evidence_items_router = APIRouter(prefix="/evidence-items", tags=["evidence-items"])


@evidence_items_router.get("")
async def get_evidence_items(case_id: Optional[int] = Query(None, description="Integer stating the case ID to filter for")):
    """
    Get evidence items. Optionally, filter by case id.
    """
    query = "SELECT * FROM evidence_items"
    params = {}

    if case_id is not None:
        query += " WHERE case_id = :case_id"
        params["case_id"] = case_id

    query += " ORDER BY evidence_items.id"
    result = run_query(query, params)
    return result


@evidence_items_router.get("/{evidence_item_id}")
async def get_evidence_item(evidence_item_id: int):
    """ Get evidence item with the given id """
    query = "SELECT * FROM evidence_items WHERE id = :evidence_item_id;"
    result = run_query(query, {"evidence_item_id": evidence_item_id})
    if result is None or len(result) == 0:
        raise HTTPException(status_code=404, detail=f"Evidence item with id {evidence_item_id} not found.")
    return result[0]

@evidence_items_router.post("")
async def add_evidence_item(new_evidence_item: EvidenceItem):
    """
    Add a new evidence item and create a process with status FINISHED for step "LagerDB / intake" (step id 0).
    """
    # Add evidence item
    logging.info(f"Adding new evidence item ({new_evidence_item}) ...")
    query = f"""INSERT INTO evidence_items (evidence_item_name, case_id)
                VALUES (:evidence_item_name, :case_id) 
                RETURNING *;"""
    result = run_query(query, {
        "evidence_item_name": new_evidence_item.evidence_item_name,
        "case_id": new_evidence_item.case_id
    })

    # Add finished process for step 0 (LagerDB /case intake)
    logging.info(f"Adding finished intake process for evidence item {result[0]["id"]} ...")
    add_new_process_to_database(
        AddProcessPayload(
            evidence_item_id=result[0]["id"],
            step_id=Step.LAGER_DB,
            status=ProcessStatus.FINISHED
        )
    )
    return result


@evidence_items_router.delete("/{evidence_item_id}")
async def delete_evidence_item(evidence_item_id):
    """
    Delete the evidence item with the given id.
    """
    logging.info(f"Deleting evidence item with id {evidence_item_id} ...")
    query = "DELETE FROM evidence_items WHERE id = :evidence_item_id RETURNING *;"
    result = run_query(query, {"evidence_item_id": evidence_item_id})
    return result
