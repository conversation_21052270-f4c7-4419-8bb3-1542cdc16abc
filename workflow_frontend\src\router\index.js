import { createRouter, createWebHashHistory } from 'vue-router';
import MainLayout from 'layouts/MainLayout.vue';
import CasesPage from 'pages/CasesPage.vue';
import GrafanaPage from 'pages/GrafanaPage.vue';
import EvidenceItemPage from "pages/EvidenceItemsPage.vue";
import HelpPage from "pages/HelpPage.vue";
import LagerDbPage from "pages/LagerDbPage.vue";
import ImageStationPage from "pages/ImageStationPage.vue";
import UnassignedProcessesPage from "pages/UnassignedProcessesPage.vue";
import ProcessesPage from "pages/ProcessesPage.vue";

const routes = [
  {
    path: '/',
    component: MainLayout,
    children: [
      { path: '', component: CasesPage},
      { path: 'cases', component: CasesPage},
      { path: 'evidence-items', component: EvidenceItemPage },
      { path: 'monitoring', component: GrafanaPage },
      { path: 'help', component: HelpPage },
      { path: 'unassigned_processes', component: UnassignedProcessesPage },
      { path: 'processes', component: ProcessesPage }
    ]
  },
  // {
  //   path: '/lagerdb',
  //   component: LagerDbPage
  // },
  {
    path: '/imagestation',
    component: ImageStationPage
  }

];

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

export default router;
