(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[712],{335:(e,t,n)=>{"use strict";n.d(t,{M:()=>o});var a=n(5959),r=n.n(a),s=n(2007),i=n(6089);const o=({isStreaming:e,iconSize:t=14})=>{const n=(0,s.useStyles2)(l,t);return e?r().createElement(s.<PERSON>lt<PERSON>,{content:"Streaming"},r().createElement(s.Icon,{name:"circle-mono",size:"md",className:n.streamingIndicator})):null},l=(e,t)=>({streamingIndicator:(0,i.css)({width:`${t}px`,height:`${t}px`,backgroundColor:e.colors.success.text,fill:e.colors.success.text,borderRadius:"50%",display:"inline-block"})})},410:(e,t,n)=>{"use strict";n.d(t,{QA:()=>i,dX:()=>o,xx:()=>l});var a=n(9262),r=n(675),s=n(8010);function i(e,t){const n=(0,a.YX)(e),i=o().setHoverHeader(!0).setOption("selectionMode","xy").build();return i.setState({extendPanelContext:(e,s)=>{s.onSelectRange=e=>{var s,i,o,c;if(0===e.length)return void n.setState({selection:void 0});const u=e[0],d={type:"manual",raw:u};if(d.timeRange={from:Math.round(((null===(s=u.x)||void 0===s?void 0:s.from)||0)/1e3),to:Math.round(((null===(i=u.x)||void 0===i?void 0:i.to)||0)/1e3)},d.timeRange.from===d.timeRange.to)return;const m=l(((null===(o=e[0].y)||void 0===o?void 0:o.from)||0)-1,t),p=l((null===(c=e[0].y)||void 0===c?void 0:c.to)||0,t);d.duration={from:m,to:p},n.onUserUpdateSelection(d),(0,a.H)(n.state.actionView)||n.setActionView("comparison"),(0,r.EE)(r.NO.analyse_traces,r.ir.analyse_traces.start_investigation,{selection:d,metric:"duration"})}}}),new s.G1({direction:"row",children:[new s.vA({body:i})]})}const o=()=>s.d0.heatmap().setOption("legend",{show:!1}).setOption("yAxis",{unit:"s",axisLabel:"duration"}).setOption("color",{scheme:"Blues",steps:16}).setOption("rowsFrame",{value:"Spans"});function l(e,t,n){if(!t)return"";if(e<0)return"0";const a=t[Math.floor(e)]*(n||1);return!a||isNaN(a)?"":a>=1?`${a.toFixed(0)}s`:`${(1e3*a).toFixed(0)}ms`}},675:(e,t,n)=>{"use strict";n.d(t,{EE:()=>s,NO:()=>i,ir:()=>o});var a=n(8531),r=n(2533);const s=(e,t,n)=>{(0,a.reportInteraction)(((e,t)=>`${r.id.replace(/-/g,"_")}_${e}_${t}`)(e,t),n)},i={analyse_traces:"analyse_traces",home:"home",common:"common"},o={[i.analyse_traces]:{action_view_changed:"action_view_changed",breakdown_group_by_changed:"breakdown_group_by_changed",breakdown_add_to_filters_clicked:"breakdown_add_to_filters_clicked",comparison_add_to_filters_clicked:"comparison_add_to_filters_clicked",select_attribute_in_comparison_clicked:"select_attribute_in_comparison_clicked",layout_type_changed:"layout_type_changed",start_investigation:"start_investigation",stop_investigation:"stop_investigation",open_trace:"open_trace",open_in_explore_clicked:"open_in_explore_clicked",add_to_investigation_clicked:"add_to_investigation_clicked",add_to_investigation_trace_view_clicked:"add_to_investigation_trace_view_clicked",span_list_columns_changed:"span_list_columns_changed",toggle_bookmark_clicked:"toggle_bookmark_clicked",primary_signal_changed:"primary_signal_changed",exception_message_clicked:"exception_message_clicked"},[i.home]:{homepage_initialized:"homepage_initialized",panel_row_clicked:"panel_row_clicked",explore_traces_clicked:"explore_traces_clicked",read_documentation_clicked:"read_documentation_clicked",filter_changed:"filter_changed",go_to_bookmark_clicked:"go_to_bookmark_clicked"},[i.common]:{metric_changed:"metric_changed",new_filter_added_manually:"new_filter_added_manually",app_initialized:"app_initialized",global_docs_link_clicked:"global_docs_link_clicked",metric_docs_link_clicked:"metric_docs_link_clicked",feedback_link_clicked:"feedback_link_clicked",go_to_full_app_clicked:"go_to_full_app_clicked"}}},783:(e,t,n)=>{"use strict";n.d(t,{Fh:()=>O,GD:()=>y,R_:()=>v});var a=n(7781),r=n(8010),s=n(5959),i=n.n(s),o=n(1769),l=n(8531),c=n(675),u=n(9262),d=n(1269);function m(e,t,n,a,r,s,i){try{var o=e[s](i),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function p(e){return function(){var t=this,n=arguments;return new Promise(function(a,r){var s=e.apply(t,n);function i(e){m(s,a,r,i,o,"next",e)}function o(e){m(s,a,r,i,o,"throw",e)}i(void 0)})}}function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const v="Add to investigation",g="grafana-exploretraces-app/investigation/v1",h="investigations_divider",b="Investigations";class y extends r.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){super(e),this.addActivationHandler(()=>{const e=[{text:"Navigation",type:"group"},{text:"Explore",iconClassName:"compass",href:w(this),onClick:()=>S()}];this.setState({body:new r.Lw({items:e})});const t=(0,u.zY)(this),n=(0,u.U4)(t),a=new o.L({query:this.state.query,dsUid:n});var s,i;a.activate(),this.setState({addToInvestigationButton:a}),this._subs.add(null==a?void 0:a.subscribeToState(()=>{var e;e=this,p(function*(){const t=e.state.addToInvestigationButton;if(t){var n;const l=yield O(t);var a;const u=null!==(a=null===(n=e.state.body)||void 0===n?void 0:n.state.items)&&void 0!==a?a:[],d=u.find(e=>e.text===v);var r,s,i,o;l&&(d?d&&(null===(r=e.state.body)||void 0===r||r.setItems(u.filter(e=>!1===[h,b,v].includes(e.text)))):(null===(s=e.state.body)||void 0===s||s.addItem({text:h,type:"divider"}),null===(i=e.state.body)||void 0===i||i.addItem({text:b,type:"group"}),null===(o=e.state.body)||void 0===o||o.addItem({text:v,iconClassName:"plus-square",onClick:e=>{l.onClick&&l.onClick(e),(0,c.EE)(c.NO.analyse_traces,c.ir.analyse_traces.add_to_investigation_clicked)}})))}})()})),a.setState((s=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){f(e,t,n[t])})}return e}({},a.state),i=null!=(i={labelValue:this.state.labelValue})?i:{},Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(i)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(i)).forEach(function(e){Object.defineProperty(s,e,Object.getOwnPropertyDescriptor(i,e))}),s))})}}f(y,"Component",({model:e})=>{const{body:t}=e.useState();return t?i().createElement(t.Component,{model:t}):i().createElement(i().Fragment,null)});const w=e=>{const t=(0,u.zY)(e),n=(0,u.U4)(t),s=r.jh.getTimeRange(e).state.value,i=(0,u.Is)(e),o=JSON.stringify({"traces-explore":{range:(0,a.toURLRange)(s.raw),queries:[{refId:"A",datasource:n,query:e.state.query,step:i}]}});var c;const d=null!==(c=l.config.appSubUrl)&&void 0!==c?c:"";return a.urlUtil.renderUrl(`${d}/explore`,{panes:o,schemaVersion:1})},S=()=>{(0,c.EE)(c.NO.analyse_traces,c.ir.analyse_traces.open_in_explore_clicked)},O=e=>p(function*(){const t=e.state.context;return void 0!==l.getPluginLinkExtensions?(0,l.getPluginLinkExtensions)({extensionPointId:g,context:t}).extensions[0]:void 0!==l.getObservablePluginLinks?(yield(0,d.firstValueFrom)((0,l.getObservablePluginLinks)({extensionPointId:g,context:t})))[0]:void 0})()},1440:(e,t,n)=>{"use strict";n.d(t,{G:()=>p});var a,r,s,i=n(6089),o=n(8010),l=n(2007),c=n(5959),u=n.n(c),d=n(3049),m=n(8709);class p extends o.Bs{}s=({model:e})=>{const t=(0,l.useTheme2)(),n=(0,l.useStyles2)(v),{component:a}=e.useState();return u().createElement("div",{className:n.container,"data-testid":m.b.loadingState},u().createElement(d.z,{baseColor:t.colors.emphasize(t.colors.background.secondary),highlightColor:t.colors.emphasize(t.colors.background.secondary,.1),borderRadius:t.shape.radius.default},a()))},(r="Component")in(a=p)?Object.defineProperty(a,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[r]=s;const f=(0,i.keyframes)({"0%":{opacity:0},"100%":{opacity:1}});function v(){return{container:(0,i.css)({label:"loading-state-scene",animationName:f,animationDelay:"100ms",animationTimingFunction:"ease-in",animationDuration:"100ms",animationFillMode:"backwards"})}}},1738:(e,t,n)=>{var a={"./af":9805,"./af.js":9805,"./ar":4449,"./ar-dz":4468,"./ar-dz.js":4468,"./ar-kw":3480,"./ar-kw.js":3480,"./ar-ly":4197,"./ar-ly.js":4197,"./ar-ma":2180,"./ar-ma.js":2180,"./ar-ps":9343,"./ar-ps.js":9343,"./ar-sa":230,"./ar-sa.js":230,"./ar-tn":2808,"./ar-tn.js":2808,"./ar.js":4449,"./az":5865,"./az.js":5865,"./be":6627,"./be.js":6627,"./bg":901,"./bg.js":901,"./bm":3179,"./bm.js":3179,"./bn":1966,"./bn-bd":969,"./bn-bd.js":969,"./bn.js":1966,"./bo":6317,"./bo.js":6317,"./br":6474,"./br.js":6474,"./bs":5961,"./bs.js":5961,"./ca":7270,"./ca.js":7270,"./cs":1564,"./cs.js":1564,"./cv":3239,"./cv.js":3239,"./cy":2366,"./cy.js":2366,"./da":2453,"./da.js":2453,"./de":6601,"./de-at":5027,"./de-at.js":5027,"./de-ch":8101,"./de-ch.js":8101,"./de.js":6601,"./dv":6080,"./dv.js":6080,"./el":2655,"./el.js":2655,"./en-au":6836,"./en-au.js":6836,"./en-ca":2086,"./en-ca.js":2086,"./en-gb":2103,"./en-gb.js":2103,"./en-ie":5964,"./en-ie.js":5964,"./en-il":4379,"./en-il.js":4379,"./en-in":765,"./en-in.js":765,"./en-nz":1502,"./en-nz.js":1502,"./en-sg":1152,"./en-sg.js":1152,"./eo":50,"./eo.js":50,"./es":3350,"./es-do":9338,"./es-do.js":9338,"./es-mx":1326,"./es-mx.js":1326,"./es-us":9947,"./es-us.js":9947,"./es.js":3350,"./et":8231,"./et.js":8231,"./eu":8512,"./eu.js":8512,"./fa":9083,"./fa.js":9083,"./fi":5059,"./fi.js":5059,"./fil":2607,"./fil.js":2607,"./fo":3369,"./fo.js":3369,"./fr":7390,"./fr-ca":6711,"./fr-ca.js":6711,"./fr-ch":6152,"./fr-ch.js":6152,"./fr.js":7390,"./fy":2419,"./fy.js":2419,"./ga":3002,"./ga.js":3002,"./gd":4914,"./gd.js":4914,"./gl":6557,"./gl.js":6557,"./gom-deva":8944,"./gom-deva.js":8944,"./gom-latn":5387,"./gom-latn.js":5387,"./gu":7462,"./gu.js":7462,"./he":9237,"./he.js":9237,"./hi":9617,"./hi.js":9617,"./hr":6544,"./hr.js":6544,"./hu":341,"./hu.js":341,"./hy-am":1388,"./hy-am.js":1388,"./id":5251,"./id.js":5251,"./is":1146,"./is.js":1146,"./it":7891,"./it-ch":7,"./it-ch.js":7,"./it.js":7891,"./ja":3727,"./ja.js":3727,"./jv":5198,"./jv.js":5198,"./ka":8974,"./ka.js":8974,"./kk":7308,"./kk.js":7308,"./km":7786,"./km.js":7786,"./kn":4807,"./kn.js":4807,"./ko":1584,"./ko.js":1584,"./ku":1906,"./ku-kmr":5305,"./ku-kmr.js":5305,"./ku.js":1906,"./ky":9190,"./ky.js":9190,"./lb":7396,"./lb.js":7396,"./lo":8503,"./lo.js":8503,"./lt":3010,"./lt.js":3010,"./lv":5192,"./lv.js":5192,"./me":1944,"./me.js":1944,"./mi":6492,"./mi.js":6492,"./mk":2934,"./mk.js":2934,"./ml":1463,"./ml.js":1463,"./mn":8377,"./mn.js":8377,"./mr":8733,"./mr.js":8733,"./ms":8030,"./ms-my":9445,"./ms-my.js":9445,"./ms.js":8030,"./mt":5887,"./mt.js":5887,"./my":7228,"./my.js":7228,"./nb":8294,"./nb.js":8294,"./ne":9559,"./ne.js":9559,"./nl":600,"./nl-be":8796,"./nl-be.js":8796,"./nl.js":600,"./nn":9570,"./nn.js":9570,"./oc-lnc":5662,"./oc-lnc.js":5662,"./pa-in":7101,"./pa-in.js":7101,"./pl":6118,"./pl.js":6118,"./pt":9198,"./pt-br":7203,"./pt-br.js":7203,"./pt.js":9198,"./ro":5565,"./ro.js":5565,"./ru":3315,"./ru.js":3315,"./sd":8473,"./sd.js":8473,"./se":1258,"./se.js":1258,"./si":8798,"./si.js":8798,"./sk":6404,"./sk.js":6404,"./sl":7057,"./sl.js":7057,"./sq":5718,"./sq.js":5718,"./sr":5363,"./sr-cyrl":478,"./sr-cyrl.js":478,"./sr.js":5363,"./ss":7260,"./ss.js":7260,"./sv":2231,"./sv.js":2231,"./sw":7104,"./sw.js":7104,"./ta":7493,"./ta.js":7493,"./te":7705,"./te.js":7705,"./tet":4457,"./tet.js":4457,"./tg":2727,"./tg.js":2727,"./th":2206,"./th.js":2206,"./tk":3419,"./tk.js":3419,"./tl-ph":7243,"./tl-ph.js":7243,"./tlh":16,"./tlh.js":16,"./tr":7020,"./tr.js":7020,"./tzl":8026,"./tzl.js":8026,"./tzm":8537,"./tzm-latn":7899,"./tzm-latn.js":7899,"./tzm.js":8537,"./ug-cn":818,"./ug-cn.js":818,"./uk":8478,"./uk.js":8478,"./ur":7893,"./ur.js":7893,"./uz":9133,"./uz-latn":311,"./uz-latn.js":311,"./uz.js":9133,"./vi":2179,"./vi.js":2179,"./x-pseudo":2455,"./x-pseudo.js":2455,"./yo":3310,"./yo.js":3310,"./zh-cn":7244,"./zh-cn.js":7244,"./zh-hk":76,"./zh-hk.js":76,"./zh-mo":2305,"./zh-mo.js":2305,"./zh-tw":8588,"./zh-tw.js":8588};function r(e){var t=s(e);return n(t)}function s(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}r.keys=function(){return Object.keys(a)},r.resolve=s,e.exports=r,r.id=1738},1769:(e,t,n)=>{"use strict";n.d(t,{L:()=>o});var a=n(8010);function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){r(e,t,n[t])})}return e}function i(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class o extends a.Bs{constructor(e){super(i(s({},e),{queries:[]})),r(this,"_onActivate",()=>{this._subs.add(this.subscribeToState(()=>{this.getQueries(),this.getContext()}))}),r(this,"getQueries",()=>{const e=a.jh.getData(this),t=a.jh.findObject(e,l);if(l(t)){const e=t.state.queries.map(e=>i(s({},e),{query:this.state.query}));JSON.stringify(e)!==JSON.stringify(this.state.queries)&&this.setState({queries:e})}}),r(this,"getContext",()=>{const{queries:e,dsUid:t,labelValue:n,type:r="traceMetrics"}=this.state,i=a.jh.getTimeRange(this);if(!i||!e||!t)return;const o={origin:"Explore Traces",type:r,queries:e,timeRange:s({},i.state.value),datasource:{uid:t},url:window.location.href,id:`${JSON.stringify(e)}`,title:`${n}`,logoPath:"public/plugins/grafana-exploretraces-app/img/1382cadfeb81ccdaa67d.svg"};JSON.stringify(o)!==JSON.stringify(this.state.context)&&this.setState({context:o})}),this.addActivationHandler(this._onActivate.bind(this))}}function l(e){return e instanceof a.dt}},1834:(e,t,n)=>{"use strict";n.d(t,{F:()=>r,p:()=>a});const a=e=>{var t;const n=e.fields.find(e=>"Baseline"===e.name),a=e.fields.find(e=>"Selection"===e.name);let r=0,s=0;for(let e=0;e<((null==n||null===(t=n.values)||void 0===t?void 0:t.length)||0);e++){const t=((null==a?void 0:a.values[e])||0)-((null==n?void 0:n.values[e])||0);Math.abs(t)>Math.abs(r||0)&&(r=t,s=e)}return{maxDifference:r,maxDifferenceIndex:s}},r=e=>{if("duration"!==e)return{query:"status = error",type:"auto"}}},2085:(e,t,n)=>{"use strict";n.d(t,{D9:()=>m,Ms:()=>u,Qt:()=>d});var a=n(5959),r=n.n(a),s=n(8010),i=n(2007),o=n(9262),l=n(9240);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class u extends s.Bs{constructor(...e){super(...e),c(this,"onClick",()=>{var e;const t=(0,o.gG)(this);var n;const a=null!==(n=null===(e=this.state.frame.fields.find(e=>e.labels))||void 0===e?void 0:e.labels)&&void 0!==n?n:{};if(this.state.labelKey){if(!a[this.state.labelKey])return}else if(1!==Object.keys(a).length)return;var r;const s=null!==(r=this.state.labelKey)&&void 0!==r?r:Object.keys(a)[0],i=(0,o.ee)(this.state.frame,this.state.labelKey);d(t,s,i),this.state.onClick({labelName:s})})}}c(u,"Component",({model:e})=>{var t,n,a,s,l;const c=null!==(l=null===(t=e.state)||void 0===t?void 0:t.labelKey)&&void 0!==l?l:"",u=null===(n=e.state)||void 0===n?void 0:n.frame.fields.filter(e=>"time"!==e.type);var d;const p=null!==(d=null==u||null===(s=u[0])||void 0===s||null===(a=s.labels)||void 0===a?void 0:a[c])&&void 0!==d?d:"";return m((0,o.gG)(e),c,p.replace(/"/g,""))?r().createElement(r().Fragment,null):r().createElement(i.Button,{variant:"primary",size:"sm",fill:"text",onClick:e.onClick,icon:"search-plus"},"Add to filters")});const d=(e,t,n)=>{const a=e.state.filters.filter(e=>e.key===l.iH||e.key!==t);history.pushState(null,""),e.setState({filters:[...a,{key:t,operator:"=",value:n}]})},m=(e,t,n)=>(0,o.gG)(e).state.filters.find(e=>e.key===t&&e.value===n)},3292:(e,t,n)=>{"use strict";n.d(t,{VV:()=>yt,jc:()=>vt});var a=n(5959),r=n.n(a),s=n(7781),i=n(8010),o=n(8693),l=n(1454),c=n(8531),u=n(6089),d=n(2007),m=n(9262),p=n(940);const f=({exploration:e})=>{const{origin:t}=(0,p.A)(),[n,s]=(0,a.useState)("Copy url");return r().createElement(d.ToolbarButton,{variant:"canvas",icon:"share-alt",tooltip:n,onClick:()=>{navigator.clipboard&&(navigator.clipboard.writeText(t+(0,m.__)(e)),s("Copied!"),setTimeout(()=>{s("Copy url")},2e3))}})};var v=n(1440),g=n(6002),h=n(3049),b=n(1269);function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){y(e,t,n[t])})}return e}function S(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const O=["span.http.method","span.http.request.method","span.http.route","span.http.path","span.http.status_code","span.http.response.status_code"],x=["Recommended","Resource","Span","Other"];function E({options:e,value:t,onChange:n}){var s;const i=(0,d.useStyles2)(j),o=(0,a.useMemo)(()=>Object.values(e.reduce((e,t)=>{if(t.label){const i=t.label.slice(t.label.indexOf(".")+1);if(O.includes(t.label)){var n;const a=null!==(n=e.recommended)&&void 0!==n?n:{label:"Recommended",options:[]};a.options.push(S(w({},t),{label:i})),e.recommended=a}else if(t.label.startsWith("resource.")){var a;const n=null!==(a=e.resource)&&void 0!==a?a:{label:"Resource",options:[]};n.options.push(S(w({},t),{label:i})),e.resource=n}else if(t.label.startsWith("span.")){var r;const n=null!==(r=e.span)&&void 0!==r?r:{label:"Span",options:[]};n.options.push(S(w({},t),{label:i})),e.span=n}else{var s;const n=null!==(s=e.other)&&void 0!==s?s:{label:"Other",options:[]};n.options.push(t),e.other=n}}return e},{})).sort((e,t)=>x.indexOf(e.label)-x.indexOf(t.label)),[e]);var l;return r().createElement("div",{className:i.container},r().createElement(d.Field,{label:"Add extra columns"},r().createElement(d.Select,{value:""!==(null==t?void 0:t.toString())&&null!==(l=null==t||null===(s=t.toString())||void 0===s?void 0:s.split(","))&&void 0!==l?l:"",placeholder:"Select an attribute",options:o,onChange:e=>n(e.map(e=>e.value).join(",")),isMulti:!0,isClearable:!0,virtualized:!0,prefix:r().createElement(d.Icon,{name:"columns"})})))}const j=()=>({container:(0,u.css)({display:"flex",minWidth:"300px","& > div":{width:"100%"}})});var k=n(675);function C(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){C(e,t,n[t])})}return e}class _ extends i.Bs{setupTransformations(){return[()=>e=>e.pipe((0,b.map)(e=>e.map(e=>{var t;const n=e.fields,a=n.find(e=>"traceName"===e.name),s={type:d.TableCellDisplayMode.Custom,cellComponent:e=>{const t=e.frame,n=null==t?void 0:t.fields.find(e=>"traceIdHidden"===e.name),a=null==t?void 0:t.fields.find(e=>"spanID"===e.name),s=null==n?void 0:n.values[e.rowIndex],i=null==a?void 0:a.values[e.rowIndex];if(!s)return e.value;const o=e.value?e.value:"<name not yet available>";return r().createElement("div",{className:"cell-link-wrapper"},r().createElement("div",{className:"cell-link",title:o,onClick:()=>{this.publishEvent(new l.vR({traceId:s,spanId:i}),!0)}},o),r().createElement(d.Link,{href:this.getLinkToExplore(s,i),target:"_blank",title:"Open in new tab"},r().createElement(d.Icon,{name:"external-link-alt",size:"sm"})))}};return(null==a||null===(t=a.config)||void 0===t?void 0:t.custom)&&(a.config.custom.cellOptions=s),function(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}(P({},e),{fields:n})})))]}updatePanel(e){var t,n;if((null==e?void 0:e.state)!==s.LoadingState.Loading&&(null==e?void 0:e.state)!==s.LoadingState.NotStarted&&(null==e?void 0:e.state)&&((null==e?void 0:e.state)!==s.LoadingState.Streaming||(null===(n=e.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.length))){if((null==e?void 0:e.state)===s.LoadingState.Done||(null==e?void 0:e.state)===s.LoadingState.Streaming)if(0===e.series.length||0===e.series[0].length){if("empty"===this.state.dataState&&this.state.panel)return;this.setState({dataState:"empty",panel:new i.G1({children:[new i.vA({body:new g.v({message:l.PL,remedyMessage:l.a5,padding:"32px"})})]})})}else"done"!==this.state.dataState&&this.setState({dataState:"done",panel:new i.G1({direction:"row",children:[new i.vA({body:i.d0.table().setHoverHeader(!0).setOverrides(e=>e.matchFieldsWithName("spanID").overrideCustomFieldConfig("hidden",!0).matchFieldsWithName("traceService").overrideCustomFieldConfig("width",350).matchFieldsWithName("traceName").overrideCustomFieldConfig("width",350)).build()})]})})}else{if("loading"===this.state.dataState)return;this.setState({dataState:"loading",panel:new i.G1({direction:"row",children:[new v.G({component:D})]})})}}constructor(e){super(P({dataState:"empty"},e)),C(this,"getLinkToExplore",(e,t)=>{const n=(0,m.zY)(this),a=(0,m.U4)(n),r=i.jh.getTimeRange(this).state.value,o=JSON.stringify({"explore-traces":{range:(0,s.toURLRange)(r.raw),queries:[{refId:"traceId",queryType:"traceql",query:e,datasource:a}],panelsState:{trace:{spanId:t}},datasource:a}});var l;const u=null!==(l=c.config.appSubUrl)&&void 0!==l?l:"";return s.urlUtil.renderUrl(`${u}/explore`,{panes:o,schemaVersion:1})}),C(this,"onChange",e=>{const t=(0,m.gi)(this);t.getValue()!==e&&(t.changeValueTo(e),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.span_list_columns_changed,{columns:e}))}),this.addActivationHandler(()=>{this.setState({$data:new i.Es({transformations:this.setupTransformations()})});const e=i.jh.getData(this);this.updatePanel(e.state.data),this._subs.add(e.subscribeToState(e=>{this.updatePanel(e.data)}))})}}C(_,"Component",({model:e})=>{const{panel:t}=e.useState(),n=N((0,d.useTheme2)()),a=(0,m.gi)(e),{attributes:i}=(0,m.YX)(e).useState();var o;if(t)return r().createElement("div",{className:n.container},r().createElement("div",{className:n.header},r().createElement("div",{className:n.description},"View a list of spans for the current set of filters."),r().createElement(E,{options:null!==(o=null==i?void 0:i.map(e=>(0,s.toOption)(e)))&&void 0!==o?o:[],value:a.getValue(),onChange:e.onChange})),r().createElement(t.Component,{model:t}))});const N=e=>({container:(0,u.css)({display:"contents",'[role="cell"] > div':{display:"flex",width:"100%"},".cell-link-wrapper":{display:"flex",gap:"4px",justifyContent:"space-between",alignItems:"center",width:"100%",a:{padding:4,fontSize:0,":hover":{background:e.colors.background.secondary}}},".cell-link":{color:e.colors.text.link,cursor:"pointer",maxWidth:"300px",overflow:"hidden",textOverflow:"ellipsis",":hover":{textDecoration:"underline"}}}),description:(0,u.css)({fontSize:e.typography.h6.fontSize,padding:`${e.spacing(1)} 0 ${e.spacing(2)} 0`}),header:(0,u.css)({display:"flex",justifyContent:"space-between",alignItems:"flex-start",gap:"10px"})}),D=()=>{const e=(0,d.useStyles2)(T);return r().createElement("div",{className:e.container},r().createElement("div",{className:e.title},r().createElement(h.A,{count:1,width:80})),[...Array(3)].map((t,n)=>r().createElement("div",{className:e.row,key:n},[...Array(6)].map((t,n)=>r().createElement("span",{className:e.rowItem,key:n},r().createElement(h.A,{count:1}))))))};function T(e){return{container:(0,u.css)({height:"100%",width:"100%",position:"absolute",backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,padding:"5px"}),title:(0,u.css)({marginBottom:"20px"}),row:(0,u.css)({marginBottom:"5px",display:"flex",justifyContent:"space-around"}),rowItem:(0,u.css)({width:"14%"})}}function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class A extends i.Bs{_onActivate(){var e;this._subs.add(null===(e=(0,m.YX)(this).state.$data)||void 0===e?void 0:e.subscribeToState(()=>{this.updateBody()})),this._subs.add((0,m.YX)(this).subscribeToState((e,t)=>{var n,a;(null===(n=e.$data)||void 0===n?void 0:n.state.key)!==(null===(a=t.$data)||void 0===a?void 0:a.state.key)&&this.updateBody()})),this._subs.add((0,m.H_)(this).subscribeToState((e,t)=>{e.value!==t.value&&this.updateBody()})),this.updateBody()}updateBody(){this.setState({body:new _({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){I(e,t,n[t])})}return e}({},e)),this.addActivationHandler(this._onActivate.bind(this))}}function $(e){if(e.attributes)for(const n of e.attributes){var t;if("nestedSetLeft"===n.key)return parseInt(n.value.intValue||(null===(t=n.value.Value)||void 0===t?void 0:t.int_value)||"0",10)}throw new Error("nestedSetLeft not found!")}function L(e){if(e.attributes)for(const n of e.attributes){var t;if("nestedSetRight"===n.key)return parseInt(n.value.intValue||(null===(t=n.value.Value)||void 0===t?void 0:t.int_value)||"0",10)}throw new Error("nestedSetRight not found!")}function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}I(A,"Component",({model:e})=>{const{body:t}=e.useState();return t&&r().createElement(t.Component,{model:t})});class B{addSpan(e){this.left=Math.min($(e),this.left),this.right=Math.max(L(e),this.right),this.spans.push(e)}addChild(e){e.parent=this,this.children.push(e)}isChild(e){return $(e)>this.left&&L(e)<this.right}findMatchingChild(e){const t=z(e);for(const e of this.children)if(e.name===t)return e;return null}constructor({name:e,serviceName:t,operationName:n,spans:a,left:r,right:s,traceID:i}){V(this,"name",void 0),V(this,"serviceName",void 0),V(this,"operationName",void 0),V(this,"spans",void 0),V(this,"left",void 0),V(this,"right",void 0),V(this,"children",void 0),V(this,"parent",void 0),V(this,"traceID",void 0),this.name=e,this.serviceName=t,this.operationName=n,this.spans=a,this.left=r,this.right=s,this.children=[],this.parent=null,this.traceID=i}}function F(e){var t,n,a;const r=null===(t=e.attributes)||void 0===t?void 0:t.find(e=>"service.name"===e.key);var s,i,o,l;return new B({left:$(e),right:L(e),name:z(e),serviceName:null!==(i=null!==(s=null==r?void 0:r.value.stringValue)&&void 0!==s?s:null==r||null===(a=r.value)||void 0===a||null===(n=a.Value)||void 0===n?void 0:n.string_value)&&void 0!==i?i:"",operationName:null!==(o=e.name)&&void 0!==o?o:"",spans:[e],traceID:null!==(l=e.traceId)&&void 0!==l?l:""})}function z(e){let t="";for(const n of e.attributes||[])"service.name"===n.key&&n.value.stringValue&&(t=n.value.stringValue);return`${t}:${e.name}`}function M(e){e.left=Number.MAX_SAFE_INTEGER,e.right=Number.MIN_SAFE_INTEGER;for(const t of e.children)M(t)}var R=n(8130);function H(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function q(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){H(e,t,n[t])})}return e}const G="0000000000000000";class W extends i.Bs{_onActivate(){var e;this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState(e=>{var t,n,a,r;if((null===(t=e.data)||void 0===t?void 0:t.state)!==s.LoadingState.Loading&&(null===(n=e.data)||void 0===n?void 0:n.state)!==s.LoadingState.Streaming){if((null===(a=e.data)||void 0===a?void 0:a.state)===s.LoadingState.Done&&(null===(r=e.data)||void 0===r?void 0:r.series.length)){var o;const t=null===(o=e.data)||void 0===o?void 0:o.series[0].fields[0].values[0];if(t){const e=function(e){const t=new B({name:"root",serviceName:"",operationName:"",left:Number.MIN_SAFE_INTEGER,right:Number.MAX_SAFE_INTEGER,spans:[],traceID:""});if(e&&e.length>0)for(const a of e){var n;if(1!==(null===(n=a.spanSets)||void 0===n?void 0:n.length))throw new Error("there should be only 1 spanset!");const e=parseInt(a.startTimeUnixNano||"0",10),r=a.spanSets[0];r.spans.sort((e,t)=>$(e)-$(t));let s=t;M(t);for(const t of r.spans){for(t.traceId=a.traceID,t.startTimeUnixNano=""+(parseInt(t.startTimeUnixNano,10)-e);null!==s.parent&&!s.isChild(t);)s=s.parent;const n=s.findMatchingChild(t);if(n){n.addSpan(t),s=n;continue}const r=F(t);r.traceID=a.traceID,s.addChild(r),s=r}}return t}(JSON.parse(t));e.children.sort((e,t)=>K(t)-K(e)),this.setState({loading:!1,tree:e,panel:new i.G1({height:"100%",wrap:"wrap",children:this.getPanels(e)})})}}}else this.setState({loading:!0})}))}getPanels(e){return e.children.map(e=>new i.vA({height:150,width:"100%",minHeight:"400px",body:this.getPanel(e)}))}getPanel(e){const t=i.jh.getTimeRange(this),n=t.state.value.from,a=t.state.value.to,r=(0,m.w$)(this);return i.d0.traces().setTitle(`Structure for ${e.serviceName} [${K(e)} spans used]`).setOption("createFocusSpanLink",(e,t)=>({title:"Open trace",href:"#",onClick:()=>r(e,t),origin:{},target:"_self"})).setData(new i.Zv({data:{state:s.LoadingState.Done,timeRange:{from:n,to:a,raw:{from:n,to:a}},series:[q({},this.buildData(e))]}})).build()}buildData(e){const t=this.getTrace(e,G),n=t[0].serviceName+":"+t[0].operationName;return(0,s.createDataFrame)({name:`Trace ${n}`,refId:`trace_${n}`,fields:[{name:"references",type:s.FieldType.other,values:t.map(e=>e.references)},{name:"traceID",type:s.FieldType.string,values:t.map(e=>e.traceID)},{name:"spanID",type:s.FieldType.string,values:t.map(e=>e.spanID)},{name:"parentSpanID",type:s.FieldType.string,values:t.map(e=>e.parentSpanId)},{name:"serviceName",type:s.FieldType.string,values:t.map(e=>e.serviceName)},{name:"operationName",type:s.FieldType.string,values:t.map(e=>e.operationName)},{name:"duration",type:s.FieldType.number,values:t.map(e=>e.duration)},{name:"startTime",type:s.FieldType.number,values:t.map(e=>e.startTime)},{name:"statusCode",type:s.FieldType.number,values:t.map(e=>e.statusCode)}]})}getTrace(e,t){const n=e.spans.reduce((e,t)=>{var n,a;return"error"===(null===(a=t.attributes)||void 0===a||null===(n=a.find(e=>"status"===e.key))||void 0===n?void 0:n.value.stringValue)?e+1:e},0);let a=1e-4;t!==G&&(a=e.spans.reduce((e,t)=>e+parseInt(t.startTimeUnixNano,10),0)/e.spans.length/1e6);const r=[{references:e.spans.slice(-5).map(e=>({refType:"EXTERNAL",traceID:e.traceId,spanID:e.spanID})),traceID:e.traceID,spanID:e.spans[0].spanID,parentSpanId:t,serviceName:e.serviceName,operationName:e.operationName,statusCode:n>0?2:0,duration:e.spans.reduce((e,t)=>e+parseInt(t.durationNanos,10),0)/e.spans.length/1e6,startTime:a}];for(const t of e.children)r.push(...this.getTrace(t,e.spans[0].spanID));return r}constructor(e){super(q({$data:new i.Es({$data:new i.dt({datasource:l.Vl,queries:[U(e.metric)]}),transformations:l.s9}),loading:!0},e)),this.addActivationHandler(this._onActivate.bind(this))}}function U(e){let t,n="";switch(e){case"errors":t="status = error",n="status = error";break;case"duration":t=`duration > ${l.Ld}`,n=`duration > ${l.xT}`;break;default:t="kind = server"}return{refId:"A",query:`{${l.ui} ${n.length?`&& ${n}`:""}} &>> { ${t} } | select(status, resource.service.name, name, nestedSetParent, nestedSetLeft, nestedSetRight)`,queryType:"traceql",tableType:"raw",limit:200,spss:20,filters:[]}}H(W,"Component",({model:e})=>{var t,n;const{tree:a,loading:i,panel:o,$data:c}=e.useState(),u=Y((0,d.useTheme2)()),p=(0,d.useTheme2)(),f=(0,m.zY)(e),{value:v}=f.getMetricVariable().useState(),g=v;let b,y=i||!(null==a?void 0:a.children.length);(null==c||null===(t=c.state.data)||void 0===t?void 0:t.state)===s.LoadingState.Done&&(y=!1);let w="";switch(g){case"rate":b=r().createElement(r().Fragment,null,r().createElement("div",null,"Analyse the service structure of the traces that match the current filters."),r().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),w="server";break;case"errors":b=r().createElement(r().Fragment,null,r().createElement("div",null,"Analyse the errors structure of the traces that match the current filters."),r().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),w="error";break;case"duration":b=r().createElement(r().Fragment,null,r().createElement("div",null,"Analyse the structure of slow spans from the traces that match the current filters."),r().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),w="slow"}const S=rt(g),O=r().createElement(r().Fragment,null,r().createElement(d.Text,{textAlignment:"center",variant:"h3"},l.PL),r().createElement(d.Text,{textAlignment:"center",variant:"body"},r().createElement("div",{className:u.longText},"The structure tab shows ",w," spans beneath what you are currently investigating. Currently, there are no descendant ",w," spans beneath the spans you are investigating.")),r().createElement(d.Stack,{gap:.5,alignItems:"center"},r().createElement(d.Icon,{name:"info-circle"}),r().createElement(d.Text,{textAlignment:"center",variant:"body"},"The structure tab works best with full traces.")),r().createElement("div",{className:u.actionContainer},"Read more about",r().createElement("div",{className:u.action},r().createElement(d.LinkButton,{icon:"external-link-alt",fill:"solid",size:"sm",target:"_blank",href:"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/concepts/#trace-structure"},`${S.toLowerCase()}`))));return r().createElement(d.Stack,{direction:"column",gap:1},r().createElement("div",{className:u.description},b),y&&r().createElement(d.Stack,{direction:"column",gap:2},r().createElement(h.A,{count:4,height:200,baseColor:p.colors.background.secondary,highlightColor:p.colors.background.primary})),!y&&a&&a.children.length>0&&r().createElement("div",{className:u.traceViewList},o&&r().createElement(o.Component,{model:o})),(null==c||null===(n=c.state.data)||void 0===n?void 0:n.state)===s.LoadingState.Done&&!(null==a?void 0:a.children.length)&&r().createElement(R.p,{message:O,padding:"32px"}))});const Y=e=>({description:(0,u.css)({fontSize:e.typography.h6.fontSize,padding:`${e.spacing(1)} 0`}),traceViewList:(0,u.css)({display:"flex",flexDirection:"column",gap:e.spacing.x1,'div[class*="panel-content"] > div':{overflow:"auto",'> :not([class*="TraceTimelineViewer"])':{display:"none"}},'div[data-testid="span-detail-component"] > :nth-child(4) > :nth-child(1)':{display:"none"},".span-detail-row":{display:"none"},'div[data-testid="TimelineRowCell"]':{'button[role="switch"]':{cursor:"text"}},'div[data-testid="span-view"]':{cursor:"text !important"}}),longText:(0,u.css)({maxWidth:"800px",margin:"0 auto"}),action:(0,u.css)({marginLeft:e.spacing(1)}),actionContainer:(0,u.css)({display:"flex",justifyContent:"space-between",alignItems:"center"})});function K(e){let t=e.spans.length;for(const n of e.children)t+=K(n);return t}var X=n(5540);function Q({options:e,radioAttributes:t,value:n,onChange:s,showAll:i=!1,model:o}){var c,u;const p=(0,d.useStyles2)(Z),f=(0,d.useTheme2)(),{fontSize:v}=f.typography,[g,h]=(0,a.useState)(""),[b,y]=(0,a.useState)(!0),[w,S]=(0,a.useState)(0),O=(0,a.useRef)(null),{initialGroupBy:x}=(0,m.zY)(o).useState(),{filters:E}=(0,m.gG)(o).useState(),{value:j}=(0,m.H_)(o).useState(),k=j;(0,X.w)({ref:O,onResize:()=>{const e=O.current;e&&S(e.clientWidth)}});const C=(0,a.useMemo)(()=>{let n=0;return t.filter(t=>{let n=!!e.find(e=>e.value===t);return!E.find(e=>e.key===t&&("="===e.operator||"!="===e.operator))&&(E.find(e=>"nestedSetParent"===e.key)&&(n=n&&"rootName"!==t&&"rootServiceName"!==t),"rate"!==k&&"errors"!==k||(n=n&&"status"!==t),n)}).map(e=>({label:e.replace(l.zd,"").replace(l.$d,""),text:e,value:e})).filter(e=>{const t=e.label||e.text||"",a=(0,d.measureText)(t,v).width;return n+a+40+180<w&&(n+=a+40,!0)})},[t,e,E,k,v,w]),P=(0,a.useMemo)(()=>{const t=e.filter(e=>!C.find(t=>{var n;return t.value===(null===(n=e.value)||void 0===n?void 0:n.toString())}));return J(t,g)},[g,e,C]),_=e=>e.filter(e=>{var t;return!l.uK.includes(null===(t=e.value)||void 0===t?void 0:t.toString())}).map(e=>{var t;return{label:null===(t=e.label)||void 0===t?void 0:t.replace(l.zd,"").replace(l.$d,""),value:e.value}});var N;const D=null!==(N=null!=x?x:null===(c=C[0])||void 0===c?void 0:c.value)&&void 0!==N?N:null===(u=P[0])||void 0===u?void 0:u.value;(0,a.useEffect)(()=>{D&&!i&&b&&(s(D,!0),y(!1))},[n,D,i,s,b]),(0,a.useEffect)(()=>{t.length>0&&y(!0)},[t]),(0,a.useEffect)(()=>{E.some(e=>e.key===n)&&y(!0)},[E,n]);const T=i?[{label:l.y2,value:l.y2}]:[],I=i?l.y2:"";return r().createElement(d.Field,{label:"Group by"},r().createElement("div",{ref:O,className:p.container},C.length>0&&r().createElement(d.RadioButtonGroup,{options:[...T,...C],value:n,onChange:s}),r().createElement(d.Select,{value:n&&_(P).some(e=>e.value===n)?n:null,placeholder:"Other attributes",options:_(P),onChange:e=>{var t;const n=null!==(t=null==e?void 0:e.value)&&void 0!==t?t:I;s(n)},className:p.select,isClearable:!0,onInputChange:(e,{action:t})=>{"input-change"===t&&h(e)},onCloseMenu:()=>h(""),virtualized:!0})))}function Z(e){return{select:(0,u.css)({maxWidth:e.spacing(22)}),container:(0,u.css)({display:"flex",gap:e.spacing(1)})}}const J=(e,t)=>{if(0===e.length)return[];if(0===t.length)return e.slice(0,l.nr);const n=t.toLowerCase();return e.filter(e=>!!(e.value&&e.value.length>0)&&e.value.toLowerCase().includes(n)).slice(0,l.nr)};function ee(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class te extends i.Bs{Selector({model:e}){const{active:t,options:n}=e.useState();return r().createElement(d.Field,{label:"View"},r().createElement(d.RadioButtonGroup,{options:n,value:t,onChange:e.onLayoutChange}))}constructor(...e){super(...e),ee(this,"onLayoutChange",e=>{this.setState({active:e}),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.layout_type_changed,{layout:e})})}}ee(te,"Component",({model:e})=>{const{layouts:t,options:n,active:a}=e.useState(),s=n.findIndex(e=>e.value===a);if(-1===s)return null;const i=t[s];return r().createElement(i.Component,{model:i})});var ne=n(2085),ae=n(9151),re=n(5533),se=n(8404);const ie=()=>i.d0.timeseries().setOption("legend",{showLegend:!1}).setOption("tooltip",{mode:d.TooltipDisplayMode.Multi}).setCustomFieldConfig("fillOpacity",15);var oe=n(7313),le=n(3241);var ce=n(9546),ue=n(783);function de(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function me(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){de(e,t,n[t])})}return e}function pe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function fe(e,t,n){const a=(0,m.zY)(e).getMetricVariable().getValue(),r=(0,re.g)(a,t.getValueText()),o={};return new te({$behaviors:[e=>{const t=new Map,n=e.subscribeToEvent(l.sv,n=>{const a=n.payload.series;null==a||a.forEach(e=>{e.fields.slice(1).forEach(n=>{t.set(e.refId,Math.max(...n.values.filter(e=>e)))})}),function(e,t){const n=i.jh.findAllObjects(e,e=>e instanceof i.Eb);for(const e of n)e.clearFieldConfigCache(),e.setState({fieldConfig:(0,le.merge)((0,le.cloneDeep)(e.state.fieldConfig),{defaults:{max:t}})})}(e,Math.max(...t.values()))});return()=>{n.unsubscribe()}}],$data:new i.Es({$data:new oe.$({maxDataPoints:64,datasource:l.Vl,queries:[r]}),transformations:[...(0,ce.G)((0,m.w$)(e)),()=>e=>e.pipe((0,b.map)(e=>(e.forEach(e=>(0,s.reduceField)({field:e.fields[1],reducers:[s.ReducerID.max]})),e.sort((e,t)=>{var n,a,r,s;return((null===(a=t.fields[1].state)||void 0===a||null===(n=a.calcs)||void 0===n?void 0:n.max)||0)-((null===(s=e.fields[1].state)||void 0===s||null===(r=s.calcs)||void 0===r?void 0:r.max)||0)}))))]}),options:[{value:"single",label:"Single"},{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new i.G1({direction:"column",children:[new i.vA({minHeight:300,body:("duration"===a?ie().setUnit("s"):ie()).build()})]}),new ae.hE({body:new i.gF({templateColumns:l.MV,autoRows:"200px",isLazy:!0,children:[]}),groupBy:!0,getLayoutChild:ve(o,m.ee,t,a,n)}),new ae.hE({body:new i.gF({templateColumns:"1fr",autoRows:"200px",isLazy:!0,children:[]}),groupBy:!0,getLayoutChild:ve(o,m.ee,t,a,n)})]})}function ve(e,t,n,a,r){return(s,o)=>{var l;const c=o.name?e[o.name]:void 0,u=new i.Zv({data:pe(me({},s),{annotations:null===(l=s.annotations)||void 0===l?void 0:l.filter(e=>e.refId===o.refId),series:[pe(me({},o),{fields:o.fields.sort((e,t)=>{var n,a,r;return(null===(r=e.labels)||void 0===r||null===(a=r.status)||void 0===a?void 0:a.localeCompare((null===(n=t.labels)||void 0===n?void 0:n.status)||""))||0})})]})});var d;if(c)return null===(d=c.state.body)||void 0===d||d.setState({$data:u}),c;const p=i.jh.interpolate(n,(0,re.n)({metric:a,extraFilters:`${n.getValueText()}=${(0,m.xo)((0,m.ee)(o))}`})),f=("duration"===a?ie().setUnit("s"):(0,se.z)(a)).setTitle(t(o,n.getValueText())).setMenu(new ue.GD({query:p,labelValue:(0,m.ee)(o)})).setData(u),v=r(o);v&&f.setHeaderActions(v);const g=new i.xK({body:f.build()});return o.name&&(e[o.name]=g),g}}function ge({description:e,tags:t}){const n=(a=(0,d.useTheme2)(),{infoFlex:(0,u.css)({display:"flex",gap:a.spacing(2),alignItems:"center",padding:`${a.spacing(1)} 0 ${a.spacing(2)} 0`}),tagsFlex:(0,u.css)({display:"flex",gap:a.spacing(1),alignItems:"center"}),tag:(0,u.css)({display:"inline-block",width:a.spacing(2),height:a.spacing(.5),borderRadius:a.spacing(.5)})});var a;return r().createElement("div",{className:n.infoFlex},r().createElement("div",{className:n.tagsFlex},e),t.length>0&&t.map(e=>r().createElement("div",{className:n.tagsFlex,key:e.label},r().createElement("div",{className:n.tag,style:{backgroundColor:e.color}}),r().createElement("div",null,e.label))))}function he(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class be extends i.Bs{_onActivate(){const e=(0,m.pl)(this);e.subscribeToState(()=>{this.setBody(e)}),(0,m.YX)(this).subscribeToState(()=>{this.setBody(e)}),this.setBody(e)}onReferencedVariableValueChanged(){const e=(0,m.pl)(this);e.changeValueTo(l.u0[0]),this.setBody(e)}onAddToFiltersClick(e){(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.breakdown_add_to_filters_clicked,e)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){he(e,t,n[t])})}return e}({},e)),he(this,"_variableDependency",new i.Sh(this,{variableNames:[l.Ao,l.PU],onReferencedVariableValueChanged:this.onReferencedVariableValueChanged.bind(this)})),he(this,"setBody",e=>{this.setState({body:fe(this,e,t=>[new ne.Ms({frame:t,labelKey:e.getValueText(),onClick:this.onAddToFiltersClick})])})}),he(this,"onChange",(e,t)=>{const n=(0,m.pl)(this);n.getValueText()!==e&&(n.changeValueTo(e,void 0,!t),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.breakdown_group_by_changed,{groupBy:e}))}),this.addActivationHandler(this._onActivate.bind(this))}}function ye(e){return{container:(0,u.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,u.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,u.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2)}),controlsRight:(0,u.css)({flexGrow:0,display:"flex",justifyContent:"flex-end"}),scope:(0,u.css)({marginRight:e.spacing(2)}),groupBy:(0,u.css)({width:"100%"}),controlsLeft:(0,u.css)({display:"flex",justifyContent:"flex-left",justifyItems:"left",width:"100%",flexDirection:"row"})}}function we(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}he(be,"Component",({model:e})=>{const{value:t}=(0,m.pl)(e).useState(),n=t,s=n.includes(l.zd)||l.jx.includes(n)?l.BS:l.bD,[i,o]=(0,a.useState)(s),{body:c}=e.useState(),u=(0,d.useStyles2)(ye),{attributes:p}=(0,m.YX)(e).useState(),f=i===l.bD?l.$d:l.zd;let v=null==p?void 0:p.filter(e=>e.includes(f));i===l.BS&&(v=null==v?void 0:v.concat(l.jx));const g=(0,m.zY)(e),{value:h}=g.getMetricVariable().useState(),b=(e=>{switch(e){case"rate":return"Attributes are ordered by their rate of requests per second.";case"errors":return"Attributes are ordered by their rate of errors per second.";case"duration":return"Attributes are ordered by their average duration.";default:throw new Error("Metric not supported")}})(h);return(0,a.useEffect)(()=>{i!==s&&o(s)},[n]),r().createElement("div",{className:u.container},r().createElement(ge,{description:b,tags:"duration"===h?[]:[{label:"Rate",color:"green"},{label:"Error",color:"red"}]}),r().createElement("div",{className:u.controls},(null==v?void 0:v.length)&&r().createElement("div",{className:u.controlsLeft},r().createElement("div",{className:u.scope},r().createElement(d.Field,{label:"Scope"},r().createElement(d.RadioButtonGroup,{options:(0,m._g)([l.bD,l.BS]),value:i,onChange:o}))),r().createElement("div",{className:u.groupBy},r().createElement(Q,{options:(0,m._g)(v),radioAttributes:i===l.bD?l.u0:l.jx,value:n,onChange:e.onChange,model:e}))),c instanceof te&&r().createElement("div",{className:u.controlsRight},r().createElement(c.Selector,{model:c}))),r().createElement("div",{className:u.content},c&&r().createElement(c.Component,{model:c})))});class Se extends i.Bs{_onActivate(){this.updateBody()}updateBody(){this.setState({body:new be({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){we(e,t,n[t])})}return e}({},e)),we(this,"_variableDependency",new i.Sh(this,{variableNames:[l.PU]})),this.addActivationHandler(this._onActivate.bind(this))}}we(Se,"Component",({model:e})=>{const{body:t}=e.useState();return t&&r().createElement(t.Component,{model:t})});var Oe=n(1625),xe=n(9958);function Ee(e){if(!e.length)return[];e.sort((e,t)=>e-t);const t=(e[e.length-1]-e[0])/1e3,n=1e3*(0,xe.KS)(t,50),a=new Map;for(const t of e){const e=Math.floor(t/n)*n;a.set(e,(a.get(e)||0)+1)}return Array.from(a.entries()).map(([e,t])=>({time:e,count:t})).sort((e,t)=>e.time-t.time)}function je(e){return e?e.replace(/\s+/g," ").trim():""}function ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){ke(e,t,n[t])})}return e}class Pe extends i.Bs{updatePanel(e){var t,n,a,r;if((null==e?void 0:e.state)===s.LoadingState.Loading||(null==e?void 0:e.state)===s.LoadingState.NotStarted||!(null==e?void 0:e.state)||(null==e?void 0:e.state)===s.LoadingState.Streaming&&!(null===(n=e.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.length))this.setState({dataState:"loading",panel:new i.G1({direction:"row",children:[new v.G({component:Ne})]})});else if((null==e?void 0:e.state)!==s.LoadingState.Done&&(null==e?void 0:e.state)!==s.LoadingState.Streaming||0!==e.series.length&&(null===(r=e.series)||void 0===r||null===(a=r[0])||void 0===a?void 0:a.length)){if(((null==e?void 0:e.state)===s.LoadingState.Done||(null==e?void 0:e.state)===s.LoadingState.Streaming)&&e.series.length>0){const t=this.calculateExceptionsCount(e);this.setState({dataState:"done",exceptionsCount:t,panel:new i.G1({children:[new i.vA({body:i.d0.table().setOption("cellHeight",Oe.qM.Lg).setHoverHeader(!0).setOverrides(e=>e.matchFieldsWithName("Service").overrideCustomFieldConfig("width",200).matchFieldsWithName("Occurrences").overrideCustomFieldConfig("width",120).matchFieldsWithName("Time Series").overrideCustomFieldConfig("width",220).matchFieldsWithName("Last Seen").overrideCustomFieldConfig("width",120)).build()})]})})}}else this.setState({dataState:"empty",exceptionsCount:0,panel:new i.G1({children:[new i.vA({body:new g.v({message:l.PL,remedyMessage:l.a5,padding:"32px"})})]})})}createTransformation(){return()=>e=>e.pipe((0,b.map)(e=>e.map(e=>{const t=e.fields.find(e=>"exception.message"===e.name),n=e.fields.find(e=>"exception.type"===e.name),a=e.fields.find(e=>"service.name"===e.name),r=e.fields.find(e=>"time"===e.name);let i=[],o=[],l=[],c=[],u=[],m=[];if(t&&t.values.length){const e=function(e,t,n,a){const r=new Map,s=new Map,i=new Map,o=new Map,l=new Map,c=new Map;for(let l=0;l<e.values.length;l++){const u=e.values[l],d=null==t?void 0:t.values[l],m=null==n?void 0:n.values[l],p=null==a?void 0:a.values[l];if(u){const e=je(u);if(r.set(e,(r.get(e)||0)+1),!s.has(e)&&d&&s.set(e,d),!o.has(e)&&p&&o.set(e,p),m){const t="string"==typeof m?parseFloat(m):m;c.has(e)||c.set(e,[]),c.get(e).push(t),t>(i.get(e)||0)&&i.set(e,t)}}}for(const[e,t]of c.entries()){const n=Ee(t);l.set(e,n)}const u=Array.from(r.entries()).sort((e,t)=>t[1]-e[1]);return{messages:u.map(([e])=>e),types:u.map(([e])=>s.get(e)||""),occurrences:u.map(([,e])=>e),services:u.map(([e])=>o.get(e)||""),timeSeries:u.map(([e])=>l.get(e)||[]),lastSeenTimes:u.map(([e])=>{const t=i.get(e);if(!t)return"";const n=Date.now()-t;return n<6e4?"Just now":n<36e5?`${Math.floor(n/6e4)}m ago`:n<864e5?`${Math.floor(n/36e5)}h ago`:`${Math.floor(n/864e5)}d ago`})}}(t,n,r,a);i=e.messages,o=e.types,l=e.occurrences,c=e.lastSeenTimes,u=e.services,m=e.timeSeries}const p={type:d.TableCellDisplayMode.Custom,cellComponent:e=>{const t=e.value;return this.renderSparklineCell(t)}};return function(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}(Ce({},e),{length:i.length,fields:[{name:"Message",type:s.FieldType.string,values:i,config:{links:i.length>0?[this.createDataLink()]:[]}},{name:"Type",type:s.FieldType.string,values:o,config:{}},{name:"Trace Service",type:s.FieldType.string,values:u,config:{}},{name:"Occurrences",type:s.FieldType.number,values:l,config:{}},{name:"Frequency",type:s.FieldType.other,values:m,config:{custom:{cellOptions:p}}},{name:"Last Seen",type:s.FieldType.string,values:c,config:{}}]})})))}createDataLink(){return{title:"View traces for this exception",url:"",onClick:e=>{var t;const n=null===(t=e.origin)||void 0===t?void 0:t.rowIndex;if(void 0!==n){var a,r,s;const t=null===(s=e.origin)||void 0===s||null===(r=s.field)||void 0===r||null===(a=r.values)||void 0===a?void 0:a[n];t&&((0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.exception_message_clicked),this.navigateToTracesWithFilter(t))}}}}escapeFilterValue(e){return e.replace(/[\n\r\t]/g," ").replace(/\s+/g," ").replace(/\\/g,"\\\\").replace(/"/g,'"').trim()}calculateExceptionsCount(e){var t;if(!(null==e||null===(t=e.series)||void 0===t?void 0:t[0]))return 0;const n=e.series[0].fields.find(e=>"Occurrences"===e.name);return(null==n?void 0:n.values)?n.values.reduce((e,t)=>e+(t||0),0):0}getExceptionsCount(){return this.state.exceptionsCount||0}constructor(e){super(Ce({$data:new i.Es({$data:new i.dt({datasource:l.Vl,queries:[{refId:"A",query:`{${l.ui} && status = error} | select(resource.service.name, event.exception.message,event.exception.stacktrace,event.exception.type) with(most_recent=true)`,queryType:"traceql",tableType:"spans",limit:400,spss:10,filters:[]}]}),transformations:[]}),dataState:"empty"},e)),ke(this,"renderSparklineCell",e=>{const t=(0,d.useStyles2)(_e),n=()=>{const n=(0,d.useTheme2)();if(!e||!e.length)return r().createElement("div",{className:t.sparklineMessage},"No data");const a=e.map(e=>e.count),i=e.map(e=>e.time),o=a.filter(e=>isFinite(e)&&!isNaN(e)),l=i.filter(e=>isFinite(e)&&!isNaN(e));if(o.length<2||l.length<2)return r().createElement("div",{className:t.sparklineMessage},"Not enough data");const c=Math.min(...o),u=Math.max(...o),m=Math.min(...l),p=Math.max(...l),f=u-c,v=p-m,g=0===f?1:f,h=0===v?1:v,b={y:{name:"count",type:s.FieldType.number,values:o,config:{},state:{range:{min:c,max:u,delta:g}}},x:{name:"time",type:s.FieldType.time,values:l,config:{},state:{range:{min:m,max:p,delta:h}}}};return r().createElement("div",{className:t.sparklineContainer},r().createElement(d.Sparkline,{width:180,height:20,sparkline:b,theme:n,config:{custom:{drawStyle:Oe.GR.Line,fillOpacity:5,fillColor:n.colors.background.secondary,lineWidth:1,showPoints:Oe.yL.Never}}}))};return r().createElement(n,null)}),ke(this,"navigateToTracesWithFilter",e=>{const t=(0,m.gG)(this);if(!t)return;const n=(0,m.YX)(this);null==n||n.setActionView("traceList");const a=t.state.filters||[],r=this.escapeFilterValue(e),s=a.findIndex(e=>"event.exception.message"===e.key),i={key:"event.exception.message",operator:"=",value:r},o=s>=0?a.map((e,t)=>t===s?i:e):[...a,i];t.setState({filters:o})}),this.state.$data.setState({transformations:[...l.s9,this.createTransformation()]}),this.addActivationHandler(()=>{const e=this.state.$data;this._subs.add(e.subscribeToState((e,t)=>{e.data!==t.data&&this.updatePanel(e.data)}))})}}ke(Pe,"Component",({model:e})=>{const t=(0,d.useStyles2)(_e),n=(0,d.useTheme2)(),{panel:a,dataState:s}=e.useState();return r().createElement("div",{className:t.container},r().createElement("div",{className:t.description},"View exception details from errored traces for the current set of filters."),"loading"===s&&r().createElement("div",{className:t.loadingContainer},r().createElement(h.A,{count:10,height:40,baseColor:n.colors.background.secondary,highlightColor:n.colors.background.primary})),a&&r().createElement(a.Component,{model:a}))});const _e=e=>({container:(0,u.css)({display:"flex",flexDirection:"column",gap:e.spacing(2),height:"100%"}),description:(0,u.css)({fontSize:e.typography.h6.fontSize,padding:`${e.spacing(1)} 0`}),loadingContainer:(0,u.css)({padding:e.spacing(2)}),sparklineContainer:(0,u.css)({width:"200px",display:"flex",alignItems:"center",justifyContent:"center"}),sparklineMessage:(0,u.css)({fontSize:e.typography.bodySmall.fontSize,color:e.colors.text.secondary,padding:e.spacing(1)})}),Ne=()=>{const e=(0,d.useStyles2)(De),t=(0,d.useTheme2)();return r().createElement("div",{className:e.container},r().createElement(h.A,{count:10,height:40,baseColor:t.colors.background.secondary,highlightColor:t.colors.background.primary}))};function De(e){return{container:(0,u.css)({padding:e.spacing(2)})}}var Te=n(4128),Ie=n(2468);function Ae(e){var t,n,a,r;let s="";if(!e)return"{}";e.query&&(s+=e.query);const i=[];(null===(t=e.duration)||void 0===t?void 0:t.from.length)&&i.push(`duration >= ${e.duration.from}`),(null===(n=e.duration)||void 0===n?void 0:n.to.length)&&i.push(`duration <= ${e.duration.to}`),i.length&&(s.length&&(s+=" && "),s+=i.join(" && "));const o=null===(a=e.timeRange)||void 0===a?void 0:a.from,l=null===(r=e.timeRange)||void 0===r?void 0:r.to;return`{${s}}, 10${o&&l?`, ${1e9*o}, ${1e9*l}`:""}`}function $e(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Le(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){$e(e,t,n[t])})}return e}function Ve(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Be(e,t,n,a){var r;const o=i.jh.getTimeRange(e),c=i.jh.getData(e),u=t.getValueText(),d=null===(r=c.state.data)||void 0===r?void 0:r.series.find(e=>e.name===u),m=[],p=null==d?void 0:d.fields.find(e=>"Value"===e.name),f=null==d?void 0:d.fields.find(e=>"Baseline"===e.name),v=null==d?void 0:d.fields.find(e=>"Selection"===e.name);if(p&&f&&v)for(let e=0;e<p.values.length;e++)p.values[e]&&(f.values[e]||v.values[e])&&m.push({name:p.values[e].replace(/"/g,""),length:1,fields:[{name:"Value",type:s.FieldType.string,values:["Baseline","Comparison"],config:{}},Ve(Le({},f),{values:[f.values[e]],labels:{[u]:p.values[e]},config:{displayName:"Baseline"}}),Ve(Le({},v),{values:[v.values[e]]})]});return new ae.hE({$data:new i.Es({$data:new i.Zv({data:{timeRange:o.state.value,state:s.LoadingState.Done,series:m}}),transformations:[()=>e=>e.pipe((0,b.map)(e=>(e.forEach(e=>(0,s.reduceField)({field:e.fields[2],reducers:[s.ReducerID.max]})),e.sort((e,t)=>{var n,a,r,s;return((null===(a=t.fields[2].state)||void 0===a||null===(n=a.calcs)||void 0===n?void 0:n.max)||0)-((null===(s=e.fields[2].state)||void 0===s||null===(r=s.calcs)||void 0===r?void 0:r.max)||0)}))))]}),body:new i.gF({templateColumns:l.MV,autoRows:"200px",isLazy:!0,children:[]}),getLayoutChild:ze({},Fe,n,a)})}const Fe=e=>e.name||"No name available";function ze(e,t,n,a){return(r,s)=>{const o=s.name?e[s.name]:void 0,l=new i.Zv({data:Ve(Le({},r),{series:[Le({},s)]})});var c;if(o)return null===(c=o.state.body)||void 0===c||c.setState({$data:l}),o;const u=(0,Te.x)(a).setTitle(t(s)).setData(l),d=n(s);d&&u.setHeaderActions(d);const m=new i.xK({body:u.build()});return s.name&&(e[s.name]=m),m}}class Me extends i.Bs{}var Re,He,qe;qe=({model:e})=>e.state.attribute?r().createElement(d.Button,{variant:"secondary",size:"sm",fill:"solid",onClick:()=>e.state.onClick()},"Inspect"):null,(He="Component")in(Re=Me)?Object.defineProperty(Re,He,{value:qe,enumerable:!0,configurable:!0,writable:!0}):Re[He]=qe;var Ge=n(1834);function We(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ue extends i.Bs{_onActivate(){const e=(0,m.pl)(this);e.changeValueTo(l.y2),this.updateData(),e.subscribeToState((t,n)=>{t.value!==n.value&&this.setBody(e)}),(0,m.h7)(this).subscribeToState(()=>{this.updateData(),this.setBody(e)}),(0,m.YX)(this).subscribeToState((t,n)=>{(0,le.isEqual)(t.selection,n.selection)||(this.updateData(),this.setBody(e))}),i.jh.getTimeRange(this).subscribeToState(()=>{this.updateData()}),this.setBody(e)}updateData(){const e=(0,m.YX)(this),t=i.jh.getTimeRange(this),n=t.state.value.from.unix(),a=t.state.value.to.unix(),r=(0,m.h7)(this).state.value,s=this.getFilteredAttributes(r);this.setState({$data:new i.Es({$data:new i.dt({datasource:l.Vl,queries:[Ye(n,a,Ae(e.state.selection))]}),transformations:[()=>e=>e.pipe((0,b.map)(e=>{const t=Ke(e);return Object.entries(t).filter(([e,t])=>!s.includes(e)).map(([e,t])=>Xe(e,t)).sort((e,t)=>{const n=(0,Ge.p)(e),a=(0,Ge.p)(t);return Math.abs(a.maxDifference)-Math.abs(n.maxDifference)})}))]})})}onReferencedVariableValueChanged(){const e=(0,m.pl)(this);e.changeValueTo(l.y2),this.setBody(e)}onAddToFiltersClick(e){(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.comparison_add_to_filters_clicked,e)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){We(e,t,n[t])})}return e}({},e)),We(this,"_variableDependency",new i.Sh(this,{variableNames:[l.Ao,l.CE],onReferencedVariableValueChanged:this.onReferencedVariableValueChanged.bind(this)})),We(this,"getFilteredAttributes",e=>"nestedSetParent<0"===e?["rootName","rootServiceName"]:[]),We(this,"setBody",e=>{const t=(0,m.zY)(this);this.setState({body:e.hasAllValue()||e.getValue()===l.y2?(0,Te.nF)(e=>new Me({attribute:e.name,onClick:()=>this.onChange(e.name||"")}),t.getMetricFunction()):Be(this,e,t=>[new ne.Ms({frame:t,labelKey:e.getValueText(),onClick:this.onAddToFiltersClick})],t.getMetricFunction())})}),We(this,"onChange",(e,t)=>{(0,m.pl)(this).changeValueTo(e,void 0,!t),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.select_attribute_in_comparison_clicked,{value:e})}),this.addActivationHandler(this._onActivate.bind(this))}}function Ye(e,t,n){const a=`${(0,Ie.duration)(t-e,"s").asSeconds()}s`;return{refId:"A",query:`{${l.ui}} | compare(${n})`,step:a,queryType:"traceql",tableType:"spans",limit:100,spss:10,filters:[]}}We(Ue,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,m.pl)(e),a=(0,m.zY)(e),{attributes:s}=(0,m.YX)(e).useState(),i=(0,d.useStyles2)(Ze);return r().createElement("div",{className:i.container},r().createElement(ge,{description:"Attributes are ordered by the difference between the baseline and selection values for each value.",tags:[{label:"Baseline",color:"duration"===a.getMetricFunction()?Te.bT:(0,d.getTheme)().visualization.getColorByName("semi-dark-green")},{label:"Selection",color:"duration"===a.getMetricFunction()?Te._E:(0,d.getTheme)().visualization.getColorByName("semi-dark-red")}]}),r().createElement("div",{className:i.controls},(null==s?void 0:s.length)&&r().createElement("div",{className:i.controlsLeft},r().createElement(Q,{options:(0,m._g)(s),radioAttributes:l.jx,value:n.getValueText(),onChange:e.onChange,showAll:!0,model:e})),t instanceof te&&r().createElement("div",{className:i.controlsRight},r().createElement(t.Selector,{model:t}))),r().createElement("div",{className:i.content},t&&r().createElement(t.Component,{model:t})))});const Ke=e=>e.reduce((e,t)=>{const n=t.fields.find(e=>"number"===e.type),a=Object.keys((null==n?void 0:n.labels)||{}).find(e=>!e.startsWith("__"));return a&&(e[a]=[...e[a]||[],t]),e},{}),Xe=(e,t)=>{const n={name:e,refId:e,fields:[],length:0},a={name:"Value",type:s.FieldType.string,values:[],config:{},labels:{[e]:e}},r={name:"Baseline",type:s.FieldType.number,values:[],config:{}},i={name:"Selection",type:s.FieldType.number,values:[],config:{}},o=t.reduce((t,n)=>{var a;const r=n.fields.find(e=>"number"===e.type),s=null==r||null===(a=r.labels)||void 0===a?void 0:a[e];return s&&(t[s]=[...t[s]||[],r]),t},{}),l=Qe(t,"baseline",o),c=Qe(t,"selection",o);return n.length=Object.keys(o).length,Object.entries(o).forEach(([e,t])=>{var n,s;a.values.push(e),r.values.push((null===(n=t.find(e=>{var t;return'"baseline"'===(null===(t=e.labels)||void 0===t?void 0:t.__meta_type)}))||void 0===n?void 0:n.values[0])/l),i.values.push((null===(s=t.find(e=>{var t;return'"selection"'===(null===(t=e.labels)||void 0===t?void 0:t.__meta_type)}))||void 0===s?void 0:s.values[0])/c)}),n.fields=[a,r,i],n};function Qe(e,t,n){const a=Object.values(n).reduce((e,n)=>{const a=n.find(e=>{var n;return(null===(n=e.labels)||void 0===n?void 0:n.__meta_type)===`"${t}"`});return e+((null==a?void 0:a.values[0])||0)},0);let r=e.reduce((e,n)=>{var a;const r=n.fields.find(e=>"number"===e.type);return(null==r||null===(a=r.labels)||void 0===a?void 0:a.__meta_type)===`"${t}_total"`?r.values[0]:e},1);return r<a||1===r||0===r?0===a?1:a:r}function Ze(e){return{container:(0,u.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,u.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,u.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2)}),controlsRight:(0,u.css)({flexGrow:0,display:"flex",justifyContent:"flex-end"}),controlsLeft:(0,u.css)({display:"flex",justifyContent:"flex-left",justifyItems:"left",width:"100%",flexDirection:"column"})}}function Je(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class et extends i.Bs{_onActivate(){const e=(0,m.H_)(this).getValue(),t=(0,m.YX)(this);if(!t.state.selection){const n=(0,Ge.F)(e);n&&t.setState({selection:n})}this.updateBody()}updateBody(){this.setState({body:new Ue({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){Je(e,t,n[t])})}return e}({},e)),Je(this,"_variableDependency",new i.Sh(this,{variableNames:[l.PU]})),this.addActivationHandler(this._onActivate.bind(this))}}Je(et,"Component",({model:e})=>{const{body:t}=e.useState();return t&&r().createElement(t.Component,{model:t})});var tt=n(3518);const nt=[{displayName:function(e){return"Breakdown"},value:"breakdown",getScene:function(){return new i.vA({body:new Se({})})}},{displayName:rt,value:"structure",getScene:function(e){return new i.vA({body:new W({metric:e})})}},{displayName:function(e){return"Comparison"},value:"comparison",getScene:function(){return new i.vA({body:new et({})})}},{displayName:function(e){return"Exceptions"},value:"exceptions",getScene:function(){return new i.vA({body:new Pe({})})}},{displayName:function(e){return"errors"===e?"Errored traces":"duration"===e?"Slow traces":"Traces"},value:"traceList",getScene:function(){return new i.vA({body:new A({})})}}];class at extends i.Bs{}function rt(e){switch(e){case"rate":return"Service structure";case"errors":return"Root cause errors";case"duration":return"Root cause latency"}}function st(e){return{actions:(0,u.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,top:5,zIndex:2}})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(at,"Component",({model:e})=>{var t,n,o,l;const c=(0,d.useStyles2)(st),[u,p]=(0,a.useState)(0),v=(0,m.YX)(e),g=(0,m.zY)(e),{actionView:h}=v.useState(),{value:b}=g.getMetricVariable().useState(),{allowedActionViews:y}=g.useState(),w=i.jh.getData(e).useState(),S=null===(o=w.data)||void 0===o||null===(n=o.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.length,O=nt.filter(e=>("exceptions"!==e.value||"errors"===b)&&(!(null==y?void 0:y.length)||y.includes(e.value))),x=(0,m.gG)(e),E=(0,m.h7)(e),j=i.jh.getTimeRange(e),{filters:k}=x.useState(),{value:C}=E.useState(),{value:P}=j.useState();return(0,a.useEffect)(()=>{if("errors"!==b)return void p(0);const t=(0,m.dB)(e);if(!t)return void p(0);p(t.getExceptionsCount());const n=t.subscribeToState((e,t)=>{e.exceptionsCount!==t.exceptionsCount&&p(e.exceptionsCount||0)});return()=>{n.unsubscribe()}},[b,e,h,k,C,P]),(0,a.useEffect)(()=>{var e;if(!v.state.hasSetView)return g.state.embedded&&(null===(e=w.data)||void 0===e?void 0:e.state)===s.LoadingState.Done&&void 0!==S&&S>20?(v.setState({hasSetView:!0}),void v.setActionView("traceList")):void 0},[null===(l=w.data)||void 0===l?void 0:l.state,g.state.embedded,v,S]),(0,tt.A)(()=>{1===O.length&&v.setActionView(O[0].value)}),1===O.length?null:r().createElement(d.Box,null,r().createElement("div",{className:c.actions},r().createElement(d.Stack,{gap:1},r().createElement(f,{exploration:g}))),r().createElement(d.TabsBar,null,O.map((e,t)=>r().createElement(d.Tab,{key:t,label:e.displayName(b),active:h===e.value,onChangeTab:()=>v.setActionView(e.value),counter:"traceList"===e.value?S:"exceptions"===e.value?u:void 0}))))});var it=n(3731),ot=n(410),lt=n(335);function ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ut extends i.Bs{_onActivate(){this.setState({$data:new i.Es({$data:new oe.$({maxDataPoints:"duration"===this.state.metric?24:64,datasource:l.Vl,queries:["duration"===this.state.metric?(0,it.z)():(0,re.g)(this.state.metric)]}),transformations:"duration"===this.state.metric?[...(0,ce.h)()]:[...(0,ce.G)((0,m.w$)(this))]}),panel:this.getVizPanel(this.state.metric)})}getVizPanel(e){return new i.G1({direction:"row",children:[new i.vA({body:"duration"===e?this.getDurationVizPanel():this.getRateOrErrorPanel(e)})]})}getRateOrErrorPanel(e){const t=(0,se.z)(e).setHoverHeader(!0).setDisplayMode("transparent");return"rate"===e?t.setCustomFieldConfig("axisLabel","span/s"):"errors"===e&&t.setTitle("Errors rate").setCustomFieldConfig("axisLabel","error/s").setColor({fixedColor:"semi-dark-red",mode:"fixed"}),t.build()}getDurationVizPanel(){return(0,ot.dX)().setTitle("Histogram by duration").setHoverHeader(!0).setDisplayMode("transparent").build()}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){ct(e,t,n[t])})}return e}({isStreaming:!1},e)),this.addActivationHandler(()=>{this._onActivate();const e=i.jh.getData(this);this._subs.add(e.subscribeToState(e=>{var t,n,a;this.setState({isStreaming:(null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Streaming}),(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Done?0===e.data.series.length||0===e.data.series[0].length||(0,m.W6)(e)?this.setState({panel:new i.G1({children:[new i.vA({body:new g.v({imgWidth:110})})]})}):this.setState({panel:this.getVizPanel(this.state.metric)}):(null===(a=e.data)||void 0===a?void 0:a.state)===s.LoadingState.Loading&&this.setState({panel:new i.G1({direction:"column",maxHeight:yt,height:yt,children:[new v.G({component:()=>(0,ae.NO)(1)})]})})}))})}}function dt(e){return{container:(0,u.css)({flex:1,width:"100%",display:"flex",flexDirection:"column",border:`1px solid ${e.colors.border.weak}`,borderRadius:"2px",background:e.colors.background.primary,paddingTop:"8px","section, section:hover":{borderColor:"transparent"},"& .show-on-hover":{display:"none"}}),headerWrapper:(0,u.css)({display:"flex",alignItems:"center",position:"absolute",top:"4px",left:"8px",zIndex:2}),clickable:(0,u.css)({cursor:"pointer",maxHeight:yt,'[class*="loading-state-scene"]':{height:yt,overflow:"hidden"},":hover":{background:e.colors.background.secondary,input:{backgroundColor:"#ffffff",border:"5px solid #3D71D9",cursor:"pointer"}}}),radioButton:(0,u.css)({display:"block"}),indicatorWrapper:(0,u.css)({position:"absolute",top:"4px",right:"8px",zIndex:2})}}function mt(e,t,n,a,r,s,i){try{var o=e[s](i),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function pt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ft(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){pt(e,t,n[t])})}return e}ct(ut,"Component",({model:e})=>{const{panel:t,isStreaming:n}=e.useState(),a=(0,d.useStyles2)(dt),s=(0,m.zY)(e),i=()=>{(0,k.EE)(k.NO.common,k.ir.common.metric_changed,{metric:e.state.metric,location:"panel"}),s.onChangeMetricFunction(e.state.metric)};if(t)return r().createElement("div",{className:(0,u.css)([a.container,a.clickable]),onClick:i},r().createElement("div",{className:a.headerWrapper},r().createElement(d.RadioButtonList,{className:a.radioButton,name:`metric-${e.state.metric}`,options:[{title:"",value:"selected"}],onChange:()=>i(),value:"not-selected"})),n&&r().createElement("div",{className:a.indicatorWrapper},r().createElement(lt.M,{isStreaming:!0,iconSize:10})),r().createElement(t.Component,{model:t}))});class vt extends i.Bs{_onActivate(){const e=new URLSearchParams(window.location.search).get("actionView");e&&nt.find(t=>t.value===e)&&this.setState({actionView:e}),this.updateBody();const t=(0,m.zY)(this).getMetricVariable();this._subs.add(t.subscribeToState((e,t)=>{if(e.value!==t.value){const t=(0,Ge.F)(e.value);t&&this.setState({selection:t}),this.updateQueryRunner(e.value),this.updateExceptionsScene(e.value),this.updateBody()}})),this.updateExceptionsScene(t.getValue()),this._subs.add(this.subscribeToState((e,n)=>{var a,r;const s=i.jh.getTimeRange(this),o=null===(r=e.selection)||void 0===r||null===(a=r.timeRange)||void 0===a?void 0:a.from;o&&o<s.state.value.from.unix()&&this.setState({selection:void 0}),(0,le.isEqual)(e.selection,n.selection)||((0,m.pl)(this).changeValueTo(l.y2),this.updateQueryRunner(t.getValue()))})),this._subs.add((0,m._b)(this).subscribeToState(()=>{this.updateAttributes()})),this._subs.add((0,m.gi)(this).subscribeToState(()=>{this.updateQueryRunner(t.getValue())})),this.updateQueryRunner(t.getValue()),this.updateAttributes()}updateBody(){const e=(0,m.zY)(this).getMetricVariable().getValue(),t=nt.find(e=>e.value===this.state.actionView);this.setState({body:Ot(e,t?[null==t?void 0:t.getScene(e)]:void 0)}),void 0===this.state.actionView&&this.setActionView("breakdown")}updateExceptionsScene(e){if("errors"===e){if(!this.state.exceptionsScene){const e=new Pe({});this.setState({exceptionsScene:e}),setTimeout(()=>{e.activate()},0)}}else this.state.exceptionsScene&&this.setState({exceptionsScene:void 0})}updateAttributes(){return(e=function*(){var e;const t=yield(0,c.getDataSourceSrv)().get(l.gR,{__sceneObject:{value:this}});if(!t)return;const n={timeRange:i.jh.getTimeRange(this).state.value,filters:[]};null===(e=t.getTagKeys)||void 0===e||e.call(t,n).then(e=>{let t=[];t="data"in e?e.data:e;const n=t.map(e=>e.text);n!==this.state.attributes&&this.setState({attributes:n})})},function(){var t=this,n=arguments;return new Promise(function(a,r){var s=e.apply(t,n);function i(e){mt(s,a,r,i,o,"next",e)}function o(e){mt(s,a,r,i,o,"throw",e)}i(void 0)})}).call(this);var e}getUrlState(){return{actionView:this.state.actionView,selection:this.state.selection?JSON.stringify(this.state.selection):void 0}}updateFromUrl(e){if("string"==typeof e.actionView){if(this.state.actionView!==e.actionView){const t=nt.find(t=>t.value===e.actionView);t&&this.setActionView(t.value)}}else null===e.actionView&&this.setActionView("breakdown");if("string"==typeof e.selection){const t=JSON.parse(e.selection);(0,le.isEqual)(t,this.state.selection)||this.setState({selection:t})}}onUserUpdateSelection(e){this._urlSync.performBrowserHistoryAction(()=>{this.setState({selection:e})})}setActionView(e){const{body:t}=this.state,n=nt.find(t=>t.value===e),a=(0,m.zY)(this).getMetricVariable().getValue();if(t.state.children.length>1&&n){let r;r="exceptions"===e&&this.state.exceptionsScene?new i.vA({body:this.state.exceptionsScene}):n.getScene(a),t.setState({children:[...t.state.children.slice(0,2),r]}),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.action_view_changed,{oldAction:this.state.actionView,newAction:e}),this.setState({actionView:n.value})}}updateQueryRunner(e){var t;const n=this.state.selection;var a;const r=null!==(a=null===(t=(0,m.gi)(this).getValue())||void 0===t?void 0:t.toString())&&void 0!==a?a:"";this.setState({$data:new i.Es({$data:new i.dt({datasource:l.Vl,queries:[wt(e,r,n)],$timeRange:St(n)}),transformations:[...l.s9,...xt]})})}constructor(e){var t;super(ft({body:null!==(t=e.body)&&void 0!==t?t:new i.G1({children:[]})},e)),pt(this,"_urlSync",new i.So(this,{keys:["actionView","selection"]})),this.addActivationHandler(this._onActivate.bind(this))}}pt(vt,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,d.useStyles2)(ht);return r().createElement(r().Fragment,null,r().createElement("div",{className:n.title},r().createElement(d.Tooltip,{content:r().createElement(gt,null),placement:"right-start",interactive:!0},r().createElement("span",{className:n.hand},"Select metric type ",r().createElement(d.Icon,{name:"info-circle"})))),r().createElement(t.Component,{model:t}))});const gt=()=>{const e=(0,d.useStyles2)(ht);return r().createElement(d.Stack,{direction:"column",gap:1},r().createElement("div",{className:e.tooltip.title},"RED metrics for traces"),r().createElement("span",{className:e.tooltip.subtitle},"Explore rate, errors, and duration (RED) metrics generated from traces by Tempo."),r().createElement("div",{className:e.tooltip.text},r().createElement("div",null,r().createElement("span",{className:e.tooltip.emphasize},"Rate")," - Spans per second that match your filter, useful to find unusual spikes in activity"),r().createElement("div",null,r().createElement("span",{className:e.tooltip.emphasize},"Errors")," -Spans that are failing, overall issues in tracing ecosystem"),r().createElement("div",null,r().createElement("span",{className:e.tooltip.emphasize},"Duration")," - Amount of time those spans take, represented as a heat map (responds time, latency)")),r().createElement("div",{className:e.tooltip.button},r().createElement(d.LinkButton,{icon:"external-link-alt",fill:"solid",size:"sm",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces/concepts/#rate-error-and-duration-metrics",onClick:()=>(0,k.EE)(k.NO.common,k.ir.common.metric_docs_link_clicked)},"Read documentation")))};function ht(e){return{title:(0,u.css)({label:"title",display:"flex",gap:e.spacing.x0_5,fontSize:e.typography.bodySmall.fontSize,paddingBottom:e.spacing.x0_5,alignItems:"center"}),hand:(0,u.css)({label:"hand",cursor:"pointer"}),tooltip:{label:"tooltip",title:(0,u.css)({fontSize:"14px",fontWeight:500}),subtitle:(0,u.css)({marginBottom:e.spacing.x1}),text:(0,u.css)({label:"text",color:e.colors.text.secondary,div:{marginBottom:e.spacing.x0_5}}),emphasize:(0,u.css)({label:"emphasize",color:e.colors.text.primary}),button:(0,u.css)({marginBottom:e.spacing.x0_5})}}}const bt=240,yt=(bt-8)/2;function wt(e,t,n){const a=""!==t?` | select(${t})`:"";let r="";switch(e){case"errors":r=" && status = error";break;case"duration":if(n){var s,i;const e=[];(null===(s=n.duration)||void 0===s?void 0:s.from.length)&&e.push(`duration >= ${n.duration.from}`),(null===(i=n.duration)||void 0===i?void 0:i.to.length)&&e.push(`duration <= ${n.duration.to}`),e.length&&(r+="&& "+e.join(" && "))}r.length||(r=`&& duration > ${l.xT}`)}return{refId:"A",query:`{${l.ui}${r}}${a}`,queryType:"traceql",tableType:"spans",limit:200,spss:10,filters:[]}}function St(e){var t,n;const a=1e3*((null==e||null===(t=e.timeRange)||void 0===t?void 0:t.from)||0),r=1e3*((null==e||null===(n=e.timeRange)||void 0===n?void 0:n.to)||0);return a&&r?new i.JZ({from:a.toFixed(0),to:r.toFixed(0),value:{from:(0,s.dateTime)(a),to:(0,s.dateTime)(r),raw:{from:(0,s.dateTime)(a),to:(0,s.dateTime)(r)}}}):void 0}function Ot(e,t){const n=new ut("rate"===e?{metric:"errors"}:{metric:"rate"}),a=new ut("duration"===e?{metric:"errors"}:{metric:"duration"});return new i.G1({direction:"column",$behaviors:[new i.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair})],children:[new i.G1({direction:"row",ySizing:"content",children:[new i.vA({minHeight:bt,maxHeight:bt,width:"60%",body:new o.Mu({})}),new i.G1({direction:"column",minHeight:bt,maxHeight:bt,children:[new i.vA({minHeight:yt,maxHeight:yt,height:yt,body:n}),new i.vA({minHeight:yt,maxHeight:yt,height:yt,ySizing:"fill",body:a})]})]}),new i.vA({ySizing:"content",body:new at({})}),...t||[]]})}const xt=[()=>e=>e.pipe((0,b.map)(e=>e.map(e=>function(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}(ft({},e),{fields:e.fields.filter(e=>!e.name.startsWith("nestedSet"))})))),{id:"sortBy",options:{fields:{},sort:[{field:"Duration",desc:!0}]}},{id:"organize",options:{indexByName:{"Start time":0,status:1,"Trace Service":2,"Trace Name":3,Duration:4,"Span ID":5,"span.http.method":6,"span.http.request.method":7,"span.http.path":8,"span.http.route":9,"span.http.status_code":10,"span.http.response.status_code":11}}}]},3731:(e,t,n)=>{"use strict";n.d(t,{z:()=>r});var a=n(1454);function r(){return{refId:"A",query:`{${a.ui}} | histogram_over_time(duration)`,queryType:"traceql",tableType:"spans",limit:1e3,spss:10,filters:[]}}},4128:(e,t,n)=>{"use strict";n.d(t,{bT:()=>w,_E:()=>S,nF:()=>O,x:()=>j});var a=n(8010),r=n(9151),s=n(2007),i=n(1625),o=n(6089),l=n(5959),c=n.n(l),u=n(9262),d=n(2085),m=n(1834);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){p(e,t,n[t])})}return e}class v extends a.Bs{_onActivate(){const{frame:e}=this.state;this.setState(f({},(0,m.p)(e))),this._subs.add(this.subscribeToState((e,t)=>{if(e.frame!==t.frame){const{frame:t}=e;this.setState(f({},(0,m.p)(t)))}}))}getAttribute(){return this.state.frame.name}getValue(){const e=this.state.frame.fields.find(e=>"Value"===e.name);return null==e?void 0:e.values[this.state.maxDifferenceIndex||0]}onAddToFilters(){const e=(0,u.gG)(this),t=this.getAttribute();t&&(0,d.Qt)(e,t,this.getValue())}constructor(e){super(f({},e)),this.addActivationHandler(()=>this._onActivate())}}function g(e){return{container:(0,o.css)({display:"flex",flexDirection:"column",flexGrow:1,height:"100%"}),differenceContainer:(0,o.css)({display:"flex",flexDirection:"column",flexGrow:1,border:`1px solid ${e.colors.secondary.border}`,background:e.colors.background.primary,padding:"8px",marginBottom:e.spacing(2),fontSize:"12px",height:"116px"}),differenceValue:(0,o.css)({fontSize:"36px",fontWeight:"bold",textAlign:"center"}),value:(0,o.css)({textAlign:"center",color:e.colors.secondary.text,textWrap:"nowrap",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}),title:(0,o.css)({fontWeight:500})}}p(v,"Component",({model:e})=>{const{maxDifference:t,maxDifferenceIndex:n,panel:a}=e.useState(),r=(0,s.useStyles2)(g),i=e.getValue();var o;const l=null!==(o=e.state.frame.name)&&void 0!==o?o:"",m=(0,d.D9)((0,u.gG)(e),l,i.replace(/"/g,""));return c().createElement("div",{className:r.container},c().createElement(a.Component,{model:a}),c().createElement("div",{className:r.differenceContainer},void 0!==t&&void 0!==n&&c().createElement(c().Fragment,null,c().createElement(s.Stack,{gap:1,justifyContent:"space-between",alignItems:"center"},c().createElement("div",{className:r.title},"Highest difference"),!m&&c().createElement(s.Button,{size:"sm",variant:"primary",icon:"search-plus",fill:"text",onClick:()=>e.onAddToFilters()},"Add to filters")),c().createElement("div",{className:r.differenceValue},(100*Math.abs(t)).toFixed(0===t?0:2),"%"),c().createElement("div",{className:r.value},i))))});var h=n(1454);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){b(e,t,n[t])})}return e}const w="#5794F299",S="#FF9930";function O(e,t){return new r.hE({body:new a.gF({templateColumns:h.MV,autoRows:"320px",children:[]}),getLayoutChild:E({},x,e,t)})}const x=e=>e.name||"No name available";function E(e,t,n,r){return(s,i)=>{const o=i.name?e[i.name]:void 0,l=new a.Zv({data:(c=y({},s),u={series:[y({},i)]},u=null!=u?u:{},Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(u)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(u)).forEach(function(e){Object.defineProperty(c,e,Object.getOwnPropertyDescriptor(u,e))}),c)});var c,u;if(o){const e=o.state.body;return e.setState({frame:i}),e.state.panel.setState({$data:l}),o}const d=j(r).setTitle(t(i)).setData(l),m=n(i);m&&d.setHeaderActions(m);const p=new a.xK({body:new v({frame:i,panel:d.build()})});return i.name&&(e[i.name]=p),p}}function j(e){return a.d0.barchart().setOption("legend",{showLegend:!1}).setOption("tooltip",{mode:i.$N.Multi}).setMax(1).setOverrides(t=>{t.matchFieldsWithName("Value").overrideCustomFieldConfig("axisPlacement",s.AxisPlacement.Hidden),t.matchFieldsWithName("Baseline").overrideColor({mode:"fixed",fixedColor:"duration"===e?w:"semi-dark-green"}).overrideUnit("percentunit"),t.matchFieldsWithName("Selection").overrideColor({mode:"fixed",fixedColor:"duration"===e?S:"semi-dark-red"}).overrideUnit("percentunit")})}},5292:(e,t,n)=>{"use strict";n.d(t,{x:()=>v});var a=n(5959),r=n.n(a),s=n(8010),i=n(9240),o=n(2007),l=n(6089),c=n(5225),u=n(675);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const m=e=>{const t=(0,o.useStyles2)(g);return r().createElement(c.c.Menu,(n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){d(e,t,n[t])})}return e}({},e),a=null!=(a={className:t.customMenu})?a:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(a)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(a,e))}),n));var n,a};function p({selectProps:e}){const t=e.menuIsOpen?"angle-up":"angle-down";return r().createElement(o.Icon,{name:t,size:"md"})}const f=()=>{const e=(0,o.useStyles2)(g);return r().createElement("div",{className:e.heading},r().createElement(o.Text,{weight:"bold",variant:"bodySmall",color:"secondary"},"Primary signal"))};class v extends s.yP{}d(v,"Component",({model:e})=>{const t=(0,o.useStyles2)(g),{value:n,isReadOnly:s}=e.useState();(0,a.useEffect)(()=>{n||e.changeValueTo(s?i.Xn[1].value:i.Xn[0].value)});const l=i.Xn.slice(0,2),c=i.Xn.find(e=>e.value===n);c&&!l.some(e=>e.filter.key===c.filter.key)&&l.push(c);const d=i.Xn.filter(e=>!l.some(t=>t.value===e.value)),v=t=>{(0,u.EE)(u.NO.analyse_traces,u.ir.analyse_traces.primary_signal_changed,{primary_signal:t}),e.changeValueTo(t,void 0,!0)};return s?r().createElement(r().Fragment,null):r().createElement(r().Fragment,null,r().createElement(o.RadioButtonGroup,{options:l,value:n,onChange:v,disabled:s,className:t.buttonGroup}),r().createElement(o.Select,{options:[{label:"Primary signal",options:d}],value:"",placeholder:"",isSearchable:!1,isClearable:!1,width:4,onChange:e=>v(e.value),className:t.select,components:{IndicatorSeparator:()=>null,SingleValue:()=>null,Menu:m,DropdownIndicator:p,GroupHeading:f}}))});const g=e=>({select:l.css`
    [class$='input-suffix'] {
      position: absolute;
      z-index: 2;
    }

    :focus-within {
      outline: none;
      box-shadow: none;
    }

    > div {
      padding: 0;
    }

    input {
      opacity: 0 !important;
    }

    border-radius: 0 2px 2px 0;
    border-left: none;
  `,buttonGroup:l.css`
    border-radius: 2px 0 0 2px;
  `,customMenu:l.css`
    width: 230px;

    [class$='grafana-select-option-grafana-select-option-focused'] {
      background: transparent;

      ::before {
        display: none;
      }
    }
  `,heading:(0,l.css)({padding:e.spacing(1,1,.75,.75),borderLeft:"2px solid transparent",borderBottom:`1px solid ${e.colors.border.weak}`})})},5533:(e,t,n)=>{"use strict";n.d(t,{g:()=>s,n:()=>r});var a=n(1454);function r({metric:e,groupByKey:t,extraFilters:n}){let r=`${a.ui}`;"errors"===e&&(r+=" && status=error"),n&&(r+=` && ${n}`),t&&t!==a.y2&&(r+=` && ${t} != nil`);let s="rate()";switch(e){case"errors":s="rate()";break;case"duration":s="quantile_over_time(duration, 0.9)"}let i=[];return t&&t!==a.y2&&i.push(t),`{${r}} | ${s} ${i.length?`by(${i.join(", ")})`:""}`}function s(e,t){return{refId:"A",query:r({metric:e,groupByKey:t}),queryType:"traceql",tableType:"spans",limit:100,spss:10,filters:[]}}},6002:(e,t,n)=>{"use strict";n.d(t,{v:()=>u});var a,r,s,i=n(8010),o=n(5959),l=n.n(o),c=n(8130);class u extends i.Bs{}s=({model:e})=>{const{message:t,remedyMessage:n,imgWidth:a,padding:r}=e.useState();return l().createElement(c.p,{message:t,remedyMessage:n,imgWidth:a,padding:r})},(r="Component")in(a=u)?Object.defineProperty(a,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[r]=s},7313:(e,t,n)=>{"use strict";n.d(t,{$:()=>l});var a=n(8010),r=n(9958);function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){s(e,t,n[t])})}return e}function o(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class l extends a.dt{_onActivateStep(){const e=(0,r.oM)(this,this.state.maxDataPoints);this.setState({queries:this.state.queries.map(t=>o(i({},t),{step:e}))}),a.jh.getTimeRange(this).subscribeToState((e,t)=>{if(e.value.from!==t.value.from||e.value.to!==t.value.to){const e=(0,r.oM)(this,this.state.maxDataPoints);this.setState({queries:this.state.queries.map(t=>o(i({},t),{step:e}))})}})}constructor(e){super(e),this.addActivationHandler(this._onActivateStep.bind(this))}}},8130:(e,t,n)=>{"use strict";n.d(t,{p:()=>p});var a=n(5959),r=n.n(a),s=n(2007),i=n(6089),o=n(1792),l=n(3241);const c=({width:e="auto",height:t,show404:n=!1})=>{const i=(0,s.useTheme2)(),{x:c,y:d}=((e=50)=>{const[t,n]=(0,a.useState)({x:null,y:null});return(0,a.useEffect)(()=>{const t=(0,l.throttle)(e=>{n({x:e.clientX,y:e.clientY})},e);return window.addEventListener("mousemove",t),()=>{window.removeEventListener("mousemove",t)}},[e]),t})(),m=(0,s.useStyles2)(u,c,d,n);return r().createElement(o.A,{src:i.isDark?"public/plugins/grafana-exploretraces-app/img/944c737f589d02ecf603.svg":"public/plugins/grafana-exploretraces-app/img/e79edcfbe2068fae2364.svg",className:m.svg,height:t,width:e})};c.displayName="GrotNotFound";const u=(e,t,n,a)=>{const{innerWidth:r,innerHeight:s}=window,o=n&&n/s,l=t&&t/r,c=null!==o?d(o,-20,5):0,u=null!==l?d(l,-5,5):0;return{svg:(0,i.css)({"#grot-404-arm, #grot-404-magnifier":{transform:`rotate(${c}deg) translateX(${u}%)`,transformOrigin:"center",transition:"transform 50ms linear"},"#grot-404-text":{display:a?"block":"none"}})}},d=(e,t,n)=>e*(n-t)+t;var m=n(8709);const p=({message:e,remedyMessage:t,imgWidth:n,padding:a})=>{const i=(0,s.useStyles2)(f,a);return r().createElement("div",{className:i.container,"data-testid":m.b.emptyState},r().createElement(s.Stack,{direction:"column",alignItems:"center",gap:3},r().createElement(c,{width:null!=n?n:300}),"string"==typeof e&&r().createElement(s.Text,{textAlignment:"center",variant:"h5"},e),"string"!=typeof e&&e,t&&r().createElement("div",{className:i.remedy},r().createElement(s.Stack,{gap:.5,alignItems:"center"},r().createElement(s.Icon,{name:"info-circle"}),r().createElement(s.Text,{textAlignment:"center",variant:"body"},t)))))};function f(e,t){return{container:(0,i.css)({width:"100%",display:"flex",justifyContent:"space-evenly",flexDirection:"column",padding:t||0}),remedy:(0,i.css)({marginBottom:e.spacing(4)})}}p.displayName="EmptyState"},8404:(e,t,n)=>{"use strict";n.d(t,{z:()=>s});var a=n(8010),r=n(2007);const s=e=>{const t="errors"===e||!1;return a.d0.timeseries().setOption("legend",{showLegend:!1}).setCustomFieldConfig("drawStyle",r.DrawStyle.Bars).setCustomFieldConfig("stacking",{mode:r.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",75).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("axisLabel","Rate").setOverrides(e=>{e.matchFieldsWithNameByRegex(".*").overrideColor({mode:"fixed",fixedColor:t?"semi-dark-red":"green"})}).setOption("tooltip",{mode:r.TooltipDisplayMode.Multi})}},8693:(e,t,n)=>{"use strict";n.d(t,{Mu:()=>N,jD:()=>T,FC:()=>D});var a=n(5959),r=n.n(a),s=n(8010),i=n(7781),o=n(1454),l=n(6002),c=n(1440),u=n(9151),d=n(8404),m=n(5533),p=n(7313),f=n(6089),v=n(2007),g=n(335),h=n(9262),b=n(410),y=n(4128),w=n(3731),S=n(3241),O=n(675);function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class E extends s.Bs{constructor({selection:e}){super({selection:e}),x(this,"startInvestigation",()=>{const e=(0,h.YX)(this);e.setState({selection:this.state.selection}),(0,h.H)(e.state.actionView)||e.setActionView("comparison"),(0,O.EE)(O.NO.analyse_traces,O.ir.analyse_traces.start_investigation,{selection:this.state.selection,metric:(0,h.GK)(this)})})}}function j(e){return{wrapper:(0,f.css)({display:"flex",gap:"16px",alignItems:"center"}),placeholder:(0,f.css)({color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,display:"flex",gap:e.spacing.x0_5})}}x(E,"Component",({model:e})=>{const{selection:t}=(0,h.YX)(e).useState(),n=(0,v.useStyles2)(j),a="auto"===(null==t?void 0:t.type),s=a?"Slowest traces are selected, navigate to the Comparison or Slow Traces tab for more details.":void 0;return r().createElement("div",{className:n.wrapper},r().createElement(v.Button,{variant:"secondary",size:"sm",fill:"solid",disabled:a,icon:"bolt",onClick:e.startInvestigation,tooltip:s},a?"Slowest traces selected":"Select slowest traces"))});var k=n(9546);function C(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){C(e,t,n[t])})}return e}function _(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class N extends s.Bs{isDuration(){return"duration"===(0,h.H_)(this).state.value}_onActivate(){const e=(0,h.H_)(this).state.value;this.setState({$data:new s.Es({$data:new p.$({maxDataPoints:this.isDuration()?24:64,datasource:o.Vl,queries:[this.isDuration()?(0,w.z)():(0,m.g)(e)]}),transformations:this.isDuration()?[...(0,k.h)()]:[...(0,k.G)((0,h.w$)(this))]}),panel:this.getVizPanel()})}getVizPanel(){const e=(0,h.H_)(this).state.value;var t;return this.isDuration()?(0,b.QA)(this,null!==(t=this.state.yBuckets)&&void 0!==t?t:[]):this.getRateOrErrorVizPanel(e)}getRateOrErrorVizPanel(e){const t=(0,d.z)(e).setHoverHeader(!0).setDisplayMode("transparent");return"rate"===e?t.setCustomFieldConfig("axisLabel","span/s"):"errors"===e&&t.setCustomFieldConfig("axisLabel","error/s").setColor({fixedColor:"semi-dark-red",mode:"fixed"}),new s.G1({direction:"row",children:[new s.vA({body:t.build()})]})}buildSelectionAnnotation(e){var t,n,a,r;if(!(0,h.H)(e.actionView))return;const s=null===(n=e.selection)||void 0===n||null===(t=n.raw)||void 0===t?void 0:t.x,o=null===(r=e.selection)||void 0===r||null===(a=r.raw)||void 0===a?void 0:a.y,l=(0,i.arrayToDataFrame)([{time:(null==s?void 0:s.from)||0,xMin:(null==s?void 0:s.from)||0,xMax:(null==s?void 0:s.to)||0,timeEnd:(null==s?void 0:s.to)||0,yMin:null==o?void 0:o.from,yMax:null==o?void 0:o.to,isRegion:!0,fillOpacity:.15,lineWidth:1,lineStyle:"solid",color:y._E,text:"Comparison selection"}]);return l.name="xymark",[l]}constructor(e){super(P({yBuckets:[],actions:[],isStreaming:!1},e)),this.addActivationHandler(()=>{this._onActivate();const e=s.jh.getData(this),t=(0,h.YX)(this),n=s.jh.getTimeRange(this);this._subs.add(e.subscribeToState(a=>{var r,d,m;if(this.setState({isStreaming:(null===(r=a.data)||void 0===r?void 0:r.state)===i.LoadingState.Streaming}),(null===(d=a.data)||void 0===d?void 0:d.state)===i.LoadingState.Done)if(0===a.data.series.length||0===a.data.series[0].length||(0,h.W6)(a))this.setState({panel:new s.G1({children:[new s.vA({body:new l.v({message:o.PL,imgWidth:150})})]})});else{let r=[];if(this.isDuration()){var p,f;if(r=D((null===(p=e.state.data)||void 0===p?void 0:p.series)||[]),t.state.selection&&(null===(f=a.data)||void 0===f?void 0:f.state)===i.LoadingState.Done){var v,g;const n=this.buildSelectionAnnotation(t.state);n&&!(null===(g=e.state.data)||void 0===g||null===(v=g.annotations)||void 0===v?void 0:v.length)&&e.setState({data:_(P({},e.state.data),{annotations:n})})}if(null==r?void 0:r.length){var y;const{minDuration:e,minBucket:a}=T(r),s={type:"auto"};(0,h.Kf)(this).changeValueTo(e),(0,h.F3)(this).changeValueTo((0,b.xx)(a-1,r,.3)),s.duration={from:e,to:""},s.raw={x:{from:1e3*n.state.value.from.unix(),to:1e3*n.state.value.to.unix()},y:{from:a-.5,to:r.length-.5}},this.setState({actions:[new E({selection:s})]}),(null===(y=t.state.selection)||void 0===y?void 0:y.duration)&&"auto"!==t.state.selection.type||t.setState({selection:s})}}this.setState({yBuckets:r,panel:this.getVizPanel()})}else(null===(m=a.data)||void 0===m?void 0:m.state)===i.LoadingState.Loading&&this.setState({panel:new s.G1({direction:"column",children:[new c.G({component:()=>(0,u.NO)(1)})]})})})),this._subs.add(t.subscribeToState((t,n)=>{var a;if((null===(a=e.state.data)||void 0===a?void 0:a.state)===i.LoadingState.Done&&(!(0,S.isEqual)(t.selection,n.selection)||t.actionView!==n.actionView)&&this.isDuration()){const n=this.buildSelectionAnnotation(t);e.setState({data:_(P({},e.state.data),{annotations:n})})}}))})}}C(N,"Component",({model:e})=>{const{panel:t,actions:n,isStreaming:a}=e.useState(),{value:s}=(0,h.H_)(e).useState(),i=(0,v.useStyles2)(I);if(!t)return;const o="duration"===s?"Click and drag to compare selection with baseline.":"";return r().createElement("div",{className:i.container},r().createElement("div",{className:i.headerContainer},r().createElement("div",{className:i.titleContainer},r().createElement("div",{className:i.titleRadioWrapper},r().createElement(v.RadioButtonList,{name:`metric-${s}`,options:[{title:"",value:"selected"}],value:"selected"}),r().createElement("span",null,(()=>{switch(s){case"errors":return"Errors rate";case"rate":return"Span rate";case"duration":return"Histogram by duration";default:return""}})())),o&&r().createElement("div",{className:i.subtitle},o)),r().createElement("div",{className:i.actions},a&&r().createElement(g.M,{isStreaming:!0,iconSize:10}),null==n?void 0:n.map(e=>r().createElement(e.Component,{model:e,key:e.state.key})))),r().createElement(t.Component,{model:t}))});const D=e=>e.map(e=>parseFloat(e.fields[1].name)).sort((e,t)=>e-t),T=e=>{const t=Math.floor(e.length/4);let n=e.length-t-1;return n<0&&(n=0),{minDuration:(0,b.xx)(n-1,e),minBucket:n}};function I(e){return{container:(0,f.css)({width:"100%",display:"flex",flexDirection:"column",border:`1px solid ${e.colors.border.weak}`,borderRadius:"2px",background:e.colors.background.primary,".show-on-hover":{display:"none"},"section, section:hover":{borderColor:"transparent"},"& .u-select":{border:"1px solid #ffffff75"}}),headerContainer:(0,f.css)({width:"100%",display:"flex",flexDirection:"row",padding:"8px",gap:"8px",justifyContent:"space-between",alignItems:"flex-start",fontWeight:e.typography.fontWeightBold}),titleContainer:(0,f.css)({display:"flex",flexDirection:"column",gap:"4px"}),titleRadioWrapper:(0,f.css)({display:"flex",alignItems:"center"}),actions:(0,f.css)({display:"flex",gap:"8px",alignItems:"center"}),subtitle:(0,f.css)({display:"flex",color:e.colors.text.secondary,fontSize:"12px",fontWeight:400,"& svg":{margin:"0 2px"}})}}},8709:(e,t,n)=>{"use strict";n.d(t,{b:()=>a});const a={emptyState:"data-testid empty-state",errorState:"data-testid error-state",loadingState:"data-testid loading-state"}},9151:(e,t,n)=>{"use strict";n.d(t,{hE:()=>_,NO:()=>D});var a,r,s,i=n(5959),o=n.n(i),l=n(7781),c=n(8010),u=n(6002),d=n(6089),m=n(2007),p=n(3049),f=n(1440),v=n(8709);class g extends c.Bs{}s=({model:e})=>{const{message:t}=e.useState();return o().createElement(m.Alert,{title:"Query error",severity:"error","data-testid":v.b.errorState},t)},(r="Component")in(a=g)?Object.defineProperty(a,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[r]=s;var h=n(3241);const b=e=>{const t=(0,m.useStyles2)(y),{searchQuery:n,onSearchQueryChange:a}=e;return o().createElement(m.Field,{className:t.searchField},o().createElement(m.Input,{placeholder:"Search",prefix:o().createElement(m.Icon,{name:"search"}),value:n,onChange:a,id:"searchFieldInput"}))};function y(e){return{searchField:(0,d.css)({marginBottom:e.spacing(1)})}}var w=n(9262),S=n(1454);function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){O(e,t,n[t])})}return e}function E(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function j(e){return E(x({},e),{fields:e.fields.map(e=>E(x({},e),{values:e.values}))})}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function C(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){k(e,t,n[t])})}return e}function P(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class _ extends c.Bs{renderFilteredData(e){e.series&&e.series.length>0?this.performRepeat(e):this.state.body.setState({children:[new c.vA({body:new u.v({message:"No data for search term",padding:"32px"})})]})}groupSeriesBy(e,t){const n=e.series.reduce((e,n)=>{var a,r;const s=null===(r=n.fields.find(e=>e.type===l.FieldType.number))||void 0===r||null===(a=r.labels)||void 0===a?void 0:a[t];return s?(e[s]||(e[s]=[]),e[s].push(n),e):e},{}),a=[];for(const e in n){const t=n[e].sort((e,t)=>{var n;return(null===(n=e.name)||void 0===n?void 0:n.localeCompare(t.name))||0}),r=j(t[0]);t.slice(1,t.length).forEach(e=>r.fields.push(e.fields[1])),a.push((0,l.sortDataFrame)(r,0))}return a}performRepeat(e){const t=[];let n=e.series;this.state.groupBy&&(n=this.groupSeriesBy(e,(0,w.pl)(this).getValueText()));for(let a=0;a<n.length;a++){if(0===n[a].fields.filter(e=>e.type===l.FieldType.number).reduce((e,t)=>e+t.values.reduce((e,t)=>e+(t||0),0)||0,0))continue;const r=this.state.getLayoutChild(e,n[a],a);t.push(r)}this.state.body.setState({children:t})}constructor(e){super(e),k(this,"onSearchQueryChange",e=>{this.setState({searchQuery:e.currentTarget.value})}),k(this,"onSearchQueryChangeDebounced",(0,h.debounce)(e=>{var t;const n=c.jh.getData(this),a=P(C({},n.state.data),{series:null===(t=n.state.data)||void 0===t?void 0:t.series.filter(I(e))});this.renderFilteredData(a)},250)),this.addActivationHandler(()=>{const e=c.jh.getData(this);this._subs.add(e.subscribeToState(e=>{var t,n,a,r,s;if((null===(t=e.data)||void 0===t?void 0:t.state)===l.LoadingState.Done||(null===(n=e.data)||void 0===n?void 0:n.state)===l.LoadingState.Streaming){if(0===e.data.series.length&&(null===(r=e.data)||void 0===r?void 0:r.state)!==l.LoadingState.Streaming)this.state.body.setState({children:[new c.vA({body:new u.v({message:S.PL,remedyMessage:S.a5,padding:"32px"})})]});else if((null===(s=e.data)||void 0===s?void 0:s.state)===l.LoadingState.Done){var i;const t=P(C({},e.data),{series:null===(i=e.data)||void 0===i?void 0:i.series.filter(I(this.state.searchQuery))});this.renderFilteredData(t),this.publishEvent(new S.sv({series:e.data.series}),!0)}}else if((null===(a=e.data)||void 0===a?void 0:a.state)===l.LoadingState.Error){var o,d,m;this.state.body.setState({children:[new c.gF({children:[new g({message:null!==(m=null===(d=e.data.errors)||void 0===d||null===(o=d[0])||void 0===o?void 0:o.message)&&void 0!==m?m:"An error occurred in the query"})]})]})}else this.state.body.setState({children:[new c.gF({children:[new f.G({component:()=>D(8)})]})]})})),this.subscribeToState((e,t)=>{var n;e.searchQuery!==t.searchQuery&&this.onSearchQueryChangeDebounced(null!==(n=e.searchQuery)&&void 0!==n?n:"")}),e.state.data&&this.performRepeat(e.state.data)})}}function N(){return{container:(0,d.css)({display:"flex",flexDirection:"column",flexGrow:1})}}k(_,"Component",({model:e})=>{const{body:t,searchQuery:n}=e.useState(),a=(0,m.useStyles2)(N);return o().createElement("div",{className:a.container},o().createElement(b,{searchQuery:null!=n?n:"",onSearchQueryChange:e.onSearchQueryChange}),o().createElement(t.Component,{model:t}))});const D=e=>{const t=(0,m.useStyles2)(T);return o().createElement("div",{className:t.container},[...Array(e)].map((e,n)=>o().createElement("div",{className:t.itemContainer,key:n},o().createElement("div",{className:t.header},o().createElement("div",{className:t.title},o().createElement(p.A,{count:1})),o().createElement("div",{className:t.action},o().createElement(p.A,{count:1}))),o().createElement("div",{className:t.yAxis},[...Array(2)].map((e,n)=>o().createElement("div",{className:t.yAxisItem,key:n},o().createElement(p.A,{count:1})))),o().createElement("div",{className:t.xAxis},[...Array(2)].map((e,n)=>o().createElement("div",{className:t.xAxisItem,key:n},o().createElement(p.A,{count:1})))))))};function T(e){return{container:(0,d.css)({display:"grid",gridTemplateColumns:S.MV,gridAutoRows:"200px",rowGap:e.spacing(1),columnGap:e.spacing(1)}),itemContainer:(0,d.css)({backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.background.secondary}`,padding:"5px"}),header:(0,d.css)({display:"flex",justifyContent:"space-between"}),title:(0,d.css)({width:"100px"}),action:(0,d.css)({width:"60px"}),yAxis:(0,d.css)({display:"flex",flexDirection:"column",justifyContent:"space-around",marginTop:"35px"}),yAxisItem:(0,d.css)({width:"60px",height:"55px"}),xAxis:(0,d.css)({display:"flex",justifyContent:"space-evenly"}),xAxisItem:(0,d.css)({width:"55px"})}}const I=e=>t=>{const n=null==e?void 0:e.trim();if(!n)return!0;const a=new RegExp(n,"i");return t.fields.some(e=>!!e.labels&&Object.values(e.labels).find(e=>a.test(e)))}},9240:(e,t,n)=>{"use strict";n.d(t,{$L:()=>s,Xn:()=>r,iH:()=>a});const a="span.db.system.name",r=[{label:"Root spans",value:"nestedSetParent<0",filter:{key:"nestedSetParent",operator:"<",value:"0"},description:"Focus your analysis on the root span of each trace"},{label:"All spans",value:"true",filter:{key:"",operator:"",value:!0},description:"View and analyse raw span data. This option may result in long query times."},{label:"Server spans",value:"kind=server",filter:{key:"kind",operator:"=",value:"server"},description:"Explore server-specific segments of traces"},{label:"Consumer spans",value:"kind=consumer",filter:{key:"kind",operator:"=",value:"consumer"},description:"Analyze interactions initiated by consumer services"},{label:"Database calls",value:`${a}!=""`,filter:{key:a,operator:"!=",value:'""'},description:"Evaluate the performance issues in database interactions"}],s=e=>r.find(t=>t.value===e)},9262:(e,t,n)=>{"use strict";n.d(t,{BB:()=>Ge,W6:()=>Re,xo:()=>qe,_g:()=>Pe,Is:()=>Fe,U4:()=>Ce,_b:()=>Be,u1:()=>Ee,dB:()=>Oe,gG:()=>$e,pl:()=>Ne,Ey:()=>Ve,oT:()=>we,ee:()=>_e,F3:()=>Ie,Kf:()=>Te,GK:()=>Me,H_:()=>Ae,Fp:()=>je,w$:()=>We,h7:()=>Le,gi:()=>De,YX:()=>Se,zY:()=>ye,__:()=>ke,Et:()=>He,em:()=>xe,H:()=>ze});var a=n(7781),r=n(8010),s=n(9351),i=n(1454),o=n(3292),l=n(6089),c=n(5959),u=n.n(c),d=n(2468),m=n(2007),p=n(1440),f=n(8531),v=n(675);const g=e=>{const{index:t,type:n,label:a,labelTitle:r,value:s,valueTitle:i,url:o}=e,l=(0,m.useStyles2)(h);return u().createElement("div",{key:t},0===t&&u().createElement("div",{className:l.rowHeader},u().createElement("span",null,r),u().createElement("span",{className:l.valueTitle},i)),u().createElement("div",{className:l.row,key:t,onClick:()=>{(0,v.EE)(v.NO.home,v.ir.home.panel_row_clicked,{type:n,index:t,value:s}),f.locationService.push(o)}},u().createElement("div",{className:"rowLabel"},a),u().createElement("div",{className:l.action},u().createElement("span",{className:l.actionText},s),u().createElement(m.Icon,{className:l.actionIcon,name:"arrow-right",size:"xl"}))))};function h(e){return{rowHeader:(0,l.css)({color:e.colors.text.secondary,display:"flex",justifyContent:"space-between",alignItems:"center",padding:`0 ${e.spacing(2)} ${e.spacing(1)} ${e.spacing(2)}`}),valueTitle:(0,l.css)({margin:"0 45px 0 0"}),row:(0,l.css)({display:"flex",justifyContent:"space-between",alignItems:"center",gap:e.spacing(2),padding:`${e.spacing(.75)} ${e.spacing(2)}`,"&:hover":{backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,cursor:"pointer",".rowLabel":{textDecoration:"underline"}}}),action:(0,l.css)({display:"flex",alignItems:"center"}),actionText:(0,l.css)({color:"#d5983c",padding:`0 ${e.spacing(1)}`,width:"max-content"}),actionIcon:(0,l.css)({cursor:"pointer",margin:`0 ${e.spacing(.5)} 0 ${e.spacing(1)}`})}}const b=e=>{var t;const{series:n,type:r}=e,s=(0,m.useStyles2)(y),o=e=>{var t;const n=e.fields.find(e=>"time"!==e.name);var a;return null!==(a=null==n||null===(t=n.labels)||void 0===t?void 0:t["resource.service.name"].replace(/"/g,""))&&void 0!==a?a:"Service name not found"},l=e=>{const t={"var-filters":`resource.service.name|=|${o(e)}`,"var-metric":"errors"};return a.urlUtil.renderUrl(i.D5,t)},c=e=>{var t;const n=e.fields.find(e=>"time"!==e.name);var a;return null!==(a=null==n||null===(t=n.values)||void 0===t?void 0:t.reduce((e,t)=>"number"!=typeof e||isNaN(e)?t:e+t,0))&&void 0!==a?a:1};return u().createElement("div",{className:s.container},null===(t=n.sort((e,t)=>c(t)-c(e)).slice(0,10))||void 0===t?void 0:t.map((e,t)=>u().createElement("span",{key:t},u().createElement(g,{type:r,index:t,label:o(e),labelTitle:"Service",value:c(e),valueTitle:"Total errors",url:l(e)}))))};function y(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}var w=n(9958);function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const O=e=>{const{series:t,type:n}=e,r=(0,m.useStyles2)(x),s=t[0].fields.find(e=>"duration"===e.name);if(s&&s.values){var o,l;const e=null==s||null===(o=s.values.map((e,t)=>t))||void 0===o?void 0:o.sort((e,t)=>(null==s?void 0:s.values[t])-(null==s?void 0:s.values[e])),c=t[0].fields.map(t=>{return n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){S(e,t,n[t])})}return e}({},t),a=null!=(a={values:null==e?void 0:e.map(e=>t.values[e])})?a:{},Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(a)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(a)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(a,e))}),n;var n,a}),d=(e,t,n)=>{let a="";return(null==e?void 0:e.values[n])&&(a=e.values[n]),(null==t?void 0:t.values[n])&&(a=0===a.length?t.values[n]:`${a}: ${t.values[n]}`),0===a.length?"Trace service & name not found":a},m=(e,t,n,r)=>{if(!(t&&t.values[r]&&n&&n.values[r]))return console.error("SpanId or traceService not found"),i.bw.Explore;const s={traceId:e,spanId:t.values[r],"var-filters":`resource.service.name|=|${n.values[r]}`,"var-metric":"duration"};return a.urlUtil.renderUrl(i.D5,s)},p=(e,t)=>e&&e.values?(0,w.a3)(e.values[t]/1e3):"Duration not found",f=c.find(e=>"traceIdHidden"===e.name),v=c.find(e=>"spanID"===e.name),h=c.find(e=>"traceName"===e.name),b=c.find(e=>"traceService"===e.name),y=c.find(e=>"duration"===e.name);return u().createElement("div",{className:r.container},null==f||null===(l=f.values)||void 0===l?void 0:l.map((e,t)=>u().createElement("span",{key:t},u().createElement(g,{type:n,index:t,label:d(b,h,t),labelTitle:"Trace",value:p(y,t),valueTitle:"Duration",url:m(e,v,b,t)}))))}return null};function x(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}const E=e=>{var t;const{series:n,type:r}=e,s=(0,m.useStyles2)(j),o=e=>{var t;const n=e.fields.find(e=>"time"!==e.name);var a;return null!==(a=null==n||null===(t=n.labels)||void 0===t?void 0:t["resource.service.name"].replace(/"/g,""))&&void 0!==a?a:"Service name not found"},l=e=>{const t={"var-filters":`resource.service.name|=|${o(e)}`,"var-metric":"duration"};return a.urlUtil.renderUrl(i.D5,t)},c=e=>{var t;const n=e.fields.find(e=>"time"!==e.name);var a;return null!==(a=null==n||null===(t=n.values)||void 0===t?void 0:t.reduce((e,t)=>"number"!=typeof e||isNaN(e)?t:e+t,0))&&void 0!==a?a:1};return u().createElement("div",{className:s.container},null===(t=n.sort((e,t)=>c(t)-c(e)).slice(0,10))||void 0===t?void 0:t.map((e,t)=>u().createElement("span",{key:t},u().createElement(g,{type:r,index:t,label:o(e),labelTitle:"Service",value:(0,w.a3)(1e6*c(e)),valueTitle:"p90",url:l(e)}))))};function j(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}const k=e=>{const{series:t,type:n,message:a}=e,r=(0,m.useStyles2)(C);if(a)return u().createElement("div",{className:r.container},u().createElement("div",{className:r.message},u().createElement(m.Icon,{className:r.icon,name:"exclamation-circle",size:"xl"}),a));if(t&&t.length>0)switch(n){case"slowest-traces":return u().createElement(O,{series:t,type:n});case"errored-services":return u().createElement(b,{series:t,type:n});case"slowest-services":return u().createElement(E,{series:t,type:n})}return u().createElement("div",{className:r.container},"No series data")};function C(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`}),icon:(0,l.css)({margin:`0 ${e.spacing(.5)} 0 ${e.spacing(1)}`}),message:(0,l.css)({display:"flex",gap:e.spacing(1.5),margin:`${e.spacing(2)} auto`,width:"60%"})}}class P extends r.Bs{}var _,N,D;function T(e){switch(e){case"slowest-services":return"clock-nine";case"slowest-traces":return"crosshair";default:return"exclamation-triangle"}}function I(e){return{container:(0,l.css)({border:`1px solid ${e.isDark?e.colors.border.medium:e.colors.border.weak}`,borderRadius:e.spacing(.5),marginBottom:e.spacing(4),width:"100%"}),title:(0,l.css)({color:e.isDark?e.colors.text.secondary:e.colors.text.primary,backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,borderTopLeftRadius:e.spacing(.5),borderTopRightRadius:e.spacing(.5),display:"flex",justifyContent:"center",alignItems:"center",fontSize:"1.3rem",padding:`${e.spacing(1.5)} ${e.spacing(2)}`}),titleText:(0,l.css)({marginLeft:e.spacing(1)})}}D=({model:e})=>{const{series:t,title:n,type:a,message:r}=e.useState(),s=(0,m.useStyles2)(I);return u().createElement("div",{className:s.container},u().createElement("div",{className:s.title},u().createElement(m.Icon,{name:T(a),size:"lg"}),u().createElement("span",{className:s.titleText},n)),u().createElement(k,{series:t,type:a,message:r}))},(N="Component")in(_=P)?Object.defineProperty(_,N,{value:D,enumerable:!0,configurable:!0,writable:!0}):_[N]=D;var A=n(3049),$=n(8693);function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function V(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){L(e,t,n[t])})}return e}class B extends r.Bs{constructor(e){var t,n;super(V({$data:new r.dt({datasource:i.Vl,queries:[(t=V({refId:"A",queryType:"traceql",tableType:"spans",limit:10},e.query),n={exemplars:0},n=null!=n?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}),t)]})},e)),this.addActivationHandler(()=>{const t=r.jh.getData(this);this._subs.add(t.subscribeToState(t=>{var n,s,i,l;if((null===(n=t.data)||void 0===n?void 0:n.state)===a.LoadingState.Done||(null===(s=t.data)||void 0===s?void 0:s.state)===a.LoadingState.Streaming)if((null===(l=t.data)||void 0===l?void 0:l.state)!==a.LoadingState.Done||0!==t.data.series.length&&0!==t.data.series[0].length){if(t.data.series.length>0){var c;if("slowest-traces"!==e.type||e.renderDurationPanel)this.setState({panel:new r.G1({children:[new P({series:t.data.series,title:e.title,type:e.type})]})});else if((null===(c=t.data)||void 0===c?void 0:c.state)===a.LoadingState.Done){var u,d;let n=(0,$.FC)(null!==(d=null===(u=t.data)||void 0===u?void 0:u.series)&&void 0!==d?d:[]);if(null==n?void 0:n.length){const{minDuration:t}=(0,$.jD)(n);var m;this.setState({panel:new r.G1({children:[new B({query:{query:`{nestedSetParent<0 && duration > ${t} ${null!==(m=e.filter)&&void 0!==m?m:""}}`},title:e.title,type:e.type,renderDurationPanel:!0})]})})}}}}else this.setState({panel:new r.G1({children:[new P({message:je(e.title.toLowerCase()),title:e.title,type:e.type})]})});else(null===(i=t.data)||void 0===i?void 0:i.state)===a.LoadingState.Error?this.setState({panel:new r.G1({children:[new P({message:Ee(t),title:e.title,type:e.type})]})}):this.setState({panel:new r.G1({direction:"column",maxHeight:o.VV,height:o.VV,children:[new p.G({component:()=>z()})]})})}))})}}function F(){return{container:(0,l.css)({minWidth:"350px",width:"-webkit-fill-available"})}}L(B,"Component",({model:e})=>{const{panel:t}=e.useState(),n=(0,m.useStyles2)(F);if(t)return u().createElement("div",{className:n.container},u().createElement(t.Component,{model:t}))});const z=()=>{const e=(0,m.useStyles2)(M);return u().createElement("div",{className:e.container},u().createElement("div",{className:e.title},u().createElement(A.A,{count:1,width:200})),u().createElement("div",{className:e.tracesContainer},[...Array(11)].map((t,n)=>u().createElement("div",{className:e.row,key:n},u().createElement("div",{className:e.rowLeft},u().createElement(A.A,{count:1})),u().createElement("div",{className:e.rowRight},u().createElement(A.A,{count:1}))))))};function M(e){return{container:(0,l.css)({border:`1px solid ${e.isDark?e.colors.border.medium:e.colors.border.weak}`,borderRadius:e.spacing(.5),marginBottom:e.spacing(4),width:"100%"}),title:(0,l.css)({color:e.colors.text.secondary,backgroundColor:e.colors.background.secondary,fontSize:"1.3rem",padding:`${e.spacing(1.5)} ${e.spacing(2)}`,textAlign:"center"}),tracesContainer:(0,l.css)({padding:`13px ${e.spacing(2)}`}),row:(0,l.css)({display:"flex",justifyContent:"space-between"}),rowLeft:(0,l.css)({margin:"7px 0",width:"150px"}),rowRight:(0,l.css)({width:"50px"})}}const R=()=>u().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"73",height:"72",viewBox:"0 0 73 72",fill:"none"},u().createElement("path",{d:"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z",fill:"#24292E",fillOpacity:"0.75"})),H=()=>u().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"73",height:"72",viewBox:"0 0 73 72",fill:"none"},u().createElement("path",{d:"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z",fill:"#CCCCDC",fillOpacity:"0.65"}));var q=n(1159);function G(e,t,n,a,r,s,i){try{var o=e[s](i),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function W(e){return function(){var t=this,n=arguments;return new Promise(function(a,r){var s=e.apply(t,n);function i(e){G(s,a,r,i,o,"next",e)}function o(e){G(s,a,r,i,o,"throw",e)}i(void 0)})}}function U(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Y=e=>(e.delete(i.$V),e.delete(`var-${i.pf}`),e.delete(`var-${i.xc}`),e),K=()=>{const e=(0,f.usePluginUserStorage)();return{getBookmarks:()=>Z(e),removeBookmark:t=>te(e,t),bookmarkExists:t=>ne(e,t),toggleBookmark:()=>J(e)}},X=e=>{if(!e||!e.params)return i.D5;const t=new URLSearchParams(e.params),n=Object.fromEntries(t.entries()),r=t.getAll(`var-${i.Ao}`),s=a.urlUtil.renderUrl(i.D5,(o=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){U(e,t,n[t])})}return e}({},n),l=null!=(l={[`var-${i.Ao}`]:r})?l:{},Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(l)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(l)).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(l,e))}),o));var o,l;return s},Q=(e,t)=>W(function*(){try{yield e.setItem(i.Sr,JSON.stringify(t))}catch(e){console.error("Failed to save bookmarks to storage:",e)}})(),Z=e=>W(function*(){try{const t=yield e.getItem(i.Sr);return t?JSON.parse(t):[]}catch(e){return console.error("Failed to get bookmarks from storage:",e),[]}})(),J=e=>W(function*(){const t={params:Y(new URLSearchParams(window.location.search)).toString()};return(yield ne(e,t))?(yield te(e,t),!1):(yield ee(e,t),!0)})(),ee=(e,t)=>W(function*(){const n=yield Z(e);n.push(t),yield Q(e,n)})(),te=(e,t)=>W(function*(){const n=(yield Z(e)).filter(e=>!ae(t,e));yield Q(e,n)})(),ne=(e,t)=>W(function*(){return(yield Z(e)).some(e=>ae(t,e))})(),ae=(e,t)=>{const n=Y(new URLSearchParams(e.params)),a=Y(new URLSearchParams(t.params)),r=`var-${i.Ao}`,s=Array.from(n.keys()).filter(e=>e!==r),o=Array.from(a.keys()).filter(e=>e!==r);if(s.length!==o.length)return!1;const l=s.every(e=>a.has(e)&&n.get(e)===a.get(e));if(!l)return!1;const c=n.getAll(r),u=a.getAll(r);return c.length===u.length&&c.every(e=>u.includes(e))};var re=n(9240);const se=({bookmark:e})=>{let{actionView:t,primarySignal:n,metric:a,filters:r}=(e=>{if(!e||!e.params)return{actionView:"",primarySignal:"",filters:"",metric:""};const t=new URLSearchParams(e.params);var n,a,r;return{actionView:null!==(n=t.get(i.V2))&&void 0!==n?n:"",primarySignal:null!==(a=t.get(i.W5))&&void 0!==a?a:"",filters:t.getAll(`var-${i.Ao}`).join(i.x5),metric:null!==(r=t.get(`var-${i.PU}`))&&void 0!==r?r:""}})(e);const s=(0,m.useStyles2)(ie);return r=((e,t)=>{const n=(e=>{const t=(0,re.$L)(e);if(!t||!t.filter)return"";const n=t.filter;return n.key&&n.operator&&void 0!==n.value?`${n.key}|${n.operator}|${n.value}`:""})(t);let a=e.split(i.x5);return a=a.filter(e=>e!==n),a.join(i.x5)})(r,n),r=r.replace(/\|=\|/g," = "),r=r.replace(i.$d,"").replace(i.zd,"").replace(i.X0,""),u().createElement("div",{title:r},u().createElement("div",null,u().createElement("b",null,Ge(a))," of ",u().createElement("b",null,n.replace("_"," "))," (",t,")"),u().createElement("div",{className:s.filters},r))};function ie(){return{filters:(0,l.css)({textOverflow:"ellipsis",overflow:"hidden",WebkitLineClamp:2,display:"-webkit-box",WebkitBoxOrient:"vertical"})}}function oe(e,t,n,a,r,s,i){try{var o=e[s](i),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function le(e){return function(){var t=this,n=arguments;return new Promise(function(a,r){var s=e.apply(t,n);function i(e){oe(s,a,r,i,o,"next",e)}function o(e){oe(s,a,r,i,o,"throw",e)}i(void 0)})}}const ce=()=>{const e=(0,m.useStyles2)(ue),{getBookmarks:t,removeBookmark:n}=K(),[a,r]=(0,c.useState)([]),[s,i]=(0,c.useState)(!0),[o,l]=(0,c.useState)(!1);return(0,c.useEffect)(()=>{le(function*(){i(!0);try{const e=yield t();r(e)}catch(e){console.error("Error loading bookmarks:",e),r([])}finally{i(!1)}})()},[]),s?u().createElement("div",null,u().createElement("div",{className:e.header},u().createElement("h4",null,"Or view bookmarks")),u().createElement("div",{className:e.loading},u().createElement(m.LoadingPlaceholder,{text:"Loading bookmarks..."}))):u().createElement("div",null,u().createElement("div",{className:e.header},u().createElement("h4",null,"Or view bookmarks")),0===a.length?u().createElement("p",{className:e.noBookmarks},"Bookmark your favorite queries to view them here."):u().createElement("div",{className:e.bookmarks},a.map((a,s)=>u().createElement("div",{className:e.bookmark,key:s,onClick:()=>(e=>{(0,v.EE)(v.NO.home,v.ir.home.go_to_bookmark_clicked);const t=X(e);f.locationService.push(t)})(a)},u().createElement("div",{className:e.bookmarkItem},u().createElement(se,{bookmark:a})),u().createElement("div",{className:e.remove},u().createElement(m.Button,{variant:"secondary",fill:"text",icon:"trash-alt",disabled:o,onClick:e=>((e,a)=>le(function*(){a.stopPropagation(),l(!0);try{yield n(e);const a=yield t();r(a)}catch(e){console.error("Error removing bookmark:",e)}finally{l(!1)}})())(a,e)}))))))};function ue(e){return{header:(0,l.css)({textAlign:"center",h4:{margin:0}}),bookmarks:(0,l.css)({display:"flex",flexWrap:"wrap",gap:e.spacing(2),margin:`${e.spacing(4)} 0 ${e.spacing(2)} 0`,justifyContent:"center"}),bookmark:(0,l.css)({display:"flex",flexDirection:"column",justifyContent:"space-between",cursor:"pointer",width:"318px",border:`1px solid ${e.colors.border.medium}`,borderRadius:e.shape.radius.default,"&:hover":{backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary}}),bookmarkItem:(0,l.css)({padding:`${e.spacing(1.5)} ${e.spacing(1.5)} 0 ${e.spacing(1.5)}`,overflow:"hidden"}),filters:(0,l.css)({textOverflow:"ellipsis",overflow:"hidden",WebkitLineClamp:2,display:"-webkit-box",WebkitBoxOrient:"vertical"}),remove:(0,l.css)({display:"flex",justifyContent:"flex-end"}),noBookmarks:(0,l.css)({margin:`${e.spacing(4)} 0 ${e.spacing(2)} 0`,textAlign:"center"}),loading:(0,l.css)({display:"flex",justifyContent:"center",margin:`${e.spacing(4)} 0`})}}class de extends r.Bs{}function me(e){return{container:(0,l.css)({display:"flex",gap:e.spacing(7),flexDirection:"column",margin:`0 0 ${e.spacing(4)} 0`,justifyContent:"center"}),header:(0,l.css)({display:"flex",alignItems:"center",backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,borderRadius:e.spacing(.5),flexWrap:"wrap",justifyContent:"center",padding:e.spacing(3),gap:e.spacing(4)}),headerTitleContainer:(0,l.css)({display:"flex",alignItems:"center"}),title:(0,l.css)({margin:`0 0 0 ${e.spacing(2)}`}),headerActions:(0,l.css)({alignItems:"center",justifyContent:"flex-start",display:"flex",gap:e.spacing(2)}),documentationLink:(0,l.css)({textDecoration:"underline","&:hover":{textDecoration:"underline"}}),subHeader:(0,l.css)({textAlign:"center",h4:{margin:`0 0 -${e.spacing(2)} 0`}}),label:(0,l.css)({fontSize:"12px"}),variablesAndControls:(0,l.css)({alignItems:"center",gap:e.spacing(2),display:"flex",justifyContent:"space-between",width:"100%"}),variables:(0,l.css)({display:"flex",gap:e.spacing(2)}),controls:(0,l.css)({display:"flex",gap:e.spacing(1)})}}function pe(e,t,n,a,r,s,i){try{var o=e[s](i),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function fe(e){return(t=function*(){const t=r.jh.interpolate(e,i.gR),n=yield(0,f.getDataSourceSrv)().get(t);if(!(n instanceof f.DataSourceWithBackend))throw console.error(new Error("getTagKeysProvider: invalid datasource!")),new Error("getTagKeysProvider: invalid datasource!");const a=n;if(a&&a.getTagKeys){const e=yield a.getTagKeys();return Array.isArray(e)?{replace:!0,values:(s=e,[...s.filter(e=>{var t;return null===(t=e.text)||void 0===t?void 0:t.includes(i.$d)}),...s.filter(e=>{var t;return null===(t=e.text)||void 0===t?void 0:t.includes(i.zd)}),...s.filter(e=>{var t,n,a,r;return!((null===(t=e.text)||void 0===t?void 0:t.includes(i.$d))||(null===(n=e.text)||void 0===n?void 0:n.includes(i.zd))||(null===(a=e.text)||void 0===a?void 0:a.includes(i.X0))||(null===(r=e.text)||void 0===r?void 0:r.includes(i.ZV))||-1!==i.uK.concat(i.ZM).indexOf(e.text))})])}:(console.error(new Error("getTagKeysProvider: invalid tagKeys!")),{values:[]})}var s;return console.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{values:[]}},function(){var e=this,n=arguments;return new Promise(function(a,r){var s=t.apply(e,n);function i(e){pe(s,a,r,i,o,"next",e)}function o(e){pe(s,a,r,i,o,"throw",e)}i(void 0)})})();var t}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(de,"Component",({model:e})=>{const t=we(e),n=(0,q.useNavigate)(),{controls:a}=t.useState(),r=(0,m.useStyles2)(me),s=(0,m.useTheme2)(),o=Be(t),l=Ve(t);return u().createElement("div",{className:r.container},u().createElement("div",{className:r.header},u().createElement("div",{className:r.headerTitleContainer},s.isDark?u().createElement(H,null):u().createElement(R,null),u().createElement("h2",{className:r.title},"Start your traces exploration!")),u().createElement("div",null,u().createElement("p",null,"Drilldown and visualize your trace data without writing a query."),u().createElement("div",{className:r.headerActions},u().createElement(m.Button,{variant:"primary",onClick:()=>{(0,v.EE)(v.NO.home,v.ir.home.explore_traces_clicked),n(i.D5)}},"Let’s start",u().createElement(m.Icon,{name:"arrow-right",size:"lg"})),u().createElement(m.LinkButton,{icon:"external-link-alt",fill:"text",size:"md",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces",className:r.documentationLink,onClick:()=>(0,v.EE)(v.NO.home,v.ir.home.read_documentation_clicked)},"Read documentation")))),u().createElement(ce,null),u().createElement("div",{className:r.subHeader},u().createElement("h4",null,"Or quick-start into your tracing data")),u().createElement(m.Stack,{gap:2},u().createElement("div",{className:r.variablesAndControls},u().createElement("div",{className:r.variables},o&&u().createElement(m.Stack,{gap:1,alignItems:"center"},u().createElement("div",{className:r.label},"Data source"),u().createElement(o.Component,{model:o})),l&&u().createElement(m.Stack,{gap:1,alignItems:"center"},u().createElement("div",{className:r.label},"Filter"),u().createElement(l.Component,{model:l}))),u().createElement("div",{className:r.controls},null==a?void 0:a.map(e=>u().createElement(e.Component,{key:e.state.key,model:e}))))))});function ve(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ge extends r.Bs{_onActivate(){const e=r.jh.getTimeRange(this),t=Ve(this);t.setState({getTagKeysProvider:fe}),Be(this).subscribeToState(e=>{e.value&&localStorage.setItem(i.cd,e.value.toString())}),Ve(this).subscribeToState((t,n)=>{if(t.filters!==n.filters){this.buildPanels(e,t.filters),localStorage.setItem(i.$U,JSON.stringify(t.filters));const a=t.filters.filter(e=>!n.filters.find(t=>t.key===e.key));a.length>0&&(0,v.EE)(v.NO.home,v.ir.home.filter_changed,{key:a[0].key})}}),e.subscribeToState((n,a)=>{n.value.from===a.value.from&&n.value.to===a.value.to||this.buildPanels(e,t.state.filters)}),this.buildPanels(e,t.state.filters)}buildPanels(e,t){const n=e.state.value.from.unix(),a=e.state.value.to.unix(),s=`${(0,d.duration)(a-n,"s").asSeconds()}s`,o=function(e){const t=e.filter(e=>e.key&&e.operator&&e.value).map(e=>(e=>{if(!e)return"";let t=e.value;return null==t||""===t?"":(He.test(t)||["kind"].includes(e.key)||"string"!=typeof t||t.startsWith('"')||t.endsWith('"')||(t=`"${t}"`),`${e.key}${e.operator}${t}`)})(e)).join(i.x5);return t.length?`&& ${t}`:""}(t);this.setState({body:new r.gF({children:[new r.gF({autoRows:"min-content",columnGap:2,rowGap:2,children:[new r.xK({body:new B({query:{query:`{nestedSetParent < 0 && status = error ${o}} | count_over_time() by (resource.service.name)`,step:s},title:"Errored services",type:"errored-services"})}),new r.xK({body:new B({query:{query:`{nestedSetParent < 0 ${o}} | quantile_over_time(duration, 0.9) by (resource.service.name)`,step:s},title:"Slow services",type:"slowest-services"})}),new r.xK({body:new B({query:{query:`{nestedSetParent<0 ${o}} | histogram_over_time(duration)`},title:"Slow traces",type:"slowest-traces",filter:o})})]})]})})}constructor(e){var t,n,a,s,o;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){ve(e,t,n[t])})}return e}({$timeRange:null!==(t=e.$timeRange)&&void 0!==t?t:new r.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:(s=e.initialFilters,o=e.initialDS,new r.Pj({variables:[new r.mI({name:i.EY,label:"Data source",value:o,pluginId:"tempo"}),new r.H9({name:i.zM,datasource:i.Vl,layout:"combobox",filters:s,allowCustomValue:!0})]})),controls:null!==(a=e.controls)&&void 0!==a?a:[new r.KE({}),new r.WM({})]},e)),this.addActivationHandler(this._onActivate.bind(this))}}function he(e){return{container:(0,l.css)({margin:`${e.spacing(4)} auto`,width:"75%","@media (max-width: 900px)":{width:"95%"}})}}ve(ge,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,m.useStyles2)(he);return u().createElement("div",{className:n.container},u().createElement(de.Component,{model:e}),t&&u().createElement(t.Component,{model:t}))});var be=n(5292);function ye(e){return r.jh.getAncestor(e,s.N)}function we(e){return r.jh.getAncestor(e,ge)}function Se(e){return r.jh.getAncestor(e,o.jc)}function Oe(e){const t=Se(e);return null==t?void 0:t.state.exceptionsScene}function xe(e,t){return new s.N({initialDS:e,initialFilters:null!=t?t:[],$timeRange:new r.JZ({from:"now-30m",to:"now"})})}function Ee(e){var t,n,a;return null!==(a=null==e||null===(n=e.data)||void 0===n||null===(t=n.error)||void 0===t?void 0:t.message)&&void 0!==a?a:"There are no Tempo data sources"}function je(e){return`No data for selected data source and filter. Select another to see ${e}.`}function ke(e){return t=r.Go.getUrlState(e),a.urlUtil.renderUrl(i.D5,t);var t}function Ce(e){return r.jh.interpolate(e,i.gR)}function Pe(e){return e.map(e=>({label:e,value:e}))}function _e(e,t){var n;const a=null===(n=e.fields.find(e=>"number"===e.type))||void 0===n?void 0:n.labels;if(!a)return"No labels";const r=Object.keys(a).filter(e=>"p"!==e);return 0===r.length?"No labels":a[t||r[0]].replace(/"/g,"")}function Ne(e){const t=r.jh.lookupVariable(i.z,e);if(!(t instanceof r.yP))throw new Error("Group by variable not found");return t}function De(e){const t=r.jh.lookupVariable(i.gP,e);if(!(t instanceof r.yP))throw new Error("Span list columns variable not found");return t}function Te(e){const t=r.jh.lookupVariable(i.pf,e);if(!(t instanceof r.yP))throw new Error("Latency threshold variable not found");return t}function Ie(e){const t=r.jh.lookupVariable(i.xc,e);if(!(t instanceof r.yP))throw new Error("Partial latency threshold variable not found");return t}function Ae(e){const t=r.jh.lookupVariable(i.PU,e);if(!(t instanceof r.yP))throw new Error("Metric variable not found");return t}function $e(e){const t=r.jh.lookupVariable(i.Ao,e);if(!(t instanceof r.H9))throw new Error("Filters variable not found");return t}function Le(e){const t=r.jh.lookupVariable(i.CE,e);if(!(t instanceof be.x))throw new Error("Primary signal variable not found");return t}function Ve(e){const t=r.jh.lookupVariable(i.zM,e);if(!(t instanceof r.H9))throw new Error("Home filter variable not found");return t}function Be(e){const t=r.jh.lookupVariable(i.EY,e);if(!(t instanceof r.mI))throw new Error("Datasource variable not found");return t}function Fe(e){var t;const n=r.jh.getData(e).state.data,a=null==n||null===(t=n.request)||void 0===t?void 0:t.targets[0];return a?a.step:void 0}function ze(e){return"comparison"===e||"traceList"===e}function Me(e){return Ae(e).useState().value}function Re(e){var t,n,a;return null!==(a=null==e||null===(n=e.data)||void 0===n||null===(t=n.series[0].fields)||void 0===t?void 0:t.some(e=>e.values.every(e=>void 0===e)))&&void 0!==a&&a}const He=/^-?\d+\.?\d*$/,qe=e=>He.test(e)||"string"!=typeof e||e.startsWith('"')||e.endsWith('"')?e:`"${e}"`,Ge=e=>{var t;return(null==e||null===(t=e[0])||void 0===t?void 0:t.toUpperCase())+(null==e?void 0:e.slice(1))||""},We=e=>(t,n)=>{e.publishEvent(new i.vR({traceId:t,spanId:n}),!0)}},9351:(e,t,n)=>{"use strict";n.d(t,{N:()=>ne});var a=n(6089),r=n(5959),s=n.n(r),i=n(7781),o=n(8010),l=n(8531),c=n(2007),u=n(1454),d=n(9262),m=n(6002),p=n(1440),f=n(3049);function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends o.Bs{getVizPanel(){const e=o.d0.traces().setHoverHeader(!0);return this.state.spanId&&e.setOption("focusedSpanId",this.state.spanId),e}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){v(e,t,n[t])})}return e}({$data:new o.dt({datasource:u.Vl,queries:[{refId:"A",query:e.traceId,queryType:"traceql"}]})},e)),this.addActivationHandler(()=>{const e=o.jh.getData(this);this._subs.add(e.subscribeToState(e=>{var t,n;(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done?this.setState({panel:this.getVizPanel().build()}):(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Loading&&this.setState({panel:new p.G({component:h})})}))})}}v(g,"Component",({model:e})=>{const{panel:t}=e.useState(),n=(0,c.useStyles2)(b);if(t)return s().createElement("div",{className:n.panelContainer},s().createElement(t.Component,{model:t}))});const h=()=>{const e=(0,c.useStyles2)(b);return s().createElement("div",{className:e.container},s().createElement("div",{className:e.header},s().createElement(f.A,{count:1,width:60}),s().createElement(f.A,{count:1,width:60})),s().createElement(f.A,{count:2,width:"80%"}),s().createElement("div",{className:e.map},s().createElement(f.A,{count:1}),s().createElement(f.A,{count:1,height:70})),s().createElement("div",{className:e.span},s().createElement("span",{className:e.service1},s().createElement(f.A,{count:1})),s().createElement("span",{className:e.bar1},s().createElement(f.A,{count:1}))),s().createElement("div",{className:e.span},s().createElement("span",{className:e.service2},s().createElement(f.A,{count:1})),s().createElement("span",{className:e.bar2},s().createElement(f.A,{count:1}))),s().createElement("div",{className:e.span},s().createElement("span",{className:e.service3},s().createElement(f.A,{count:1})),s().createElement("span",{className:e.bar3},s().createElement(f.A,{count:1}))),s().createElement("div",{className:e.span},s().createElement("span",{className:e.service4},s().createElement(f.A,{count:1})),s().createElement("span",{className:e.bar4},s().createElement(f.A,{count:1}))),s().createElement("div",{className:e.span},s().createElement("span",{className:e.service5},s().createElement(f.A,{count:1})),s().createElement("span",{className:e.bar5},s().createElement(f.A,{count:1}))),s().createElement("div",{className:e.span},s().createElement("span",{className:e.service6},s().createElement(f.A,{count:1})),s().createElement("span",{className:e.bar6},s().createElement(f.A,{count:1}))))};function b(e){return{panelContainer:(0,a.css)({display:"flex",height:"100%",'& [data-testid="data-testid panel content"] > div':{overflow:"auto"},"& .show-on-hover":{display:"none"}}),container:(0,a.css)({height:"calc(100% - 32px)",width:"calc(100% - 32px)",position:"absolute",backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,padding:"5px"}),header:(0,a.css)({marginBottom:"20px",display:"flex",justifyContent:"space-between"}),map:(0,a.css)({marginTop:"20px",marginBottom:"20px"}),span:(0,a.css)({display:"flex"}),service1:(0,a.css)({width:"25%"}),bar1:(0,a.css)({marginLeft:"5%",width:"70%"}),service2:(0,a.css)({width:"25%"}),bar2:(0,a.css)({marginLeft:"10%",width:"15%"}),service3:(0,a.css)({width:"20%",marginLeft:"5%"}),bar3:(0,a.css)({marginLeft:"10%",width:"65%"}),service4:(0,a.css)({width:"20%",marginLeft:"5%"}),bar4:(0,a.css)({marginLeft:"15%",width:"60%"}),service5:(0,a.css)({width:"15%",marginLeft:"10%"}),bar5:(0,a.css)({marginLeft:"20%",width:"35%"}),service6:(0,a.css)({width:"15%",marginLeft:"10%"}),bar6:(0,a.css)({marginLeft:"30%",width:"15%"})}}var y=n(675);function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class S extends o.Bs{_onActivate(){this.updateBody(),(0,d.zY)(this).subscribeToState((e,t)=>{e.traceId===t.traceId&&e.spanId===t.spanId||(this.updateBody(),(0,y.EE)(y.NO.analyse_traces,y.ir.analyse_traces.open_trace,{traceId:e.traceId,spanId:e.spanId}))})}updateBody(){const e=(0,d.zY)(this);e.state.traceId?this.setState({body:new g({traceId:e.state.traceId,spanId:e.state.spanId})}):this.setState({body:new m.v({message:"No trace selected"})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){w(e,t,n[t])})}return e}({},e)),this.addActivationHandler(this._onActivate.bind(this))}}w(S,"Component",({model:e})=>{const{body:t}=e.useState();return t&&s().createElement(t.Component,{model:t})});var O=n(2245),x=n(5292),E=n(9240);class j extends o.Bs{_onActivate(){this.runIssueDetectionQuery();const e=(0,d._b)(this);this._subs.add(e.subscribeToState((e,t)=>{e.value!==t.value&&(this.resetIssues(),this.runIssueDetectionQuery())}))}runIssueDetectionQuery(){const e=(0,d._b)(this),t=(0,i.dateTime)(),n=(0,i.dateTime)(t).subtract(1,"minute"),a=new o.JZ({from:n.toISOString(),to:t.toISOString()}),r=new o.dt({maxDataPoints:1,datasource:{uid:String(e.state.value)},$timeRange:a,queries:[{refId:"issueDetectorQuery",query:"{} | rate()",queryType:"traceql",tableType:"spans",limit:1,spss:1,filters:[]}]});this._subs.add(r.subscribeToState(e=>{var t,n,a,r;(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Error&&(((null===(r=e.data)||void 0===r||null===(a=r.errors)||void 0===a||null===(n=a[0])||void 0===n?void 0:n.message)||"").includes("localblocks processor not found")&&this.setState({hasIssue:!0}))})),r.activate()}resetIssues(){this.setState({hasIssue:!1})}constructor(){super({hasIssue:!1}),this.addActivationHandler(this._onActivate.bind(this))}}const k=({detector:e})=>{const{hasIssue:t}=e.useState();return t?s().createElement(c.Alert,{severity:"warning",title:"TraceQL metrics not configured"},s().createElement("p",null,'We found an error running a TraceQL metrics query: "localblocks processor not found". This typically means the "local-blocks" processor is not configured in Tempo, which is required for Grafana Traces Drilldown to work.',s().createElement(c.LinkButton,{icon:"external-link-alt",fill:"text",size:"sm",target:"_blank",href:"https://grafana.com/docs/tempo/latest/operations/traceql-metrics"},"Read documentation"))):null};var C=n(1769),P=n(783),_=n(3292);function N({serviceName:e,model:t}){const{isLoading:n,component:a}=(0,l.usePluginComponent)("grafana-asserts-app/entity-assertions-widget/v1"),[i,c]=(0,r.useState)();return(0,r.useEffect)(()=>{const e=o.jh.getTimeRange(t);c(e.state.value);const n=e.subscribeToState(e=>{c(e.value)});return()=>{n.unsubscribe()}},[t]),!n&&a&&i?s().createElement(a,{size:"md",source:"Traces Drilldown",query:{start:i.from.valueOf(),end:i.to.valueOf(),entityName:e,entityType:"Service"},returnToPrevious:!0}):null}var D=n(5755),T=n(1893),I=n(4568),A=n(1508),$=n(7186),L=n(9814);function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){V(e,t,n[t])})}return e}n(1546);const F={sm:{width:"25%",minWidth:384},md:{width:"50%",minWidth:568},lg:{width:"75%",minWidth:744}};function z({children:e,onClose:t,closeOnMaskClick:n=!0,scrollableContent:s=!0,title:i,subtitle:o,size:l="md",tabs:u}){const[d,m,p]=function(){const[e,t]=(0,r.useState)(void 0),n=(0,r.useCallback)(e=>{t(M(e.clientX))},[]),a=(0,r.useCallback)(e=>{const n=e.touches[0];t(M(n.clientX))},[]),s=(0,r.useCallback)(e=>{document.removeEventListener("mousemove",n),document.removeEventListener("mouseup",s)},[n]),i=(0,r.useCallback)(e=>{document.removeEventListener("touchmove",a),document.removeEventListener("touchend",i)},[a]);return[e,function(e){e.stopPropagation(),e.preventDefault(),document.addEventListener("mousemove",n),document.addEventListener("mouseup",s)},function(e){e.stopPropagation(),e.preventDefault(),document.addEventListener("touchmove",a),document.addEventListener("touchend",i)}]}(),f=(0,c.useStyles2)(R),v=(0,c.useStyles2)(H,l),g=(0,c.useStyles2)(c.getDragStyles),h=r.useRef(null),{dialogProps:b,titleProps:y}=(0,D.s)({},h),{overlayProps:w}=(0,I.e)({isDismissable:!1,isOpen:!0,onClose:t},h);(0,r.useEffect)(()=>{if(document.body)return document.body.classList.add("body-drawer-open"),()=>{document.body.classList.remove("body-drawer-open")}},[]);const S=r.createElement("div",{className:f.content},e),O=null!=d?d:F[l].width,x=F[l].minWidth;return r.createElement(A.A,{open:!0,onClose:t,placement:"right",getContainer:"#trace-exploration",className:f.drawerContent,rootClassName:f.drawer,classNames:{wrapper:v},styles:{wrapper:{width:O,minWidth:x}},width:"",motion:{motionAppear:!0,motionName:f.drawerMotion},maskClassName:f.mask,maskClosable:n,maskMotion:{motionAppear:!0,motionName:f.maskMotion}},r.createElement(T.n1,{restoreFocus:!0,contain:!0,autoFocus:!0},r.createElement("div",(E=B({"aria-label":"string"==typeof i?$.Tp.components.Drawer.General.title(i):$.Tp.components.Drawer.General.title("no title"),className:f.container},w,b),j=null!=(j={ref:h})?j:{},Object.getOwnPropertyDescriptors?Object.defineProperties(E,Object.getOwnPropertyDescriptors(j)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(j)).forEach(function(e){Object.defineProperty(E,e,Object.getOwnPropertyDescriptor(j,e))}),E),r.createElement("div",{className:(0,a.cx)(g.dragHandleVertical,f.resizer),onMouseDown:m,onTouchStart:p}),r.createElement("div",{className:(0,a.cx)(f.header,Boolean(u)&&f.headerWithTabs)},r.createElement("div",{className:f.actions},r.createElement(c.IconButton,{name:"times",variant:"secondary",onClick:t,"data-testid":$.Tp.components.Drawer.General.close,tooltip:(0,L.t)("grafana-ui.drawer.close","Close")})),"string"==typeof i?r.createElement("div",{className:f.titleWrapper},r.createElement(c.Text,B({element:"h3"},y),i),o&&r.createElement("div",{className:f.subtitle,"data-testid":$.Tp.components.Drawer.General.subtitle},o)):i,u&&r.createElement("div",{className:f.tabsWrapper},u)),s?r.createElement(c.ScrollContainer,{showScrollIndicators:!0},S):S)));var E,j}function M(e){let t=document.body.offsetWidth-(e-document.body.offsetLeft);return`${Math.min(t/document.body.clientWidth*100,98).toFixed(2)}vw`}const R=e=>{var t,n;return{container:(0,a.css)({display:"flex",flexDirection:"column",height:"100%",flex:"1 1 0",minHeight:"100%",position:"relative"}),drawer:(0,a.css)({top:0,position:"absolute !important",".rc-drawer-content-wrapper":{boxShadow:e.shadows.z3}}),drawerContent:(0,a.css)({backgroundColor:`${e.colors.background.primary} !important`,display:"flex",overflow:"unset !important",flexDirection:"column"}),drawerMotion:(0,a.css)({"&-appear":{transform:"translateX(100%)",transition:"none !important","&-active":{transition:`${e.transitions.create("transform")} !important`,transform:"translateX(0)"}}}),mask:(0,a.css)({backgroundColor:"transparent !important",position:"absolute !important","&:before":{backgroundColor:`${e.components.overlay.background} !important`,bottom:0,content:'""',left:0,position:"absolute",right:0,top:0}}),maskMotion:(0,a.css)({"&-appear":{opacity:0,"&-active":{opacity:1,transition:e.transitions.create("opacity")}}}),header:(0,a.css)({label:"drawer-header",flexGrow:0,padding:e.spacing(2,2,3),borderBottom:`1px solid ${e.colors.border.weak}`}),headerWithTabs:(0,a.css)({borderBottom:"none"}),actions:(0,a.css)({position:"absolute",right:e.spacing(1),top:e.spacing(1)}),titleWrapper:(0,a.css)({label:"drawer-title",overflowWrap:"break-word"}),subtitle:(0,a.css)({label:"drawer-subtitle",color:e.colors.text.secondary,paddingTop:e.spacing(1)}),content:(0,a.css)({padding:e.spacing(null!==(n=null===(t=e.components.drawer)||void 0===t?void 0:t.padding)&&void 0!==n?n:2),height:"100%",flexGrow:1,minHeight:0}),tabsWrapper:(0,a.css)({label:"drawer-tabs",paddingLeft:e.spacing(2),margin:e.spacing(1,-1,-3,-3)}),resizer:(0,a.css)({top:0,left:e.spacing(-1),bottom:0,position:"absolute",zIndex:e.zIndex.modal})}};function H(e,t){return(0,a.css)({label:`drawer-content-wrapper-${t}`,overflow:"unset !important",[e.breakpoints.down("md")]:{width:`calc(100% - ${e.spacing(2)}) !important`,minWidth:"0 !important"}})}const q=({children:e,title:t,isOpen:n,onClose:a,embedded:r=!1,forceNoDrawer:i=!1,investigationButton:o})=>{const l=(0,c.useStyles2)(G);return n?i||r?s().createElement("div",{className:l.container},s().createElement("div",{className:l.drawerHeader},s().createElement(c.Button,{variant:"primary",fill:"text",size:"md",icon:"arrow-left",onClick:a},"Back to all traces"),r&&o),e):s().createElement(z,{size:"lg",title:t,onClose:a},e):null},G=e=>({container:(0,a.css)({height:"100%",width:"100%",background:e.colors.background.primary,padding:e.spacing(2),display:"flex",flexDirection:"column",position:"absolute",top:0,left:0,zIndex:3}),drawerHeader:(0,a.css)({display:"flex",justifyContent:"space-between",alignItems:"center",paddingBottom:e.spacing(2),h4:{margin:0}})});function W(e){const t=e.filter(e=>e.key&&e.operator&&e.value).map(e=>function(e){let t=e.value;var n;return!["span.messaging.destination.partition.id","span.network.protocol.version"].includes(e.key)&&(null!=(n=t)&&""!==n&&!isNaN(Number(n.toString().trim()))||["status","kind","span:status","span:kind","duration","span:duration","trace:duration","event:timeSinceStart"].includes(e.key)||["true","false"].includes(t))||"string"==typeof t&&(t=t.replace(/["\\]/g,e=>`\\${e}`),t=`"${t}"`),`${e.key}${e.operator}${t}`}(e)).join("&&");return t.length?t:"true"}function U(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){U(e,t,n[t])})}return e}function K(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class X extends o.H9{constructor(e){var t;super({addFilterButtonText:"Add filter",name:u.Ao,datasource:u.Vl,hide:i.VariableHide.hideLabel,layout:"combobox",filters:(null!==(t=e.initialFilters)&&void 0!==t?t:[]).map(t=>K(Y({},t),{readOnly:e.embedded,origin:e.embedderName})),allowCustomValue:!0,expressionBuilder:W}),U(this,"initialFilters",void 0),U(this,"embedderName",void 0),U(this,"embedded",void 0),this.initialFilters=e.initialFilters,this.embedderName=e.embedderName,this.embedded=e.embedded,this.subscribeToState(e=>{if(e.filters&&this.embedded){let t=!1;const n=e.filters.map(e=>{var n;return(null===(n=this.initialFilters)||void 0===n?void 0:n.find(t=>t.key===e.key&&t.operator===e.operator&&t.value===e.value))&&!e.readOnly&&e.origin!==this.embedderName?(t=!0,K(Y({},e),{readOnly:!0,origin:this.embedderName})):e});t&&this.setState({filters:n})}})}}function Q(e,t,n,a,r,s,i){try{var o=e[s](i),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(a,r)}function Z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),a.forEach(function(t){Z(e,t,n[t])})}return e}function ee(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t.push.apply(t,n)}return t}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const te=`${"2025-08-13T08:34:24.713Z".split("T")[0]} (38d0e3a)`;class ne extends o.Bs{_onActivate(){this.state.topScene||this.setState({topScene:new _.jc({})}),this._subs.add(this.subscribeToEvent(u.vR,e=>{this.setupInvestigationButton(e.payload.traceId),this.setState({traceId:e.payload.traceId,spanId:e.payload.spanId})})),this.state.traceId&&this.setupInvestigationButton(this.state.traceId),o.jh.lookupVariable(u.EY,this).subscribeToState(e=>{e.value&&localStorage.setItem(u.cd,e.value.toString())}),this.state.issueDetector&&(this.state.issueDetector.isActive||this.state.issueDetector.activate())}getUrlState(){return{traceId:this.state.traceId,spanId:this.state.spanId}}updateFromUrl(e){const t={};(e.traceId||e.spanId)&&(t.traceId=e.traceId?e.traceId:void 0,t.spanId=e.spanId?e.spanId:void 0),this.setState(t)}getMetricVariable(){const e=o.jh.lookupVariable(u.PU,this);if(!(e instanceof o.yP))throw new Error("Metric variable not found");var t;return e.getValue()||e.changeValueTo(null!==(t=this.state.initialMetric)&&void 0!==t?t:"rate"),e}getMetricFunction(){return this.getMetricVariable().getValue()}closeDrawer(){this.setState({traceId:void 0,spanId:void 0})}setupInvestigationButton(e){const t=(0,d.zY)(this),n=(0,d.U4)(t),a=new o.dt({datasource:{uid:n},queries:[{refId:"A",query:e,queryType:"traceql"}]}),r=new C.L({query:e,type:"trace",dsUid:n,$data:a});r.activate(),this.setState({addToInvestigationButton:r}),this._subs.add(r.subscribeToState(()=>{this.updateInvestigationLink()})),a.activate(),this._subs.add(a.subscribeToState(e=>{var t,n,a;if((null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done&&(null===(a=e.data)||void 0===a||null===(n=a.series)||void 0===n?void 0:n.length)>0){var s,o;const t=null===(o=e.data.series[0])||void 0===o||null===(s=o.fields)||void 0===s?void 0:s.find(e=>"serviceName"===e.name);t&&t.values[0]&&r.setState(ee(J({},r.state),{labelValue:`${t.values[0]}`}))}})),r.setState(ee(J({},r.state),{labelValue:e}))}updateInvestigationLink(){return(e=function*(){const{addToInvestigationButton:e}=this.state;if(!e)return;const t=yield(0,P.Fh)(e);t&&this.setState({investigationLink:t})},function(){var t=this,n=arguments;return new Promise(function(a,r){var s=e.apply(t,n);function i(e){Q(s,a,r,i,o,"next",e)}function o(e){Q(s,a,r,i,o,"throw",e)}i(void 0)})}).call(this);var e}constructor(e){var t,n,a;super(J({$timeRange:null!==(t=e.$timeRange)&&void 0!==t?t:new o.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:ie(e),controls:null!==(a=e.controls)&&void 0!==a?a:[new o.KE({}),new o.WM({})],body:new ae({}),drawerScene:new S({}),issueDetector:new j},e)),Z(this,"_urlSync",new o.So(this,{keys:["traceId","spanId"]})),Z(this,"onChangeMetricFunction",e=>{const t=this.getMetricVariable();e&&t.getValue()!==e&&t.changeValueTo(e,void 0,!0)}),this.addActivationHandler(this._onActivate.bind(this))}}Z(ne,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,c.useStyles2)(oe);return s().createElement("div",{className:n.bodyContainer}," ",t&&s().createElement(t.Component,{model:t})," ")});class ae extends o.Bs{}Z(ae,"Component",({model:e})=>{const t=(0,d.zY)(e),{controls:n,topScene:a,drawerScene:r,traceId:i,issueDetector:o,investigationLink:l,addToInvestigationButton:u,embedded:m}=t.useState(),{hasIssue:p}=(null==o?void 0:o.useState())||{hasIssue:!1},f=(0,c.useStyles2)(oe);return s().createElement("div",{className:f.container,id:"trace-exploration"},p&&o&&s().createElement(k,{detector:o}),m?s().createElement(re,{model:e}):s().createElement(se,{controls:n,model:e}),s().createElement("div",{className:f.body},a&&s().createElement(a.Component,{model:a})),s().createElement(q,{isOpen:!!r&&!!i,onClose:()=>t.closeDrawer(),title:`View trace ${i}`,embedded:m,forceNoDrawer:m,investigationButton:u&&l&&s().createElement(c.Button,{variant:"secondary",size:"sm",icon:"plus-square",onClick:e=>{(null==l?void 0:l.onClick)&&l.onClick(e),(0,y.EE)(y.NO.analyse_traces,y.ir.analyse_traces.add_to_investigation_trace_view_clicked),setTimeout(()=>t.closeDrawer(),100)}},P.R_)},r&&s().createElement(r.Component,{model:r})))});const re=({model:e})=>{var t;const n=(0,l.useReturnToPrevious)(),a=(0,c.useStyles2)(oe,!0),i=(0,d.zY)(e),{returnToPreviousSource:u}=i.useState(),m=(0,d.gG)(i),p=(0,d.h7)(i),f=i.state.controls.find(e=>e instanceof o.KE),v=null===(t=i.state.$timeRange)||void 0===t?void 0:t.useState(),g=m.useState(),h=i.getMetricVariable().useState(),[b,w]=s().useState(()=>(0,d.__)(i));return null==p||p.changeValueTo(E.Xn[1].value),(0,r.useEffect)(()=>{w((0,d.__)(i))},[v,g,h,i]),s().createElement("div",{className:a.headerContainer},s().createElement(c.Stack,{gap:1,alignItems:"center",wrap:"wrap",justifyContent:"space-between"},s().createElement(p.Component,{model:p}),m&&s().createElement("div",null,s().createElement(m.Component,{model:m})),s().createElement(c.Stack,{gap:1,alignItems:"center"},s().createElement(c.LinkButton,{href:b,variant:"secondary",icon:"arrow-right",onClick:()=>{n(u||"previous"),(0,y.EE)(y.NO.common,y.ir.common.go_to_full_app_clicked)}},"Traces Drilldown"),f&&s().createElement(f.Component,{model:f}))))},se=({controls:e,model:t})=>{const n=(0,c.useStyles2)(oe),[a,i]=s().useState(!1),m=(e=>{const[t,n]=s().useState(),a=(0,d.zY)(e),i=(0,d.gG)(a),o=e=>{var t;const n=e.find(e=>"resource.service.name"===e.key);return"="===(null==n?void 0:n.operator)||"=~"===(null==n?void 0:n.operator)?null==n||null===(t=n.value)||void 0===t?void 0:t.replace(/"/g,""):void 0};return(0,r.useEffect)(()=>{n(o(i.state.filters));const e=i.subscribeToState(e=>{n(o(e.filters))});return()=>{e.unsubscribe()}},[i]),t})(t),p=(0,d.zY)(t),f=o.jh.lookupVariable(u.EY,p),v=(0,d.gG)(p),g=(0,d.h7)(p);function h(){const e=(0,c.useStyles2)(oe);return s().createElement("div",{className:e.menuHeader},s().createElement("h5",null,"Grafana Traces Drilldown v","1.1.3"),s().createElement("div",{className:e.menuHeaderSubtitle},"Last update: ",te))}const b=s().createElement(c.Menu,{header:s().createElement(h,null)},s().createElement("div",{className:n.menu},l.config.feedbackLinksEnabled&&s().createElement(c.Menu.Item,{label:"Give feedback",ariaLabel:"Give feedback",icon:"comment-alt-message",url:"https://grafana.qualtrics.com/jfe/form/SV_9LUZ21zl3x4vUcS",target:"_blank",onClick:()=>(0,y.EE)(y.NO.common,y.ir.common.global_docs_link_clicked)}),s().createElement(c.Menu.Item,{label:"Documentation",ariaLabel:"Documentation",icon:"external-link-alt",url:"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/",target:"_blank",onClick:()=>(0,y.EE)(y.NO.common,y.ir.common.feedback_link_clicked)})));return s().createElement("div",{className:n.headerContainer},s().createElement(c.Stack,{gap:1,justifyContent:"space-between",wrap:"wrap"},s().createElement(c.Stack,{gap:1,alignItems:"center",wrap:"wrap"},f&&s().createElement(c.Stack,{gap:0,alignItems:"center"},s().createElement("div",{className:n.datasourceLabel},"Data source"),s().createElement(f.Component,{model:f}))),s().createElement("div",{className:n.controls},s().createElement(N,{serviceName:m||"",model:t}),s().createElement(c.Dropdown,{overlay:b,onVisibleChange:()=>i(!a)},s().createElement(c.Button,{variant:"secondary",icon:"info-circle"},"Need help",s().createElement(c.Icon,{className:n.helpIcon,name:a?"angle-up":"angle-down",size:"lg"}))),e.map(e=>s().createElement(e.Component,{key:e.state.key,model:e})))),s().createElement(c.Stack,{gap:1,alignItems:"center",wrap:"wrap"},s().createElement(c.Stack,{gap:0,alignItems:"center"},s().createElement("div",{className:n.datasourceLabel},"Filters"),g&&s().createElement(g.Component,{model:g})),v&&s().createElement("div",null,s().createElement(v.Component,{model:v}))))};function ie(e){return new o.Pj({variables:[new o.mI({name:u.EY,label:"Data source",value:e.initialDS,pluginId:"tempo",isReadOnly:e.embedded}),new x.x({name:u.CE,isReadOnly:e.embedded}),new X({initialFilters:e.initialFilters,embedderName:e.embedderName,embedded:e.embedded}),new o.yP({name:u.PU,hide:O.zL.hideVariable}),new o.yP({name:u.z,defaultToAll:!1,value:e.initialGroupBy}),new o.yP({name:u.gP,defaultToAll:!1}),new o.yP({name:u.pf,defaultToAll:!1,hide:O.zL.hideVariable}),new o.yP({name:u.xc,defaultToAll:!1,hide:O.zL.hideVariable})]})}function oe(e,t){return{bodyContainer:(0,a.css)({label:"bodyContainer",flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),container:(0,a.css)({label:"container",flexGrow:1,display:"flex",gap:e.spacing(1),minHeight:"100%",flexDirection:"column",padding:`0 ${e.spacing(2)} ${e.spacing(2)} ${e.spacing(2)}`,overflow:"auto",maxHeight:"100%",position:"relative"}),drawerHeader:(0,a.css)({display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:`1px solid ${e.colors.border.weak}`,paddingBottom:e.spacing(2),marginBottom:e.spacing(2),h3:{margin:0}}),drawerHeaderButtons:(0,a.css)({display:"flex",justifyContent:"flex-end",gap:e.spacing(1.5)}),body:(0,a.css)({label:"body",flexGrow:1,display:"flex",flexDirection:"column",gap:e.spacing(1)}),headerContainer:(0,a.css)({label:"headerContainer",backgroundColor:t?e.colors.background.primary:e.colors.background.canvas,display:"flex",flexDirection:"column",position:"sticky",top:0,zIndex:3,padding:`${e.spacing(1.5)} 0`,gap:e.spacing(1)}),datasourceLabel:(0,a.css)({label:"datasourceLabel",fontSize:"12px",padding:`0 ${e.spacing(1)}`,height:"32px",display:"flex",alignItems:"center",justifyContent:"flex-start",fontWeight:e.typography.fontWeightMedium,position:"relative",right:-1,width:"90px"}),controls:(0,a.css)({label:"controls",display:"flex",gap:e.spacing(1),zIndex:3,flexWrap:"wrap"}),menu:(0,a.css)({label:"menu","svg, span":{color:e.colors.text.link}}),menuHeader:a.css`
      padding: ${e.spacing(.5,1)};
      white-space: nowrap;
    `,menuHeaderSubtitle:a.css`
      color: ${e.colors.text.secondary};
      font-size: ${e.typography.bodySmall.fontSize};
    `,tooltip:(0,a.css)({label:"tooltip",fontSize:"14px",lineHeight:"22px",width:"180px",textAlign:"center"}),helpIcon:(0,a.css)({label:"helpIcon",marginLeft:e.spacing(1)}),filters:(0,a.css)({label:"filters",marginTop:e.spacing(1),display:"flex",gap:e.spacing(1)})}}},9546:(e,t,n)=>{"use strict";n.d(t,{G:()=>s,h:()=>i});var a=n(1269),r=n(7781);const s=e=>[{topic:r.DataTopic.Annotations,operator:()=>t=>t.pipe((0,a.map)(t=>t.map(t=>{if("exemplar"===t.name){const n=t.fields.find(e=>"traceId"===e.name);n&&(n.config.links=[{title:"View trace",url:"#${__value.raw}",onClick:t=>{var n,a,r;t.e.stopPropagation();const s=null===(r=t.e.target)||void 0===r||null===(a=r.parentElement)||void 0===a||null===(n=a.parentElement)||void 0===n?void 0:n.href;if(!s||-1===s.indexOf("#"))return;const i=s.split("#")[1];i&&""!==i&&(null==e||e(i))}}])}return t})))}],i=()=>[{topic:r.DataTopic.Annotations,operator:()=>e=>e.pipe((0,a.map)(e=>e.filter(e=>"exemplar"!==e.name)))}]},9958:(e,t,n)=>{"use strict";n.d(t,{KS:()=>l,a3:()=>o,oM:()=>c});var a=n(3241),r=n(8010),s=n(2689);const i=(Math.log10(1e3),[{unit:"d",microseconds:864e8,ofPrevious:24},{unit:"h",microseconds:36e8,ofPrevious:60},{unit:"m",microseconds:6e7,ofPrevious:60},{unit:"s",microseconds:1e6,ofPrevious:1e3},{unit:"ms",microseconds:1e3,ofPrevious:1e3},{unit:"μs",microseconds:1,ofPrevious:1e3}]),o=e=>{const[t,n]=(0,a.dropWhile)(i,({microseconds:t},n)=>n<i.length-1&&t>e);if(1e3===t.ofPrevious)return`${(0,a.round)(e/t.microseconds,2)}${t.unit}`;let r=Math.floor(e/t.microseconds),s=e/n.microseconds%t.ofPrevious;const o=Math.round(s);o===t.ofPrevious?(r+=1,s=0):s=o;const l=`${r}${t.unit}`;return 0===s?l:`${l} ${s}${n.unit}`},l=(e,t=50)=>Math.floor(e/t)||1,c=(e,t)=>{const n=r.jh.getTimeRange(e),a=n.state.value.from.unix(),i=n.state.value.to.unix(),o=(0,s.duration)(i-a,"s");return`${l(o.asSeconds(),t)}s`}}}]);
//# sourceMappingURL=712.js.map