"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[351],{5351:(e,t,a)=>{a.r(t),a.d(t,{default:()=>y});var r=a(6089),n=a(8531),l=a(2007),i=a(5959),c=a.n(i),s=a(1160),o=a(2445),m=a(5731),d=a(2993),u=a(7781),p=a(1792);function g(){const e=(0,l.useStyles2)(f),t=(0,l.useTheme2)();return c().createElement("div",{className:e.wrap},c().createElement("div",{className:e.graphicContainer},c().createElement(p.A,{src:(t.isDark,"/public/plugins/grafana-metricsdrilldown-app/img/logo.svg")})),c().createElement("div",{className:e.text},c().createElement("h3",{className:e.title},"Welcome to Grafana Metrics Drilldown"),c().createElement("p",null,"We noticed there is no Prometheus datasource configured.",c().createElement("br",null),"Add a"," ",c().createElement("a",{className:"external-link",href:u.locationUtil.assureBaseUrl("/connections/datasources/new")},"Prometheus datasource")," ","to view metrics."),c().createElement("br",null),c().createElement("p",null,"Check"," ",c().createElement("a",{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/metrics/",target:"_blank",className:"external-link",rel:"noreferrer"},"our docs")," ","to learn more or",c().createElement("br",null),c().createElement("a",{href:"https://play.grafana.org/a/grafana-metricsdrilldown-app/drilldown",target:"_blank",className:"external-link",rel:"noreferrer"},"try it online")," ","in Grafana Play!")))}const f=e=>({graphicContainer:(0,r.css)({[e.breakpoints.up("md")]:{alignSelf:"flex-end",height:"auto",padding:e.spacing(1),width:"300px"},[e.breakpoints.up("lg")]:{alignSelf:"flex-end",height:"auto",padding:e.spacing(1),width:"400px"},display:"flex",height:"250px",justifyContent:"center",margin:"0 auto",padding:e.spacing(1),width:"200px"}),text:(0,r.css)({alignItems:"center",display:"flex",flexDirection:"column",justifyContent:"center"}),title:(0,r.css)({marginBottom:"1.5rem"}),wrap:(0,r.css)({[e.breakpoints.up("md")]:{flexDirection:"row",margin:"4rem auto auto auto"},alignItems:"center",display:"flex",flexDirection:"column",margin:"0 auto auto auto",padding:"2rem",textAlign:"center"})});var h=a(8732),w=a(1522),x=a(3347);var E=a(2745),v=a(7476);const k=(0,i.createContext)(null);(0,s.Js)();const b=Object.values(n.config.datasources).filter(v.aQ);try{m.K.migrate()}catch(e){o.v.error(e,{cause:"User preferences migration"})}function y(e){const t=(0,l.useStyles2)(C),[a]=(0,w.n)(),{trail:r,goToUrlForTrail:n}=(0,E.w)();return function(){const e=(0,i.useRef)(!1);(0,i.useEffect)(()=>{if(!e.current){e.current=!0;const t=new URL(window.location.href).searchParams.get("metric")?"metric-details":"metrics-reducer";(0,x.z)("app_initialized",{view:t})}},[])}(),a?c().createElement("div",{className:t.appContainer,"data-testid":"metrics-drilldown-app"},c().createElement(d.E,{error:a})):b.length?c().createElement("div",{className:t.appContainer,"data-testid":"metrics-drilldown-app"},c().createElement(k.Provider,{value:e},c().createElement(E.J.Provider,{value:{trail:r,goToUrlForTrail:n}},c().createElement(h.S,null)))):c().createElement(g,null)}function C(e){return{appContainer:(0,r.css)({display:"flex",flexDirection:"column",height:"100%",backgroundColor:e.colors.background.primary})}}}}]);
//# sourceMappingURL=351.js.map?_cache=eca2ae683cc3d0cb9596