"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[854],{2781:(n,t,e)=>{n.exports=e.p+"649058283f564041551d.wasm"},1854:(n,t,e)=>{let r;e.r(t),e.d(t,{ChangepointDetector:()=>j,custom_init:()=>v,default:()=>R,initLogging:()=>I,initSync:()=>S});const _="undefined"!=typeof TextDecoder?new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}):{decode:()=>{throw Error("TextDecoder not available")}};"undefined"!=typeof TextDecoder&&_.decode();let i=null;function o(){return null!==i&&0!==i.byteLength||(i=new Uint8Array(r.memory.buffer)),i}function c(n,t){return n>>>=0,_.decode(o().subarray(n,n+t))}const a=new Array(128).fill(void 0);a.push(void 0,null,!0,!1);let g=a.length;function b(n){g===a.length&&a.push(a.length+1);const t=g;return g=a[t],a[t]=n,t}function f(n){return a[n]}function u(n){const t=f(n);return function(n){n<132||(a[n]=g,g=n)}(n),t}let s=0;const w="undefined"!=typeof TextEncoder?new TextEncoder("utf-8"):{encode:()=>{throw Error("TextEncoder not available")}},d="function"==typeof w.encodeInto?function(n,t){return w.encodeInto(n,t)}:function(n,t){const e=w.encode(n);return t.set(e),{read:n.length,written:e.length}};function l(n,t,e){if(void 0===e){const e=w.encode(n),r=t(e.length,1)>>>0;return o().subarray(r,r+e.length).set(e),s=e.length,r}let r=n.length,_=t(r,1)>>>0;const i=o();let c=0;for(;c<r;c++){const t=n.charCodeAt(c);if(t>127)break;i[_+c]=t}if(c!==r){0!==c&&(n=n.slice(c)),_=e(_,r,r=c+3*n.length,1)>>>0;const t=o().subarray(_+c,_+r);c+=d(n,t).written,_=e(_,r,c,1)>>>0}return s=c,_}function y(n){return null==n}let p=null;function h(){return(null===p||!0===p.buffer.detached||void 0===p.buffer.detached&&p.buffer!==r.memory.buffer)&&(p=new DataView(r.memory.buffer)),p}function m(n){const t=typeof n;if("number"==t||"boolean"==t||null==n)return`${n}`;if("string"==t)return`"${n}"`;if("symbol"==t){const t=n.description;return null==t?"Symbol":`Symbol(${t})`}if("function"==t){const t=n.name;return"string"==typeof t&&t.length>0?`Function(${t})`:"Function"}if(Array.isArray(n)){const t=n.length;let e="[";t>0&&(e+=m(n[0]));for(let r=1;r<t;r++)e+=", "+m(n[r]);return e+="]",e}const e=/\[object ([^\]]+)\]/.exec(toString.call(n));let r;if(!(e.length>1))return toString.call(n);if(r=e[1],"Object"==r)try{return"Object("+JSON.stringify(n)+")"}catch(n){return"Object"}return n instanceof Error?`${n.name}: ${n.message}\n${n.stack}`:r}function I(n){try{const e=r.__wbindgen_add_to_stack_pointer(-16);r.initLogging(e,y(n)?0:b(n));var t=h().getInt32(e+0,!0);if(h().getInt32(e+4,!0))throw u(t)}finally{r.__wbindgen_add_to_stack_pointer(16)}}function v(){r.custom_init()}function A(n,t){try{return n.apply(this,t)}catch(n){r.__wbindgen_exn_store(b(n))}}const k="undefined"==typeof FinalizationRegistry?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(n=>r.__wbg_changepointdetector_free(n>>>0,1));class j{static __wrap(n){n>>>=0;const t=Object.create(j.prototype);return t.__wbg_ptr=n,k.register(t,t.__wbg_ptr,t),t}__destroy_into_raw(){const n=this.__wbg_ptr;return this.__wbg_ptr=0,k.unregister(this),n}free(){const n=this.__destroy_into_raw();r.__wbg_changepointdetector_free(n,0)}constructor(n){try{const _=r.__wbindgen_add_to_stack_pointer(-16);r.changepointdetector_new(_,b(n));var t=h().getInt32(_+0,!0),e=h().getInt32(_+4,!0);if(h().getInt32(_+8,!0))throw u(e);return this.__wbg_ptr=t>>>0,k.register(this,this.__wbg_ptr,this),this}finally{r.__wbindgen_add_to_stack_pointer(16)}}static normalGamma(n){try{const _=r.__wbindgen_add_to_stack_pointer(-16);r.changepointdetector_normalGamma(_,y(n)?0:b(n));var t=h().getInt32(_+0,!0),e=h().getInt32(_+4,!0);if(h().getInt32(_+8,!0))throw u(e);return j.__wrap(t)}finally{r.__wbindgen_add_to_stack_pointer(16)}}static defaultArgpcp(n){try{const _=r.__wbindgen_add_to_stack_pointer(-16);r.changepointdetector_defaultArgpcp(_,y(n)?0:b(n));var t=h().getInt32(_+0,!0),e=h().getInt32(_+4,!0);if(h().getInt32(_+8,!0))throw u(e);return j.__wrap(t)}finally{r.__wbindgen_add_to_stack_pointer(16)}}detectChangepoints(n){try{const _=r.__wbindgen_add_to_stack_pointer(-16);r.changepointdetector_detectChangepoints(_,this.__wbg_ptr,b(n));var t=h().getInt32(_+0,!0),e=h().getInt32(_+4,!0);if(h().getInt32(_+8,!0))throw u(e);return u(t)}finally{r.__wbindgen_add_to_stack_pointer(16)}}}function x(){const n={wbg:{}};return n.wbg.__wbindgen_error_new=function(n,t){return b(new Error(c(n,t)))},n.wbg.__wbindgen_is_string=function(n){return"string"==typeof f(n)},n.wbg.__wbindgen_object_drop_ref=function(n){u(n)},n.wbg.__wbindgen_string_get=function(n,t){const e=f(t),_="string"==typeof e?e:void 0;var i=y(_)?0:l(_,r.__wbindgen_malloc,r.__wbindgen_realloc),o=s;h().setInt32(n+4,o,!0),h().setInt32(n+0,i,!0)},n.wbg.__wbindgen_object_clone_ref=function(n){return b(f(n))},n.wbg.__wbindgen_string_new=function(n,t){return b(c(n,t))},n.wbg.__wbindgen_is_object=function(n){const t=f(n);return"object"==typeof t&&null!==t},n.wbg.__wbindgen_is_undefined=function(n){return void 0===f(n)},n.wbg.__wbindgen_in=function(n,t){return f(n)in f(t)},n.wbg.__wbindgen_number_get=function(n,t){const e=f(t),r="number"==typeof e?e:void 0;h().setFloat64(n+8,y(r)?0:r,!0),h().setInt32(n+0,!y(r),!0)},n.wbg.__wbindgen_is_bigint=function(n){return"bigint"==typeof f(n)},n.wbg.__wbindgen_bigint_from_u64=function(n){return b(BigInt.asUintN(64,n))},n.wbg.__wbindgen_jsval_eq=function(n,t){return f(n)===f(t)},n.wbg.__wbindgen_boolean_get=function(n){const t=f(n);return"boolean"==typeof t?t?1:0:2},n.wbg.__wbg_new_abda76e883ba8a5f=function(){return b(new Error)},n.wbg.__wbg_stack_658279fe44541cf6=function(n,t){const e=l(f(t).stack,r.__wbindgen_malloc,r.__wbindgen_realloc),_=s;h().setInt32(n+4,_,!0),h().setInt32(n+0,e,!0)},n.wbg.__wbg_error_f851667af71bcfc6=function(n,t){let e,_;try{e=n,_=t,console.error(c(n,t))}finally{r.__wbindgen_free(e,_,1)}},n.wbg.__wbg_mark_f0616123624944ec=function(n,t){performance.mark(c(n,t))},n.wbg.__wbg_log_914e3639af348b4e=function(n,t){let e,_;try{e=n,_=t}finally{r.__wbindgen_free(e,_,1)}},n.wbg.__wbg_log_12b4ba535cbd9499=function(n,t,e,_,i,o,c,a){let g,b;try{g=n,b=t}finally{r.__wbindgen_free(g,b,1)}},n.wbg.__wbg_measure_a990198e921c09fd=function(){return A(function(n,t,e,_){let i,o,a,g;try{i=n,o=t,a=e,g=_,performance.measure(c(n,t),c(e,_))}finally{r.__wbindgen_free(i,o,1),r.__wbindgen_free(a,g,1)}},arguments)},n.wbg.__wbindgen_is_function=function(n){return"function"==typeof f(n)},n.wbg.__wbindgen_jsval_loose_eq=function(n,t){return f(n)==f(t)},n.wbg.__wbindgen_as_number=function(n){return+f(n)},n.wbg.__wbg_String_b9412f8799faab3e=function(n,t){const e=l(String(f(t)),r.__wbindgen_malloc,r.__wbindgen_realloc),_=s;h().setInt32(n+4,_,!0),h().setInt32(n+0,e,!0)},n.wbg.__wbindgen_number_new=function(n){return b(n)},n.wbg.__wbg_getwithrefkey_edc2c8960f0f1191=function(n,t){return b(f(n)[f(t)])},n.wbg.__wbg_set_f975102236d3c502=function(n,t,e){f(n)[u(t)]=u(e)},n.wbg.__wbg_call_1084a111329e68ce=function(){return A(function(n,t){return b(f(n).call(f(t)))},arguments)},n.wbg.__wbg_get_3baa728f9d58d3f6=function(n,t){return b(f(n)[t>>>0])},n.wbg.__wbg_length_ae22078168b726f5=function(n){return f(n).length},n.wbg.__wbg_new_a220cf903aa02ca2=function(){return b(new Array)},n.wbg.__wbg_next_de3e9db4440638b2=function(n){return b(f(n).next)},n.wbg.__wbg_next_f9cb570345655b9a=function(){return A(function(n){return b(f(n).next())},arguments)},n.wbg.__wbg_done_bfda7aa8f252b39f=function(n){return f(n).done},n.wbg.__wbg_value_6d39332ab4788d86=function(n){return b(f(n).value)},n.wbg.__wbg_iterator_888179a48810a9fe=function(){return b(Symbol.iterator)},n.wbg.__wbg_get_224d16597dbbfd96=function(){return A(function(n,t){return b(Reflect.get(f(n),f(t)))},arguments)},n.wbg.__wbg_new_525245e2b9901204=function(){return b(new Object)},n.wbg.__wbg_set_673dda6c73d19609=function(n,t,e){f(n)[t>>>0]=u(e)},n.wbg.__wbg_isArray_8364a5371e9737d8=function(n){return Array.isArray(f(n))},n.wbg.__wbg_instanceof_ArrayBuffer_61dfc3198373c902=function(n){let t;try{t=f(n)instanceof ArrayBuffer}catch(n){t=!1}return t},n.wbg.__wbg_isSafeInteger_7f1ed56200d90674=function(n){return Number.isSafeInteger(f(n))},n.wbg.__wbg_entries_7a0e06255456ebcd=function(n){return b(Object.entries(f(n)))},n.wbg.__wbg_buffer_b7b08af79b0b0974=function(n){return b(f(n).buffer)},n.wbg.__wbg_new_ea1883e1e5e86686=function(n){return b(new Uint8Array(f(n)))},n.wbg.__wbg_set_d1e79e2388520f18=function(n,t,e){f(n).set(f(t),e>>>0)},n.wbg.__wbg_length_8339fcf5d8ecd12e=function(n){return f(n).length},n.wbg.__wbg_instanceof_Uint8Array_247a91427532499e=function(n){let t;try{t=f(n)instanceof Uint8Array}catch(n){t=!1}return t},n.wbg.__wbindgen_bigint_get_as_i64=function(n,t){const e=f(t),r="bigint"==typeof e?e:void 0;h().setBigInt64(n+8,y(r)?BigInt(0):r,!0),h().setInt32(n+0,!y(r),!0)},n.wbg.__wbindgen_debug_string=function(n,t){const e=l(m(f(t)),r.__wbindgen_malloc,r.__wbindgen_realloc),_=s;h().setInt32(n+4,_,!0),h().setInt32(n+0,e,!0)},n.wbg.__wbindgen_throw=function(n,t){throw new Error(c(n,t))},n.wbg.__wbindgen_memory=function(){return b(r.memory)},n}function O(n,t){return r=n.exports,E.__wbindgen_wasm_module=t,p=null,i=null,r.__wbindgen_start(),r}function S(n){if(void 0!==r)return r;void 0!==n&&Object.getPrototypeOf(n)===Object.prototype?({module:n}=n):console.warn("using deprecated parameters for `initSync()`; pass a single object instead");const t=x();n instanceof WebAssembly.Module||(n=new WebAssembly.Module(n));return O(new WebAssembly.Instance(n,t),n)}async function E(n){if(void 0!==r)return r;void 0!==n&&Object.getPrototypeOf(n)===Object.prototype?({module_or_path:n}=n):console.warn("using deprecated parameters for the initialization function; pass a single object instead"),void 0===n&&(n=new URL(e(2781),e.b));const t=x();("string"==typeof n||"function"==typeof Request&&n instanceof Request||"function"==typeof URL&&n instanceof URL)&&(n=fetch(n));const{instance:_,module:i}=await async function(n,t){if("function"==typeof Response&&n instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(n,t)}catch(t){if("application/wasm"==n.headers.get("Content-Type"))throw t;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",t)}const e=await n.arrayBuffer();return await WebAssembly.instantiate(e,t)}{const e=await WebAssembly.instantiate(n,t);return e instanceof WebAssembly.Instance?{instance:e,module:n}:e}}(await n,t);return O(_,i)}const R=E}}]);
//# sourceMappingURL=854.js.map?_cache=9da793b3efc18875808d