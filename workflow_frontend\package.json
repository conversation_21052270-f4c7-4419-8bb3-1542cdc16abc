{"name": "itf-workflow-app", "version": "0.0.2", "description": " The frontend app for DigiFors ITF workflow infrastructure", "productName": "ITF Workflow App", "author": "DigiFors GmbH <<EMAIL>>", "private": true, "scripts": {"lint": "eslint --ext .js,.vue ./", "format": "prettier --write \"**/*.{js,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@quasar/extras": "^1.16.4", "dotenv": "^16.4.7", "dotenv-webpack": "^8.1.0", "quasar": "^2.16.0", "vue": "^3.4.18", "vue-router": "^4.0.12"}, "devDependencies": {"@quasar/app-vite": "^2.1.4", "autoprefixer": "^10.4.2", "eslint": "^8.57.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "postcss": "^8.4.14", "prettier": "^2.5.1", "typescript": "^5.6.2", "vite-plugin-checker": "^0.8.0", "vue-tsc": "^2.1.6"}, "engines": {"node": "^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}