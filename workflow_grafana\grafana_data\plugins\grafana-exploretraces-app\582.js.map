{"version": 3, "file": "582.js", "mappings": "mKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,sCACxB,iBAAkB,gCAClB,yBAA0B,qCAE3B,yBAA0B,CACzB,sBAAuB,sCACvB,mBAAoB,gDAErB,gBAAiB,CAChBC,QAAS,uEACTC,SAAU,wCACVC,MAAO,kBAER,wBAAyB,CACxB,wBAAyB,aACzB,sBAAuB,eACvB,sBAAuB,cAExB,iBAAkB,CACjB,iBAAkB,oBAClB,cAAe,eACf,uBAAwB,iBAEzB,iBAAkB,CACjB,eAAgB,cAChB,aAAc,iBAEf,oCAAqC,CACpC,eAAgB,aAChB,iBAAkB,kCAEnBC,SAAU,CACT,gCAAiC,kCAElC,YAAa,CACZD,MAAO,CACNA,MAAO,UAGT,2BAA4B,CAC3BE,QAAS,aAEV,qBAAsB,CACrB,uBAAwB,4BACxB,sCAAuC,gDAExC,yBAA0B,CACzB,2DAA4D,uGAC5D,kBAAmB,gDAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,QAEnB,oBAAqB,CACpB,uBAAwB,qBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,0BAEpC,wBAAyB,CACxB,wBAAyB,cACzB,mBAAoB,eAErB,yBAA0B,CACzB,2BAA4B,aAC5B,aAAc,CACb,2BAA4B,oBAE7B,qBAAsB,mBACtB,sBAAuB,aACvB,eAAgB,CACf,2BAA4B,oBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,aAGb,8CAA+C,CAC9C,mBAAoB,UACpBC,QAAS,8GACT,gDAAiD,mDAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,mCACzB,uBAAwB,mCACxB,gCAAiC,qCACjC,oDAAqD,wFACrD,0BAA2B,uCAC3B,uBAAwB,iCACxB,mBAAoB,qCACpB,mDAAoD,+DACpD,uBAAwB,qCACxB,kDAAmD,6GACnD,iCAAkC,iEAClC,oCAAqC,uCAIxC,6BAA8B,CAC7B,+BAAgC,0BAChC,6BAA8B,0BAE/B,oBAAqB,CACpB,2BAA4B,mBAE7B,8BAA+B,CAC9B,kBAAmB,oBAEpB,2BAA4B,CAC3BC,MAAO,YAER,yBAA0B,CACzB,mBAAoB,wBAErB,4BAA6B,CAC5B,6CAA8C,6EAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,WAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,UAGR,uBAAwB,CACvB,0BAA2B,oBAE5B,wBAAyB,CACxB,2BAA4B,qB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/pl-PL/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Edytuj filtr z kluczem {{keyLabel}}\",\n\t\t\t\"managed-filter\": \"Filtr zarządzany ({{origin}})\",\n\t\t\t\"remove-filter-with-key\": \"Usuń filtr z kluczem {{keyLabel}}\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Usuń wartość filtra – {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Użyj wartości niestandardowej: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Je<PERSON><PERSON> doprowadził Cię tutaj link, może to oznaczać błąd w aplikacji.\",\n\t\t\tsubTitle: \"Adres URL nie pasuje do żadnej strony\",\n\t\t\ttitle: \"Nie znaleziono\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"<PERSON><PERSON><PERSON> scenę\",\n\t\t\t\"expand-button-label\": \"Roz<PERSON><PERSON> scenę\",\n\t\t\t\"remove-button-label\": \"<PERSON>u<PERSON> scenę\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Szczegóły obiektu\",\n\t\t\t\"scene-graph\": \"Wykres sceny\",\n\t\t\t\"title-scene-debugger\": \"Debuger sceny\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Zwiń wiersz\",\n\t\t\t\"expand-row\": \"Rozwiń wiersz\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Porównanie\",\n\t\t\t\"button-tooltip\": \"Włącz porównanie ram czasowych\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Widżet zmiany rozmiaru okienka\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Tytuł\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Eksploruj\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Ładowanie panelu wtyczki…\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"Wtyczka panelu nie zawiera komponentu panelu\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Renderowanie zbyt wielu szeregów w jednym panelu może wpłynąć na wydajność i utrudnić odczyt danych.\",\n\t\t\t\"warning-message\": \"Wyświetlanie tylko {{seriesLimit}} szeregów\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Usuń\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Anuluj zapytanie\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Edytuj operator filtra\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Dodaj filtr\",\n\t\t\t\"title-add-filter\": \"Dodaj filtr\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Usuń filtr\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Wybierz etykietę\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Wybierz etykietę\",\n\t\t\t\"title-remove-filter\": \"Usuń filtr\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Wybierz wartość\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"domyślne\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"wyczyść\",\n\t\t\ttooltip: \"Zastosowano domyślnie do tego pulpitu. W przypadku edycji zmiana zostanie uwzględniona na innych pulpitach.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Przywróć grupowanie ustawione przez ten pulpit.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Wartości rozdzielone przecinkami\",\n\t\t\t\t\t\"double-quoted-values\": \"Wartości w podwójnym cudzysłowie\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Formatowanie daty na różne sposoby\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Formatowanie zmiennych wielowartościowych za pomocą składni glob, np. {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"Modyfikowanie wartości w kodzie HTML\",\n\t\t\t\t\t\"json-stringify-value\": \"Wartość konwersji na ciąg JSON\",\n\t\t\t\t\t\"keep-value-as-is\": \"Zachowaj wartość w obecnej postaci\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Wiele wartości jest sformatowanych w postaci zmienna=wartość\",\n\t\t\t\t\t\"single-quoted-values\": \"Wartości w pojedynczym cudzysłowie\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Przydatne w przypadku wartości unikowych w adresach URL z uwzględnieniem znaków składni identyfikatora URI\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Przydatne w przypadku wartości znaków unikowych w adresach URL\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Wartości są rozdzielone znakiem |\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Grupuj według selektora\",\n\t\t\t\"placeholder-group-by-label\": \"Grupuj według etykiety\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Wybierz wartość\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Ładowanie opcji…\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Zastosuj\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Nie znaleziono opcji\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Podczas pobierania etykiet wystąpił błąd. Kliknij, aby spróbować ponownie\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Cześć!\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Tekst\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Wprowadź wartość\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Wybierz wartość\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}