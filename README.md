# Workflow Automation with Quasar Frontend, FastAPI and PostgreSQL Database

This project integrates several technologies to manage and automate workflows for forensic case management.

## Project Structure
**Backend:**
- workflow_backend: Python with FastAPI for implementing business logic and a REST-API
- workflow_database: PostgreSQL database for data storage
- workflow_migrations: Containerized YoYo migrations for database schema management

**Frontend:**
- workflow_frontend: Quasar/Vue/Node.js for building a web application for user interaction with the workflow system 
- grafana: Visualization component for monitoring and analytics

# How to set up
First, clone this repository.
```shell
git clone ssh://************************:222/IT-Forensik/IT-Forensic_Workflow_Automation.git
cd IT-Forensic_Workflow_Automation
```
## Backend

### 1. Configuration
Create a .env file in the project root directory (see example file .env.template). Set usernames and passwords.

### 2. Install docker

Linux:
```shell
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

Windows: 
<br> Download from [docker website](https://docs.docker.com/desktop/install/windows-install/), complete installation and start Docker Desktop

### 3. Start containers and Apply Database Migrations

_Note: Parsing the image folder path from ImageStation result_data in order to automatically create case and evidence item is not working when running the backend in a docker container UNDER WINDOWS (because docker is not running on the windows system itself but on its linux subsystem)_

```shell
cd IT-Forensic_Workflow_Automation                      # go to folder where docker-compose.yml file is located

# note: under Windows, run all commands without 'sudo'
# note: "docker-compose" is deprecated, now it is "docker compose"
sudo docker compose up --build -d workflow_database     # Build and start the container.
sudo docker compose up --build -d workflow_backend      # Build and start the container.
sudo docker compose up --build -d workflow_migrations   # container will apply migrations and exit automatically
sudo docker compose ps                                  # Check the container is up and running (wait for the migrations container
                                                        # to automatically stop after it finished applying all migrations)
```
#### Database Migrations with YoYo

The `workflow_migrations` container manages database migrations using YoYo. To apply migrations:

 - Place your migration files in the `workflow_database/migrations` directory.
 - The `workflow_migrations` container will automatically apply pending migrations when started. Wait till it stops automatically, then it finished applying the migrations.

---
**For development:**

If you do not want the API/Backend to run inside a docker container because you want your changes to be hot re-loaded, do instead:

Install python requirements:
```sudo apt install python3 python3-pip python3-virtualenv```

Now activate Python virtual environment:
```shell
cd workflow_backend

# create virtual environment
python -m venv .venv

# activate venv
pipenv shell

# update Pipfile.lock from Pipfile
pipenv lock # add "--dev" if You need development dependencies too

# install dependencies from Pipfile
pipenv sync # add "--dev" if You need development dependencies too

# see locally installed Python dependencies
pip list
```
Run API in the activated virtual environment:

```shell
# for development mode
fastapi dev --no-reload main.py

# for production mode
fastapi run --no-reload main.py
```

Note: The log file of the backend is located at `workflow_backend/forensic_workflow.log`.

---


## Frontend
### 1. Configuration
Create a .env file in the project root directory if it does not exist yet (see example file .env.template). Set the URL to the workflow API, LagerDB GUI and ImageStation GUI.

### 2. Run the frontend web application

**For development:**

Install dependencies:

```shell
cd workflow_frontend

npm install   # install all dependencies from package.json 
              # optional: --include=dev to install development dependencies
npm install -g @quasar/cli # install quasar globally
```

Run:
```shell
quasar dev    # start the dev server
# or
quasar build  # build the web application to folder "dist"
```

**For production:**

Assuming docker has been installed during backend set up, you can now simply bring the container up:

```shell
cd IT-Forensic_Workflow_Automation                      # go to folder where docker-compose.yml file is located

# note: under Windows, run all commands without 'sudo'
sudo docker compose up --build -d workflow_frontend     # Build and start the container.
sudo docker compose ps                                  # Check the container is up and running
```

# Backend documentation
### What you can do with the backend
**cases**
- the standard things like get, add and delete cases (see Swagger UI of API for details)
- get progress of a case (returns dict with 'relative_progress' and 'progress_int_percent')

**evidence items**
- the standard things like get, add and delete evidence items (see Swagger UI of API for details)
  - *Note: adding an evidence item automatically creates a process with status FINISHED for step "LagerDB / intake" (step id 0).

**processes**
- standard: get and delete processes (see Swagger UI of API for details)
- get all processes that are NOT linked to an evidence item AND a case
- Add a new process or update a process by id. 
  - If an ImageStation process has not evidence item id, yet, and contains result data, 
    the result data is being parsed to assign the process to an evidence item and a case. Both case and evidence item are created if they do not exist, yet. If an evidence item is created, a new finished process for LagerDB/intake is added, too. 
  - If the status of the process is FINISHED, automatically queue the next step if applicable.
    (The next step can only be queued if the process is assigned to an evidence item.)

**more**
- **process logs**: add an entry to process logs table (see Swagger UI of API for details)
- **forensic steps**: get all entries in table 'forensic_steps'
- **get next job**: Get the next process to run in the given step and set the process to status "assigned".
  - If FOCUS.AI is requesting its next job, make sure that the maximum of FOCUS instances (that are allowed to run in parallel based on the value specified in the .env file) is not exceeded.

### Parsing of image folder path from ImageStation
Requirements for the parsing to work:
- the path ends with "/client/case_folder/evidence_item_folder"
- case_folder contains at least 2 underscores and follows this structure: `[reference number]_[last name]_[prefix of first name]`. 
The reference number can contain additional underscores ([artifact of qr print web service](https://gitea.digifors.local/IT-Forensik/QRPrintWebservice/src/commit/a0b49953328ac944d12e164a51c9e792ea55e097/qrprinter/main.py#L335)) which are replaced back to "/" when inserting or searching for the case in the database.
- evidence_item_folder case_folder contains at least 2 underscores and follows this structure: `[last name]_[prefix of first name]_[evidence item name]`. The evidence item name is allowed to have additional underscores (e.g. "ass_9").

*Paths generated from the [QRPrintWebservice](https://gitea.digifors.local/IT-Forensik/QRPrintWebservice) do meet the requirements.*

The application is then checking if a case with this client, this reference number and a case name like `[last name]%[prefix of first name]%` or `[prefix of first name]% [last name]` and an evidence item with the evidence item name already exist. If not, it creates both.

### Input and result data of the steps
The following fields are expected to be in the process' result_data.
- **ImageStation**: `image_folder_path`, `image_file_extension`, `image_name`, `device_size`

The following fields are expected to be in the process' input_data.
- **InvestiGator/XwaysDÜ**: `first_image_file_path`
- **FOCUS.AI**: `first_image_file_path`, `triage_file_path`, `evidence_item_name`


- `first_image_file_path`
  - is constructed from the ImageStation result_data: `first_image_file_path = [image_folder_path]/[image_name][image_file_extension]`
  - e.g."path/to/LKA Börlin/AZ 666-666_Müller_Fried/Müller_Fried_Asservat_01/Müller_Fried_Asservat_01.E01"
- `triage_file_path`
  - is constructed from first_image_file_path: `triage_file_path = [grandparent directory of first_image_file_path]/[name of grandparent directory of first_image_file_path].triage`
  - example: "path/to/LKA Börlin/AZ 666-666_Müller_Fried/Müller_Fried_Asservat_01/Müller_Fried_Asservat_01.E01" would return "path/to/LKA Börlin/AZ 666-666_Müller_Fried/AZ 666-666_Müller_Fried.triage"
- `evidence_item_name`
  - is queried from the database by getting the evidence item the process is assigned to


## Database Structure
```shell
cases
├─ id
├─ case_name       # usually the suspect's name
├─ reference_number
├─ client
├─ intake_at       # time of case intake (set to now from workflow software or received from LagerDB)

evidence_items
├─ id
├─ evidence_item_name
├─ case_id          # references id from table "cases"

forensic_steps
├─ id
├─ name             # e.g. "ImageStation", "XwaysDÜ", "FOCUS.AI"
├─ display_name     # e.g. "Imaging", "Voranalyse", "Triage"
├─ display_position # where to display in the frontend

processes
├─ id
├─ evidence_item_id # references id from table "evidence_items"
├─ step_id          # references id from table "forensic_steps"
├─ status           # 0 = queued, 1 = running, 2 = finished, 3 = failed, 4 = canceled, 5 = assigned
├─ updated_at       # timestamp set every time the status is set
├─ input_data       # string, e.g. "{ 'image_path': 'my/path/image.E01' }"
├─ result_data      # string, e.g. "{ 'result_folder': 'my/path' }"
├─ prioritize       # boolean, serves as a flag
├─ started_running  # timestamp when status became 'running'
├─ ended_at         # timestamp when status became 'finished' or 'failed'

process_logs
├─ id
├─ process_id       # references the process
├─ message          # the log message
├─ created_at       # timestamp when entry was created
├─ loglevel         # loglevel like "ERROR" or "INFO"
```
